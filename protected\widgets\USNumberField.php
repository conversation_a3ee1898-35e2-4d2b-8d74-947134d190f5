<?php

class <PERSON>NumberField extends CWidget {

    public $id;
    public $model;
    public $attribute;
    public $value = null;
    public $name = null;
    public $label = null;
    public $prepend = null;
    public $hint = null;
    public $class = null;
    //controles
    public $value_id = null;
    public $up_id = null;
    public $up_name = null;
    public $down_id = null;
    public $down_name = null;
    public $max_attribute = null;
    public $min_attribute = null;
    //    
    public $required = false;
    public $readonly_text = false;
    public $readonly_buttons = false;
    public $has_up_option = true;
    public $has_down_option = true;
    public $error = false;
    public $show_label = false;
    public $visible = true;
    public $style_div = "";
    //         
    public $max_value = null;
    public $min_value = null;
    public $class_number = 'us-double2';
    //PRIVATE
    private $controller;

    /**
     * Initializes the widget.
     */
    public function init() {

        //CONTROLADOR DE ORIGEN
        $this->controller = Yii::app()->controller;
        //ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //LABEL DEL CAMPO VALUE
        $this->label = $this->_getLabel();
        //VALOR DEL CAMPO VALUE
        $this->value = isset($this->model) ? $this->model->{$this->attribute} : $this->value;
        //NOMBRE DEL CAMPO VALUE
        $this->name = isset($this->name) ? $this->name : (isset($this->model) && isset($this->attribute) ? get_class($this->model) . "[{$this->attribute}]" : null);
        //ERROR DEL CAMPO VALUE
        $this->error = isset($this->model) ? $this->model->hasErrors($this->attribute) : $this->error;
        //ID'S DE LOS CAMPOS DEL BUSCADOR
        $this->value_id = isset($this->value_id) ? $this->value_id : $this->controller->getServerId();
        $this->up_id = isset($this->up_id) ? $this->up_id : $this->controller->getServerId();
        $this->down_id = isset($this->down_id) ? $this->down_id : $this->controller->getServerId();
        // SI EL CAMPO ES REQUERIDO            
        $this->required = isset($this->required) ? $this->required : $this->model->isAttributeRequired($this->attribute);

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        $(document).ready(function() { 
        
        });   
        
        //AL HACER CLICK EN UP AUMENTAMOS 0.01 EL DATO SELECCIONADO
        $('body').on('click', '#{$this->up_id}',function(e){
            
            var new_value = USMath.plus(parseFloat($('#{$this->value_id}').val()).toFixed(2), 0.01);             
            var max_value = $('#{$this->value_id}').attr('max');
                
            if(max_value != undefined){
                    
                if(new_value <= max_value){
                    $('#{$this->value_id}').floatVal(2, new_value);
                    $('#{$this->value_id}').change();
                }    
            }else{
                $('#{$this->value_id}').floatVal(2, new_value);
                $('#{$this->value_id}').change();
            }                   
        });
        
        //AL HACER CLICK EN DOWN DISMINUIMOS 0.01 EL DATO SELECCIONADO
        $('body').on('click', '#{$this->down_id}',function(e){
            
            var min_value = $('#{$this->value_id}').attr('min');
            var new_value = USMath.minus(parseFloat($('#{$this->value_id}').val()).toFixed(2), 0.01);
                
            if(new_value >= min_value){
                $('#{$this->value_id}').floatVal(2, new_value);
                $('#{$this->value_id}').change();
            }
        });

        $('#{$this->value_id}').change(function() {
            var selectedValue = parseFloat($(this).val());
            var max_value = parseFloat($('#{$this->value_id}').attr('max'));
            var min_value = parseFloat($('#{$this->value_id}').attr('min'));

            if(max_value){
                if(selectedValue > max_value){
                    $(this).val(max_value);
                }
            }

            if(min_value){
                if(selectedValue < min_value){
                    $(this).val(min_value);
                }
            }
        });

        unfocusable();

        ", CClientScript::POS_END);
        //}
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id={$this->id} style='margin-bottom:0px;{$this->style_div}' class='us-number-field form-group " . ($this->error ? 'has-error' : '') . "'>";
        if (isset($this->label) && $this->show_label) {
            $b_required = ((isset($this->model) && $this->model->isAttributeRequired($this->attribute)) || $this->required );
            echo CHtml::label($this->label . ($b_required ? " <span class='required'>*</span>" : ""), $this->value_id, array(
                'class' => 'control-label ' . ($b_required ? 'required' : ''),
                'style' => 'text-align:left;display: block;'
            ));
        }

        $this->renderValue();

        if (isset($this->hint)) {
            echo "<span class='help-block'>{$this->hint}</span>";
        }
        echo "</div>";
    }

    private function renderValue() {

        $max_value = $this->max_value;
        $min_value = $this->min_value;

        if (isset($this->model) && isset($this->max_attribute)) {
            $max_value = round($this->model[$this->max_attribute], 2);
        }

        if (isset($this->model) && isset($this->min_attribute)) {
            $min_value = round($this->model[$this->min_attribute], 2);
            $min_value = $min_value < 0 ? 0 : $min_value;
        }

        $htmlOptions = [
            'id' => $this->value_id,
            'class' => 'form-control '. $this->class_number .' '.  $this->class,
            'autocomplete' => 'off',
            'readonly' => $this->readonly_text,
            'style' => 'text-align:right',
            'prepend' => $this->prepend,
        ];

        if (isset($max_value))
            $htmlOptions['max'] = $max_value;

        $htmlOptions['min'] = isset($min_value) ? $min_value : 0;

        echo "<div class='input-group' " . ($this->visible ? "" : "style = 'display:none;'") . ">";
        echo $this->textFieldNonActive($this->name, round($this->model[$this->attribute], 2), $htmlOptions);

        if ($this->has_up_option) {

            $this->up_name = isset($this->up_name) ? $this->up_name : (isset($this->model) && isset($this->max_attribute) ? get_class($this->model) . "[{$this->max_attribute}]" : null);

            echo "<span class='input-group-addon'>";
            echo CHtml::hiddenField("{$this->up_name}", $max_value,
                    ["id" => "{$this->value_id}_max", 'class' => "{$this->class}-max"]);
            $this->widget('application.widgets.USLink', array(
                'id' => $this->up_id,
                'icon' => 'fa fa-1x fa-plus',
                'title' => 'Subir',
                'visible' => !$this->readonly_buttons,
                'class' => 'initial',
            ));
            echo "</span>";
        }
        if ($this->has_down_option) {

            $this->down_name = isset($this->down_name) ? $this->down_name : (isset($this->model) && isset($this->min_attribute) ? get_class($this->model) . "[{$this->min_attribute}]" : null);

            echo "<span class='input-group-addon'>";
            echo CHtml::hiddenField("{$this->down_name}", $min_value, ["id" => "{$this->value_id}_min", 'class' => "{$this->class}-min"]);
            $this->widget('application.widgets.USLink', array(
                'id' => $this->down_id,
                'icon' => 'fa fa-1x fa-minus',
                'title' => 'Bajar',
                'visible' => !$this->readonly_buttons,
                'class' => 'initial',
            ));
            echo "</span>";
        }

        echo "</div>";
        if (isset($this->model)) {
            echo CHtml::error($this->model, $this->attribute, array(
                'class' => 'help-block ' . ($this->error ? 'error' : '')
            ));
        }
    }

    private function textFieldNonActive($name, $value, array $htmlOptions = []) {

        $htmlOptions['id'] = isset($htmlOptions['id']) ? $htmlOptions['id'] : Yii::app()->controller->getServerId();
        $htmlOptions['class'] = 'form-control ' . (isset($htmlOptions['class']) ? $htmlOptions['class'] : '');
        $html = '';
        $html .= self::getPrependAppend(CHtml::textField($name, $value, $htmlOptions), $htmlOptions);
        return $html;
    }

    private function getPrependAppend($p_s_input_html = '', array $p_a_htmlOptions = []) {
        $s_html = '';
        //Agregamos prepend
        if (isset($p_a_htmlOptions['prepend']) || isset($p_a_htmlOptions['append'])) {
            if (isset($p_a_htmlOptions['prepend'])) {
                $s_html .= "<span class='input-group-addon'>{$p_a_htmlOptions['prepend']}</span>";
            }
        }
        //Agregamos input
        $s_html .= $p_s_input_html;
        //Agregamos append
        if (isset($p_a_htmlOptions['prepend']) || isset($p_a_htmlOptions['append'])) {
            if (isset($p_a_htmlOptions['append'])) {
                $s_html .= "<span class='input-group-addon'>{$p_a_htmlOptions['append']}</span>";
            }
        }
        return $s_html;
    }

    private function _getLabel() {
        return isset($this->model) && is_null($this->label) ? $this->model->getAttributeLabel($this->attribute) : $this->label;
    }

}
