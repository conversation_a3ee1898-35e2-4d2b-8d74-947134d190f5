<?php

/**
 * Documentación
 * http://miromannino.github.io/Justified-Gallery/getting-started/
 * http://www.jacklmoore.com/colorbox/
 */

class USGallery extends CWidget {

    public $id;
    public $images;
    public $options = [];
    //PRIVATE
    private $clientScript;
    private $controller;

    public function init() {
        //ID
        $this->controller = Yii::app()->controller;
        $this->clientScript = Yii::app()->clientScript;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        SIANAssets::registerScriptFile('other/justifiedGallery/js/jquery.justifiedGallery.min.js');
        SIANAssets::registerScriptFile('other/justifiedGallery/js/jquery.colorbox-min.js');

        SIANAssets::registerCssFile('other/justifiedGallery/css/justifiedGallery.min.css');
        SIANAssets::registerCssFile('other/justifiedGallery/css/colorbox.css');

        $this->clientScript->registerScript('us_gallery', "
        $(document).ready(function() {

            $('#{$this->id}').justifiedGallery(" . CJSON::encode($this->options) . ").on('jg.complete', function () {
                
                $('#{$this->id} a').each(function( index ) {
                    var element = $(this);

                    element.colorbox({
                        maxWidth : '80%',
                        maxHeight : '80%',
                        opacity : 0.8,
                        transition : 'elastic',
                        current : '',
                        title: '<a title=\'' + element.data('description') +'\'>' + element.data('title') + '</a>'
                    });
                });


            });
        });
        
//        $('body').on('click', '.us-gallery-image', function(e) {
//            var element = $(this);
//            bootbox.modal('<img src=\'' + element.attr('href') + '\'><hr><p>' + element.data('description') + '</p>', element.data('title'));
//            e.preventDefault();
//        });
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}'>";
        foreach ($this->images as $image) {
            echo CHtml::link(CHtml::image($image['image'], $image['title']), $image['url'], array(
                'class' => 'us-gallery-image',
                'data-title' => $image['title'],
                'data-description' => $image['description'],
                'rel' => $this->id,
            ));
        }
        echo "</div>";
    }

}
