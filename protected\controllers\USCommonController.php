<?php

class USCommonController extends USController {

    public $plural = 'Test';
    public $singular = 'Test';

    public function accessRules() {
        return array(
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('snapshot', 'savesnapshot'),
                'users' => array('@'),
            ),
            array('deny', // deny all users
                'users' => array('*'),
            ),
        );
    }

    public function actionSnapshot($id, $modal_id = null, $parent_id = null, $element_id = null, $cms = 'default') {

        $this->title = "Tomar foto";

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array(
                'parent_id' => $parent_id,
                'modal_id' => $modal_id
            )
                ), true);

        $content_html = $this->renderPartial("application.views.common._snapshot", array(
            'data' => array(
                'id' => $id,
                'modal_id' => $modal_id,
                'element_id' => $element_id,
                'cms' => $cms,
            )
                ), true);

        echo CJSON::encode(array(
            'id' => $id,
            'modal_id' => $modal_id,
            'parent_id' => $parent_id,
            'modal_html' => $modal_html,
            "element_id" => $element_id,
            "content_html" => $content_html
        ));
    }

    public function actionSavesnapshot($cms = 'default') {
        echo SIANImage::saveImages(USTime::getDateCode(), 'php://input', $cms, 'png');
    }





}
