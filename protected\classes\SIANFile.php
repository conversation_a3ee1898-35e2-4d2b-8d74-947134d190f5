<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANFile
 *
 * <AUTHOR>
 */
class SIANFile {

//    public static function writeJSON($object, $name) {
//        file_put_contents("{$name}.txt", CJSON::encode($object));
//    }

    public static function writeErrors($model) {

        if ($model->hasErrors()) {
            $class = get_class($model);
            $pk = is_array($model->primaryKey) ? implode('-', $model->primaryKey) : $model->primaryKey;

            file_put_contents("{$class}-{$pk}-errors.txt", CHtml::errorSummary($model));
        }
    }

    public static function writeAttributes($model) {

        $class = get_class($model);
        $pk = is_array($model->primaryKey) ? implode('-', $model->primaryKey) : $model->primaryKey;

        file_put_contents("{$class}-{$pk}-attributes.txt", json_encode($model->attributes, JSON_PRETTY_PRINT));
    }

    public static function writeAttribute($model, $attribute) {

        $class = get_class($model);
        $pk = is_array($model->primaryKey) ? implode('-', $model->primaryKey) : $model->primaryKey;

        file_put_contents("{$class}-{$pk}-{$attribute}.txt", json_encode($model->{$attribute}, JSON_PRETTY_PRINT));
    }

    public static function writeArray(array $array, $filename = 'array') {
        file_put_contents(USString::generateAlias($filename) . ".txt", json_encode($array, JSON_PRETTY_PRINT));
    }

    public static function writeString($string, $filename = 'string') {
        file_put_contents(USString::generateAlias($filename) . ".txt", $string);
    }

    public static function writeJSON($object, $filename = 'string') {
        file_put_contents(USString::generateAlias($filename) . ".txt", json_encode($object, JSON_PRETTY_PRINT));
    }

    /**
     * Esta función devuelve el número de bytes que fueron escritos en el fichero, o false en caso de error.
     * Advertencia: Esta función puede devolver el valor booleano false, pero también puede devolver un valor no booleano que se evalúa como false. 
     * Use el operador === para comprobar el valor devuelto por esta función.
     */
    public static function download($url, $storage_path)
    {
        $file_content = file_get_contents($url);
        $response = file_put_contents($storage_path, $file_content);
        return $response;
    }

}
