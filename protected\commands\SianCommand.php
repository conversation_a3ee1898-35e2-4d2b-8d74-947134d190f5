<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of newPHPClass
 *
 * <AUTHOR>
 */
class SianCommand extends CConsoleCommand {

    public function actionGetExchangeRate($args) {

        ini_set('max_execution_time', 300);

        try {
            //Iniciamos transacción
            Yii::app()->dbSian->beginTransaction();

            //Obtenemos fecha
            if (count($args) > 0) {
                $s_date = $args[0];
            } else {
                //Fecha de hoy
                $s_date = SIANTime::today();
            }
            
            //Buscamos modelo
            $o_model = SIANExchangeRate::model()->findByPk([
                'date' => $s_date,
            ]);
            $a_data = [];
            //Modelo NO existe?
            if (!isset($o_model)) {

                //INSERTAR
                
                $a_data = SIANSunat::getExchangeRateByDate($s_date, Yii::app()->params['date_sql_format'], false);

                if ($a_data) {
                    //Changelog DATA
                    USDatabase::multipleInsert('SIANExchangeRate', [
                        [
                            'date' => $s_date,
                            'purchase' => $a_data['purchase'],
                            'sale' => $a_data['sale'],
                        ]
                    ]);
                }
            }

            Yii::app()->dbSian->getCurrentTransaction()->commit();

            echo CJSON::encode($a_data);
        } catch (CDbException $ex) {
            USLog::save($ex);
            Yii::app()->dbSian->getCurrentTransaction()->rollback();
            echo $ex->errorInfo[2];
        } catch (Exception $ex) {
            USLog::save($ex);
            Yii::app()->dbSian->getCurrentTransaction()->rollback();
            echo $ex->getMessage();
        }
    }

    public function actionGetExchangeRates($args) {

        ini_set('max_execution_time', 300);

        try {
            //Iniciamos transacción
            Yii::app()->dbSian->beginTransaction();

            //Obtenemos fecha
            if (count($args) > 1) {
                $s_begin_date = $args[0];
                $s_end_date = $args[1];
            } else {
                throw new Exception('Debe especificar por lo menos 2 parámetros: fecha de inicio y fin');
            }

            $a_items = Yii::app()->dbSian->createCommand()
                    ->select([
                        'EX.date'
                    ])
                    ->from('exchange_rate EX')
                    ->where('EX.date >= :begin_date AND EX.date <= :end_date', [
                        ':begin_date' => $s_begin_date,
                        ':end_date' => $s_end_date
                    ])
                    ->queryAll();

            $a_dates = USArray::array_column($a_items, 'date');

            $a_tcs = SIANSunat::getExchangeRate($s_begin_date, $s_end_date);

            $a_insert = [];
            foreach ($a_tcs as $a_tc) {
                if (!in_array($a_tc['date'], $a_dates)) {
                    $a_insert[] = $a_tc;
                }
            }

            //Changelog DATA
            USDatabase::multipleInsert('SIANExchangeRate', $a_insert);

            Yii::app()->dbSian->getCurrentTransaction()->commit();

            echo CJSON::encode($a_tcs);
        } catch (CDbException $ex) {
            USLog::save($ex);
            Yii::app()->dbSian->getCurrentTransaction()->rollback();
            echo $ex->errorInfo[2];
        } catch (Exception $ex) {
            USLog::save($ex);
            Yii::app()->dbSian->getCurrentTransaction()->rollback();
            echo $ex->getMessage();
        }
    }

}
