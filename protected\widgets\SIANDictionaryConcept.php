<?php

class SIANDictionaryConcept extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $child_modal_id;
    public $column_items;
    public $field_items;
    //PRIVATE
    private $controller;
    private $div_field;
    private $field_input_id;
    private $aux_id;
    private $short_concept_name_input_id;
    private $concept_name_input_id;
    private $type_concept_input_id;
    private $column_concept_name_input_id;
    private $export_plame_id;
    private $div_code_plame;
    private $code_plame_id;
    private $is_printable_id;
    private $type_formula_id;
    private $sentence_input_id;
    private $condition_input_id;
    private $true_sentence_input_id;
    private $false_sentence_input_id;
    private $order_input_id;
    private $array_concept_input_id;
    private $textarea_id;
    private $setting_button_id;
    private $add_button_id;
    private $modify_button_id;
    private $cancel_button_id;
    private $validate_button_id;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->modal_id = isset($this->modal_id) ? $this->modal_id : $this->controller->getServerId();
        $this->child_modal_id = isset($this->child_modal_id) ? $this->child_modal_id : $this->controller->getServerId();
        //PRIVATE
        $this->short_concept_name_input_id = $this->controller->getServerId();
        $this->concept_name_input_id = $this->controller->getServerId();
        $this->field_input_id = $this->controller->getServerId();
        $this->aux_id = $this->controller->getServerId();
        $this->type_concept_input_id = $this->controller->getServerId();
        $this->column_concept_name_input_id = $this->controller->getServerId();
        $this->export_plame_id = $this->controller->getServerId();
        $this->code_plame_id = $this->controller->getServerId();
        $this->is_printable_id = $this->controller->getServerId();
        $this->type_formula_id = $this->controller->getServerId();
        $this->sentence_input_id = $this->controller->getServerId();
        $this->condition_input_id = $this->controller->getServerId();
        $this->true_sentence_input_id = $this->controller->getServerId();
        $this->false_sentence_input_id = $this->controller->getServerId();
        $this->order_input_id = $this->controller->getServerId();
        $this->array_concept_input_id = $this->controller->getServerId();
        $this->div_field = $this->controller->getServerId();
        $this->div_code_plame = $this->controller->getServerId();

        $this->setting_button_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();
        $this->modify_button_id = $this->controller->getServerId();
        $this->cancel_button_id = $this->controller->getServerId();
        $this->validate_button_id = $this->controller->getServerId();
        $this->textarea_id = $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-dictionary-concept.js');
        SIANAssets::registerScriptFile('other/select2/js/select2.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
        SIANAssets::registerCssFile('css/sian-dictionary-concept.css');
        SIANAssets::registerCssFile('other/select2/css/select2.css');


        //ITEMS
        $conceptItems = [];

        foreach ($this->model->tempConcepts as $concept) {
            $attributes = [];
            array_push($attributes, "field:'{$concept->field}'");
            array_push($attributes, "short_name:'{$concept->short_name}'");
            array_push($attributes, "concept_name:'{$concept->concept_name}'");
            array_push($attributes, "type_concept:'{$concept->type_concept}'");
            array_push($attributes, "type_formula:'{$concept->type_formula}'");
            array_push($attributes, "sentence:'{$concept->sentence}'");
            array_push($attributes, "condition:'{$concept->condition}'");
            array_push($attributes, "true_sentence:'{$concept->true_sentence}'");
            array_push($attributes, "false_sentence:'{$concept->false_sentence}'");
            array_push($attributes, "export_plame:'{$concept->export_plame}'");
            array_push($attributes, "plame_id:'{$concept->plame_id}'");
            array_push($attributes, "plame_code:'{$concept->plame_code}'");
            array_push($attributes, "is_printable:'{$concept->is_printable}'");
            array_push($attributes, "errors:" . json_encode($concept->getErrors()));
            array_push($conceptItems, '{' . implode(',', $attributes) . "}");
        }

        $conceptItems = '[' . implode(',', $conceptItems) . ']';
//
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        $(document).ready(function() {
        
            SIANDictionaryConceptInit('{$this->id}', false); 
                
            $('#{$this->id}').find('tbody').sortable({                    
                    stop: function( event, ui ) {
                        SIANDictionaryConceptUpdate('{$this->id}');
                    }
            });
            
            $('#{$this->field_input_id}').val('0').trigger('change.select2');
            $('#{$this->code_plame_id}').val('0').trigger('change.select2');
            $('#{$this->modify_button_id}').attr('disabled', true);
            $('#{$this->cancel_button_id}').attr('disabled', true);
                
            var buttonFormula =  $('#{$this->setting_button_id}');
            $('input[type=radio][name={$this->type_formula_id}]').change(function() {
                if (this.value == '" . Concept::TYPE_SIMPLE . "') {                
                  buttonFormula.removeAttr('disabled');                  
                }
                else if (this.value == '" . Concept::TYPE_CONDITIONAL . "') {
                  buttonFormula.removeAttr('disabled');
                }else if (this.value == '" . Concept::TYPE_INFORMED . "') {
                   buttonFormula.attr('disabled',true);
                }
                $('#{$this->textarea_id}').text('');
                $('#{$this->sentence_input_id}').val('');
                $('#{$this->condition_input_id}').val('');
                $('#{$this->true_sentence_input_id}').val('');
                $('#{$this->false_sentence_input_id}').val('');
                $('#{$this->array_concept_input_id}').val('');
            });
                
            $('#{$this->export_plame_id}').change(function() {
                
                if(!$('#{$this->export_plame_id}').prop('checked')){
                    $('#{$this->code_plame_id}').val('0').trigger('change.select2');
                    $('#{$this->code_plame_id}').attr('disabled', 'disabled');
                }else{
                    $('#{$this->code_plame_id}').removeAttr('disabled');
                }                  
            });
            
            $('body').on('change', '#{$this->field_input_id}', function () {                
                var field_name = $('#{$this->field_input_id}').val() == null ? '' : $('#{$this->field_input_id}').val() == 0? '': $('#{$this->field_input_id}').select2('data')[0].text;
                $('#{$this->concept_name_input_id}').val(field_name);   
            });
                        
            //COUNT
            $('#{$this->id}').data('count', 0);
            //MODAL
            $('#{$this->id}').data('modal_id', '{$this->modal_id}');
            //MOVIMIENTOS
            var array = {$conceptItems};

            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {
                    var type_formula_name = '';
                    
                    switch (array[i]['type_formula']) {
                        case '" . Concept::TYPE_SIMPLE . "':
                            type_formula_name = 'Simple';
                            break;
                        case '" . Concept::TYPE_CONDITIONAL . "':
                            type_formula_name = 'Condicional';
                            break;
                        case '" . Concept::TYPE_INFORMED . "':
                            type_formula_name =  'Informado';
                            break;
                    }
                    
                    var string_analyzed = array[i]['sentence']+ ' '+ array[i]['condition'] + ' '+ array[i]['true_sentence']+' '+array[i]['false_sentence'];
                    var concept_used ='';
                    var a_concept = string_analyzed.split(' ');
                    for(var j=0;j<a_concept.length;j++){
                    
                        if(a_concept[j].trim().substring(0,2)== 'c_'){
                            if(concept_used==''){
                                concept_used += a_concept[j].trim().substring(2,a_concept[j].trim().length);
                            }else{
                                concept_used+= ','+ a_concept[j].trim().substring(2,a_concept[j].trim().length);
                            }
                        }
                    }
                    
                    SIANDictionaryConceptAddItem('{$this->id}', 
                                       array[i]['short_name'], 
                                       array[i]['concept_name'], 
                                       array[i]['type_concept'], 
                                       type_formula_name, 
                                       array[i]['type_formula'],
                                       array[i]['sentence'], 
                                       array[i]['condition'], 
                                       array[i]['true_sentence'], 
                                       array[i]['false_sentence'], 
                                       array[i]['export_plame'], 
                                       array[i]['plame_id'],
                                       array[i]['plame_code'],
                                       array[i]['is_printable'],
                                       array[i]['field'],
                                       array[i]['order'],  
                                       array[i]['errors'],
                                       concept_used);                    
                }                
            }
            else
            {
                $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
            }
            
            SIANDictionaryConceptUpdate('{$this->id}');
            SIANDictionaryConceptBuildScenarioField_selectSimple('#{$this->div_field}','','{$this->id}','{$this->field_input_id}');
            SIANDictionaryConceptBuildPlameCode_selectSimple('#{$this->div_code_plame}','{$this->code_plame_id}');
            $('#{$this->code_plame_id}').attr('disabled', 'disabled');
            unfocusable();
             
        });
        
        $('body').on('click', '#{$this->setting_button_id}', function(e) {
            var a_concept = [];
            
            var concept_edit = '';
            if ($('#{$this->id}').find('input.sdcf-edit').length > 0)
            {
               concept_edit = $('#{$this->id}').find('input.sdcf-edit').val();               
            }
            
            $('#{$this->id}').find('tr').find('.sian-concept-item-short-name').each(function() {
                if(this.value != concept_edit){
                    a_concept.push(this.value);
                }
            });

            var type_formula = $('input:radio[name={$this->type_formula_id}]:checked').val();
            window.typeFormula = type_formula;
            window.concepts = a_concept;
            window.sentence_simple = $('#{$this->sentence_input_id}').val();
            window.condition = $('#{$this->condition_input_id}').val();
            window.true_sentence = $('#{$this->true_sentence_input_id}').val();
            window.false_sentence = $('#{$this->false_sentence_input_id}').val();
            window.concept_name = $('#{$this->concept_name_input_id}').val();
            window.short_name = $('#{$this->short_concept_name_input_id}').val();  
            window.concept_used = $('#{$this->array_concept_input_id}').val();  
        });
        
        $('body').on('click', '#{$this->cancel_button_id}', function(e) {
            {$this->id}SIANDictionaryConceptCleanElements();
        });
        
        $('body').on('click', '#{$this->add_button_id}', function(e) {
            
            var type           = '" . Scenario::TYPE_PAYROLL . "';
            var field          = $('#{$this->field_input_id}').val();               
            var short_name     = $('#{$this->short_concept_name_input_id}').val();            
            var concept_name   = $('#{$this->concept_name_input_id}').val();            
            var type_concept   = $('#{$this->type_concept_input_id}').val();
            var type_formula   = $('input:radio[name={$this->type_formula_id}]:checked').val();
            var sentence       = $('#{$this->sentence_input_id}').val();
            var condition      = $('#{$this->condition_input_id}').val();
            var true_sentence  = $('#{$this->true_sentence_input_id}').val();
            var false_sentence = $('#{$this->false_sentence_input_id}').val();
            var export_plame   = $('#{$this->export_plame_id}').prop('checked')? '1':'0';
            var plame_id   = $('#{$this->code_plame_id}').val();
            var plame_code     = $('#{$this->code_plame_id} option:selected').text();
            var is_printable   = $('#{$this->is_printable_id}').prop('checked')? '1':'0';
            var order          = $('#{$this->order_input_id}').val();
            var concept_used   = $('#{$this->array_concept_input_id}').val();
//            var order    = '';

            if(export_plame == '0'){                
                plame_code  = '';
                plame_id = '';
            }else{
                if(plame_code != ''){
                    plame_code = plame_code.split('|')[0].trim();
                }
            }
            
            if(field != ''){
                field = field.split('-')[1];
            }
  
            if({$this->id}SIANDictionaryConceptValidateFields(true, type, field, short_name, concept_name, type_concept, type_formula, sentence, condition, true_sentence, false_sentence, export_plame, plame_id, plame_code, is_printable, order))
            {   
                short_name = short_name.toUpperCase();
                var type_formula_name = '';                    
                switch (type_formula) {
                    case '" . Concept::TYPE_SIMPLE . "':
                        type_formula_name = 'Simple';
                        break;
                    case '" . Concept::TYPE_CONDITIONAL . "':
                        type_formula_name = 'Condicional';
                        break;
                    case '" . Concept::TYPE_INFORMED . "':
                        type_formula_name =  'Informado';
                        break;
                }
                SIANDictionaryConceptAddItem('{$this->id}', 
                                    short_name,
                                    concept_name, 
                                    type_concept, 
                                    type_formula_name,
                                    type_formula,
                                    sentence, 
                                    condition, 
                                    true_sentence, 
                                    false_sentence, 
                                    export_plame, 
                                    plame_id,
                                    plame_code, 
                                    is_printable,  
                                    field, 
                                    order, 
                                    [],
                                    concept_used);            

                {$this->id}SIANDictionaryConceptCleanElements();
                SIANDictionaryConceptUpdate('{$this->id}');
                SIANDictionaryConceptBuildScenarioField_selectSimple('#{$this->div_field}','','{$this->id}','{$this->field_input_id}');
                unfocusable();
                
            }
        });
        
         $('body').on('click', '#{$this->modify_button_id}', function(e) {
            
            var type           = '" . Scenario::TYPE_PAYROLL . "';
            var field          = $('#{$this->field_input_id}').val();               
            var short_name     = $('#{$this->short_concept_name_input_id}').val();            
            var concept_name   = $('#{$this->concept_name_input_id}').val();            
            var type_concept   = $('#{$this->type_concept_input_id}').val();
            var type_formula   = $('input:radio[name={$this->type_formula_id}]:checked').val();
            var sentence       = $('#{$this->sentence_input_id}').val();
            var condition      = $('#{$this->condition_input_id}').val();
            var true_sentence  = $('#{$this->true_sentence_input_id}').val();
            var false_sentence = $('#{$this->false_sentence_input_id}').val();
            var export_plame   = $('#{$this->export_plame_id}').prop('checked')? '1':'0';
            var plame_id   = $('#{$this->code_plame_id}').val();
            var plame_code     = $('#{$this->code_plame_id} option:selected').text();
            var is_printable   = $('#{$this->is_printable_id}').prop('checked')? '1':'0';
            var order          = $('#{$this->order_input_id}').val();
            var concept_used   = $('#{$this->array_concept_input_id}').val();
//            var order    = 0;
            
            if(export_plame == '0'){                
                plame_code  = '';
                plame_id = '';
            }else{
                if(plame_code != ''){
                    plame_code = plame_code.split('|')[0].trim();
                }
            }

            if(field != ''){
                field = field.split('-')[1];
            }
          
            if({$this->id}SIANDictionaryConceptValidateFields(false, type, field, short_name, concept_name, type_concept, type_formula, sentence, condition, true_sentence, false_sentence, export_plame, plame_code, is_printable, order))
            {   
                short_name = short_name.toUpperCase();
                var type_formula_name = '';                    
                switch (type_formula) {
                    case '" . Concept::TYPE_SIMPLE . "':
                        type_formula_name = 'Simple';
                        break;
                    case '" . Concept::TYPE_CONDITIONAL . "':
                        type_formula_name = 'Condicional';
                        break;
                    case '" . Concept::TYPE_INFORMED . "':
                        type_formula_name =  'Informado';
                        break;
                }                
                    
                $('#{$this->id}').find('tr.sian-concept-item').each(function( index ) {
                    if($(this).find('span.sian-concept-item-order').text() == order){
                        $(this).find('input.sian-concept-item-short-name').val(short_name);
                        $(this).find('input.sian-concept-item-concept-name').val(concept_name);
                        $(this).find('input.sian-concept-item-sentence').val(sentence);
                        $(this).find('input.sian-concept-item-condition').val(condition);
                        $(this).find('input.sian-concept-item-true-sentence').val(true_sentence);
                        $(this).find('input.sian-concept-item-false-sentence').val(false_sentence);
                        $(this).find('input.sian-concept-item-type-concept').val(type_concept);                        
                        $(this).find('input.sian-concept-item-is-printable').val(is_printable);
                        $(this).find('input.sian-concept-item-plame').val(export_plame);
                        $(this).find('input.sian-concept-item-plame-code').val(plame_code);
                        $(this).find('input.sian-concept-item-type-formula-label').val(type_formula_name);
                        $(this).find('input.sian-concept-item-type-formula').val(type_formula);
                        $(this).find('input.sian-concept-item-field').val(field);
                        $(this).find('input.sian-concept-item-concept-used').val(concept_used);
                    }
                });

                {$this->id}SIANDictionaryConceptCleanElements();
                SIANDictionaryConceptUpdate('{$this->id}')
                unfocusable();
            }
        });
        
        $('body').on('click', '#{$this->validate_button_id}', function(e) {
          
                var formula = '';
                var sentence ='';
                var a_result=[];
                var valid =  false;
                var result_validation = '';
                var concepts_used = '';

                if(window.typeFormula == 'simple'){ 

                    a_result = SIANDictionaryConceptGetFormula('{$this->child_modal_id}','sdcf-simple-sentence');
                    formula = a_result[0];
                    concepts_used = a_result[2];                    
                    $('#{$this->sentence_input_id}').val(a_result[1]);
                    $('#{$this->condition_input_id}').val();
                    $('#{$this->true_sentence_input_id}').val();
                    $('#{$this->false_sentence_input_id}').val();

                    try{
                        USMath.calcExpression(formula,false,2);
                        if($('#{$this->sentence_input_id}').val() == ''){                            
                            bootbox.alert(us_message('Debe ingresar una sentencia.', 'warning'));
                            return;
                        }
                        valid = true;
                        $('#{$this->array_concept_input_id}').val(concepts_used);
                    }catch (e) {
                        var s_message = e.toString();
                        bootbox.alert(us_message(s_message, 'error'));
                    }

                }else if(window.typeFormula == 'conditional'){

                    try{
                        a_result = SIANDictionaryConceptGetFormula('{$this->child_modal_id}','sdcf-condition');
                        formula = a_result[0]+ '\\n______________________________________\\n';
                        var condition = a_result[0].replace(/ O /g, ' || ').replace(/ Y /g, ' && ');
                        a_result[1] = a_result[1].replace(/ O /g, ' || ').replace(/ Y /g, ' && ');
                        //alert(condition + '      ' + a_result[1] + '      ' + a_result[2]);                        
                        if(a_result[2]!= ''){
                            concepts_used += a_result[2];                            
                        }
                        USMath.calcExpression(condition,false,2);
                        $('#{$this->condition_input_id}').val(a_result[1]);                        
                        
                        a_result = SIANDictionaryConceptGetFormula('{$this->child_modal_id}','sdcf-true-sentence');
                        formula += a_result[0]+ '\\n';
                        if(a_result[2]!= ''){
                            if(concepts_used!=''){
                                concepts_used += ','+ a_result[2];
                            }else{
                                concepts_used += a_result[2];
                            }
                        }
                        USMath.calcExpression(a_result[0],false,2);
                        $('#{$this->true_sentence_input_id}').val(a_result[1]);                            
                        
                        a_result = SIANDictionaryConceptGetFormula('{$this->child_modal_id}','sdcf-false-sentence');
                        formula += a_result[0];
                        if(a_result[2]!= ''){
                            if(concepts_used!=''){
                                concepts_used += ','+ a_result[2];
                            }else{
                                concepts_used += a_result[2];
                            }
                        }
                        USMath.calcExpression(a_result[0],false,2);
                        $('#{$this->false_sentence_input_id}').val(a_result[1]);                            
                        
                        
                        if($('#{$this->condition_input_id}').val() == ''){
                            bootbox.alert(us_message('Debe ingresar una condición.', 'warning'));
                            return;
                        }
                        if($('#{$this->true_sentence_input_id}').val() == ''){
                            bootbox.alert(us_message('Debe ingresar una sentencia cuando la condición es verdadera.', 'warning'));
                            return;
                        }
                        if($('#{$this->false_sentence_input_id}').val() == ''){
                            bootbox.alert(us_message('Debe ingresar una sentencia cuando la condición es falsa.', 'warning'));                            
                            return;
                        }
                        $('#{$this->array_concept_input_id}').val(concepts_used);
                        valid = true;
                    }catch (e) {
                        var s_message = e.toString();
                        bootbox.alert(us_message(s_message, 'error'));
                    }
                }else if(window.typeFormula == 'informed'){
                    $('#{$this->array_concept_input_id}').val(concepts_used);
                } 
                if(valid){
                    $('#{$this->child_modal_id}').modal('toggle');
                    $('#{$this->textarea_id}').text(formula);
                }            
        });       
        
       ", CClientScript::POS_END);


        Yii::app()->clientScript->registerScript(get_class($this), "
               
        function {$this->id}SIANDictionaryConceptValidateFields(is_New, type, field,  short_name, concept_name, type_concept, type_formula, sentence, condition, true_sentence, false_sentence, export_plame, plame_id, is_printable, order){
            
            if(!field)
            {
                bootbox.alert(us_message('Debe elegir un campo!', 'warning'));
                $('#{$this->field_input_id}').focus();
                return false;
            }
            if(!short_name)
            {
                bootbox.alert(us_message('Ingrese el identificador del concepto!', 'warning'));
                $('#{$this->short_concept_name_input_id}').focus();
                return false;
            }
            $('#{$this->short_concept_name_input_id}').val($('#{$this->short_concept_name_input_id}').val().toUpperCase());
            
            var field_duplicated = false;
            var short_name_duplicated = false;
            if(is_New){
                
                $('#{$this->id}').find('tr.sian-concept-item').each(function( index ) {  
                    
                    if($(this).find('input.sian-concept-item-field').val() == field){
                        field_duplicated = true;
                        return;
                    } 
                    if($(this).find('input.sian-concept-item-short-name').val() == short_name.toUpperCase()){
                        short_name_duplicated = true;
                        return;
                    } 
                });   
                
            }else{
                
                $('#{$this->id}').find('tr.sian-concept-item').each(function( index ) {  
                    
                    var order_item = $(this).find('span.sian-concept-item-order').text();                    
                    
                    if(order_item != order){
                        if($(this).find('input.sian-concept-item-field').val() == field){
                            field_duplicated = true;
                            return;
                        } 
                        if($(this).find('input.sian-concept-item-short-name').val() == short_name.toUpperCase()){
                           short_name_duplicated = true;
                            return;
                        } 
                    }
                }); 
            }
            if(field_duplicated){
                bootbox.alert(us_message('El campo ya fue configurado, debe elegir otro!', 'warning'));
                $('#{$this->field_input_id}').focus();
                return false;
            }
            
            if(short_name_duplicated){
                bootbox.alert(us_message('El identificador ya fue registrado, debe elegir otro!', 'warning'));
                $('#{$this->short_concept_name_input_id}').focus();
                return false;
            }
            
            if(!(/^[a-z0-9A-Z]*$/i.test(short_name))){
                bootbox.alert(us_message('Ingrese sólo letras y Números sin espacios en el Identificador!', 'warning'));
                $('#{$this->short_concept_name_input_id}').focus();
                return false;
            }
            
            if(!concept_name)
            {
                bootbox.alert(us_message('Ingrese el nombre del concepto!', 'warning'));
                $('#{$this->concept_name_input_id}').focus();
                return false;
            }
            
            if(export_plame == '1'){
                if(plame_id == '' || plame_id == null){
                    bootbox.alert(us_message('Debe elegir un código Plame!', 'warning'));
                    $('#{$this->code_plame_id}').focus();
                    return false;                
                }
            }
            if(!type_formula)
            {
                bootbox.alert(us_message('Elige el tipo de fórmula!', 'warning'));
                $('#{$this->concept_name_input_id}').focus();
                return false;
            }
            if(type_formula == 'simple'){
                if(sentence == ''){
                    bootbox.alert(us_message('No se ha configurado la fórmula!', 'warning'));
                    $('#{$this->setting_button_id}').focus();
                    return false;
                }
            }
            if(type_formula == 'conditional'){
                if(condition == '' || true_sentence == '' || false_sentence == ''){
                    bootbox.alert(us_message('No se ha configurado la fórmula condicional!', 'warning'));
                    $('#{$this->setting_button_id}').focus();
                    return false;
                }
            }
            return true;
        }    

        function {$this->id}SIANDictionaryConceptCleanElements(){
              
            $('#{$this->id}').removeAttr('disabled');
            $('#{$this->add_button_id}').attr('disabled', false);
            $('#{$this->modify_button_id}').attr('disabled', true);
            $('#{$this->cancel_button_id}').attr('disabled', true);
            $('#{$this->code_plame_id}').attr('disabled', true);    

                       
            $('#{$this->export_plame_id}').attr('checked', false);
            $('#{$this->is_printable_id}').attr('checked', false);   
                        
            $('#{$this->textarea_id}').text('');
            $('#{$this->sentence_input_id}').val('');
            $('#{$this->condition_input_id}').val('');
            $('#{$this->true_sentence_input_id}').val('');
            $('#{$this->false_sentence_input_id}').val('');
            $('#{$this->array_concept_input_id}').val('');
            $('#{$this->concept_name_input_id}').val('');
            $('#{$this->short_concept_name_input_id}').val('');
            $('#{$this->order_input_id}').val('');
            $('#{$this->field_input_id}').val('0').trigger('change.select2');
            $('#{$this->type_concept_input_id}').prop('selectedIndex',0);
            $('#{$this->code_plame_id}').val('0').trigger('change.select2');
            $('#{$this->type_formula_id}_0').prop('checked',true);
            SIANDictionaryConceptAbilityOption('{$this->id}',true);
            unfocusable();
        }
        
        function {$this->id}SIANDictionaryConceptGetItem(table_id, id)
        {
            if(!$('#' + id).find('a.sdc-get-item').hasAttr('disabled')){
            
                var item =  $('#' + id);
                var table = $('#' + table_id);  
                
                item.css('background-color', 'lightblue');                
                item.find('input.sian-concept-item-short-name').addClass('sdcf-edit');  
                SIANDictionaryConceptBuildScenarioField_selectSimple('#{$this->div_field}',item.find('input.sian-concept-item-field').val(),'{$this->id}','{$this->field_input_id}');
                
                $('#{$this->field_input_id}').val('" . Scenario::TYPE_PAYROLL . "-' + item.find('input.sian-concept-item-field').val()).trigger('change.select2');
                $('#{$this->add_button_id}').attr('disabled', true);
                $('#{$this->modify_button_id}').attr('disabled', false);
                $('#{$this->cancel_button_id}').attr('disabled', false);
                $('#{$this->id}').attr('disabled','disabled');
                $('#{$this->short_concept_name_input_id}').val(item.find('input.sian-concept-item-short-name').val());
                $('#{$this->concept_name_input_id}').val(item.find('input.sian-concept-item-concept-name').val());
                $('#{$this->sentence_input_id}').val(item.find('input.sian-concept-item-sentence').val());
                $('#{$this->condition_input_id}').val(item.find('input.sian-concept-item-condition').val());
                $('#{$this->true_sentence_input_id}').val(item.find('input.sian-concept-item-true-sentence').val());
                $('#{$this->false_sentence_input_id}').val(item.find('input.sian-concept-item-false-sentence').val());
                $('#{$this->type_concept_input_id}').val(item.find('input.sian-concept-item-type-concept').val());
                $('#{$this->order_input_id}').val(item.find('span.sian-concept-item-order').text());
                $('#{$this->array_concept_input_id}').val(item.find('input.sian-concept-item-concept-used').val());

                var is_printable = false;
                if(item.find('input.sian-concept-item-is-printable').val() == '1'){                
                    is_printable = true;
                };

                var export_plame = false;
                if(item.find('input.sian-concept-item-plame').val() == '1'){
                    export_plame = true;
                    $('#{$this->code_plame_id}').val(item.find('input.sian-concept-item-plame-id').val()).trigger('change.select2');   
                }else{
                    $('#{$this->code_plame_id}').val('0').trigger('change.select2');   
                }

                var type_formula = item.find('input.sian-concept-item-type-formula').val();
                var field_code = item.find('input.sian-concept-item-field').val();

                $('#{$this->is_printable_id}').prop( 'checked', is_printable);
                $('#{$this->export_plame_id}').prop( 'checked', export_plame);
                   
                if(export_plame){
                    $('#{$this->code_plame_id}').removeAttr('disabled');
                }else{
                    $('#{$this->code_plame_id}').attr('disabled', 'disabled');                    
                }
                
                switch(type_formula) {
                    case '" . Concept::TYPE_SIMPLE . "': 
                        var formula = item.find('input.sian-concept-item-sentence').val();
                        formula = formula.replace(/p_/g, '').replace(/f_/g, '').replace(/c_/g, '').replace(/n_/g, '').replace(/t_/g, '').replace(/x_/g, '');
                        $('#{$this->textarea_id}').text(formula);
                        $('#{$this->type_formula_id}_0').prop('checked',true);
                        $('#{$this->setting_button_id}').removeAttr('disabled');
                        break;
                    case '" . Concept::TYPE_CONDITIONAL . "':
                        var formula = item.find('input.sian-concept-item-condition').val().replace(/ && /g, ' Y ').replace(/ \|\| /g, ' O ');
                        formula += '\\n______________________________________';
                        formula += '\\n'+ item.find('input.sian-concept-item-true-sentence').val();
                        formula += '\\n'+ item.find('input.sian-concept-item-false-sentence').val();                                      
                        formula = formula.replace(/p_/g, '').replace(/f_/g, '').replace(/c_/g, '').replace(/n_/g, '').replace(/t_/g, '').replace(/x_/g, '');
                        $('#{$this->textarea_id}').text(formula);
                        $('#{$this->type_formula_id}_1').prop('checked',true);
                        $('#{$this->setting_button_id}').removeAttr('disabled');
                        break;
                    case '" . Concept::TYPE_INFORMED . "':
                        $('#{$this->textarea_id}').text('');
                        $('#{$this->type_formula_id}_2').prop('checked',true);
                        $('#{$this->setting_button_id}').attr('disabled',true);
                        break;
                }
                SIANDictionaryConceptAbilityOption(table_id,false);
            }
        }
        
        // LISTA DE CAMPOS - COMBOBOX SIMPLE
        function SIANDictionaryConceptBuildScenarioField_selectSimple(container, code_edit, table_id,field_input_id) {
            var content = $(document.createElement('div'));
            var label = $(document.createElement('label')).addClass('control-label').text('Campo');
            var element = $(document.createElement('select')).addClass('form-control').attr({id:field_input_id, name: field_input_id, style: 'width: 100%;'});

            content.append(label).append(element);
            
            var a_ids = []; 
            
            $('#'+table_id).find('input.sian-concept-item-field').each(function( index ) {
                if(code_edit != $(this).val()){
                    a_ids.push($(this).val());    
                }
            });
            
            $.ajax({
                data: {a_ids: a_ids}, 
                url: '/admin/components/listScenarioField',
                type: 'post',                
                async: false,
                dataType: 'json',
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },                  
                success: function (response) {
                    var data = response.data, l = data.length, i;
                    if (l > 0) {
                        element.append($(document.createElement('option')).attr({value: 0}).text('" . Strings::SELECT_OPTION . "'));
                        for (i = 0; i < l; i++) {
                            element.append($(document.createElement('option')).attr({value: data[i].pk}).text(data[i].label));
                        }
                    }
                    window.active_ajax--;
                },
                error: function(request, status, error) { // if error occured
                    window.active_ajax--;
                    bootbox.alert(us_message(request.responseText, 'error'));
                },                
            });
            $(container).html(content);
             $('#'+field_input_id).select2({
                language: 'en',
                placeholder: '" . Strings::SELECT_OPTION . "'
            });
        }
        
        function SIANDictionaryConceptBuildPlameCode_selectSimple(container, code_plame_id) {
            var content = $(document.createElement('div'));
            var label = $(document.createElement('label')).addClass('control-label').text('Código Plame :');
            var element = $(document.createElement('select')).addClass('form-control').attr({id: code_plame_id, name: code_plame_id, style: 'width: 100%;'});

            content.append(label).append(element);

            $.ajax({
                url: '/admin/components/listPlameCodes',
                type: 'post',
                data: {a_ids : []},
                async: false,
                dataType: 'json',
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },                 
                success: function (response) {
                    var data = response.data, l = data.length, i;
                    if (l > 0) {
                        element.append($(document.createElement('option')).attr({value: ''}).text('" . Strings::SELECT_OPTION . "'));
                        for (i = 0; i < l; i++) {
                            element.append($(document.createElement('option')).attr({value: data[i].pk}).text(data[i].label));
                        }
                    }
                    window.active_ajax--;
                },
                error: function(request, status, error) { // if error occured
                    window.active_ajax--;
                    bootbox.alert(us_message(request.responseText, 'error'));
                },                    
            });
             $(container).html(content);
             $('#'+code_plame_id).select2({
                language: 'en',
                placeholder: '" . Strings::SELECT_OPTION . "'
            });
        }
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array('title' => "Conceptos",
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempConcepts') ? 'us-error' : ''
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-5 col-md-5 col-sm-12 col-xs-12'>";
        echo "<div id='" . $this->div_field . "'>";
        echo "</div>";
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo SIANForm::textFieldNonActive($this->model->getAttributeLabel('concepts.short_name'), null, null, array(
            'id' => $this->short_concept_name_input_id,
            'placeholder' => $this->model->getAttributeLabel('concepts.short_name'),
            'maxlength' => 15,
            'required' => true,
                //'disabled' => true,
        ));
        echo "</div>";
        echo "<div class='col-lg-5 col-md-5 col-sm-4 col-xs-12'>";
        echo SIANForm::textFieldNonActive($this->model->getAttributeLabel('concepts.concept_name'), null, null, array(
            'id' => $this->concept_name_input_id,
            'placeholder' => $this->model->getAttributeLabel('concepts.concept_name'),
            'maxlength' => 50,
            'required' => true,
                //'disabled' => true,
        ));
        echo "</div>";
        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo SIANForm::dropDownListNonActive($this->model->getAttributeLabel('concepts.type_concept'), null, null, Concept::getTypeConcepto(), array(
            'id' => $this->type_concept_input_id,
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
        echo CHtml::label('Imprimir?', $this->is_printable_id);
        echo '<br/>';
        echo CHtml::checkBox(null, 0, array(
            'id' => $this->is_printable_id,
        ));
        echo "</div>";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
        echo CHtml::label('Plame?', $this->export_plame_id);
        echo '<br/>';
        echo CHtml::checkBox(null, 0, array(
            'id' => $this->export_plame_id,
        ));
        echo "</div>";
        echo "<div class='col-lg-7 col-md-7 col-sm-12 col-xs-12'>";
        echo "<div id='" . $this->div_code_plame . "'>";
        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "<br/>";
        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo CHtml::label('Tipo de formulación', $this->type_formula_id);
        echo '<br/>';
        echo CHtml::radioButtonList($this->type_formula_id, Concept::TYPE_SIMPLE, array(
            Concept::TYPE_SIMPLE => "Fórmula Simple",
            Concept::TYPE_CONDITIONAL => "Fórmula Condicional",
            Concept::TYPE_INFORMED => "Informado"), array(
        ));
        echo "</div>";

        echo "<div class='col-lg-9 col-md-9 col-sm-9 col-xs-12'>";
        echo "<div class='row'>";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
        echo CHtml::label('Fórmula', $this->setting_button_id, []);
        echo "<br/>";

        $this->widget('application.widgets.USButton', array(
            'id' => $this->setting_button_id,
            'route' => '/human/dictionary/formulate',
            'title' => 'Editar Fórmula',
            'class' => 'form',
            'block' => true,
            'context' => 'primary',
            'icon' => 'fa fa-lg fa-superscript white',
            'size' => 'default',
            'title' => 'Añadir',
            'data' => array(
                'modal_id' => $this->child_modal_id,
                'parent_id' => $this->modal_id,
                'validate_id' => $this->validate_button_id,
        )));

        echo "</div>";
        echo "<br/>";
        echo "<div class='col-lg-11 col-md-11 col-sm-11 col-xs-12'>";

        echo SIANForm::textAreaNonActive('', 'Formula', '', array('id' => $this->textarea_id,
            'style' => 'height:100px;font-weight: bold;',
            'disabled' => TRUE,
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo CHtml::hiddenField($this->sentence_input_id, '', []);
        echo CHtml::hiddenField($this->condition_input_id, '', []);
        echo CHtml::hiddenField($this->true_sentence_input_id, '', []);
        echo CHtml::hiddenField($this->false_sentence_input_id, '', []);
        echo CHtml::hiddenField($this->order_input_id, '', []);
        echo CHtml::hiddenField($this->array_concept_input_id, '', []);
        echo "</div>";

        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "<br/>";
        echo "<div class='row'>";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
        echo CHtml::label('Agregar', $this->add_button_id, []);
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_button_id,
            'block' => true,
            'context' => 'primary',
            'icon' => 'fa fa-lg fa-plus white',
            'size' => 'default',
            'title' => 'Añadir',
            'class' => 'add',
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";

        echo CHtml::label('Confirmar', $this->modify_button_id, []);
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->modify_button_id,
            'block' => true,
            'context' => 'success',
            'icon' => 'fa fa-lg fa-check white',
            'size' => 'default',
            'title' => 'Confirmar',
            'class' => 'edit',
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
        echo CHtml::label('Cancelar', $this->cancel_button_id, []);
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->cancel_button_id,
            'block' => true,
            'context' => 'danger',
            'icon' => 'fa fa-lg fa-ban white',
            'size' => 'default',
            'title' => 'Cancelar',
        ));
        echo "</div>";
        echo "</div>";

        echo "<hr>";
        echo "<table id='{$this->id}' class='table table-condensed table-hover'>"; //  type='" . $this->model->type . "'
        echo "<thead>";
        echo "<tr>";
        echo "<th width='3%'>#</th>";
        echo "<th width='12%' style='text-transform: uppercase;'>{$this->model->getAttributeLabel('concepts.short_name')}</th>";
        echo "<th width='30%' style='text-transform: uppercase;'>{$this->model->getAttributeLabel('concepts.concept_name')}</th>";
        echo "<th width='12%' style='text-transform: uppercase;'>{$this->model->getAttributeLabel('concepts.type_concept')}</th>";
        echo "<th width='12%' style='text-transform: uppercase;'>{$this->model->getAttributeLabel('concepts.type_formula_label')}</th>";
        echo "<th width='0%' style='text-transform: uppercase;'></th>";
        echo "<th width='10%' style='text-transform: uppercase;'>{$this->model->getAttributeLabel('concepts.plame_code')}</th>";
        echo "<th width='15%' style='text-transform: uppercase;'>{$this->model->getAttributeLabel('concepts.field')}</th>";
        echo "<th width='10%' style='text-transform: uppercase;'>Operaciones</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody></tbody>";
        echo "<tfoot>";
        echo "</tfoot>";
        echo "</table>";
        $this->endWidget();
    }

}
