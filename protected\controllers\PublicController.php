<?php

//class TestController extends SIANWSController {
class PublicController extends USBaseController {

    public $plural = 'Test';
    public $singular = 'Test';

    public function accessRules() {
        return array(
            array('allow',
                'actions' => array('resources', 'generateFAIcon', 'getFAIcon', 'evaluateCreditNoteRequest'),
                'users' => array('*'),
            ),
            array('deny', // deny all users
                'users' => array('*'),
            ),
        );
    }

    public function actionResources($type, $title) {

        $o_model = Resource::model()->findByAttributes([
            'owner' => Organization::OWNER,
            'owner_id' => $this->getOrganization()->organization_id,
            'type' => $type,
            'title' => $title
        ]);

        if (isset($o_model)) {
            $this->redirect($o_model->url);
        } else {
            throw new CHttpException('El recurso buscado no existe.', 404);
        }
    }

    public function actionGenerateFAIcon($icon, $size = 'lg') {

        //https://fontawesome.com/how-to-use/on-the-web/styling/sizing-icons
        $f_left_margin = 1;
        $f_top_margin = 0;
        switch ($size) {
            case 'lg':
                $f_left_margin = 1.33333;
                $f_top_margin = 4;
                break;
            case '2x':
                $f_left_margin = 2;
                break;
            case '3x':
                $f_left_margin = 3;
                break;
            case '3x':
                $f_left_margin = 3;
                break;
            case '4x':
                $f_left_margin = 4;
                break;
            case '5x':
                $f_left_margin = 5;
                break;
        }

        echo $this->renderPartial('_icon', [
            'icon' => $icon,
            'size' => $size,
            'left_margin' => $f_left_margin,
            'top_margin' => $f_top_margin,
            'color' => $this->getOrganization()->globalVar->color,
                ], true);
    }

    public function actionGetFAIcon($icon, $size = 'lg') {

        //Pixeles
        $f_size_em = 1;
        switch ($size) {
            case 'lg':
                $f_size_em = 1.33333;
                break;
            case '2x':
                $f_size_em = 2;
                break;
            case '3x':
                $f_size_em = 3;
                break;
            case '3x':
                $f_size_em = 3;
                break;
            case '4x':
                $f_size_em = 4;
                break;
            case '5x':
                $f_size_em = 5;
                break;
        }

        $s_image_path = Yii::app()->params['org_dir'] . DIRECTORY_SEPARATOR . 'images' . DIRECTORY_SEPARATOR . 'icons' . DIRECTORY_SEPARATOR . 'fontawesome' . DIRECTORY_SEPARATOR . $icon . '_' . $size . '.png';

        if (!file_exists($s_image_path)) {
            $p_s_url = $this->createAbsoluteUrl('generateFAIcon') . "?icon={$icon}&size={$size}";
            USImage::urlToPng($p_s_url, $s_image_path, floor($f_size_em * 16), floor($f_size_em * 16), 100);
        }

        $s_filename = basename($s_image_path);
        $file_extension = strtolower(substr(strrchr($s_filename, "."), 1));

        switch ($file_extension) {
            case "gif": $ctype = "image/gif";
                break;
            case "png": $ctype = "image/png";
                break;
            case "jpeg":
            case "jpg": $ctype = "image/jpeg";
                break;
            default:
        }

        header('Content-type: ' . $ctype);
        readfile($s_image_path);
    }

    public function actionEvaluateCreditNoteRequest($token = null, $state = null) {
        if (!isset($token)) {
            echo "No se encontró un token";
            return;
        }
        if (!isset($state)) {
            echo "No se encontró un estado";
            return;
        }
        if (!in_array($state, [SIANPin::STATE_ACCEPTED, SIANPin::STATE_REJECTED], true)) {
            echo "El estado no es válido";
            return;
        }
        //Decodificamos el token
        $o_token_data = SIANCache::get($token);
        if ($o_token_data === false || !isset($o_token_data->user_id) || !isset($o_token_data->person_name)) {
            echo "Token inválido";
            return;
        }
        //Chequeamos PIN
        $a_pin = SIANPin::getPIN($token);
        if (!isset($a_pin) || $a_pin === false) {
            return $this->_renderInvalidRequest($o_token_data);
        }
        //Verificamos estado anterior del PIN
        switch ($a_pin['state']) {
            case SIANPin::STATE_REJECTED:
                return $this->_renderInvalidRequest($o_token_data, $a_pin);
            case SIANPin::STATE_ACCEPTED:
                goto Valid;
            case SIANPin::STATE_NONE:
                //Actualizamos estado del token
                SIANPin::changePINState($token, $state);
                //
                switch ($state) {
                    case SIANPin::STATE_REJECTED:
                        return $this->_renderInvalidRequest($o_token_data, $a_pin);
                    case SIANPin::STATE_ACCEPTED:
                        goto Valid;
                }
                break;
        }

        Valid:
        //Obtenemos factura
        $o_saleBill = $this->_getSaleBill($o_token_data->movement_code);
        if (!isset($o_saleBill)) {
            echo "Comprobante no existe o es inválido";
            return;
        }
        //Obtenemos usuario
        $o_user = $this->_getUser($o_token_data->user_id);
        if (!isset($o_user)) {
            echo "Usuario no existe o está inactivo";
            return;
        }
        //
        $this->_renderValidRequest($o_token_data, $o_saleBill, $o_user, $a_pin);
    }

    private function _getSaleBill($p_s_movement_code) {

        $o_model = CommercialMovement::model()->with([
                    'movement' => [
                        'select' => [
                            "M.movement_id",
                            "M.movement_code",
                            "M.document_code",
                            "M.document_serie",
                            "M.document_correlative",
                            "M.aux_person_id",
                            "M.emission_date",
                            "M.register_date",
                            "M.currency",
                        ],
                        'alias' => "M",
                        'joinType' => 'join',
                        'on' => "M.`status`",
                        'with' => [
                            'auxPerson' => [
                                'select' => [
                                    "XP.identification_type",
                                    "XP.identification_number",
                                    "XP.person_name"
                                ],
                                'alias' => "XP",
                                'joinType' => 'join',
                            ],
                            'person' => [
                                'select' => [
                                    'P.person_id',
                                    'P.identification_type',
                                    'P.identification_number',
                                    'P.person_name'
                                ],
                                'alias' => 'P',
                                'joinType' => 'join',
                                'with' => [
                                    'user' => [
                                        'select' => ['U.user_id'],
                                        'alias' => 'U',
                                        'joinType' => 'join',
                                    ]
                                ]
                            ],
                            'extraInfo' => [
                                'select' => ['EI.delivery_date', 'EI.kardex_clock', 'EI.shipping_type'],
                                'alias' => 'EI',
                                'joinType' => 'join',
                            ],
                            //Direcciones entrega
                            'addresses' => [
                                'with' => [
                                    'geoloc' => [
                                        'alias' => 'DAG'
                                    ]
                                ],
                            ],
                        ]
                    ]
                ])->find([
            'select' => ['CM.movement_id', 'CM.condition', 'CM.real_pen', 'CM.real_usd'],
            'alias' => 'CM',
            'join' => 'join ' . GlobalVar::model()->tableName() . ' GV',
            'condition' => "M.movement_code = :movement_code AND M.route = :route",
            'params' => [
                ':movement_code' => $p_s_movement_code,
                ':route' => 'commercial/saleBill',
            ]
        ]);

        return $o_model;
    }

    private function _getUser($p_i_user_id) {
        return User::model()->with([
                    'person' => [
                        'select' => ['P.person_id', 'P.identification_type', 'P.person_name'],
                        'alias' => 'P',
                        'joinType' => 'JOIN',
                    ]
                ])->findByAutoIncrement($p_i_user_id, [
                    'select' => [
                        'U.user_id',
                        'U.username',
                        'U.password',
                        'U.person_id',
                    ],
                    'alias' => 'U',
                    'condition' => 'U.`status`'
        ]);
    }

    private function _renderValidRequest($p_o_token, $p_o_saleBill, $p_o_user, $p_a_pin) {

        $a_reasons = (new DpMultitable(Multitable::CATALOG_9))->getListData("value", "description");

        echo $this->renderPartial('//public/template', [
            'icon' => 'check',
            'color' => $this->getOrganization()->globalVar->color,
            'header' => "Solicitud para creación de nota de crédito",
            'view' => '//public/_valid_request',
            'footer' => CHtml::link('Visita nuestra web ', $this->getOrganization()->web, [
                'target' => '_BLANK',
            ]),
            'stores' => Store:: get(Store::MODE_API),
            'data' => [
                'token' => $p_o_token,
                'model' => $p_o_saleBill,
                'user' => $p_o_user,
                'pin' => $p_a_pin,
                'reason' => $a_reasons[$p_o_token->reason_code],
                'seconds' => SIANTime::substract($p_a_pin['expires_at'], SIANTime::now())
            ]], true, true);
    }

    private function _renderInvalidRequest($p_o_token, $p_a_pin = null) {

        if (isset($p_a_pin)) {
            switch ($p_a_pin['state']) {
                case SIANPin::STATE_NONE:
                    $s_header = "Solicitud rechazada con éxito.";
                    break;
                case SIANPin::STATE_REJECTED:
                    $s_header = "Solicitud rechazada previamente.";
                    break;
            }
        } else {
            $s_header = "Solicitud vencida o expirada";
        }

        echo $this->renderPartial('//public/template', [
            'icon' => 'warning',
            'color' => $this->getOrganization()->globalVar->color,
            'header' => $s_header,
            'view' => '//public/_invalid_request',
            'footer' => CHtml::link('Visita nuestra web ', $this->getOrganization()->web, [
                'target' => '_BLANK',
            ]),
            'stores' => Store:: get(Store::MODE_API),
            'data' => [
                'token' => $p_o_token,
                'pin' => $p_a_pin
            ]], true, true);
    }

}
