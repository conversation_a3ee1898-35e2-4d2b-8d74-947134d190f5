<?php

class SIANInventory extends CWidget {

    //CONST
    public $id;
    public $form;
    public $model;
    public $transaction;
    public $reason_items = [];
    public $scenarios_items = [];
    public $state_items = [];
    public $owner_items = [];
    public $warehouse_items = [];
    public $business_unit_items = [];
    public $access_manage_state;
    public $readonly = false;
    public $disabled = false;
    public $required = false;
    public $onChange = '';
    //PRIVATE
    private $controller;
    // cols (div)
    public $col_owner_id;
    public $col_percent_id;
    public $col_product_total_id;
    public $col_reason_annul_id;
    // ObjIds (inputs, selects, etc)
    public $inventory_id;
    public $warehouse_id;
    public $business_unit_id;
    public $scenarios_id;
    public $owner_id;
    public $percent_id;
    public $product_total_id;
    public $with_stock_id;
    public $with_stock_2_id;
    //
    public $switch_with_stock_id;
    public $wrap_switch_with_stock_id;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        // colsIds
        $this->col_owner_id = isset($this->col_owner_id) ? $this->col_owner_id : $this->controller->getServerId();
        $this->col_percent_id = isset($this->col_percent_id) ? $this->col_percent_id : $this->controller->getServerId();
        $this->col_product_total_id = isset($this->col_product_total_id) ? $this->col_product_total_id : $this->controller->getServerId();
        $this->col_reason_annul_id = isset($this->col_reason_annul_id) ? $this->col_reason_annul_id : $this->controller->getServerId();
        // ObjIds
        $this->inventory_id = isset($this->inventory_id) ? $this->inventory_id : $this->controller->getServerId();
        $this->warehouse_id = isset($this->warehouse_id) ? $this->warehouse_id : $this->controller->getServerId();
        $this->business_unit_id = isset($this->business_unit_id) ? $this->business_unit_id : $this->controller->getServerId();
        $this->scenarios_id = isset($this->scenarios_id) ? $this->scenarios_id : $this->controller->getServerId();
        $this->owner_id = isset($this->owner_id) ? $this->owner_id : $this->controller->getServerId();
        $this->percent_id = isset($this->percent_id) ? $this->percent_id : $this->controller->getServerId();
        $this->product_total_id = isset($this->product_total_id) ? $this->product_total_id : $this->controller->getServerId();
        $this->with_stock_id = isset($this->with_stock_id) ? $this->with_stock_id : $this->controller->getServerId();
        $this->with_stock_2_id = isset($this->with_stock_2_id) ? $this->with_stock_2_id : $this->controller->getServerId();
        //
        $this->switch_with_stock_id = isset($this->switch_with_stock_id) ? $this->switch_with_stock_id : $this->controller->getServerId();
        $this->wrap_switch_with_stock_id = isset($this->wrap_switch_with_stock_id) ? $this->wrap_switch_with_stock_id : $this->controller->getServerId();
        // 
        SIANAssets::registerScriptFile('js/sian-inventory.js');

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        var divObj = $('#{$this->id}');
        divObj.data('business_unit_url', '{$this->controller->createUrl('/widget/getBusinessUnit')}');

        //Evento load 
        $(document).ready(function() {
            var objIdsInventory = {};
            objIdsInventory.inventory_id = '{$this->inventory_id}';
            objIdsInventory.warehouse_id = '{$this->warehouse_id}';
            objIdsInventory.business_unit_id = '{$this->business_unit_id}';
            objIdsInventory.scenarios_id = '{$this->scenarios_id}';
            objIdsInventory.owner_id = '{$this->owner_id}';
            objIdsInventory.percent_id = '{$this->percent_id}';
            objIdsInventory.product_total_id = '{$this->product_total_id}';
            objIdsInventory.with_stock_id = '{$this->with_stock_id}';
            objIdsInventory.switch_with_stock_id = '{$this->switch_with_stock_id}';
            objIdsInventory.wrap_switch_with_stock_id = '{$this->wrap_switch_with_stock_id}';
            objIdsInventory.with_stock_2_id = '{$this->with_stock_2_id}';
            
            SIANInventoryIds(objIdsInventory);
            if ($('#{$this->warehouse_id}').val() == ''){
                $('#{$this->business_unit_id}').empty().append('<option value>' + STRINGS_SELECT_OPTION + '</option>'); 
            } else {
                SIANInventoryGetBusinessUnit('{$this->id}', $('#{$this->warehouse_id}').val(), '{$this->model->business_unit_id}');
            }
            SIANInventoryReasonAnul('{$this->col_reason_annul_id}', '" . $this->model->state . "', '" . Inventory::STATE_CANCELED . "');
            SIANInventoryScenarios($('#{$this->scenarios_id}').val(), '" . Inventory::SCENARIO_NORMAL . "', '{$this->col_owner_id}', '{$this->col_percent_id}', '{$this->col_product_total_id}');
            SIANInventoryWithStock('" . $this->model->inventory_id . "', '" . $this->model->with_stock . "', '" . $this->model->scenarios . "');
            

            
        });
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}'>";

            echo "<div class='row'>";
                echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
                echo $this->form->hiddenField($this->model, 'inventory_id', array('id' => $this->inventory_id));
                echo $this->form->textFieldRow($this->model, 'inventory_name', array());
                echo "</div>";

                echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
                echo $this->form->dropDownListRow($this->model, 'inventory_type_id', $this->reason_items, array(
                    'empty' => Strings::SELECT_OPTION,
                    'readonly' => $this->model->owner_count > 0 || $this->model->state != Inventory::STATE_PLANNING,
                ));
                echo "</div>";

                echo "<div id='divObjWarehouse' class='col-lg-3 col-md-3 col-sm-12 col-xs-12'>";
                echo $this->form->dropDownListRow($this->model, 'warehouse_id', $this->warehouse_items, array(
                    'id' => $this->warehouse_id,
                    'class' => 'warehouse_id',
                    'empty' => Strings::SELECT_OPTION,
                    'readonly' => $this->model->owner_count > 0 || $this->model->state != Inventory::STATE_PLANNING,
                    'onchange' => "
                                    if ($(this).val() != null){
                                        if ($(this).val() != ''){
                                            SIANInventoryGetBusinessUnit('{$this->id}', this.value, '{$this->model->business_unit_id}');   
                                        }else{
                                            $('#{$this->business_unit_id}').empty().append('<option value>' + STRINGS_SELECT_OPTION + '</option>');                                  
                                        }
                                    }
                                "
                        ,));
                echo "</div>";

                echo "<div class='col-lg-3 col-md-3 col-sm-12 col-xs-12'>";
                echo $this->form->dropDownListRow($this->model, 'business_unit_id', $this->business_unit_items, array(
                    'id' => $this->business_unit_id,
                    'class' => 'business_unit_id',
                    'empty' => Strings::SELECT_OPTION,
                    'readonly' => $this->model->owner_count > 0 || $this->model->state != Inventory::STATE_PLANNING,
                ));
                echo "</div>";
            echo "</div>";

            echo "<div class='row'>";
                echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
                    echo $this->form->textFieldRow($this->model, 'description', array());
                echo "</div>";

                echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
                    echo $this->form->dropDownListRow($this->model, 'state', $this->state_items, array(
                        'empty' => Strings::SELECT_OPTION,          
                        'readonly' => $this->model->IsNewRecord,
                        'onchange' => "SIANInventoryReasonAnul('{$this->col_reason_annul_id}', $(this).val(), '" . Inventory::STATE_CANCELED . "');"
                    ));
                echo "</div>";

                echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
                echo $this->form->dropDownListRow($this->model, 'scenarios', $this->scenarios_items, array(
                    'id' => $this->scenarios_id,
                    'readonly' => $this->model->state != Inventory::STATE_PLANNING,
                    'onchange' => "SIANInventoryScenarios($(this).val(), '" . Inventory::SCENARIO_NORMAL . "', '{$this->col_owner_id}', '{$this->col_percent_id}', '{$this->col_product_total_id}');",
                ));
                echo "</div>";

                echo "<div id='{$this->col_owner_id}' class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
                echo $this->form->dropDownListRow($this->model, 'owner', $this->owner_items, array(
                    'id' => $this->owner_id,
                    'empty' => Strings::SELECT_OPTION,
                    'readonly' => $this->model->state != Inventory::STATE_PLANNING,
                    'onchange' => "InventoryOnchangeOwner(this.value)",
                ));
                echo "</div>";

                echo "<div id='{$this->col_percent_id}' class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
                echo $this->form->numberFieldRow($this->model, 'percent', array(
                    'id' => $this->percent_id,
                    'readonly' => $this->model->state != Inventory::STATE_PLANNING,
                    'min' => 1,
                    'max' => 100,
                    'step' => 1,
                ));
                echo "</div>";
                
                echo "<div id='{$this->col_product_total_id}' class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
                echo $this->form->numberFieldRow($this->model, 'product_total', array(
                    'id' => $this->product_total_id,
                    'readonly' => true,
                    'step' => 1,
                ));
                echo "</div>";
            echo "</div>";

            echo "<div class='row'>";
                echo "<div class='col-lg-10 col-md-10 col-sm-12 col-xs-12'>";
                $this->widget('application.widgets.USAutocomplete', array(
                    'model' => $this->model,
                    'attribute' => 'person_id',
                    'view' => array(
                        'model' => 'DpUser',
                        'attributes' => array(
                            array('name' => 'person_id', 'width' => 10, 'types' => array('id', 'value', 'aux')),
                            array('name' => 'person_name', 'width' => 90, 'types' => array('text')),
                        ),
                    ),
                ));
                echo "</div>";

                echo "<div class='col-lg-2 col-md-2 col-sm-3 col-xs-12'>";
                $disabledClass = ($this->model->state != Inventory::STATE_PLANNING) ? "disabledbutton" : '';
                echo "<div class='form-group'>";
                    echo "<label class='control-label'>¿Con stock?</label>";
                    echo $this->form->hiddenField($this->model, 'with_stock', array('id' => $this->with_stock_id));
                    echo "<div style='height: 36px; position: relative; display: table; border-collapse: separate;'>";
                        echo "<div class='ll-button ll-button-switch ".$disabledClass."' id='{$this->wrap_switch_with_stock_id}'>";
                            echo "<input id='{$this->switch_with_stock_id}' type='checkbox' class='form-control ll-checkbox'/>";
                            echo "<div class='ll-knobs'></div><div class='ll-layer'></div>";
                        echo "</div>";
                        echo $this->form->hiddenField($this->model, 'with_stock', array('id' => $this->with_stock_2_id));
                    echo "</div>";
                echo "</div>";
                echo "</div>";

                echo "<div id='{$this->col_reason_annul_id}' class='col-lg-9 col-md-9 col-sm-12 col-xs-12'>";
                    echo $this->form->textFieldRow($this->model, 'reason_annul', array());
                echo "</div>";
            echo "</div>";

        echo "</div>";
    }

}
