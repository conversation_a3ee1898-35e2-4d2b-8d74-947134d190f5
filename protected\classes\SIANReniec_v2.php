<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANReniec
 *
 * <AUTHOR>
 */
class SIANReniec_v2
{

    const DNI_URL = 'http://aplicaciones007.jne.gob.pe/srop_publico/Consulta/api/AfiliadoApi/GetNombresCiudadano?DNI=';
    const MAX_DATA_ATTEMPS = 5;
    const VALIDATION_NOT_FOUND = 'DNI no encontrado en Padrón Electoral';
    const NON_EXISTS = 'El DNI no es válido o es de un menor de edad o alguien fallecido.';
    const ERROR_NETWORK = 'Hubo un error al procesar la petición, vuelva a intentarlo después';

    /**
     * Descarga los datos de DNI de RENIEC
     * @param string $p_s_dni DNI a consultar
     * @return string Contenido en formato String
     */
    private static function _downloadDNIData($p_s_dni) {
        //Obtenemos la data con CURL
        $o_ch = curl_init();

        curl_setopt($o_ch, CURLOPT_VERBOSE, false);
        curl_setopt($o_ch, CURLOPT_URL, self::DNI_URL);
        curl_setopt($o_ch, CURLOPT_POST, true);
        curl_setopt($o_ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($o_ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($o_ch, CURLOPT_VERBOSE, 1);
        curl_setopt($o_ch, CURLOPT_HEADER, 1);
        //Ejecutamos
        $a_response = curl_exec($o_ch);
        //Obtenemos la data
        $header_size = curl_getinfo($o_ch, CURLINFO_HEADER_SIZE);
        //Cerramos recursos
        curl_close($o_ch);

        return substr($a_response, $header_size);
    }

    /**
     * Obtiene los datos de una persona desde RENIEC
     * @param string $p_s_dni DNI de la persona a consultar
     * @return array Datos obtenidos o FALSE si el DNI no existe
     * @throws Exception Error de conexión o de procesamiento
     */
    public static function getRawDNIData($p_s_dni) {
        return self::_getRawDNIData($p_s_dni, 0);
    }

    /**
     * Obtiene los datos de una persona desde RENIEC
     * @param string $p_s_dni DNI de la persona a consultar
     * @return array Datos obtenidos o FALSE si el DNI no existe
     * @throws Exception Error de conexión o de procesamiento
     */
    public static function getDNIData($p_s_dni) {

        $a_data = self::getRawDNIData($p_s_dni);

        if ($a_data === false) {
            return false;
        }

        //
        $a_fdata = [];
        $a_fdata['identification_type'] = USIdentificationValidator::DNI;
        $a_fdata['identification_number'] = $p_s_dni;
        $a_fdata['paternal'] = $a_data['paternal'];
        $a_fdata['maternal'] = $a_data['maternal'];
        $a_fdata['firstname'] = $a_data['firstname'];
        $a_fdata['person_name'] = $a_data['paternal'] . ',' . $a_data['maternal'] . ',' . $a_data['firstname'];

        return $a_fdata;
    }

    /**
     * Obtiene los datos de una persona desde RENIEC
     * @param string $p_s_dni DNI de la persona a consultar
     * @param integer $p_i_attempt Número de intento
     * @return array Datos obtenidos o FALSE si el DNI no existe
     * @throws Exception Error de conexión o de procesamiento
     */
    private static function _getRawDNIData($p_s_dni, $p_i_attempt) {

        //Descargamos la data
        $s_data = self::_downloadDNIData($p_s_dni);
        //Verificamos si está en el padrón
        foreach (explode('|', $s_data) as $s_word) {
            if (trim($s_word) === self::VALIDATION_NOT_FOUND) {
                return false;
            }
        }
        //Convertimos el DOM a un objeto
        $o_dom = USHtml::getDOM($s_data);


        $o_title = $o_dom->find('h1', 0);
        if (isset($o_title) && $o_title->plaintext === self::$error) {
            return false;
        } else {
            $a_parts = explode('|', $o_dom->plaintext);

            return [
                'dni' => $p_s_dni,
                'name' => implode(' ', $a_parts),
                'paternal' => $a_parts[0],
                'maternal' => $a_parts[1],
                'firstname' => $a_parts[2],
            ];
        }
    }

    /**
     * Determina si el DNI existe o no
     * @param string $p_s_dni DNI
     * @param array $p_a_data (Opcional) Array dónde se guardarán los datos del RUC
     * @return boolean TRUE si existe o FALSE si no
     */
    public static function existsDNI($p_s_dni, &$p_a_data = null) {

        $p_a_data = self::getDNIData($p_s_dni);

        //No existe
        if ($p_a_data === false) {
            return $p_a_data;
        }

        return true;
    }

}
