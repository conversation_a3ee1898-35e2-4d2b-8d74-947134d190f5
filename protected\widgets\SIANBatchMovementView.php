<?php

class SIANBatchMovementView extends CWidget {

    public $id;
    public $model;
    public $dataProvider;
    public $title = 'Detalle de movimiento por Lotes';
    public $modal_id = null;
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //CONTROL
        if (is_null($this->dataProvider)) {
            throw new Exception("Debe especificar el dataProvider!");
        }
        $this->dataProvider->pagination = false;
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->dataProvider->getTotalItemCount() > 0) {

            //Accesos
            $grid_id = $this->id;
            //GRID
            $columns = [];

            array_push($columns, array(
                'name' => 'row_number',
                'headerHtmlOptions' => array('style' => 'width:5%; text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center;'),
                'type' => 'raw',
                'value' => function($row) {
                    return $row->row_number;
                },
            ));

            array_push($columns, array(
                'name' => 'batch_id',
                'headerHtmlOptions' => array('style' => 'width:5%; text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center'),
                'type' => 'raw',
                'value' => function($row) {
                    return $row->batch_id;
                },
            ));

            array_push($columns, array(
                'name' => 'batch_code',
                'headerHtmlOptions' => array('style' => 'width:20%;text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center'),
                'type' => 'raw',
                'value' => function($row) {
                    return $row->batch_code;
                },
            ));

            array_push($columns, array(
                'name' => 'expiration_date',
                'headerHtmlOptions' => array('style' => 'width:20%; text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center;'),
                'type' => 'raw',
                'value' => function($row) {
                    return $row->expiration_date;
                },
            ));

            array_push($columns, array(
                'name' => 'pres_quantity',
                'headerHtmlOptions' => array('style' => 'width:20%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right'),
                'type' => 'raw',
                'value' => function($row) {
                    return $row->pres_quantity;
                },
            ));

            array_push($columns, array(
                'name' => 'measure_name',
                'headerHtmlOptions' => array('style' => 'width:20%;text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center'),
                'type' => 'raw',
                'value' => function($row) {
                    return isset($row->measure_name) ? $row->measure_name : Yii::app()->controller->getDefaultMerchandiseMeasure()->abbreviation;
                },
            ));

            $gridParams = array(
                'id' => $grid_id,
                'type' => 'hover condensed',
                'dataProvider' => $this->dataProvider,
                'enableSorting' => true,
                'selectableRows' => 0,
                'columns' => $columns,
                'template' => '{items}',
                'nullDisplay' => Strings::NONE,
            );

            $this->widget('application.widgets.USGridView', $gridParams);
        }
    }

}
