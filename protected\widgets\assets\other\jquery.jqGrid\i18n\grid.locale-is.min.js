(function(a){a.jgrid=a.jgrid||{};a.extend(a.jgrid,{defaults:{recordtext:"Skoða {0} - {1} af {2}",emptyrecords:"Engar færslur",loadtext:"Hleður...",pgtext:"Síða {0} af {1}"},search:{caption:"Leita...",Find:"Leita",Reset:"Endursetja",odata:["sama og","ekki sama og","minna en","minna eða jafnt og","stærra en","stærra eða jafnt og","byrjar á","byrjar ekki á","er í","er ekki í","endar á","endar ekki á","inniheldur","inniheldur ekki"],groupOps:[{op:"AND",text:"allt"},{op:"OR",text:"eða"}],matchText:" passar",rulesText:" reglur"},edit:{addCaption:"Bæta við færslu",editCaption:"<PERSON>rey<PERSON> færslu",bSubmit:"Vista",bCancel:"Hætta við",bClose:"Loka",saveData:"Gögn hafa breyst! Vista breytingar?",bYes:"Já",bNo:"Nei",bExit:"Hætta við",msg:{required:"Reitur er nauðsynlegur",number:"Vinsamlega settu inn tölu",minValue:"gildi verður að vera meira en eða jafnt og ",maxValue:"gildi verður að vera minna en eða jafnt og ",email:"er ekki löglegt email",integer:"Vinsamlega settu inn tölu",date:"Vinsamlega setti inn dagsetningu",url:"er ekki löglegt URL. Vantar ('http://' eða 'https://')",nodefined:" er ekki skilgreint!",novalue:" skilagildi nauðsynlegt!",customarray:"Fall skal skila fylki!",customfcheck:"Fall skal vera skilgreint!"}},view:{caption:"Skoða færslu",bClose:"Loka"},del:{caption:"Eyða",msg:"Eyða völdum færslum ?",bSubmit:"Eyða",bCancel:"Hætta við"},nav:{edittext:" ",edittitle:"Breyta færslu",addtext:" ",addtitle:"Ný færsla",deltext:" ",deltitle:"Eyða færslu",searchtext:" ",searchtitle:"Leita",refreshtext:"",refreshtitle:"Endurhlaða",alertcap:"Viðvörun",alerttext:"Vinsamlega veldu færslu",viewtext:"",viewtitle:"Skoða valda færslu"},col:{caption:"Sýna / fela dálka",bSubmit:"Vista",bCancel:"Hætta við"},errors:{errcap:"Villa",nourl:"Vantar slóð",norecords:"Engar færslur valdar",model:"Lengd colNames <> colModel!"},formatter:{integer:{thousandsSeparator:" ",defaultValue:"0"},number:{decimalSeparator:".",thousandsSeparator:" ",decimalPlaces:2,defaultValue:"0.00"},currency:{decimalSeparator:".",thousandsSeparator:" ",decimalPlaces:2,prefix:"",suffix:"",defaultValue:"0.00"},date:{dayNames:["Sun","Mán","Þri","Mið","Fim","Fös","Lau","Sunnudagur","Mánudagur","Þriðjudagur","Miðvikudagur","Fimmtudagur","Föstudagur","Laugardagur"],monthNames:["Jan","Feb","Mar","Apr","Maí","Jún","Júl","Ágú","Sep","Oct","Nóv","Des","Janúar","Febrúar","Mars","Apríl","Maí","Júný","Júlý","Ágúst","September","Október","Nóvember","Desember"],AmPm:["am","pm","AM","PM"],S:function(b){return b<11||b>13?["st","nd","rd","th"][Math.min((b-1)%10,3)]:"th"},srcformat:"Y-m-d",newformat:"d/m/Y",masks:{ISO8601Long:"Y-m-d H:i:s",ISO8601Short:"Y-m-d",ShortDate:"n/j/Y",LongDate:"l, F d, Y",FullDateTime:"l, F d, Y g:i:s A",MonthDay:"F d",ShortTime:"g:i A",LongTime:"g:i:s A",SortableDateTime:"Y-m-d\\TH:i:s",UniversalSortableDateTime:"Y-m-d H:i:sO",YearMonth:"F, Y"},reformatAfterEdit:false},baseLinkUrl:"",showAction:"",target:"",checkbox:{disabled:true},idName:"id"}})})(jQuery);