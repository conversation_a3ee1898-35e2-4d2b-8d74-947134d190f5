Options +FollowSymLinks
IndexIgnore */*
<IfModule mod_rewrite.c>
RewriteEngine on

# Remove URL
# RewriteCond %{SERVER_NAME} !localhost
# RewriteCond %{SERVER_NAME} !sian

 RewriteCond %{SERVER_NAME} siansystem.com

 RewriteCond %{SERVER_PORT} 80 
 RewriteCond %{REQUEST_URI} admin 
 RewriteRule ^(.*)$ https://%{SERVER_NAME}/admin/$1 [R,L]

# if a directory or a file exists, use it directly
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# otherwise forward it to index.php
RewriteRule . index.php
</IfModule>

# Header always set Access-Control-Allow-Origin "*" 
