<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'viensac.siansystem.com/admin';
$domain = 'https://viensac.siansystem.com';
$domain2 = 'http://viensac.siansystem.com';
$report_domain = 'rviensac.siansystem.com';
$org = 'viensac';
//SIAN 2
$api_sian = 'https://api.siansystem.com/';
//database enterprise
$database_server = '161.132.48.88';
$database_name = 'viensac';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_EFACT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = 'viensac.p12';
$e_billing_certificate_pass = 'sQFu9AWnBzpSza7q';//PIN
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20481242836VIENSAC1',//usuario
        'password' => 'Conta147_'
    ],
    YII_OSE_EFACT => [
        'username' => '20481242836',
        'password' => '0Lc9jz9i48'
    ]
];
$smtp_username = '<EMAIL>';
$smtp_password = '761esanaf22im6';
$environment_reports = YII_ENVIRONMENT_PRODUCTION;
$environment = YII_ENVIRONMENT_PRODUCTION;
