<?php

class SIANSunatApi {

    private $path;
    private $token = null;

    public function __construct($s_path, $s_token = null) {
        $this->path = $s_path;
        $this->token = $s_token;
    }

    public function call($a_params, $p_s_type = "", $p_is_post = true) {

        $a_response = [];
        $a_header = [];

        if ($p_is_post) {
            if ($p_s_type == 'JSON') {
                $a_post_data = json_encode($a_params);
                $a_header = ['Content-Type: application/json'];
            } else {
                $a_post_data = http_build_query($a_params);
                $a_header = ['Content-Type: application/x-www-form-urlencoded'];
            }
        }
        if ($this->token) {
            $a_header [] = 'Authorization: Bearer  ' . $this->token;
        }
        try {
            $o_curl = curl_init();
            curl_setopt($o_curl, CURLOPT_URL, $this->path);
            curl_setopt($o_curl, CURLOPT_HTTPHEADER, $a_header);
            if ($p_is_post) {
                curl_setopt($o_curl, CURLOPT_POST, $p_is_post);
                curl_setopt($o_curl, CURLOPT_POSTFIELDS, $a_post_data);
            }
            curl_setopt($o_curl, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($o_curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($o_curl, CURLOPT_USERAGENT, "PostmanRuntime/7.32.3");
            curl_setopt($o_curl, CURLOPT_USERAGENT, "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:35.0) Gecko/20100101 Firefox/35.0");

            $s_response = curl_exec($o_curl);
            $s_err = curl_error($o_curl);
            curl_close($o_curl);

            if ($s_err) {
                $a_response = [
                    'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                    'message' => $s_err,
                ];
            } else {
                $a_response = (array) json_decode($s_response);

                if (count($a_response) > 0) {

                    $a_response = [
                        'code' => USREST::CODE_SUCCESS,
                        'message' => USREST::SUCCESS_MESSAGE,
                        'data' => $a_response
                    ];
                } else {
                    $a_response = [
                        'code' => USREST::CODE_NOT_FOUND,
                        'message' => 'No se encontraron resultados',
                    ];
                }
            }
        } catch (\Throwable $th) {
            throw $th;
        }
        return $a_response;
    }

}
