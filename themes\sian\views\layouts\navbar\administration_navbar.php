<?php

$arrayItemsNavbar = array(
    // array('icon' => 'home', 'label' => 'Datos generales', 'url' => '#', 'items' => array(
    array('label' => 'Datos generales', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'star', 'label' => 'Organización', 'route' => 'organization/index'),
                array('icon' => 'barcode', 'label' => 'Unidades de negocio', 'route' => 'businessUnit/index'),
                array('icon' => 'shopping-cart', 'label' => 'Tiendas', 'route' => 'store/index'),
            ),
            array(
                array('icon' => 'list', 'label' => 'Grupos de objetos', 'route' => 'ownerGroup/index'),
                array('icon' => 'list', 'label' => 'Configuración de reportes', 'route' => 'crossReport/index'),
            ),
            array(
                array('icon' => 'user', 'label' => 'Personas', 'route' => 'person/index'),
                array('icon' => 'user', 'label' => 'Usuarios WEB', 'route' => 'webUser/index'),
            )
        )),
    // array('icon' => 'cog', 'label' => 'Procesos', 'url' => '#', 'items' => array(
    array('label' => 'Procesos', 'url' => '#', 'items' => array(
            array('icon' => 'ok', 'label' => 'Confirmación de Permisos y Transiciones', 'route' => 'confirmation/index'),
            array('icon' => 'book', 'label' => 'Libro de Reclamaciones (Responder)', 'route' => 'claim/index'),
        )),
    // array('icon' => 'eye-open', 'label' => 'Monitoreo', 'url' => '#', 'items' => array(
    array('label' => 'Monitoreo', 'url' => '#', 'items' => array(
            array('icon' => 'file', 'label' => 'Log de Lockers', 'route' => 'lockerlog/index'),
        )),
    // array('icon' => 'file', 'label' => 'Reportes', 'url' => '#', 'items' => array(array(
    array('label' => 'Reportes', 'url' => '#', 'items' => array(array(
                array('icon' => 'list', 'label' => 'Reporte Gerencial de Ventas', 'route' => 'report/salesManagement'),
                array('icon' => 'list', 'label' => 'Reporte Gráfico de Ventas', 'route' => 'report/salesManagementCharts'),
                array('icon' => 'list', 'label' => 'Consolidado de Saldos Diarios', 'route' => 'report/banksConsolidated')
            )))
);

$this->widget('application.widgets.SIANNavbar', array(
    'brand' => Yii::app()->id,
    'class' => 'navbar-administration',
    'items' => $this->items_menu, //$arrayItemsNavbar,
));
