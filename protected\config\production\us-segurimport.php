<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'segurimport.siansystem.com/admin';
$domain = 'https://segurimport.siansystem.com';
$domain2 = 'http://segurimport.siansystem.com';
$report_domain = 'rsegurimport.siansystem.com';
$org = 'segurimport';
//SIAN 2
$domain_react_app = 'https://segurimport2.siansystem.com';
$api_sian = 'https://api.siansystem.com/';
//database enterprise
$database_server = '161.132.48.88';
$database_name = 'segurimport';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = 'segurimport.p12';
$e_billing_certificate_pass = 'iweL/[mT66Shkbqw';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20477239901BILLING7',
        'password' => 'Billing7'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = '75neazvim4w4f0';
$environment_reports = YII_ENVIRONMENT_PRODUCTION;
$environment = YII_ENVIRONMENT_PRODUCTION;
$admin_emails = array(
    'Segurimport SIAN' => '<EMAIL>',
);
