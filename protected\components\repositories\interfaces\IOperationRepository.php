<?php

interface IOperationRepository {

    /**
     * @param int $scenarioId
     * @param int|null $internal
     * @return array|null
     */
    public function getFirstOperation($scenarioId, $internal = null, $operationCode = null, $default = null);

    /**
     * @param int $scenarioId
     * @param int|null $internal
     * @return array
     */
    public function getAllOperations($scenarioId, $internal = null, $operationCode = null, $default = null);
}
