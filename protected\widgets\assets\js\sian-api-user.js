function SIANApiUserInit(div_id, variants)
{
    var divObj = $('#' + div_id);
    var tableObj = $('#' + div_id + '_table');
    //Tipos de destino
    if (variants.length > 0) {
        for (var i = 0; i < variants.length; i++)
        {
            SIANApiUserAddVariant(div_id, variants[i]['api_user_variant_id'], variants[i]['variant_code'], variants[i]['variant_name'], variants[i]['default'], variants[i]['store_id'], variants[i]['warehouse_id'], variants[i]['supplies_warehouse_id'], variants[i]['cashbox_id'], variants[i]['destination_cashbox_id'], variants[i]['business_unit_id'], variants[i]['default_price'], variants[i]['adminVariants'], variants[i]['cashboxVariants'], variants[i]['is_removable'], variants[i]['has_admin_variants'], variants[i]['has_cashbox_variants'], variants[i]['use_shift'], variants[i]['sales_without_stock'], variants[i]['errors']);
        }
    } else
    {
        tableObj.find('tbody.sian-api-user-variant-tbody').html(SIANApiUserGetNullRow());
    }
    //Seteamos contador
    divObj.data('count', variants.length);
    SIANApiUserPaintVariants(div_id);

    unfocusable();
}

function SIANApiUserAddVariant(div_id, api_user_variant_id, variant_code, variant_name, xdefault, store_id, warehouse_id, supplies_warehouse_id, cashbox_id, destination_cashbox_id, business_unit_id, default_price, adminVariants, cashboxVariants, is_removable, has_admin_variants, has_cashbox_variants, use_shift, sales_without_stock, errors)
{
    //DIV
    var divObj = $('#' + div_id);
    var tableObj = $('#' + div_id + '_table');
    var count = parseInt(divObj.data('count'));
    var disabled = divObj.data('disabled');

    if (!disabled)
    {
        var row_id = getLocalId();
        var variant_code_id = getLocalId();

        //HTML
        var row = '';
        row += '<tr id="' + row_id + '" class="sian-api-user-variant">';

        row += '<td class="form-group ' + (errors['variant_code'] ? 'has-error' : '') + '">';
        row += '<input class="sian-api-user-variant-api-user-variant-id" type="hidden" name="ApiUserVariant[' + row_id + '][api_user_variant_id]" value="' + api_user_variant_id + '">';
        row += '<input id="' + variant_code_id + '" class="form-control sian-api-user-variant-code" type="text" name="ApiUserVariant[' + row_id + '][variant_code]" value="' + variant_code + '" placeholder="Ingrese código de dispositivo">';
        if (errors['variant_code'])
        {
            row += '<span class="help-block error">' + errors['variant_code'] + '</span>';
        }
        row += '</td>';

        //Nombre variante
        row += '<td class="form-group ' + (errors['variant_name'] ? 'has-error' : '') + '">';
        row += '<input id="' + variant_code_id + '" class="form-control sian-api-user-variant-variant_name" type="text" name="ApiUserVariant[' + row_id + '][variant_name]" value="' + variant_name + '" placeholder="Ingrese nombre de variante">';
        if (errors['variant_name'])
        {
            row += '<span class="help-block error">' + errors['variant_name'] + '</span>';
        }
        row += '</td>';
        //Fin Nombre variante

        row += '<td class="form-group ' + (errors['default'] ? 'has-error' : '') + '" style="text-align:center">';
        row += '<input class="sian-api-user-variant-default" type="radio" name="ApiUserVariant[' + row_id + '][default]" value=1 ' + (xdefault == 1 ? 'checked' : '') + '>';
        if (errors['default'])
        {
            row += '<span class="help-block error">' + errors['default'] + '</span>';
        }
        row += '</td>';

        row += '<td class="form-group ' + (errors['store_id'] ? 'has-error' : '') + '">';
        row += '<select class="form-control sian-api-user-variant-store-id" name="ApiUserVariant[' + row_id + '][store_id]" onChange="SIANApiUserChangeStore(\'' + div_id + '\', \'' + row_id + '\')">' + SIANApiUserGetStoreOptions(div_id, store_id) + '</select>';
        if (errors['store_id'])
        {
            row += '<span class="help-block error">' + errors['store_id'] + '</span>';
        }
        row += '</td>';

        row += '<td class="form-group ' + (errors['warehouse_id'] ? 'has-error' : '') + '">';
        row += '<select class="form-control sian-api-user-variant-warehouse-id" name="ApiUserVariant[' + row_id + '][warehouse_id]">' + SIANApiUserGetWarehouseOptions(div_id, store_id, warehouse_id) + '</select>';
        if (errors['warehouse_id'])
        {
            row += '<span class="help-block error">' + errors['warehouse_id'] + '</span>';
        }
        row += '</td>';

        row += '<td class="form-group ' + (errors['supplies_warehouse_id'] ? 'has-error' : '') + '">';
        row += '<select class="form-control sian-api-user-variant-supplies-warehouse-id" name="ApiUserVariant[' + row_id + '][supplies_warehouse_id]">' + SIANApiUserGetWarehouseOptions(div_id, store_id, supplies_warehouse_id) + '</select>';
        if (errors['supplies_warehouse_id'])
        {
            row += '<span class="help-block error">' + errors['supplies_warehouse_id'] + '</span>';
        }
        row += '</td>';

        row += '<td class="form-group ' + (errors['cashbox_id'] ? 'has-error' : '') + '">';
        row += '<select class="form-control sian-api-user-variant-cashbox-id" name="ApiUserVariant[' + row_id + '][cashbox_id]">' + SIANApiUserGetCashboxOptions(div_id, store_id, cashbox_id) + '</select>';
        if (errors['cashbox_id'])
        {
            row += '<span class="help-block error">' + errors['cashbox_id'] + '</span>';
        }
        row += '</td>';

        row += '<td class="form-group ' + (errors['destination_cashbox_id'] ? 'has-error' : '') + '">';
        row += '<select class="form-control sian-api-user-variant-destination-cashbox-id" name="ApiUserVariant[' + row_id + '][destination_cashbox_id]">' + SIANApiUserGetDestinationCashboxOptions(div_id, store_id, destination_cashbox_id) + '</select>';
        if (errors['destination_cashbox_id'])
        {
            row += '<span class="help-block error">' + errors['destination_cashbox_id'] + '</span>';
        }
        row += '</td>';

        row += '<td class="form-group ' + (errors['business_unit_id'] ? 'has-error' : '') + '">';
        row += '<select class="form-control sian-api-user-variant-business-unit-id" name="ApiUserVariant[' + row_id + '][business_unit_id]">' + SIANApiUserGetBusinessUnits(div_id, business_unit_id) + '</select>';
        if (errors['business_unit_id'])
        {
            row += '<span class="help-block error">' + errors['business_unit_id'] + '</span>';
        }
        row += '</td>';

        row += '<td class="form-group ' + (errors['default_price'] ? 'has-error' : '') + '">';
        row += '<select class="form-control sian-api-user-variant-default-price" name="ApiUserVariant[' + row_id + '][default_price]">' + SIANApiUserGetDefaultPrice(div_id, default_price) + '</select>';
        ;
        if (errors['default_price'])
        {
            row += '<span class="help-block error">' + errors['default_price'] + '</span>';
        }
        row += '</td>';

        row += '<td>';
        row += '<input class="sian-api-user-variant-is-removable" type="hidden" name="ApiUserVariant[' + row_id + '][is_removable]" value="' + is_removable + '"/>';
        row += '<input class="sian-api-user-variant-has-admin-variants" type="checkbox" title="Mostrar/ocultar administradores" name="ApiUserVariant[' + row_id + '][has_admin_variants]" value="1" ' + (has_admin_variants == 1 ? 'checked' : '') + ' onclick="SIANApiUserTriggerAdminVariantTable(\'' + div_id + '\', \'' + row_id + '\', this.checked, true)"/> ';
        row += '<input class="sian-api-user-variant-has-cashbox-variants" type="checkbox" title="Mostrar/ocultar variantes de caja" name="ApiUserVariant[' + row_id + '][has_cashbox_variants]" value="1" ' + (has_cashbox_variants == 1 ? 'checked' : '') + ' onclick="SIANApiUserTriggerCashboxVariantTable(\'' + div_id + '\', \'' + row_id + '\', this.checked, true)"/> ';
        row += '</td>';

        row += '<td>';
        row += '<input class="sian-api-user-variant-use-shift" type="checkbox" title="Maneja turnos" name="ApiUserVariant[' + row_id + '][use_shift]" value="1" ' + (use_shift == 1 ? 'checked' : '') + '/> ';
        row += '<input class="sian-api-user-variant-sales-without-stock" type="checkbox" title="Ventas sin stock?" name="ApiUserVariant[' + row_id + '][sales_without_stock]" value="1" ' + (sales_without_stock == 1 ? 'checked' : '') + '/> ';
        row += '</td>';

        row += '<td>';
        if (is_removable == 1)
        {
            row += '<a title="Remover" onclick="SIANApiUserRemoveVariant(\'' + div_id + '\', \'' + row_id + '\', true); SIANApiUserPaintVariants(\'' + div_id + '\');"><span class="fa fa-times fa-lg black"></span></a>';
        } else
        {
            row += '<span class="fa fa-times fa-lg grey">';
        }
        row += '</td>';

        row += '</tr>';


        //COUNT DE ITEMS
        if (count === 0)
        {
            tableObj.find('tbody.sian-api-user-variant-tbody').html(row);
        } else
        {
            tableObj.find('tbody.sian-api-user-variant-tbody').append(row);
        }

        //
        divObj.data('count', count + 1);
        //
        $('#' + row_id).data('adminVariants', adminVariants);
        $('#' + row_id).data('cashboxVariants', cashboxVariants);
        //Si tiene variantes de caja
        SIANApiUserTriggerAdminVariantTable(div_id, row_id, has_admin_variants == 1, false);
        SIANApiUserTriggerCashboxVariantTable(div_id, row_id, has_cashbox_variants == 1, false);
        //Seteamos error a la tabla
        SIANApiUserSetAdminTableError(div_id, row_id, errors['tempAdminVariants']);
        SIANApiUserSetCashboxTableError(div_id, row_id, errors['tempCashboxVariants']);
        //
        $('#' + variant_code_id).focus();

    } else
    {
        bootbox.alert(us_message('No se puede \'Agregar\' en este momento porque el componente está deshabilitado.', 'warning'));
    }
}

function SIANApiUserTriggerAdminVariantTable(div_id, row_id, visible, is_new)
{
    var divObj = $('#' + div_id);
    var rowObj = $('#' + row_id);
    //Tiene tabla
    if (visible == 1)
    {
        var html = SIANApiUserGetAdminTableHtml(div_id, row_id);
        //Seteamos después
        rowObj.after(html);

        //Agregamos variantes de caja
        var adminTableObj = $('#' + row_id + '_admin_table');
        //Seteamos contador
        adminTableObj.data('count', 0);
        //
        adminTableObj.find('thead th').each(function (index) {
            var thObj = $(this);
            //Vemos si está para required o no        
            if (thObj.html().length > 0) {
                thObj.makeHeaderRequired(true);
            }
        });
        //
        var adminVariants = rowObj.data('adminVariants');
        if (adminVariants.length > 0)
        {
            for (var i = 0; i < adminVariants.length; i++)
            {
                SIANApiUserAddAdminVariant(div_id, row_id, adminVariants[i]['api_user_admin_variant_id'], adminVariants[i]['person_id'], adminVariants[i]['email_address'], adminVariants[i]['phone_number'], adminVariants[i]['errors']);
            }
        } else
        {
            if (is_new) {
                SIANApiUserAddAdminVariant(div_id, row_id, '', '', '', '', []);
            } else {
                adminTableObj.find('tbody.sian-api-user-admin-variant-tbody').html(SIANApiUserGetNullRow());
            }
        }
    } else
    {
        var adminTableObj = $('#' + row_id + '_admin_table');
        //
        var xcontinue = true;
        if (is_new) {
            xcontinue = adminTableObj.data('count') === 0 || confirm("Ha elegido dejar de ver los administradores de esta variante. Esto eliminará las que ya ha agregado. ¿Está seguro de continuar?");
        }

        //Si debe continuar
        if (xcontinue) {
            $('#' + row_id + '_admin_row').remove();
        } else {
            rowObj.find('input.sian-api-user-variant-has-admin-variants').prop('checked', true);
        }
    }

    if (rowObj.hasClass('success')) {
        $('#' + row_id + '_admin_row').addClass('success');
    } else if (rowObj.hasClass('info')) {
        $('#' + row_id + '_admin_row').addClass('info');
    }
}

function SIANApiUserTriggerCashboxVariantTable(div_id, row_id, visible, is_new)
{
    var divObj = $('#' + div_id);
    var rowObj = $('#' + row_id);
    //Tiene tabla
    if (visible == 1)
    {
        var html = SIANApiUserGetCashboxTableHtml(div_id, row_id);
        //Seteamos después
        var adminRow = $('#' + row_id + '_admin_row');
        if (adminRow.length > 0) {
            adminRow.after(html);
        } else {
            rowObj.after(html);
        }

        //Agregamos variantes de caja
        var cashboxTableObj = $('#' + row_id + '_cashbox_table');
        //Seteamos contador
        cashboxTableObj.data('count', 0);
        //
        cashboxTableObj.find('thead th').each(function (index) {
            var thObj = $(this);
            //Vemos si está para required o no        
            if (thObj.html().length > 0) {
                thObj.makeHeaderRequired(true);
            }
        });
        //
        var cashboxVariants = rowObj.data('cashboxVariants');
        if (cashboxVariants.length > 0)
        {
            for (var i = 0; i < cashboxVariants.length; i++)
            {
                SIANApiUserAddCashboxVariant(div_id, row_id, cashboxVariants[i]['api_user_cashbox_variant_id'], cashboxVariants[i]['type'], cashboxVariants[i]['currency'], cashboxVariants[i]['card_type'], cashboxVariants[i]['agreement_id'], cashboxVariants[i]['cashbox_id'], cashboxVariants[i]['errors']);
            }
        } else
        {
            if (is_new) {
                SIANApiUserAddCashboxVariant(div_id, row_id, '', '', '', '', '', '', []);
            } else {
                cashboxTableObj.find('tbody.sian-api-user-cashbox-variant-tbody').html(SIANApiUserGetNullRow());
            }
        }
    } else
    {
        var cashboxTableObj = $('#' + row_id + '_cashbox_table');
        //
        var xcontinue = true;
        if (is_new) {
            xcontinue = cashboxTableObj.data('count') === 0 || confirm("Ha elegido dejar de ver las variantes de caja. Esto eliminará las que ya ha agregado. ¿Está seguro de continuar?");
        }

        //Si debe continuar
        if (xcontinue) {
            $('#' + row_id + '_cashbox_row').remove();
        } else {
            rowObj.find('input.sian-api-user-variant-has-cashbox-variants').prop('checked', true);
        }
    }

    if (rowObj.hasClass('success')) {
        $('#' + row_id + '_cashbox_row').addClass('success');
    } else if (rowObj.hasClass('info')) {
        $('#' + row_id + '_cashbox_row').addClass('info');
    }
}

function SIANApiUserSetAdminTableError(div_id, row_id, error)
{
    var adminRowObj = $('#' + row_id + '_admin_row');
    if (isset(error))
    {
        adminRowObj.find('div.panel-body').addClass('us-error').prepend('<div class="form-group has-error"><span class="help-block error">' + error + '</span></div>');
    }
}

function SIANApiUserSetCashboxTableError(div_id, row_id, error)
{
    var cashboxRowObj = $('#' + row_id + '_cashbox_row');
    if (isset(error))
    {
        cashboxRowObj.find('div.panel-body').addClass('us-error').prepend('<div class="form-group has-error"><span class="help-block error">' + error + '</span></div>');
    }
}

function SIANApiUserGetAdminTableHtml(div_id, row_id)
{
    var row = '<tr id="' + row_id + '_admin_row">';
    row += '<td colspan="99">';
    row += '<div class="panel panel-default">';
    row += '<div class="panel-body">';
    row += '<table id="' + row_id + '_admin_table" class="sian-api-user-admin-variant-table items table table-hover table-condensed table-striped table-bordered">';
    row += '<thead>';
    row += '<tr>';
    row += '<th width="35%">Usuario</th>';
    row += '<th width="30%">Correo</th>';
    row += '<th width="30%">Teléfono</th>';
    row += '<th width="5%"></th>';
    row += '</tr>';
    row += '</thead>';
    row += '<tbody class="sian-api-user-admin-variant-tbody">';
    row += '</tbody>';
    row += '<tfoot>';
    row += '<tr>';
    row += '<td colspan="99">';
    row += '<a onclick="SIANApiUserAddAdminVariant(\'' + div_id + '\', \'' + row_id + '\', \'\', \'\', \'\', \'\', [])"><span class="fa fa-lg fa-plus black"></span> Agregar administrador</a> ';
    row += '</td>';
    row += '</tr>';
    row += '</tfoot>';
    row += '</table>';
    row += '</div>';
    row += '</div>';
    row += '</td>';
    row += '</tr>';

    return row;
}

function SIANApiUserGetCashboxTableHtml(div_id, row_id)
{
    var row = '<tr id="' + row_id + '_cashbox_row">';
    row += '<td colspan="99">';
    row += '<div class="panel panel-default">';
    row += '<div class="panel-body">';
    row += '<table id="' + row_id + '_cashbox_table" class="sian-api-user-cashbox-variant-table items table table-hover table-condensed table-striped table-bordered">';
    row += '<thead>';
    row += '<tr>';
    row += '<th width="15%">Tipo</th>';
    row += '<th width="15%">Moneda</th>';
    row += '<th width="15%">Tipo de tarjeta</th>';
    row += '<th width="20%">Convenio</th>';
    row += '<th width="30%">Caja/banco</th>';
    row += '<th width="5%"></th>';
    row += '</tr>';
    row += '</thead>';
    row += '<tbody class="sian-api-user-cashbox-variant-tbody">';
    row += '</tbody>';
    row += '<tfoot>';
    row += '<tr>';
    row += '<td colspan="99">';
    row += '<a onclick="SIANApiUserAddCashboxVariant(\'' + div_id + '\', \'' + row_id + '\', \'\', \'\', \'\', \'\', \'\', \'\', [])"><span class="fa fa-lg fa-plus black"></span> Agregar variante de caja</a> ';
    row += '</td>';
    row += '</tr>';
    row += '</tfoot>';
    row += '</table>';
    row += '</div>';
    row += '</div>';
    row += '</td>';
    row += '</tr>';

    return row;
}

function SIANApiUserAddAdminVariant(div_id, row_id, api_user_admin_variant_id, person_id, email_address, phone_number, errors)
{
    var divObj = $('#' + div_id);
    var rowObj = $('#' + row_id);
    var adminTableObj = $('#' + row_id + '_admin_table');
    var count = parseInt(adminTableObj.data('count'));
    //Traemos data
    var person_items = divObj.data('person_items');
    //
    var subrow_id = getLocalId();
    var admin_variant_code_id = getLocalId();

    //HTML
    var row = '';
    row += '<tr id="' + subrow_id + '" class="sian-api-user-admin-variant">';

    row += '<td class="form-group ' + (errors['person_id'] ? 'has-error' : '') + '">';
    row += '<input class="sian-api-user-admin-variant-api-user-admin-variant-id" type="hidden" name="ApiUserVariant[' + row_id + '][adminVariants][' + subrow_id + '][api_user_admin_variant_id]" value="' + api_user_admin_variant_id + '">';
    row += '<select class="form-control sian-api-user-admin-variant-person-id" name="ApiUserVariant[' + row_id + '][adminVariants][' + subrow_id + '][person_id]" onChange="SIANApiUserChangePersonId(\'' + div_id + '\', \'' + row_id + '\', \'' + subrow_id + '\')">' + getComplexOptionsHtml(person_items, 'person_id', 'person_name', person_id, true, true, ['email_address', 'phone_number']) + '</select>';
    if (errors['person_id'])
    {
        row += '<span class="help-block error">' + errors['person_id'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group ' + (errors['email_address'] ? 'has-error' : '') + '">';
    row += '<input class="form-control sian-api-user-admin-variant-email-address" type="text" name="ApiUserVariant[' + row_id + '][adminVariants][' + subrow_id + '][email_address]" value="' + email_address + '" placeholder="Escriba un correo">';
    if (errors['email_address'])
    {
        row += '<span class="help-block error">' + errors['email_address'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group ' + (errors['phone_number'] ? 'has-error' : '') + '">';
    row += '<input class="form-control sian-api-user-admin-variant-phone-number" type="text" name="ApiUserVariant[' + row_id + '][adminVariants][' + subrow_id + '][phone_number]" value="' + phone_number + '" placeholder="Escriba un teléfono">';
    if (errors['phone_number'])
    {
        row += '<span class="help-block error">' + errors['phone_number'] + '</span>';
    }
    row += '</td>';

    row += '<td>';
    row += '<a title="Remover" onclick="SIANApiUserRemoveAdminVariant(\'' + div_id + '\', \'' + row_id + '\', \'' + subrow_id + '\', true)"><span class="fa fa-times fa-lg black"></span></a>';
    row += '</td>';

    row += '</tr>';

    //COUNT DE ITEMS
    if (count === 0)
    {
        adminTableObj.find('tbody.sian-api-user-admin-variant-tbody').html(row);
    } else
    {
        adminTableObj.find('tbody.sian-api-user-admin-variant-tbody').append(row);
    }

    adminTableObj.data('count', count + 1);

    $('#' + admin_variant_code_id).focus();

    return subrow_id;

}

function SIANApiUserAddCashboxVariant(div_id, row_id, api_user_cashbox_variant_id, type, currency, card_type, agreement_id, cashbox_id, errors)
{
    var divObj = $('#' + div_id);
    var rowObj = $('#' + row_id);
    var cashboxTableObj = $('#' + row_id + '_cashbox_table');
    var count = parseInt(cashboxTableObj.data('count'));
    //Traemos data
    var type_items = divObj.data('type_items');
    var currency_items = divObj.data('currency_items');
    var card_type_items = divObj.data('card_type_items');
    var agreement_items = divObj.data('agreement_items');
    //
    var store_id = rowObj.find('select.sian-api-user-variant-store-id').val();

    //
    var subrow_id = getLocalId();
    var cashbox_variant_code_id = getLocalId();

    //HTML
    var row = '';
    row += '<tr id="' + subrow_id + '" class="sian-api-user-cashbox-variant">';

    row += '<td class="form-group ' + (errors['type'] ? 'has-error' : '') + '">';
    row += '<input class="sian-api-user-cashbox-variant-api-user-cashbox-variant-id" type="hidden" name="ApiUserVariant[' + row_id + '][cashboxVariants][' + subrow_id + '][api_user_cashbox_variant_id]" value="' + api_user_cashbox_variant_id + '">';
    row += '<select class="form-control sian-api-user-cashbox-variant-type" name="ApiUserVariant[' + row_id + '][cashboxVariants][' + subrow_id + '][type]" onChange="SIANApiUserChangeCashboxVariantType(\'' + div_id + '\', \'' + row_id + '\', \'' + subrow_id + '\')">' + getOptionsHtml(type_items, type, false) + '</select>';
    if (errors['type'])
    {
        row += '<span class="help-block error">' + errors['type'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group ' + (errors['currency'] ? 'has-error' : '') + '">';
    row += '<select class="form-control sian-api-user-cashbox-variant-currency" name="ApiUserVariant[' + row_id + '][cashboxVariants][' + subrow_id + '][currency]">' + getOptionsHtml(currency_items, currency, false) + '</select>';
    if (errors['currency'])
    {
        row += '<span class="help-block error">' + errors['currency'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group ' + (errors['card_type'] ? 'has-error' : '') + '">';
    row += '<select class="form-control sian-api-user-cashbox-variant-card-type" name="ApiUserVariant[' + row_id + '][cashboxVariants][' + subrow_id + '][card_type]" ' + (type !== API_USER_CASHBOX_VARIANT_TYPE_CARD ? 'readonly' : '') + '>' + getOptionsHtml(card_type_items, card_type, false) + '</select>';
    if (errors['card_type'])
    {
        row += '<span class="help-block error">' + errors['card_type'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group ' + (errors['agreement_id'] ? 'has-error' : '') + '">';
    row += '<select class="form-control sian-api-user-cashbox-variant-agreement-id" name="ApiUserVariant[' + row_id + '][cashboxVariants][' + subrow_id + '][agreement_id]" ' + (type !== API_USER_CASHBOX_VARIANT_AGREEMENT ? 'readonly' : '') + '>' + getOptionsHtml(agreement_items, agreement_id, true) + '</select>';
    if (errors['agreement_id'])
    {
        row += '<span class="help-block error">' + errors['agreement_id'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group ' + (errors['cashbox_id'] ? 'has-error' : '') + '">';
    row += '<select class="form-control sian-api-user-cashbox-variant-cashbox-id" name="ApiUserVariant[' + row_id + '][cashboxVariants][' + subrow_id + '][cashbox_id]">' + SIANApiUserGetCashboxOptions(div_id, store_id, cashbox_id) + '</select>';
    if (errors['cashbox_id'])
    {
        row += '<span class="help-block error">' + errors['cashbox_id'] + '</span>';
    }
    row += '</td>';

    row += '<td>';
    row += '<a title="Remover" onclick="SIANApiUserRemoveCashboxVariant(\'' + div_id + '\', \'' + row_id + '\', \'' + subrow_id + '\', true)"><span class="fa fa-times fa-lg black"></span></a>';
    row += '</td>';

    row += '</tr>';

    //COUNT DE ITEMS
    if (count === 0)
    {
        cashboxTableObj.find('tbody.sian-api-user-cashbox-variant-tbody').html(row);
    } else
    {
        cashboxTableObj.find('tbody.sian-api-user-cashbox-variant-tbody').append(row);
    }

    cashboxTableObj.data('count', count + 1);

    $('#' + cashbox_variant_code_id).focus();

    return subrow_id;

}

function SIANApiUserGetStoreOptions(div_id, store_id)
{
    //DIV
    var divObj = $('#' + div_id);
    var store_items = divObj.data('store_items');
    //
    var items = {};
    $.each(store_items, function (index, store) {
        items[store.store_id] = store.store_name;
    });
    //
    return getOptionsHtml(items, store_id, true);
}

function SIANApiUserGetWarehouseOptions(div_id, store_id, warehouse_id)
{
    //DIV
    var divObj = $('#' + div_id);
    var warehouse_items = divObj.data('warehouse_items');
    //
    var items = {};
    $.each(warehouse_items, function (index, warehouse) {
        //Veamos que pertenezca a la misma
        //        if (warehouse.store_id == store_id) {
        items[warehouse.warehouse_id] = warehouse.warehouse_name;
        //        }
    });
    //
    return getOptionsHtml(items, warehouse_id, true);
}

function SIANApiUserGetCashboxOptions(div_id, store_id, cashbox_id)
{
    //DIV
    var divObj = $('#' + div_id);
    var cashbox_items = divObj.data('cashbox_items');
    //
    var items = {};
    $.each(cashbox_items, function (index, cashbox) {
        //Veamos que pertenezca a la misma       
        if (isset(cashbox.store_id) && cashbox.store_id == store_id) {
            items[cashbox.cashbox_id] = cashbox.cashbox_name;
        }
    });
    //
    return getOptionsHtml(items, cashbox_id, true);
}

function SIANApiUserGetDestinationCashboxOptions(div_id, store_id, destination_cashbox_id)
{
    var divObj = $('#' + div_id); //DIV
    var destination_cashbox_items = divObj.data('destination_cashbox_items');
    var items = {};
    destination_cashbox_items.forEach((item, index, arr) => {
        if (typeof item.store_id !== 'undefined' && item.store_id !== null) {  //Veamos que pertenezca a la misma 
            if (item.store_id == store_id) {
                items[item.cashbox_id] = item.cashbox_name;
            }
        } else {
            items[item.cashbox_id] = item.cashbox_name;
        }
    });

    return getOptionsHtml(items, destination_cashbox_id, true);
}

function SIANApiUserGetBusinessUnits(div_id, business_unit_id)
{
    //DIV
    var divObj = $('#' + div_id);
    var business_unit_items = divObj.data('business_unit_items');
    //
    var items = {};
    $.each(business_unit_items, function (index, business_unit) {
        items[business_unit.business_unit_id] = business_unit.business_unit_name;
    });
    //
    return getOptionsHtml(items, business_unit_id, true);
}

function SIANApiUserGetDefaultPrice(div_id, default_price)
{
    var divObj = $('#' + div_id);
    var default_price_items = divObj.data('default_price_items');
    var items = {};
    $.each(default_price_items, function (index, price) {
        //console.log(index, price);
        items[index] = price;
    });
    return getOptionsHtml(items, default_price, true);
}

function SIANApiUserRemoveVariant(div_id, row_id, p_b_confirm)
{
    if (!p_b_confirm || confirm("Está seguro de eliminar esta fila?"))
    {
        var divObj = $('#' + div_id);
        var tableObj = $('#' + div_id + '_table');
        var rowObj = $('#' + row_id);
        var cashboxRowObj = $('#' + row_id + '_cashbox_row');
        var xdefault = rowObj.find('input.sian-api-user-variant-default').prop('checked');
        //Obtenemos contador
        var count = parseInt(divObj.data('count'));
        //Removemos
        rowObj.remove();
        cashboxRowObj.remove();
        //Si el eliminado fue el por defecto se debe asignar por defecto a otro
        if (xdefault)
        {
            tableObj.find('input.sian-api-user-variant-default').first().prop('checked', true).change();
        }
        //Si contador era 1 entonces ya no queda nada
        if (count === 1)
        {
            tableObj.find('tbody.sian-api-user-variant-tbody').html(SIANApiUserGetNullRow());
        }
        //Contador
        divObj.data('count', count - 1);
    }
}

function SIANApiUserRemoveAdminVariant(div_id, row_id, subrow_id, p_b_confirm)
{
    if (!p_b_confirm || confirm("Está seguro de eliminar esta fila?"))
    {
        var divObj = $('#' + div_id);
        var rowObj = $('#' + row_id + '_table');
        var adminTableObj = $('#' + row_id + '_admin_table');
        var subrowObj = $('#' + subrow_id);
        //Obtenemos contador
        var count = parseInt(adminTableObj.data('count'));
        //Removemos
        subrowObj.remove();
        //Si contador era 1 entonces ya no queda nada
        if (count === 1)
        {
            adminTableObj.find('tbody.sian-api-user-admin-variant-tbody').html(SIANApiUserGetNullRow());
        }
        //Contador
        adminTableObj.data('count', count - 1);
    }
}

function SIANApiUserRemoveCashboxVariant(div_id, row_id, subrow_id, p_b_confirm)
{
    if (!p_b_confirm || confirm("Está seguro de eliminar esta fila?"))
    {
        var divObj = $('#' + div_id);
        var rowObj = $('#' + row_id + '_table');
        var cashboxTableObj = $('#' + row_id + '_cashbox_table');
        var subrowObj = $('#' + subrow_id);
        //Obtenemos contador
        var count = parseInt(cashboxTableObj.data('count'));
        //Removemos
        subrowObj.remove();
        //Si contador era 1 entonces ya no queda nada
        if (count === 1)
        {
            cashboxTableObj.find('tbody.sian-api-user-cashbox-variant-tbody').html(SIANApiUserGetNullRow());
        }
        //Contador
        cashboxTableObj.data('count', count - 1);
    }
}

function SIANApiUserGetNullRow()
{
    return '<tr><td class=\'empty\' colspan=\'99\'>' + STRINGS_NO_DATA + '</td></tr>';
}

function SIANApiUserChangeStore(div_id, row_id)
{
    var divObj = $('#' + div_id);
    var tableObj = $('#' + div_id + '_table');
    var rowObj = $('#' + row_id);
    var cashboxTableObj = $('#' + row_id + '_cashbox_table');
    //
    var store_id = rowObj.find('select.sian-api-user-variant-store-id').val();
    //Verificamos si no está en blanco
    if (!isBlank(store_id))
    {
        var warehouse_id = rowObj.find('select.sian-api-user-variant-warehouse-id').val();
        var cashbox_id = rowObj.find('select.sian-api-user-variant-cashbox-id').val();
        var destination_cashbox_id = rowObj.find('select.sian-api-user-variant-destination-cashbox-id').val();
        //
        var a_warehouse_options = SIANApiUserGetWarehouseOptions(div_id, store_id, warehouse_id);
        var a_cashbox_options = SIANApiUserGetCashboxOptions(div_id, store_id, cashbox_id);
        var a_destination_cashbox_options = SIANApiUserGetDestinationCashboxOptions(div_id, store_id, destination_cashbox_id);
        //
        rowObj.find('select.sian-api-user-variant-warehouse-id').html(a_warehouse_options);
        rowObj.find('select.sian-api-user-variant-cashbox-id').html(a_cashbox_options);
        rowObj.find('select.sian-api-user-variant-destination-cashbox-id').html(a_destination_cashbox_options);
        //Actualizamos cajas de tabla hija
        cashboxTableObj.find('tr.sian-api-user-cashbox-variant').each(function (index) {
            var subRow = $(this);
            var subcashbox_id = rowObj.find('select.sian-api-user-cashbox-variant-cashbox-id').val();
            var a_subcashbox_options = SIANApiUserGetCashboxOptions(div_id, store_id, subcashbox_id);
            //
            subRow.find('select.sian-api-user-cashbox-variant-cashbox-id').html(a_subcashbox_options);
        });
    }
}

function SIANApiUserChangePersonId(div_id, row_id, subrow_id)
{
    var divObj = $('#' + div_id);
    var tableObj = $('#' + div_id + '_table');
    var rowObj = $('#' + row_id);
    var subRowObj = $('#' + subrow_id);
    //
    var optionObj = subRowObj.find('select.sian-api-user-admin-variant-person-id').find('option:selected');

    subRowObj.find('input.sian-api-user-admin-variant-email-address').val(optionObj.data('email_address'));
    subRowObj.find('input.sian-api-user-admin-variant-phone-number').val(optionObj.data('phone_number'));
}

function SIANApiUserChangeCashboxVariantType(div_id, row_id, subrow_id)
{
    var divObj = $('#' + div_id);
    var tableObj = $('#' + div_id + '_table');
    var rowObj = $('#' + row_id);
    var subRowObj = $('#' + subrow_id);
    //
    var type = subRowObj.find('select.sian-api-user-cashbox-variant-type').val();
    //
    subRowObj.find('select.sian-api-user-cashbox-variant-card-type').attr('readonly', type !== API_USER_CASHBOX_VARIANT_TYPE_CARD);
    subRowObj.find('select.sian-api-user-cashbox-variant-agreement-id').attr('readonly', type !== API_USER_CASHBOX_VARIANT_AGREEMENT);

    if (type !== API_USER_CASHBOX_VARIANT_TYPE_CARD)
    {
        subRowObj.find('select.sian-api-user-cashbox-variant-card-type').val(API_USER_CASHBOX_VARIANT_CARD_TYPE_ANY);
    }
    if (type !== API_USER_CASHBOX_VARIANT_AGREEMENT)
    {
        subRowObj.find('select.sian-api-user-cashbox-variant-agreement-id').val('');
    }
}

function SIANApiUserChangeType(div_id, type)
{
    var divObj = $('#' + div_id);
    var tableObj = $('#' + div_id + '_table');
    var count = divObj.data('count');

    var o_required = {
        store_id: inArray(type, [API_USER_TYPE_POS, API_USER_TYPE_WEB, API_USER_TYPE_KIOSK]) && !isBlank(type),
        warehouse_id: type === API_USER_TYPE_POS && !isBlank(type),
        cashbox_id: type === API_USER_TYPE_POS && !isBlank(type),
        business_unit_id: inArray(type, [API_USER_TYPE_POS, API_USER_TYPE_WEB, API_USER_TYPE_KIOSK]) && !isBlank(type)
    };

    tableObj.find('thead th').each(function (index) {
        var thObj = $(this);
        //Obtenemos field
        var field = thObj.attr('field');
        //Vemos si está para required o no        
        if (isset(o_required[field]))
        {
            thObj.makeHeaderRequired(o_required[field]);
        }
    });

    //Si es POS o WEB, si es locker se borran
    if (inArray(type, [API_USER_TYPE_POS, API_USER_TYPE_WEB, API_USER_TYPE_KIOSK]))
    {
        //Abilitamos
        SIANApiUserDisabled(div_id, false);
        //Mostramos
        SIANApiUserVisibleTable(div_id, true);
        //
        var o_disabled = {
            store_id: type === API_USER_TYPE_LOCKER || isBlank(type),
            warehouse_id: type === API_USER_TYPE_LOCKER || isBlank(type),
            cashbox_id: type === API_USER_TYPE_LOCKER || isBlank(type),
            business_unit_id: type === API_USER_TYPE_LOCKER || isBlank(type)
        };
        //Recorremos filas para setear disabled
        tableObj.find('tbody tr.sian-api-user-variant').each(function (index) {
            var rowObj = $(this);
            var row_id = rowObj.attr('id');
            //
            if (isset(o_disabled['store_id'])) {
                rowObj.find('select.sian-api-user-variant-store-id').prop('disabled', o_disabled['store_id']);
            }
            //
            if (isset(o_disabled['warehouse_id'])) {
                rowObj.find('select.sian-api-user-variant-warehouse-id').prop('disabled', o_disabled['store_id']);
            }
            //
            if (isset(o_disabled['cashbox_id'])) {
                rowObj.find('select.sian-api-user-variant-cashbox-id').prop('disabled', o_disabled['store_id']);
            }
            //
            if (isset(o_disabled['business_unit_id'])) {
                rowObj.find('select.sian-api-user-variant-business-unit-id').prop('disabled', o_disabled['store_id']);
            }
            //
            rowObj.find('input.sian-api-user-variant-has-admin-variants').prop('disabled', type !== API_USER_TYPE_POS);
            if (type !== API_USER_TYPE_POS) {
                rowObj.find('input.sian-api-user-variant-has-admin-variants').prop('checked', false);
                $('#' + row_id + '_admin_row').remove();
            }

        });
    } else {
        //Eliminamos todo
        SIANApiUserClean(div_id);
        //Ocultamos
        SIANApiUserVisibleTable(div_id, false);
        //Deshabilitamos
        SIANApiUserDisabled(div_id, true);
    }
}

function SIANApiUserClean(div_id)
{
    var divObj = $('#' + div_id);
    var tableObj = $('#' + div_id + '_table');
    //Recorremos filas para setear disabled
    tableObj.find('tbody tr.sian-api-user-variant').each(function (index) {
        var rowObj = $(this);
        var row_id = rowObj.attr('id');
        SIANApiUserRemoveVariant(div_id, row_id);
    });
    SIANApiUserPaintVariants(div_id);
}

function SIANApiUserVisibleTable(div_id, visible)
{
    var divObj = $('#' + div_id);
    var tableObj = $('#' + div_id + '_table');

    if (visible)
    {
        tableObj.closest('div.panel').show();
    } else
    {
        tableObj.closest('div.panel').hide();
    }
}

function SIANApiUserDisabled(div_id, disabled)
{
    var divObj = $('#' + div_id);
    divObj.data('disabled', disabled);
}

function SIANApiUserPaintVariants(div_id)
{
    var divObj = $('#' + div_id);
    var tableObj = $('#' + div_id + '_table');
    //Recorremos filas para setear disabled
    tableObj.find('tbody tr.sian-api-user-variant').each(function (index) {
        var rowObj = $(this);
        var row_id = rowObj.attr('id');

        rowObj.removeClass('success').removeClass('info');
        $('#' + row_id + '_admin_row').removeClass('success').removeClass('info');
        $('#' + row_id + '_cashbox_row').removeClass('success').removeClass('info');
        if (index % 2 === 0) {
            rowObj.addClass('success');
            $('#' + row_id + '_admin_row').addClass('success');
            $('#' + row_id + '_cashbox_row').addClass('success');
        } else {
            rowObj.addClass('info');
            $('#' + row_id + '_admin_row').addClass('info');
            $('#' + row_id + '_cashbox_row').addClass('info');
        }
    });
}