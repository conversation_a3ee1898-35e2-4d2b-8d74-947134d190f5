<?php

class USBooleanLabel extends CWidget {

    public $value;
    public $true_label = "SI";
    public $false_label = "NO";
    public $null_label = null;

    /**
     * Initializes the widget.
     */
    public function init() {
        
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if (isset($this->null_label)) {

            if (isset($this->value)) {
                echo $this->controller->widget('booster.widgets.TbLabel', array(
                    'context' => ($this->value == 1) ? 'success' : 'danger',
                    'label' => $this->value ? $this->true_label : $this->false_label
                        ), true);
            } else {
                echo $this->null_label;
            }
        } else {
            echo $this->controller->widget('booster.widgets.TbLabel', array(
                'context' => ($this->value == 1) ? 'success' : 'danger',
                'label' => $this->value ? $this->true_label : $this->false_label
                    ), true);
        }
    }

}
