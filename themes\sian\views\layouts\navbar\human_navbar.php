<?php

$arrayItemsNavbar = array(
    // array('icon' => 'th-list', 'label' => 'Datos principales', 'url' => '#', 'items' => array(
    array('label' => 'Datos principales', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'flag', 'label' => 'Areas', 'route' => 'area/index'),
                array('icon' => 'list', 'label' => 'Puestos', 'route' => 'station/index'),
                array('icon' => 'list', 'label' => 'Categorías', 'route' => 'category/index'),
                array('icon' => 'user', 'label' => 'Colaboradores', 'route' => 'employee/index'),
            ),
            array(
                array('icon' => 'list', 'label' => 'Ofertas Laborales', 'route' => 'jobOffer/index'),
            ),
            array(
                array('icon' => 'piggy-bank', 'label' => 'Sistemas de Pension', 'route' => 'pensionSystem/index'),
                array('icon' => 'user', 'label' => 'Usuarios', 'route' => 'user/index'),
                array('icon' => 'fa fa-users', 'label' => 'Grupo de Usuarios', 'route' => 'userGroup/index'),
            ),
            array(
                array('icon' => 'list', 'label' => 'Regímenes Laborales', 'route' => 'regime/index'),
                array('icon' => 'list', 'label' => 'Factores de cálculo', 'route' => 'factor/index'),
                array('icon' => 'list', 'label' => 'Diccionarios', 'route' => 'dictionary/index'),
                array('icon' => 'list', 'label' => 'Conceptos', 'route' => 'concept/index'),
            )
        )),
    // array('icon' => 'th-list', 'label' => 'Transacciones', 'url' => '#', 'items' => array(
    array('label' => 'Transacciones', 'url' => '#', 'items' => array(
            array('icon' => 'list-alt', 'label' => 'Resumen de Asistencia', 'route' => 'assistanceSummary/index'),
            array('icon' => 'list-alt', 'label' => 'Planillas', 'route' => 'payroll/index'),
        )),
    // array('icon' => 'flash', 'label' => 'Declaraciones', 'url' => '#', 'items' => array(
    array('label' => 'Declaraciones', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'file', 'label' => 'PLAME', 'route' => 'plame/txt'),
            )
        )),
    // array('icon' => 'file', 'label' => 'Reportes', 'url' => '#', 'items' => array(
    array('label' => 'Reportes', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'file', 'label' => 'Impresión Formatos', 'route' => 'report/paycheck'),
            )
        )),
);

$this->widget('application.widgets.SIANNavbar', array(
    'brand' => Yii::app()->id,
    'class' => 'navbar-human',
    'items' => $this->items_menu, //$arrayItemsNavbar,
));
