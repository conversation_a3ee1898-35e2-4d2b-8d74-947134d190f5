<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'staging2logisystem.siansystem.com/admin';
$domain = 'https://staging2logisystem.siansystem.com';
$report_domain = 'rstaging2.siansystem.com';
$org = 'logisystem';
$us = 'us_staging2';
$database_server = '69.10.37.246';
$database_name = 'logisystem_staging2';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = true;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_EFACT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'logisystem.p12';
$e_billing_certificate_pass = 'drvgbHkeJZjHnEDA';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20481818991BILLING7',
        'password' => 'Billing7'
    ],
    YII_OSE_EFACT => [
        'username' => '20481818991',
        'password' => 'G00FmvemUx'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_STAGING;
$environment = YII_ENVIRONMENT_STAGING;
$admin_emails = array(
    'Logisystem SIAN' => '<EMAIL>',
);

