<?php

class SIANECommerce extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $currencyItems;
    public $fixed = 2;
    public $step = 0.01;
//    //PRIVATE
    private $controller;

    public function init() {
        //CONTROL
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-e-commerce.js');

        //Tipos de destino
        $destinyTypes = [];
        foreach ($this->model->tempDestinyTypes as $destinyType) {
            $attributes = [];
            $attributes[] = "destiny_type:'{$destinyType->destiny_type}'";
            $attributes[] = "errors:" . CJSON::encode(array(
                        'destiny_type' => $destinyType->getError('destiny_type'),
            ));
            $destinyTypes[] = '{' . implode(',', $attributes) . '}';
        }
        $destinyTypes = '[' . implode(',', $destinyTypes) . ']';

        //Tarifas
        $tariffs = [];
        foreach ($this->model->tempTariffs as $tariff) {
            $attributes = [];
            $attributes[] = "destiny_type:'{$tariff->destiny_type}'";
            $attributes[] = "begin_weight:'{$tariff->begin_weight}'";
            $attributes[] = "final_weight:'{$tariff->final_weight}'";
            $attributes[] = "cost:'{$tariff->cost}'";
            $attributes[] = "row:{$tariff->row}";
            $attributes[] = "column:{$tariff->column}";
            $attributes[] = "errors:" . CJSON::encode(array(
                        'destiny_type' => $tariff->getError('destiny_type'),
                        'begin_weight' => $tariff->getError('begin_weight'),
                        'final_weight' => $tariff->getError('final_weight'),
                        'cost' => $tariff->getError('cost'),
            ));
            $tariffs[] = '{' . implode(',', $attributes) . '}';
        }
        $tariffs = '[' . implode(',', $tariffs) . ']';

        //Destinos
        $destinies = [];
        foreach ($this->model->tempDestinies as $destiny) {
            $attributes = [];
            $attributes[] = "destiny_type:'{$destiny->destiny_type}'";
            $attributes[] = "dept_code:'{$destiny->dept_code}'";
            $attributes[] = "prov_code:'{$destiny->prov_code}'";
            $attributes[] = "dist_code:'{$destiny->dist_code}'";
            $attributes[] = "days:{$destiny->days}";
            $attributes[] = "collectable:{$destiny->collectable}";

            $attributes[] = "errors:" . CJSON::encode(array(
                        'destiny_type' => $destiny->getError('destiny_type'),
                        'dept_code' => $destiny->getError('dept_code'),
                        'prov_code' => $destiny->getError('prov_code'),
                        'dist_code' => $destiny->getError('dist_code'),
                        'days' => $destiny->getError('days'),
                        'collectable' => $destiny->getError('collectable'),
            ));
            $destinies[] = '{' . implode(',', $attributes) . '}';
        }
        $destinies = '[' . implode(',', $destinies) . ']';

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        $(document).ready(function() {
            //Fixed y step
            $('#{$this->id}').data('fixed', {$this->fixed});
            $('#{$this->id}').data('step', {$this->step});
            //Contador de tabla tipos de destino
            $('#{$this->id} table.sian-e-commerce-main-table').data('count', 0);
            $('#{$this->id} table.sian-e-commerce-destiny-table').data('count', 0);
            window.geoloc = " . CJSON::encode($this->controller->getGeoloc()) . ";
            //Tipos de destino
            var destinyTypes = {$destinyTypes};
            var tariffs = {$tariffs};
            var destinies = {$destinies};
            SIANECommerceInit('{$this->id}', destinyTypes, tariffs, destinies);
        });        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->widget('booster.widgets.TbTabs', array(
            'id' => $this->id,
            'type' => 'tabs',
            'tabs' => array(
                array(
                    'icon' => 'fa fa-lg fa-list black',
                    'label' => 'Principal',
                    'content' => $this->renderMain(),
                    'active' => true,
                ),
                array(
                    'icon' => 'fa fa-lg fa-dollar black',
                    'label' => $this->model->getAttributeLabel('tempTariffs'),
                    'content' => $this->renderTariff(),
                ),
                array(
                    'icon' => 'fa fa-lg fa-dropbox black',
                    'label' => $this->model->getAttributeLabel('tempDestinies'),
                    'content' => $this->renderDelivery(),
                ),
            ),
            'htmlOptions' => array(
                'class' => 'sian-e-commerce',
            ))
        );
    }

    private function renderMainPerson() {
        $html = '';
        $html .= "<div class='row'>";
        $html .= "<div class='col-lg-9 col-md-9 col-sm-9 col-xs-12'>";
        $html .= $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model,
            'attribute' => 'person_id',
            'parent_id' => $this->modal_id,
            'view' => array(
                'model' => 'DpBusinessPartner',
                'scenario' => DpBusinessPartner::SCENARIO_PEOPLE,
                'attributes' => array(
                    array(
                        'name' => 'person_id',
                        'width' => 10,
                        'types' => array('id', 'value'),
                        'hidden' => true,
                        'not_in' => '[1]',
                    ),
                    array(
                        'name' => 'identification_name',
                        'width' => 20,
                        'search' => false
                    ),
                    array(
                        'name' => 'identification_number',
                        'width' => 20,
                        'types' => array('aux')
                    ),
                    array(
                        'name' => 'person_name',
                        'width' => 60,
                        'types' => array('text')
                    ),
                    array(
                        'name' => 'person_type',
                        'width' => 0,
                        'hidden' => true,
                    ),
                )
            ),
            'maintenance' => array(
                'module' => 'administration',
                'controller' => 'person',
            )
                ), true);
        $html .= "</div>";
        $html .= "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        $html .= $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'status',
                ), true);
        $html .= "</div>";
        $html .= "</div>";
        return $html;
    }

    private function renderMainTariff() {
        $html = '';
        $html .= "<div class='row'>";
        $html .= "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        $html .= $this->form->dropDownListRow($this->model, 'currency', $this->currencyItems, array(
            'empty' => Strings::SELECT_OPTION,
            'class' => 'sian-e-commerce-currency',
            'onchange' => "SIANECommerceUpdateCurrency(this)",
        ));
        $html .= "</div>";
        $html .= "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        $html .= $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'include_igv',
                ), true);
        $html .= "</div>";
        $html .= "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        $html .= $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'include_insurance',
                ), true);
        $html .= "</div>";
        $html .= "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        $html .= $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'use_volumetric_weight',
                ), true);
        $html .= "</div>";
        $html .= "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        $html .= $this->form->numberFieldRow($this->model, 'additional_percentage', array(
            'step' => 0.01,
            'class' => 'us-double',
        ));
        $html .= "</div>";
        $html .= "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        $html .= $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'by_item',
            'hint' => 'Si está activado el costo de envío se calcula por cada item'
                ), true);
        $html .= "</div>";
        $html .= "</div>";
        return $html;
    }

    private function renderMainTable() {
        $html = '';
        $html .= "<table class='sian-e-commerce-main-table table table-condensed table-hover'>";
        $html .= "<thead>";
        $html .= "<tr>";
        $html .= "<th width='90%'>{$this->model->getAttributeLabel('destinyTypes.destiny_type')}</th>";
        $html .= "<th width='10%'></th>";
        $html .= "</tr>";
        $html .= "</thead>";
        $html .= "<tbody>";
        $html .= "</tbody>";
        $html .= "<tfoot>";
        $html .= "<tr>";
        $html .= "<td>";
        $html .= CHtml::link("<span class='fa fa-lg fa-plus black'></span>", Strings::LINK_TEXT, array(
                    'title' => 'Agregar fila',
                    'onclick' => "SIANECommerceMainAddRow(this, '', {})"
        ));
        $html .= "</td>";
        $html .= "</tr>";
        $html .= "</tfoot>";
        $html .= "</table>";
        return $html;
    }

    private function renderMain() {
        $html = '<br>';
        $html .= "<div class='row'>";
        $html .= "<div class='col-lg-9 col-md-9 col-sm-9 col-xs-12'>";
        $html .= $this->widget('booster.widgets.TbPanel', array(
            'title' => 'Persona',
            'headerIcon' => 'fa fa-lg fa-user black',
            'content' => $this->renderMainPerson(),
                ), true);
        $html .= $this->widget('booster.widgets.TbPanel', array(
            'title' => 'Datos de tarifas',
            'headerIcon' => 'fa fa-lg fa-dollar black',
            'content' => $this->renderMainTariff(),
                ), true);
        $html .= "<div class='row'>";
        $html .= "</div>";
        $html .= "</div>";
        $html .= "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        $html .= $this->widget('booster.widgets.TbPanel', array(
            'title' => $this->model->getAttributeLabel('destinyTypes'),
            'headerIcon' => 'fa fa-lg fa-dropbox black',
            'htmlOptions' => array(
                'class' => ($this->model->hasErrors('tempDestinyTypes') ? 'us-error' : ''),
            ),
            'content' => $this->renderMainTable(),
                ), true);
        $html .= "</div>";
        $html .= "</div>";

        return $html;
    }

    private function renderTariff() {
        return '<br>' . $this->widget('booster.widgets.TbPanel', array('title' => $this->model->getAttributeLabel('tempTariffs'),
                    'headerIcon' => 'fa fa-lg fa-dollar black',
                    'htmlOptions' => array(
                        'class' => ($this->model->hasErrors('tempTariffs') ? 'us-error' : ''),
                    ),
                    'content' => $this->renderTariffTable(),
                        ), true);
    }

    private function renderTariffTable() {
        $html = '';
        $html .= "<table border=1 class='sian-e-commerce-tariff-table'>";
        $html .= "<thead>";
        $html .= "<tr>";
        $html .= "<th colspan=2>Rangos</th>";
        $html .= "<th width='5px'>";
        $html .= CHtml::link("<span class='fa fa-lg fa-plus black'></span>", Strings::LINK_TEXT, array(
                    'title' => 'Agregar columna',
                    'onclick' => "SIANECommerceTariffAddColumn(this)"
        ));
        $html .= "</th>";
        $html .= "</tr>";
        $html .= "</thead>";
        $html .= "<tbody>";
        $html .= "</tbody>";
        $html .= "<tfoot>";
        $html .= "<tr>";
        $html .= "<td colspan=2>";
        $html .= CHtml::link("<span class='fa fa-lg fa-plus black'></span>", Strings::LINK_TEXT, array(
                    'title' => 'Agregar fila',
                    'onclick' => "SIANECommerceTariffAddRow(this)"
        ));
        $html .= "</td>";
        $html .= "<td>";
        $html .= CHtml::link("<span class='fa fa-lg fa-plus black'></span>", Strings::LINK_TEXT, array(
                    'title' => 'Agregar columna y fila',
                    'onclick' => "SIANECommerceTariffAddBoth(this);"
        ));
        $html .= "</td>";
        $html .= "</tr>";
        $html .= "</tfoot>";
        $html .= "</table>";
        $html .= "<div class='sian-e-commerce-tariff-hidden'></div>";
        return $html;
    }

    private function renderDelivery() {
        return '<br>' . $this->widget('booster.widgets.TbPanel', array('title' => $this->model->getAttributeLabel('tempDestinies'),
                    'headerIcon' => 'fa fa-lg fa-dropbox black',
                    'htmlOptions' => array(
                        'class' => ($this->model->hasErrors('tempDestinies') ? 'us-error' : ''),
                    ),
                    'content' => $this->renderDestinyTable(),
                        ), true);
    }

    private function renderDestinyTable() {
        $html = '';
        $html .= "<table class='sian-e-commerce-destiny-table table table-condensed table-hover'>";
        $html .= "<thead>";
        $html .= "<tr>";
        $html .= "<th width='15%'>{$this->model->getAttributeLabel('destinyTypes.destinies.destiny_type')}</th>";
        $html .= "<th width='20%'>{$this->model->getAttributeLabel('destinyTypes.destinies.dept_code')}</th>";
        $html .= "<th width='20%'>{$this->model->getAttributeLabel('destinyTypes.destinies.prov_code')}</th>";
        $html .= "<th width='20%'>{$this->model->getAttributeLabel('destinyTypes.destinies.dist_code')}</th>";
        $html .= "<th width='10%'>{$this->model->getAttributeLabel('destinyTypes.destinies.days')}</th>";
        $html .= "<th width='10%'>{$this->model->getAttributeLabel('destinyTypes.destinies.collectable')}</th>";
        $html .= "<th width='5%'></th>";
        $html .= "</tr>";
        $html .= "</thead>";
        $html .= "<tbody></tbody>";
        $html .= "<tfoot>";
        $html .= "<tr>";
        $html .= "<td colspan=99>";
        $html .= CHtml::link("<span class='fa fa-lg fa-plus black'></span>" . Strings::SPACE . "Agregar", Strings::LINK_TEXT, array(
                    'title' => 'Agregar',
                    'onclick' => "SIANECommerceDestinyAddRow(this, null, null, null, null, 0, 1, {})",
        ));
        $html .= "</td>";
        $html .= "</tr>";
        $html .= "</tfoot>";
        $html .= "</table>";
        return $html;
    }

}
