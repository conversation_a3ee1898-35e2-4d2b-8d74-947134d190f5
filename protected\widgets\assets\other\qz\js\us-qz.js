function USQZConnect(certUrl, signUrl, dropdown_id) {

    qz.security.setCertificatePromise(function (resolve, reject) {
        $.ajax(certUrl).then(resolve, reject);
    });

    qz.security.setSignaturePromise(function (toSign) {
        return function (resolve, reject) {
            $.ajax(signUrl + "?request=" + toSign).then(resolve, reject);
        };
    });

    qz.websocket.connect().then(function () {
        USQZGetVersion();
        USQZFillPrinters(dropdown_id);
        window.qz_ready = true;
        window.qz_dropdown_id = dropdown_id;
    }).catch(USQZDisplayError);
}

function USQZGetVersion() {
    qz.api.getVersion().then(function (data) {
        var message = 'QZ Versión ' + data + ' cargado correctamente';
        USQZDisplayMessage(message, 'success');
    }).catch(USQZDisplayError);
}

function USQZDisplayError(message) {
    USQZDisplayMessage(message, 'error');
}
function USQZDisplayMessage(message, className) {
    $.notify(message, {
        className: className,
        showDuration: 50,
        hideDuration: 50,
        autoHideDelay: 3000
    });
}

function USQZFillPrinters(dropdown_id)
{
    //Obtenemos impresora por defecto
    qz.printers.getDefault().then(function (defaultPrinter) {
        USQZDisplayMessage("Impresora encontrada: " + defaultPrinter, 'success');
        //LLenamos impresoras
        qz.printers.find().then(function (printers) {
            var found = false;
            //Recorremos impresoras
            $.each(printers, function (index, printer) {
                var optionObj = $('<option>').val(printer).text(printer);
                $('#' + dropdown_id).append(optionObj);

                if (defaultPrinter === printer)
                {
                    found = true;
                }
            });

            if (found)
            {
                $('#' + dropdown_id).val(defaultPrinter);
            }

        }).catch(USQZDisplayError);
    }).catch(USQZDisplayError);
}

function USQZPrintESCPOS(printData, callback)
{
    var selected_printer = $('#' + window.qz_dropdown_id).val();

    qz.printers.find(selected_printer).then(function (printer_name) {
        //Imprimimos
        var config = qz.configs.create(printer_name, {
            encoding: 'ISO-8859-1',
            altPrinting: true,
        });
        qz.print(config, printData).then(function () {
            window[callback]();
        });
    }).catch(USQZDisplayError);
}

function USQZdisconnect() 
{
    // Verifica si QZ Tray está conectado antes de intentar desconectar
    if (qz.websocket.isActive()) {
        qz.websocket.disconnect()
            .then(() => {
                console.log("QZ Tray desconectado exitosamente.");
            })
            .catch(err => {
                console.error("Error al desconectar QZ Tray:", err);
            });
    } else {
        console.log("QZ Tray ya está desconectado.");
    }
}