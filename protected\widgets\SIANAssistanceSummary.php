<?php

class SIANAssistanceSummary extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $child_modal_id;
    public $column_items;
    public $field_items;
    public $type_absence_items;
    //PRIVATE
    private $controller;
    private $add_all_button_id;
    private $add_employee_button_id;
    private $delete_all_button_id;
    private $person_div_id;
    private $person_input_id;
    private $year_input_id;
    private $week_input_id;
    private $split_week_input_id;
    private $month_input_id;
    private $regime_input_id;
    private $regime_code_input_id;
    private $work_days_input_id;
    private $back_link_id;
    private $title_id;
    private $row_control_id;
    private $div_tables_id;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->modal_id = isset($this->modal_id) ? $this->modal_id : $this->controller->getServerId();
        $this->child_modal_id = isset($this->child_modal_id) ? $this->child_modal_id : $this->controller->getServerId();
        //--
        $this->add_all_button_id = $this->controller->getServerId();
        $this->add_employee_button_id = $this->controller->getServerId();
        $this->delete_all_button_id = $this->controller->getServerId();
        $this->person_div_id = $this->controller->getServerId();
        $this->person_input_id = $this->controller->getServerId();
        $this->year_input_id = $this->controller->getServerId();
        $this->week_input_id = $this->controller->getServerId();
        $this->split_week_input_id = $this->controller->getServerId();
        $this->month_input_id = $this->controller->getServerId();
        $this->regime_input_id = $this->controller->getServerId();
        $this->regime_code_input_id = $this->controller->getServerId();
        $this->work_days_input_id = $this->controller->getServerId();
        $this->back_link_id = $this->controller->getServerId();
        $this->title_id = $this->controller->getServerId();
        $this->row_control_id = $this->controller->getServerId();
        $this->div_tables_id = $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-assistance-summary.js');
        SIANAssets::registerScriptFile('other/select2/js/select2.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
        SIANAssets::registerCssFile('other/select2/css/select2.css');

        $head_monthly = "<thead>" . "<tr>" . "<th width='2%'>#</th>"
                . "<th width='7%' style='text-transform: uppercase;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.person_id')}</th>"
                . "<th width='35%' style='text-transform: uppercase;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.Nombre')}</th>"
                . "<th width='7%'  style='text-transform: uppercase;text-align:center;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.work_days_employee')}</th>"
                . "<th width='10%' style='text-transform: uppercase;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.absences')}</th>"
                . "<th width='7%'  style='text-transform: uppercase;text-align:center; display:none;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.work_hours')}</th>"
                . "<th width='10%' style='text-transform: uppercase;text-align:center;'>Hrs Extra 25%</th>"
                . "<th width='10%' style='text-transform: uppercase;text-align:center;'>Hrs Extra 35%</th>"
                . "<th width='10%' style='text-transform: uppercase;text-align:center; display:none;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.night_hours')}</th>"
                . "<th width='10%' style='text-transform: uppercase;text-align:center; display:none;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.holiday_hours')}</th>"
                . "<th width='10%' style='text-transform: uppercase;text-align:center;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.lateness')}</th>"
                . "</tr>"
                . "</thead>";

        $head_weekly = "<thead>" . "<tr>" . "<th width='2%'>#</th>"
                . "<th width='7%' style='text-transform: uppercase;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.person_id')}</th>"
                . "<th width='35%' style='text-transform: uppercase;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.Nombre')}</th>"
                . "<th width='7%'  style='text-transform: uppercase;text-align:center;display:none;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.work_days_employee')}</th>"
                . "<th width='10%' style='text-transform: uppercase;display:none;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.absences')}</th>"
                . "<th width='7%'  style='text-transform: uppercase;text-align:center;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.work_hours')}</th>"
                . "<th width='9%' style='text-transform: uppercase;text-align:center;'>Hrs Extra 60%</th>"
                . "<th width='9%' style='text-transform: uppercase;text-align:center;'>Hrs Extra 100%</th>"
                . "<th width='9%' style='text-transform: uppercase;text-align:center;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.night_hours')}</th>"
                . "<th width='9%' style='text-transform: uppercase;text-align:center;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.holiday_hours')}</th>"
                . "<th width='9%' style='text-transform: uppercase;text-align:center; display:none;'>{$this->model->getAttributeLabel('assistanceSummaryEmployees.lateness')}</th>"
                . "</tr>"
                . "</thead>";

        //ITEMS
        $assistanceSummaryEmployeItems = [];
        $disabled = '';
        if (count($this->model->tempAssistanceSummaryEmployees) > 0) {
            $disabled = "$('#{$this->regime_input_id}').attr('readonly', true);"
                    . "$('#{$this->year_input_id}').attr('readonly', true);"
                    . "$('#{$this->month_input_id}').attr('readonly', true);"
                    . "$('#{$this->week_input_id}').attr('readonly', true);";
        }
        foreach ($this->model->tempAssistanceSummaryEmployees as $assistanceSummaryEmploye) {
            $attributes = [];
            $attributes["person_id"] = $assistanceSummaryEmploye->person_id;
            $attributes["work_days_employee"] = $assistanceSummaryEmploye->work_days_employee;
            $attributes["work_days_calc"] = $assistanceSummaryEmploye->work_days_calc;
            $attributes["person_name"] = $assistanceSummaryEmploye->person_name;
            $attributes["absences"] = $assistanceSummaryEmploye->absences;
            $attributes["work_hours"] = $assistanceSummaryEmploye->work_hours;
            $attributes["extrahours1"] = $assistanceSummaryEmploye->extrahours1;
            $attributes["extrahours2"] = $assistanceSummaryEmploye->extrahours2;
            $attributes["night_hours"] = $assistanceSummaryEmploye->night_hours;
            $attributes["holiday_hours"] = $assistanceSummaryEmploye->holiday_hours;
            $attributes["lateness"] = $assistanceSummaryEmploye->lateness;

            $dates = [];

            foreach ($assistanceSummaryEmploye->tempDates as $date) {
                $attributes2 = [];
                $attributes2["date"] = $date->date;
                $attributes2["work_hours"] = $date->work_hours;
                $attributes2["extrahours"] = $date->extrahours;
                $attributes2["night_hours"] = $date->night_hours;
                $attributes2["holiday_hours"] = $date->holiday_hours;
                $attributes2["night_hours"] = $date->night_hours;
                $attributes2["lateness"] = $date->lateness;
                $attributes2["absence"] = $date->absence;
                $attributes2["type_absence"] = $date->type_absence;
                $attributes2["errors"] = $date->getErrors();
                $dates[] = $attributes2;
            }

            $attributes["subItems"] = $dates;
            $attributes["errors"] = $assistanceSummaryEmploye->getErrors();
            $assistanceSummaryEmployeItems[] = $attributes;
        }

        $regimeList = CJSON::encode(Regime::getListData());
        $weekList = CJSON::encode(Calendar::getListWeekYear());
        $typeAbsenceList = CJSON::encode(Multitable::getTypeAbsences());
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        //Seteamos data
        var div = $('#{$this->id}');
        div.data('regimeList', {$regimeList});
        div.data('weekList', {$weekList});
        div.data('back_link_id','{$this->back_link_id}');
        div.data('submit_id', '{$this->form->submit_id}');
        div.data('cancel_id', '{$this->form->cancel_id}');
        div.data('title_id', '{$this->title_id}');
        div.data('row_control_id', '{$this->row_control_id}');
        div.data('div_tables_id', '{$this->div_tables_id}');
        div.data('typeAbsencesList', {$typeAbsenceList});
            
        $(document).ready(function() {                  
            
            //COUNT
            $('#{$this->id}_table').data('count', 0);
            //MODAL
            $('#{$this->id}').data('modal_id', '{$this->modal_id}');
                
            var regime_id =  $('#{$this->regime_input_id}').val();
            if(regime_id != null){
                var data = $('#{$this->id}').data('regimeList')[regime_id];
                $('#{$this->id}').data('type_period',data['type_period']);
                $('#{$this->id}').find('thead').remove();
                $('#{$this->regime_code_input_id}').val(data['regime_code']);
                if(data['type_period'] == '" . Regime::TYPE_PERIOD_MONTHLY . "'){
                    $('#{$this->week_input_id}').find('option').remove();
                    $('#{$this->week_input_id}').attr('readonly', true);
                    $('#{$this->split_week_input_id}').prop('checked', false);
                    $('#{$this->id}_table').find('thead').remove();
                    $('#{$this->id}_table').find('tbody').before(\"" . $head_monthly . "\");                          
                }
                if(data['type_period'] == '" . Regime::TYPE_PERIOD_WEEKLY . "'){
                    $('#{$this->week_input_id}').removeAttr('readonly');
                    SIANLoadWeeks('{$this->model->week}');
                    $('#{$this->id}_table').find('thead').remove();
                    $('#{$this->id}_table').find('tbody').before(\"" . $head_weekly . "\");
                }                
                SIANAssistanceSumaryBuildEmployee('#{$this->person_div_id}',$('#{$this->regime_input_id}').val());
            }
            // ------------------------------

            var xyear  = $('#{$this->year_input_id}').val();
            var xmonth = $('#{$this->month_input_id}').val();
            var xweek  = $('#{$this->week_input_id}').val();

            $.ajax({
                type: 'post',
                url: '" . Yii::app()->controller->createUrl('/project/period/datesInPeriod') . "',
                data: {
                    year: xyear, month: xmonth, week: xweek
                },
                async: false,
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },                  
                success: function(data) {
                    if(data.code == " . USREST::CODE_SUCCESS . ")
                    {
                        $('#{$this->id}').data('datesInPeriod', data.data);                                 
                    }
                    else
                    {
                        bootbox.alert(us_message(data.message, 'warning'));
                    }
                    window.active_ajax--;
                },
                error: function(request, status, error) { // if error occured
                    bootbox.alert(us_message(request.responseText, 'error'));
                    window.active_ajax--;
                },
                dataType: 'json'
            }); 
            
            if(!$('#{$this->week_input_id}').hasAttr('readonly')){
                SIANLoadWeeks('{$this->model->week}');
                //$('#{$this->week_input_id}').change();
            } 

            //MOVIMIENTOS
            var array = " . CJSON::encode($assistanceSummaryEmployeItems) . ";
            var regime = $('#{$this->id}').data('regimeList')[$('#{$this->regime_input_id}').val()];           
            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {                    
                    SIANAssistanceSummaryAddItem('{$this->id}', 
                                       '{$this->week_input_id}',
                                       '{$this->month_input_id}',
                                       '{$this->year_input_id}',
                                       '{$this->regime_input_id}',
                                        regime['control_detail'],
                                       array[i]['person_id'], 
                                       array[i]['person_name'],
                                       array[i]['work_days_employee'],  
                                       array[i]['work_days_calc'],
                                       array[i]['absences'],
                                       array[i]['work_hours'], 
                                       array[i]['extrahours1'], 
                                       array[i]['extrahours2'], 
                                       array[i]['night_hours'], 
                                       array[i]['holiday_hours'], 
                                       array[i]['lateness'], 
                                       array[i]['subItems'], 
                                       array[i]['errors']);                    
                }                
            }
            else
            {
                $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
            }
           
            SIANAssistanceSummaryUpdate('{$this->id}');
            unfocusable();
            SIANAssistanceSumaryBuildEmployee('#{$this->person_div_id}',$('#{$this->regime_input_id}').val());
            
            $('#{$this->regime_input_id}').change();  
            {$disabled}
        });
        
        // ----------------------------------
        // ------- Eventos de elementos -----
        // ----------------------------------      
        
        $('body').on('change','#{$this->regime_input_id}', function(){
            var regime_id =  $('#{$this->regime_input_id}').val();
            if(regime_id != null){
                var data = $('#{$this->id}').data('regimeList')[regime_id];
                $('#{$this->id}').data('type_period',data['type_period']);
                $('#{$this->id}').find('thead').remove();
                $('#{$this->regime_code_input_id}').val(data['regime_code']);
                if(data['type_period'] == '" . Regime::TYPE_PERIOD_MONTHLY . "'){
                    $('#{$this->week_input_id}').find('option').remove();
                    $('#{$this->week_input_id}').attr('readonly', true);
                    $('#{$this->split_week_input_id}').prop('checked', false);
                    $('#{$this->id}_table').find('thead').remove();
                    $('#{$this->id}_table').find('tbody').before(\"" . $head_monthly . "\");                            
                }
                if(data['type_period'] == '" . Regime::TYPE_PERIOD_WEEKLY . "'){
                    $('#{$this->week_input_id}').removeAttr('readonly');
                    SIANLoadWeeks('');
                    $('#{$this->id}_table').find('thead').remove();
                    $('#{$this->id}_table').find('tbody').before(\"" . $head_weekly . "\");   
                }
                SIANAssistanceSumaryBuildEmployee('#{$this->person_div_id}',$('#{$this->regime_input_id}').val());
            }
        });            

        $('body').on('change','#{$this->week_input_id}', function(){
            var year =  $('#{$this->year_input_id}').val();
            var month =  $('#{$this->month_input_id}').val();
            var data = $('#{$this->id}').data('weekList');
            var split_week = data[year][month][this.value] == 1? true : false;
            $('#{$this->split_week_input_id}').prop('checked', split_week);
        });

        $('body').on('click', '#{$this->add_employee_button_id}', function(e) {

            var div = $('#' + '{$this->id}');
            var employee_id = $('#' + '{$this->person_input_id}').val();
            var regime = div.data('regimeList')[$('#{$this->regime_input_id}').val()];      
            if(employee_id != 0){
                SIANAssistanceSummaryLoadAll('{$this->id}','{$this->controller->createUrl('/human/assistanceSummary/getAllEmployee')}'," . USREST::CODE_SUCCESS . ", '{$this->week_input_id}', '{$this->month_input_id}', '{$this->year_input_id}', '{$this->regime_input_id}', regime['control_detail'],employee_id);
                SIANAssistanceSummaryReadOnly('{$this->month_input_id}',true);
                SIANAssistanceSummaryReadOnly('{$this->year_input_id}',true);
                SIANAssistanceSummaryReadOnly('{$this->regime_input_id}',true);
                if(regime['type_period'] == '" . Regime::TYPE_PERIOD_WEEKLY . "'){
                    SIANAssistanceSummaryReadOnly('{$this->week_input_id}',true);
                }
                SIANAssistanceSumaryBuildEmployee('#{$this->person_div_id}', $('#{$this->regime_input_id}').val());
            }
        });

        $('body').on('click', '#{$this->add_all_button_id}', function(e) {

            var div = $('#' + '{$this->id}');
            var regime = div.data('regimeList')[$('#{$this->regime_input_id}').val()];  
            var table = $('#' + '{$this->id}_table');
            
            if(table.find('tbody').find('tr.sian-assistance-item').length > 0 ){

                if (confirm('Desea eliminar todo lo registrado y volver a cargar todos los empleado?'))
                {
                    SIANAssistanceSummaryRemoveAll('{$this->id}',false);
                    SIANAssistanceSummaryLoadAll('{$this->id}','{$this->controller->createUrl('/human/assistanceSummary/getAllEmployee')}'," . USREST::CODE_SUCCESS . ", '{$this->week_input_id}', '{$this->month_input_id}', '{$this->year_input_id}', '{$this->regime_input_id}',regime['control_detail'], null);
                    SIANAssistanceSummaryReadOnly('{$this->month_input_id}',true);
                    SIANAssistanceSummaryReadOnly('{$this->year_input_id}',true);
                    SIANAssistanceSummaryReadOnly('{$this->regime_input_id}',true);
                    if(regime['type_period'] == '" . Regime::TYPE_PERIOD_WEEKLY . "'){
                        SIANAssistanceSummaryReadOnly('{$this->week_input_id}',true);
                    }
                    SIANAssistanceSumaryBuildEmployee('#{$this->person_div_id}', $('#{$this->regime_input_id}').val());
                }
            }else{
                SIANAssistanceSummaryLoadAll('{$this->id}','{$this->controller->createUrl('/human/assistanceSummary/getAllEmployee')}'," . USREST::CODE_SUCCESS . ", '{$this->week_input_id}', '{$this->month_input_id}', '{$this->year_input_id}', '{$this->regime_input_id}',regime['control_detail'], null);
                SIANAssistanceSummaryReadOnly('{$this->month_input_id}',true);
                SIANAssistanceSummaryReadOnly('{$this->year_input_id}',true);
                SIANAssistanceSummaryReadOnly('{$this->regime_input_id}',true);
                if(regime['type_period'] == '" . Regime::TYPE_PERIOD_WEEKLY . "'){
                    SIANAssistanceSummaryReadOnly('{$this->week_input_id}',true);
                }
                SIANAssistanceSumaryBuildEmployee('#{$this->person_div_id}', $('#{$this->regime_input_id}').val());
            }
        });  

        $('body').on('click', '#{$this->delete_all_button_id}', function(e) {
            var table = $('#' + '{$this->id}');
            var regime = table.data('regimeList')[$('#{$this->regime_input_id}').val()]; 
            SIANAssistanceSummaryRemoveAll('{$this->id}',true);
            SIANAssistanceSummaryReadOnly('{$this->month_input_id}',false);
            SIANAssistanceSummaryReadOnly('{$this->year_input_id}',false);
            SIANAssistanceSummaryReadOnly('{$this->regime_input_id}',false);
            if(regime['type_period'] == '" . Regime::TYPE_PERIOD_WEEKLY . "'){
                SIANAssistanceSummaryReadOnly('{$this->week_input_id}',false);
            }
            SIANAssistanceSumaryBuildEmployee('#{$this->person_div_id}', $('#{$this->regime_input_id}').val());
        });

        // ----------------------------------
        // ------- Eventos de clases --------
        // ----------------------------------

        $('body').on('keyup','#{$this->id} .input-number', function(){
            this.value = (this.value + '').replace(/[^0-9]/g, '');
        });

        $('body').on('change','#{$this->id} .sian-assistance-item-absences', function(){
            var days_in_month_employee = $(this).parent().parent().find('input.sian-assistance-item-work-days').val();
            var absence = $(this).val();
            if(parseInt(absence)>parseInt(days_in_month_employee)){
                $(this).val(0);
            }
//                var absence = $(this).val();
//                var days_in_month = SIANAssistanceSummaryDaysInMonth($('#{$this->month_input_id}').val(), $('#{$this->year_input_id}').val());
//                $(this).parent().parent().find('input.sian-assistance-item-work-days').val(days_in_month-absence);
//                
//                var days_in_accounting_month = $('#{$this->work_days_input_id}').val();
//                var days_calc = (days_in_accounting_month-absence < 0)? 0 : days_in_accounting_month-absence;
//                $(this).parent().parent().find('input.sian-assistance-item-work-days-calc').val(days_calc);
        });

        $('body').on('change','#{$this->id} .sian-assistance-item-extrahours1', function(){
            var cantidad = $(this).val();
            if(parseInt(cantidad)>parseInt(60)){
                $(this).val(0);
            }
        });

        $('body').on('change','#{$this->id} .sian-assistance-item-extrahours2', function(){
            var cantidad = $(this).val();
            if(parseInt(cantidad)>parseInt(150)){
                $(this).val(0);
            }
        });

        $('body').on('change','#{$this->id} .sian-assistance-item-lateness', function(){
            var cantidad = $(this).val();
            if(parseInt(cantidad)>parseInt(14400)){
                $(this).val(0);
            }
        });

        $('body').on('change','#{$this->id} .sian-change-week', function(){
            if(!$('#{$this->week_input_id}').hasAttr('readonly')){
                SIANLoadWeeks('');   
                //$('#{$this->week_input_id}').change();
            }               
        });

        $('body').on('change','#{$this->id} .sian-change-dates', function(){
            var xyear  = $('#{$this->year_input_id}').val();
            var xmonth = $('#{$this->month_input_id}').val();
            var xweek  = $('#{$this->week_input_id}').val();            

            $.ajax({
                type: 'post',
                url: '" . Yii::app()->controller->createUrl('/project/period/datesInPeriod') . "',
                data: {
                    year: xyear, month: xmonth, week: xweek
                },
                async: true,
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },                 
                success: function(data) {
                    if(data.code == " . USREST::CODE_SUCCESS . ")
                    {
                        $('#{$this->id}').data('datesInPeriod', data.data);                                 
                    }
                    else
                    {
                        bootbox.alert(us_message(data.message, 'warning'));
                    }
                    window.active_ajax--;
                },
                error: function(request, status, error) { // if error occured
                    bootbox.alert(us_message(request.responseText, 'error'));
                    window.active_ajax--;
                },
                dataType: 'json'
            });                          
        });
        
        $('body').on('change','.sian-assistance-summary-date-absence-checkbox', function(){            
            if($(this).is(':checked')){
                $(this).parent().parent().find('select.sian-assistance-summary-date-type-absence').removeAttr('readonly');
            }else{
                var cbxTypeAbsence = $(this).parent().parent().find('select.sian-assistance-summary-date-type-absence');
                cbxTypeAbsence.attr('readonly',1);
                cbxTypeAbsence.val('');
            }
        });
        
        ", CClientScript::POS_END);

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "    
        // LISTA DE CAMPOS - COMBOBOX SIMPLE
        function SIANAssistanceSumaryBuildEmployee(container, regime_id) {
        
            var content = $(document.createElement('div'));
            var label = $(document.createElement('label')).addClass('control-label').text('Colaborador :');
            var element = $(document.createElement('select')).addClass('form-control').attr({id: '{$this->person_input_id}', name: '{$this->person_input_id}', style: 'width: 100%;'});

            content.append(label).append(element);
                      
            var a_ids = [];             
            $('#{$this->id}_table').find('input.sian-assistance-item-person-id').each(function( index ) {
               a_ids.push($(this).val());               
            });    

            $.ajax({
                data: {a_ids: a_ids, regime_id : regime_id}, 
                url: '/admin/components/listEmployee',                
                type: 'POST',  
                async: false,
                dataType: 'json',
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },                 
                success: function (response) {
                    var data = response.data, l = data.length, i;
                    if (l > 0) {
                        element.append($(document.createElement('option')).attr({value: 0}).text('" . Strings::SELECT_OPTION . "'));
                        for (i = 0; i < l; i++) {
                            element.append($(document.createElement('option')).attr({value: data[i].id}).text(data[i].value));
                        }
                    }
                    window.active_ajax--;
                },
                error: function(request, status, error) { // if error occured
                    bootbox.alert(us_message(request.responseText, 'error'));
                    window.active_ajax--;
                },                
            });
            
            $(container).html(content);
            $('#{$this->person_input_id}').select2({
                language: 'en',
                placeholder: '" . Strings::SELECT_OPTION . "'
            });
        }

        function SIANAssistanceSummaryRemoveItem(table_id, id, confirmation, weekElement, monthElement, yearElement, regimenElement)
        {
            if (!$('#' + id).find('a.sian-assistance-summary-remove-item').hasAttr('disabled')) {

                if (confirmation ? confirm('¿Está seguro de eliminar este ítem?') : true)
                {
                    var table = $('#' + table_id);
                    var count = parseInt(table.data('count'));
                    var regime = $('#{$this->id}').data('regimeList')[ $('#' + regimenElement).val()];
                    
                    $('#' + id).remove();
                    //Borramos subtabla
                    SIANAssitanceSummaryRemoveTable(id + '_table', false);
        
                    if (count === 1)
                    {
                        table.find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
                        SIANAssistanceSummaryReadOnly(monthElement,false);
                        SIANAssistanceSummaryReadOnly(yearElement,false);
                        SIANAssistanceSummaryReadOnly(regimenElement,false);
                        if(regime['type_period'] == '" . Regime::TYPE_PERIOD_WEEKLY . "'){
                            SIANAssistanceSummaryReadOnly(weekElement,false);
                        }
                    }
                    SIANAssistanceSumaryBuildEmployee('#{$this->person_div_id}',$('#{$this->regime_input_id}').val());
                    //COUNT DE ITEMS
                    table.data('count', count - 1);
                    SIANAssistanceSummaryUpdate(table_id);
                }
            }
        }
        
        function SIANAssistanceSummaryRemoveAll(div_id, confirmation)
        {
            if (confirmation ? confirm('¿Está seguro de eliminar todo lo registrado?') : true)
            {
                var div = $('#' + div_id);
                var table = $('#' + div_id + '_table'); //main table
                var div_tables_id = div.data('div_tables_id');
                
                var div_tables = $('#' + div_tables_id);
                div_tables.find('table').remove();

                table.find('tbody').find('tr').remove();
                table.find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'><em>" . Strings::NO_DATA . "</em></td></tr>');
                table.data('count', 0);                              
                
                SIANAssistanceSumaryBuildEmployee('#{$this->person_div_id}',$('#{$this->regime_input_id}').val());
                SIANAssistanceSummaryUpdate(table.prop('id'));
            }
        }

        function SIANLoadWeeks(current){
        
            var xyear = $('#{$this->year_input_id}').val();
            var xmonth = $('#{$this->month_input_id}').val();

            $.ajax({
                    type: 'post',
                    url: '" . Yii::app()->controller->createUrl('/components/listWeek') . "',
                    data: {
                        year: xyear, month: xmonth,
                    },
                    beforeSend: function (xhr) {
                        window.active_ajax++;
                        //Ocultamos los tooltip
                        $('div.ui-tooltip').remove();
                    },                           
                    success: function(data) {
                        if(data.code == " . USREST::CODE_SUCCESS . ")
                        {
                            var xdata = data.data,  l = xdata.length, i;
                            $('#{$this->week_input_id}').find('option').remove();
                            if (l > 0) {

                                var found = false;

                                for (i = 0; i < l; i++) {   
                                   $('#{$this->week_input_id}').append($(document.createElement('option')).attr('value', xdata[i].id).text(xdata[i].value));
                                    
                                    if (xdata[i].id === current)
                                    {
                                        found = true;
                                    }                                       

                                }
                                
                                if (found)
                                {
                                    $('#{$this->week_input_id}').val(current);
                                }
    
                                $('#{$this->week_input_id}').change();
                            }      
                        }
                        else
                        {
                            bootbox.alert(us_message(data.message, 'warning'));
                        }
                        window.active_ajax--;
                    },
                    error: function(request, status, error) { // if error occured
                        bootbox.alert(us_message(request.responseText, 'error'));
                        window.active_ajax--;
                    },
                    dataType: 'json'
                });
            
        }
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array('title' => "Principal",
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
            )
        ));
        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'regime_id', Regime::getRegime('id'), array('id' => $this->regime_input_id,
            'class' => 'sian-change-dates'));
        echo $this->form->hiddenField($this->model, 'regime_code', array('id' => $this->regime_code_input_id));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'year', Calendar::getListYear(), array('id' => $this->year_input_id,
            'readonly' => !$this->model->isNewRecord,
            'class' => 'sian-change-week sian-change-dates'));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'period', USTime::getMonths(false), array('id' => $this->month_input_id,
            'readonly' => !$this->model->isNewRecord,
            'class' => 'sian-change-week sian-change-dates'));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'week', [], array('id' => $this->week_input_id,
            'readonly' => !$this->model->isNewRecord,
            'class' => 'sian-change-dates'));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo "<BR>";
        echo $this->form->checkboxRow($this->model, 'split_week', array(
            'id' => $this->split_week_input_id,
            'inline' => true,
            'disabled' => true,
        ));
        echo "</div>";

        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-9 col-md-5 col-sm-5 col-xs-12'>";
        echo $this->form->hiddenField($this->model, 'work_days', array(
            'id' => $this->work_days_input_id,
            'maxlength' => 2,
            'value' => 30,
            'placeholder' => 'Días Laborables',
            'readonly' => true,
        ));
        echo $this->form->textFieldRow($this->model, 'observation', array(
            'maxlength' => 200,
            'placeholder' => $this->model->getAttributeLabel('observation'),
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-3 col-xs-12'>";
        $this->widget('application.widgets.USSwitch', array('model' => $this->model, 'attribute' => 'status'));
        echo "</div>";
        echo "</div>";

        $this->endWidget();

        $this->beginWidget('booster.widgets.TbPanel', array('title' => "Detalle",
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempAssistanceSummaryEmployees') ? 'us-error' : ''
            )
        ));

        echo "<div class='row' id='" . $this->row_control_id . "'>";
        echo "<div class='col-lg-7 col-md-7 col-sm-12 col-xs-12'>";
        echo "<div id ='" . $this->person_div_id . "'></div>";
        echo "</div>";

        echo "<br/>";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-1'>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_employee_button_id,
            'block' => true,
            'context' => 'primary',
            'icon' => 'fa fa-lg fa-plus white',
            'size' => 'default',
            'title' => 'Añadir Empleado',
            'class' => 'add',
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-1'>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_all_button_id,
            'block' => true,
            'context' => 'primary',
            'icon' => 'fa fa-lg fa-plus-circle white',
            'size' => 'default',
            'title' => 'Añadir Todos',
            'class' => 'add',
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-1'>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->delete_all_button_id,
            'block' => true,
            'context' => 'danger',
            'icon' => 'fa fa-lg fa-times-circle white',
            'size' => 'default',
            'title' => 'Borrar Todos',
            'class' => 'add',
        ));
        echo "</div>";
        echo "</div>";

        echo "<hr>";
        echo "<table id='{$this->id}_table' class='table table-condensed table-hover sian-assistance-summary-main'>";
        echo "<thead></thead>";
        echo "<tbody></tbody>";
        echo "<tfoot>";
        echo "</tfoot>";
        echo "</table>";
        echo "<h3  id=" . $this->title_id . "></h3><br>";
        echo "<div id=" . $this->div_tables_id . " class='sian-assistance-summary-tables'>";
        echo "</div>";
        echo "<a id='" . $this->back_link_id . "' style='visibility:hidden;'  onclick='SIANAssistanceSummaryBackToMain(\"" . $this->id . "\", 100);' href='javascript:void(0)'><span class='fa fa-lg fa-backward black'></span> Regresar a vista resumida</a>";

        $this->endWidget();
    }

}
