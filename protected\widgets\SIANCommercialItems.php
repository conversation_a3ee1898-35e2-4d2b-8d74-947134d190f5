<?php

class SIANCommercialItems extends CWidget {

    public $id;
    public $model;
    public $dataProvider;
    public $route = '/logistic/product/preview';
    public $title = 'Lista de productos';
    public $modal_id = null;
    public $compact = false;
    public $by_grouping = false;
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //CONTROL
        if (is_null($this->model)) {
            throw new Exception("Debe especificar la instancia del modelo 'CommercialMovement'!");
        }
        if (is_null($this->dataProvider)) {
            throw new Exception("Debe especificar el dataProvider!");
        }
        //
        $this->dataProvider->pagination = false;
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->dataProvider->getTotalItemCount() > 0) {

            if (!$this->compact) {
                echo "<h4>{$this->title}:</h4>";
            }
            //Accesos
            $grid_id = $this->id;
            $route1 = $this->route;
            $route2 = '/movement/getCommercialItems';
            $access1 = $this->controller->checkRoute($route1);
            $access2 = $this->controller->checkRoute('/logistic/merchandise/kardex');
            $currency = $this->model->movement->currency;
            $modal_id = $this->modal_id;
            $igv_affection_items = CommercialMovementProduct::getIGVAffectionItems();
            //GRID
            $columns = [];

            array_push($columns, array(
                'name' => 'item_number',
                'headerHtmlOptions' => array('style' => 'width:2%; text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center;'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->item_number;
                },
            ));

            if($this->by_grouping){
                array_push($columns, array(
                    'name' => 'parent_item_movement_document',
                    'headerHtmlOptions' => array('style' => 'width:5%;'),
                    'type' => 'raw',
                    'value' => function ($row)  {
                        return Yii::app()->controller->widget('application.widgets.USLink', array(
                                    'label' => $row->parent_item_movement_document,
                                    'title' => 'Ver Documento',
                                    'route' => "/{$row->parent_item_movement_route}/view",
                                    'params' => array(
                                        'id' => $row->parent_item_movement_id,
                                    ),
                                    'target' => $row->parent_item_movement_id,
                                    'visible' => true,
                        ), true);
                    },
                ));
            }

            array_push($columns, array(
                'name' => 'product_id',
                'headerHtmlOptions' => array('style' => 'width:5%; text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center'),
                'type' => 'raw',
                'value' => function ($row) {
                    return isset($row->product_id) ? $row->product_id : 'Grupo';
                },
            ));

            array_push($columns, array(
                'name' => 'product_name',
                'headerHtmlOptions' => array('style' => 'width:' . $this->by_grouping == 1 ? '18' : '27' .'%;'),
                'type' => 'raw',
                'value' => function ($row) use ($access1, $route1, $route2, $modal_id) {

                    if (in_array($row->product_type, [Product::TYPE_COMBO, Product::TYPE_GROUP])) {
                        return Yii::app()->controller->widget('application.widgets.USLink', array(
                                    'route' => $route2,
                                    'label' => $row->product_name,
                                    'title' => 'Ver ítems del combo o grupo',
                                    'class' => 'form',
                                    'data' => array(
                                        'id' => $row->item_id,
                                        'parent_id' => $modal_id,
                                    ),
                                    'visible' => true,
                                        ), true);
                    } else {

                        $s_preffix = "";
                        if (isset($row->promotion_item_id)) {
                            $s_preffix = "<i title='En promoción' class='fa fa-lg fa-star blue'></i> ";
                        } elseif (isset($row->promotion_gift_option_id)) {
                            $s_preffix = "<i title='Regalo' class='fa fa-lg fa-gift blue'></i> ";
                        }

                        return $s_preffix . Yii::app()->controller->widget('application.widgets.USLink', array(
                                    'route' => $route1,
                                    'label' => $row->product_name,
                                    'title' => 'Ver producto',
                                    'class' => 'form',
                                    'data' => array(
                                        'id' => $row->product_id,
                                        'parent_id' => $modal_id,
                                    ),
                                    'visible' => $access1,
                                        ), true);
                    }
                },
            ));

            array_push($columns, array(
                'name' => 'product_type',
                'headerHtmlOptions' => array('style' => 'width:2%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->product_type;
                },
            ));

            array_push($columns, array(
                'name' => 'pres_quantity',
                'headerHtmlOptions' => array('style' => 'width:6%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->pres_quantity;
                },
            ));
            array_push($columns, array(
                'name' => 'measure_name',
                'headerHtmlOptions' => array('style' => 'width:6%;text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center'),
                'type' => 'raw',
                'value' => function ($row) {
                    if ($row->product_type === Product::TYPE_GROUP) {
                        return isset($row->measure_id) ? $row->abbreviation : Yii::app()->controller->getDefaultMerchandiseMeasure()->abbreviation;
                    } else {
                        return isset($row->measure_name) ? $row->measure_name : Yii::app()->controller->getDefaultMerchandiseMeasure()->abbreviation;
                    }
                },
            ));

            array_push($columns, array(
                'name' => 'igv_affection',
                'headerHtmlOptions' => array('style' => 'width:7%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right'),
                'type' => 'raw',
                'value' => function ($row) use ($igv_affection_items) {
                    return $igv_affection_items[$row->igv_affection];
                },
            ));

            array_push($columns, array(
                'name' => 'igv_percentage',
                'headerHtmlOptions' => array('style' => 'width:5%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->igv_percentage . '%';
                },
            ));

            array_push($columns, array(
                'name' => "price_{$currency}",
                'headerHtmlOptions' => array('style' => 'width:7%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right;'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->displayValue('price', $row->currency, CommercialMovement::IFIXED);
                },
            ));

            array_push($columns, array(
                'name' => "discount_{$currency}",
                'headerHtmlOptions' => array('style' => 'width:7%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right;'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->displayValue('discount', $row->currency, CommercialMovement::IFIXED);
                },
            ));

            array_push($columns, array(
                'name' => "crude_{$currency}",
                'headerHtmlOptions' => array('style' => 'width:7%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->displayValue('crude', $row->currency);
                },
            ));

            array_push($columns, array(
                'name' => "net_{$currency}",
                'headerHtmlOptions' => array('style' => 'width:7%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->displayValue('net', $row->currency);
                },
            ));

            array_push($columns, array(
                'name' => "igv_{$currency}",
                'headerHtmlOptions' => array('style' => 'width:7%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->displayValue('igv', $row->currency);
                },
            ));

            array_push($columns, array(
                'name' => "total_{$currency}",
                'headerHtmlOptions' => array('style' => 'width:7%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->displayValue('total', $row->currency);
                },
            ));

            array_push($columns, array(
                'header' => 'Opc.',
                'type' => 'raw',
                'headerHtmlOptions' => array('style' => 'width:3%;text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center'),
                'value' => function ($row) use ($grid_id, $access2) {
                    //Si bloquea stock
                    if (isset($row->direction) && isset($row->warehouse_id) && $row->product_type === Product::TYPE_MERCHANDISE) {
                        $i_kardex_mode = '';
                        $s_label = '';
                        switch ($row->direction) {
                            case Kardex::DIRECTION_COMING_IN:
                                $i_kardex_mode = Kardex::MODE_PURCHASE;
                                $s_label = '(para compras)';
                                break;
                            case Kardex::DIRECTION_COMING_OUT:
                                $i_kardex_mode = Kardex::MODE_SALE;
                                $s_label = '(disponible)';
                                break;
                            default:
                                throw new Exception('Dirección de kardex no soportada');
                        }

                        return Yii::app()->controller->widget('application.widgets.USGridButtons', array(
                                    'id' => $row->product_id,
                                    'grid_id' => $grid_id,
                                    'buttons' => array(
                                        array('icon' => 'fa fa-retweet fa-lg', 'title' => 'Ver kardex ' . $s_label, 'route' => '/logistic/merchandise/kardex', 'params' => array('warehouse_id' => $row->warehouse_id, 'mode' => $i_kardex_mode), 'target' => $row->movement_id . "_" . $row->product_id, 'visible' => $access2),
                                    )), true);
                    }

                    return '-';
                },
            ));

            $gridParams = array(
                'id' => $grid_id,
                'type' => 'hover condensed',
                'dataProvider' => $this->dataProvider,
                'enableSorting' => true,
                'selectableRows' => 0,
                'columns' => $columns,
                'template' => '{items}',
                'nullDisplay' => Strings::NONE,
            );

            $this->widget('application.widgets.USGridView', $gridParams);
        }

        if (!$this->compact) {
            $this->widget('application.widgets.SIANCommercialObservationAndAmounts', [
                'model' => $this->model,
            ]);
        }
    }

}
