<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'staging3supermercadosmia.siansystem.com/admin';
$domain = 'https://staging3supermercadosmia.siansystem.com';
$report_domain = 'rstaging3.siansystem.com';
$org = 'supermercadosmia';
$us = 'us_staging3';

//SIAN 2
$domain2 = 'https://staging3supermercadosmia.siansystem.com';
$domain_react_app = 'http://staging3supermercadosmia2.siansystem.com';
$api_sian = 'http://api-staging3.siansystem.com/';

$database_server = '161.132.48.88';
$database_name = 'supermercadosmia_staging3';
$database_username = 'sian_test';
$database_password = '75nppt6vr57lx4';
$database_sian = 'sian_staging';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
$database_sian = 'sian_staging';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'supermercadosmia.p12';
$e_billing_certificate_pass = 'HtpngSBeBTUAGR8k';
$e_billing_ri = '';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20607629481BILLING7',
        'password' => 'Wu4121ds'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_STAGING;
$environment = YII_ENVIRONMENT_STAGING;
