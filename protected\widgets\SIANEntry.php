<?php

/**
 * No necesita scripts para cambios de moneda o de tipo de cambio
 */
class SIANEntry extends CWidget {

    const MODE_NORMAL = 0;
    const MODE_PROVISION = 1;

    public $id;
    public $form;
    public $model;
    public $mode = self::MODE_NORMAL;
    public $readonly = false;
    public $field = Scenario::TYPE_DYNAMIC;
    public $show_panel = true;
    //CONTROL
    public $fixed = 2;
    public $step = 0.01;
    public $account_min_input_length = 2;
    public $owner_min_input_length = 2;
    public $combination_min_input_length = 2;
    //IDS
    public $provisioning_id;
    public $account_code_id;
    public $detail_id;
    public $amount_id;
    public $column_id;
    public $entry_number_id;
    //CONFIG
    //PRIVATE
    private $controller;
    private $readonly_template = false;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        if ($this->model->scenario->type === Scenario::TYPE_AUTOMATIC && !isset($this->provisioning_id)) {
            throw new Exception('Debe especificar el ID del widget SIANProvisioningSelector');
        }
        //PUBLIC IDS
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->account_code_id = isset($this->account_code_id) ? $this->account_code_id : $this->controller->getServerId();
        $this->detail_id = isset($this->detail_id) ? $this->detail_id : $this->controller->getServerId();
        $this->amount_id = isset($this->amount_id) ? $this->amount_id : $this->controller->getServerId();
        $this->column_id = isset($this->column_id) ? $this->column_id : $this->controller->getServerId();
        $this->entry_number_id = isset($this->entry_number_id) ? $this->entry_number_id : $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('other/select2/js/select2.min.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
        SIANAssets::registerCssFile('other/select2/css/select2.css');
        SIANAssets::registerScriptFile('js/us-span.js');
        SIANAssets::registerScriptFile('js/us-select2.js');
        SIANAssets::registerScriptFile('js/us-cost-level.js');
        SIANAssets::registerScriptFile('js/sian-entry.js');

        //ITEMS
        $a_target_ids = $this->_getTargetIds($this->model->accountingMovement->tempEntries);

        $entryItems = [];
        foreach ($this->model->accountingMovement->tempEntries as $entry) {
            //En modo normal NO se agregan los destinos
            if (($entry->destiny == 0 || $this->mode === self::MODE_PROVISION) && !in_array($entry->dynamic_account, [Account::WILDCARD_MISSING, Account::WILDCARD_SURPLUS])) {
                $attributes = [];
                $attributes['entry_id'] = $entry->entry_id;
                $attributes['account_code'] = $entry->account_code;
                $attributes['account_name'] = $entry->account_name;
                $attributes['detail'] = isset($entry->detail) ? $entry->detail : '';
                $attributes['amount'] = $entry->amount;
                $attributes['column'] = $entry->column;
                $attributes['entry_number'] = $entry->entry_number;
                $attributes['field'] = isset($entry->field) ? $entry->field : '';
                $attributes['exchange_rate'] = $entry->exchange_rate;
                $attributes['expiration_date'] = $entry->expiration_date;
                $attributes['dynamic_account'] = isset($entry->dynamic_account) ? $entry->dynamic_account : '';
                $attributes['owner'] = $entry->owner;
                $attributes['owner_id'] = $entry->owner_id;
                $attributes['owner_name'] = $entry->owner_name;
                $attributes['fee_number'] = $entry->fee_number;
                $attributes['destiny_type'] = isset($entry->destiny_type) ? $entry->destiny_type : '';
                $attributes['has_combination'] = isset($entry->has_combination) ? $entry->has_combination : 0;
                $attributes['combination_id'] = isset($entry->combination_id) ? $entry->combination_id : '';
                $attributes['combination_name'] = isset($entry->combination_name) ? $entry->combination_name : '';
                $attributes['target_ids'] = isset($entry->account_code) && isset($a_target_ids[$entry->account_code]) ? $a_target_ids[$entry->account_code] : [];
                $attributes['destiny'] = $entry->destiny;
                $attributes['locked'] = $entry->locked;
                $attributes['distributable'] = $entry->distributable;
                $attributes['errors'] = $entry->getErrors();

                $entryItems[] = $attributes;
            }
        }

        if ($this->model->scenario->type == Scenario::TYPE_DYNAMIC) {
            $this->readonly_template = !Yii::app()->controller->checkRoute("/{$this->model->route}/has_no_dynamic");
        }

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        //VAR
        var grid = $('#{$this->id}'); 
        grid.data('fixed', {$this->fixed});
        grid.data('step', {$this->step});
        grid.data('count', 0);
        grid.data('exchange_rate', {$this->model->exchange_rate});
        grid.data('expiration_date', '{$this->model->emission_date}');
        //
        grid.data('url', '{$this->controller->createUrl("/widget/select2Items")}');
        //
        grid.data('" . Account::OWNER . "-dp', '{$this->getDp(Account::OWNER)}');
        grid.data('" . Organization::OWNER . "-dp', '{$this->getDp(Organization::OWNER)}');
        grid.data('" . Person::OWNER . "-dp', '{$this->getDp(Person::OWNER)}');
        grid.data('" . Warehouse::OWNER . "-dp', '{$this->getDp(Warehouse::OWNER)}');
        grid.data('" . FixedAsset::OWNER . "-dp', '{$this->getDp(FixedAsset::OWNER)}');
        //
        grid.data('" . Account::OWNER . "-scenario', '{$this->getScenario(Account::OWNER)}');
        grid.data('" . Organization::OWNER . "-scenario', '{$this->getScenario(Organization::OWNER)}');
        grid.data('" . Person::OWNER . "-scenario', '{$this->getScenario(Person::OWNER)}');
        grid.data('" . Warehouse::OWNER . "-scenario', '{$this->getScenario(Warehouse::OWNER)}');
        grid.data('" . FixedAsset::OWNER . "-scenario', '{$this->getScenario(FixedAsset::OWNER)}');
        //
        grid.data('" . Account::OWNER . "-url', '{$this->controller->createUrl('/accounting/account/preview')}');
        grid.data('" . Organization::OWNER . "-url', '{$this->controller->createUrl('/administration/organization/preview')}');
        grid.data('" . Person::OWNER . "-url', '{$this->controller->createUrl('/administration/person/preview')}');
        grid.data('" . Warehouse::OWNER . "-url', '{$this->controller->createUrl('/logistic/warehouse/preview')}');
        grid.data('" . FixedAsset::OWNER . "-url', '');            
        //
        grid.data('mode', {$this->mode});
        //
        var ownerItems = " . CJSON::encode(Account::getOwnerListData()) . ";
        var columnItems = " . CJSON::encode(Entry::getColumnItems(false)) . ";
        var entryItems = " . CJSON::encode($entryItems) . ";
        var currency = '{$this->model->currency}';
        var readonly = " . json_encode($this->readonly) . ";
        var readonly_template = " . json_encode($this->readonly_template) . ";

        //AJAX
        $.ajax({
            type: 'post',
            url: '{$this->controller->createUrl('/accounting/account/getAssociative')}',
            data: {},
            async: false,//Por ahora tiene que ser síncrono
            beforeSend: function (xhr) {
                window.active_ajax++;
                //Ocultamos los tooltip
                $('div.ui-tooltip').remove();
            },                   
            success: function (data) {
                SIANEntryInit('{$this->id}', data, ownerItems, columnItems, entryItems, currency, readonly, readonly_template);
                window.active_ajax--;
            },
            error: function (request, status, error) { // if error occured
                window.active_ajax--;
                bootbox.alert(us_message(request.responseText, 'error'));
            },
            dataType: 'json'
        });
       
        //Actualizar dinámicamente
        $('#{$this->id}').data('automaticEntry', function(){

            var grid = $('#{$this->id}');
            var ndynamics = " . (in_array($this->model->route, ["accounting/adjust", "accounting/closing", "accounting/closingBalance"]) ? "grid.data('dynamics');" : "[]") . "     
            var isClosingBalanceAccount = " . ($this->model->route == "accounting/closingBalance" ? "1" : "0") . ";     
            var exchange_rate = grid.data('exchange_rate');
            var expiration_date = grid.data('expiration_date');

            var provisionData = $('#{$this->provisioning_id}').data('getData')();

            if(provisionData.year != '' && provisionData.year != null && provisionData.period != '' && provisionData.period != null){

                waitingDialog.show(STRINGS_WAITING_MESSAGE);
                //AJAX
                $.ajax({
                    type: 'post',
                    url: '{$this->controller->createUrl('/' . $this->model->route . '/automaticEntry')}',
                    data: {
                        dynamics: ndynamics, 
                        route: '{$this->model->route}', 
                        isClosingBalanceAccount: isClosingBalanceAccount, 
                        year: provisionData.year, 
                        period: provisionData.period 
                    },
                    beforeSend: function (xhr) {
                        window.active_ajax++;
                        //Ocultamos los tooltip
                        $('div.ui-tooltip').remove();
                    },                       
                    success: function (data) {
                        waitingDialog.hide();
                        SIANEntryDynamic('{$this->id}', data, exchange_rate, expiration_date, false);                           
                        window.active_ajax--;
                    },
                    error: function (request, status, error) { // if error occured
                        waitingDialog.hide();

                        var s_message = processError(request.responseText);
                        bootbox.alert(us_message(s_message, 'error'));
                        window.active_ajax--;
                    },
                    dataType: 'json'
                });   
            }
        });
                
        $('#{$this->id}').data('changeOperation', function(changeData){

            var table = $('#{$this->id}');
            var exchange_rate = table.data('exchange_rate');
            var expiration_date = table.data('expiration_date');
                
            //Guardamos las dinámicas
            table.data('dynamics', changeData.dynamics);
            //Si ya está listo y hubo un cambio real
            if(changeData.realChange)
            {
                var xcontinue = true;
                if(table.data('count') > 0)
                {
                    xcontinue = confirm('El asiento no está vacío. ¿Desea regenerarlo con la dinámica de la operación elegida?');
                }

                if(xcontinue)
                {
                    //Generamos asiento
                    " . ($this->model->scenario->type !== Scenario::TYPE_AUTOMATIC ? "SIANEntryDynamic('{$this->id}', changeData.dynamics, exchange_rate, expiration_date, true);" : "") . "  
                    " . ($this->model->scenario->type === Scenario::TYPE_AUTOMATIC ? "$('#{$this->id}').data('automaticEntry')();" : "") . "
                }

                SIANEntrySortable('{$this->id}');
            }
        }); 
        
        $('#{$this->id}').data('changeEmission', function(changeData){

            var tableObj = $('#{$this->id}');
            tableObj.data('expiration_date', changeData.emission_date);

            SIANEntryUpdateExpiration('{$this->id}');
        }); 
        
        $('#{$this->id}').data('changeExchange', function(exchange_rate){
            var table = $('#{$this->id}');
                
            //Seteamos
            table.data('exchange_rate', exchange_rate); 
            
            var fixed = 3;

            table.find('tr.sian-entry-table-item').each(function (index) {
                var row = $(this);
                var amount = row.find('input.sian-entry-table-item-exchange-rate').floatVal(fixed, exchange_rate);
            });
        }); 
        
        $('#{$this->id}').data('changeBusinessUnit', function(changeData){
            var tableObj = $('#{$this->id}');
            tableObj.data('business_unit_combination_id', changeData.combination_id);
            SIANEntryRefreshOverwriteIds('{$this->id}');
        });
        
        " . (!$this->readonly ? "
                    
            $('body').on('click', 'span.sian-entry-table-item-account-code-span', function(e) {

                var spanObj = $(this);
                var span_id = spanObj.attr('id');
                var owner = spanObj.data('owner');
                var preload = spanObj.data('preload');
                var initial_value = spanObj.data('initial_value');

                var dp = $('#{$this->id}').data(owner + '-dp');
                var scenario = $('#{$this->id}').data(owner + '-scenario');
                
                USSelect2Init(span_id, dp, scenario, {preload: preload, initial_value: initial_value}, {$this->account_min_input_length});
            });
            
            $('body').on('click', 'span.sian-entry-table-item-owner-id-span', function(e) {

                var spanObj = $(this);
                var span_id = spanObj.attr('id');
                var owner = spanObj.data('owner');
                var account = spanObj.data('account');
                
                if(owner === '" . Combination::OWNER . "')
                {
                    USCostLevelInit(span_id, 1, {$this->combination_min_input_length}, 400);
                }
                else
                {
                    if(owner === '" . FixedAsset::OWNER . "')
                    {
                        var dp = $('#{$this->id}').data(owner + '-dp');
                        var scenario = $('#{$this->id}').data(owner + '-scenario');
                        var extra = {account : account};

                        USSelect2Init(span_id, dp, scenario, extra, {$this->owner_min_input_length});
                        $('#' + span_id).data('account', account);
                    }else{
                        var dp = $('#{$this->id}').data(owner + '-dp');
                        var scenario = $('#{$this->id}').data(owner + '-scenario');

                        USSelect2Init(span_id, dp, scenario, [], {$this->owner_min_input_length});
                    }                    
                }
            });
            
            $('body').on('click', 'span.sian-entry-table-item-combination-id-span', function(e) {

                var spanObj = $(this);
                var span_id = spanObj.attr('id');

                USCostLevelInit(span_id, 0, {$this->combination_min_input_length}, 400);
            });
        " : "") . "
         
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        //Si se muestra el panel
        if ($this->show_panel) {
            $this->beginWidget('booster.widgets.TbPanel', array(
                'title' => $this->model->accountingMovement->getAttributeLabel('entries'),
                'headerIcon' => 'list',
                'htmlOptions' => array(
                    'class' => 'sian-entry ' . ($this->model->accountingMovement->hasErrors('tempEntries') ? 'us-error' : '')
                )
            ));
        }

        echo CHtml::tag('table', array(
            'id' => $this->id,
            'class' => 'table table-condensed table-hover sian-entry-table ' . ($this->model->accountingMovement->hasErrors('tempEntries') && $this->mode === self::MODE_PROVISION ? 'us-error' : ''),
            'data-readonly' => $this->readonly,
                ), $this->renderTable(), true);

        //Si es modo normal, mostramos campo para observaciones
        if ($this->mode === self::MODE_NORMAL) {
            echo "<hr>";
            echo "<div class='row'>";
            echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
            echo $this->form->textAreaRow($this->model, 'observation', array(
                'rows' => 3,
                'maxlength' => 300,
                'required' => $this->model->isObservationRequired()
            ));
            echo "</div>";
            echo "</div>";
        }

        //Si se muestra el panel
        if ($this->show_panel) {
            $this->endWidget();
        }
    }

    private function renderTable() {
        $html = '';
        //Según el modo
        switch ($this->mode) {
            case self::MODE_NORMAL:
                $html .= "<thead>";
                $html .= "<tr>";
                $html .= "<th width='5%'>{$this->model->accountingMovement->getAttributeLabel('entries.entry_number')}</th>";
                $html .= "<th width='16%'>{$this->model->accountingMovement->getAttributeLabel('entries.account_code')}</th>";
                $html .= "<th width='11%'>{$this->model->accountingMovement->getAttributeLabel('entries.owner')}</th>";
                $html .= "<th width='16%'>{$this->model->accountingMovement->getAttributeLabel('entries.owner_name')}</th>";
                $html .= "<th width='16%'>{$this->model->accountingMovement->getAttributeLabel('entries.combination_id')}</th>";
                $html .= "<th width='16%'>{$this->model->accountingMovement->getAttributeLabel('entries.detail')}</th>";
                $html .= "<th width='10%' style='text-align:right;'>{$this->model->accountingMovement->getAttributeLabel('entries.amount')}</th>";
                $html .= "<th width='10%'>+/-</th>";
                $html .= "</tr>";
                $html .= "</thead>";
                $html .= "<tbody></tbody>";
                $html .= "<tfoot>";
                $html .= "<tr>";
                $html .= "<td colspan='5'>";
                //Si es automático NO se puede agregar
                if ($this->model->scenario->type !== Scenario::TYPE_AUTOMATIC && !$this->readonly_template && !$this->readonly) {
                    $html .= CHtml::link("<span class='fa fa-lg fa-plus black'></span> Agregar", Strings::LINK_TEXT, array(
                                'onclick' => "SIANEntryAddItem('{$this->id}', 0, '', '', '', 0, null, 1, '{$this->field}', null, '', '', $('#{$this->id}').data('exchange_rate'), $('#{$this->id}').data('expiration_date'), '', 1, '', 0, '', '', [], 0, 0, 0, [], null);",
                    ));
                }
                $html .= "</td>";
                $html .= "<td style='text-align:right;'><b>" . Entry::DEBIT_COLUMN . "</b></td>";
                $html .= "<td style='text-align:right;'><span class='sian-entry-total-debit'></span></td>";
                $html .= "<td></td>";
                $html .= "</tr>";
                $html .= "<tr>";
                $html .= "<td colspan='6' style='text-align:right;'><b>" . Entry::CREDIT_COLUMN . "</b></td>";
                $html .= "<td style='text-align:right;'><span class='sian-entry-total-credit'></span></td>";
                $html .= "<td></td>";
                $html .= "</tr>";
                $html .= "</tfoot>";
                break;
            case self::MODE_PROVISION:
                $html .= '<thead>';
                $html .= "<th width='33%'>{$this->model->accountingMovement->getAttributeLabel('entries.account_code')}</th>";
                $html .= "<th width='29%'>{$this->model->accountingMovement->getAttributeLabel('entries.owner_name')}</th>";
                $html .= "<th style='text-align:center' width='13%'>Debe <span class='currency-symbol'></span></th>";
                $html .= "<th style='text-align:center' width='13%'>Haber <span class='currency-symbol'></span></th>";
                $html .= "<th style='text-align:right' width='9%'>Porc.%</th>";
                $html .= "<th style='text-align:right' width='3%'></th>";
                $html .= "</thead>";
                $html .= "<tbody>";
                $html .= "</tbody>";
                $html .= "<tfoot>";
                $html .= "</tfoot>";
                break;
        }

        return $html;
    }

    private function getDp($owner) {

        switch ($owner) {
            case Account::OWNER:
                return 'DpAccount';
            case Organization::OWNER:
                return 'DpOrganization';
            case Person::OWNER:
                return 'DpBusinessPartner';
            case Warehouse::OWNER:
                return 'DpWarehouse';
            case FixedAsset::OWNER:
                return 'DpFixedAsset';
        }
    }

    private function getScenario($owner) {
        switch ($owner) {
            case Account::OWNER:
                return DpAccount::SCENARIO_SIAN_ENTRY;
            case Organization::OWNER:
                return '';
            case Person::OWNER:
                return DpBusinessPartner::SCENARIO_PEOPLE;
            case Warehouse::OWNER:
                return DpWarehouse::SCENARIO_WAREHOUSE_ID;
            case FixedAsset::OWNER:
                return DpFixedAsset::SCENARIO_ENTRY;
        }
    }

    /**
     * Obtiene los targets para las cuentas de los asientos
     * @param array $p_a_entries Asientos
     * @return array Array asociativo de las combinaciones para cada cuenta
     */
    private function _getTargetIds($p_a_entries) {
        $a_account_codes = [];
        foreach ($p_a_entries as $o_entry) {
            if (isset($o_entry->account_code) && $o_entry->has_combination == 1 && $o_entry->destiny_type === Account::DESTINY_TYPE_SELECTABLE) {
                $a_account_codes[] = $o_entry->account_code;
            }
        }

        //Si hay valores para buscar
        if (count($a_account_codes) > 0) {
            $a_items = (new DpAccount(DpAccount::SCENARIO_SIAN_ENTRY))->addInCondition('account_code', $a_account_codes)->findAll();

            $a_data = [];
            foreach ($a_items as $o_item) {
                $a_data[$o_item->account_code] = explode(',', $o_item->combination_ids);
            }

            return $a_data;
        } else {
            return [];
        }
    }

}
