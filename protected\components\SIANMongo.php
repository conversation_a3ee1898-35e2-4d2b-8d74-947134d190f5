<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANMongo
 *
 * <AUTHOR>
 */
class SIANMongo extends CApplicationComponent {

    private $client = null;
    private $db = null;

    public function connect() {

        $mongo_server = Yii::app()->params['mongo_server'];
        $mongo_port = Yii::app()->params['mongo_port'];
        $mongo_db = Yii::app()->params['mongo_db'];
        $mongo_username = Yii::app()->params['mongo_username'];
        $mongo_password = Yii::app()->params['mongo_password'];

        //Obtenemos cliente
        $this->client = USMongo::connect($mongo_server, $mongo_port, $mongo_db, $mongo_username, $mongo_password);

        //Seleccionamos DB
        $this->db = $this->client->{$mongo_db};
    }

    public function insertOne($p_s_collection, $p_a_data) {

        if (!isset($this->db)) {
            $this->connect();
        }

        return $this->db->{$p_s_collection}->insertOne($p_a_data);
    }

    public function count($p_s_collection, $p_a_filter = [], $p_a_options = []) {
        if (!isset($this->db)) {
            $this->connect();
        }

        return $this->db->{$p_s_collection}->count($p_a_filter, $p_a_options);
    }

    public function find($p_s_collection, $p_a_filter = [], $p_a_options = []) {
        if (!isset($this->db)) {
            $this->connect();
        }

        return $this->db->{$p_s_collection}->find($p_a_filter, $p_a_options);
    }

    public function findOne($p_s_collection, $p_a_filter = [], $p_a_options = []) {
        if (!isset($this->db)) {
            $this->connect();
        }

        return $this->db->{$p_s_collection}->findOne($p_a_filter, $p_a_options);
    }

    public function aggregate($p_s_collection, $p_a_params) {
        if (!isset($this->db)) {
            $this->connect();
        }

        return $this->db->{$p_s_collection}->aggregate($p_a_params);
    }

    public function deleteOne($p_s_collection, $p_a_filter = [], $p_a_options = []) {
        if (!isset($this->db)) {
            $this->connect();
        }

        return $this->db->{$p_s_collection}->deleteOne($p_a_filter, $p_a_options);
    }

}
