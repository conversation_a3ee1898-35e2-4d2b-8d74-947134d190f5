<?php

class USHiddenField extends CWidget {

    public $id;
    public $name;
    public $value;
    public $model;
    public $attribute;

    public function init() {
        $this->id = isset($this->id) ? $this->id : Yii::app()->controller->getServerId();

        if ($this->model) {
            $this->name = get_class($this->model) . '[' . $this->attribute . ']';
            $this->value = $this->model->{$this->attribute};
        }
    }

    /**
     * Runs the widget.
     */
    public function run() {
        echo CHtml::hiddenField($this->name, $this->value, array(
            'id' => $this->id
        ));
    }

}
