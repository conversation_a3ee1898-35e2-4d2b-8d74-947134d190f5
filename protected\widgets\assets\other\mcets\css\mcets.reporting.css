/* 
    Document   : jquery.ibusplus.reporting
    Created on : 04/01/2011, 12:26:15 AM
    Author     : <PERSON>:
*/
.iReportPlus-container{      
    position: relative;    
    margin-bottom: 10px;
    min-height: 83px;
}
.iReportPlus-bar{
    /*    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='gray', endColorstr='#F0F0F0');  for IE 
        background: -webkit-gradient(linear, left top, left bottom, from(gray), to(#F0F0F0));  for webkit browsers 
        background: -moz-linear-gradient(top,  gray,  #F0F0F0);  for firefox 3.6+ */
    border: gray thin solid;
    background-color: white;
}
.iReportPlus-bar > a:hover{
    box-shadow: 1px 1px 11px #888;
    -moz-box-shadow: 1px 1px 11px #888;
    -webkit-box-shadow: 1px 1px 11px #888;
}
.iReportPlus-bar-left{
    float: left !important;
}
.iReportPlus-bar-right{
    float: right !important;
}
.iReportPlus-hide{
    display: none !important; 
}
.iReportPlus-radius-border-all{
    border-radius: 4px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
}

.iReportPlus-iFrame{
    width: 90%;
    display: block;
    margin: 0 auto;
    height: 550px;
    -moz-box-shadow: 10px 10px 5px #888;
    -webkit-box-shadow: 10px 10px 5px #888;
    background-color: white;
}

.iReportPlus-iFrame-style{
    border: 1px solid gray;
    box-shadow: 10px 10px 5px #888;
}

.iReportPlus-horizontal-bar{
      background: none;
  background-color: rgb(0, 5, 29) !important;
}

.iReportPlus-vertical-bar{
    width: 60px; margin: 5px auto; max-height: 250px; float: left; position: relative;
    /*padding: 5px; */
}

.iReportPlus-export-option-horizontal{
    display: inline-block; min-width: 48px; min-height: 48px; margin: 2px; background-repeat: no-repeat;
}

.iReportPlus-export-option-vertical{
    display: block; min-width: 48px; min-height: 48px; margin: 2px; background-repeat: no-repeat;
}


.iReportPlus-export-option-link{
    cursor:pointer;
    background-repeat: no-repeat;
}


.iReportPlus-export-waiting{
    border-radius: 0;
    display: none;
    padding-top: 40px;
    width: 100%;
    height: 100%;
    margin: 0 auto;
    line-height: 52px;
    position: absolute;
    background-color: rgba(13, 13, 13, 0.81);
    top: 0;
    text-align: center;
}

.iReportPlus-export-waiting .iReportFlatLoader{
    font-size: 12px;
    vertical-align: top;
}
.iReportPlus-export-waiting .iReportFlatLoader-txt{
    display: inline-block;
    vertical-align: top;
    margin: 0;
    margin-left: 5px;
    margin-top: -7px;
    font-weight: 300;
    color: rgb(187, 186, 186);
}
.iReportPlus-export-waiting-only-img{
    color: transparent;
}

.iReportPlus-export-option-link-default-docx{background-image: url('../../../img/docx.png');}
.iReportPlus-export-option-link-default-html{background-image: url('../../../img/html.png');}
.iReportPlus-export-option-link-default-pdf{background-image: url('../../../img/pdf.png');}
.iReportPlus-export-option-link-default-txt{background-image: url('../../../img/txt.png');}
.iReportPlus-export-option-link-default-xls,.iReportPlus-export-option-link-default-xlsx{background-image: url('../../../img/xls.png');}

.iReportPlus-export-option-link-3d-docx{background-image: url('themes/3d/docx.png');}
.iReportPlus-export-option-link-3d-html{background-image: url('themes/3d/html.png');}
.iReportPlus-export-option-link-3d-pdf{background-image: url('themes/3d/pdf.png');}
.iReportPlus-export-option-link-3d-txt{background-image: url('themes/3d/txt.png');}
.iReportPlus-export-option-link-3d-xls{background-image: url('themes/3d/xls.png');}

.iReportPlus-export-option-link-folder-docx{background-image: url('themes/folder/docx.png');}
.iReportPlus-export-option-link-folder-html{background-image: url('themes/folder/html.png');}
.iReportPlus-export-option-link-folder-pdf{background-image: url('themes/folder/pdf.png');}
.iReportPlus-export-option-link-folder-txt{background-image: url('themes/folder/txt.png');}
.iReportPlus-export-option-link-folder-xls{background-image: url('themes/folder/xls.png');}

.iReportPlus-dialog-overlay{
    background-color: black;
    opacity: 0.4;
    position: fixed;
    top:0;
    left: 0;
    width: 100%;
    height: 102%;
    z-index: 2000;
}
.iReportPlus-dialog{      
   margin: 0 auto;
  background: none;
  background-color: rgba(251, 251, 251, 1);
  padding: 8px;
  text-align: center;
  border-radius: 0;
  border: none;
  min-height: 450px;
  padding-top: 30px;
  border-bottom: 1px solid rgb(233, 233, 233);
}

#rjDialog > div{
    color: #7B7B7B;
    font-size: 13px;
    display: inline-block;
    vertical-align: top;
}
.iReportPlus-dialog > div.msg{
    position:absolute;color:gray;left:95px;top:27px;
}
.iReportPlus-dialog > div > div.blink-msg{
    position:relative;color:black;left:0px;top:-22px;text-decoration: blink;
}

.iReportPlus-json-report-dialog{
    -webkit-transition: background-color 0.4s;
    -moz-transition: background-color 0.4s;
    -o-transition: background-color 0.4s;
    transition: background-color 0.4s;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -ms-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 0 1px #fff,inset 0 2px #fff,0 0 0 1px rgba(0,0,0,0.1),0 1px rgba(0,0,0,0.2);
    -moz-box-shadow: inset 0 0 0 1px #fff,inset 0 2px #fff,0 0 0 1px rgba(0,0,0,0.1),0 1px rgba(0,0,0,0.2);
    box-shadow: inset 0 0 0 1px #fff,inset 0 2px #fff,0 0 0 1px rgba(0,0,0,0.1),0 1px rgba(0,0,0,0.2);
    border: 0px;
    background-color: rgba(255, 255, 255, 0.99);
    position: fixed;
    margin-bottom: 1em;
    font-size: 13px;
    color: #666666;
    font-weight: 500;
    padding: 10px 10px;
    right: 13px;
    bottom: 7px;
    width: 300px;
      z-index: 1000000;
}


/**/
.iReportFlatIframe{
     border: none;
  border-radius: 0;
  width: 100%;
  box-shadow: none;
  border: 1px solid rgb(233, 233, 233);
  border-top: 0;
  border-left: 0;
}

.group-ul-flat{
    position: absolute
}
.iReportFlat{
    border: none;
}
.iReportFlat  ul{
    padding: 0;
    list-style: none;
    margin: 0;
    margin-bottom: -5px;
    border-radius: 0 !important;
}
.iReportFlat  li.has-sub{
    display: inline-block;
    font-size: 13px;
}
.iReportFlat .o-export a{
    height: 69px;
    background-size: 46px;
    background-position: center;
    min-width: 64px !important;
}
.iReportFlat .o-export ul{
    display: inline-block
}

.iReportFlat  li.has-sub > a > i{
    width: 19px;
    font-size: 18px;
    margin-right: 5px;
    position: relative;
    top: 2px;
}
.iReportFlat  li.has-sub a {
    color: rgb(228, 228, 228);
  font-weight: 500;
  padding: 5px 13px;
  display: inline-block;
  text-decoration: none;
  cursor: pointer;
  min-width: 97px;
  margin: 0;
  background-color: rgb(0, 5, 29);
  border-radius: 0 !important;
  min-height: 23px;
}

.iReportFlat li.has-sub a:hover {
     background-color: rgba(10, 26, 52, 0.99);
  color: white;
  font-weight: bold;
}
.iReportFlat  li.has-sub > .group-ul-flat{
    display: none;    
}
.iReportFlat  li.has-sub:hover >.group-ul-flat{
    display: block
}
.iReportFlatLoader-txt{
    margin-left: 5px;
    margin-top: 10px;
    font-size: 13px;
}
.iReportFlatLoader {
    border-left: 0.35em solid rgba(43, 77, 111, 1);
    border-right: 0.35em solid rgba(132, 130, 130, 1);
    border-bottom: 0.35em solid rgba(132, 130, 130, 1);
    border-top: 0.35em solid rgba(132, 130, 130, 1);
    display: inline-block;
    font-size: 3px;
    position: relative;
    text-indent: -9999em;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation: load8 1.1s infinite linear;
    animation: load8 1.1s infinite linear;
}
.iReportFlatLoader,
.iReportFlatLoader:after {
    border-radius: 50%;
    width: 3em;
    height: 3em;
}
@-webkit-keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

/*Aqui se agrego el loading de cargando*/

.iReportPlus-json-report-dialog > div > ul{
    padding: 0
}

.iReportPlus-json-report-dialog-border{border: #888 solid medium;}
.iReportPlus-json-report-dialog li{   list-style-image: url('themes/download.png');
                                      margin-left: 35px;  word-break: break-all;}
.iReportPlus-json-report-dialog > div{ margin: 10px; position: relative;}
.iReportPlus-json-report-dialog > div > .iReportPlus-json-report-dialog-close{ position: absolute; right: 0; cursor: pointer; width: 10px; text-align: center;}
.iReportPlus-json-report-dialog > div > .ui-button{ position: absolute !important; right: 0 !important; top:-5px !important; background-image: none;}
.iReportPlus-json-report-dialog > div > div:hover{ font-weight: bold;}
.iReportPlus-json-report-dialog a{  margin-right: 37px;
                                    vertical-align: top;
                                    display: inline-block;
                                    margin-top: 3px;}
.iReportPlus-json-report-dialog a:hover{text-decoration: underline;}
.iReportPlus-json-report-success > a{color: green !important;}
.iReportPlus-json-report-error >a{  color: rgb(240, 77, 77) !important;}