<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'stagingpuntov.siansystem.com/admin';
$domain = 'https://stagingpuntov.siansystem.com';
$report_domain = 'rstaging.siansystem.com';
$org = 'puntov';
$us = 'us_staging';

//SIAN 2
$api_sian = 'http://api-staging.siansystem.com/';


//database enterprise
$database_server = '69.10.37.246';
$database_name = 'puntov_release';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$database_sian = 'sian_staging';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'puntov.p12';
$e_billing_certificate_pass = 'TRESTABA2024';
$e_billing_ri = '0620050000126';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20607109169BILLING1',
        'password' => 'Puntovi1234'
    ]
];
//
$e_billing_send_automatically_to_client = false;
//
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_STAGING;
$environment = YII_ENVIRONMENT_STAGING;
