function SIANAddressListVisible(div_id, visible)
{
    var divObj = $('#' + div_id);
    //Seteamos
    divObj.data('visible', visible);
    //Obtnemos
    if (visible)
    {
        divObj.show();
    } else
    {
        divObj.hide();
    }
}

function SIANAddressListDisabled(div_id, disabled)
{
    var divObj = $('#' + div_id);
    //Seteamos
    divObj.data('disabled', disabled);
    //Campos
    var address_attributes = divObj.data('address_attributes');
    $.each(address_attributes, function (index, attribute) {
        $('#' + div_id + '_' + attribute).prop('disabled', disabled);
    });
    //Cambiamos
    if (disabled)
    {
        SIANAddressListClean(div_id);
    } else
    {
        var selected_data = SIANAddressListGetSelected(div_id);
        SIANAddressListGetItems(div_id, selected_data);
    }
}

function SIANAddressListGetItems(div_id, selected_data)
{
    var divObj = $('#' + div_id);
    //
    var has_prerequisites = SIANAddressListCheckPrerequisites(div_id);
    //
    if (has_prerequisites)
    {
        var type = divObj.data('type');
        var attribute_id = divObj.data('attribute_id');
        var items_url = divObj.data('items_url');
        var readonly = divObj.data('readonly');

        $.ajax({
            type: 'post',
            url: items_url,
            data: {
                type: type,
                prerequisites: SIANAddressListGetPrerequisites(div_id)
            },
            beforeSend: function (xhr) {
                window.active_ajax++;
                //Ocultamos los tooltip
                $('div.ui-tooltip').remove();
            },
            success: function (data) {

                if (data.code == REST_CODE_SUCCESS)
                {
                    if (data.data.items.length > 0)
                    {
                        //
                        var found_id = false;

                        var subDivObj = $('<div/>');
                        $.each(data.data.items, function (index, itemObj) {

                            var input_id = getLocalId();
                            if (isBlank(selected_data.attribute_id))
                            {
                                var is_same = SIANAddressListIsSame(div_id, selected_data.address_data, itemObj);
                            } else
                            {
                                var is_same = selected_data.attribute_id == itemObj[attribute_id];
                            }

                            if (is_same)
                            {
                                found_id = input_id;
                            }
                            //
                            var title = '';
                            switch (type)
                            {
                                case OWNER_LOCKER:
                                    title = '<b>' + itemObj.locker_name + '</b> - ' + itemObj.address + ' (' + itemObj.dept_name + ' - ' + itemObj.prov_name + ' - ' + itemObj.dist_name + ')';
                                    break;
                                case OWNER_PERSON:
                                    title = '<b>' + itemObj.address + '</b> (' + itemObj.dept_name + ' - ' + itemObj.prov_name + ' - ' + itemObj.dist_name + ')';
                                    break;
                            }

                            var radioObj = $('<div/>').addClass('radio');
                            var labelObj = $('<label/>').addClass('sian-address-list-item-label');
                            var inputObj = $('<input/>').attr({id: input_id, type: 'radio', name: 'SelectedAddressList', readonly: readonly}).addClass('sian-address-list-item-address-id').val(itemObj[attribute_id]).prop('checked', is_same).data('address_data', itemObj);
                            var spanObj = $('<span/>').html(title);
                            //Agregamos input y span a label
                            labelObj.append(inputObj).append(spanObj);
                            //Agregamos label a radio
                            radioObj.append(labelObj);
                            //Agregamos radio a div
                            subDivObj.append(radioObj);
                        });
                        //Seteamos
                        divObj.find('div.sian-address-list-items').html(subDivObj);
                        //Generamos evento
                        if (found_id === false) {
                            $('input.sian-address-list-item-address-id:first').prop('checked', true).change();
                        } else {
                            $('#' + found_id).change();
                        }
                    } else
                    {
                        SIANAddressListClean(div_id);
                    }

                } else
                {
                    bootbox.alert(us_message(data.message, 'warning'));
                }
                window.active_ajax--;
            },
            error: function (request, status, error) { // if error occured
                window.active_ajax--;
                bootbox.alert(us_message(request.responseText, 'error'));
            },
            dataType: 'json'
        });
    }
}

function SIANAddressListClean(div_id)
{
    var divObj = $('#' + div_id);
    divObj.find('div.sian-address-list-items').html('<div class=\'text-center\'>' + STRINGS_NO_DATA + '</div>');
    //Limipiamos campos
    var address_attributes = divObj.data('address_attributes');
    $.each(address_attributes, function (index, attribute) {
        $('#' + div_id + '_' + attribute).val(null);
    });
}

function SIANAddressListGetSelected(div_id)
{
    var divObj = $('#' + div_id);
    //
    var attribute_input_id = divObj.data('attribute_input_id');
    var attribute_id = $('#' + attribute_input_id).val();
    //
    var address_attributes = divObj.data('address_attributes');

    var address_data = {};
    $.each(address_attributes, function (index, attribute) {
        address_data[attribute] = $('#' + div_id + '_' + attribute).val();
    });

    return {
        attribute_id: attribute_id,
        address_data: address_data
    };
}

function SIANAddressListCheckPrerequisites(div_id)
{
    var divObj = $('#' + div_id);
    //
    var prerequisites_attributes = divObj.data('prerequisites_attributes');
    //Seteamos prerequisitos
    var is_set = true;
    $.each(prerequisites_attributes, function (index, attribute) {
        is_set = is_set && isset(divObj.data(attribute));
    });
    //
    return is_set;
}

function SIANAddressListIsSame(div_id, address_data1, address_data2)
{
    var divObj = $('#' + div_id);
    //
    var address_key = divObj.data('address_key');

    var is_same = true;
    $.each(address_key, function (index, attribute) {
        //Basta que uno sea diferente para que todo sea diferente
        is_same = is_same && (address_data1[attribute] == address_data2[attribute]);
    });

    return is_same;
}

function SIANAddressListGetPrerequisites(div_id)
{
    var divObj = $('#' + div_id);
    //
    var prerequisites_attributes = divObj.data('prerequisites_attributes');
    //Seteamos prerequisitos
    var prerequisites = {};
    $.each(prerequisites_attributes, function (index, attribute) {
        if (isset(divObj.data(attribute)))
        {
            prerequisites[attribute] = divObj.data(attribute);
        }
    });

    return prerequisites;
}

function SIANAddressListSetPrerequisites(div_id, prerequisites)
{
    var divObj = $('#' + div_id);
    //
    var prerequisites_attributes = divObj.data('prerequisites_attributes');
    var disabled = divObj.data('disabled');
    //Seteamos prerequisitos
    $.each(prerequisites_attributes, function (index, attribute) {
        if (isset(prerequisites[attribute]))
        {
            divObj.data(attribute, prerequisites[attribute]);
        }
    });
    //Si está activado
    if (disabled == 0)
    {
        var selected_data = SIANAddressListGetSelected(div_id);
        SIANAddressListGetItems(div_id, selected_data);
    }
}

function SIANAddressListCleanPrerequisites(div_id)
{
    var divObj = $('#' + div_id);
    //
    var prerequisites_attributes = divObj.data('prerequisites_attributes');
    //Seteamos prerequisitos
    $.each(prerequisites_attributes, function (index, attribute) {
        divObj.data(attribute, null);
    });

    SIANAddressListClean(div_id);
}
