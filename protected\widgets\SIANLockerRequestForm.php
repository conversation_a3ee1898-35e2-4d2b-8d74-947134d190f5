<?php

class SIANLockerRequestForm extends CWidget {

    //CONST
    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $person_client_autocomplete_id;
    public $person_deliverer_autocomplete_id;
    public $max_accesses = 3;
    //PRIVATE
    private $locker_input_id;
    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->person_client_autocomplete_id = isset($this->person_client_autocomplete_id) ? $this->person_client_autocomplete_id : $this->controller->getServerId();
        $this->person_deliverer_autocomplete_id = isset($this->person_deliverer_autocomplete_id) ? $this->person_deliverer_autocomplete_id : $this->controller->getServerId();
        //
        $this->locker_input_id = $this->controller->getServerId();
        //
        SIANAssets::registerScriptFile('other/jquery.autocomplete/js/jquery.autocomplete.js');
        SIANAssets::registerScriptFile('js/sian-locker-request-form.js');
        SIANAssets::registerCssFile('other/jquery.autocomplete/css/jquery.autocomplete.css');
        //
        $a_accesses = [];
        foreach ($this->model->tempAccesses as $o_access) {

            if (!in_array($o_access->type, [LockerAccess::TYPE_CLIENT, LockerAccess::TYPE_DELIVERER], true)) {
                throw new Exception("Tipo de acceso no válido: {$o_access->type}");
            }

            $a_accesses[$o_access->type][] = [
                'firstname' => isset($o_access->firstname) ? $o_access->firstname : '',
                'lastname' => isset($o_access->lastname) ? $o_access->lastname : '',
                'dni' => isset($o_access->dni) ? $o_access->dni : '',
                'email_address' => isset($o_access->email_address) ? $o_access->email_address : '',
                'errors' => $o_access->getAllErrors()
            ];
        }

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        //Evento load porque se ejecuta cuando ya todo cargó
        $(document).ready(function() {
            var divObj = $('#{$this->id}');
            divObj.data('person_client_autocomplete_id', '{$this->person_client_autocomplete_id}');
            divObj.data('person_deliverer_autocomplete_id', '{$this->person_deliverer_autocomplete_id}');
            divObj.data('contact_url', '{$this->controller->createUrl('/widget/getContactForLockerRequest')}');
            divObj.data('max_accesses', {$this->max_accesses});
                
            //Llenamos
            SIANLockerRequestFormInit('{$this->id}', " . CJSON::encode($a_accesses) . ");
        });
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}'>";

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Datos principales',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id . '_basic'
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-10 col-md-10 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model,
            'attribute' => 'locker_id',
            'view' => array(
                'model' => 'DpLocker',
                'scenario' => DpLocker::SCENARIO_NORMAL,
                'attributes' => array(
                    array('name' => 'locker_id', 'width' => 5, 'types' => array('id', 'value')),
                    array('name' => 'locker_code', 'width' => 10, 'types' => array('aux')),
                    array('name' => 'locker_name', 'width' => 40, 'types' => array('text')),
                    array('name' => 'address', 'width' => 40),
                    array('name' => 'box_count', 'width' => 5),
                )
            ),
            'parent_id' => $this->modal_id,
            'maintenance' => array(
                'module' => 'systems',
                'controller' => 'locker',
            ),
        ));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'status',
                ), true);
        echo "</div>";
        //
        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->person_client_autocomplete_id,
            'model' => $this->model,
            'attribute' => 'person_client_id',
            'view' => array(
                'model' => 'DpBusinessPartner',
                'scenario' => DpBusinessPartner::SCENARIO_CLIENT,
                'attributes' => array(
                    array('name' => 'person_id', 'width' => 0, 'types' => array('id', 'value'), 'not_in' => '[1]', 'hidden' => true),
                    array('name' => 'identification_type', 'width' => 5, 'search' => false),
                    array('name' => 'identification_number', 'width' => 15, 'types' => array('aux')),
                    array('name' => 'person_name', 'width' => 40, 'types' => array('text')),
                    array('name' => 'official_address', 'width' => 40, 'search' => false),
                )
            ),
            'parent_id' => $this->modal_id,
            'maintenance' => array(
                'module' => 'administration',
                'controller' => 'person',
            ),
        ));
        echo "</div>";

        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->person_deliverer_autocomplete_id,
            'model' => $this->model,
            'attribute' => 'person_deliverer_id',
            'view' => array(
                'model' => 'DpBusinessPartner',
                'scenario' => DpBusinessPartner::SCENARIO_CLIENT,
                'attributes' => array(
                    array('name' => 'person_id', 'width' => 0, 'types' => array('id', 'value'), 'not_in' => '[1]', 'hidden' => true),
                    array('name' => 'identification_type', 'width' => 5, 'search' => false),
                    array('name' => 'identification_number', 'width' => 15, 'types' => array('aux')),
                    array('name' => 'person_name', 'width' => 40, 'types' => array('text')),
                    array('name' => 'official_address', 'width' => 40, 'search' => false),
                )
            ),
            'parent_id' => $this->modal_id,
            'maintenance' => array(
                'module' => 'administration',
                'controller' => 'person',
            ),
        ));
        echo "</div>";
        echo "</div>";

        $this->endWidget();

        echo "<div class='row sian-locker-request-accesses'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Especificar personas que recogerán el producto',
            'headerIcon' => 'user',
            'htmlOptions' => array(
                'id' => $this->id . '_client_panel'
            )
        ));
        echo $this->_renderAccessTable(LockerAccess::TYPE_CLIENT);
        $this->endWidget();
        echo "</div>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Especificar personas que dejarán el producto',
            'headerIcon' => 'user',
            'htmlOptions' => array(
                'id' => $this->id . '_deliverer_panel'
            )
        ));
        echo $this->_renderAccessTable(LockerAccess::TYPE_DELIVERER);
        $this->endWidget();
        echo "</div>";
        echo "</div>";
        //
        echo "</div>";
    }

    private function _renderAccessTable($p_s_type) {
        $s_html = "<table class='{$p_s_type}_table items table table-hover table-condensed'>";
        $s_html .= "<thead>";
        $s_html .= "<tr>";
        $s_html .= "<th width'20%'><b>{$this->model->getAttributeLabel('lockerAccesses.firstname')}</b></th>";
        $s_html .= "<th width='25%'><b>{$this->model->getAttributeLabel('lockerAccesses.lastname')}</b></th>";
        $s_html .= "<th width='15%'><b>{$this->model->getAttributeLabel('lockerAccesses.dni')}</b></th>";
        $s_html .= "<th width='35%'><b>{$this->model->getAttributeLabel('lockerAccesses.email_address')}</b></th>";
        $s_html .= "<th width='5%'></th>";
        $s_html .= "</tr>";
        $s_html .= "</thead>";
        $s_html .= "<tbody>";
        $s_html .= "</tbody>";
        $s_html .= "<tfoot>";
        $s_html .= "<tr>";
        $s_html .= "<td colpan=5>";
        $s_html .= "<a onclick='SIANLockerRequestFormAddAccessItem(\"{$this->id}\", \"\", \"\", \"\", \"{$p_s_type}\", \"\", [], true);' title='Agregar nuevo'><span class='fa fa-plus fa-lg black'></span> Agregar nuevo</a>";
        $s_html .= "</td>";
        $s_html .= "</tr>";
        $s_html .= "</tfoot>";
        $s_html .= "</table>";

        return $s_html;
    }

}
