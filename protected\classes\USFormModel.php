<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of USCriteria
 *
 * <AUTHOR>
 */
class USFormModel extends CFormModel {

    protected function beforeValidate() {

        foreach ($this->attributes as $key => $value) {

            if (USString::isBlank($value)) {
                $this->unsetAttributes(array($key));
            } else {
                $this->{$key} = trim($value);
            }
        }

        return parent::beforeValidate();
    }

}
