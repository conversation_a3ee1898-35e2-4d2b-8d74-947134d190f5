<?php

class SIANCombo extends CWidget {

    public $id;
    public $grid_id;
    public $grid_price_id;
    public $form;
    public $model;
    public $ifixed = Combo::IFIXED;
    public $tfixed = Combo::TFIXED;
    public $item_product_type_id;
    public $item_pres_quantity_id;
    public $item_equivalence_id;
    public $item_allow_decimals_id;
    public $print_dispatch_ticket_id;
    public $item_mprice_id;
    public $item_imprice_id;
    public $item_aprice_id;
    public $item_iaprice_id;
    public $item_wprice_id;
    public $item_iwprice_id;
    public $add_button_id;
    public $advanced_id;
    public $modal_id = null;
    public $measure_id = null;
    public $measure_items = [];
    public $custom_measure_items = [];
    public $currency_items = [];
    public $item_types = [];
    public $store_items = [];
    //PRIVATE
    private $controller;
    private $autocomplete_id;
    private $preview_access;
    private $presentation_mode;
    private $presentation_url;
    private $presentationItems = [];
    private $istep;
    private $tstep;
    private $currency_input_id;

    public function init() {
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->grid_id = isset($this->grid_id) ? $this->grid_id : $this->controller->getServerId();
        $this->grid_price_id = isset($this->grid_price_id) ? $this->grid_price_id : $this->controller->getServerId();
        $this->modal_id = isset($this->modal_id) ? $this->modal_id : $this->controller->getServerId();
        $this->autocomplete_id = $this->controller->getServerId();
        $this->item_product_type_id = $this->controller->getServerId();
        $this->item_pres_quantity_id = $this->controller->getServerId();
        $this->item_equivalence_id = $this->controller->getServerId();
        $this->item_allow_decimals_id = $this->controller->getServerId();
        $this->item_mprice_id = $this->controller->getServerId();
        $this->item_imprice_id = $this->controller->getServerId();
        $this->item_aprice_id = $this->controller->getServerId();
        $this->item_iaprice_id = $this->controller->getServerId();
        $this->item_wprice_id = $this->controller->getServerId();
        $this->item_iwprice_id = $this->controller->getServerId();
        $this->advanced_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();
        $this->currency_input_id = $this->controller->getServerId();
        $this->print_dispatch_ticket_id = $this->controller->getServerId();
        //ACCESS
        $this->preview_access = $this->controller->checkRoute('/logistic/product/preview');
        $this->presentation_mode = SpGetProductPresentations::MODE_COMBOBOX_WITH_PRICES;
        $this->presentation_url = $this->controller->createUrl("/movement/getPresentations");
        //VAR
        $this->istep = USMath::toStep($this->ifixed);
        $this->tstep = USMath::toStep($this->tfixed);

        //ITEMS
        $a_product_ids = [];
        foreach ($this->model->tempItems as $o_item) {
            $a_product_ids[] = $o_item->sub_product_id;
        }

        if (count($a_product_ids) > 0) {
            //Si hay productos...
            $this->presentationItems = SpGetProductPresentations::getAssociative($this->presentation_mode, $a_product_ids, $this->model->product->price_currency);
        }

        $items = [];

        foreach ($this->model->tempItems as $o_item) {
            $attributes = [];

            $attributes['sub_product_id'] = $o_item->sub_product_id;
            $attributes['product_name'] = $o_item->product_name;
            $attributes['product_type'] = $o_item->product_type;
            $attributes['pres_quantity'] = $o_item->pres_quantity;
            $attributes['sub_equivalence'] = $o_item->sub_equivalence;
            $attributes['allow_decimals'] = $o_item->allow_decimals;
            $attributes['mprice'] = $o_item->{"mprice_{$this->model->product->price_currency}"};
            $attributes['imprice'] = $o_item->{"imprice_{$this->model->product->price_currency}"};
            $attributes['aprice'] = $o_item->{"aprice_{$this->model->product->price_currency}"};
            $attributes['iaprice'] = $o_item->{"iaprice_{$this->model->product->price_currency}"};
            $attributes['wprice'] = $o_item->{"wprice_{$this->model->product->price_currency}"};
            $attributes['iwprice'] = $o_item->{"iwprice_{$this->model->product->price_currency}"};
            $attributes['presentationItems'] = isset($this->presentationItems[$o_item->sub_product_id]) ? $this->presentationItems[$o_item->sub_product_id] : [];

            $attributes['errors'] = [
                'sub_product_id' => $o_item->getError('sub_product_id'),
                'product_name' => $o_item->getError('product_name'),
                'product_type' => $o_item->getError('product_type'),
                'pres_quantity' => $o_item->getError('pres_quantity'),
                'sub_equivalence' => $o_item->getError('sub_equivalence'),
                'allow_decimals' => $o_item->getError('allow_decimals'),
                'mprice' => $o_item->getError("mprice_{$this->model->product->price_currency}"),
                'aprice' => $o_item->getError("aprice_{$this->model->product->price_currency}"),
                'wprice' => $o_item->getError("wprice_{$this->model->product->price_currency}"),
                'imprice' => $o_item->getError("imprice_{$this->model->product->price_currency}"),
                'iaprice' => $o_item->getError("iaprice_{$this->model->product->price_currency}"),
                'iwprice' => $o_item->getError("iwprice_{$this->model->product->price_currency}"),
            ];

            $items[] = $attributes;
        }

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-combo.js');

        Yii::app()->clientScript->registerScript($this->id, "
        
        var divObj = $('#{$this->id}');
        divObj.data('view-access', " . json_encode($this->preview_access) . ");//ACCESS
        divObj.data('view-url', '{$this->controller->createUrl('/logistic/product/preview')}');//URL
        divObj.data('presentation_mode', '{$this->presentation_mode}');
        divObj.data('presentation_url', '{$this->presentation_url}');
        divObj.data('ifixed', {$this->ifixed});
        divObj.data('tfixed', {$this->tfixed});
        divObj.data('istep', {$this->istep});
        divObj.data('tstep', {$this->tstep});                
        divObj.data('price_mode', {$this->model->product->price_mode});
        divObj.data('link_prices', {$this->model->link_prices});
        divObj.data('igv', {$this->controller->getOrganization()->globalVar->igv});
        divObj.data('count', 0);
        //IDS
        divObj.data('item_equivalence_id', '{$this->item_equivalence_id}');
        divObj.data('item_mprice_id', '{$this->item_mprice_id}');
        divObj.data('item_imprice_id', '{$this->item_imprice_id}');
        divObj.data('item_aprice_id', '{$this->item_aprice_id}');
        divObj.data('item_iaprice_id', '{$this->item_iaprice_id}');
        divObj.data('item_wprice_id', '{$this->item_wprice_id}');
        divObj.data('item_iwprice_id', '{$this->item_iwprice_id}');
        divObj.data('grid_id', '{$this->grid_id}');
        divObj.data('grid_price_id', '{$this->grid_price_id}');
        divObj.data('modal_id', '{$this->modal_id}');
        divObj.data('currency', '{$this->model->product->price_currency}');

        $(document).ready(function() { 
                
            //MOVIMIENTOS
            var array = " . CJSON::encode($items) . ";

            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {
                    SIANComboAddItem('{$this->id}', array[i]['sub_product_id'], array[i]['product_type'], array[i]['product_name'], array[i]['pres_quantity'], array[i]['presentationItems'], array[i]['sub_equivalence'], array[i]['allow_decimals'], array[i]['mprice'], array[i]['imprice'], array[i]['aprice'], array[i]['iaprice'], array[i]['wprice'], array[i]['iwprice'], array[i]['errors']);
                }
            }
                
            SIANComboGetIds('{$this->id}');
            SIANComboUpdateAmounts('{$this->id}', false);

            //Seteamos símbolo de moneda
            $('#{$this->id}').find('span.currency-symbol').text(USCurrencyGetSymbol('{$this->model->product->price_currency}'));
        });
            
        $('#{$this->id}').data('changeAdvance', function(advanced){
            //Llamamos a advanced de presentation
            SIANPresentationChangeAdvance('{$this->grid_id}', advanced);
        });
        
        $('#{$this->id}').data('changeCurrency', function(currency){
                        
            var table = $('#{$this->id}').find('table.sian-combo-items'); 
            var exchange_rate = " . Yii::app()->controller->getLastExchange() . ";
            var ifixed = {$this->ifixed};
            var tfixed = {$this->tfixed};
            $('#{$this->id}').data('currency', currency);
            $('#{$this->id}').find('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            USAutocompleteReset('{$this->autocomplete_id}');
                
            $.each(table.find('tr.sian-combo-item'), function( index, row ) {
            
                var rowObj = $(row);                
                rowObj.find('input.sian-presentation-item-currency').val(currency);
                
                if(currency == '" . Currency::USD . "'){  
                    
                    rowObj.find('input.sian-combo-item-mprice').toUsd(exchange_rate, ifixed);
                    rowObj.find('input.sian-combo-item-imprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-combo-item-aprice').toUsd(exchange_rate, ifixed);
                    rowObj.find('input.sian-combo-item-iaprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-combo-item-wprice').toUsd(exchange_rate, ifixed);
                    rowObj.find('input.sian-combo-item-iwprice').toUsd(exchange_rate, tfixed);                    
                }else{

                    rowObj.find('input.sian-combo-item-mprice').toPen(exchange_rate, ifixed);
                    rowObj.find('input.sian-combo-item-imprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-combo-item-aprice').toPen(exchange_rate, ifixed);
                    rowObj.find('input.sian-combo-item-iaprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-combo-item-wprice').toPen(exchange_rate, ifixed);
                    rowObj.find('input.sian-combo-item-iwprice').toPen(exchange_rate, tfixed);                    
                }
            });
            SIANComboUpdateAmounts('{$this->id}', true);
        });
        
        $('body').on('change', '#{$this->item_equivalence_id}', function(e) {
            var divObj = $('#{$this->id}');
            //
            SIANComboSetPrices('{$this->id}', $('#{$this->item_equivalence_id}'), $('#{$this->item_mprice_id}'), $('#{$this->item_imprice_id}'), $('#{$this->item_aprice_id}'), $('#{$this->item_iaprice_id}'), $('#{$this->item_wprice_id}'), $('#{$this->item_iwprice_id}'));
        });
       
        $('body').on('change', '#{$this->id} select.sian-combo-item-sub-equivalence', function(e) {

            var row = $(this).closest('tr');
            
            var divObj = $('#{$this->id}');
            //
            var equivalenceObj = row.find('select.sian-combo-item-sub-equivalence');
            var mPriceObj = row.find('input.sian-combo-item-mprice');
            var imPriceObj = row.find('input.sian-combo-item-imprice');
            var aPriceObj = row.find('input.sian-combo-item-aprice');
            var iaPriceObj = row.find('input.sian-combo-item-iaprice');
            var wPriceObj = row.find('input.sian-combo-item-wprice');
            var iwPriceObj = row.find('input.sian-combo-item-iwprice');
            //
            SIANComboSetPrices('{$this->id}', equivalenceObj, mPriceObj, imPriceObj, aPriceObj, iaPriceObj, wPriceObj, iwPriceObj);
            //Actualizar montos
            SIANComboUpdateAmounts('{$this->id}', true);
       });
       
        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var divObj = $('#{$this->id}');

            var product_id = USAutocompleteField('{$this->autocomplete_id}', 'value');
            var product_name = USAutocompleteField('{$this->autocomplete_id}', 'text');
            var product_type = $('#{$this->item_product_type_id}').val();
            var pres_quantity = $('#{$this->item_pres_quantity_id}').floatVal(2);
            var presentationItems = $('#{$this->item_equivalence_id}').data('presentationItems');
            var equivalence = $('#{$this->item_equivalence_id}').floatVal(2);
            var allow_decimals = $('#{$this->item_allow_decimals_id}').integer();
            var mprice = $('#{$this->item_mprice_id}').floatVal({$this->ifixed});
            var imprice = $('#{$this->item_imprice_id}').floatVal({$this->tfixed});
            var aprice = $('#{$this->item_aprice_id}').floatVal({$this->ifixed});
            var iaprice = $('#{$this->item_iaprice_id}').floatVal({$this->tfixed});
            var wprice = $('#{$this->item_wprice_id}').floatVal({$this->ifixed});
            var iwprice = $('#{$this->item_iwprice_id}').floatVal({$this->tfixed});

            if (product_id.length === 0)
            {
                USAutocompleteFocus('{$this->autocomplete_id}');
                return;
            }
            
            SIANComboAddItem('{$this->id}', product_id, product_type, product_name, pres_quantity, presentationItems, equivalence, allow_decimals, mprice, imprice, aprice, iaprice, wprice, iwprice, []);

            USAutocompleteReset('{$this->autocomplete_id}');
                
            //FOCUS
            USAutocompleteFocus('{$this->autocomplete_id}');
            SIANComboGetIds('{$this->id}');
            SIANComboUpdateAmounts('{$this->id}', true);
            unfocusable();
        });
        
        //Llamamos
        $('#{$this->id}').data('changeAdvance')({$this->model->product->advanced});
        SIANComboChangePriceMode('{$this->id}', {$this->model->product->price_mode});                
        SIANPresentationChangePriceMode('{$this->grid_id}', {$this->model->product->price_mode});                

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        echo "<div id='{$this->id}' class='sian-combo'>";

        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->renderGeneral();
        echo "</div>";
        echo "</div>";
        //
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->renderItems();
        echo "</div>";
        echo "</div>";
        //
        echo "<div class='row'>";
        echo "<div class='col-lg-8 col-md-12 col-sm-12 col-xs-12'>";
        $this->renderPresentation();
        echo "</div>";
        echo "<div class='col-lg-4 col-md-12 col-sm-12 col-xs-12'>";
        $this->renderPrice();
        echo "</div>";
        echo "</div>";
        //
        echo "</div>";
    }

    private function renderGeneral() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Datos generales',
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-8 col-md-8 col-sm-12 col-xs-12'>";
        echo $this->form->textFieldRow($this->model->product, 'product_name', array(
        ));
        echo $this->form->textAreaRow($this->model->product, 'description', array(
        ));
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->product, 'item_type_id', $this->item_types, array(
            'empty' => Strings::SELECT_OPTION,
            'hint' => 'No tiene mayor efecto, debido a que en almacén el combo se desagrupa'
        ));
        echo "</div>";
        echo "</div>";
        //
        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array('model' => $this->model->product, 'attribute' => 'status'), true);
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', [
            'model' => $this->model,
            'attribute' => 'link_prices',
            'switchChange' => "SIANComboChangeLinkPrices('{$this->id}', this.checked)",
            'hint' => 'Enlaza los precios de los productos, si alguno cambia, el precio del combo cambia'
                ], true);
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model->product,
            'attribute' => 'price_mode',
            'switchChange' => "
                //Llamamos a price mode de presentation
                SIANComboChangePriceMode('{$this->id}', (this.checked ? 1 : 0));
                SIANPresentationChangePriceMode('{$this->grid_id}', (this.checked ? 1 : 0));
            "
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'id' => $this->print_dispatch_ticket_id,
            'model' => $this->model->product,
            'attribute' => 'print_dispatch_ticket',
            'switchChange' => "",
            'hint' => 'Para despacho'
                ), true);
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->product, 'price_currency', $this->currency_items, [
            'id' => $this->currency_input_id,
            'onchange' => " var currency = $(this).val();                            
                            $('#{$this->id}').data('changeCurrency')(currency);",
            'class' => 'sian-combo-currency']);
        echo '</div>';
        echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
        echo SIANForm::textFieldNonActive('TC', 'exchangeRate', Yii::app()->controller->getLastExchange(), ['readonly' => 1]);
        echo '</div>';
        echo "</div>";

        $this->endWidget();
    }

    private function renderItems() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Ítems*',
            'headerIcon' => 'list',
            'htmlOptions' => array(
            )
        ));

        //BAR
        $this->renderBar();
        echo "<hr>";
        echo CHtml::tag('table', array(
            'class' => 'table table-condensed table-hover sian-combo-items ' . ($this->model->hasErrors('tempItems') ? 'us-error' : ''),
                ), $this->renderTable(), true);

        echo "<hr>";
        echo "<p>(*) " . Combo::ACCEPTANCE_MESSAGE . "</p>";

        $this->endWidget();
    }

    private function renderBar() {
        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        echo $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->autocomplete_id,
            'label' => 'Mercadería / Servicio',
            'name' => null,
            'view' => array(
                'model' => 'DpAllProductPresentations',
                'scenario' => DpAllProductPresentations::SCENARIO_WITHOUT_STOCK,
                'attributes' => array(
                    array('name' => 'product_id', 'width' => 10, 'types' => array('id', 'value', 'aux'), 'not_in' => "window.sianComboIds"),
                    array('name' => 'product_type', 'width' => 5, 'in' => "['" . Product::TYPE_MERCHANDISE . "', '" . Product::TYPE_SERVICE . "']"),
                    array('name' => 'barcode', 'width' => 20),
                    array('name' => 'product_name', 'width' => 20, 'types' => array('text')),
                    array('name' => 'model', 'hidden' => true),
                    array('name' => 'part_number', 'hidden' => true),
                    array('name' => 'perception_affected', 'hidden' => true, 'search' => false, 'in' => '[0]'),
                    array('name' => 'currency', 'hidden' => true, 'search' => false),
                    array('name' => 'currency_name', 'width' => 5, 'search' => false),
                    array('name' => 'equivalence', 'hidden' => true, 'search' => false),
                    array('name' => 'allow_decimals', 'hidden' => true, 'search' => false),
                    array('name' => 'measure_name', 'width' => 10, 'search' => false),
                    array('name' => 'mprice', 'hidden' => true),
                    array('name' => 'imprice', 'width' => 10),
                    array('name' => 'aprice', 'hidden' => true),
                    array('name' => 'iaprice', 'width' => 10),
                    array('name' => 'wprice', 'hidden' => true),
                    array('name' => 'iwprice', 'width' => 10),
                ),
                'params' => "{
                        currency: function () { 
                            return $('#{$this->currency_input_id}').val()
                        }
                    }",
            ),
            'maintenance' => array(
                'module' => 'logistic',
                'controller' => 'product',
                'buttons' => array(
                    'preview' => array(
                        'access' => $this->preview_access,
                    ),
                    'create' => [],
                    'update' => [],
                ),
            ),
            "onselect" => "
                    var divObj = $('#{$this->id}');
                    
                    var step = (allow_decimals == 0 ? 1 : 0.01);

                    $('#{$this->item_product_type_id}').val(product_type);
                    $('#{$this->item_allow_decimals_id}').val(allow_decimals);
                    $('#{$this->item_pres_quantity_id}').val(1).allowDecimals(allow_decimals * 2);
                    $('#{$this->item_pres_quantity_id}').attr('step', step).attr('min', step);

                    SIANComboGetPresentations('{$this->id}', product_id, equivalence);
                    ",
            'onreset' => "
                    $('#{$this->item_pres_quantity_id}').allowDecimals(0);    
                    $('#{$this->item_pres_quantity_id}').val(1);    
                    $('#{$this->item_pres_quantity_id}').attr('step', 1).attr('min', 1);    
                    $('#{$this->item_pres_quantity_id}').removeAttr('disabled');
                    $('#{$this->item_equivalence_id}').html('<option value>" . Strings::SELECT_OPTION . "</option>'); 
                    $('#{$this->item_mprice_id}').val(0);
                    $('#{$this->item_imprice_id}').val(0);
                    $('#{$this->item_aprice_id}').val(0);
                    $('#{$this->item_iaprice_id}').val(0).attr('min', 0);
                    $('#{$this->item_wprice_id}').val(0);
                    $('#{$this->item_iwprice_id}').val(0);
                "
                ), true);
        echo "</div>";
        echo "<div class='col-lg-1 col-md-1 col-sm-6 col-xs-6'>";
        echo SIANForm::dropDownListNonActive('Tipo', null, null, Product::getTypeItems(), array(
            'id' => $this->item_product_type_id,
            'disabled' => true
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-6'>";
        echo SIANForm::numberFieldNonActive('Cantidad', null, 1, array(
            'id' => $this->item_pres_quantity_id,
            'class' => 'enterTab',
            'style' => 'text-align:right'
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_allow_decimals_id,
        ));
        echo "</div>";

        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-6'>";
        $this->renderPrices();
        echo "</div>";

        echo "</div>";
    }

    private function renderPrices() {
        $mprice_label = 'P.Min.';
        $aprice_label = Yii::app()->controller->getOrganization()->globalVar->aprice_label;
        $wprice_label = Yii::app()->controller->getOrganization()->globalVar->wprice_label;

        echo "<div class=row'>";
        //
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo SIANForm::dropDownListNonActive('Pres.', null, null, [], array(
            'id' => $this->item_equivalence_id,
            'empty' => Strings::SELECT_OPTION,
        ));
        echo "</div>";
        //
        echo "<div class='col-lg-3 col-md-3 col-sm-12 col-xs-12 sian-combo-without-igv' " . ($this->model->product->price_mode == 0 ? '' : "style='display: none;'") . ">";
        echo SIANForm::numberFieldNonActive($mprice_label . " <span class = 'currency-symbol'></span>", null, 0, array(
            'id' => $this->item_mprice_id,
            'class' => "us-double{$this->ifixed} sian-combo-price-input enterTab",
            'min' => 0,
            'step' => $this->istep,
            'readonly' => ($this->model->link_prices == 1)
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-12 col-xs-12 sian-combo-with-igv' " . ($this->model->product->price_mode == 0 ? '' : "style='display: none;'") . ">";
        echo SIANForm::numberFieldNonActive($mprice_label . " <span class = 'currency-symbol'></span> (Inc. IGV)", null, 0, array(
            'id' => $this->item_imprice_id,
            'class' => "us-double{$this->tfixed} sian-combo-price-input enterTab",
            'min' => 0,
            'step' => $this->tstep,
            'readonly' => ($this->model->link_prices == 1)
        ));
        echo "</div>";
        //
        echo "<div class='col-lg-3 col-md-3 col-sm-12 col-xs-12 sian-combo-without-igv' " . ($this->model->product->price_mode == 0 ? '' : "style='display: none;'") . ">";
        echo SIANForm::numberFieldNonActive($aprice_label . " <span class = 'currency-symbol'></span>", null, 0, array(
            'id' => $this->item_aprice_id,
            'class' => "us-double{$this->ifixed} sian-combo-price-input enterTab",
            'min' => 0,
            'step' => $this->istep,
            'readonly' => ($this->model->link_prices == 1)
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-12 col-xs-12 sian-combo-with-igv' " . ($this->model->product->price_mode == 0 ? '' : "style='display: none;'") . ">";
        echo SIANForm::numberFieldNonActive($aprice_label . " <span class = 'currency-symbol'></span> (Inc. IGV)", null, 0, array(
            'id' => $this->item_iaprice_id,
            'class' => "us-double{$this->tfixed} sian-combo-price-input enterTab",
            'min' => 0,
            'step' => $this->tstep,
            'readonly' => ($this->model->link_prices == 1)
        ));
        echo "</div>";
        //
        echo "<div class='col-lg-3 col-md-3 col-sm-12 col-xs-12 sian-combo-without-igv' " . ($this->model->product->price_mode == 0 ? '' : "style='display: none;'") . ">";
        echo SIANForm::numberFieldNonActive($wprice_label . " <span class = 'currency-symbol'></span>", null, 0, array(
            'id' => $this->item_wprice_id,
            'class' => "us-double{$this->ifixed} sian-combo-price-input enterTab",
            'min' => 0,
            'step' => $this->istep,
            'readonly' => ($this->model->link_prices == 1)
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-12 col-xs-12 sian-combo-with-igv' " . ($this->model->product->price_mode == 0 ? '' : "style='display: none;'") . ">";
        echo SIANForm::numberFieldNonActive($wprice_label . " <span class = 'currency-symbol'></span> (Inc. IGV)", null, 0, array(
            'id' => $this->item_iwprice_id,
            'class' => "us-double{$this->tfixed} sian-combo-price-input enterTab",
            'min' => 0,
            'step' => $this->tstep,
            'readonly' => ($this->model->link_prices == 1)
        ));
        echo "</div>";
        //
        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-6 text-center'>";
        echo CHtml::label('Agregar', $this->add_button_id, []);
        echo "<br/>";
        $this->widget('application.widgets.USButtons', array(
            'buttons' => array(
                array(
                    'id' => $this->add_button_id,
                    'context' => 'primary',
                    'icon' => 'fa fa-lg fa-plus white',
                    'size' => 'default',
                    'title' => 'Añadir'
                )
            )
        ));
        echo "</div>";
        //
        echo "</div>";
    }

    private function renderTable() {
        $mprice_label = 'P.Min.';
        $aprice_label = Yii::app()->controller->getOrganization()->globalVar->aprice_label;
        $wprice_label = Yii::app()->controller->getOrganization()->globalVar->wprice_label;

        $html = '';
        $html .= '<thead>';
        $html .= "<th width='2%'>{$this->model->getAttributeLabel('items.item_number')}</th>";
        $html .= "<th width='4%'>{$this->model->getAttributeLabel('items.sub_product_id')}</th>";
        $html .= "<th width='35%'>{$this->model->product->getAttributeLabel('product_name')}</th>";
        $html .= "<th width='4%'>{$this->model->product->getAttributeLabel('product_type')}</th>";
        $html .= "<th width='10%'>{$this->model->getAttributeLabel('items.pres_quantity')}</th>";
        $html .= "<th width='10%'>{$this->model->getAttributeLabel('items.sub_equivalence')}</th>";
        $html .= "<th width='10%' class='sian-combo-without-igv' " . ($this->model->product->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">" . $mprice_label . " <span class = 'currency-symbol'></span></th>";
        $html .= "<th width='10%' class='sian-combo-with-igv' " . ($this->model->product->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">" . $mprice_label . " <span class = 'currency-symbol'></span> (Inc. IGV)</th>";
        $html .= "<th width='10%' class='sian-combo-without-igv' " . ($this->model->product->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">" . $aprice_label . " <span class = 'currency-symbol'></span></th>";
        $html .= "<th width='10%' class='sian-combo-with-igv' " . ($this->model->product->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">" . $aprice_label . " <span class = 'currency-symbol'></span> (Inc. IGV)</th>";
        $html .= "<th width='10%' class='sian-combo-without-igv' " . ($this->model->product->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">" . $wprice_label . " <span class = 'currency-symbol'></span></th>";
        $html .= "<th width='10%' class='sian-combo-with-igv' " . ($this->model->product->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">" . $wprice_label . " <span class = 'currency-symbol'></span> (Inc. IGV)</th>";
        $html .= "<th style='text-align:right' width='5%'></th>";
        $html .= "</thead>";
        $html .= "<tbody>";
        $html .= "</tbody>";
        $html .= "<tfoot>";
        $html .= "<tfoot>";

        return $html;
    }

    private function renderPresentation() {
        $this->widget('application.widgets.SIANPresentation', array(
            'id' => $this->grid_id,
            'form' => $this->form,
            'model' => $this->model->product,
            'measure_id' => $this->measure_id,
            'measure_items' => $this->measure_items,
            'lock_prices' => true,
            'lock_default' => true,
            'has_panel' => true
        ));
    }

    private function renderPrice() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Precios ' . '<span class="sian-combo-without-igv" style="display: none;"> (NO incluyen IGV)</span><span class="sian-combo-with-igv"> (Incluyen IGV)</span>',
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
            )
        ));

        $this->widget('application.widgets.SIANPresentationStore', array(
            'id' => $this->grid_price_id,
            'form' => $this->form,
            'model' => $this->model->product,
            'measure_id' => $this->measure_id,
            'measure_items' => $this->measure_items,
            'custom_measure_items' => $this->custom_measure_items,
            'cost' => 0,
            'store_items' => $this->store_items,
            'lock_add_and_remove' => true,
            'show_only_prices' => true,
            'has_panel' => false,
            'unique_price' => true,
            'readonly' => true,
        ));
        $this->endWidget();
    }

}
