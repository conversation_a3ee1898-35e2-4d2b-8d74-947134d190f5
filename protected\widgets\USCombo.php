<?php

class USCombo extends CWidget {

    public $id = null;
    public $form;
    public $model;
    public $attribute;
    public $maintenance;
    public $items;
    public $params = [];
    public $onchange = '';
    //
    private $controller;
    private $buttons = [];

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        if (isset($this->maintenance)) {

            if (!isset($this->maintenance['buttons'])) {
                $this->maintenance['buttons'] = array(
                    'create' => [],
                    'update' => [],
                    'delete' => [],
                    'preview' => []
                );
            }

            //PARA CADA BUTTON
            foreach ($this->maintenance['buttons'] as $action => $parameters) {
                //ID
                if (!isset($parameters['id'])) {
                    $this->maintenance['buttons'][$action]['id'] = $this->controller->getServerId();
                }
                //DATA
                if (!isset($parameters['data'])) {
                    $this->maintenance['buttons'][$action]['data'] = [];
                }

                //ACCESS
                if (!isset($parameters['access'])) {
                    $this->maintenance['buttons'][$action]['access'] = $this->controller->checkRoute("/{$this->maintenance['module']}/{$this->maintenance['controller']}/{$action}");
                }
            }
        }
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $links = [];

        if (isset($this->maintenance)) {
            if (array_key_exists('preview', $this->maintenance['buttons'])) {

                $links[] = $this->controller->widget('application.widgets.USLink', array(
                    'id' => $this->maintenance['buttons']['preview']['id'],
                    'icon' => 'fa fa-lg fa-eye',
                    'title' => 'Vista previa',
                    'class' => 'form',
                    'route' => "/{$this->maintenance['module']}/{$this->maintenance['controller']}/preview",
                    'data' => array_merge(array(
                        'id' => isset($this->model) ? $this->model->{$this->attribute} : null,
                        'modal_id' => $this->controller->getServerId(),
                        'parent_id' => $this->maintenance['parent_id']
                            ), $this->maintenance['buttons']['preview']['data']),
                    'visible' => isset($this->model->{$this->attribute}) ? $this->maintenance['buttons']['preview']['access'] : false,
                        ), true);
            }

            if (array_key_exists('create', $this->maintenance['buttons'])) {

                $links[] = $this->controller->widget('application.widgets.USLink', array(
                    'id' => $this->maintenance['buttons']['create']['id'],
                    'icon' => 'fa fa-lg fa-plus',
                    'title' => 'Vista previa',
                    'class' => 'form',
                    'route' => "/{$this->maintenance['module']}/{$this->maintenance['controller']}/create",
                    'data' => array_merge(array(
                        'modal_id' => $this->controller->getServerId(),
                        'parent_id' => $this->maintenance['parent_id'],
                        'type' => 'select',
                        'element_id' => $this->id
                            ), $this->maintenance['buttons']['create']['data']),
                    'visible' => $this->maintenance['buttons']['create']['access'],
                        ), true);
            }

            if (array_key_exists('update', $this->maintenance['buttons'])) {

                $links[] = $this->controller->widget('application.widgets.USLink', array(
                    'id' => $this->maintenance['buttons']['update']['id'],
                    'icon' => 'fa fa-lg fa-pencil',
                    'title' => 'Editar',
                    'class' => 'form',
                    'route' => "/{$this->maintenance['module']}/{$this->maintenance['controller']}/update",
                    'data' => array_merge(array(
                        'id' => isset($this->model) ? $this->model->{$this->attribute} : null,
                        'modal_id' => $this->controller->getServerId(),
                        'parent_id' => $this->maintenance['parent_id'],
                        'type' => 'select',
                        'element_id' => $this->id
                            ), $this->maintenance['buttons']['update']['data']),
                    'visible' => isset($this->model->{$this->attribute}) ? $this->maintenance['buttons']['update']['access'] : false
                        ), true);

                $this->buttons[] = array(
                    'type' => 'default',
                );
            }

            if (array_key_exists('delete', $this->maintenance['buttons'])) {

                $links[] = $this->controller->widget('application.widgets.USLink', array(
                    'id' => $this->maintenance['buttons']['delete']['id'],
                    'icon' => 'fa fa-lg fa-times',
                    'title' => 'Eliminar',
                    'class' => 'confirm',
                    'route' => "/{$this->maintenance['module']}/{$this->maintenance['controller']}/delete",
                    'data' => array_merge(array(
                        'id' => isset($this->model) ? $this->model->{$this->attribute} : null,
                        'modal_id' => $this->controller->getServerId(),
                        'parent_id' => $this->maintenance['parent_id'],
                        'type' => 'select',
                        'element_id' => $this->id
                            ), $this->maintenance['buttons']['delete']['data']),
                    'visible' => isset($this->model->{$this->attribute}) ? $this->maintenance['buttons']['delete']['access'] : false
                        ), true);
            }
        }


        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, $this->attribute, $this->items, array(
            'id' => $this->id,
            'append' => implode('&nbsp', $links),
            'empty' => Strings::SELECT_OPTION,
        ));
        echo "</div>";
        echo "</div>";

        if (isset($this->maintenance)) {
            Yii::app()->clientScript->registerScript($this->id . "_change", "
                $('body').on('change', '#$this->id', function(e) {
                    
                    var object = $(this);
                    
                    {$this->onchange}

                    " . (isset($this->maintenance) && array_key_exists('update', $this->maintenance['buttons']) && $this->maintenance['buttons']['update']['access'] ? "
                        USLinkStatus('{$this->maintenance['buttons']['update']['id']}', object.val().length > 0, {id: object.val()});
                    " : "")
                    . (isset($this->maintenance) && array_key_exists('delete', $this->maintenance ['buttons']) && $this->maintenance['buttons']['delete']['access'] ? "
                        USLinkStatus('{$this->maintenance['buttons']['delete']['id']}', object.val().length > 0, {id: object.val()});
                    " : "" )
                    . (isset($this->maintenance) && array_key_exists('preview', $this->maintenance['buttons']) && $this->maintenance['buttons']['preview']['access'] ? "
                        USLinkStatus('{$this->maintenance['buttons']['preview']['id']}', object.val().length > 0, {id: object.val()});
                    " : "") . "
                    
                });
            ");
        }
    }

}
