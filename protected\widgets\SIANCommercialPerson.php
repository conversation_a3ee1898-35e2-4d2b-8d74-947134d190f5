<?php

class SIANCommercialPerson extends CWidget {

    public $id;
    public $model;
    public $form;
    public $maintenance;
    public $person_class;
    public $condition_class;
    public $onselect = '';
    public $onreset = '';
    public $onUpdatePersonType = '';
    public $readonly = false;
    public $readonly_condition = true;
    public $condition = true;
    public $condition_items;
    public $useOnkeydown = false; // Detectar escritura en teclado?
    public $autoChoose = true; // Elegir automáticamente el valor del resultado de la busqueda 
    //IDS
    public $condition_input_id;
    public $credit_days_input_id;
    public $person_type_input_id;
    public $account_number_input_id;
    //private
    public $controller;
    public $person_credit_days_input_id;

    public function init() {
        $this->controller = Yii::app()->controller;
        // Detectar escritura en teclado
        $this->useOnkeydown = isset($this->useOnkeydown) ? $this->useOnkeydown : false;
        // Elegir automáticamente el valor del resultado de la busqueda
        $this->autoChoose = isset($this->autoChoose) ? $this->autoChoose : true;
        //SI está habilitada la condición
        if ($this->condition) {
            $this->condition_input_id = isset($this->condition_input_id) ? $this->condition_input_id : $this->controller->getServerId();
            $this->credit_days_input_id = isset($this->credit_days_input_id) ? $this->credit_days_input_id : $this->controller->getServerId();
            $this->person_credit_days_input_id = isset($this->person_credit_days_input_id) ? $this->person_credit_days_input_id : $this->controller->getServerId();
        }
        $this->person_type_input_id = isset($this->person_type_input_id) ? $this->person_type_input_id : $this->controller->getServerId();
        $this->account_number_input_id = isset($this->account_number_input_id) ? $this->account_number_input_id : $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-commercial-person.js');

        //SI está habilitada la condición
        if ($this->condition) {
            //SCRIPT
            Yii::app()->clientScript->registerScript($this->controller->getServerId(), "         
            $('body').on('change', '#{$this->condition_input_id}', function(e) {
                switch ($(this).val())
                {
                    case '" . Condition::CREDIT . "':
                        $('#{$this->credit_days_input_id}').readonly(false);
                        " . ($this->condition && in_array($this->model->movement->route, ['logistic/purchaseOrder', 'logistic/operatingCost', 'accounting/operatingCost']) ? "
                                let person_credit_days = $('#{$this->person_credit_days_input_id}').val();
                                if(person_credit_days > 0){
                                    $('#{$this->credit_days_input_id}').val(person_credit_days);
                                }
                                " : "" ) . "                        
                        break;
                     case '" . Condition::ON_ACCOUNT . "':
                        $('#{$this->credit_days_input_id}').readonly(false);
                        break;
                    default:
                        $('#{$this->credit_days_input_id}').val(0);
                        $('#{$this->credit_days_input_id}').readonly(true);
                        break;
                }
            });
            ", CClientScript::POS_END);
        }
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $a_auxAttributes = array(
            array('name' => 'identification_name', 'width' => 5, 'search' => false),
            array('name' => 'identification_number', 'width' => 15, 'types' => array('aux')),
            array('name' => 'person_id', 'width' => 0, 'types' => array('id', 'value'), 'hidden' => true),
            array('name' => 'person_name', 'width' => 40, 'types' => array('text')),
            array('name' => 'official_address', 'width' => 40, 'search' => false),
            array('name' => 'reference', 'hidden' => true, 'search' => false),
            array('name' => 'dept_code', 'hidden' => true, 'search' => false),
            array('name' => 'prov_code', 'hidden' => true, 'search' => false),
            array('name' => 'dist_code', 'hidden' => true, 'search' => false),
            array('name' => 'lat', 'hidden' => true, 'search' => false),
            array('name' => 'lng', 'hidden' => true, 'search' => false),
            array('name' => 'type', 'hidden' => true, 'search' => false),
            array('name' => 'expired', 'hidden' => true, 'search' => false),
            array('name' => 'person_type', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->person_type_input_id}').val(person_type);" . $this->onUpdatePersonType),
            array('name' => 'retention', 'hidden' => true, 'search' => false, 'update' => "SIANCommercialPersonProcess('{$this->model->movement->direction}', expired, retention)"),
            array('name' => 'account_number', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->account_number_input_id}').val(account_number);"),
        );

        if ($this->condition) {
            $a_auxAttributes[] = array('name' => 'credit_days', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->person_credit_days_input_id}').val(credit_days);");
        }

        //Alcance
        switch ($this->model->movement->scope) {
            case Scenario::SCOPE_NATIONAL:
                $a_auxAttributes[] = array('name' => 'not_domiciled', 'hidden' => true, 'in' => '[0]');
                $a_auxAttributes[] = array('name' => 'account_number', 'hidden' => true);
                break;
            case Scenario::SCOPE_IMPORT:
            case Scenario::SCOPE_EXPORT:
                $a_auxAttributes[] = array('name' => 'not_domiciled', 'hidden' => true, 'in' => '[0,1]');
                break;
            case Scenario::SCOPE_NOT_DOMICILED:
                $a_auxAttributes[] = array('name' => 'not_domiciled', 'hidden' => true, 'in' => '[1]');
                break;
            case Scenario::SCOPE_PURCHASE_LIQUIDATION:
                $a_auxAttributes[] = array('name' => 'not_domiciled', 'hidden' => true, 'in' => '[0]');
                $a_auxAttributes[] = array('name' => 'has_address', 'hidden' => true, 'in' => '[1]');
                $a_auxAttributes[] = array('name' => 'identification_type', 'hidden' => true, 'not_in' => "['" . USIdentificationValidator::RUC . "']");
                break;
        }
        //Sí está habilitada la condición
        if ($this->condition) {

            $a_auxAttributes[] = array('name' => 'credit', 'search' => false, 'hidden' => true);

            $this->onreset .= "
                        $('#{$this->condition_input_id}').val('" . Condition::COUNTED . "');
                        $('#{$this->credit_days_input_id}').val(0);
                        $('#{$this->credit_days_input_id}').readonly(true);";
        }

        echo "<div class='row'>";
        $span = $this->condition ? 8 : 12;
        echo "<div class='col-lg-{$span} col-md-{$span} col-sm-{$span} col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model->movement,
            'label' => (in_array($this->model->movement->route, ['logistic/purchaseOrder', 'logistic/purchaseBill']) ? 'Proveedor' : (in_array($this->model->movement->route, ['commercial/saleOrder', 'commercial/saleBill']) ? 'Cliente' : 'Socio de Negocio')),
            'attribute' => 'aux_person_id',
            'view' => array(
                'model' => 'DpBusinessPartner',
                'scenario' => $this->model->movement->scenario->direction == Scenario::DIRECTION_OUT ? 'client' : 'provider',
                'attributes' => $a_auxAttributes
            ),
            'maintenance' => $this->maintenance,
            'onselect' => $this->onselect,
            'onreset' => ( $this->condition ? "$('#{$this->person_credit_days_input_id}').val('');" : "" ) . "$('#{$this->account_number_input_id}').val('');" . $this->onreset,
            'value_class' => $this->person_class,
            'readonly' => $this->readonly,
            'useOnkeydown' => $this->useOnkeydown,
            'autoChoose' => $this->autoChoose,
        ));
        if ($this->condition) {
            if (isset($this->model->movement->auxPerson)) {
                echo $this->form->hiddenField($this->model->movement->auxPerson, 'provider_credit_days', array(
                    'id' => $this->person_credit_days_input_id,
                ));
            } else {
                echo CHtml::hiddenField(null, '', array('id' => $this->person_credit_days_input_id));
            }
        }
        if (isset($this->model->movement->auxPerson)) {
            echo $this->form->hiddenField($this->model->movement->auxPerson, 'person_type', array(
                'id' => $this->person_type_input_id
            ));
        } else {
            echo CHtml::hiddenField(null, '', array(
                'id' => $this->person_type_input_id
            ));
        }
        if (isset($this->model->account_number)) {
            echo $this->form->hiddenField($this->model, 'account_number', array(
                'id' => $this->account_number_input_id,
            ));
        } else {
            echo CHtml::hiddenField('CommercialMovement[account_number]', '', array(
                'id' => $this->account_number_input_id
            ));
        }
        echo "</div>";
        //Sí está habilitada la condición readonly_condition 
        if ($this->readonly_condition) {
            $aux = $this->readonly;
        } else {
            $aux = $this->readonly_condition;
        }
        if ($this->condition) {
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
            echo $this->form->dropDownListRow($this->model, 'condition', $this->condition_items, array(
                'id' => $this->condition_input_id,
                'readonly' => $aux,
                'class' => $this->condition_class
            ));
            echo "</div>";
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
            echo $this->form->numberFieldRow($this->model, 'credit_days', array(
                'id' => $this->credit_days_input_id,
                'class' => 'us-double0',
                'maxlength' => 3,
                'min' => '0',
                'readonly' => $this->readonly || ($this->model->condition != Condition::CREDIT && $this->model->condition != Condition::ON_ACCOUNT),
            ));
            echo "</div>";
        }

        echo "</div>";
    }

}
