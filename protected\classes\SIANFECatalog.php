<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANFECatalog
 *
 * <AUTHOR>
 */

/**
 * http://cpe.sunat.gob.pe/sites/default/files/inline-images/GUIA_Resumen_de_Boletas_11-01-2018%20%282%29_2.pdf
 */
class SIANFECatalog {

    //
    const CATALOG_05_1000 = '1000';
//    const CATALOG_05_1016 = '1016';
//    const CATALOG_05_2000 = '2000';
//    const CATALOG_05_3000 = '3000';
//    const CATALOG_05_7152 = '7152';
    const CATALOG_05_9995 = '9995';
    const CATALOG_05_9996 = '9996';
    const CATALOG_05_9997 = '9997';
    const CATALOG_05_9998 = '9998';
//    const CATALOG_05_9999 = '9999';
    //
    const CATALOG_07_10 = "10";
    const CATALOG_07_11 = "11";
    const CATALOG_07_12 = "12";
    const CATALOG_07_13 = "13";
    const CATALOG_07_14 = "14";
    const CATALOG_07_15 = "15";
    const CATALOG_07_16 = "16";
    const CATALOG_07_17 = "17";
    const CATALOG_07_20 = "20";
    const CATALOG_07_21 = "21";
    const CATALOG_07_30 = "30";
    const CATALOG_07_31 = "31";
    const CATALOG_07_32 = "32";
    const CATALOG_07_33 = "33";
    const CATALOG_07_34 = "34";
    const CATALOG_07_35 = "35";
    const CATALOG_07_36 = "36";
    const CATALOG_07_40 = "40";
    //
    const CATALOG_11_AFFECTED = "01";
    const CATALOG_11_NOBILL = "02";
    const CATALOG_11_INAFFECTED = "03";
    const CATALOG_11_EXPORT = "04";
    const CATALOG_11_FREE = "05";
    //
    const CATALOG_14_AFFECTED = "1001"; // Total valor de venta - operaciones gravadas (affected)
    const CATALOG_14_INAFFECTED = "1002"; // Total valor de venta - operaciones inafectas (inaffected)
    const CATALOG_14_NOBILL = "1003"; // Total valor de venta - operaciones exoneradas (nobill)
    const CATALOG_14_FREE = "1004"; // Total valor de venta – Operaciones gratuitas (free_transfer)
    const CATALOG_14_NET = "1005"; // Sub total de venta
    const CATALOG_14_PERCEPTION = "2001"; // Percepciones
    const CATALOG_14_RETENTION = "2002"; // Retenciones
    const CATALOG_14_DETRACTION = "2003"; // Detracciones
    const CATALOG_14_BONIFICATION = "2004"; // Bonificaciones
    const CATALOG_14_DISCOUNT = "2005"; // Total descuentos (descuentos globales)
    //
    const CATALOG_15_AMOUNT_TO_LETTERS = "1000";
    const CATALOG_15_FREE_TRANSFER = "1002";
    //
    const CATALOG_16_UNITARY_PRICE = "01";
    const CATALOG_16_REFERENCIAL_VALUE = "02";
    //
    const CATALOG_19_ADD = 1;
    const CATALOG_19_EDIT = 2;
    const CATALOG_19_ANNUL = 3;
    //
    const CATALOG_18_PUBLIC = '01';
    const CATALOG_18_PRIVATE = '02';
    //
    const CATALOG_22_INTERNAL = "01";
    const CATALOG_22_FUEL = "02";
    const CATALOG_22_SPECIAL = "03";
    //
    const CATALOG_51_INTERNAL_SALE = "0101";
    const CATALOG_51_EXPORT = "0102";
    const CATALOG_51_SALE_DETRACTION = "1001";
    const CATALOG_51_INTERNAL_PURCHASE = "0501";
    const CATALOG_51_PURCHASE_ADVANCE = "0502";
    
    //
    const CATALOG_52_AMOUNT_TO_LETTERS = "1000";
    const CATALOG_52_FREE_TRANSFER = "1002";
    const CATALOG_52_OPERATION_DETRACTION = "2006";

    //
    public static function getCatalog05Map() {
        return [
            self::CATALOG_05_1000 => 'affected',
            self::CATALOG_05_9998 => 'inaffected',
            self::CATALOG_05_9997 => 'nobill',
            self::CATALOG_05_9995 => 'export',
            self::CATALOG_05_9996 => 'free',
        ];
    }

    //
    public static function getCatalog05DutyCodes() {
        return [
            self::CATALOG_05_1000 => 'S',
//            self::CATALOG_05_1016 => '???',
//            self::CATALOG_05_2000 => 'S',
//            self::CATALOG_05_3000 => '???',
//            self::CATALOG_05_7152 => '???',
            self::CATALOG_05_9995 => 'G',
            self::CATALOG_05_9996 => 'Z',
            self::CATALOG_05_9997 => 'E',
            self::CATALOG_05_9998 => 'O',
//            self::CATALOG_05_9999 => 'S',
        ];
    }

    //
    public static function getCatalog05InterCodes() {
        return [
            self::CATALOG_05_1000 => 'VAT',
//            self::CATALOG_05_1016 => 'VAT',
//            self::CATALOG_05_2000 => 'EXC',
//            self::CATALOG_05_3000 => 'TOX',
//            self::CATALOG_05_7152 => 'OTH',
            self::CATALOG_05_9995 => 'FRE',
            self::CATALOG_05_9996 => 'FRE',
            self::CATALOG_05_9997 => 'VAT',
            self::CATALOG_05_9998 => 'FRE',
//            self::CATALOG_05_9999 => 'OTH',
        ];
    }

    public static function getCatalog05Names() {
        return [
            self::CATALOG_05_1000 => 'IGV',
//            self::CATALOG_05_1016 => 'IVAP',
//            self::CATALOG_05_2000 => 'ISC',
//            self::CATALOG_05_3000 => 'IR',
//            self::CATALOG_05_7152 => 'ICBPER',
            self::CATALOG_05_9995 => 'EXP',
            self::CATALOG_05_9996 => 'GRA',
            self::CATALOG_05_9997 => 'EXO',
            self::CATALOG_05_9998 => 'INA',
//            self::CATALOG_05_9999 => 'OTROS',
        ];
    }

    public static function getCatalog11Map() {
        return [
            self::CATALOG_11_AFFECTED => 'affected',
            self::CATALOG_11_INAFFECTED => 'inaffected',
            self::CATALOG_11_NOBILL => 'nobill',
            self::CATALOG_11_EXPORT => 'export',
            self::CATALOG_11_FREE => 'free',
        ];
    }

}
