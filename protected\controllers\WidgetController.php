<?php

class WidgetController extends SIANController {

    use MerchandiseControllerTrait;

    public $plural = "Movimientos";
    public $singular = "movimiento";
    public $modelClass = 'Movement';
    public $viewClass = '';
    public $store_id = null;

    public function accessRules() {
        return array(
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('loadProvisionItems', 'getProvisioned', 'autocomplete', 'select2Items', 'objectView', 'getDpModel', 'createMassiveProducts', 'createRelatedProducts', 'setPageSize', 'getDefaultFilters', 'getAccountsToSchedule', 'getCostLevels', 'getCombination', 'costProducts', 'productsAttention', 'flowAttention', 'productAttention', 'getComboItems', 'exportAccess', 'getAddressList', 'getLockerBoxes', 'getContactForLockerRequest', 'getPromotionGifts', 'getPromotionGiftOptions', 'getOperations', 'getDocuments', 'getBusinessUnit', 'previewCustomCategory', 'previewConfirmationDetail', 'loadProducts', 'saveImagesMassive', 'defineMassivePrices', 'getPersonByDni'),
                'users' => array('@'),
            ),
            array('deny', // deny all users
                'users' => array('*'),
            ),
        );
    }

    public function actionLoadProvisionItems() {
        try {
            $periods = $_POST['periods'];

            $provision_items = AccountingFile::getProvisionItems($periods);

            echo CJSON::encode(array(
                'code' => USREST::CODE_SUCCESS,
                'message' => 'Success',
                'data' => SIANProvisioningSelector::getFileItems($provision_items)
            ));
        } catch (CDbException $ex) {
            echo CJSON::encode(array(
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => $ex->errorInfo[2],
            ));
        } catch (Exception $ex) {
            echo CJSON::encode(array(
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => $ex->getMessage(),
            ));
        }
    }

    public function actionGetProvisioned($id, $year, $period, $modal_id = null, $parent_id = null) {

        $model = AccountingPeriod::model()->findByPk([
            'accounting_file_id' => $id,
            'year' => $year,
            'period' => $period,
        ]);

        //Seteamos título
        $this->title = "Correlativos ocupados para el periodo {$model->toString()}";

        $data = Yii::app()->db->createCommand()
                ->select([
                    "M.movement_id",
                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) AS document",
                    "M.route",
                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') AS emission_date",
                    "M.aux_person_id",
                    "REPLACE(XP.person_name, ',', ' ') AS aux_person_name",
                    "AM.correlative",
                    "AM.checked",
                ])
                ->from('accounting_movement AM')
                ->join('movement M', 'M.movement_id = AM.movement_id AND M.`status`')
                ->join('person XP', 'XP.person_id = M.aux_person_id')
                ->where('AM.accounting_file_id = :accounting_file_id AND AM.`year` = :year AND AM.period = :period', array(':accounting_file_id' => $id, ':year' => $year, ':period' => $period))
                ->order("AM.correlative ASC")
                ->queryAll();

        $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('parent_id' => $parent_id, 'modal_id' => $modal_id)), true, true);

        $content_html = $this->renderPartial('_provisioned', array(
            'data' => array(
                'model' => $model,
                'modal_id' => $modal_id,
                'data' => $data,
            )), true, true);

        echo CJSON::encode(array(
            //'success' => $success,
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html,
            //'type' => $type,
            //'element_id' => $element_id,
            //'primaryKey' => $model->primaryKey,
            'toString' => $model->toString(),
        ));
    }

    public function actionAutocomplete() {
        try {
            // US
            $s_model_class = $_GET['modelClass'];
            $s_scenario = isset($_GET['scenario']) ? $_GET['scenario'] : null;
            $a_select_attributes = $_GET['selectAttributes'];
            $a_search_attributes = $_GET['searchAttributes'];
            $a_in = isset($_GET['in']) ? $_GET['in'] : [];
            $a_not_in = isset($_GET['not_in']) ? $_GET['not_in'] : [];
            $a_params = isset($_GET['params']) ? $_GET['params'] : [];
            //
            $i_page = $_GET['page']; // get the requested page
            $i_limit = $_GET['rows']; // get how many rows we want to have into the grid
            $s_sidx = $_GET['sidx']; // get index row - i.e. user click to sort
            $s_sord = $_GET['sord']; // get the direction
            $s_search_term = isset($_GET['searchTerm']) ? $_GET['searchTerm'] : '';

            //Instanciamos
            if (USString::isBlank($s_scenario)) {
                $o_model = new $s_model_class();
            } else {
                $o_model = new $s_model_class($s_scenario);
            }

            //Seteamos parámetros (si hubiera)
            if (count($a_params) > 0) {
                $o_model->setAttributes($a_params);
            }

            //Si usa la versión antigua
            if ($o_model instanceof DpBase) {

                $o_model->addTermCondition($a_search_attributes, $s_search_term);

                if (count($a_in) > 0) {
                    foreach ($a_in as $name => $array) {
                        $o_model->addInCondition($name, $array);
                    }
                }

                if (count($a_not_in) > 0) {
                    foreach ($a_not_in as $name => $array) {
                        $o_model->addNotInCondition($name, $array);
                    }
                }

                $a_order = false;
                if (!USString::isBlank($s_sidx)) {
                    $a_order = [
                        $s_sidx => $s_sord
                    ];
                }

                $a_response = $o_model->setOrder($a_order)->findPage($i_page, $i_limit, true);
            } else {
                if (!$s_sidx) {
                    $s_sidx = $a_select_attributes[0];
                }
                if ($s_search_term == "") {
                    $s_search_term = "%";
                } else {
                    $s_search_term = '%' . str_replace(' ', '%', $s_search_term) . '%';
                }

                $criteria = new CDbCriteria();
                $criteria->select = $a_select_attributes;

                $attribute_condition = [];
                foreach ($a_select_attributes as $attribute) {
                    array_push($attribute_condition, "$attribute like '$s_search_term'");
                }

                $criteria->addCondition("(" . implode(' or ', $attribute_condition) . ")");

                if (count($a_in) > 0) {
                    $in_conditions = [];

                    foreach ($a_in as $name => $array) {
                        array_push($in_conditions, $name . " in " . USMySQL::getSQLArray($array, "'"));
                    }

                    if (count($in_conditions) > 0) {
                        $criteria->addCondition("(" . implode(' and ', $in_conditions) . ")");
                    }
                }

                if (count($a_not_in) > 0) {
                    $not_in_conditions = [];

                    foreach ($a_not_in as $name => $array) {

                        if (count($array) > 0) {
                            array_push($not_in_conditions, $name . " not in " . USMySQL::getSQLArray($array, "'"));
                        }
                    }

                    if (count($not_in_conditions) > 0) {
                        $criteria->addCondition("(" . implode(' and ', $not_in_conditions) . ")");
                    }
                }

                $count = $o_model->count($criteria);

                if ($count > 0) {
                    $total_pages = ceil($count / $i_limit);
                } else {
                    $total_pages = 0;
                }
                if ($i_page > $total_pages) {
                    $i_page = $total_pages;
                }
                $start = ($i_limit * $i_page) - $i_limit; // do not put $limit*($page - 1)
                //$finish = $start + $limit - 1;

                $criteria->order = "$s_sidx $s_sord";

                if ($total_pages != 0) {

                    $criteria->offset = $start;
                    $criteria->limit = $i_limit;
                }


                $a_response = [];
                $a_response['page'] = $i_page;
                $a_response['total'] = $total_pages;
                $a_response['records'] = $count;
                $a_response['rows'] = [];

                $i = 0;
                foreach ($o_model->findAll($criteria) as $o_model) {
                    $a_response['rows'][$i] = $o_model->attributes;
                    $i++;
                }
            }

            echo CJSON::encode($a_response);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function actionObjectView() {
        $id_attribute = $_POST['id_attribute'];
        $value_attribute = $_POST['value_attribute'];
        $aux_attribute = $_POST['aux_attribute'];
        $text_attribute = $_POST['text_attribute'];
        $id = $_POST['id'];

        $model = new $_POST['viewClass']($_POST['scenario']);

        if ($model instanceof DpBase) {
            $model->addEqualCondition($id_attribute, $id);
            echo CJSON::encode($model->find());
        } else {
            $criteria = new CDbCriteria();
            $criteria->select = [$value_attribute, $aux_attribute, $text_attribute];
            $criteria->compare($id_attribute, $id);

            echo CJSON::encode($model->find($criteria));
        }
    }

    public function actionGetDpModel() {
        $attribute = $_POST['attribute'];
        $attribute_in = $_POST['attribute_in'];
        $presentationMode = $_POST['presentationMode'];
        $model = new $_POST['viewClass']($_POST['scenario']);
        $use_related = isset($_POST['use_related']) ? $_POST['use_related'] : null;
        $filter_presentation = isset($_POST['filter_presentation']) ? $_POST['filter_presentation'] : null;
        $filter_related = isset($_POST['filter_related']) ? $_POST['filter_related'] : null;

        $model->addInCondition($attribute, $attribute_in);

        if ($use_related != null && $use_related == 1) {
            $model->use_related = 1;
        }

        if ($filter_presentation != null) {
            $model->filter_presentation = $filter_presentation;
        }


        if ($filter_related != null) {
            $model->filter_related = $filter_related;
        }

        $a_data = $model->findAll();

        $product_ids = [];
        foreach ($a_data as $row) {
            $product_ids[] = $row['product_id'];
        }

        $presentationItems = SpGetProductPresentations::getAssociative($presentationMode, $product_ids, Yii::app()->controller->getOrganization()->globalVar->display_currency);
        $a_presentations = [];
        foreach ($a_data as $row) {
            $row->presentationItems = $presentationItems[$row['product_id']];
            $a_presentations[] = $row;
        }
        echo CJSON::encode($a_presentations);
    }

    public function actionGetPersonByDni() {

        $model = new DpBusinessPartner();
        $model->scenario = DpBusinessPartner::SCENARIO_ASSOCIATED;
        $model->addInCondition('identification_number', $_POST['dni']);
        $model->addEqualCondition('identification_type', '1');

        echo CJSON::encode($model->find());
    }

    public function actioncreateMassiveProducts() {
        $data = $_POST['data'];

        $a_error_products = [];

        $index = 1;
        $savedIndex = 0;

        foreach ($data as $row) {

            $merchandise = null;

            $merchandise = Merchandise::model()->with(array(
                        'product' => array(
                            'with' => array(
                                'presentations' => array(
                                    'together' => true,
                                    'condition' => 'presentations.barcode=:barcode',
                                    'params' => array(':barcode' => $barcode),
                                ),
                            ),
                            'together' => true,
                        ),
                    ))->find();

            if (!$merchandise) {
                $custom1_label = Yii::app()->controller->getOrganization()->globalVar->product_custom_name_1;
                $custom2_label = Yii::app()->controller->getOrganization()->globalVar->product_custom_name_2;
                $aprice_label = Yii::app()->controller->getOrganization()->globalVar->aprice_label . ' (Inc. IGV)';
                $wprice_label = Yii::app()->controller->getOrganization()->globalVar->wprice_label . ' (Inc. IGV)';

                $spUploadProduct = (new SpUploadProduct())->setParams([
                    'nro_item' => $index,
                    'xproduct_name' => $row['Producto'],
                    'xdescription' => $row['Descripción'],
                    'xdivision_name' => $row['División'],
                    'xline_name' => $row['Línea'],
                    'xsubline_name' => $row['Sub-Línea'],
                    'xmark_name' => $row['Marca'],
                    'xmodel' => $row['Modelo'],
                    'xpart_number' => isset($row['N° de Parte']) ? $row['N° de Parte'] : "",
                    'xmeasure_name' => $row['Presentación'],
                    'xequivalence' => $row['Equivalencia'],
                    'xweight' => isset($row['Peso']) ? $row['Peso'] : 0,
                    'xbarcode' => isset($row['Código de Barras']) ? $row['Código de Barras'] : "",
                    'xitem_type' => $row['Tipo Producto'],
                    'xsize' => isset($row['Talla']) ? $row['Talla'] : "",
                    'xcustom1' => isset($row[$custom1_label]) ? $row[$custom1_label] : "",
                    'xcustom2' => isset($row[$custom2_label]) ? $row[$custom2_label] : "",
                    'ximprice' => isset($row['P. Min. (Inc. IGV)']) ? $row['P. Min. (Inc. IGV)'] : "",
                    'xiaprice' => isset($row[$aprice_label]) ? $row[$aprice_label] : "",
                    'xuse_batch' => isset($row['Usa Lotes']) ? $row['Usa Lotes'] == "SI" ? 1 : 0 : 0,
                    'xbatch_date' => isset($row['Fecha de Ingreso']) && isset($row['Usa Lotes']) ? $row['Fecha de Ingreso'] : "",
                    'xperson_id' => Yii::app()->user->getState('person_id')
                ]);

                if ($spUploadProduct->validate()) {
                    $response = $spUploadProduct->execute();
                    if ($response) {
                        $a_error_products[] = 'Nro Item:' . $index . ' - Error: ' . $row['Producto'] . ' y presentación ya registrados.';
                    } else {
                        $savedIndex++;
                    }
                }
            } else {
                $a_error_products[] = 'Nro Item:' . $index . ' - Error: ' . $row['Producto'] . ' y presentación ya registrados.';
            }
            $index++;
        }

        $o_response = [
            'message' => "Se procesaron " . $savedIndex . ' de ' . count($data) . ' registros',
            'errors' => $a_error_products,
        ];

        echo CJSON::encode($o_response);
    }

    /**
     * Funcion que solo debe ser ejecutada cuando use_products_related de global_var este activa
     */
    public function actioncreateRelatedProducts() {
        $data = $_POST['data'];

        $generateSamples = $_POST['generateSamples'];

        $a_error_products = [];

        $index = 1;
        $savedIndex = 0;

        $a_stores_items = CHtml::listData(Store::model()->findAll(['condition' => 'status = 1']), 'store_id', 'store_name');
        $i_merchandise_type_id = Multitable::model()->findMultiIdForMerchandise();
        $i_sample_type_id = Multitable::model()->findMultiIdForSample();

        $imprice_label = 'P.Min. (Inc. IGV)';
        $iaprice_label = Yii::app()->controller->getOrganization()->globalVar->aprice_label . ' (Inc. IGV)';
        $iwprice_label = Yii::app()->controller->getOrganization()->globalVar->wprice_label . ' (Inc. IGV)';
        $custom1_label = Yii::app()->controller->getOrganization()->globalVar->product_custom_name_1;
        $custom2_label = Yii::app()->controller->getOrganization()->globalVar->product_custom_name_2;
        $batch_name = Yii::app()->controller->getOrganization()->globalVar->business_line_type == GlobalVar::BUSINESS_LINE_FOOTWEAR ? 'Packing' : 'Código de Lote';

        foreach ($data as $row) {
            if (isset($row['Código de Barras'])) {
                $merchandise = null;

                $merchandise = Merchandise::model()->with(array(
                            'product' => array(
                                'with' => array(
                                    'presentations' => array(
                                        'together' => true,
                                        'condition' => 'presentations.barcode=:barcode',
                                        'params' => array(':barcode' => $row['Código de Barras']),
                                    ),
                                ),
                                'together' => true,
                            ),
                        ))->find();

                if (!$merchandise) {
                    if (isset($row[$batch_name]) && $row[$batch_name] !== "") {
                        $a_sizes_quantity = $this->processSizeQuantity($row['Talla/Cantidad'], $a_error_products, $index, $row['Producto']);

                        if ($a_sizes_quantity) {
                            $spUploadProduct = (new SpUploadProduct())->setParams([
                                'nro_item' => $index,
                                'xproduct_name' => $row['Producto'],
                                'xdescription' => $row['Descripción'],
                                'xdivision_name' => null,
                                'xline_name' => null,
                                'xsubline_name' => 'STANDARD',
                                'xmark_name' => $row['Marca'],
                                'xmodel' => $row['Modelo'],
                                'xpart_number' => "",
                                'xmeasure_name' => $row['Presentación'],
                                'xequivalence' => 1,
                                'xweight' => 0,
                                'xbarcode' => isset($row['Código de Barras']) ? $row['Código de Barras'] : "",
                                'xitem_type' => "Mercadería",
                                'xsize' => "",
                                'xcustom1' => isset($row[$custom1_label]) ? $row[$custom1_label] : "",
                                'xcustom2' => isset($row[$custom2_label]) ? $row[$custom2_label] : "",
                                'ximprice' => isset($row[$imprice_label]) ? $row[$imprice_label] : 0,
                                'xiaprice' => isset($row[$iaprice_label]) ? $row[$iaprice_label] : 0,
                                'xiwprice' => isset($row[$iwprice_label]) ? $row[$iwprice_label] : 0,
                                'xuse_batch' => 1,
                                'xbatch_date' => isset($row['Fecha de Ingreso']) ? $row['Fecha de Ingreso'] : SIANTime::todayWithCustomformat('Y-m-d'),
                                'xperson_id' => Yii::app()->user->getState('person_id'),
                                'xbatch_code' => isset($row[$batch_name]) ? $row[$batch_name] : '',
                            ]);

                            if ($spUploadProduct->validate()) {
                                $response = $spUploadProduct->execute();
                                if ($response) {
                                    $a_error_products[] = 'Nro Item:' . $index . ' - Error: ' . $row['Producto'] . ' y presentación ya registrados.';
                                } else {
                                    $savedIndex++;

                                    $criteria = new CDbCriteria();
                                    $criteria->order = 'product_id DESC';
                                    $saved_product = Merchandise::model()->find($criteria);
                                    $this->processQuantityAndSizes($saved_product, $a_sizes_quantity, $row, $index, $a_stores_items, $i_merchandise_type_id, $custom1_label, $custom2_label);
                                }
                            }
                        }
                    } else {
                        $spUploadProduct = (new SpUploadProduct())->setParams([
                            'nro_item' => $index,
                            'xproduct_name' => $row['Producto'],
                            'xdescription' => $row['Descripción'],
                            'xdivision_name' => null,
                            'xline_name' => null,
                            'xsubline_name' => 'STANDARD',
                            'xmark_name' => $row['Marca'],
                            'xmodel' => $row['Modelo'],
                            'xpart_number' => "",
                            'xmeasure_name' => $row['Presentación'],
                            'xequivalence' => 1,
                            'xweight' => 0,
                            'xbarcode' => isset($row['Código de Barras']) ? $row['Código de Barras'] : "",
                            'xitem_type' => "Mercadería",
                            'xsize' => $row['Talla'],
                            'xcustom1' => isset($row[$custom1_label]) ? $row[$custom1_label] : "",
                            'xcustom2' => isset($row[$custom2_label]) ? $row[$custom2_label] : "",
                            'ximprice' => isset($row[$imprice_label]) ? $row[$imprice_label] : 0,
                            'xiaprice' => isset($row[$iaprice_label]) ? $row[$iaprice_label] : 0,
                            'xiwprice' => isset($row[$iwprice_label]) ? $row[$iwprice_label] : 0,
                            'xuse_batch' => 0,
                            'xbatch_date' => isset($row['Fecha de Ingreso']) ? $row['Fecha de Ingreso'] : SIANTime::todayWithCustomformat('Y-m-d'),
                            'xperson_id' => Yii::app()->user->getState('person_id'),
                            'xbatch_code' => '',
                        ]);

                        if ($spUploadProduct->validate()) {
                            $response = $spUploadProduct->execute();
                            if ($response) {
                                $a_error_products[] = 'Nro Item:' . $index . ' - Error: ' . $row['Producto'] . ' y presentación ya registrados.';
                            } else {
                                $savedIndex++;
                            }
                        }

                        if ($row['Presentación'] == Presentation::PRESENTATION_PAR && $generateSamples == "1") {
                            $id = Merchandise::model()->getLastProductId();
                            $o_parent = Merchandise::model()->with(array(
                                        'product' => array(
                                            'alias' => 'P',
                                            'joinType' => 'join',
                                            'with' => array(
                                                'unitPresentation'
                                            )
                                        ),
                                    ))->findByPk(array('product_id' => $id));
                            $this->generateSamples($o_parent, $i_sample_type_id);
                        }
                    }
                } else {
                    if (isset($row[$batch_name]) && $row[$batch_name] !== "") {
                        $a_sizes_quantity = $this->processSizeQuantity($row['Talla/Cantidad'], $a_error_products, $index, $row['Producto']);
                        if ($a_sizes_quantity) {
                            if (isset($row[$batch_name])) {
                                $alert = 'Nro Item:' . $index . ' - Error: ' . $row['Producto'] . ' y presentación ya registrados ';

                                $batch = null;
                                $batch = Batch::model()->find(array(
                                    'condition' => 'batch_code=:batch_code AND product_id=:product_id',
                                    'params' => array(':batch_code' => $row[$batch_name], ':product_id' => $merchandise->product_id),
                                ));

                                if (!$batch) {
                                    $o_new_batch = new Batch(Batch::SCENARIO_LOGISTIC);
                                    $o_new_batch->batch_code = $row[$batch_name];
                                    $o_new_batch->product_id = $merchandise->product_id;
                                    $o_new_batch->expiration_date = SIANTime::todayPHP();
                                    $o_new_batch->user_register = Yii::app()->user->getState('person_id');
                                    if ($o_new_batch->validate()) {
                                        $o_new_batch->insert();
                                        $alert = $alert . ", se añadió el nuevo lote con el packing " . $row[$batch_name];

                                        $packing = Packing::model()->find(array(
                                            'condition' => 'packing_code=:packing_code',
                                            'params' => array(':packing_code' => $row[$batch_name])
                                        ));

                                        if (!$packing) {
                                            $o_new_packing = new Packing();
                                            $o_new_packing->packing_id = $merchandise->product_id;
                                            $o_new_packing->packing_code = $row[$batch_name];
                                            $o_new_packing->n_products = 1;
                                            $o_new_packing->insert();
                                        } else {
                                            $packing->n_products = $packing->n_products + 1;
                                            $packing->update();
                                        }
                                    } else {
                                        $alert = $alert . ", no se pudo añadir el nuevo lote";
                                    }

                                    $a_error_products[] = $alert;
                                } else {
                                    $a_error_products[] = 'Nro Item:' . $index . ' - Error: ' . $row['Producto'] . ' y presentación ya registrados.';
                                }
                            }
                            $this->processQuantityAndSizes($merchandise, $a_sizes_quantity, $row, $index, $a_stores_items, $i_merchandise_type_id, $custom1_label, $custom2_label);
                        }
                    } else {
                        if ($row['Presentación'] == Presentation::PRESENTATION_PAR && $generateSamples == "1") {

                            $children_count = Merchandise::model()->countProductLinks($merchandise->product_id);

                            if ($children_count == 0) {
                                $o_parent = Merchandise::model()->with(array(
                                            'product' => array(
                                                'alias' => 'P',
                                                'joinType' => 'join',
                                                'with' => array(
                                                    'unitPresentation'
                                                )
                                            ),
                                        ))->findByPk(array('product_id' => $merchandise->product_id));
                                $this->generateSamples($o_parent, $i_sample_type_id);
                            }
                        }
                    }
                }
            }
            $index++;
        }

        $o_response = [
            'message' => "Se procesaron " . $savedIndex . ' de ' . count($data) . ' registros',
            'errors' => $a_error_products,
        ];

        echo CJSON::encode($o_response);
    }

    public function actionSaveImagesMassive() {
        $searchAttribute = $_POST['searchAttribute'];
        $a_not_find = [];
        $n_files = 0;
        $n_add_images = 0;

        foreach ($_FILES as $file) {
            $model = new DpAllProductToFile;

            $dotPosition = strrpos($file['name'], '.');

            $value = substr($file['name'], 0, $dotPosition);
            $fileExtension = substr($file['name'], $dotPosition);

            $model->addInCondition($searchAttribute, [$value]);
            $a_products = $model->findAll();

            $url_path = Yii::app()->params['org_url'] . '/upload/merchandise/images/';

            if (count($a_products) > 0) {
                $path = "../" . Yii::app()->params['skin'] . "/upload/merchandise/";
                $n_files++;

                foreach ($a_products as $o_product) {

                    $sql_n_images = "SELECT count(*) from `resource` as R
                    WHERE R.owner = :owner AND R.type = :type AND R.owner_id = :owner_id";

                    $count = Yii::app()->db->createCommand($sql_n_images)->queryScalar([
                        ':owner' => 'Product',
                        ':type' => 'Image',
                        ':owner_id' => $o_product['product_id'],
                    ]);

                    $count = $count + 1;

                    $uploadedFile = new CUploadedFile($file['name'], $file['tmp_name'], $file['type'], $file['size'], $file['error']);

                    $imageSize = getimagesize($uploadedFile->getTempName());
                    $originalWidth = $imageSize[0];
                    $originalHeight = $imageSize[1];

                    $sizes = array(
                        array('width' => 100, 'height' => 100, 'folder' => '.thumbs/images/'),
                        array('width' => 300, 'height' => 300, 'folder' => '.medium/images/'),
                        array('width' => $originalWidth, 'height' => $originalHeight, 'folder' => 'images/'),
                    );

                    foreach ($sizes as $size) {
                        if ($originalWidth > $originalHeight) {
                            $newWidth = $size['width'];
                            $newHeight = intval(($originalHeight / $originalWidth) * $newWidth);
                        } else {
                            $newHeight = $size['height'];
                            $newWidth = intval(($originalWidth / $originalHeight) * $newHeight);
                        }

                        $image = imagecreatefromstring(file_get_contents($uploadedFile->getTempName()));
                        $newImage = imagecreatetruecolor($newWidth, $newHeight);
                        imagecopyresampled($newImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);

                        $folderPath = $path . $size['folder'];
                        if (!file_exists($folderPath)) {
                            mkdir($folderPath, 0777, true);
                        }
                        $newFileName = $folderPath . $o_product['product_id'] . '-' . $count . $fileExtension;
                        imagepng($newImage, $newFileName);
                        imagedestroy($image);
                        imagedestroy($newImage);
                    }

                    $sql = "INSERT INTO `resource` (`owner`, `type`, `sub_type`, `resource_number`, `title`, `description`, `url`, `width`, `height`, `owner_id`, `target_url`)
                            VALUES (:owner, :type, :sub_type, :resource_number, :title, :description, :url, :width, :height, :owner_id, :target_url)";

                    Yii::app()->db->createCommand($sql)->execute([
                        ':owner' => 'Product',
                        ':type' => 'Image',
                        ':sub_type' => 'Default',
                        ':resource_number' => $count + 1,
                        ':title' => $o_product['product_name'],
                        ':description' => $o_product['product_name'],
                        ':url' => $url_path . $o_product['product_id'] . '-' . $count . $fileExtension,
                        ':width' => $originalWidth,
                        ':height' => $originalHeight,
                        ':owner_id' => $o_product['product_id'],
                        ':target_url' => null,
                    ]);
                    $n_add_images++;
                }
            } else {
                $a_not_find[] = "No se encontraron productos registrados con " . $value;
            }
        }

        echo CJSON::encode(["success" => true, "errors" => $a_not_find, "message" => "Se procesaron " . $n_files . " de " . count($_FILES) . " imagenes, agregando a " . $n_add_images . " productos"]);
    }

    public function actionSelect2Items() {

        $s_dp = isset($_POST['dp']) ? $_POST['dp'] : null;
        $s_scenario = isset($_POST['scenario']) ? $_POST['scenario'] : '';
        $s_search = isset($_POST['search']) ? $_POST['search'] : '';
        $b_key = isset($_POST['key']) ? $_POST['key'] : false;
        $i_page = isset($_POST['page']) ? $_POST['page'] : 1;
        $i_limit = isset($_POST['limit']) ? $_POST['limit'] : 10;
        $a_extra = isset($_POST['extra']) ? $_POST['extra'] : [];

        if (USString::isBlank($s_scenario)) {
            $dpObj = new $s_dp();
        } else {
            $dpObj = new $s_dp($s_scenario);
        }

        switch ($s_dp) {
            case 'DpBusinessPartner':
                if ($b_key) {
                    $a_results = $dpObj
                            ->addEqualCondition('person_id', $s_search)
                            ->findSelect2('person_id', 'identification_number', 'person_name', 1, false);
                } else {
                    $a_results = $dpObj
                            ->addTermCondition(['identification_number', 'person_name'], $s_search)
                            ->findSelect2('person_id', 'identification_number', 'person_name', $i_page, $i_limit);
                }
                break;
            case 'DpFixedAsset':
                if ($b_key) {
                    if (isset($a_extra['account'])) {
                        $dpObj->account = $a_extra['account'];
                    }
                    $a_results = $dpObj->findSelect2('fixed_asset_id', 'code', 'description', 1, false);
                } else {
                    if (isset($a_extra['account'])) {
                        $dpObj->account = $a_extra['account'];
                    }
                    $a_results = $dpObj->findSelect2('fixed_asset_id', 'code', 'description', $i_page, $i_limit);
                }

                break;
            case 'DpAccount':
                if (isset($a_extra['initial_value']) && $a_extra['initial_value'] == Account::WILDCARD_CASHBOX) {
                    $dpObj = new $s_dp(DpAccount::SCENARIO_CASHBOX);
                }
                if ($b_key) {
                    $a_results = $dpObj
                            ->addEqualCondition('account_code', $s_search)
                            ->findSelect2('account_code', 'account_code', 'account_name', 1, false);
                } else {
                    $a_results = $dpObj
                            ->addLikeCondition('account_code', (isset($a_extra['preload']) ? $a_extra['preload'] : null), DpBase::LIKE_BEGINS_WITH)
                            ->addTermCondition(['account_code', 'account_name'], $s_search)
                            ->findSelect2('account_code', 'account_code', 'account_name', $i_page, $i_limit);
                }

                break;
            case 'DpCostLevelItem':
                if ($b_key) {
                    $a_results = $dpObj
                            ->addEqualCondition('cost_level_item_id', $s_search)
                            ->findSelect2('cost_level_item_id', 'cost_level_item_code', 'cost_level_item_name', 1, false);
                } else {
                    $a_results = $dpObj
                            ->addLikeCondition('cost_level_item_name', $s_search, DpBase::LIKE_CONTAINS)
                            ->findSelect2('cost_level_item_id', 'cost_level_item_code', 'cost_level_item_name', $i_page, $i_limit);
                }
                break;
            case 'DpCostLevelItemFromCombination':
                //Verificamos
                $a_target_ids = [];
                if (isset($a_extra['target_ids']) && count($a_extra['target_ids']) > 0) {
                    $this->_addCombinations($a_extra['target_ids'], $a_target_ids);
                }
                if (count($a_target_ids) == 0) {
                    throw new Exception('No se puede cargar el componente.');
                }
                $dpObj->combination_id = implode(',', $a_target_ids);
                $dpObj->level1_id = isset($a_extra['level1_id']) && strlen($a_extra['level1_id']) > 0 ? $a_extra['level1_id'] : null;
                $dpObj->level2_id = isset($a_extra['level2_id']) && strlen($a_extra['level2_id']) > 0 ? $a_extra['level2_id'] : null;
                $dpObj->level3_id = isset($a_extra['level3_id']) && strlen($a_extra['level3_id']) > 0 ? $a_extra['level3_id'] : null;
                $dpObj->level4_id = isset($a_extra['level4_id']) && strlen($a_extra['level4_id']) > 0 ? $a_extra['level4_id'] : null;
                $dpObj->level5_id = isset($a_extra['level5_id']) && strlen($a_extra['level5_id']) > 0 ? $a_extra['level5_id'] : null;

                if (isset($a_extra['parent_id'])) {
                    $dpObj->parent_id = $a_extra['parent_id'];
                }
                if ($b_key) {
                    $a_results = $dpObj
                            ->addEqualCondition('cost_level_item_id', $s_search)
                            ->findSelect2('cost_level_item_id', 'cost_level_item_code', 'cost_level_item_name', 1, false);
                } else {
                    $a_results = $dpObj
                            ->addLikeCondition('cost_level_item_all', $s_search, DpBase::LIKE_CONTAINS)
                            ->findSelect2('cost_level_item_id', 'cost_level_item_code', 'cost_level_item_name', $i_page, $i_limit);
                }
                break;
            case 'DpOrganization':
                if ($b_key) {
                    $a_results = $dpObj
                            ->addEqualCondition('organization_id', $s_search)
                            ->findSelect2('organization_id', 'organization_id', 'organization_name', 1, false);
                } else {
                    $a_results = $dpObj
                            ->addLikeCondition('organization_name', $s_search, DpBase::LIKE_BEGINS_WITH)
                            ->findSelect2('organization_id', 'organization_id', 'organization_name', $i_page, $i_limit);
                }
                break;
            case 'DpStore':
                if ($b_key) {
                    $a_results = $dpObj
                            ->addEqualCondition('store_id', $s_search)
                            ->findSelect2('store_id', 'store_id', 'store_name', 1, false);
                } else {
                    $a_results = $dpObj
                            ->addTermCondition(['store_name'], $s_search)
                            ->findSelect2('store_id', 'store_id', 'store_name', $i_page, $i_limit);
                }
                break;
            case 'DpWarehouse':
                if ($b_key) {
                    $a_results = $dpObj
                            ->addEqualCondition('warehouse_id', $s_search)
                            ->findSelect2('warehouse_id', 'warehouse_id', 'warehouse_name', 1, false);
                } else {
                    $a_results = $dpObj
                            ->addTermCondition(['acronyme', 'warehouse_name'], $s_search)
                            ->findSelect2('warehouse_id', 'warehouse_id', 'warehouse_name', $i_page, $i_limit);
                }
                break;
            case 'DpApiUserVariant':
                if ($b_key) {
                    $a_results = $dpObj
                            ->addEqualCondition('api_user_variant_id', $s_search)
                            ->findSelect2('api_user_variant_id', 'api_user_variant_id', 'api_user_variant_name', 1, false);
                } else {
                    $a_results = $dpObj
                            ->addTermCondition(['api_user_variant_name'], $s_search)
                            ->findSelect2('api_user_variant_id', 'api_user_variant_id', 'api_user_variant_name', $i_page, $i_limit);
                }
                break;
            case 'DpMultitable':
                if ($b_key) {
                    $a_results = $dpObj
                            ->addEqualCondition('multi_id', $s_search)
                            ->findSelect2('multi_id', 'multi_id', 'description', 1, false);
                } else {
                    $a_results = $dpObj
                            ->addTermCondition(['value', 'description'], $s_search)
                            ->findSelect2('multi_id', 'multi_id', 'description', $i_page, $i_limit);
                }
                break;
        }

        echo CJSON::encode($a_results);
    }

    public function actionSetPageSize() {

        $grid_id = $_POST['grid_id'];
        $page_size = $_POST['page_size'];

        echo CJSON::encode(SIANUserCache::add($grid_id, $page_size));
    }

    public function actionGetDefaultFilters() {

        $underscored_route = $_POST['underscored_route'];

        echo CJSON::encode(SIANUserCache::get($underscored_route . '_DEFAULT'));
    }

    public function actionGetAccountsToSchedule() {
        //Obtenemos datos
        $a_schedule_sunat_code = isset($_POST['schedule_sunat_code']) ? $_POST['schedule_sunat_code'] : null;
        $s_schedule_document = isset($_POST['schedule_document']) ? $_POST['schedule_document'] : null;
        $s_shedule_identification_number = isset($_POST['schedule_identification_number']) ? $_POST['schedule_identification_number'] : null;
        $s_schedule_person_name = isset($_POST['schedule_person_name']) ? $_POST['schedule_person_name'] : null;
        $s_schedule_date_type = isset($_POST['schedule_date_type']) ? $_POST['schedule_date_type'] : null;
        $s_schedule_begin_date = isset($_POST['schedule_begin_date']) ? $_POST['schedule_begin_date'] : null;
        $s_schedule_end_date = isset($_POST['schedule_end_date']) ? $_POST['schedule_end_date'] : null;
        //Obligatorios
        if (!isset($s_schedule_date_type)) {
            throw new Exception('Debe especificar tipo de fecha.');
        }
        if (!isset($s_schedule_begin_date)) {
            throw new Exception('Debe especificar fecha de inicio.');
        }
        if (!isset($s_schedule_end_date)) {
            throw new Exception('Debe especificar fecha de fin.');
        }
        //Inicializamos DP
        $s_scenario = DpNotPaidOrCollectedAccounts::SCENARIO_PROVIDER_SCHEDULE;
        $o_dp = (new DpNotPaidOrCollectedAccounts($s_scenario));
        //Filtros
        if (isset($a_schedule_sunat_code) && is_array($a_schedule_sunat_code) && count($a_schedule_sunat_code) > 0) {
            $o_dp->addInCondition('sunat_code', $a_schedule_sunat_code);
        }
        if (isset($s_schedule_document) && strlen($s_schedule_document) > 0) {
            $o_dp->addTermCondition(['document', 'detail'], $s_schedule_document);
        }
        if (isset($s_shedule_identification_number) && strlen($s_shedule_identification_number) > 0) {
            $a_identification_numbers = explode(',', $s_shedule_identification_number);
            $o_dp->addInCondition('identification_number', $a_identification_numbers);
        }
        if (isset($s_schedule_person_name) && strlen($s_schedule_person_name) > 0) {
            $o_dp->addLikeCondition('aux_person_name', $s_schedule_person_name);
        }
        switch ($s_schedule_date_type) {
            case SIANPaymentSchedule::DATE_TYPE_EMISSION:
                $o_dp->addSimpleBetweenCondition('emission_date', $s_schedule_begin_date, $s_schedule_end_date);
                break;
            case SIANPaymentSchedule::DATE_TYPE_EXPIRATION:
                $o_dp->addSimpleBetweenCondition('expiration_date', $s_schedule_begin_date, $s_schedule_end_date);
                break;
            default:
                throw new Exception('Debe especificar el tipo de fecha sobre la cual se buscarán las cuentas');
        }

        echo CJSON::encode([
            'code' => USREST::CODE_SUCCESS,
            'message' => 'Success',
            'data' => $o_dp->findAll()
        ]);
    }

    public function actionGetCostLevels() {

        $i_combination_id = isset($_POST['combination_id']) ? $_POST['combination_id'] : null;
        $i_all = isset($_POST['all']) ? $_POST['all'] : 1;
        $a_target_ids = isset($_POST['target_ids']) ? $_POST['target_ids'] : [];
        $a_overwrite_ids = isset($_POST['overwrite_ids']) ? $_POST['overwrite_ids'] : [];

        //Nos aseguramos que sean arrays
        if (!is_array($a_target_ids)) {
            $a_target_ids = [$a_target_ids];
        }
        if (!is_array($a_overwrite_ids)) {
            $a_overwrite_ids = [$a_overwrite_ids];
        }
        $a_data = [];
        //Si está seteada la combinación
        if (isset($i_combination_id) && strlen($i_combination_id) > 0) {
            //Obtemos niveles
            $a_data = $this->_getCombinationLevels($i_combination_id);
        } else {
            //Si se permite mostrar todos
            if ($i_all == 1) {
                //Si está seteado los overwrite
                if (count($a_overwrite_ids) > 0) {
                    $a_data = $this->_getCombinationLevels($a_overwrite_ids[0]);
                } else {
                    $a_data = $this->_getCommmonLevels();
                }
            } else {
                if (count($a_target_ids) > 0) {
                    //Obtemos niveles
                    $a_targets = $this->_getCombinations($a_target_ids);
                    //Si está seteado los overwrite
                    if (count($a_overwrite_ids) > 0) {
                        //Evaluamos si el placeholder está entre los valores permitidos
                        $a_overwrites = $this->_getCombinations($a_overwrite_ids);
                        //Obtenemos los valores por defecto más adecuados
                        $a_data = $this->_overwriteCombinations($a_targets, $a_overwrites);
                    } else {
                        $a_data = $this->_getData($a_targets[0]);
                    }
                } else {
                    throw new Exception('No se han especificado combinaciones como objetivo!');
                }
            }
        }

        echo CJSON::encode([
            'code' => USREST::CODE_SUCCESS,
            'data' => $a_data
        ]);
    }

    public function actionGetCombination() {
        $i_level1_id = isset($_POST['level1_id']) ? $_POST['level1_id'] : null;
        $i_level2_id = isset($_POST['level2_id']) ? $_POST['level2_id'] : null;
        $i_level3_id = isset($_POST['level3_id']) ? $_POST['level3_id'] : null;
        $i_level4_id = isset($_POST['level4_id']) ? $_POST['level4_id'] : null;
        $i_level5_id = isset($_POST['level5_id']) ? $_POST['level5_id'] : null;

        if (isset($i_level1_id) && strlen($i_level1_id) > 0 &&
                isset($i_level2_id) && strlen($i_level2_id) > 0 &&
                isset($i_level3_id) && strlen($i_level3_id) > 0 &&
                isset($i_level4_id) && strlen($i_level4_id) > 0 &&
                isset($i_level5_id) && strlen($i_level5_id) > 0
        ) {
            $o_model = Combination::model()->findByAttributes([
                'level1_id' => $i_level1_id,
                'level2_id' => $i_level2_id,
                'level3_id' => $i_level3_id,
                'level4_id' => $i_level4_id,
                'level5_id' => $i_level5_id,
                    ], [
                'select' => [
                    'combination_id', 'combination_name', 'status'
                ]
            ]);

            //Si hay la combinación se devuelve, sino se crea
            if (isset($o_model)) {

                if ($o_model->status == 1) {
                    echo CJSON::encode([
                        'code' => USREST::CODE_SUCCESS,
                        'data' => [
                            'combination_id' => $o_model->combination_id,
                            'combination_name' => $o_model->combination_name,
                        ]
                    ]);
                } else {
                    echo CJSON::encode([
                        'code' => USREST::CODE_BAD_REQUEST,
                        'message' => 'No se encuentra la combinación o está deshabilitada'
                    ]);
                }
            } else {
                $s_combination_name = Yii::app()->db->createCommand()
                        ->select([
                            "CONCAT_WS(' | ',
                                CLI1.cost_level_item_name,
                                CLI2.cost_level_item_name,
                                CLI3.cost_level_item_name,
                                CLI4.cost_level_item_name,
                                CLI5.cost_level_item_name
                            )"
                        ])
                        ->from('cost_level_item CLI1, cost_level_item CLI2, cost_level_item CLI3, cost_level_item CLI4, cost_level_item CLI5')
                        ->where('CLI1.cost_level_item_id = :level1_id AND CLI2.cost_level_item_id = :level2_id AND CLI3.cost_level_item_id = :level3_id AND CLI4.cost_level_item_id = :level4_id AND CLI5.cost_level_item_id = :level5_id', [
                            ':level1_id' => $i_level1_id,
                            ':level2_id' => $i_level2_id,
                            ':level3_id' => $i_level3_id,
                            ':level4_id' => $i_level4_id,
                            ':level5_id' => $i_level5_id
                        ])
                        ->queryScalar();

                $o_new = new Combination();
                $o_new->level1_id = $i_level1_id;
                $o_new->level2_id = $i_level2_id;
                $o_new->level3_id = $i_level3_id;
                $o_new->level4_id = $i_level4_id;
                $o_new->level5_id = $i_level5_id;
                $o_new->combination_name = $s_combination_name;

                $o_new->insert();

                echo CJSON::encode([
                    'code' => USREST::CODE_SUCCESS,
                    'data' => [
                        'combination_id' => $o_new->combination_id,
                        'combination_name' => $o_new->combination_name,
                    ]
                ]);
            }
        } else {
            echo CJSON::encode([
                'code' => USREST::CODE_BAD_REQUEST,
                'message' => 'Debe especificar todos los niveles'
            ]);
        }
    }

    private function _addCombinations($p_a_array, &$p_a_combination_ids) {
        foreach ($p_a_array as $i_combination_id) {
            $p_a_combination_ids[] = $i_combination_id;
        }
    }

    private function _getCombinations($p_a_combination_ids) {

        if (count($p_a_combination_ids) > 0) {

            $o_criteria = new USCriteria();
            $o_criteria->alias = 'C';
            $o_criteria->addInCondition('C.combination_id', $p_a_combination_ids);

            return Combination::model()->with([
                        'level1' => [
                            'select' => ['L1.cost_level_item_id', 'L1.alias', 'L1.cost_level_item_code', 'L1.cost_level_item_name', 'L1.child_count'],
                            'alias' => 'L1'
                        ],
                        'level2' => [
                            'select' => ['L2.cost_level_item_id', 'L2.alias', 'L2.cost_level_item_code', 'L2.cost_level_item_name', 'L2.child_count'],
                            'alias' => 'L2'
                        ],
                        'level3' => [
                            'select' => ['L3.cost_level_item_id', 'L3.alias', 'L3.cost_level_item_code', 'L3.cost_level_item_name', 'L3.child_count'],
                            'alias' => 'L3'
                        ],
                        'level4' => [
                            'select' => ['L4.cost_level_item_id', 'L4.alias', 'L4.cost_level_item_code', 'L4.cost_level_item_name', 'L4.child_count'],
                            'alias' => 'L4'
                        ],
                        'level5' => [
                            'select' => ['L5.cost_level_item_id', 'L5.alias', 'L5.cost_level_item_code', 'L5.cost_level_item_name', 'L5.child_count'],
                            'alias' => 'L5'
                        ],
                    ])->findAll($o_criteria);
        } else {
            return [];
        }
    }

    private function _getCombinationLevels($p_i_combination_id) {
        $o_model = Combination::model()->with([
                    'level1' => [
                        'select' => ['L1.alias', 'L1.cost_level_item_code', 'L1.cost_level_item_name', 'L1.child_count', 'L1.parent_id'],
                        'alias' => 'L1'
                    ],
                    'level2' => [
                        'select' => ['L2.alias', 'L2.cost_level_item_code', 'L2.cost_level_item_name', 'L2.child_count', 'L2.parent_id'],
                        'alias' => 'L2'
                    ],
                    'level3' => [
                        'select' => ['L3.alias', 'L3.cost_level_item_code', 'L3.cost_level_item_name', 'L3.child_count', 'L3.parent_id'],
                        'alias' => 'L3'
                    ],
                    'level4' => [
                        'select' => ['L4.alias', 'L4.cost_level_item_code', 'L4.cost_level_item_name', 'L4.child_count', 'L4.parent_id'],
                        'alias' => 'L4'
                    ],
                    'level5' => [
                        'select' => ['L5.alias', 'L5.cost_level_item_code', 'L5.cost_level_item_name', 'L5.child_count', 'L5.parent_id'],
                        'alias' => 'L5'
                    ],
                ])->findByAutoIncrement($p_i_combination_id, [
            'alias' => 'C',
            'condition' => 'C.`status`'
        ]);

        if (isset($o_model)) {
            return $this->_getData($o_model);
        } else {
            return $this->_getData();
        }
    }

    private function _getData($p_o_model = null) {

        $o_gv = $this->getOrganization()->globalVar;

        if (isset($p_o_model)) {
            return [
                'level1_id' => $p_o_model->level1_id,
                'level1_name' => $p_o_model->level1->cost_level_item_code . ' - ' . $p_o_model->level1->cost_level_item_name,
                'level1_alias' => $p_o_model->level1->alias,
                'level1_parent_id' => $p_o_model->level1->parent_id,
                'level1_label' => $o_gv->cost_level_name1,
                'level2_id' => $p_o_model->level2_id,
                'level2_name' => $p_o_model->level2->cost_level_item_code . ' - ' . $p_o_model->level2->cost_level_item_name,
                'level2_alias' => $p_o_model->level2->alias,
                'level2_parent_id' => $p_o_model->level2->parent_id,
                'level2_label' => $o_gv->cost_level_name2,
                'level3_id' => $p_o_model->level3_id,
                'level3_name' => $p_o_model->level3->cost_level_item_code . ' - ' . $p_o_model->level3->cost_level_item_name,
                'level3_alias' => $p_o_model->level3->alias,
                'level3_parent_id' => $p_o_model->level3->parent_id,
                'level3_label' => $o_gv->cost_level_name3,
                'level4_id' => $p_o_model->level4_id,
                'level4_name' => $p_o_model->level4->cost_level_item_code . ' - ' . $p_o_model->level4->cost_level_item_name,
                'level4_alias' => $p_o_model->level4->alias,
                'level4_parent_id' => $p_o_model->level4->parent_id,
                'level4_label' => $o_gv->cost_level_name4,
                'level5_id' => $p_o_model->level5_id,
                'level5_name' => $p_o_model->level5->cost_level_item_code . ' - ' . $p_o_model->level5->cost_level_item_name,
                'level5_alias' => $p_o_model->level5->alias,
                'level5_parent_id' => $p_o_model->level5->parent_id,
                'level5_label' => $o_gv->cost_level_name5,
            ];
        } else {
            return [
                'level1_id' => null,
                'level1_name' => null,
                'level1_alias' => null,
                'level1_parent_id' => null,
                'level1_label' => $o_gv->cost_level_name1,
                'level2_id' => null,
                'level2_name' => null,
                'level2_alias' => null,
                'level2_parent_id' => null,
                'level2_label' => $o_gv->cost_level_name2,
                'level3_id' => null,
                'level3_name' => null,
                'level3_alias' => null,
                'level3_parent_id' => null,
                'level3_label' => $o_gv->cost_level_name3,
                'level4_id' => null,
                'level4_name' => null,
                'level4_alias' => null,
                'level4_parent_id' => null,
                'level4_label' => $o_gv->cost_level_name4,
                'level4_id' => null,
                'level5_name' => null,
                'level5_alias' => null,
                'level5_parent_id' => null,
                'level5_label' => $o_gv->cost_level_name5,
            ];
        }
    }

    private function _getCommmonLevels() {

        //$o_gv = $this->getOrganization()->globalVar;
        //Obtenemos valores por defecto
        $a_common = CostLevelItem::model()->findAll([
            'select' => ['cost_level_item_id', 'cost_level_item_name', 'cost_level', 'child_count'],
            'condition' => "alias = '" . CostLevelItem::ALIAS_COMMON . "'"
        ]);

        $a_data = $this->_getData();
        foreach ($a_common as $o_costLevelItem) {
            $a_data["level{$o_costLevelItem->cost_level}_id"] = $o_costLevelItem->cost_level_item_id;
            $a_data["level{$o_costLevelItem->cost_level}_name"] = $o_costLevelItem->cost_level_item_id . ' - ' . $o_costLevelItem->cost_level_item_name;
            //$a_data["level{$o_costLevelItem->cost_level}_label"] = $o_gv->{"cost_level_name{$o_costLevelItem->cost_level}"};
        }

        return $a_data;
    }

    private function _overwriteCombinations($p_a_targets, $p_a_overwrites) {
        $a_data = [];
        //Recorremos todos los targets
        foreach ($p_a_targets as $i => $o_target) {
            //Obtenemos data
            $a_data[$i] = $this->_getData($o_target);
            $a_data[$i]['matchs'] = 0;
            //Recorremos todos los overwrites
            foreach ($p_a_overwrites as $j => $o_overwrite) {
                //Recorremos cada nivel
                for ($k = 1; $k <= 5; $k++) {
                    if ($a_data[$i]["level{$k}_alias"] === CostLevelItem::ALIAS_COMMON || $a_data[$i]["level{$k}_id"] == $o_overwrite->{"level{$k}"}->cost_level_item_id) {
                        $a_data[$i]["level{$k}_id"] = $o_overwrite->{"level{$k}"}->cost_level_item_id;
                        $a_data[$i]["level{$k}_alias"] = $o_overwrite->{"level{$k}"}->alias;
                        $a_data[$i]["level{$k}_name"] = $o_overwrite->{"level{$k}"}->cost_level_item_name;
                        $a_data[$i]['matchs']++;
                    }
                }
            }
        }

        //Ordenamos por match
        USArray::usortString($a_data, 'matchs', USArray::SORT_DIRECTION_DESC);

        unset($a_data[0]['matchs']);
        return $a_data[0];
    }

    public function actionCostProducts($id = null, $modal_id = null, $parent_id = null, $aux_person_id = null) {

        //últimas órdenes de productos.
        $a_product_ids = isset($_GET['product_ids']) ? $_GET['product_ids'] : null;
        $i_warehouse_id = isset($_GET['warehouse_id']) ? ($_GET['warehouse_id'] == '' ? 0 : $_GET['warehouse_id']) : 0;
        $i_include_igv = isset($_GET['include_igv']) ? $_GET['include_igv'] : 0;

        $a_product_ids_presentation = [];
        $a_row = [];
        $a_result = [];
        $a_totals = [];

        if ($a_product_ids != null) {
            foreach ($a_product_ids as $key => $value) {
                $a_product_ids_presentation[] = $value['product_id'] . '-' . number_format($value['equivalence'], 2, '.', ',');
            }

            $a_items = Yii::app()->db->createCommand()->select([
                        "CONCAT(P.product_id, '-', PR.equivalence) as pk",
                        "P.product_id",
                        "P.product_name",
                        "PR.equivalence",
                        "ROUND(IFNULL(S.licost * PR.equivalence * " . ($i_include_igv == 1 ? "(1 + GV.igv)" : "1") . ", 0), 2) as licost", //presentación unitaria * equivalencia
                    ])
                    ->from('product P')
                    ->join('merchandise ME', 'ME.product_id = P.product_id')
                    ->join('presentation PR', 'PR.product_id = P.product_id')
                    ->join('global_var GV', 'GV.organization_id = 1')
                    ->leftJoin('stock S', 'S.stock_id = ME.stock_id')
                    ->where(['in', "CONCAT(P.product_id, '-', PR.equivalence)", $a_product_ids_presentation])
                    ->queryAll();

            $a_costs = USArray::assocBy($a_items, 'pk', true);

            foreach ($a_product_ids as $key => $value) {
                $a_row = [];
                //Esta G no es del tipo de producto GRUPO
                if ($value['type'] == 'G') {
                    $a_item = explode('.', $key);
                    $a_row['pk'] = $key;
                    $a_row['number_item'] = $a_item[0];
                    $a_row['type'] = $value['type'];
                    $a_row['subitem'] = $a_item[1];
                } else {
                    $a_row['pk'] = strval(intval($key));
                    $a_row['number_item'] = $key;
                    $a_row['type'] = '';
                    $a_row['subitem'] = '';
                }

                $s_key = $value['product_id'] . '-' . number_format($value['equivalence'], 2, '.', ',');
                $a_row['product_id'] = $value['product_id'];
                $a_row['product_name'] = $a_costs[$s_key]['product_name'];
                $a_row['equivalence'] = number_format($value['equivalence'], 2, '.', ',');
                $a_row['pres_quantity'] = $value['pres_quantity'];
                //Valores unitarios
                $a_row['unit_price'] = number_format($value['price'], 2, '.', ',');
                $a_row['unit_licost'] = $a_costs[$s_key]['licost'];

                if ($a_row['unit_licost'] != 0) {
                    $a_row['unit_margin'] = number_format((($value['price'] - $a_costs[$s_key]['licost']) / $a_costs[$s_key]['licost']) * 100, 2, '.', ',');
                } else {
                    $a_row['unit_margin'] = '-';
                }
                //Totales de fila
                $a_row['licost'] = $value['pres_quantity'] * $a_costs[$s_key]['licost'];
                $a_row['price'] = $value['pres_quantity'] * $value['price'];

                $a_result[] = $a_row;
            }

            foreach ($a_result as $item) {
                if (count($a_totals) == 0) {
                    $a_totals = [
                        'licost' => $item['licost'],
                        'price' => $item['price']
                    ];
                } else {
                    $a_totals['licost'] += $item['licost'];
                    $a_totals['price'] += $item['price'];
                }
            }

            if ($a_totals['licost'] != 0) {
                $a_totals['margin'] = number_format((($a_totals['price'] - $a_totals['licost']) / $a_totals['licost']) * 100, 2, '.', ',');
            } else {
                $a_totals['margin'] = '-';
            }
            $a_totals['licost'] = number_format($a_totals['licost'], 2, '.', ',');
            $a_totals['price'] = number_format($a_totals['price'], 2, '.', ',');
        }
        $dpCostProducts = new USArrayDataProvider($a_result, array(
            'keyField' => 'pk', // PRIMARY KEY
            'sort' => array(
                'attributes' => array(
                    'pk'
                )
            ),
            'pagination' => false,
            'sort' => []//Al ser procedure, no se permite la ordenación
        ));

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array(
                'parent_id' => $parent_id,
                'modal_id' => $modal_id
            )
                ), true, true);
        $content_html = $this->renderPartial("_costProducts", array(
            'data' => array(
                'costProducts' => $dpCostProducts,
                'totals' => $a_totals,
                'include_igv' => $i_include_igv,
                'modal_id' => $modal_id
            )
                ), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html
        ));
    }

    public function actionProductsAttention($id = null, $modal_id = null, $parent_id = null) {

        //Items
        $itemsDataProvider = (new SpGetProductAttention())->setParams([
                    'xtype' => Movement::OWNER,
                    'xmovement_id' => $id,
                ])->getDataProvider(['item_id']);

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array(
                'parent_id' => $parent_id,
                'modal_id' => $modal_id
            )
                ), true, true);
        $content_html = $this->renderPartial("_productsAttention", array(
            'data' => array(
                'grid_id' => $this->id . "-grid",
                'itemsDataProvider' => $itemsDataProvider,
                'modal_id' => $modal_id
            )
                ), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html
        ));
    }

    public function actionflowAttention($id = null, $modal_id = null, $parent_id = null) {

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array(
                'parent_id' => $parent_id,
                'modal_id' => $modal_id
            )
                ), true, true);

        $content_html = $this->renderPartial("_flowAttention", array(
            'data' => array(
                'modal_id' => $modal_id,
                'modal_html' => $modal_html,
            )
                ), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html
        ));
    }

    public function actionProductAttention($id, $rowid = null) {

        $detailDataProvider = (new SpGetProductAttention())->setParams([
                    'xtype' => Item::OWNER,
                    'xitem_id' => $id,
                ])->getDataProvider(['pk']);

        $this->render('_productAttention', array(
            'data' => array(
                'grid_id' => $this->id . "-grid",
                'detailDataProvider' => $detailDataProvider,
            )
        ));
    }

    public function actionLoadProducts($id = null, $modal_id = null, $parent_id = null, $aux_person_id = null) {

        //últimas órdenes de productos.
        $a_product_ids = isset($_GET['product_ids']) ? $_GET['product_ids'] : null;
        $i_warehouse_id = isset($_GET['warehouse_id']) ? ($_GET['warehouse_id'] == '' ? 0 : $_GET['warehouse_id']) : 0;
        $i_include_igv = isset($_GET['include_igv']) ? $_GET['include_igv'] : 0;

        $a_product_ids_presentation = [];
        $a_row = [];
        $a_result = [];
        $a_totals = [];

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array(
                'parent_id' => $parent_id,
                'modal_id' => $modal_id
            )
                ), true, true);
        $content_html = $this->renderPartial("_load_products", array(
            'data' => array(
                'modal_id' => $modal_id,
                'id' => $id,
            )
                ), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html
        ));
    }

    public function actionPreviewCustomCategory($id, $modal_id = null, $parent_id = null) {

        $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('parent_id' => $parent_id, 'modal_id' => $modal_id)), true, true);

        $o_model = Movement::model()->findByPk($id);

        $this->title = $o_model->getCustomCategoryLabel();

        $content_html = $this->renderPartial('_customer_category_attachment', array(
            'data' => array(
                'model' => $o_model,
                'modal_id' => $modal_id,
            )), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html,
            'toString' => '',
        ));
    }

    public function actionPreviewConfirmationDetail($id, $modal_id = null, $parent_id = null, $state) {

        $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('parent_id' => $parent_id, 'modal_id' => $modal_id)), true, true);
        $o_model = null;

        if ($state == Confirmation::STATE_PENDIENT) {
            $o_ability = Ability::model()->with([
                        'confirmation' => [
                            'with' => [
                                'userRequest' => [
                                    'select' => ['person_id', 'person_name', 'identification_type'],
                                    'alias' => 'UR'
                                ],
                                'userConfirm' => [
                                    'select' => ['person_id', 'person_name', 'identification_type'],
                                    'alias' => 'UC'
                                ],
                                'groupConfirm' => [
                                    'alias' => 'GC',
                                    'select' => ['user_group_id', 'user_group_name'],
                                    'with' => [
                                        'owner' => [
                                            'select' => ['pair_id', 'parent_id', 'type', 'owner', 'owner_id'],
                                            'with' => [
                                                'childrenDefault' => [
                                                    'select' => ['pair_id', 'parent_id', 'type', 'owner', 'owner_id'],
                                                    'with' => [
                                                        'user' =>
                                                        [
                                                            'select' => ['user_id', 'person_id'],
                                                            'with' => [
                                                                'person' => [
                                                                    'select' => ['person_id', 'person_name', 'identification_type'],
                                                                ]
                                                            ]
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ],
                            ]
                        ]
                    ])->findByPk($id, [
                'select' => ['movement_id', 'confirm_id']
            ]);
            $o_model = $o_ability->confirmation;
        } else {
            $o_model = Confirmation::model()->with([
                        'userRequest' => [
                            'select' => ['person_id', 'person_name', 'identification_type'],
                            'alias' => 'UR'
                        ],
                        'userConfirm' => [
                            'select' => ['person_id', 'person_name', 'identification_type'],
                            'alias' => 'UC'
                        ],
                        'groupConfirm' => [
                            'select' => ['user_group_id', 'user_group_name'],
                            'alias' => 'GC'
                        ],
                    ])->find([
                'condition' => "movement_id = :movement_id AND state = :state",
                'params' => [
                    ':movement_id' => $id,
                    ':state' => $state
                ],
                'order' => 'confirm_date DESC',
            ]);
        }
        $this->title = 'Detalles';

        $content_html = $this->renderPartial('_confirmation_detail', array(
            'data' => array(
                'model' => $o_model,
                'modal_id' => $modal_id,
            )), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html,
            'toString' => '',
        ));
    }

    public function actionGetComboItems() {
        try {
            //Valores
            $i_product_id = $_POST['product_id'];
            //
            $a_data = (new DpComboItems())->addEqualCondition('product_id', $i_product_id)->findAll(true);
            //
            echo CJSON::encode($a_data);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function actionExportAccess($key) {

        $s_folder = Yii::app()->params['files_dir'] . DIRECTORY_SEPARATOR . 'access' . DIRECTORY_SEPARATOR;
        $s_filename = $key . '.txt';

        if (Yii::app()->request->isAjaxRequest) {

            $a_array = [];

            if (isset($_POST['Actions'])) {
                foreach ($_POST['Actions'] as $s_module => $a_module) {
                    foreach ($a_module as $s_controller => $a_action) {
                        foreach ($a_action as $s_action => $m_value) {
                            $a_array[] = array(
                                'module' => $s_module,
                                'controller' => $s_controller,
                                'action' => $s_action,
                            );
                        }
                    }
                }
            }

            echo file_put_contents($s_folder . $s_filename, CJSON::encode($a_array));
        } else {

            header("Cache-Control: public");
            header("Content-Description: File Transfer");
            header("Content-Length: " . filesize($s_folder . $s_filename) . ";");
            header("Content-Disposition: attachment; filename={$s_filename}");
            header("Content-Type: application/octet-stream;");
            header("Content-Transfer-Encoding: binary");

            readfile($s_folder . $s_filename);
        }
    }

    public function actionGetAddressList() {

        $i_type = isset($_POST['type']) ? $_POST['type'] : null;
        $a_prerequisites = isset($_POST['prerequisites']) ? $_POST['prerequisites'] : [];
        //
        switch ($i_type) {
            case Locker::OWNER:
                $o_dp = new DpLocker(DpLocker::SCENARIO_NORMAL);
                $o_dp->addMathCondition('box_count', 0, '>');
                $a_items = $o_dp->findAll();
                break;
            case Person::OWNER:
                if (!isset($a_prerequisites)) {
                    echo CJSON::encode([
                        'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                        'message' => 'No se ha especificado un ID de socio de negocio.'
                    ]);
                    return;
                }
                $o_dp = new DpAddress(DpAddress::SCENARIO_PERSON_ADDRESS);
                foreach ($a_prerequisites as $s_attribute => $s_value) {
                    $o_dp->addEqualCondition($s_attribute, $s_value);
                }
                $a_items = $o_dp->findAll();
                break;
            default:
                throw new Exception('Tipo inválido');
        }

        echo CJSON::encode([
            'code' => USREST::CODE_SUCCESS,
            'data' => [
                'items' => $a_items
            ]
        ]);
    }

    public function actionGetLockerBoxes() {
        $i_locker_id = $_POST['locker_id'];

        $o_locker = Locker::model()->with([
                    'lockerBoxes' => [
                        'select' => [
                            'locker_box_id',
                            'box_number',
                            'available'
                        ],
                        'alias' => 'LB',
                        'on' => 'LB.available'
                    ],
                    'address.geoloc'
                ])->findByAutoIncrement($i_locker_id, [
            'select' => [
                'locker_name'
            ],
            'alias' => 'L',
            'order' => 'LB.box_number'
        ]);

        $a_data = [
            'locker_id' => $i_locker_id,
            'locker_name' => $o_locker->locker_name,
            'address' => $o_locker->address->address,
            'dept_code' => $o_locker->address->dept_code,
            'prov_code' => $o_locker->address->prov_code,
            'dist_code' => $o_locker->address->dist_code,
            'dept_name' => $o_locker->address->geoloc->dept_name,
            'prov_name' => $o_locker->address->geoloc->prov_name,
            'dist_name' => $o_locker->address->geoloc->dist_name,
        ];

        foreach ($o_locker->lockerBoxes as $o_lockerBox) {
            $a_data['boxes'][] = [
                'locker_box_id' => $o_lockerBox->locker_box_id,
                'box_number' => $o_lockerBox->box_number,
            ];
        }

        echo CJSON::encode([
            'code' => USREST::CODE_SUCCESS,
            'data' => $a_data
        ]);
    }

    public function actionGetContactForLockerRequest($id, $type, $field, $query) {

        if (!USString::isBlank($id)) {
            $s_column = '';
            switch ($field) {
                case 'firstname':
                case 'lastname':
                case 'dni':
                case 'email_address':
                    $s_column = $field;
                    break;
                default:
                    throw new Exception('Campo no válido');
            }

            $a_contacts = Yii::app()->db->createCommand()
                    ->select([
                        "C.firstname",
                        "C.lastname",
                        "C.dni",
                        "C.type",
                        "C.email_address"
                    ])
                    ->from('contact C')
                    ->where("C.person_id = :person_id AND C.type = :type AND C.{$s_column} LIKE :query", [
                        ':person_id' => $id,
                        ':type' => $type,
                        ':query' => '%' . str_replace(' ', '%', $query) . '%'
                    ])
                    ->limit(10)
                    ->queryAll();
        } else {
            $a_contacts = [];
        }

        echo CJSON::encode([
            'code' => USREST::CODE_SUCCESS,
            'data' => [
                'query' => $query,
                'contacts' => $a_contacts
            ]
        ]);
    }

    public function actionGetPromotionGifts() {

        $i_promotion_item_id = isset($_POST['promotion_item_id']) ? $_POST['promotion_item_id'] : null;
        $s_currency = isset($_POST['currency']) ? $_POST['currency'] : null;
        $i_store_id = isset($_POST['store_id']) ? $_POST['store_id'] : null;
        $s_emission_date = isset($_POST['emission_date']) ? $_POST['emission_date'] : null;
        $i_exclude_id = isset($_POST['exclude_id']) ? $_POST['exclude_id'] : null;
        //
        //Si es null
        if (!isset($i_promotion_item_id)) {
            echo CJSON::encode([
                'code' => USREST::CODE_BAD_REQUEST,
                'message' => 'Debe especificar el ID del ítem de promoción',
            ]);
            return;
        }
        //
        $o_promotionItem = PromotionItem::model()->with([
                    'promotion' => [
                        'select' => [
                            'price_currency',
                        ],
                        'alias' => 'P'
                    ],
                    'presentation' => [
                        'select' => [
                            'product_id',
                            'equivalence',
                        ],
                        'alias' => 'PPR',
                        'with' => [
                            'product' => [
                                'select' => [
                                    'product_name'
                                ],
                                'alias' => 'PP'
                            ]
                        ]
                    ],
                ])->findByAutoIncrement($i_promotion_item_id, [
            'select' => [
                'discount_factor',
                'pprice_pen',
                'pprice_usd'
            ],
            'alias' => 'PI'
        ]);
        //
        if (!isset($o_promotionItem)) {
            echo CJSON::encode([
                'code' => USREST::CODE_NOT_FOUND,
                'message' => 'No se encontró la promoción solicitada.',
            ]);
            return;
        }
        //
        $a_raw = (new SpGetPromotionGiftOptions())->setParams([
                    'mode' => SpGetPromotionGiftOptions::MODE_PROMOTION_ITEM,
                    'id' => $i_promotion_item_id,
                    'currency' => $o_promotionItem->promotion->price_currency,
                    'store_id' => $i_store_id,
                    'date' => isset($s_emission_date) ? SIANTime::formatDate($s_emission_date, true) : null,
                    'exclude_id' => $i_exclude_id,
                ])->findAll();
        //
        $a_gifts = USArray::assocBy($a_raw, 'gift_number');

        $a_data = [];
        foreach ($a_gifts as $i_gift_number => $a_options) {
            $a_gift = [
                'gift_number' => $i_gift_number,
                'options' => []
            ];

            foreach ($a_options as $o_option) {
                $a_option = [
                    'promotion_gift_option_id' => $o_option->promotion_gift_option_id,
                    'product_id' => $o_option->product_id,
                    'product_name' => $o_option->product_name,
                    'product_type' => $o_option->product_type,
                    'equivalence' => $o_option->equivalence,
                    'allow_decimals' => $o_option->allow_decimals,
                    'measure_name' => $o_option->measure_name,
                    'carry' => (double) $o_option->carry,
                    'pprice' => (double) $o_option->pprice,
                    'ipprice' => (double) $o_option->ipprice,
                    'pres_stock' => (double) $o_option->pres_stock,
                ];

                $a_gift['options'][] = $a_option;
            }

            $a_data[] = $a_gift;
        }

        //
        echo CJSON::encode([
            'code' => USREST::CODE_SUCCESS,
            'message' => Strings::SUCCESS_OPERATION,
            'data' => [
                'product_name' => $o_promotionItem->presentation->product->product_name,
                'currency' => $o_promotionItem->promotion->price_currency,
                'gifts' => $a_data
            ]
        ]);
    }

    public function actionGetPromotionGiftOptions() {

        $a_option_ids = $_POST['option_ids'];
        $s_currency = $_POST['currency'];
        $i_store_id = $_POST['store_id'];
        $s_emission_date = $_POST['emission_date'];
        //Valores
        $i_stock_mode = isset($_POST['stock_mode']) ? $_POST['stock_mode'] : null;
        $i_exclude_id = isset($_POST['exclude_id']) ? $_POST['exclude_id'] : null;
        //
        $a_options = SpGetPromotionGiftOptions::getAssociative(SpGetPromotionGiftOptions::MODE_PROMOTION_GIFT_OPTION_WIDGET, $a_option_ids, $s_currency, $i_store_id, $s_emission_date, $i_exclude_id);
        //Obtenemos ID's de productos
        $a_stocks = null;
        if (isset($i_stock_mode)) {
            $a_pks = [];
            foreach ($a_options as $a_option) {
                foreach ($a_option as $a_presentation) {
                    $a_pks[] = $a_presentation['product_id'] . "-" . number_format($a_presentation['equivalence'], 2);
                }
            }
            //Obtenemos stocks
            $a_stocks = DpAllProductPresentations::getAssocStocks(DpAllProductPresentations::SCENARIO_WITH_STOCK, $i_stock_mode, $a_pks, $s_emission_date, $i_exclude_id);
        }
        //Combinamos
        $a_data = [];
        foreach ($a_options as $i_promotion_gift_option_id => $a_aux) {
            //
            foreach ($a_aux as $s_equivalence => $a_option) {
                //Armamos
                $a_data[$i_promotion_gift_option_id] = [
                    'promotion_gift_option_id' => $i_promotion_gift_option_id,
                    'product_id' => $a_option['product_id'],
                    'product_name' => $a_option['product_name'],
                    'product_type' => $a_option['product_type'],
                    'item_type_id' => $a_option['item_type_id'],
                    'carry' => $a_option['carry'],
                    'equivalence' => $s_equivalence,
                    //Generamos un array de presentaciones artificialmente
                    'presentationItems' => $a_aux,
                    'igv_affection' => $a_option['igv_affection'],
                    'perception_affected' => $a_option['perception_affected'],
                    'allow_decimals' => $a_option['allow_decimals'],
                    'expiration' => $a_option['expiration'],
                ];

                //Obtenemos stock
                if (isset($a_stocks)) {
                    $a_stock = $a_stocks[$a_option['product_id'] . "-" . $s_equivalence];
                    //Seteamos stock
                    $a_data[$i_promotion_gift_option_id]['unit_stock'] = $a_stock['unit_stock'];
                    $a_data[$i_promotion_gift_option_id]['mixed_stock'] = $a_stock['mixed_stock'];
                }
            }
        }

        echo CJSON::encode([
            'code' => USREST::CODE_SUCCESS,
            'message' => Strings::SUCCESS_OPERATION,
            'data' => [
                'options' => $a_data
            ]
        ]);
    }

    public function actionGetOperations() {
        $s_route = $_POST['route'];

        if ($s_route != "") {
            try {

                $o_model = (new DpScenarioOperation())
                        ->setAttributes([
                    'route' => $s_route,
                ]);

                $a_operations = $o_model->getListData('operation_id', 'operation_name');

                echo CJSON::encode([
                    'code' => USREST::CODE_SUCCESS,
                    'operations' => $a_operations,
                ]);
            } catch (Exception $ex) {
                throw $ex;
            }
        }
    }

    public function actionGetDocuments() {
        $s_route = $_POST['route'];

        try {
            $o_model = (new DpScenarioDocument())
                    ->setAttributes([
                'route' => $s_route
            ]);

            $a_documents = $o_model->getListData('document_id', 'document_name');

            echo CJSON::encode([
                'code' => USREST::CODE_SUCCESS,
                'documents' => $a_documents,
            ]);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function actionGetBusinessUnit() {
        $i_warehouse_id = $_POST['warehouse_id'];

        try {
            $a_business_units = (new DpBusinessUnit(DpBusinessUnit::SCENARIO_WAREHOUSE))
                            ->setAttributes([
                                'warehouse_id' => $i_warehouse_id
                            ])->getListData('business_unit_id', 'business_unit_name');

            if (count($a_business_units) == 0) {
                $a_business_units = (new DpBusinessUnit(DpBusinessUnit::SCENARIO_WAREHOUSE_WITHOUT_STORE))->getListData('business_unit_id', 'business_unit_name');
            }

            echo CJSON::encode([
                'code' => USREST::CODE_SUCCESS,
                'businessunits' => $a_business_units,
            ]);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function actionDefineMassivePrices() {
        $data = $_POST['data'];
        $attribute = $_POST['attribute'];
        $attribute_name = $_POST['attribute_name'];
        $imprice_label = 'P.Min. (Inc. IGV)';
        $iaprice_label = Yii::app()->controller->getOrganization()->globalVar->aprice_label . ' (Inc. IGV)';
        $iwprice_label = Yii::app()->controller->getOrganization()->globalVar->wprice_label . ' (Inc. IGV)';

        $updated_prices = 0;

        foreach ($data as $row) {
            $presentations = [];
            $dp = new DpAllProductToFile;

            $dp->addInCondition($attribute, [$row[$attribute_name]]);
            $presentations = $dp->findAll();

            if (count($presentations) > 0) {
                foreach ($presentations as $presentation) {
                    $sp = new SpDefinePrices();
                    $params = array(
                        'xproduct_id' => $presentation['product_id'],
                        'xequivalence' => $presentation['equivalence'],
                        'ximprice' => isset($row[$imprice_label]) ? $row[$imprice_label] : 0,
                        'xiaprice' => isset($row[$iaprice_label]) ? $row[$iaprice_label] : 0,
                        'xiwprice' => isset($row[$iwprice_label]) ? $row[$iwprice_label] : 0,
                    );

                    $sp->setParams($params);
                    $result = $sp->execute();

                    $updated_prices++;
                }
            }
        }

        $o_response = [
            'message' => "Se actualizó el precio de " . $updated_prices . ' productos, de ' . count($data) . ' registros',
        ];

        echo CJSON::encode($o_response);
    }

    private function processQuantityAndSizes($saved_product, $a_sizes_quantity, $row, $index, $a_stores_items, $i_merchandise_type_id, $custom1_label, $custom2_label) {
        if ($a_sizes_quantity && count($a_sizes_quantity) > 0) {
            foreach ($a_sizes_quantity as $size_quantity) {
                $index++;
                $barcode = $row['Modelo'] . "-" . $row[$custom2_label];
                $merchandise_related = null;
                $merchandise_related = Merchandise::model()->with(array(
                            'product' => array(
                                'with' => array(
                                    'presentations' => array(
                                        'together' => true,
                                        'condition' => 'presentations.barcode=:barcode',
                                        'params' => array(':barcode' => $barcode . " T-" . $size_quantity['size']),
                                    ),
                                ),
                                'together' => true,
                            ),
                        ))->find();
                if (!$merchandise_related) {
                    $merchandise_related = $this->_generateProductRelated($saved_product, $a_stores_items, $i_merchandise_type_id, $size_quantity['size'], $row[$custom1_label], $barcode);
                    if ($merchandise_related->savingPresentations()->savingPrices()->validate()) {
                        try {
                            $merchandise_related->insert();
                            $product_link = new ProductLink();
                            $product_link->product_parent_id = $saved_product->product_id;
                            $product_link->product_id = $merchandise_related->product_id;
                            $product_link->quantity = $size_quantity['quantity'];
                            $product_link->insert();
                        } catch (Exception $ex) {
                            throw $ex;
                        }
                    }
                } else {
                    $product_link = ProductLink::findByParentAndChild($saved_product->product_id, $merchandise_related->product_id);
                    if (!$product_link) {
                        try {
                            $product_link = new ProductLink();
                            $product_link->product_parent_id = $saved_product->product_id;
                            $product_link->product_id = $merchandise_related->product_id;
                            $product_link->quantity = $size_quantity['quantity'];
                            $product_link->insert();
                        } catch (Exception $ex) {
                            throw $ex;
                        }
                    }
                }
            }
        }
    }

    /**
     * Funcion para procesar los datos desde jsoon y generar nuevos datos para insertar
     */
    private function processSizeQuantity($p_s_size_quantity, &$a_errors, $item_number, $product_name) {

        if ($p_s_size_quantity) {
            $size_quantity = [];
            $explode_size_quantity_string = explode('/', $p_s_size_quantity);

            $sizes_string = $explode_size_quantity_string[0];
            $explode_sizes = explode(',', $sizes_string);
            $sizes = [];

            foreach ($explode_sizes as $size) {
                if (strpos($size, '-') !== false) {
                    $sizes_explode = explode('-', $size);
                    $inicial_size = floatval($sizes_explode[0]);
                    $end_size = floatval($sizes_explode[1]);
                    for ($i = $inicial_size; $i <= $end_size; $i++) {
                        $sizes[] = $i;
                    }
                } else {
                    $sizes[] = floatval($size);
                }
            }
            $quantity_string = $explode_size_quantity_string[1];
            $explode_quantity = explode(',', $quantity_string);

            if (count($explode_quantity) == count($sizes)) {

                for ($i = 0; $i < count($sizes); $i++) {
                    $size_quantity[] = [
                        'size' => $sizes[$i],
                        'quantity' => floatval($explode_quantity[$i]),
                    ];
                }

                return $size_quantity;
            } else if (count($explode_quantity) < count($sizes)) {
                $a_errors[] = "El item N°{$item_number} {$product_name}, no se han propocionado las cantidades suficientes para la cantidad de pares";
                return null;
            } else if (count($explode_quantity) > count($sizes)) {
                $a_errors[] = "El item N°{$item_number} {$product_name}, se han propocionado más cantidades que pares";
                return null;
            }
        } else {
            $a_errors[] = "El item N°{$item_number} {$product_name}, no se han proporcionado tallas/cantidades.";
        }

        return null;
    }

    private function _generateProductRelated($p_o_parent, $p_a_stores, $p_sample_type_id, $p_s_size, $custom1_label, $barcode) {

        $o_product_parent = $p_o_parent->product->getAttributes();
        $o_merchandise_parent = $p_o_parent->getAttributes();
        $a_presentation_parent = $p_o_parent->product->unitPresentation->getAttributes();

        // Merchandise
        $o_merchandise = new Merchandise(Merchandise::SCENARIO_LOGISTIC);
        foreach ($o_merchandise_parent as $k_attribute_merchandise => $value) {
            $o_merchandise->{$k_attribute_merchandise} = $value;
        }

        $o_merchandise->use_batch = 0;
        //Se añade la talla
        $o_merchandise->size = $p_s_size;
        // Product
        foreach ($o_product_parent as $k_attribute => $value) {
            if ($k_attribute !== 'product_id') {
                $o_merchandise->product->{$k_attribute} = $value;
            }
        }
        $o_merchandise->product->scenario = Merchandise::SCENARIO_LOGISTIC;

        // Construimos product_name
        $product_name = "";

        if (isset($custom1_label) && $custom1_label != "") {
            $product_name = $custom1_label . " ";
        }

        $product_name = $product_name . $barcode . " T-" . $p_s_size;

        $o_merchandise->product->product_name = $product_name;
        $o_merchandise->product->product_type = Product::TYPE_MERCHANDISE;
        $o_merchandise->product->item_type_id = $p_sample_type_id;

        // Presentation
        $a_par_merchandise_measure = (new DpMultitable(Multitable::MEASURE_UNIT))->addEqualCondition('value', Presentation::PRESENTATION_VALUE_PAR)->find();
        $a_presentation_parent['measure_id'] = $a_par_merchandise_measure->multi_id;
        $a_presentation_parent['measure_name'] = $a_par_merchandise_measure->abbreviation;
        $a_presentation_parent['abbreviation'] = $a_par_merchandise_measure->abbreviation;
        $a_presentation_parent['barcode'] = $barcode . " T-" . $p_s_size;
        $o_merchandise->product->addPresentation($a_presentation_parent);

        // Prices
        $a_attributes_price = [];
        $a_attributes_price_presentation = [];

        foreach ($o_merchandise->product->tempPresentations as $o_presentation) {
            $a_attributes_price_presentation[] = [
                'equivalence' => $o_presentation['equivalence'],
                'measure_id' => $o_presentation['measure_id'],
                'currency' => $o_presentation['currency'],
                'custom_measure_id' => $o_presentation['custom_measure_id'],
                'mprice' => 0,
                'aprice' => 0,
                'wprice' => 0,
                'imprice' => 0,
                'iaprice' => 0,
                'iwprice' => 0,
            ];
        }
        $a_attributes_price['stores'] = $p_a_stores;
        $a_attributes_price['pricePresentations'] = $a_attributes_price_presentation;
        $o_merchandise->product->addPrice($a_attributes_price);

        return $o_merchandise;
    }

}
