<?php

class SIANCommercialAmounts extends CWidget {

    public $id;
    public $form;
    public $model;
    public $as_bill_checkbox_id;
    public $include_igv_id;
    public $retention_checkbox_id;
    public $detraction_checkbox_id;
    public $fixed = 2;
    public $step = 0.01;
    public $max_iterations = 1000;
    public $as_leasing = false;
    public $onChangeCCDependence = ''; //Para hacer un puente
    public $readonly_igv = true;
    public $detraction_items = [];
    //PRIVATE
    private $controller;
    private $parent_currency_input_id;
    private $max_total_pen_field_id;
    private $max_total_usd_field_id;
    private $crude_field_id;
    private $affected_field_id;
    //LEASING
    private $amortization_field_id;
    private $interest_field_id;
    private $desgravamen_field_id;
    private $oae_adm_field_id;
    private $oae_financial_field_id;
    private $oae_sales_field_id;
    private $porte_field_id;
    private $itf_field_id;
    private $other_expenses_field_id;
    //
    private $inaffected_field_id;
    private $nobill_field_id;
    private $net_field_id;
    private $igv_percent_field_id;
    private $igv_field_id;
    private $total_field_id;
    private $perception_field_id;
    private $real_field_id;
    private $detraction_code_id;
    private $retention_percent_field_id;
    private $detraction_percent_field_id;
    private $ret_field_id;
    private $det_field_id;
    private $no_ret_field_id;
    private $warehouse_input_id;
    private $currency_symbol;
    private $isCreditNote = false;
    private $max_adjust_index = 3;
    private $div_retention_id;
    private $div_detraction_id;
    private $div_no_ret_pen_id;

    public function init() {

        $this->controller = Yii::app()->controller;
        //GRID ID
        $this->id = isset($this->id) ? $this->id : Yii::app()->controller->getServerId();
        $this->parent_currency_input_id = Yii::app()->controller->getServerId();
        $this->max_total_pen_field_id = Yii::app()->controller->getServerId();
        $this->max_total_usd_field_id = Yii::app()->controller->getServerId();
        $this->crude_field_id = Yii::app()->controller->getServerId();
        $this->affected_field_id = Yii::app()->controller->getServerId();
        $this->amortization_field_id = Yii::app()->controller->getServerId();
        $this->interest_field_id = Yii::app()->controller->getServerId();
        $this->desgravamen_field_id = Yii::app()->controller->getServerId();
        $this->oae_adm_field_id = Yii::app()->controller->getServerId();
        $this->oae_financial_field_id = Yii::app()->controller->getServerId();
        $this->oae_sales_field_id = Yii::app()->controller->getServerId();
        $this->porte_field_id = Yii::app()->controller->getServerId();
        $this->itf_field_id = Yii::app()->controller->getServerId();
        $this->other_expenses_field_id = Yii::app()->controller->getServerId();
        $this->inaffected_field_id = Yii::app()->controller->getServerId();
        $this->nobill_field_id = Yii::app()->controller->getServerId();
        $this->net_field_id = Yii::app()->controller->getServerId();
        $this->igv_percent_field_id = Yii::app()->controller->getServerId();
        $this->igv_field_id = Yii::app()->controller->getServerId();
        $this->total_field_id = Yii::app()->controller->getServerId();
        $this->perception_field_id = Yii::app()->controller->getServerId();
        $this->real_field_id = Yii::app()->controller->getServerId();
        $this->detraction_code_id = Yii::app()->controller->getServerId();
        $this->retention_percent_field_id = Yii::app()->controller->getServerId();
        $this->detraction_percent_field_id = Yii::app()->controller->getServerId();
        $this->ret_field_id = Yii::app()->controller->getServerId();
        $this->det_field_id = Yii::app()->controller->getServerId();
        $this->no_ret_field_id = Yii::app()->controller->getServerId();
        $this->as_bill_checkbox_id = Yii::app()->controller->getServerId();
        $this->include_igv_id = Yii::app()->controller->getServerId();
        $this->retention_checkbox_id = Yii::app()->controller->getServerId();
        $this->detraction_checkbox_id = Yii::app()->controller->getServerId();
        $this->warehouse_input_id = $this->controller->getServerId();
        $this->div_retention_id = Yii::app()->controller->getServerId();
        $this->div_detraction_id = Yii::app()->controller->getServerId();
        $this->div_no_ret_pen_id = Yii::app()->controller->getServerId();
        //VAR
        $this->currency_symbol = $this->model->movement->getCurrencySymbol();
        $this->isCreditNote = in_array($this->model->movement->route, $this->controller->getCreditNoteRoutes());
        //VAR ID MAP
        $a_id_map = [
            CommercialMovementProduct::IGV_AFFECTION_AFFECTED => [
                $this->affected_field_id,
                $this->amortization_field_id,
                $this->interest_field_id,
                $this->desgravamen_field_id,
                $this->oae_adm_field_id,
                $this->oae_financial_field_id,
                $this->oae_sales_field_id,
                $this->porte_field_id,
                $this->itf_field_id,
                $this->other_expenses_field_id],
            CommercialMovementProduct::IGV_AFFECTION_INAFFECTED => [$this->inaffected_field_id],
            CommercialMovementProduct::IGV_AFFECTION_EXONERATED => [$this->nobill_field_id],
        ];
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-commercial-amounts.js');
        //SCRIPTS
        Yii::app()->clientScript->registerScript(Yii::app()->controller->getServerId(), "

        var divObj = $('#{$this->id}');
        divObj.data('scope', '{$this->model->movement->scope}');
        divObj.data('is_buy', " . CJSON::encode($this->model->isBuy()) . ");
        divObj.data('is_sale', " . CJSON::encode($this->model->isSale()) . ");
        divObj.data('is_commercial_route', " . CJSON::encode($this->model->movement->scenario->isCommercialRoute()) . ");            
        divObj.data('as_leasing', " . CJSON::encode($this->as_leasing) . ");
        divObj.data('global_igv', {$this->controller->getOrganization()->globalVar->igv});
        divObj.data('igv_list', " . CJSON::encode(GlobalVar::getIgvList()) . ");
        divObj.data('igv_percent', " . CJSON::encode($this->model->igv_percent) . ");
        divObj.data('max_iterations', {$this->max_iterations});
        divObj.data('max_adjust_index', {$this->max_adjust_index});
        divObj.data('fixed', {$this->fixed});
        divObj.data('step', {$this->step});
        divObj.data('independent_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INDEPENDENT_WITHOUT_ITEMS . "');
        divObj.data('inherit_mode', '" . ($this->model->movement->route === 'logistic/operatingCost' ? SpGetNetWildcardData::MODE_WILDCARD_APPORTIONMENT : SpGetNetWildcardData::MODE_WILDCARD_INHERIT_WITHOUT_ITEMS) . "');
        divObj.data('igv_affection_map', " . CJSON::encode(CommercialMovementProduct::getIGVAffectionMap()) . ");
        divObj.data('id_map', " . CJSON::encode($a_id_map) . ");
        divObj.data('modifiers', " . CJSON::encode(Document::getModifiers()) . ");
        // Detracción y Retención
        divObj.data('retention4', {$this->controller->getOrganization()->globalVar->retention4});
        divObj.data('retention_input_id', '{$this->retention_checkbox_id}');
        divObj.data('retention_allowed_document', 0);
        divObj.data('amount_allow_retention', 0);
        divObj.data('no_retention_max_amount_pen', {$this->controller->getOrganization()->globalVar->no_retention_max_amount_pen});
        divObj.data('no_retention_max_amount_usd', {$this->controller->getOrganization()->globalVar->no_retention_max_amount_usd});
        divObj.data('no_detraction_max_amount_pen', {$this->controller->getOrganization()->globalVar->no_detraction_max_amount_pen});
        divObj.data('no_detraction_max_amount_usd', {$this->controller->getOrganization()->globalVar->no_detraction_max_amount_usd});
        divObj.data('amount_allow_detraction', 0);
        divObj.data('detraction_allowed_document', 0);
        divObj.data('detraction_input_id', '{$this->detraction_checkbox_id}');
        divObj.data('detraction_global', '{$this->controller->getOrganization()->globalVar->detraction}');
        divObj.data('detraction_items', " . CJSON::encode($this->detraction_items) . ");
        divObj.data('detraction_code', " . CJSON::encode($this->model->detraction_code) . ");
        divObj.data('detraction_value', " . CJSON::encode($this->model->detraction_percent) . ");
            
        $(document).ready(function() {
            SIANCommercialAmountsFillIgv('{$this->igv_percent_field_id}', '{$this->id}');
            SIANCommercialAmountsCalculateTotals('{$this->id}', true);
            
            //CURRENCY SYMBOL
            $('span.currency-symbol').text('" . Currency::getSymbol($this->model->movement->currency) . "');
        });
        
        //SI CAMBIA LA MONEDA
        $('#{$this->id}').data('changeCurrency', function(currency){

            var exchange_rate = $('#{$this->id}').data('exchange_rate');

            var affected1Input = $('#{$this->affected_field_id}');
            var affected2Input = $('#{$this->interest_field_id}');
            var affected3Input = $('#{$this->porte_field_id}');
            var inaffectedInput = $('#{$this->inaffected_field_id}');
            var nobillInput = $('#{$this->nobill_field_id}');
            var igvInput = $('#{$this->igv_field_id}');
            var perceptionInput = $('#{$this->perception_field_id}');

            switch(currency)
            {
                case '" . Currency::PEN . "':
                    affected1Input.toPen(exchange_rate, {$this->fixed});
                    affected2Input.toPen(exchange_rate, {$this->fixed});
                    affected3Input.toPen(exchange_rate, {$this->fixed});
                    inaffectedInput.toPen(exchange_rate, {$this->fixed});
                    nobillInput.toPen(exchange_rate, {$this->fixed});
                    igvInput.toPen(exchange_rate, {$this->fixed});
                    perceptionInput.toPen(exchange_rate, {$this->fixed});
                break;
                case '" . Currency::USD . "':
                    affected1Input.toUsd(exchange_rate, {$this->fixed});
                    affected2Input.toUsd(exchange_rate, {$this->fixed});
                    affected3Input.toUsd(exchange_rate, {$this->fixed});
                    inaffectedInput.toUsd(exchange_rate, {$this->fixed});
                    nobillInput.toUsd(exchange_rate, {$this->fixed});
                    igvInput.toUsd(exchange_rate, {$this->fixed});
                    perceptionInput.toUsd(exchange_rate, {$this->fixed});
                break;
                default:
                    bootbox.alert(us_message('Moneda Inválida', 'error'));
                break;
            }

            SIANCommercialAmountsCalculateTotals('{$this->id}', true);

            $('#{$this->id}').data('currency', currency);
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            
            //Actualizamos montos
            SIANCommercialAmountsUpdate('{$this->id}');
        });  
        
        $('#{$this->id}').data('changeOperation', function(changeData){

            var divObj = $('#{$this->id}');

            //GUARDAMOS INFO DE OPERACIÓN
            divObj.data('operationChangeData', changeData);

            //Actualizamos montos
            SIANCommercialAmountsUpdate('{$this->id}');
        });  
        
        $('#{$this->id}').data('changeDocument', function(changeData){            
            var divObj = $('#{$this->id}');
            var scope = divObj.data('scope');
            var is_commercial_route = divObj.data('is_commercial_route');
            var modifiers = divObj.data('modifiers');            
            " .
                ($this->model->isBuy() ? "
            //Retención: Desactivamos y seteamos data de sunat
            $('#{$this->retention_checkbox_id}').data('sunat_code', changeData.sunat_code);
            //Comparamos
            if(scope == '" . Scenario::SCOPE_NOT_DOMICILED . "' || changeData.sunat_code == '" . Document::HONORARIOS . "')
            {
                $('#{$this->id}').data('retention_allowed_document', 1);
                SIANCommercialAmountsEnableOrDisabledRetentionDetraction('{$this->id}');
            }else{
                $('#{$this->id}').data('retention_allowed_document', 0);
                $('#{$this->retention_checkbox_id}').prop('checked', false);
                SIANCommercialAmountsEnableOrDisabledRetentionDetraction('{$this->id}');
            }
            //Detracción: Desactivamos y seteamos data de sunat
            $('#{$this->detraction_checkbox_id}').data('sunat_code', changeData.sunat_code);
            if(changeData.sunat_code == '" . Document::FACTURA . "')
            {
                $('#{$this->id}').data('detraction_allowed_document', 1);
                SIANCommercialAmountsEnableOrDisabledRetentionDetraction('{$this->id}');
            } 
            else 
            {
                $('#{$this->id}').data('detraction_allowed_document', 0);
                $('#{$this->detraction_checkbox_id}').prop('checked', false);
                SIANCommercialAmountsEnableOrDisabledRetentionDetraction('{$this->id}');
            } 
            " : "") .
                ($this->as_leasing ? "" : "            
            //Verificamos si está deshabilitado
            var as_bill_readonly = is_commercial_route || jQuery.inArray(changeData.sunat_code, ['" . Document::TICKET . "']) == -1;

            $('#{$this->as_bill_checkbox_id}').attr('readonly', as_bill_readonly);

            if(as_bill_readonly){

                if(!jQuery.inArray(changeData.sunat_code, modifiers))
                {
                    $('#{$this->as_bill_checkbox_id}').prop('checked', is_commercial_route || jQuery.inArray(changeData.sunat_code, ['" . Document::BOLETA . "', '" . Document::BOLETO . "', '" . Document::HONORARIOS . "']) == -1).change();            
                }
            } else {
                $.notify('Se activó la casilla \'Crédito fiscal\'.', {
                    className: 'info',
                    showDuration: 30,
                    hideDuration: 50,
                    autoHideDelay: 1000,
                }); 
            }
            " ) . "
        });
        
        $('#{$this->id}').data('changeExchange', function(exchange_rate){
            
            //Seteamos TC
            $('#{$this->id}').data('exchange_rate', exchange_rate);
                
            var maxTotalPenInput = $('#{$this->max_total_pen_field_id}');
            var maxTotalUsdInput = $('#{$this->max_total_usd_field_id}');
            var totalInput = $('#{$this->total_field_id}');
            var currency = $('#{$this->id}').data('currency');

            var parent_currency = $('#{$this->parent_currency_input_id}').val();

            if(parent_currency.length > 0)
            {
                switch(parent_currency)
                {
                    case '" . Currency::PEN . "':
                        maxTotalUsdInput.floatVal({$this->fixed}, maxTotalPenInput.floatVal({$this->fixed}) / exchange_rate);
                    break;
                    case '" . Currency::USD . "':
                        maxTotalPenInput.floatVal({$this->fixed}, maxTotalUsdInput.floatVal({$this->fixed}) * exchange_rate);
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida', 'error'));
                    break;
                }
            }

            //Actualizamos el max
            switch(currency)
            {
                case '" . Currency::PEN . "':
                    totalInput.floatAttr('max', {$this->fixed}, maxTotalPenInput.floatVal({$this->fixed}));
                break;
                case '" . Currency::USD . "':
                    totalInput.floatAttr('max', {$this->fixed}, maxTotalUsdInput.floatVal({$this->fixed}));
                break;
                default:
                    bootbox.alert(us_message('Moneda Inválida', 'error'));
                break;
            }
            
            //Actualizamos montos
            SIANCommercialAmountsUpdate('{$this->id}');
        });
        
        $('#{$this->id}').data('changeCCDependence', function(operationChangeData){
            {$this->onChangeCCDependence};
        });
        
        $('#{$this->id}').data('changeWarehouse', function(changeData){
            var grid = $('#{$this->id}');
            //Seteamos
            $('#{$this->warehouse_input_id}').val(changeData.warehouse_id);
            //
            if(changeData.warehouse_id !== 'undefined' && changeData.warehouse_id.length > 0)
            {
                SIANCommercialAmountsUpdate('{$this->id}');
            }            
        });
        
        function SIANCommercialAmountsActivateRetention() {
            
            var divObj = $('#{$this->id}');
            var is_buy = divObj.data('is_buy');            
            var retention_global = divObj.data('retention_global');
            var retention4 = divObj.data('retention4'); 
            var sunat_code = $('#{$this->retention_checkbox_id}').data('sunat_code');
            
            if ($('#{$this->retention_checkbox_id}').attr('readonly') == undefined)
            {
                if ($('#{$this->retention_checkbox_id}').is(':checked'))
                {
                    $('#{$this->div_retention_id}').removeClass('hide');
                    $('#{$this->div_no_ret_pen_id}').removeClass('hide');
                    $('#{$this->retention_percent_field_id}').attr('readonly', true);
                    //Si es recibo por honorarios se pone 8%
                    if (sunat_code == '" . Document::HONORARIOS . "') {
                        $('#{$this->retention_percent_field_id}').val(retention4 * 100);
                    }
                }else{
                    $('#{$this->div_retention_id}').addClass('hide');
                    if (!$('#{$this->detraction_checkbox_id}').is(':checked')) {
                        $('#{$this->div_no_ret_pen_id}').addClass('hide');
                    }
                }                
            } else
            {
                $('#{$this->div_retention_id}').addClass('hide');
                if (!$('#{$this->detraction_checkbox_id}').is(':checked')) {
                    $('#{$this->div_no_ret_pen_id}').addClass('hide');
                }
            }
        }
        
        function SIANCommercialAmountsActivateDetraction() {
        
            var sunat_code = $('#{$this->detraction_checkbox_id}').data('sunat_code');
            
            if($('#{$this->detraction_checkbox_id}').attr('readonly') == undefined)
            {
                if ($('#{$this->detraction_checkbox_id}').is(':checked'))
                {
                    $('#{$this->div_detraction_id}').removeClass('hide'); 
                    $('#{$this->div_no_ret_pen_id}').removeClass('hide'); 
                    SIANCommercialAmountsFillDetraction('{$this->detraction_percent_field_id}', '{$this->id}');
                    SIANCommercialAmountsFillDetractionCode('{$this->detraction_code_id}', '{$this->id}');
                }else{
                    //$('#{$this->detraction_percent_field_id}').val(0);
                    $('#{$this->div_detraction_id}').addClass('hide');
                    if(!$('#{$this->retention_checkbox_id}').is(':checked')){
                        $('#{$this->div_no_ret_pen_id}').addClass('hide');
                    }
                }
            }
            else{ 
                $('#{$this->detraction_percent_field_id}').val(0);
                $('#{$this->div_detraction_id}').addClass('hide');
                if(!$('#{$this->retention_checkbox_id}').is(':checked')){
                    $('#{$this->div_no_ret_pen_id}').addClass('hide');
                }
            }
        }
        " .
                (!$this->as_leasing ? "                    
        $('body').on('change', '#{$this->as_bill_checkbox_id}', function(e) {
            
            var divObj = $('#{$this->id}');
            var as_bill = $(this).prop('checked') ? 1 : 0;
              
            SIANCommercialAmountsEnableOrDisableAll('{$this->id}', as_bill);       
            //Actualizamos montos
            SIANCommercialAmountsUpdate('{$this->id}');
        });
        
        $('body').on('change', '#{$this->retention_checkbox_id}', function(e) {
            
            var divObj = $('#{$this->id}');
            var retention4 = divObj.data('retention4');            
            var elementObj = $(this);            
            var sunat_code = elementObj.data('sunat_code');
            var retention = elementObj.prop('checked') ? 1 : 0;

            if(retention != elementObj.data('retention'))
            {
                SIANCommercialAmountsActivateRetention();
                SIANCommercialAmountsCalculate('{$this->id}');
                SIANCommercialAmountsEnableOrDisabledRetentionDetraction('{$this->id}');
                $(this).data('last', retention);
            }
        });
        
        $('body').on('change', '#{$this->detraction_checkbox_id}', function(e) {
            if(window.documents_loaded)
            {
                consoleLog('Se entró al evento onchange de detraction pues ya terminaron de cargar los documentos');
                var current = $(this).prop('checked') ? 1 : 0;
                $('#{$this->id}').data('detraction', current);
                    
                //Aquí empieza
                var divObj = $('#{$this->id}');
                var elementObj = $(this);            
                var sunat_code = elementObj.data('sunat_code');                
                var detraction = elementObj.prop('checked') ? 1 : 0;

                if(detraction != elementObj.data('detraction'))
                {  
                    SIANCommercialAmountsActivateDetraction();
                    SIANCommercialAmountsCalculate('{$this->id}');
                    SIANCommercialAmountsEnableOrDisabledRetentionDetraction('{$this->id}');
                    $('#{$this->detraction_percent_field_id}').change();
                    $(this).data('last', detraction);
                }
            }
            else
            {
                consoleLog('Se intentó cambiar detracción pero aún no terminaban de cargar los documentos');
            }
        });
        " : "
        $('body').on('change', '#{$this->as_bill_checkbox_id}', function(e) {
            SIANCommercialAmountsCalculate('{$this->id}', '');
        });              
        " ), CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Montos ',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
                'data-currency' => $this->model->movement->currency,
            )
        ));

        if ($this->as_leasing) {
            echo "<div class='row'>";

            echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
            echo "<h4><b>Detalle de Montos de Leasing</b></h4>";
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
            echo $this->form->numberFieldRow($this->model, "amortization_{$this->model->movement->currency}", array(
                'id' => $this->amortization_field_id,
                'label' => "Amortiz. <span class='currency-symbol'>{$this->currency_symbol}</span>",
                'name' => 'Amounts[amortization]',
                'class' => "us-double{$this->fixed} sian-commercial-amounts-amortization",
                'step' => $this->step,
                'readonly' => $this->model["amortization_{$this->model->movement->currency}"] == 0,
                'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'amortization');"
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
            echo $this->form->numberFieldRow($this->model, "interest_{$this->model->movement->currency}", array(
                'id' => $this->interest_field_id,
                'label' => "Interés <span class='currency-symbol'>{$this->currency_symbol}</span>",
                'name' => 'Amounts[interest]',
                'class' => "us-double{$this->fixed} sian-commercial-amounts-interest",
                'step' => $this->step,
                'adjustIndex' => 0,
                'readonly' => $this->model["interest_{$this->model->movement->currency}"] == 0,
                'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'interest');"
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12' " . ($this->as_leasing ? '' : 'style=\'display: none;\'') . ">";
            echo $this->form->numberFieldRow($this->model, "desgravamen_{$this->model->movement->currency}", array(
                'id' => $this->desgravamen_field_id,
                'label' => "Desgrav. <span class='currency-symbol'>{$this->currency_symbol}</span>",
                'name' => 'Amounts[desgravamen]',
                'class' => "us-double{$this->fixed} sian-commercial-amounts-desgravamen",
                'step' => $this->step,
                'adjustIndex' => 1,
                'readonly' => $this->model["affected2_{$this->model->movement->currency}"] == 0,
                'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'desgravamen');"
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12' " . ($this->as_leasing ? '' : 'style=\'display: none;\'') . ">";
            echo $this->form->numberFieldRow($this->model, "oae_adm_{$this->model->movement->currency}", array(
                'id' => $this->oae_adm_field_id,
                'label' => "G. Adm.<span class='currency-symbol'>{$this->currency_symbol}</span>",
                'name' => 'Amounts[oae_adm]',
                'class' => "us-double{$this->fixed} sian-commercial-amounts-oae_adm",
                'step' => $this->step,
                'adjustIndex' => 2,
                'readonly' => $this->model["oae_adm_{$this->model->movement->currency}"] == 0,
                'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'oae_adm');"
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12' " . ($this->as_leasing ? '' : 'style=\'display: none;\'') . ">";
            echo $this->form->numberFieldRow($this->model, "oae_financial_{$this->model->movement->currency}", array(
                'id' => $this->oae_financial_field_id,
                'label' => "G. Finan.<span class='currency-symbol'>{$this->currency_symbol}</span>",
                'name' => 'Amounts[oae_financial]',
                'class' => "us-double{$this->fixed} sian-commercial-amounts-oae_financial",
                'step' => $this->step,
                'adjustIndex' => 1,
                'readonly' => $this->model["oae_financial_{$this->model->movement->currency}"] == 0,
                'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'oae_financial');"
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12' " . ($this->as_leasing ? '' : 'style=\'display: none;\'') . ">";
            echo $this->form->numberFieldRow($this->model, "oae_sales_{$this->model->movement->currency}", array(
                'id' => $this->oae_sales_field_id,
                'label' => "G. Ventas<span class='currency-symbol'>{$this->currency_symbol}</span>",
                'name' => 'Amounts[oae_sales]',
                'class' => "us-double{$this->fixed} sian-commercial-amounts-oae_sales",
                'step' => $this->step,
                'adjustIndex' => 2,
                'readonly' => $this->model["oae_sales_{$this->model->movement->currency}"] == 0,
                'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'oae_sales');"
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12' " . ($this->as_leasing ? '' : 'style=\'display: none;\'') . ">";
            echo $this->form->numberFieldRow($this->model, "porte_{$this->model->movement->currency}", array(
                'id' => $this->porte_field_id,
                'label' => "Porte<span class='currency-symbol'>{$this->currency_symbol}</span>",
                'name' => 'Amounts[porte]',
                'class' => "us-double{$this->fixed} sian-commercial-amounts-porte",
                'step' => $this->step,
                'readonly' => $this->model["porte_{$this->model->movement->currency}"] == 0,
                'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'porte');"
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12' " . ($this->as_leasing ? '' : 'style=\'display: none;\'') . ">";
            echo $this->form->numberFieldRow($this->model, "itf_{$this->model->movement->currency}", array(
                'id' => $this->itf_field_id,
                'label' => "Itf <span class='currency-symbol'>{$this->currency_symbol}</span>",
                'name' => 'Amounts[itf]',
                'class' => "us-double{$this->fixed} sian-commercial-amounts-itf",
                'step' => $this->step,
                'readonly' => $this->model["itf_{$this->model->movement->currency}"] == 0,
                'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'itf');"
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12' " . ($this->as_leasing ? '' : 'style=\'display: none;\'') . ">";
            echo $this->form->numberFieldRow($this->model, "other_expenses_{$this->model->movement->currency}", array(
                'id' => $this->other_expenses_field_id,
                'label' => "O.Gastos <span class='currency-symbol'>{$this->currency_symbol}</span>",
                'name' => 'Amounts[other_expenses]',
                'class' => "us-double{$this->fixed} sian-commercial-amounts-other_expenses",
                'step' => $this->step,
                'readonly' => false,
                'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'other_expenses');"
            ));
            echo "</div>";

            echo "</div>";
            echo "<hr>";
        }

        echo "<div class='row'>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "crude_{$this->model->movement->currency}", array(
            'id' => $this->crude_field_id,
            'label' => "Bruto <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[crude]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-crude",
            'step' => $this->step,
            'readonly' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "affected1_{$this->model->movement->currency}", array(
            'id' => $this->affected_field_id,
            'label' => "Afecto <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[affected1]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-affected1",
            'step' => $this->step,
            'hint' => 'Usese por defecto',
            'adjustIndex' => 0,
            'readonly' => !$this->model->as_bill || $this->isCreditNote || $this->as_leasing == 1,
            'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'affected1');"
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12' style=\"display: none;\">";
        echo $this->form->numberFieldRow($this->model, "affected2_{$this->model->movement->currency}", array(
            'id' => $this->interest_field_id,
            'label' => "Interés <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[affected2]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-affected2",
            'step' => $this->step,
            'hint' => 'Usese en casos de leasing',
            'adjustIndex' => 1,
            'readonly' => !$this->model->as_bill || $this->isCreditNote,
            'disabled' => !$this->as_leasing,
            'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'affected2');"
        ));
        echo "</div>";
        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12' style=\"display: none;\">";
        echo $this->form->numberFieldRow($this->model, "affected3_{$this->model->movement->currency}", array(
            'id' => $this->porte_field_id,
            'label' => "Porte <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[affected3]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-affected3",
            'step' => $this->step,
            'hint' => 'Usese en casos de leasing',
            'adjustIndex' => 2,
            'readonly' => !$this->model->as_bill || $this->as_leasing || $this->isCreditNote,
            'disabled' => !$this->as_leasing,
            'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'affected3');"
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "inaffected_{$this->model->movement->currency}", array(
            'id' => $this->inaffected_field_id,
            'label' => "Inafec.<span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[inaffected]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-inaffected",
            'step' => $this->step,
            'adjustIndex' => 1,
            'readonly' => !$this->model->as_bill || $this->as_leasing || $this->isCreditNote,
            'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'inaffected');"
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "nobill_{$this->model->movement->currency}", array(
            'id' => $this->nobill_field_id,
            'label' => "Exoner.<span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[nobill]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-nobill",
            'step' => $this->step,
            'adjustIndex' => 2,
            'readonly' => $this->as_leasing || $this->isCreditNote,
            'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'nobill');"
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "net_{$this->model->movement->currency}", array(
            'id' => $this->net_field_id,
            'label' => "Subtotal <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[net]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-net",
            'step' => $this->step,
            'readonly' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, "igv_percent", [], array(
            'id' => $this->igv_percent_field_id,
            'label' => "%IGV",
            'name' => 'Amounts[igv_percent]',
            'class' => "sian-commercial-amounts-igv-percent",
            'step' => $this->step,
            'readonly' => $this->readonly_igv,
            'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', '');"
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "igv_{$this->model->movement->currency}", array(
            'id' => $this->igv_field_id,
            'label' => "IGV <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[igv]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-igv",
            'step' => $this->step,
            'hint' => 'Mueva las centésimas para cuadrar',
            'readonly' => !$this->model->as_bill,
            'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'igv');"
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "icbp_{$this->model->movement->currency}", array(
            'id' => $this->igv_field_id,
            'label' => "ICBP <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[icbp]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-icbp",
            'step' => $this->step,
            'hint' => 'Mueva las centésimas para cuadrar',
            'readonly' => !$this->model->as_bill,
            'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'icbp');"
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
        echo $this->form->hiddenField($this->model, 'parent_currency', array(
            'id' => $this->parent_currency_input_id,
        ));
        echo $this->form->hiddenField($this->model, 'max_total_pen', array(
            'id' => $this->max_total_pen_field_id,
        ));
        echo $this->form->hiddenField($this->model, 'max_total_usd', array(
            'id' => $this->max_total_usd_field_id,
        ));
        echo $this->form->hiddenField($this->model, 'warehouse_id', [
            'id' => $this->warehouse_input_id,
            'class' => 'sian-commercial-amounts-warehouse-id'
        ]);
        echo $this->form->numberFieldRow($this->model, "total_{$this->model->movement->currency}", array(
            'id' => $this->total_field_id,
            'label' => "Total <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[total]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-total",
            'step' => $this->step,
            'max' => $this->model->getValue('max_total'),
//            'readonly' => $this->as_leasing,
            'hint' => 'Úselo sólo para ajustar montos',
            'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'total');"
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "perception_{$this->model->movement->currency}", array(
            'id' => $this->perception_field_id,
            'label' => "Percep.<span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[perception]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-perception",
            'step' => $this->step,
            'min' => 0,
            'adjustIndex' => 3,
            'readonly' => $this->as_leasing || $this->isCreditNote,
            'onchange' => "SIANCommercialAmountsCalculate('{$this->id}', 'perception');"
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "real_{$this->model->movement->currency}", array(
            'id' => $this->real_field_id,
            'label' => "F.Total <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[real]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-real",
            'step' => $this->step,
            'readonly' => true,
        ));
        echo "</div>";

        echo $this->form->hiddenField($this->model->movement, 'validate_items', array(
            'value' => 0,
        ));

        echo "</div>";

        echo "<div class='row'>";
        $this->_renderCheckboxes();
        if ($this->model->isBuy() || ($this->model->isSale())) {
            $this->_renderRetentionDetraction();
        }
        echo "</div>";

        $this->endWidget();
    }

    private function _renderCheckboxes() {
        echo "<div class='col-lg-1 col-md-1 col-sm-6 col-xs-12'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->include_igv_id,
            'model' => $this->model,
            'attribute' => 'include_igv',
            'htmlOptions' => array(
                'readonly' => true,
            ),
                ), true);
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-6 col-xs-12'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->as_bill_checkbox_id,
            'model' => $this->model,
            'attribute' => 'as_bill',
            'htmlOptions' => array(
                'data-last' => $this->model->as_bill,
                'class' => 'sian-commercial-amounts-as-bill'
            ),
                ), true);
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-6 col-xs-12'>";
        if ($this->model->isBuy()) {
            echo $this->widget('application.widgets.USCheckBox', array(
                'id' => $this->retention_checkbox_id,
                'model' => $this->model,
                'attribute' => 'retention',
                'htmlOptions' => array(
                    'readonly' => false,
                ),
                    ), true);
        }
        echo "</div>";
        echo "<div class='col-lg-1 col-md-1 col-sm-6 col-xs-12'>";
        if ($this->model->isBuy()) {
            echo $this->widget('application.widgets.USCheckBox', array(
                'id' => $this->detraction_checkbox_id,
                'model' => $this->model,
                'attribute' => 'detraction',
                'htmlOptions' => array(
                    'readonly' => false,
                ),
                    ), true);
        }
        echo "</div>";
    }

    private function _renderRetentionDetraction() {

        echo "<div id='{$this->div_retention_id}' class='col-lg-2 col-md-2 col-sm-2 col-xs-12 " . ($this->model->retention == 0 ? 'hide' : '') . "'>";

        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "retention_percent", array(
            'id' => $this->retention_percent_field_id,
            'label' => "%Retención",
            'name' => 'Amounts[retention_percent]',
            'class' => "us-integer sian-commercial-amounts-retention-percent",
            'step' => 0,
            'readonly' => $this->model->retention == 0,
            'onchange' => "SIANCommercialAmountsCalculateRetentionAndDetraction('{$this->id}');"
        ));
        echo "</div>";

        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "ret_{$this->model->movement->currency}", array(
            'id' => $this->ret_field_id,
            'label' => "Retenido <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[ret]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-ret",
            'step' => $this->step,
            'readonly' => true,
        ));
        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "<div id='{$this->div_detraction_id}' class='col-lg-4 col-md-4 col-sm-4 col-xs-12 " . ($this->model->detraction == 0 ? 'hide' : '') . "'>";

        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'detraction_code', [0 => Strings::SELECT_OPTION], [
            'id' => $this->detraction_code_id,
            'class' => "sian-commercial-amounts-detraction-code",
            'label' => "Código Detracción",
            'required' => true
        ]);
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'detraction_percent', [0 => Strings::SELECT_OPTION], [
            'id' => $this->detraction_percent_field_id,
            'class' => "sian-commercial-amounts-detraction-percent",
            'label' => "%Detracción",
            'name' => 'Amounts[detraction_percent]',
            'onchange' => "
                    var detraction_percent = 0;
                    if($('#{$this->detraction_checkbox_id}').prop('checked') == 1){
                        detraction_percent = $('this').floatVal(2);
                    }                    
                    if(!isNaN(detraction_percent)){
                        $('#{$this->id}').data('detraction_value', detraction_percent);
                    }                    
                    SIANCommercialAmountsCalculateRetentionAndDetraction('{$this->id}');"
        ]);
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "det_{$this->model->movement->currency}", array(
            'id' => $this->det_field_id,
            'label' => "Detraído <span class='currency-symbol'></span>",
            'name' => 'Amounts[det]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-det",
            'onchange' => "SIANCommercialAmountsSetNoRetDet('{$this->id}');",
            'step' => $this->step,
        ));
        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "<div id='{$this->div_no_ret_pen_id}' class='col-lg-1 col-md-1 col-sm-6 col-xs-12 " . ($this->model->retention == 0 && $this->model->detraction == 0 ? 'hide' : '') . "'>";
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "no_ret_{$this->model->movement->currency}", array(
            'id' => $this->no_ret_field_id,
            'label' => "No Ret/Det <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[no_ret]',
            'class' => "us-double{$this->fixed} sian-commercial-amounts-no-ret",
            'step' => $this->step,
            'readonly' => true,
        ));
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }

}
