<?php

$arrayItemsNavbar = array(
    // array('icon' => 'list-alt', 'label' => 'Datos Generales', 'url' => '#', 'items' => array(
    array('label' => 'Datos Generales', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'list', 'label' => 'Plan Contable', 'route' => 'account/index'),
                array('icon' => 'list', 'label' => 'Niveles de Costos', 'route' => 'costLevelItem/index'),
                array('icon' => 'refresh', 'label' => 'Tipo de cambio', 'route' => 'exchangeRate/index'),
                array('icon' => 'book', 'label' => 'File Contables', 'route' => 'accountingFile/index'),
            ),
            array(
                array('icon' => 'calendar', 'label' => 'Ejercicios', 'route' => 'year/index'),
                array('icon' => 'calendar', 'label' => 'Periodos', 'route' => 'period/index'),
            ),
            array(
                array('icon' => 'briefcase', 'label' => 'Escenarios', 'route' => 'scenario/index'),
                array('icon' => 'certificate', 'label' => 'Operaciones', 'route' => 'operation/index'),
                array('icon' => 'certificate', 'label' => 'Campos', 'route' => 'scenarioField/index'),
                array('icon' => 'file', 'label' => 'Documentos', 'route' => 'document/index'),
                array('icon' => 'file', 'label' => 'Series de Documentos', 'route' => 'documentSerie/index'),
            ),
            array(
                array('icon' => 'cog', 'label' => 'Variables globales', 'route' => 'globalVar/index'),
                array('icon' => 'cog', 'label' => 'Tipos de productos', 'route' => 'productTypeSetting/index'),
            )
        )
    ),
    // array('icon' => 'th-list', 'label' => 'Movimientos', 'url' => '#', 'items' => array(
    array('label' => 'Movimientos', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'adjust', 'label' => '1.- Asientos de Ajuste', 'route' => 'adjust/index'),
                array('icon' => 'folder-close', 'label' => '2.- Cierre de Resultados', 'route' => 'closing/index'),
                array('icon' => 'folder-close', 'label' => '3.- Cierre de Cuentas de Balance', 'route' => 'closingBalance/index'),
                array('icon' => 'folder-open', 'label' => '4.- Asientos de Apertura', 'route' => 'opening/index'),
            ),
            array(
                array('icon' => 'ok', 'label' => 'Provisionar', 'route' => 'provision/index'),
                array('icon' => 'ok', 'label' => 'Edición de asientos', 'route' => 'entry/index'),
            ),
            array(
                array('icon' => 'file', 'label' => 'C. Retención de Clientes', 'route' => 'clientWithholding/index'),
                array('icon' => 'file', 'label' => 'C. Retención para Proveedores', 'route' => 'providerWithholding/index'),
            ),
            array(
                array('icon' => 'shopping-cart', 'label' => 'Gastos operativos', 'route' => 'operatingCost/index'),
                array('icon' => 'list', 'label' => 'Movimiento Manual (Voucher Contable)', 'route' => 'manual/index'),
                array('icon' => 'list', 'label' => 'Linea FEC', 'route' => 'fecline/index'),
            ),
            array(
                array('icon' => 'usd', 'label' => 'Obligaciones Financieras', 'route' => 'loan/index'),
            ),
            array(
                array('icon' => 'file', 'label' => 'Facturas de leasing', 'route' => 'leasing/index'),
            ),
        )),
    array('label' => 'Sunat', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'file', 'label' => 'PLE', 'route' => 'ple/txt'),
                array('icon' => 'file', 'label' => 'SIRE', 'route' => 'sire/txt'),
                array('icon' => 'file', 'label' => 'PDT', 'route' => 'pdt/txt'),
            ),
            array(
                array('icon' => 'send', 'label' => 'FE Envío de Documentos', 'route' => 'ebilling/tab1'),
                array('icon' => 'send', 'label' => 'FE Envío de resúmenes', 'route' => 'ebilling/tab2'),
                array('icon' => 'circle-arrow-down', 'label' => 'FE Comunicación de baja', 'route' => 'ebilling/tab3'),
                array('icon' => 'search', 'label' => 'FE Consulta de resúmenes', 'route' => 'ebilling/tab4'),
                array('icon' => 'check', 'label' => 'FE Documentos Declarados', 'route' => 'ebilling/tab5'),
            ),
            array(
                array('icon' => 'send', 'label' => 'FE Liquidación de Compra', 'route' => 'ebilling/tab6'),
                array('icon' => 'send', 'label' => 'FE Guía de Remisión', 'route' => 'ebilling/tab7'),
            ),
        )),
    array('label' => 'Reportes Auxiliares', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'list', 'label' => 'Plan Contable', 'route' => 'report/accountingPlan'),
                array('icon' => 'list', 'label' => 'Dinámicas Contables', 'route' => 'report/dynamics'),
                array('icon' => 'list', 'label' => 'Analítico de Cuentas', 'route' => 'report/accountAnalytic'),
                array('icon' => 'list', 'label' => 'Analítico por Asociado', 'route' => 'report/ownerAnalytic'),
                array('icon' => 'list', 'label' => 'Saldo por Cuenta', 'route' => 'report/accountBalance'),
                array('icon' => 'list', 'label' => 'Estado de Cuenta', 'route' => 'report/accountStatus'),
                array('icon' => 'list', 'label' => 'Asientos Contables', 'route' => 'report/accountingEntries'),
                array('icon' => 'list', 'label' => 'Asiento por Empleados', 'route' => 'report/entriesByEmployee'),
                array('icon' => 'list', 'label' => 'Aperturas y Clausuras', 'route' => 'report/openingAndClosing'),
                array('icon' => 'list', 'label' => 'Documentos Enviados', 'route' => 'report/sentDocuments'),
                array('icon' => 'list', 'label' => 'Gastos Operativos', 'route' => 'report/operatingCost'),
            ),
            array(
                array('icon' => 'list', 'label' => 'Lista de Pagos', 'route' => 'report/toPay'),
                array('icon' => 'list', 'label' => 'Lista de Cobros', 'route' => 'report/toCollect'),
                array('icon' => 'list', 'label' => 'Lista de Comprobantes', 'route' => 'report/billsList'),
                array('icon' => 'list', 'label' => 'Retenciones', 'route' => 'report/retention'),
                array('icon' => 'list', 'label' => 'Documentos Ausentes', 'route' => 'report/absentDocument'),
                array('icon' => 'list', 'label' => 'Centro de Costos', 'route' => 'report/costCenter'),
                array('icon' => 'list', 'label' => 'Control de Provisiones', 'route' => 'report/provision'),
                array('icon' => 'eye-open', 'label' => 'Control de Operaciones', 'route' => 'report/operationsControl'),
            ),
            array(
                array('icon' => 'list', 'label' => 'Histórico por Pagar', 'route' => 'report/historyToPay'),
                array('icon' => 'list', 'label' => 'Histórico por Cobrar', 'route' => 'report/historyToCollect'),
            )
        )
    ),
    array('label' => 'Formatos Contables', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'list', 'label' => 'Registro de Compras', 'route' => 'report/buyRecord'),
                array('icon' => 'list', 'label' => 'Registro de Ventas', 'route' => 'report/saleRecord'),
                array('icon' => 'book', 'label' => 'Libro Diario', 'route' => 'report/diary'),
                array('icon' => 'book', 'label' => 'Libro Mayor', 'route' => 'report/major'),
                array('icon' => 'book', 'label' => 'Libro Cajas y Bancos', 'route' => 'report/cashbox'),
                array('icon' => 'list', 'label' => 'Inventario Permanente Valorizado', 'route' => 'report/permanentInventory'),
                array('icon' => 'file', 'label' => 'Balance de Comprobación', 'route' => 'report/checkingBalance'),
                array('icon' => 'file', 'label' => 'Balance General', 'route' => 'report/generalBalance'),
                array('icon' => 'usd', 'label' => 'Estado por Naturaleza', 'route' => 'report/byNature'),
                array('icon' => 'usd', 'label' => 'Estado por Función', 'route' => 'report/byFunction'),
            ),
            array(
                array('icon' => 'list', 'label' => 'DAOT de Ventas', 'route' => 'report/tpoSale'),
                array('icon' => 'list', 'label' => 'DAOT de Compras', 'route' => 'report/tpoBuy'),
            )
        )
    ),
);

$ObjSIANNavbar = $this->widget('application.widgets.SIANNavbar', array(
    'brand' => Yii::app()->id,
    'class' => 'navbar-accounting',
    'items' => $this->items_menu,
        ));

$itemsUserMenu = $ObjSIANNavbar->itemsUserMenu;
