<?php

class SIANInventoryDetailResult extends CWidget {

    public $id;
    public $model;
    public $attribute;
    public $items = [];
    public $reason_items = [];
    public $advanced = 1;
    public $title = 'Causas raíz y acciones a tomar';
    public $name_preffix = 'InventoryDetailResult';
    public $limit = 5;
    public $hint = false;
    public $class = '';
    public $disabled = false;
    public $readonly = false;
    //PROTECTED
    public $msg_without_data = 'false';
    //PRIVATE
    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;

        //Si hay model seteamos items
        if (isset($this->model, $this->attribute)) {
            $this->items = $this->model->{$this->attribute};
        }

        $a_detailResult = [];
        foreach ($this->items as $item) {
            $a_attributes = [];
            $a_attributes['inventory_reason_id'] = isset($item->inventory_reason_id) ? $item->inventory_reason_id : '';
            $a_attributes['action_type'] = isset($item->action_type) ? $item->action_type : '';
            $a_attributes['real_stock'] = isset($item->real_stock) ? $item->real_stock : '';
            $a_attributes['observation'] = isset($item->observation) ? $item->observation : '';
            $a_attributes['errors'] = $item->getAllErrors();

            $a_detailResult[] = $a_attributes;
        }
        
        if(count($this->items) === 0){
            $this->msg_without_data = '<p style="color: red; text-align: center"><em>(Debe agregar causas raíz)</em></p>';
        }

        //Registramos script
        SIANAssets::registerScriptFile('js/sian-inventory-detail-result.js');

        Yii::app()->clientScript->registerScript($this->id, "

        //COUNT
        var divObj = $('#{$this->id}');
        divObj.data('count', 0);
        divObj.data('advanced', {$this->advanced});
        divObj.data('name_preffix', '{$this->name_preffix}');
        divObj.data('disabled', " . CJSON::encode($this->disabled) . ");
        divObj.data('readonly', " . CJSON::encode($this->readonly) . ");
        divObj.data('limit', " . CJSON::encode($this->limit) . ");
        divObj.data('reason_items', " . CJSON::encode($this->reason_items) . ");                       
        
        var array = " . CJSON::encode($a_detailResult) . ";

        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANInventoryDetailResultAddItem('{$this->id}', array[i]['inventory_reason_id'], array[i]['action_type'], array[i]['real_stock'], array[i]['observation'], array[i]['errors']);                
            }
        }
        else
        {            
            $('#{$this->id}').html('{$this->msg_without_data}');
        }
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->title . ($this->hint ? '*' : ''),
            'headerIcon' => 'envelope',
            'htmlOptions' => array(
                'class' => isset($this->model, $this->attribute) && $this->model->hasErrors($this->attribute) ? 'us-error' : ''
            )
        ));

        echo "<div id='{$this->id}' class='{$this->class}'></div>";
        echo CHtml::link("<span class='fa fa-plus fa-lg black'></span> Agregar nuevo", null, array(
            'onclick' => "SIANInventoryDetailResultAddItem('{$this->id}', '', '', '0', '', [])",
            'title' => 'Agregar'
        ));

        if ($this->hint) {
            echo "<hr>";
            echo "<p>(*) {$this->hint}</p>";
        }
        $this->endWidget();
    }

}
