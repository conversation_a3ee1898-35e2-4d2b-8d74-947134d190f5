.dark {
    color: #333!important;
}
.white {
    color: #fff!important;
}
.red {
    color: #ff0000!important;
}
.light-red {
    color: #f77!important;
}
.ambar{
    color: #FFCC00!important;
}
.blue {
    color: #478fca!important;
}
.light-blue {
    color: #93cbf9!important;
}
.green {
    color: #69aa46!important;
}
.light-green {
    color: #b6e07f!important;
}
.orange {
    color: #ff892a!important;
}
.purple {
    color: #a069c3!important;
}
.pink {
    color: #c6699f!important;
}
.pink2 {
    color: #d6487e!important;
}
.brown {
    color: #a52a2a!important;
}
.grey {
    color: #777!important;
}
.light-grey {
    color: #bbb!important;
}
.black {
    color: #000!important;
}

textarea {
    resize: vertical;
}

a:hover, input.us-datepicker:hover{
    cursor: pointer;
}

a[disabled] {
    color: black;
}

a[disabled]:hover, a[disabled]:link:after, a[disabled]:visited:after  {
    color: black;
    text-decoration: none;
    cursor: text;
}

a[disabled]:hover span, a[disabled]:link:after span, a[disabled]:visited:after span {
    cursor: default;
}

.us-sortable-item {
    cursor: move;
}

div.us-modal div.modal-dialog {
    /*    width:940px;left:36%;*/
    width: 70%;  /*desired relative width*/
    max-width: 1160px;
    margin: 10px auto;
    margin-top: 10px;
    margin-right: auto;
    margin-bottom: 10px;
    margin-left: auto;
}

div.us-modal div.modal-header {
    padding-top: 5px;
    padding-bottom: 5px;
}

div.us-modal a.close,
div.us-modal button.close{
    font-size: 25px;
    line-height: 1.5;
}

.us-modal.fade.in {
    top: 0%;
}

.us-label{
    padding-top: 5px;
}

input.us-autocomplete-text {
    text-align: right;
}

tr.us-disabled input, tr.us-disabled textarea, tr.us-disabled td
{
    font-style: italic;
    color:#CCC;
}

tr.us-disabled td
{
    padding: 0px 5px;
}

td.empty{
    text-align: center !important;
}

table.error
{
    border-color: #b94a48;
}

span.null {
    color: #ffc0cb;
}

div.us-error, table.us-error{
    border-style: solid;
    border-width: 1px;
    border-color: #b94a48;
}

span.us-error, label.us-error{
    color: #b94a48;
}

input[type="checkbox"]:hover  {
    cursor: pointer;
}

input[type="radio"]:hover  {
    cursor: pointer;
}

input[readonly], input[readonly="readonly"] {
    pointer-events: none;
    cursor: not-allowed;
}

input[type="checkbox"][readonly],
input[type="radio"][readonly],
input[type="checkbox"][disabled],
input[type="radio"][disabled]
{
    opacity: 0.5;
}

.us-thumb {
    /*The following will work in almost all browsers*/
    max-height:100px;
    max-width:100px;
}

* html .us-thumb {
    max-height:100px;
    max-height:100px;
}

.us-map-container {
    position: relative;
    padding-bottom: 40%;
    height: 0;
    overflow: hidden;
    max-width: 100%;
}

.us-map-container iframe,
.us-map-container object,
.us-map-container embed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.us-map-container img {
    max-width: none;
}

.us-map-container label {
    width: auto;
    display:inline;
}

.pac-input {
    margin-top: 10px;
}

.pac-container {
    z-index: 10000 !important;
}

div.has-error div.cke
{
    border-style: solid;
    border-width: 1px;
    border-color: #b94a48;
}

td.us-image-td,
td.us-image-td img
{
    width: 100px;
}

/*JQuery UI Selectable*/
.us-selectable .thumbnail {
    color: #555555;
    text-shadow: none;
}
.us-selectable .ui-selecting .thumbnail {
    background: #FECA40;
}
.us-selectable .ui-selected .thumbnail {
    background: #F39814;
    color: white;
    text-shadow: none;
}
.us-selectable .thumbnail:hover {
    cursor: pointer;
}

.us-unselectable {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* modal más largo */
div.us-modal div.modal-dialog.sian-modal-lg {
    width: 100%; /* desired relative width */
    max-width: 1400px;
    margin: 10px auto;
}

/* modal más largo */
div.us-modal div.modal-dialog.sian-modal-xs {
    width: 40%; /* desired relative width */
    max-width: 1400px;
    margin: 10px auto;
}
/* btn ok, de sweetalert (https://sweetalert.js.org/docs/) */
.swal-button.swal-button--ok {
    background-color: #FF6B00 !important;
}

.swal-title {
    font-size: 20px;
}
@media (min-width: 1200px) {
    div.us-modal div.modal-dialog.sian-modal-lg {
        width: 85%; /* desired relative width */
    }

    div.us-modal div.modal-dialog.sian-modal-xs {
        width: 40%; /* desired relative width */
    }
}

@media (max-width: 899px) {
    div.us-modal div.modal-dialog.sian-modal-xs {
        width: 95%;
    }
}
