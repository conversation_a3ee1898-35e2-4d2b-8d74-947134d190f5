<?php

class SIANManageAttachment extends CWidget {

    public $id;
    public $form;
    public $model;
    public $title = "Adjuntar archivos";
    public $attribute;
    public $name = 'Attachment';
    public $type = Resource::TYPE_ATTACHMENT;
    public $min_file_count = 1;
    public $max_file_count = 5;
    public $accept = "*";
    public $readonly = false;
    public $count_input_id;
    public $sub_path = "";
    public $has_limit = true;
    public $limit = 0;
    public $property_count = "attachment_count";
    //PRIVATE
    private $controller;
    private $input_id;
    private $max_file_size;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->count_input_id = isset($this->count_input_id) ? $this->count_input_id : $this->controller->getServerId();
        if ($this->sub_path != "") {
            $this->property_count = $this->sub_path . "_count";
        }
        //
        $this->input_id = $this->controller->getServerId();
        $this->max_file_size = $this->controller->getOrganization()->globalVar->max_attachment_size * 1024;
        $this->name = isset($this->model) ? get_class($this->model) : $this->name;
        SIANAssets::registerScriptFile('js/sian-manage-attachment.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/js/plugins/piexif.min.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/js/plugins/sortable.min.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/js/plugins/purify.min.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/js/fileinput.min.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/themes/fas/theme.min.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/js/locales/es.js');
        //
        SIANAssets::registerCssFile('other/bootstrap-fileinput/css/fileinput.min.css');
        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        var divObj = $('#{$this->id}');
        //Seteamos data
        divObj.data('input_id', '{$this->input_id}');
        divObj.data('load_attachments_url', '{$this->controller->createUrl("viewAttachments")}');
        divObj.data('upload_attachments_url', '{$this->controller->createUrl("uploadAttachments")}');
        divObj.data('sort_attachments_url', '{$this->controller->createUrl("sortAttachments")}');
        divObj.data('model_id', '{$this->model[$this->model->primaryKey()]}');
        divObj.data('owner', '{$this->model->getOwner()}');
        divObj.data('type', '{$this->type}');
        divObj.data('sub_path', '{$this->sub_path}');
        divObj.data('property_count', '{$this->property_count}');
        divObj.data('has_limit', " . ($this->has_limit ? 1 : 0) . ");
        divObj.data('limit', '{$this->limit}');
        divObj.data('min_file_count', '{$this->min_file_count}');
        divObj.data('max_file_count', '{$this->max_file_count}');
        divObj.data('max_file_size', '{$this->max_file_size}');
        divObj.data('count_input_id', '{$this->count_input_id}');
        divObj.data('allow_modify', " . CJSON::encode($this->model->status == 1 && $this->controller->checkRoute("modifyAttachments")) . ");
        $(document).ready(function() {
            SIANManageAttachmentInit('{$this->id}');
        });
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->title,
            'headerIcon' => 'file',
            'htmlOptions' => [
                'id' => $this->id
            ]
        ));
        echo "<div class='file-loading'>";
        echo "<input id='{$this->input_id}' name='{$this->name}[]' type='file' accept='{$this->accept}' multiple " . ($this->readonly ? "readonly" : "") . ">";
        echo "</div>";
        $this->endWidget();
    }

}
