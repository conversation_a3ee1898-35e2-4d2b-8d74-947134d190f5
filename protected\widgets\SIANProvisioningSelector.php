<?php

class SIANProvisioningSelector extends CWidget {

    public $id;
    public $form;
    public $model;
    public $provision_items;
    public $modal_id;
    public $readonly = false;
    public $fixed = 2;
    public $step = 0.01;
    public $render_detraction = true;
    public $periods = null;
    public $render_entry = false;
    public $pre_pay = null;
    //
    public $entry_id;
    public $accounting_file_input_id;
    public $year_input_id;
    public $period_input_id;
    public $day_input_id;
    public $correlative_input_id;
    public $detraction_proof_input_id;
    public $detraction_date_input_id;
    public $refresh_button_id;
    public $provisioned_button_id;
    public $detraccion_readonly = false;
    //PRIVATE
    private $controller;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;

        if ($this->model->scenario->type === Scenario::TYPE_AUTOMATIC && !isset($this->entry_id)) {
            throw new Exception('Debe especificar el ID del widget SIANEntry');
        }

        $this->periods = isset($this->periods) ? $this->periods : USTime::periods;
        //
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->accounting_file_input_id = isset($this->accounting_file_input_id) ? $this->accounting_file_input_id : $this->controller->getServerId();
        $this->year_input_id = isset($this->year_input_id) ? $this->year_input_id : $this->controller->getServerId();
        $this->period_input_id = isset($this->period_input_id) ? $this->period_input_id : $this->controller->getServerId();
        $this->day_input_id = isset($this->day_input_id) ? $this->day_input_id : $this->controller->getServerId();
        $this->correlative_input_id = isset($this->correlative_input_id) ? $this->correlative_input_id : $this->controller->getServerId();
        $this->detraction_proof_input_id = isset($this->detraction_proof_input_id) ? $this->detraction_proof_input_id : $this->controller->getServerId();
        $this->detraction_date_input_id = isset($this->detraction_date_input_id) ? $this->detraction_date_input_id : $this->controller->getServerId();

        //Buttons
        $this->refresh_button_id = isset($this->refresh_button_id) ? $this->refresh_button_id : $this->controller->getServerId();
        $this->provisioned_button_id = isset($this->provisioned_button_id) ? $this->provisioned_button_id : $this->controller->getServerId();


        if(isset($this->pre_pay) && count($this->pre_pay) > 0){
            $this->model->auto_apply = 1;
        }

        //Registramos assets
        SIANAssets::registerScriptFile('other/select2/js/select2.min.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
        SIANAssets::registerCssFile('other/select2/css/select2.css');
        SIANAssets::registerScriptFile('js/sian-provisioning-selector.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        var div = $('#{$this->id}');
        //Seteamos data
        div.data('accounting_file_input_id', '{$this->accounting_file_input_id}');
        div.data('year_input_id', '{$this->year_input_id}');
        div.data('period_input_id', '{$this->period_input_id}');
        div.data('day_input_id', '{$this->day_input_id}');
        div.data('correlative_input_id', '{$this->correlative_input_id}');
        div.data('load_provision_items_url', '{$this->controller->createUrl('/widget/loadProvisionItems')}');
        //
        div.data('periods', " . CJSON::encode($this->periods) . ");
        div.data('provision_items', " . CJSON::encode(self::getFileItems($this->provision_items)) . ");

        $(document).ready(function() {
            SIANProvisioningSelectorInit('{$this->id}', " . CJSON::encode($this->model->accountingMovement->accounting_file_id) . ", " . CJSON::encode($this->model->accountingMovement->year) . ", " . CJSON::encode($this->model->accountingMovement->period) . ", " . CJSON::encode($this->model->accountingMovement->day) . ", " . CJSON::encode($this->model->accountingMovement->correlative) . ", '{$this->model->currency}', " . CJSON::encode($this->readonly) . ");
        });
        
        $('#{$this->id}').data('getData', function(){

            var accounting_file_id = $('#{$this->accounting_file_input_id}').val();
            var year = $('#{$this->year_input_id}').val();
            var period = $('#{$this->period_input_id}').val();
            var day = $('#{$this->day_input_id}').val();
            var correlative = $('#{$this->correlative_input_id}').val();

            return {
                accounting_file_id: accounting_file_id,
                year: year,
                period: period,
                day: day,
                correlative: correlative
            };            
        });

        $('body').on('change', '#{$this->accounting_file_input_id}, #{$this->year_input_id}, #{$this->period_input_id}', function(e) {
            
            var accounting_file_id = $('#{$this->accounting_file_input_id}').val();
            var year = $('#{$this->year_input_id}').val();
            var period = $('#{$this->period_input_id}').val();
            var day = $('#{$this->day_input_id}').val();
                
            SIANProvisioningSelectorUpdate('{$this->id}', accounting_file_id, year, period, day, 0);
                
            //Actualizamos botón Provisioned
            $('#{$this->provisioned_button_id}').data('id', accounting_file_id);
            $('#{$this->provisioned_button_id}').data('year', year);
            $('#{$this->provisioned_button_id}').data('period', period);
           
            " . ($this->model->scenario->type === Scenario::TYPE_AUTOMATIC ? "$('#{$this->entry_id}').data('automaticEntry')();" : "") . "
        });  
        
        $('body').on('click', '#{$this->refresh_button_id}', function(e) {
            if ($(this).hasAttr('disabled') === true)
            {
                return false;
            }
            else
            {
                SIANProvisioningSelectorRefresh('{$this->id}');
            }                
        });   
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Datos del provisionamiento',
            'headerIcon' => 'file',
            'htmlOptions' => [
                'id' => $this->id,
            ],
        ));

        if ($this->render_detraction) {
            echo "<div class='row'>";
            echo "<div class='col-lg-8 col-md-8 col-sm-8 col-xs-12'>";
            $this->renderForm();
            echo "</div>";
            echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
            $this->renderDetraction();
            echo "</div>";
            echo "</div>";
        } else {
            echo "<div class='row'>";
            echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
            $this->renderForm();
            echo "</div>";
            echo "</div>";
        }
        if ($this->render_entry && isset($this->model->accountingMovement) && count($this->model->accountingMovement->tempEntries) > 0) {
            echo "<hr>";

            echo "<div class='row'>";
            echo "<div class='col-lg-3 col-md-3 col-sm-12 col-xs-12'>";
            if(isset($this->pre_pay) && count($this->pre_pay) > 0){
                $key = 'real_' . $this->model->currency;
                $i_prePay_quantity = count($this->pre_pay);
                $i_prePay_total =  0;

                foreach($this->pre_pay as $paymentPrePay) {
                    $i_prePay_total += $paymentPrePay[$key];
                }

                echo "<div class='row'>";
                echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
                echo "<div class='alert alert-warning' style='font-size: 1em; font-weight: bold; animation: fadeIn 0.5s;'>";
                echo "<i class='glyphicon glyphicon-warning-sign'></i>";
                echo "<strong>¡Advertencia!</strong>";
                echo "<p>";
                echo "El documento tiene {$i_prePay_quantity} adelanto(s) pagado(s) por la suma de ";
                echo "<strong>";
                echo Currency::getSymbol($this->model->currency);
                echo $i_prePay_total;
                echo "</strong>";
                echo "</p>";
                echo "</div>";
                echo "<div style='display: flex; justify-content: center;' >";
                $this->widget('application.widgets.USSwitch', array(
                    'model' => $this->model,
                    'attribute' => 'auto_apply',
                    'label' => 'Aplicar Automaticamente',
                    'htmlOptions' => [
                        'name' => 'auto_apply',
                        'defaultValue' => true,
                    ]
                ));
                echo "</div>";
                echo "</div>";
                echo "</div>";

                echo "<style> @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; }} </style>";

            }
            echo "</div>";
            echo "<div class='col-lg-9 col-md-9 col-sm-12 col-xs-12 text-right'>";
            $this->renderEntry();
            echo "</div>";
            echo "</div>";
        }

        $this->endWidget();
    }

    private function renderForm() {
        echo "<div class='row'>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->accountingMovement, 'accounting_file_id', [], array(
            'id' => $this->accounting_file_input_id,
            'class' => 'sian-provisioning-selector-accounting-file-input-id',
            'required' => true,
            'readonly' => true,
        ));
        echo "</div>";


        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->accountingMovement, 'year', [], array(
            'id' => $this->year_input_id,
            'class' => 'sian-provisioning-selector-year',
            'required' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->accountingMovement, 'period', [], array(
            'id' => $this->period_input_id,
            'class' => 'sian-provisioning-selector-period',
            'required' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->accountingMovement, 'day', [], array(
            'id' => $this->day_input_id,
            'class' => 'sian-provisioning-selector-day',
            'required' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model->accountingMovement, 'correlative', array(
            'id' => $this->correlative_input_id,
            'class' => 'sian-provisioning-selector-correlative',
            'required' => true,
            'step' => 1,
            'min' => 1,
            'readonly' => !$this->model->accountingMovement->isNewRecord,
            'append' => $this->widget('application.widgets.USLink', array(
                'id' => $this->refresh_button_id,
                'icon' => 'fa fa-lg fa-refresh',
            ), true) . ' ' .
                $this->widget('application.widgets.USLink', array(
                    'class' => 'form',
                    'id' => $this->provisioned_button_id,
                    'icon' => 'fa fa-lg fa-eye',
                    'route' => '/widget/getProvisioned',
                    'data' => array(
                        'id' => $this->model->accountingMovement->accounting_file_id,
                        'parent_id' => isset($this->modal_id) ? $this->modal_id : null,
                        'year' => $this->model->accountingMovement->year,
                        'period' => $this->model->accountingMovement->period,
                    ),
                    'visible' => true,
                ), true)
        ));
        //Este campo hace que no se obtenga automaticamente el correlativo
        echo $this->form->hiddenField($this->model->accountingMovement, 'automatic', [
            'value' => 0,
        ]);
        echo "</div>";

        echo "</div>";
    }

    private function renderDetraction() {

        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model->accountingMovement,
            'attribute' => 'has_detraction',
            'readonly' => $this->detraccion_readonly,
            'switchChange' => "$('#{$this->detraction_proof_input_id}').prop('disabled', !state);$('#{$this->detraction_date_input_id}').prop('disabled', !state);",
        ), true);
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->form->textFieldRow($this->model->accountingMovement, 'detraction_proof', array(
            'id' => $this->detraction_proof_input_id,
            'disabled' => !$this->model->accountingMovement->has_detraction
        ));
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->widget('application.widgets.USDatePicker', array(
            'id' => $this->detraction_date_input_id,
            'form' => $this->form,
            'model' => $this->model->accountingMovement,
            'attribute' => 'detraction_date',
            'options' => array(
                'minDate' => $this->model->emission_date,
            ),
            'htmlOptions' => array(
                'disabled' => !$this->model->accountingMovement->has_detraction
            )
        ), true);
        echo "</div>";


        echo "</div>";
    }

    private function renderEntry() {
        $this->widget('application.widgets.SIANEntry', array(
            'form' => $this->form,
            'model' => $this->model,
            'readonly' => $this->readonly,
            'mode' => SIANEntry::MODE_PROVISION,
            'show_panel' => false,
        ));
    }

    public static function getFileItems($provisionItems) {
        $items = [];
        foreach ($provisionItems as $file) {
            $items[] = array(
                'accounting_file_id' => $file->accounting_file_id,
                'accounting_file_name' => "{$file->accounting_file_code} | {$file->accounting_file_name}",
                'years' => self::getYearItems($file),
            );
        }
        return $items;
    }

    public static function getYearItems($file) {
        $items = [];
        foreach ($file->years as $year) {
            $items[] = array(
                'year' => $year->year,
                'periods' => self::getPeriodItems($year),
            );
        }
        return $items;
    }

    public static function getPeriodItems($year) {
        $items = [];
        foreach ($year->periods as $period) {
            $items[] = array(
                'period' => $period->period,
                'period_name' => USTime::getMonthName($period->period, true),
                'correlative' => $period->correlative,
                'days' => self::getDayItems($period),
            );
        }
        return $items;
    }

    public static function getDayItems($period) {
        $items = [];
        foreach ($period->days as $day) {
            $items[] = array(
                'day' => $day->day,
                'day_name' => $day->toString(),
            );
        }
        return $items;
    }

}
