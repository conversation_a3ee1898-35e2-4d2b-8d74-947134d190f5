<?php

class USLabelColor extends CWidget {

    public $value = 'default';
    public $labels = null;
    public $types = array(
        'default' => 'default',
        'primary' => 'primary',
        'success' => 'success',
        'info' => 'info',
        'warning' => 'warning',
        'danger' => 'danger'
    );

    /**
     * Initializes the widget.
     */
    public function init() {
        
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo $this->controller->widget('booster.widgets.TbLabel', array(
            'context' => $this->types[$this->value],
            'label' => isset($this->labels) ? $this->labels[$this->value] : $this->value
                ), true);
    }

}
