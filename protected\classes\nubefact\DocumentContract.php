<?php

abstract class DocumentContract
{

    //Tipos de Comprobante
    public static $FACTURA = 1;
    public static $BOLETA = 2;
    public static $NOTA_CREDITO = 3;
    public static $NOTA_DEBITO = 4;

    //Sunat Transaction
    public static $VENTA_INTERNA = 1; // INTERNAL_SALES
    public static $EXPORTACION = 2; // EXPORT
    public static $VENTA_INTERNA_ANTICIPOS = 4; // INTERNAL_SALES_ADVANCES
    // public static $NO_DOMICILIADO = 29; // NON_DOMESTIC_SALES_THAT_DO_NOT_QUALIFY_AS_EXPORTS
    public static $VENTAS_NO_DOMICILIADOS_QUE_NO_CALIFICAN_COMO_EXPORTACIÓN = 29; // NON_DOMESTIC_SALES_THAT_DO_NOT_QUALIFY_AS_EXPORTS
    public static $OPERACION_SUJETA_A_DETRACCION = 30; // OPERATION_SUBJECT_TO_DETRACTION => OPERACIÓN SUJETA A DETRACCIÓN.
    public static $DETRACCION_SERVICIOS_DE_TRANSPORTE_CARGA = 33; // 'DETRACTION_CARGO_TRANSPORTATION_SERVICES' => 33 = DETRACCIÓN - SERVICIOS DE TRANSPORTE CARGA 
    public static $OPERACION_SUJETA_A_PERCEPCION = 34; //'OPERATION_SUBJECT_TO_COLLECTION' => 34 = OPERACIÓN SUJETA A PERCEPCIÓN
    public static $DETRACCION_SERVICIOS_DE_TRANSPORTE_DE_PASAJEROS = 32; // 'DETRACTION_PASSENGER_TRANSPORTATION_SERVICES' => 32 = DETRACCIÓN - SERVICIOS DE TRANSPORTE DE PASAJEROS.
    public static $DETRACCION_RECURSOS_HIDROBIOLOGICOS = 31; // 'DETRACTION_HYDROBIOLOGICAL_RESOURCES' => 31 = DETRACCIÓN - RECURSOS HIDROBIOLÓGICOS


    //Tipo de Documento
    public static $RUC = 6;
    public static $DNI = 1;
    public static $VARIOS = '-';
    public static $CARNET_EXTRANJERIA = 4;
    public static $PASAPORTE = 7;
    public static $CEDULA_DIPLOMATICA_IDENTIDAD = 'A';
    public static $NO_DOMICILIADO_SIN_RUC = 0; //Exportación

    //Monedas
    public static $SOL = 1;
    public static $DOLAR = 2;
    public static $EURO = 3;
    public static $LIBRA_ESTERLINA = 4;

    //Percepción Tipo
    public static $PERCEPCION_VENTA_INTERNA = 1; // Tasa 2%
    public static $PERCEPCION_ADQUISICION_COMBUSTIBLE = 2; // Tasa 1%
    public static $PERCEPCION_AL_AGENTE_TASA_ESPECIAL = 3; // Tasa 0.5%

    //Unidades de Medida por defecto
    public static $PRODUCTO = 'NIU';
    public static $SERVICIO = 'ZZ';

    //Tipo de IGV
    public static $IGV_GRAVADO = 1;
    public static $IGV_GRAVADO_RETIRO_PREMIO = 2;
    public static $IGV_GRAVADO_RETIRO_DONACION = 3;
    public static $IGV_GRAVADO_RETIRO = 4;
    public static $IGV_GRAVADO_RETIRO_PUBLICIDAD = 5;
    public static $IGV_GRAVADO_BONIFICACIONES = 6;
    public static $IGV_GRAVADO_RETIRO_ENTREGA_TRABAJADORES = 7;
    public static $IGV_EXONERADO = 8;
    public static $IGV_INAFECTO = 9;
    public static $IGV_INAFECTO_RETIRO_BONIFICACION = 10;
    public static $IGV_INAFECTO_RETIRO = 11;
    public static $IGV_INAFECTO_RETIRO_MUESTRAS_MEDICAS = 12;
    public static $IGV_INAFECTO_RETIRO_CONVENIO_COLECTIVO = 13;
    public static $IGV_INAFECTO_RETIRO_PREMIO = 14;
    public static $IGV_INAFECTO_RETIRO_PUBLICIDAD = 15;
    public static $IGV_EXPORTACION = 16;

    /**
     * Se usa para obtener Array generado de Acuerdo a las especificaciones Nubefact
     * @return mixed
     */
    abstract public function getArray();
}