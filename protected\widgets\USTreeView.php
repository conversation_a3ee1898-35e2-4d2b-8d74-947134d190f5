<?php

class USTreeView extends CWidget {

    public $id = null;
    public $view;
    public $module;
    public $controller;
    public $id_attribute;
    public $label_attribute;
    public $status_attribute;
    public $parent_attribute;
    public $collapsed = true;
    public $expanded = true;
//PRIVATE
    private $items = [];
    private $children = array('parents' => []);
    private $unknown = [];
    private $preview_access;
    private $create_access;
    private $update_access;
    private $delete_access;

    /**
     * Initializes the widget.
     */
    public function init() {
        $this->preview_access = Yii::app()->controller->checkRoute("/{$this->module}/{$this->controller}/preview");
        $this->create_access = Yii::app()->controller->checkRoute("/{$this->module}/{$this->controller}/create");
        $this->update_access = Yii::app()->controller->checkRoute("/{$this->module}/{$this->controller}/update");
        $this->delete_access = Yii::app()->controller->checkRoute("/{$this->module}/{$this->controller}/delete");
    }

    /**
     * Runs the widget.
     */
    public function run() {
        //GENERAMOS UN ID SI NO HA SIDO DEFINIDO
        $this->id = isset($this->id) ? $this->id : $this->owner->getServerId();
        //CREAMOS UNA NUEVA INSTANCIA DE LA VISTA
        $view = new $this->view;
        //CREAMOS EL CRITERIO DE BUSQUEDA
        $criteria = new CDbCriteria();
        $criteria->select = array($this->id_attribute, $this->label_attribute, $this->status_attribute, $this->parent_attribute);
        $criteria->order = "{$this->parent_attribute}, {$this->label_attribute}";
        //RECORREMOS TODOS LOS MODELOS
        foreach ($view->findAll($criteria) as $model) {
            $id = $model->{$this->id_attribute};
            $label = $model->{$this->label_attribute};
            $status = $model->{$this->status_attribute};
            $parent_id = $model->{$this->parent_attribute};
            //LLENAMOS LOS ARRAYS
            $this->items[$id] = [];
            $this->items[$id]['status'] = $status;
            $this->items[$id]['label'] = $label;
            $this->children[$id] = [];
            //SI NO TIENE PADRE
            if (strlen($parent_id) == 0) {
                $this->children['parents'][] = $id;
            } else {
                if (isset($this->children[$parent_id])) {
                    $this->children[$parent_id][] = $id;
                } else {
                    $this->unknown[$id] = $parent_id;
                }
            }
        }

        foreach ($this->unknown as $id => $parent_id) {
            $this->children[$parent_id][] = $id;
        }

        $models = $this->process($this->children['parents']);


        echo"<div id='" . $this->id . "'>";
        if (count($models) > 0) {
            echo $this->widget('system.web.widgets.CTreeView', array(
                'id' => $this->owner->getServerId(),
                'data' => $models,
                'collapsed' => $this->collapsed,
                'animated' => 'fast',
                'control' => '#faq_treeview',
//            'url' => array('frameset/getTreeMenu'),
                'htmlOptions' => array(
                    'class' => 'filetree treeview-black',
                ),
                    ), true);
            echo "</div>";
        } else
            echo "<em>" . Strings::NO_DATA . "</em>";
    }

    private function process($ids) {

        $items = [];

        foreach ($ids as $id) {
            $label = $this->items[$id]['label'];
            $status = $this->items[$id]['status'];
            unset($this->items[$id]);

            $children_ids = $this->children[$id];
            unset($this->children[$id]);

            $children = [];

            $html = '<span>' . $label . '&nbsp' . Yii::app()->controller->widget('application.widgets.USBooleanLabel', array(
                        'value' => $status,
                            ), true) . '&nbsp';
            $html .= "&nbsp";
            $html .= Yii::app()->controller->widget('application.widgets.USLink', array(
                'route' => "/{$this->module}/{$this->controller}/preview",
                'icon' => 'fa fa-lg fa-eye',
                'title' => 'Vista previa',
                'class' => 'form',
                'data' => array(
                    'id' => $id,
                    'type' => 'html',
                    'element_id' => $this->id,
                ),
                'visible' => $this->preview_access,
                    ), true);
            $html .= "&nbsp";
            $html .= Yii::app()->controller->widget('application.widgets.USLink', array(
                'route' => "/{$this->module}/{$this->controller}/create",
                'icon' => 'fa fa-lg fa-plus',
                'title' => 'Crear subelemento',
                'class' => 'form',
                'data' => array(
                    'type' => 'html',
                    'element_id' => $this->id,
                    $this->parent_attribute => $id
                ),
                'visible' => $this->create_access,
                    ), true);
            $html .= "&nbsp";
            $html .= Yii::app()->controller->widget('application.widgets.USLink', array(
                'route' => "/{$this->module}/{$this->controller}/update",
                'icon' => 'fa fa-lg fa-pencil',
                'title' => 'Editar',
                'class' => 'form',
                'data' => array(
                    'id' => $id,
                    'type' => 'html',
                    'element_id' => $this->id,
                ),
                'visible' => $this->update_access,
                    ), true);
            if (count($children_ids) == 0) {
                $html .= "&nbsp";
                $html .= Yii::app()->controller->widget('application.widgets.USLink', array(
                    'route' => "/{$this->module}/{$this->controller}/delete",
                    'icon' => 'fa fa-lg fa-times',
                    'title' => 'Eliminar',
                    'class' => 'confirm',
                    'data' => array(
                        'id' => $id,
                        'type' => 'html',
                        'element_id' => $this->id,
                    ),
                    'visible' => $this->delete_access,
                        ), true);
            }
            $html .= "</span>";
            $children['text'] = $html;
            $children['expanded'] = $this->expanded;
            $children['children'] = $this->process($children_ids);

            array_push($items, $children);
        }

        return $items;
    }

}
