<?php

class USSearchForm extends SIANForm {

    public $export_id;
    public $grid_id = null;
    public $onsearch = '';
    public $onreset = '';
    public $route;
    public $submit_id;
    //PRIVATE
    private $controller;
    private $toggle_id;
    private $div_id;

    /**
     * Initializes the widget.
     */
    public function init() {

        if (!isset($this->grid_id)) {
            throw new Exception('Debe especificar el ID de la grilla');
        }

        //Seteamos modo de búsqueda
        $this->search_mode = true;

        $this->controller = Yii::app()->controller;

        $this->method = 'GET';
        $this->enableAjaxValidation = false;
        $this->enableClientValidation = false;

        parent::init();

        //PRIVATE
        $this->export_id = $this->controller->getServerId();
        $this->toggle_id = $this->controller->getServerId();
        $this->div_id = $this->controller->getServerId();
        $this->submit_id = isset($this->submit_id) ? $this->submit_id : $this->controller->getServerId();
        $this->action = $this->controller->createUrl($this->route);

        //JS
        SIANAssets::registerScriptFile("other/kayalshri-tableExport/tableExport.js");
        SIANAssets::registerScriptFile("other/kayalshri-tableExport/jquery.base64.js");
        SIANAssets::registerScriptFile("other/kayalshri-tableExport/html2canvas.js");
        SIANAssets::registerScriptFile("other/kayalshri-tableExport/jspdf/libs/sprintf.js");
        SIANAssets::registerScriptFile("other/kayalshri-tableExport/jspdf/jspdf.js");
        SIANAssets::registerScriptFile("other/kayalshri-tableExport/jspdf/libs/base64.js");
        //
        Yii::app()->clientScript->registerScript(get_class($this), "
            
        $(document).ready(function() {
            " . ($this->controller->default_values_changed ? "
            $.notify('Se han cargado los filtros de su búsqueda anterior, si no los desea, pulse el botón \'Limpiar\'', {
                className: 'info',
                showDuration: 50,
                hideDuration: 50,
                autoHideDelay: 5000
            });                
            " : "") . "
        });
        
        $('body').on('click', '#{$this->toggle_id}', function() {
            $('#{$this->div_id}').toggle('fast');
            $(this).children('i').toggleClass('fa-chevron-up fa-chevron-down');
            resizeFooter();
            setTimeout(resizeFooter, 100);
            setTimeout(resizeFooter, 200);
            setTimeout(resizeFooter, 300);
            return false;
        });
        
        $('body').on('submit', '#{$this->id}', function(e) {
            e.preventDefault();
            
            var form = $('#{$this->id}');
            var data = form.serialize();
            $.fn.yiiGridView.update('{$this->grid_id}', {
                url: window.location.href,
                data: data,
                complete: function(response, status) { 
                    {$this->onsearch}
                }
            });
            
        });
        
        $('body').on('click', '#{$this->cancel_id}', function(e) {

            e.preventDefault();

            var form = $('#{$this->id}');
            //form[0].reset();
            //Reseteamos select
            form.find('input').each(function() {
                $(this).val(null);
            });
            form.find('select').each(function() {
                $(this).prop('selectedIndex', 0);
            });
            //Resetamos select2 si hubiera
            form.find('.select2-container').each(function() {
                $(this).select2('val', '');
            });
            
            //Restauramos por defecto
            $.ajax({
                type: 'post',
                url: '{$this->controller->createUrl('/widget/getDefaultFilters')}',
                data: {
                    underscored_route: '{$this->controller->getUnderscoredRoute()}',
                },
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },
                success: function (data) {
                    if(data)
                    {
                        $.each(data, function(attribute, value) {
                            $('[attribute=\"' + attribute + '\"]').val(value);
                        });
                    }

                    window.active_ajax--;
                    
                    var data = form.serialize();
                    $.fn.yiiGridView.update('{$this->grid_id}', {
                        url: window.location.href,                        
                        data: data,
                        complete: function(response, status) { 
                            {$this->onreset}  
                        }
                    });
                },
                error: function (request, status, error) { // if error occured
                    bootbox.alert(us_message(request.responseText, 'error'));

                    window.active_ajax--;
                },
                dataType: 'json'
            });
        });

        $('body').on('click', '#{$this->export_id} a', function(e) {
            
            e.preventDefault();
            
            var table = $('#{$this->grid_id} table');
            var count = table.find('th').length;    
            var ignoreColumn = [];
            $(table.find('th')).each(function( index ) {
                var column = $(this);
                if(column.hasClass('checkbox-column') || index == (count - 1))
                {
                    ignoreColumn.push(index);
                }
            });

            var element = $(this);
            table.tableExport({
                filename: '{$this->controller->title}',
                type: element.data('type'), 
                escape: 'false', 
                ignoreColumn: ignoreColumn
            });
        });
    ", CClientScript::POS_END);

        echo "<p>";
        echo "<b style='font-size: 14px;'>Filtros </b>";
        echo $this->widget('booster.widgets.TbButton', array(
            'id' => $this->toggle_id,
            'context' => 'default',
            'icon' => 'fa fa-lg fa-chevron-up black',
            'size' => 'extra_small',
            'label' => null,
            'htmlOptions' => array(
                'class' => 'search-button'
            )
                ), true);
        echo "</p>";
        echo "<div id='{$this->div_id}'>";
    }

    public function run() {

        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12 text-center'>";
        $this->widget('booster.widgets.TbButtonGroup', array(
            'buttons' => array(
                array(
                    'buttonType' => 'submit',
                    'context' => 'primary',
                    'label' => 'Buscar',
                    'size' => 'extra_small',
                    'icon' => 'search white',
                    'htmlOptions' => array(
                        'id' => $this->submit_id
                    )
                ),
                array(
                    'buttonType' => 'reset',
                    'context' => 'default',
                    'size' => 'extra_small',
                    'label' => 'Limpiar',
                    'icon' => 'fire',
                    'htmlOptions' => array(
                        'id' => $this->cancel_id
                    )
                ),
            )
        ));
        ?>
        <br/><br/>
        <?php
        $this->widget('booster.widgets.TbButtonGroup', array(
            'buttons' => array(
                array(
                    'buttonType' => 'button',
                    'context' => 'success',
                    'size' => 'extra_small',
                    'label' => 'Exportar',
                    'icon' => 'file',
                    'htmlOptions' => array(
                    ),
                    'dropdownOptions' => array(
                        'id' => $this->export_id,
                    ),
                    'encodeLabel' => false,
                    'items' => $this->getExportItems()
                ),
            )
        ));
        ?>
        <?php
        echo "</div>";
        echo "</div>";

        parent::run();

        echo "</div>";
    }

    private function getExportItems() {
        $exportItems = array(
            array(
                'format' => 'xls',
                'label' => 'Excel',
            ),
            array(
                'format' => 'doc',
                'label' => 'Word',
            ),
            array(
                'format' => 'ppt',
                'label' => 'Power Point',
            ),
            '---',
            array(
                'format' => 'pdf',
                'label' => 'PDF',
            ),
            array(
                'format' => 'csv',
                'label' => 'CSV',
            ),
            array(
                'format' => 'txt',
                'label' => 'TXT',
            ),
            '---',
            array(
                'format' => 'json',
                'label' => 'JSON',
            ),
            array(
                'format' => 'xml',
                'label' => 'XML',
            ),
            array(
                'format' => 'sql',
                'label' => 'SQL',
            ),
            '---',
            array(
                'format' => 'png',
                'label' => 'PNG',
            ),
        );

        $buttons = [];
        foreach ($exportItems as $item) {
            if (is_array($item)) {
                $icon = CHtml::image(Yii::app()->params['admin_url'] . '/images/export/' . $item['format'] . '.png', $item['format'], array('width' => '16'));
                $buttons[] = array(
                    'label' => $icon . ' ' . $item['label'],
                    'url' => Strings::LINK_TEXT,
                    'linkOptions' => array(
                        'data-type' => $item['format'],
                    )
                );
            } else {
                $buttons[] = $item;
            }
        }

        return $buttons;
    }

}
