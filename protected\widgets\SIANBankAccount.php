<?php

class SIANBankAccount extends CWidget {

    public $id;
    public $model;
    public $attribute;
    public $items = [];
    public $bank_items = [];
    public $type_items = [];
    public $currency_items = [];
    public $advanced = 1;
    public $title = 'Cuentas Bancarias';
    public $name_preffix = 'BankAccount';
    public $limit = 10;
    public $hint = false;
    public $class = '';
    public $class_panel = '';
    public $disabled = false;
    public $readonly = false;
    //PRIVATE
    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;
        //
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //Si hay model seteamos items
        if (isset($this->model, $this->attribute)) {
            $this->items = $this->model->{$this->attribute};
        }

        $a_bankAccount = [];
        foreach ($this->items as $item) {
            $a_attributes = [];
            $a_attributes['entity_multi_id'] = isset($item->entity_multi_id) ? $item->entity_multi_id : '';
            $a_attributes['type'] = isset($item->type) ? $item->type : '';
            $a_attributes['currency'] = isset($item->currency) ? $item->currency : '';
            $a_attributes['account_number'] = isset($item->account_number) ? $item->account_number : '';
            $a_attributes['icc'] = isset($item->icc) ? $item->icc : '';
            $a_attributes['errors'] = $item->getAllErrors();

            $a_bankAccount[] = $a_attributes;
        }

        //Registramos script
        SIANAssets::registerScriptFile('js/sian-bank-account.js');

        Yii::app()->clientScript->registerScript($this->id, "

        //COUNT
        var divObj = $('#{$this->id}');
        divObj.data('count', 0);
        divObj.data('advanced', {$this->advanced});
        divObj.data('name_preffix', '{$this->name_preffix}');
        divObj.data('disabled', " . CJSON::encode($this->disabled) . ");
        divObj.data('readonly', " . CJSON::encode($this->readonly) . ");
        divObj.data('limit', " . CJSON::encode($this->limit) . ");
        divObj.data('bank_items', " . CJSON::encode($this->bank_items) . ");
        divObj.data('currency_items', " . CJSON::encode($this->currency_items) . ");
        divObj.data('type_items', " . CJSON::encode($this->type_items) . ");
              
        var array = " . CJSON::encode($a_bankAccount) . ";
            
        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANBankAccountAddItem('{$this->id}', array[i]['entity_multi_id'], array[i]['type'], array[i]['currency'], array[i]['account_number'], array[i]['icc'], array[i]['errors']);                
            }
        }
        else
        {
            $('#{$this->id}').html('<p>" . Strings::NO_DATA . "</p>');
        }
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->title . ($this->hint ? '*' : ''),
            'headerIcon' => 'envelope',
            'htmlOptions' => array(
                'class' => $this->class_panel . ' ' . ( isset($this->model, $this->attribute) && $this->model->hasErrors($this->attribute) ? 'us-error' : '')
            )
        ));

        echo "<div id='{$this->id}' class='{$this->class}'></div>";
        echo CHtml::link("<span class='fa fa-plus fa-lg black'></span> Agregar nuevo", null, array(
            'onclick' => "SIANBankAccountAddItem('{$this->id}', '', '', '', '', '', [])",
            'title' => 'Agregar'
        ));

        if ($this->hint) {
            echo "<hr>";
            echo "<p>(*) {$this->hint}</p>";
        }
        $this->endWidget();
    }

}
