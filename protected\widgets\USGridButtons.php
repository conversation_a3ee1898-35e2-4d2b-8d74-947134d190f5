<?php

class USGridButtons extends CWidget {

    public $id;
    public $grid_id;
    public $buttons = [];
    public $nullDisplay = Strings::NONE;
    //PRIVATE
    private $controller;
    public $action_menu = false;

    /**
     * Initializes the widget.
     */
    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->normalize($this->id) : null;
    }

    /**
     * Runs the widget.
     */
    public function run() {
        $a_buttons = [];
        foreach ($this->buttons as $btn) {
            $when = isset($btn['when']) ? $btn['when'] : 1;
            $addOption = true;
            if (isset($btn['route']) && $btn['route'] == 'ability') {
                $addOption = Yii::app()->user->getState('level') == User::LEVEL_ROOT;
            }
            if ($when && $addOption) {
                $a_buttons[] = $btn;
            }
        }
        $this->buttons = $a_buttons;

        $a_buttons = [];
        $menuButtons = [];

        if ($this->action_menu == true) {
            $menuButtons = array_slice($this->buttons, 3);
            $this->buttons = array_slice($this->buttons, 0, 3);
        }

        foreach ($this->buttons as $button) {

            $button = $this->normalizeButton($button);
            $params = null;
            if ($this->controller->id == 'scenario') {
                $button['data']['id'] = $this->id;
            } else {
                if (!isset($button['params']['id']) && !isset($button['data']['id'])) {
                    $params = array_merge($button['params'], ['id' => $this->id]);
                } else {
                    $params = $button['params'];
                }
            }

            $a_buttons[] = $this->widget('application.widgets.USLink', array(
                'route' => isset($button['route']) ? $button['route'] : null,
                'url' => isset($button['url']) ? $button['url'] : null,
                'icon' => isset($button['icon']) ? $button['icon'] : null,
                'subicons' => isset($button['subicons']) ? $button['subicons'] : [],
                'color' => isset($button['color']) ? $button['color'] : 'black',
                'label' => isset($button['label']) ? $button['label'] : null,
                'title' => $button['title'],
                'class' => isset($button['class']) ? $button['class'] : null,
                'params' => $params,
                'data' => $button['data'],
                'jsonData' => $button['jsonData'],
                'target' => isset($button['target']) ? $button['target'] : null,
                'confirm' => isset($button['confirm']) ? $button['confirm'] : null,
                'visible' => $button['visible'],
                'onclick' => isset($button['onclick']) ? $button['onclick'] : null,
                    ), true);
        }

        echo "<div style='display:flex; justify-content:center; width:100%;' >";
        if (count($a_buttons) > 0) {
            echo implode('&nbsp', $a_buttons);
        } else {
            echo $this->nullDisplay;
        }

        if (count($menuButtons) > 0) {
            echo Yii::app()->controller->widget('application.widgets.USActionMenu', [
                'id' => $this->id,
                'grid_id' => $this->grid_id,
                'buttons' => $menuButtons
                    ], true);
        }
        echo "</div>";
    }

    private function normalizeButton(array $button) {
        if (!isset($button['visible'])) {
            $button['visible'] = isset($button['route']) ? $this->controller->checkRoute($button['route']) : true;
        }
        $button['params'] = isset($button['params']) ? $button['params'] : [];
        $button['data'] = isset($button['data']) ? $button['data'] : [];
        $button['jsonData'] = isset($button['jsonData']) ? $button['jsonData'] : [];
        $button['data']['type'] = 'grid';
        $button['data']['element_id'] = $this->grid_id;
        return $button;
    }

    private function normalize($id) {
        if (is_array($id)) {
            return (count($id) > 0) ? implode('-', $id) : null;
        } else {
            return USString::unsetBlank($id);
        }
    }

}
