<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'testpuntov.siansystem.com/admin';
$domain = 'https://testpuntov.siansystem.com';
$domain2 = 'http://testpuntov.siansystem.com';
$report_domain = 'rtest.siansystem.com';
$org = 'puntov';
$us = 'us_test';
$database_server = '69.10.37.246';
$database_name = 'puntov_test';
$database_username = 'sian_test';
$database_password = '75nppt6vr57lx4';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'puntov.p12';
$e_billing_certificate_pass = 'TRESTABA2024';
$e_billing_ri = '0620050000126';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20607109169BILLING1',
        'password' => 'Puntovi1234'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_TESTING;
$environment = YII_ENVIRONMENT_TESTING;
