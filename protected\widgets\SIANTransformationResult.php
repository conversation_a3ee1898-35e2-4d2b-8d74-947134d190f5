<?php

class SIANTransformationResult extends CWidget {

    public $id;
    public $form;
    public $model;
    public $readonly = false;
    //IDS
    public $product_id_input_id;
    public $item_type_id_input_id;
    public $product_type_input_id;
    public $product_name_input_id;
    public $pres_quantity_input_id;
    public $equivalence_input_id;
    public $allow_decimals_input_id;
    //PRIVATE
    private $controller;
    private $presentationUrl;
    private $presentationMode;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->presentationMode = SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES;
        $this->presentationUrl = $this->controller->createUrl("/movement/getPresentations");
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->product_id_input_id = isset($this->product_id_input_id) ? $this->product_id_input_id : $this->controller->getServerId();
        $this->item_type_id_input_id = isset($this->item_type_id_input_id) ? $this->item_type_id_input_id : $this->controller->getServerId();
        $this->product_type_input_id = isset($this->product_type_input_id) ? $this->product_type_input_id : $this->controller->getServerId();
        $this->product_name_input_id = isset($this->product_name_input_id) ? $this->product_name_input_id : $this->controller->getServerId();
        $this->pres_quantity_input_id = isset($this->pres_quantity_input_id) ? $this->pres_quantity_input_id : $this->controller->getServerId();
        $this->equivalence_input_id = isset($this->equivalence_input_id) ? $this->equivalence_input_id : $this->controller->getServerId();
        $this->allow_decimals_input_id = isset($this->allow_decimals_input_id) ? $this->allow_decimals_input_id : $this->controller->getServerId();


        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-transformation-result.js');

        Yii::app()->clientScript->registerScript($this->id, "
            
        //EVENTOS
        $(document).ready(function()
        {
            {$this->id}GetPresentations('{$this->model->itemObj->product_id}', '{$this->model->itemObj->equivalence}');
        });  
        
        $('#{$this->id}').data('changeIds', function(ids, pks){
            SIANTransformationResultUpdateIds('{$this->id}', ids);
        });

        function {$this->id}GetPresentations(product_id, equivalence)
        {
            if(product_id != undefined && product_id != '')
            {

                $.ajax({
                    type: 'post',
                    url: '{$this->presentationUrl}',
                    data: {
                        mode: {$this->presentationMode},
                        product_ids: [product_id],
                        currency: '" . $this->controller->getOrganization()->globalVar->display_currency . "'
                    },
                    beforeSend: function (xhr) {
                        window.active_ajax++;
                        //Ocultamos los tooltip
                        $('div.ui-tooltip').remove();
                    },                      
                    success: function(data) {
                    
                        $.each(data, function(index, item) {
                            SIANTransformationResultFillPresentations('{$this->equivalence_input_id}', item, equivalence);
                        });     
                        
                        window.hasPresentation = 1;
                        window.active_ajax--;
                    },
                    error: function(request, status, error) { // if error occured
                        bootbox.alert(request.responseText);
                        window.active_ajax--;
                    },
                    dataType: 'json'
                });
            }
        }
   
      
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {


        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "Producto Resultante",
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => $this->model->hasErrors() ? 'us-error' : ''
            )
        ));

        echo "<div class = 'col-lg-8 col-md-7 col-sm-7 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->product_id_input_id,
            'label' => 'Producto',
            'value' => $this->model->itemObj->product_id,
            'name' => 'Transformed[value]',
            'id_name' => 'Transformed[product_id]',
            'readonly' => $this->readonly,
            'view' => array(
                'model' => 'DpAllMerchandisePresentations',
                'scenario' => DpAllMerchandisePresentations::SCENARIO_WITHOUT_STOCK,
                'attributes' => array(
                    array('name' => 'product_id', 'width' => 10, 'types' => array('id', 'value', 'aux'), 'not_in' => "window.sianTransformationResultIds"),
                    array('name' => 'item_type_id', 'hidden' => true, 'update' => "$('#{$this->item_type_id_input_id}').val(item_type_id)"),
                    array('name' => 'product_type', 'hidden' => true, 'update' => "$('#{$this->product_type_input_id}').val(product_type)"),
                    array('name' => 'barcode', 'width' => 20),
                    array('name' => 'product_name', 'width' => 35, 'types' => array('text'), 'update' => "$('#{$this->product_name_input_id}').val(product_name)"),
                    array('name' => 'equivalence', 'width' => 15, 'update' => "$('#{$this->equivalence_input_id}').val(equivalence)"),
                    array('name' => 'allow_decimals', 'hidden' => true, 'update' => "$('#{$this->allow_decimals_input_id}').val(allow_decimals)"),
                    array('name' => 'measure_name', 'width' => 15),
                )
            ),
            'onselect' => "{$this->id}GetPresentations(product_id, equivalence);",
            'onreset' => "
                        $('#{$this->item_type_id_input_id}').val(null); 
                        $('#{$this->product_type_input_id}').val(null); 
                        $('#{$this->product_name_input_id}').val(null); 
                        $('#{$this->pres_quantity_input_id}').val(1);    
                        $('#{$this->pres_quantity_input_id}').attr('step', 1).attr('min', 1);    
                        $('#{$this->equivalence_input_id}').html('<option value>" . Strings::SELECT_OPTION . "</option>');
                        $('#{$this->allow_decimals_input_id}').val(null); 
                            ",
            'maintenance' => array(
                'module' => 'logistic',
                'controller' => 'product',
                'buttons' => array(
                    'preview' => array(
                    ),
                ),
            )
        ));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->form->numberFieldRow($this->model->itemObj, 'pres_quantity', array(
            'id' => $this->pres_quantity_input_id,
            'name' => 'Transformed[pres_quantity]',
            'class' => 'enterTab',
            'style' => 'text-align:right',
            'readonly' => $this->readonly,
        ));
        echo "</div>";

        echo "<div class = 'col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->itemObj, 'equivalence', array(), array(
            'id' => $this->equivalence_input_id,
            'name' => 'Transformed[equivalence]',
            'empty' => Strings::SELECT_OPTION,
            'readonly' => $this->readonly,
        ));

        //Ocultos
        echo $this->form->hiddenField($this->model->itemObj, 'item_type_id', array(
            'id' => $this->item_type_id_input_id,
            'name' => 'Transformed[item_type_id]',
            'readonly' => $this->readonly,
        ));
        echo $this->form->hiddenField($this->model->itemObj, 'product_type', array(
            'id' => $this->product_type_input_id,
            'name' => 'Transformed[product_type]',
            'readonly' => $this->readonly,
        ));
        echo $this->form->hiddenField($this->model->itemObj, 'product_name', array(
            'id' => $this->product_name_input_id,
            'name' => 'Transformed[product_name]',
            'readonly' => $this->readonly,
        ));
        echo $this->form->hiddenField($this->model->itemObj, 'allow_decimals', array(
            'id' => $this->allow_decimals_input_id,
            'name' => 'Transformed[allow_decimals]',
            'readonly' => $this->readonly,
        ));
        echo "</div>";

        $this->endWidget();
    }

}
