<?php

class SIANCommercialCheckedGrid2 extends SIANCommercialCheckedGrid {

    public $validate_cost = false;
    public $main_title = 'Mercaderías y servicios (Listado principal)';
    public $edit_prices = false;
    public $is_retention_agent = 0;
    public $ability_affected_igv = 0;
    public $mode = CommercialMovement::FORM_MODE_NORMAL;
    //
    private $comboItems = [];
    //Totales
    private $summary_crude_input_id;
    private $summary_advance_affected1_input_id;
    private $summary_affected1_input_id;
    private $summary_advance_affected_input_id;
    private $summary_affected_input_id;
    private $summary_advance_inaffected_input_id;
    private $summary_inaffected_input_id;
    private $summary_advance_nobill_input_id;
    private $summary_nobill_input_id;
    private $summary_export_input_id;
    private $summary_advance_free_input_id;
    private $summary_free_input_id;
    private $summary_nnet_input_id;
    private $summary_fnet_input_id;
    private $summary_net_input_id;
    private $summary_advance_input_id;
    private $summary_isc_input_id;
    private $summary_nigv_input_id;
    private $summary_figv_input_id;
    private $summary_igv_input_id;
    private $summary_ntotal_input_id;
    private $summary_ftotal_input_id;
    private $summary_total_input_id;
    private $summary_perception_input_id;
    private $summary_nreal_input_id;
    private $summary_freal_input_id;
    public $summary_real_input_id;
    public $summary_inet_input_id;
    private $summary_is_editable;
    private $local_storage_name;
    private $summary_advance_percentage_input_id;
    private $summary_advance_amount_input_id;
    private $summary_advance_balance_input_id;
    private $summary_adjust_affected_gain_input_id;
    private $summary_adjust_affected_loss_input_id;
    private $summary_adjust_inaffected_gain_input_id;
    private $summary_adjust_inaffected_loss_input_id;
    private $summary_adjust_gain_input_id;
    private $summary_adjust_loss_input_id;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->summary_crude_input_id = $this->controller->getServerId();
        $this->summary_advance_affected1_input_id = $this->controller->getServerId();
        $this->summary_affected1_input_id = $this->controller->getServerId();
        $this->summary_advance_affected_input_id = $this->controller->getServerId();
        $this->summary_affected_input_id = $this->controller->getServerId();
        $this->summary_advance_inaffected_input_id = $this->controller->getServerId();
        $this->summary_inaffected_input_id = $this->controller->getServerId();
        $this->summary_advance_nobill_input_id = $this->controller->getServerId();
        $this->summary_nobill_input_id = $this->controller->getServerId();
        $this->summary_export_input_id = $this->controller->getServerId();
        $this->summary_advance_free_input_id = $this->controller->getServerId();
        $this->summary_free_input_id = $this->controller->getServerId();
        $this->summary_nnet_input_id = $this->controller->getServerId();
        $this->summary_fnet_input_id = $this->controller->getServerId();
        $this->summary_inet_input_id = $this->controller->getServerId();
        $this->summary_advance_input_id = $this->controller->getServerId();
        $this->summary_net_input_id = $this->controller->getServerId();
        $this->summary_isc_input_id = $this->controller->getServerId();
        $this->summary_nigv_input_id = $this->controller->getServerId();
        $this->summary_figv_input_id = $this->controller->getServerId();
        $this->summary_igv_input_id = $this->controller->getServerId();
        $this->summary_ntotal_input_id = $this->controller->getServerId();
        $this->summary_ftotal_input_id = $this->controller->getServerId();
        $this->summary_total_input_id = $this->controller->getServerId();
        $this->summary_perception_input_id = $this->controller->getServerId();
        $this->summary_nreal_input_id = $this->controller->getServerId();
        $this->summary_freal_input_id = $this->controller->getServerId();
        $this->summary_advance_percentage_input_id = $this->controller->getServerId();
        $this->summary_advance_amount_input_id = $this->controller->getServerId();
        $this->summary_advance_balance_input_id = $this->controller->getServerId();
        $this->summary_adjust_affected_gain_input_id = $this->controller->getServerId();
        $this->summary_adjust_affected_loss_input_id = $this->controller->getServerId();
        $this->summary_adjust_inaffected_gain_input_id = $this->controller->getServerId();
        $this->summary_adjust_inaffected_loss_input_id = $this->controller->getServerId();
        $this->summary_adjust_gain_input_id = $this->controller->getServerId();
        $this->summary_adjust_loss_input_id = $this->controller->getServerId();
        $this->summary_real_input_id = isset($this->summary_real_input_id) ? $this->summary_real_input_id : $this->controller->getServerId();

        $this->summary_is_editable = $this->model->isBuy() || in_array($this->model->movement->route, ['logistic/purchaseOrder', 'logistic/creditNote2', 'logistic/creditNote3']);
        $this->model->totals_editable = $this->summary_is_editable ? 1 : 0;
        $this->local_storage_name = 'datos_' . $this->id;
        //
        $this->grid_number = 2;
        parent::init();
        //CONTROL
        if ($this->model->movement->validate_stock == 1 && !isset($this->model->movement->stock_mode)) {
            throw new Exception('Se necesita un modo de stock');
        }
        //Sise puede editar los precios entonces también se hace dinámico la elección del IGV
        if ($this->edit_prices) {
            $this->igv_affections = CommercialMovementProduct::getIGVAffectionOptions($this->is_commercial_route, $this->as_bill, $this->scope, $this->model->use_inaffected, $this->model->use_free, false);
        }

        //CHECKED ITEMS
        $a_new_products = [];
        foreach ($this->model->tempItems as $o_item) {

            if (USString::isBlank($o_item->itemObj->parent_item_id)) {
                throw new Exception('No está seteado el campo parent_item_id para uno o más ítems que deben ir en la grilla.');
            }
            //Si vienen cargados los items (update, clone o recargar del create) y hay tipos de productos original NEW 
            if (in_array($o_item->itemObj->parent_product_type_original, [Product::TYPE_NEW])) {
                $a_attributes = [];
                $a_attributes['item_id'] = $o_item->itemObj->parent_item_id;
                $a_attributes['product_id'] = $o_item->itemObj->product_id;
                $a_attributes['product_name'] = $o_item->itemObj->product_name;
                $a_attributes['product_type'] = $o_item->itemObj->product_type;
                $a_attributes['equivalence'] = $o_item->itemObj->equivalence;
                $a_attributes['item_type_id'] = $o_item->itemObj->item_type_id;
                $a_attributes['allow_decimals'] = $o_item->itemObj->allow_decimals;
                $a_attributes['max_pres_quantity'] = $o_item->itemObj->max_pres_quantity;
                $a_attributes['max_unit_quantity'] = $o_item->itemObj->max_unit_quantity;
                $a_new_products[$o_item->itemObj->parent_item_id] = $a_attributes;
            }
            $this->checked_items[$o_item->itemObj->parent_item_id] = $o_item;
        }

        //MODEL
        $price_field = $this->model->movement->currency == Currency::PEN ? 'price_pen' : 'price_usd';
        $discount_field = $this->model->movement->currency == Currency::PEN ? 'discount_pen' : 'discount_usd';
        //ITEMS
        $a_product_ids = [];
        $a_combo_ids = [];
        $a_promotion_gift_option_ids = [];

        //Se recorre los items para llenar los arrays de productos, combos.
        foreach ($this->items as $spItem) {

            if (!USString::isBlank($spItem->product_id)) {
                $a_product_ids[] = $spItem->product_id;
            }
            if (!USString::isBlank($spItem->promotion_gift_option_id)) {
                $a_promotion_gift_option_ids[] = $spItem->promotion_gift_option_id;
            }

            switch ($spItem->product_type) {

                case Product::TYPE_COMBO:
                    $a_combo_ids[] = $spItem->product_id;
                    foreach ($spItem->subItems as $a_subItem) {
                        $a_product_ids[] = $a_subItem['product_id'];
                    }
                    break;
                case Product::TYPE_GROUP:
                    foreach ($spItem->subItems as $a_subItem) {
                        $a_product_ids[] = $a_subItem['product_id'];
                    }
                    break;
                case Product::TYPE_NEW:
                    if (isset($a_new_products[$spItem->parent_item_id]) && isset($a_new_products[$spItem->parent_item_id]['product_id'])) {
                        $a_product_ids[] = $a_new_products[$spItem->parent_item_id]['product_id'];
                    }
                    break;
            }
        }
        //Comunes
        $i_store_id = $this->is_commercial_route == 1 ? $this->model->movement->store_id : null;
        $s_emission_date = $this->is_commercial_route == 1 ? $this->model->movement->emission_date : null;
        $i_exclude_id = $this->model->movement->kardex_unlock_exclude_id;
        //
        if (count($a_product_ids) > 0) {
            $this->presentationItems = SpGetProductPresentations::getAssociative($this->presentationMode, $a_product_ids, $this->controller->getOrganization()->globalVar->display_currency, 0, $i_store_id, $s_emission_date, $i_exclude_id);
        }
        if (count($a_combo_ids) > 0) {
            $this->comboItems = DpComboItems::getAssociative(array_unique($a_combo_ids));
        }
        foreach ($this->items as $spItem) {

            $spItem['id'] = $spItem->parent_item_id;

            $xmprice = $this->model->include_igv == 1 ? 'imprice' : 'mprice';
            $xaprice = $this->model->include_igv == 1 ? 'iaprice' : 'aprice';
            //
            $s_equivalence = number_format($spItem->equivalence, 2);
            //Seteamos subitems
            switch ($spItem->product_type) {
                case Product::TYPE_GROUP:
                    //Seteamos precios a cada subitem
                    foreach ($spItem->subItems as $o_subItem) {
                        $a_subPresentationOption = $this->presentationItems[$o_subItem->product_id][number_format($o_subItem->equivalence, 2)];
                        $this->_processItem($o_subItem, $a_subPresentationOption, $xmprice, $xaprice);
                    }
                    break;
                case Product::TYPE_COMBO:
                    $a_presentationOption = $this->presentationItems[$spItem->product_id][$s_equivalence];
                    $this->_processItem($spItem, $a_presentationOption, $xmprice, $xaprice);
                    //Seteamos precios a cada subitem
                    foreach ($spItem->subItems as $o_subItem) {
                        $a_subPresentationOption = $this->comboItems[$spItem->product_id][$o_subItem->product_id][$s_equivalence];
                        $this->_processItem($o_subItem, $a_subPresentationOption, $xmprice, $xaprice);
                    }
                    break;
                case Product::TYPE_NEW:
                    $a_presentationOption = [];
                    if (isset($a_new_products[$spItem->parent_item_id])) {

                        $a_new = $a_new_products[$spItem->parent_item_id];
                        $spItem->product_id = isset($a_new['product_id']) ? $a_new['product_id'] : $spItem->product_id;
                        $spItem->product_name = isset($a_new['product_name']) ? $a_new['product_name'] : $spItem->product_name;
                        $spItem->product_type = isset($a_new['product_type']) ? $a_new['product_type'] : $spItem->product_type;
                        $spItem->item_type_id = isset($a_new['item_type_id']) ? $a_new['item_type_id'] : $spItem->item_type_id;
                        $spItem->allow_decimals = isset($a_new['allow_decimals']) ? $a_new['allow_decimals'] : $spItem->allow_decimals;
                        $spItem->max_pres_quantity = isset($a_new['max_pres_quantity']) ? $a_new['max_pres_quantity'] : $spItem->max_pres_quantity;
                        $spItem->max_unit_quantity = isset($a_new['max_unit_quantity']) ? $a_new['max_unit_quantity'] : $spItem->max_unit_quantity;
                    }
                    $this->_processItem($spItem, $a_presentationOption, $xmprice, $xaprice);
                    break;
                default:
                    $a_presentationOption = $this->presentationItems[$spItem->product_id][$s_equivalence];
                    $this->_processItem($spItem, $a_presentationOption, $xmprice, $xaprice);
                    break;
            }

            if (isset($this->checked_items[$spItem->parent_item_id])) {
                $spItem->checked = true;
                $spItem->equivalence = $this->checked_items[$spItem->parent_item_id]->itemObj->equivalence;
                $spItem->pres_quantity = $this->checked_items[$spItem->parent_item_id]->itemObj->pres_quantity;
                $spItem->igv_affection = $this->checked_items[$spItem->parent_item_id]->igv_affection;
                $spItem->igv_percentage = $this->checked_items[$spItem->parent_item_id]->igv_percentage;
                $spItem->price = number_format($this->checked_items[$spItem->parent_item_id]->{$price_field}, $this->ifixed, '.', '');
                $spItem->discount = number_format($this->checked_items[$spItem->parent_item_id]->{$discount_field}, $this->ifixed, '.', '');

                $spItem->max_price_pen = number_format($this->checked_items[$spItem->parent_item_id]->max_price_pen, $this->ifixed, '.', '');
                $spItem->max_price_usd = number_format($this->checked_items[$spItem->parent_item_id]->max_price_usd, $this->ifixed, '.', '');
                $spItem->max_discount_pen = number_format($this->checked_items[$spItem->parent_item_id]->max_discount_pen, $this->ifixed, '.', '');
                $spItem->max_discount_usd = number_format($this->checked_items[$spItem->parent_item_id]->max_discount_usd, $this->ifixed, '.', '');

                if ($this->checked_items[$spItem->parent_item_id]->hasErrors('product_id')) {
                    $spItem->addError('product_id', $this->checked_items[$spItem->parent_item_id]->getError('product_id'));
                }

                if ($this->checked_items[$spItem->parent_item_id]->hasErrors('igv_affection')) {
                    $spItem->addError('igv_affection', $this->checked_items[$spItem->parent_item_id]->getError('igv_affection'));
                }

                if ($this->checked_items[$spItem->parent_item_id]->hasErrors('igv_percentage')) {
                    $spItem->addError('igv_percentage', $this->checked_items[$spItem->parent_item_id]->getError('igv_percentage'));
                }

                if ($this->checked_items[$spItem->parent_item_id]->itemObj->hasErrors('equivalence')) {
                    $spItem->addError('equivalence', $this->checked_items[$spItem->parent_item_id]->itemObj->getError('equivalence'));
                }

                if ($this->checked_items[$spItem->parent_item_id]->itemObj->hasErrors('unit_quantity')) {
                    $spItem->addError('unit_quantity', $this->checked_items[$spItem->parent_item_id]->itemObj->getError('unit_quantity'));
                }

                if ($this->checked_items[$spItem->parent_item_id]->itemObj->hasErrors('pres_quantity')) {
                    $spItem->addError('pres_quantity', $this->checked_items[$spItem->parent_item_id]->itemObj->getError('pres_quantity'));
                }

                if ($this->checked_items[$spItem->parent_item_id]->hasErrors($price_field)) {
                    $spItem->addError('price', $this->checked_items[$spItem->parent_item_id]->getError($price_field));
                }

                if ($this->checked_items[$spItem->parent_item_id]->hasErrors($discount_field)) {
                    $spItem->addError('discount', $this->checked_items[$spItem->parent_item_id]->getError($discount_field));
                }

                //Si es un grupo
                if (in_array($spItem->product_type, [Product::TYPE_COMBO, Product::TYPE_GROUP])) {
                    foreach ($this->checked_items[$spItem->parent_item_id]->tempItems as $o_subItem) {

                        if ($o_subItem->hasErrors('igv_affection')) {
                            $spItem->subItems[$o_subItem->itemObj->parent_item_id]->addError('igv_affection', $o_subItem->getError('igv_affection'));
                        }

                        if ($o_subItem->hasErrors('igv_percentage')) {
                            $spItem->subItems[$o_subItem->itemObj->parent_item_id]->addError('igv_percentage', $o_subItem->getError('igv_percentage'));
                        }

                        if ($o_subItem->itemObj->hasErrors('unit_quantity')) {
                            $spItem->subItems[$o_subItem->itemObj->parent_item_id]->addError('unit_quantity', $o_subItem->itemObj->getError('unit_quantity'));
                        }

                        if ($o_subItem->itemObj->hasErrors('pres_quantity')) {
                            $spItem->subItems[$o_subItem->itemObj->parent_item_id]->addError('pres_quantity', $o_subItem->itemObj->getError('pres_quantity'));
                        }

                        if ($o_subItem->itemObj->hasErrors('equivalence')) {
                            $spItem->subItems[$o_subItem->itemObj->parent_item_id]->addError('equivalence', $o_subItem->itemObj->getError('equivalence'));
                        }

                        if ($o_subItem->hasErrors($price_field)) {
                            $spItem->subItems[$o_subItem->itemObj->parent_item_id]->addError('price', $o_subItem->getError($price_field));
                        }

                        if ($o_subItem->hasErrors($discount_field)) {
                            $spItem->subItems[$o_subItem->itemObj->parent_item_id]->addError('discount', $o_subItem->getError($discount_field));
                        }

                        //Si hay errores en subitems, se seteará un error en el nombre del producto
                        if ($o_subItem->itemObj->hasErrors('unit_quantity') || $o_subItem->itemObj->hasErrors('pres_quantity') || $o_subItem->itemObj->hasErrors('equivalence') || $o_subItem->hasErrors($price_field) || $o_subItem->hasErrors($discount_field)) {
                            $spItem->addError('product_name', $this->checked_items[$spItem->parent_item_id]->getError('tempItems'));
                            $spItem->addError('subItems', $this->checked_items[$spItem->parent_item_id]->getError('tempItems'));
                        }
                    }
                }
            } else {
                $spItem->checked = false;
                $spItem->unit_stock = 0;
                $spItem->loadMax($this->model->movement->exchange_rate);
            }
        }

        if ($this->summary_is_editable) {
            if (!isset($this->model->max_affected1) || $this->model->max_affected1 == 0) {
                $this->model->max_affected1 = ($this->model->movement->currency == Currency::PEN ? $this->model->affected1_pen : $this->model->affected1_usd) + $this->tolerance;
            }
            if (!isset($this->model->min_affected1) || $this->model->min_affected1 == 0) {
                $this->model->min_affected1 = ($this->model->movement->currency == Currency::PEN ? $this->model->affected1_pen : $this->model->affected1_usd) - $this->tolerance;
                $this->model->min_affected1 = ($this->model->min_affected1 < 0 ? 0 : $this->model->min_affected1);
            }
            if (!isset($this->model->max_inaffected) || $this->model->max_inaffected == 0) {
                $this->model->max_inaffected = ($this->model->movement->currency == Currency::PEN ? $this->model->inaffected_pen : $this->model->inaffected_usd) + $this->tolerance;
            }
            if (!isset($this->model->min_inaffected) || $this->model->min_inaffected == 0) {
                $this->model->min_inaffected = ($this->model->movement->currency == Currency::PEN ? $this->model->inaffected_pen : $this->model->inaffected_usd) - $this->tolerance;
                $this->model->min_inaffected = ($this->model->min_inaffected < 0 ? 0 : $this->model->min_inaffected);
            }
            if (!isset($this->model->max_nobill) || $this->model->max_nobill == 0) {
                $this->model->max_nobill = ($this->model->movement->currency == Currency::PEN ? $this->model->nobill_pen : $this->model->nobill_usd) + $this->tolerance;
            }
            if (!isset($this->model->min_nobill) || $this->model->min_nobill == 0) {
                $this->model->min_nobill = ($this->model->movement->currency == Currency::PEN ? $this->model->nobill_pen : $this->model->nobill_usd) - $this->tolerance;
                $this->model->min_nobill = ($this->model->min_nobill < 0 ? 0 : $this->model->min_nobill);
            }
            if (!isset($this->model->max_igv) || $this->model->max_igv == 0) {
                $this->model->max_igv = ($this->model->movement->currency == Currency::PEN ? $this->model->igv_pen : $this->model->igv_usd) + $this->tolerance;
            }
            if (!isset($this->model->min_igv) || $this->model->min_igv == 0) {
                $this->model->min_igv = ($this->model->movement->currency == Currency::PEN ? $this->model->igv_pen : $this->model->igv_usd) - $this->tolerance;
                $this->model->min_igv = ($this->model->min_igv < 0 ? 0 : $this->model->min_igv);
            }
            if (!isset($this->model->max_perception) || $this->model->max_perception == 0) {
                $this->model->max_perception = ($this->model->movement->currency == Currency::PEN ? $this->model->perception_pen : $this->model->perception_usd) + $this->tolerance;
            }
            if (!isset($this->model->min_perception) || $this->model->min_perception == 0) {
                $this->model->min_perception = ($this->model->movement->currency == Currency::PEN ? $this->model->perception_pen : $this->model->perception_usd) - $this->tolerance;
                $this->model->min_perception = ($this->model->min_perception < 0 ? 0 : $this->model->min_perception);
            }
        }


        //DATA PROVIDER
        $this->dataProvider = new USArrayDataProvider($this->items, array(
            'keyField' => array('parent_item_id'), // PRIMARY KEY
            'sort' => false,
        ));
        $this->dataProvider->pagination = false;

        //ACCESS
        $this->preview_access = $this->controller->checkRoute('/logistic/product/preview');

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-commercial-checked-grid-2.js');


        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        var grid = $('#{$this->id}');
        grid.data('include_igv', $('#{$this->include_igv_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('as_bill', $('#{$this->as_bill_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('force_igv', $('#{$this->force_igv_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('allow_duplicate', $('#{$this->allow_duplicate_checkbox_id}').prop('checked') ? 1 : 0);
        //
        grid.data('retention_input_id', '{$this->retention_checkbox_id}');
        grid.data('detraction_input_id', '{$this->detraction_checkbox_id}');
        grid.data('retention', $('#{$this->retention_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('detraction', $('#{$this->detraction_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('retention_global', {$this->controller->getOrganization()->globalVar->retention});
        grid.data('retention4', {$this->controller->getOrganization()->globalVar->retention4});
        grid.data('detraction_global', '{$this->controller->getOrganization()->globalVar->detraction}');
        grid.data('no_retention_max_amount_pen', {$this->controller->getOrganization()->globalVar->no_retention_max_amount_pen});
        grid.data('no_retention_max_amount_usd', {$this->controller->getOrganization()->globalVar->no_retention_max_amount_usd});
        grid.data('no_detraction_max_amount_pen', {$this->controller->getOrganization()->globalVar->no_detraction_max_amount_pen});
        grid.data('no_detraction_max_amount_usd', {$this->controller->getOrganization()->globalVar->no_detraction_max_amount_usd});            
        grid.data('use_purchase_adjust', {$this->controller->getOrganization()->globalVar->use_purchase_adjust});            
        grid.data('is_retention_agent', {$this->is_retention_agent});
        grid.data('ability_affected_igv', {$this->ability_affected_igv});
        grid.data('retention_allowed_document', 0);
        grid.data('detraction_allowed_document', 0);
        grid.data('amount_allow_retention', 0);
        grid.data('amount_allow_detraction', 0);
        //
        grid.data('is_commercial_route', " . CJSON::encode($this->model->movement->scenario->isCommercialRoute()) . ");
        grid.data('scope', '{$this->model->movement->scope}');
        grid.data('is_buy', " . CJSON::encode($this->model->isBuy()) . ");
        grid.data('is_sale', " . CJSON::encode($this->model->isSale()) . ");
        grid.data('summary_is_editable', " . CJSON::encode($this->summary_is_editable) . ");
        grid.data('ifixed', {$this->ifixed});
        grid.data('tfixed', {$this->tfixed});
        grid.data('tolerance', {$this->tolerance});
        grid.data('igv_global', " . $this->controller->getOrganization()->globalVar->igv . ");
        grid.data('perception_global', " . $this->controller->getOrganization()->globalVar->perception . ");        
        grid.data('currency', '{$this->model->movement->currency}');
        grid.data('exchange_rate', '{$this->model->movement->exchange_rate}');
        grid.data('round_mode', {$this->model->round_mode});
        grid.data('stockUrl', '{$this->controller->createUrl("/movement/loadCostsAndStocks")}');
        grid.data('commercialStockUrl', '{$this->controller->createUrl("/movement/loadCommercialStocks")}');
        grid.data('stock_mode', " . CJSON::encode($this->model->movement->stock_mode) . ");
        grid.data('exclude_id', " . CJSON::encode($this->model->movement->kardex_unlock_exclude_id) . ");            
        grid.data('validate_stock', " . CJSON::encode($this->model->movement->validate_stock == 1) . ");
        grid.data('validate_cost', " . CJSON::encode($this->validate_cost) . ");
        grid.data('edit_prices', " . CJSON::encode($this->edit_prices) . ");
        grid.data('main_title', '{$this->main_title}');
        grid.data('direction', '{$this->model->movement->scenario->direction}');
        grid.data('submit_id', '{$this->form->submit_id}');
        grid.data('independent_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INDEPENDENT . "');
        grid.data('inherit_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INHERIT . "');
        grid.data('use_inaffected', " . CJSON::encode($this->model->use_inaffected) . ");
        grid.data('use_free', " . CJSON::encode($this->model->use_free) . ");
        grid.data('summary_id', '{$this->summary_id}');
        grid.data('modifiers', " . CJSON::encode(Document::getModifiers()) . ");
        grid.data('detraction_items', " . CJSON::encode($this->detraction_items) . ");
        grid.data('detraction_code', " . CJSON::encode($this->model->detraction_code) . ");
        grid.data('detraction_value', " . CJSON::encode($this->model->detraction_percent) . ");
        grid.data('set_summary_total', " . ($this->summary_is_editable ? '0' : '1') . ");
        grid.data('local_storage_name', '{$this->local_storage_name}');
        grid.data('new_products', " . CJSON::encode($a_new_products) . "); 
        //CHANGES
        grid.data('has_operation_changed', false);
        
        $(document).ready(function() {    

            //CURRENCY SYMBOL
            $('span.currency-symbol').text('" . Currency::getSymbol($this->model->movement->currency) . "');
            //Generamos cambios      
            SIANCommercialCheckedGrid2Calculate('{$this->id}');
            $('#{$this->id}').data('set_summary_total', 1);
            SIANCommercialCheckedGrid2Load('{$this->id}');            
        });

        $('#{$this->summary_advance_amount_input_id}').change(function() {
            var value = parseFloat($(this).val());
            var total_value = parseFloat($('#{$this->summary_real_input_id}').val());

            if(value > total_value){
                $(this).val(total_value)
            }

            var new_value = parseFloat($(this).val())

            var percentage = (new_value / total_value) * 100;

            $('#{$this->summary_advance_percentage_input_id}').val(isNaN(percentage) ? 0 : percentage.toFixed(2));
        });

        $('#{$this->summary_advance_percentage_input_id}').change(function() {
            
            var percentage = parseFloat($(this).val());
            var total_value = parseFloat($('#{$this->summary_real_input_id}').val());
            var new_value = parseFloat($(this).val());
            var result = 0;

            if(percentage <= 100){
                result = (total_value * percentage) / 100;
            } else {
                result = total_value;
            }

            $('#{$this->summary_advance_amount_input_id}').val(isNaN(result) ? 0 : result.toFixed(2));
        });
        
        //SI CAMBIA OPERACION
        $('#{$this->id}').data('changeOperation', function(changeData){
            
            var divObj = $('#{$this->id}');
            //GUARDAMOS INFO DE OPERACIÓN
            divObj.data('operationChangeData', changeData);
            SIANCommercialCheckedGrid2Calculate('{$this->id}');
        });
        
        $('#{$this->id}').data('changeCCDependence', function(operationChangeData){
            {$this->onChangeCCDependence};
        });

        //SI CAMBIA LA MONEDA
        $('#{$this->id}').data('changeCurrency', function(currency){
            //GET
            var divObj = $('#{$this->id}');

            //Cambiamos la moneda
            SIANCommercialCheckedGrid2ChangeCurrency('{$this->id}', currency);

            //SET
            divObj.data('currency', currency);
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            SIANCommercialCheckedGrid2Calculate('{$this->id}');
        });    

        $('#{$this->id}').data('changeDocument', function(changeData){
            var divObj = $('#{$this->id}');
            var scope = divObj.data('scope');
            var is_commercial_route = divObj.data('is_commercial_route');
            var modifiers = divObj.data('modifiers');                
            " .
                ($this->model->isBuy() ? "
            //Retención: Desactivamos y seteamos data de sunat
            $('#{$this->retention_checkbox_id}').data('sunat_code', changeData.sunat_code);
            //
            if(scope == '" . Scenario::SCOPE_NOT_DOMICILED . "' || changeData.sunat_code == '" . Document::HONORARIOS . "')
            {
                $('#{$this->id}').data('retention_allowed_document', 1);
                SIANCommercialCheckedGrid2EnableOrDisabledRetentionDetraction('{$this->id}');
            }else{
                $('#{$this->id}').data('retention_allowed_document', 0);
                $('#{$this->retention_checkbox_id}').prop('checked', false);
                SIANCommercialCheckedGrid2EnableOrDisabledRetentionDetraction('{$this->id}');
            }            
            //Detracción: Desactivamos y seteamos data de sunat
            $('#{$this->detraction_checkbox_id}').data('sunat_code', changeData.sunat_code);
            if(changeData.sunat_code == '" . Document::FACTURA . "')
            {
                $('#{$this->id}').data('detraction_allowed_document', 1);
                SIANCommercialCheckedGrid2EnableOrDisabledRetentionDetraction('{$this->id}');
            } 
            else 
            {
                $('#{$this->id}').data('detraction_allowed_document', 0);
                $('#{$this->detraction_checkbox_id}').prop('checked', false);
                SIANCommercialCheckedGrid2EnableOrDisabledRetentionDetraction('{$this->id}');
            }  
            " : "") .
                ($this->model->isSale() ? "
            //Desactivamos y seteamos data de sunat
            $('#{$this->retention_checkbox_id}').data('sunat_code', changeData.sunat_code);         
            if(changeData.sunat_code == '" . Document::FACTURA . "')
            {
                $('#{$this->id}').data('retention_allowed_document', 1);
                SIANCommercialCheckedGrid2EnableOrDisabledRetentionDetraction('{$this->id}');
            } 
            else{ 
                $('#{$this->id}').data('retention_allowed_document', 0);
                $('#{$this->retention_checkbox_id}').prop('checked', false);
                SIANCommercialCheckedGrid2EnableOrDisabledRetentionDetraction('{$this->id}');
            }   
            
            //Desactivamos y seteamos data de sunat
            $('#{$this->detraction_checkbox_id}').data('sunat_code', changeData.sunat_code);
            if(changeData.sunat_code == '" . Document::FACTURA . "')
            {
                $('#{$this->id}').data('detraction_allowed_document', 1);
                SIANCommercialCheckedGrid2EnableOrDisabledRetentionDetraction('{$this->id}');
            } 
            else 
            {
                $('#{$this->id}').data('detraction_allowed_document', 0);
                $('#{$this->detraction_checkbox_id}').prop('checked', false);
                SIANCommercialCheckedGrid2EnableOrDisabledRetentionDetraction('{$this->id}');
            }            
            " : "") .
                "SIANCommercialCheckedGrid2Calculate('{$this->id}');
            var as_bill_readonly = is_commercial_route || jQuery.inArray(changeData.sunat_code, ['" . Document::TICKET . "']) == -1;

            $('#{$this->as_bill_checkbox_id}').attr('readonly', as_bill_readonly);

            if(as_bill_readonly){

                if(!jQuery.inArray(changeData.sunat_code, modifiers))
                {
                    $('#{$this->as_bill_checkbox_id}').prop('checked', is_commercial_route || jQuery.inArray(changeData.sunat_code, ['" . Document::BOLETA . "', '" . Document::BOLETO . "', '" . Document::HONORARIOS . "']) == -1).change();            
                }

            } else {

                $.notify('Se activó la casilla \'Crédito fiscal\'.', {
                    className: 'info',
                    showDuration: 30,
                    hideDuration: 50,
                    autoHideDelay: 1000,
                }); 
            }
        }); 
        
        $('#{$this->id}').data('changeStore', function(changeData){
            //
            var divObj = $('#{$this->id}');
            //Seteamos
            divObj.data('store_id', changeData.store_id);
        });
        
        $('#{$this->id}').data('changeEmission', function(changeData){
            var grid = $('#{$this->id}');
            //Obtenemos
            var warehouse_id = $('#{$this->warehouse_input_id}').val();
            var validate_stock = grid.data('validate_stock');
            var validate_cost = grid.data('validate_cost');

            //Seteamos
            grid.data('emission_date', changeData.emission_date);
            grid.find('a.sian-commercial-checked-grid-2-item-mixed-stock').data('emission_date', changeData.emission_date); 
            grid.find('a.sian-commercial-checked-grid-2-subitem-mixed-stock').data('emission_date', changeData.emission_date); 
            //
            if(warehouse_id !== undefined && warehouse_id.length > 0)
            {
                if(validate_cost)
                {
                    SIANCommercialCheckedGrid2LoadCosts(warehouse_id, changeData.emission_date, '{$this->id}', validate_stock);     
                }
                else
                {
                    //Si hay validación de stock
                    if(validate_stock)
                    {
                        SIANCommercialCheckedGrid2LoadStocks(changeData.emission_date, '{$this->id}', true);     
                    }
                    SIANCommercialCheckedGrid2Calculate('{$this->id}');
                }
            }
        });

        $('#{$this->id}').data('changeExchange', function(exchange_rate){

            var divObj = $('#{$this->id}');
            //Obtenemos
            SIANCommercialCheckedGrid2ChangeExchange('{$this->id}', exchange_rate);
            //Seteamos TC
            divObj.data('exchange_rate', exchange_rate); 
            //Actualizamos montos
            SIANCommercialCheckedGrid2Calculate('{$this->id}');
        });
        
        $('#{$this->id}').data('changeWarehouse', function(changeData){
            var grid = $('#{$this->id}');
            //Obtenemos
            var emission_date = grid.data('emission_date');
            var validate_stock = grid.data('validate_stock');
            var validate_cost = grid.data('validate_cost');
            //Seteamos
            $('#{$this->warehouse_input_id}').val(changeData.warehouse_id);
            //
            if(changeData.warehouse_id !== 'undefined' && changeData.warehouse_id.length > 0 && emission_date)
            {
                if(validate_cost)
                {
                    SIANCommercialCheckedGrid2LoadCosts(changeData.warehouse_id, emission_date, '{$this->id}', false);                
                }
                else
                {
                    SIANCommercialCheckedGrid2Calculate('{$this->id}');
                }
            }           
        });

        $('body').on('change', '#{$this->id} select.sian-commercial-checked-grid-2-item-equivalence', function(e) {
            
            var grid = $('#{$this->id}');
            var validate_stock = grid.data('validate_stock');
            var emission_date = grid.data('emission_date');

            var selectObj = $(this);
            var row = $(this).closest('tr');
     
            var maxUnitQuantityInput = row.find('input.sian-commercial-checked-grid-2-item-max-unit-quantity');
            var presQuantityInput = row.find('input.sian-commercial-checked-grid-2-item-pres-quantity');
            var igvAffectionObj = row.find('select.sian-commercial-checked-grid-2-item-igv-affection');
            presQuantityInput.floatAttr('max', 2, maxUnitQuantityInput.double2() / selectObj.double2());

            SIANCommercialCheckedGrid2SetPrices('{$this->id}', selectObj, igvAffectionObj);
            
            selectObj.floatData('equivalence', 2, selectObj.double2());

            //Actualizar montos
            SIANCommercialCheckedGrid2Calculate('{$this->id}');
            //SIANCommercialCheckedGrid2Calculate stocks
            if(validate_stock) {
                SIANCommercialCheckedGrid2LoadStock(emission_date, '{$this->id}', row, true);
            }            
       });
       
        $('body').on('change', '#{$this->retention_checkbox_id}', function(e) {
            if(window.documents_loaded)
            {
                //consoleLog('Se entró al evento onchange de retention pues ya terminaron de cargar los documentos');
                var current = $(this).prop('checked') ? 1 : 0;
                $('#{$this->id}').data('retention', current);
                    
                //Aquí empieza            
                var divObj = $('#{$this->id}');
                var retention_global = divObj.data('retention_global');
                var retention4 = divObj.data('retention4');        
                var elementObj = $(this);            
                var sunat_code = elementObj.data('sunat_code');
                var retention = elementObj.prop('checked') ? 1 : 0;
                //
//              if(retention != elementObj.data('retention'))
//              {  
                    SIANCommercialCheckedGrid2ActivateRetention();
                    SIANCommercialCheckedGrid2Calculate('{$this->id}');
                    SIANCommercialCheckedGrid2EnableOrDisabledRetentionDetraction('{$this->id}');
                    $(this).data('last', retention);
//              }                
            }
            else
            {
                //consoleLog('Se intentó cambiar retention pero aún no terminaban de cargar los documentos');
            }
        });
        
        $('body').on('change', '#{$this->detraction_checkbox_id}', function(e) {
            if(window.documents_loaded)
            {
                //consoleLog('Se entró al evento onchange de detraction pues ya terminaron de cargar los documentos');
                var current = $(this).prop('checked') ? 1 : 0;
                $('#{$this->id}').data('detraction', current);
                    
                //Aquí empieza
                var divObj = $('#{$this->id}');
                var detraction_global = divObj.data('detraction_global');
                var elementObj = $(this);            
                var sunat_code = elementObj.data('sunat_code');                
                var detraction = elementObj.prop('checked') ? 1 : 0;

                if(detraction != elementObj.data('detraction'))
                {  
                    SIANCommercialCheckedGrid2ActivateDetraction();
                    SIANCommercialCheckedGrid2Calculate('{$this->id}');
                    SIANCommercialCheckedGrid2EnableOrDisabledRetentionDetraction('{$this->id}');
                    $('#{$this->detraction_percent_field_id}').change();
                    $(this).data('last', detraction);
                }
            }
            else
            {
                //consoleLog('Se intentó cambiar detracción pero aún no terminaban de cargar los documentos');
            }
        });
        
        $('body').on('change', '#{$this->summary_isc_input_id}, #{$this->summary_adjust_affected_gain_input_id}, #{$this->summary_adjust_affected_loss_input_id}, #{$this->summary_adjust_inaffected_gain_input_id}, #{$this->summary_adjust_inaffected_loss_input_id}', function(e) {

            if(this.value == undefined || isNaN(this.value) || this.value < 0){
                $(this).Val(0);
                SIANCommercialCheckedGrid2Calculate('{$this->id}');
            }else{
                SIANCommercialCheckedGrid2Calculate('{$this->id}');
            }               
        });
                  
        $('body').on('change', '#{$this->summary_affected1_input_id}, #{$this->summary_inaffected_input_id}, #{$this->summary_nobill_input_id}, #{$this->summary_igv_input_id}, #{$this->summary_perception_input_id}', function(e) {
            SIANCommercialCheckedGrid2CalculateTotals();
        });
        
        function SIANCommercialCheckedGrid2CalculateTotals(){
        
            var divObj = $('#{$this->id}');
            var include_igv = divObj.data('include_igv');
            var igv_global = divObj.data('igv_global');
            var round_mode = divObj.data('round_mode');
            var tolerance = divObj.data('tolerance');
            
            var element_id = $(this).attr('id');
            
            var summary_inaffected = $('#{$this->summary_inaffected_input_id}').double2();
            var summary_advance_inaffected = $('#{$this->summary_advance_inaffected_input_id}').double2();
            var summary_net_inaffected = summary_inaffected - summary_advance_inaffected; 
            
            var summary_nobill = $('#{$this->summary_nobill_input_id}').double2();
            var summary_advance_nobill = $('#{$this->summary_advance_nobill_input_id}').double2();
            var summary_net_nobill = summary_nobill - summary_advance_nobill; 
            
            var summary_free = $('#{$this->summary_free_input_id}').double2();
            var summary_advance_free = $('#{$this->summary_advance_free_input_id}').double2();
            var summary_net_free = summary_free - summary_advance_free;

            var summary_igv = $('#{$this->summary_igv_input_id}').double2();
            var summary_perception = $('#{$this->summary_perception_input_id}').double2();            
            
            var summary_affected1 = $('#{$this->summary_affected1_input_id}').double2();
            var summary_advance_affected1 = $('#{$this->summary_advance_affected1_input_id}').double2();
            var summary_net_affected = summary_affected1 - summary_advance_affected1;


            var summary_isc = $('#{$this->summary_isc_input_id}').double2();
            var summary_adjust_affected_loss = $('#{$this->summary_adjust_affected_loss_input_id}').double2();
            var summary_adjust_affected_gain = $('#{$this->summary_adjust_affected_gain_input_id}').double2();
            var summary_adjust_inaffected_loss = $('#{$this->summary_adjust_inaffected_loss_input_id}').double2();
            var summary_adjust_inaffected_gain = $('#{$this->summary_adjust_inaffected_gain_input_id}').double2();
            
            var summary_advance = $('#{$this->summary_advance_input_id}').double2();
            
            if (summary_isc == undefined || isNaN(summary_isc) || summary_isc < 0) {
                summary_isc = 0;
            }
            if (summary_adjust_affected_loss == undefined || isNaN(summary_adjust_affected_loss) || summary_adjust_affected_loss < 0) {
                summary_adjust_affected_loss = 0;
            } 
            if (summary_adjust_affected_gain == undefined || isNaN(summary_adjust_affected_gain) || summary_adjust_affected_gain < 0) {
                summary_adjust_affected_gain = 0;
            } 
            if (summary_adjust_inaffected_loss == undefined || isNaN(summary_adjust_inaffected_loss) || summary_adjust_inaffected_loss < 0) {
                summary_adjust_inaffected_loss = 0;
            } 
            if (summary_adjust_inaffected_gain == undefined || isNaN(summary_adjust_inaffected_gain) || summary_adjust_inaffected_gain < 0) {
                summary_adjust_inaffected_gain = 0;
            } 
            if (summary_perception == undefined || isNaN(summary_perception) || summary_perception < 0) {
                summary_perception = 0;
            }            
            var summary_igv = 0;
            var summary_nnet = summary_net_affected + summary_net_inaffected + summary_net_nobill + summary_net_free;
            var summary_fnet = summary_net_free;
            var summary_inet = summary_affected1 + summary_inaffected + summary_nobill;
            var summary_net = summary_inet - summary_advance;
            var summary_affected = summary_net_affected;
            
            if( element_id == '{$this->summary_affected1_input_id}'){
                var isc_igv = USMath.round(USMath.multiply(igv_global, summary_isc), {$this->tfixed}, round_mode);
                var adjust_igv = USMath.round(USMath.multiply(igv_global, USMath.minus(summary_adjust_affected_loss, summary_adjust_affected_gain)), {$this->tfixed}, round_mode);
                var igv = USMath.round(USMath.multiply(igv_global, summary_net_affected), {$this->tfixed}, round_mode);
                summary_igv  = igv + isc_igv + adjust_igv;
            }else if(summary_advance_affected1 > 0){
                summary_igv = USMath.round(USMath.multiply(igv_global, summary_net_affected), {$this->tfixed}, round_mode);
            }else{
                    summary_igv = $('#{$this->summary_igv_input_id}').double2();
            }
            var summary_adjust_loss = summary_adjust_affected_loss + summary_adjust_inaffected_loss;
            var summary_adjust_gain = summary_adjust_affected_gain + summary_adjust_inaffected_gain;
            var summary_nigv = summary_igv;
            var summary_figv = 0;              
            var summary_ntotal = summary_nnet + summary_isc + summary_nigv + summary_adjust_loss - summary_adjust_gain;
            var summary_ftotal = summary_fnet + summary_figv;
            var summary_total  = summary_net  + summary_isc + summary_igv + summary_adjust_loss - summary_adjust_gain;            
            var summary_nreal  = summary_total + summary_perception;
            var summary_freal  = summary_ftotal;
            var summary_real   = summary_nreal;            
            var summary_crude = 0;
            var summary_advance_amount = parseFloat($('#{$this->summary_advance_amount_input_id}').val());
            
            if(include_igv == 1){
                summary_crude = summary_total;
            }else{
                summary_crude = summary_net;
            }

            var new_sumary_advance_percentage = (summary_advance_amount / summary_real) * 100
            new_sumary_advance_percentage = isNaN(new_sumary_advance_percentage) ? 0 : new_sumary_advance_percentage;
            
            // if(element_id == '{$this->summary_affected1_input_id}'){                
            //     }
            
            $('#{$this->summary_affected_input_id}').floatVal({$this->tfixed}, summary_net_affected);            
            $('#{$this->summary_inaffected_input_id}').floatVal({$this->tfixed}, summary_net_inaffected);            
            $('#{$this->summary_nobill_input_id}').floatVal({$this->tfixed}, summary_net_nobill);            
            $('#{$this->summary_free_input_id}').floatVal({$this->tfixed}, summary_net_free);            
            
            $('#{$this->summary_affected1_input_id}').floatVal({$this->tfixed}, summary_affected);
            $('#{$this->summary_crude_input_id}').floatVal({$this->tfixed}, summary_crude);
            $('#{$this->summary_affected_input_id}').floatVal({$this->tfixed}, summary_affected);            
            $('#{$this->summary_nnet_input_id}').floatVal({$this->tfixed}, summary_nnet);
            $('#{$this->summary_fnet_input_id}').floatVal({$this->tfixed}, summary_fnet);
            $('#{$this->summary_inet_input_id}').floatVal({$this->tfixed}, summary_inet);
            $('#{$this->summary_net_input_id}').floatVal({$this->tfixed}, summary_net);
            $('#{$this->summary_nigv_input_id}').floatVal({$this->tfixed}, summary_nigv);
            $('#{$this->summary_figv_input_id}').floatVal({$this->tfixed}, summary_figv);            
            $('#{$this->summary_igv_input_id}').floatVal({$this->tfixed}, summary_igv);               
            $('#{$this->summary_ntotal_input_id}').floatVal({$this->tfixed}, summary_ntotal);
            $('#{$this->summary_ftotal_input_id}').floatVal({$this->tfixed}, summary_ftotal);
            $('#{$this->summary_total_input_id}').floatVal({$this->tfixed}, summary_total);
            $('#{$this->summary_nreal_input_id}').floatVal({$this->tfixed}, summary_nreal);
            $('#{$this->summary_freal_input_id}').floatVal({$this->tfixed}, summary_freal);
            $('#{$this->summary_real_input_id}').floatVal({$this->tfixed}, summary_real);
            $('#{$this->summary_adjust_loss_input_id}').floatVal({$this->tfixed}, summary_adjust_loss);
            $('#{$this->summary_adjust_gain_input_id}').floatVal({$this->tfixed}, summary_adjust_gain);
            $('#{$this->summary_advance_percentage_input_id}').floatVal({$this->tfixed}, new_sumary_advance_percentage);
        }
        
        function SIANCommercialCheckedGrid2ActivateRetention() {
            
            var divObj = $('#{$this->id}');
            var is_sale = divObj.data('is_sale');
            var is_buy = divObj.data('is_buy');            
            var retention_global = divObj.data('retention_global');
            var retention4 = divObj.data('retention4'); 
            var sunat_code = $('#{$this->retention_checkbox_id}').data('sunat_code');
            
            if ($('#{$this->retention_checkbox_id}').attr('readonly') == undefined)
            {
                if ($('#{$this->retention_checkbox_id}').is(':checked'))
                {
                    $('#{$this->div_retention_id}').removeClass('hide');
                    $('#{$this->div_no_ret_pen_id}').removeClass('hide');
                    $('#{$this->retention_percent_field_id}').attr('readonly', true);
                    //Si es recibo por honorarios se pone 8%
                    if (sunat_code == '" . Document::HONORARIOS . "') {
                        $('#{$this->retention_percent_field_id}').val(retention4 * 100);
                    }
                    if (is_sale == 1) {
                        $('#{$this->retention_percent_field_id}').val(retention_global * 100);
                    }
                }else{
                    $('#{$this->div_retention_id}').addClass('hide');
                    if (!$('#{$this->detraction_checkbox_id}').is(':checked')) {
                        $('#{$this->div_no_ret_pen_id}').addClass('hide');
                    }
                }                
            } else
            {
                $('#{$this->div_retention_id}').addClass('hide');
                if (!$('#{$this->detraction_checkbox_id}').is(':checked')) {
                    $('#{$this->div_no_ret_pen_id}').addClass('hide');
                }
            }
        }
        
        function SIANCommercialCheckedGrid2ActivateDetraction() {
        
            var sunat_code = $('#{$this->detraction_checkbox_id}').data('sunat_code');
            
            if($('#{$this->detraction_checkbox_id}').attr('readonly') == undefined)
            {
                if ($('#{$this->detraction_checkbox_id}').is(':checked'))
                {
                    $('#{$this->div_detraction_id}').removeClass('hide'); 
                    $('#{$this->div_no_ret_pen_id}').removeClass('hide'); 
                    SIANCommercialCheckedGrid2FillDetraction('{$this->detraction_percent_field_id}', '{$this->id}');
                    SIANCommercialCheckedGrid2FillDetractionCode('{$this->detraction_code_id}', '{$this->id}');
                }else{
                    //$('#{$this->detraction_percent_field_id}').val(0);
                    $('#{$this->div_detraction_id}').addClass('hide');
                    if(!$('#{$this->retention_checkbox_id}').is(':checked')){
                        $('#{$this->div_no_ret_pen_id}').addClass('hide');
                    }
                }
            }
            else{ 
                $('#{$this->detraction_percent_field_id}').val(0);
                $('#{$this->div_detraction_id}').addClass('hide');
                if(!$('#{$this->retention_checkbox_id}').is(':checked')){
                    $('#{$this->div_no_ret_pen_id}').addClass('hide');
                }
            }
        }
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->main_title,
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => 'sian-commercial-checked-grid-2 ' . ($this->model->hasErrors('tempItems') ? 'us-error' : '')
            )
        ));

        $this->_renderItems();
        //Generamos tablas de subitems
        $this->_createDataProviders();

        echo "<div class='row'>";

        echo "<div class='col-lg-7 col-md-7 col-sm-7 col-xs-12'>";
        $this->_renderCheckboxes();
        $this->_renderObservations();
//        $this->_renderSalesSpecifications();
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        if ($this->model->isBuy() || ($this->model->isSale())) {
            $this->_renderRetentionDetraction();
        }
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        $this->_renderSummary();
        echo "</div>";

        echo "</div>";

        $this->endWidget();
    }

    private function _createDataProviders() {
        foreach ($this->items as $o_item) {
            if (in_array($o_item->product_type, [Product::TYPE_COMBO, Product::TYPE_GROUP])) {
                $this->_renderSubItems($o_item);
            }
        }
    }

    private function _renderCheckboxes() {

        echo "<div class='row'>";
        if ($this->model->isSale()) {
            echo "<div class='col-lg-10 col-md-10 col-sm-4 col-xs-6'>";
        }
        $style_include_igv = $this->mode == CommercialMovement::FORM_MODE_GROUP ? "style='display:none;'" : "";

        echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-6' {$style_include_igv} >";
        echo $this->widget('application.widgets.USCheckBox', array('id' => $this->include_igv_checkbox_id,
            'model' => $this->model,
            'attribute' => 'include_igv',
            'hint' => 'Si los precios incluyen IGV',
            'htmlOptions' => array(
                'readonly' => !$this->edit_prices,
                'onchange' => !$this->edit_prices ? "" : "
                    var include_igv = $(this).prop('checked') ? 1 : 0;
                    $('#{$this->id}').data('include_igv', include_igv);
                    $('#{$this->force_igv_checkbox_id}').readonly(include_igv == 1);
                    SIANCommercialCheckedGrid2ChangeProperty('{$this->id}','include_igv',include_igv)
                    SIANCommercialCheckedGrid2Calculate('{$this->id}');
                ",
            )
                ), true);
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array('id' => $this->as_bill_checkbox_id,
            'model' => $this->model,
            'attribute' => 'as_bill',
            'hint' => 'Afecta el crédito fiscal (editable solo en compras)',
            'htmlOptions' => array(
                'onchange' => "     
                    if(window.documents_loaded)
                    {
                        //consoleLog('Se entró al evento onchange de as_bill pues ya terminaron de cargar los documentos');
                        var current = $(this).prop('checked') ? 1 : 0;

                        $('#{$this->id}').data('as_bill', current);
                        SIANCommercialCheckedGrid2Calculate('{$this->id}');                     
                    }
                    else
                    {
                        //consoleLog('Se intentó cambiar as_bill pero aún no terminaban de cargar los documentos');
                    }
                ",
            )
                ), true);
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->force_igv_checkbox_id,
            'model' => $this->model,
            'attribute' => 'force_igv',
            'hint' => 'Para indicar si se aumenta el IGV al monto inafecto/exonerado',
            'htmlOptions' => array(
                'readonly' => !$this->edit_prices || $this->model->include_igv == 1,
                'onchange' => !$this->edit_prices ? "" : "
                    //consoleLog('Se entró al evento onchange de force_igv pues ya terminaron de cargar los documentos');
                    var force_igv = $(this).prop('checked') ? 1 : 0;
                    $('#{$this->id}').data('force_igv', force_igv);
                    SIANCommercialCheckedGrid2ChangeProperty('{$this->id}','force_igv',force_igv);
                    SIANCommercialCheckedGrid2Calculate('{$this->id}');
                ",
            )
                ), true);
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->allow_duplicate_checkbox_id,
            'model' => $this->model,
            'attribute' => 'allow_duplicate',
            'htmlOptions' => array(
                'readonly' => true
            )
                ), true);
        echo "</div>";

        if ($this->model->isBuy()) {
            echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-6'>";
            echo $this->widget('application.widgets.USCheckBox', array(
                'id' => $this->retention_checkbox_id,
                'model' => $this->model,
                'attribute' => 'retention',
                'htmlOptions' => array(
                    'readonly' => false,
                ),
                    ), true);
            echo "</div>";

            echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-6'>";
            echo $this->widget('application.widgets.USCheckBox', array(
                'id' => $this->detraction_checkbox_id,
                'model' => $this->model,
                'attribute' => 'detraction',
                'htmlOptions' => array(
                ),
                    ), true);
            echo "</div>";
        }

        if ($this->model->isSale()) {

            echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-6'>";
            echo $this->widget('application.widgets.USCheckBox', array(
                'id' => $this->retention_checkbox_id,
                'model' => $this->model,
                'attribute' => 'retention',
                'htmlOptions' => array(
                ),
                    ), true);
            echo "</div>";

            echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-6'>";
            echo $this->widget('application.widgets.USCheckBox', array(
                'id' => $this->detraction_checkbox_id,
                'model' => $this->model,
                'attribute' => 'detraction',
                'htmlOptions' => array(
                ),
                    ), true);
            echo "</div>";
            echo "</div>";
        }

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->form->dropDownListRow($this->model, 'round_mode', [
            PHP_ROUND_HALF_UP => 'Hacia arriba',
            PHP_ROUND_HALF_DOWN => 'Hacia abajo',
                ], [
            'id' => $this->round_mode_select_id,
            'readonly' => !$this->edit_prices,
            'onchange' => !$this->edit_prices ? "" : "
                    $('#{$this->id}').data('round_mode', this.value);
                    SIANCommercialCheckedGrid2Calculate('{$this->id}');
                "
        ]);
        echo $this->form->hiddenField($this->model, 'warehouse_id', [
            'id' => $this->warehouse_input_id,
            'class' => 'sian-commercial-checked-grid-2-warehouse-id'
        ]);
        echo "</div>";

        echo "</div>";
    }

    private function _renderSalesSpecifications() {

        echo "<div class='row'>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->textAreaRow($this->model, 'sales_specifications', array(
            'rows' => 3,
            'maxlength' => 500,
        ));
        echo "</div>";

        echo "</div>";
    }

    private function _renderObservations() {

        echo "<div class='row'>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->textAreaRow($this->model->movement, 'observation', array(
            'id' => $this->observation_input_id,
            'rows' => 3,
            'maxlength' => 500,
            'required' => $this->model->movement->isObservationRequired()
        ));
        echo "</div>";

        echo "</div>";
    }

    private function _renderRetentionDetraction() {

        echo "<div id='{$this->div_retention_id}' class='row " . ($this->model->retention == 0 ? 'hide' : '') . "'>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "retention_percent", array(
            'id' => $this->retention_percent_field_id,
            'label' => "%Retención",
            'class' => "us-integer sian-commercial-checked-grid-2-retention-percent",
            'step' => 0,
            'readonly' => $this->model->retention == 0,
            'onchange' => "SIANCommercialCheckedGrid2CalculateRetentionAndDetraction('{$this->id}');"
        ));
        echo "</div>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "ret_{$this->model->movement->currency}", array(
            'id' => $this->ret_field_id,
            'label' => "Retenido <span class='currency-symbol'></span>",
            'class' => "us-double{$this->tfixed} sian-commercial-checked-grid-2-ret",
            'step' => $this->tstep,
            'readonly' => true,
        ));
        echo "</div>";
        echo "</div>";

        echo "<div id='{$this->div_detraction_id}'  class='row " . ($this->model->detraction == 0 ? 'hide' : '') . "'>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'detraction_code', [0 => Strings::SELECT_OPTION], [
            'id' => $this->detraction_code_id,
            'class' => "sian-commercial-checked-grid-2-detraction-code",
            'label' => "Código Detracción",
            'required' => true
        ]);
        echo "</div>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'detraction_percent', [0 => Strings::SELECT_OPTION], [
            'id' => $this->detraction_percent_field_id,
            'class' => "sian-commercial-checked-grid-2-detraction-percent",
            'label' => "%Detracción",
            'onchange' => "
                    var detraction_percent = 0;
                    if($('#{$this->detraction_checkbox_id}').prop('checked') == 1){
                        detraction_percent = $('this').floatVal(2);
                    }                    
                    if(!isNaN(detraction_percent)){
                        $('#{$this->id}').data('detraction_value', detraction_percent);
                    }                    
                    SIANCommercialCheckedGrid2CalculateRetentionAndDetraction('{$this->id}');"
        ]);
        echo "</div>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "det_{$this->model->movement->currency}", array(
            'id' => $this->ret_field_id,
            'label' => "Detraído <span class='currency-symbol'></span>",
            'class' => "us-double{$this->tfixed} sian-commercial-checked-grid-2-det",
            'onchange' => "SIANCommercialCheckedGrid2SetNoRetDet('{$this->id}');",
            'step' => $this->tstep,
        ));
        echo "</div>";
        echo "</div>";

        echo "<div id='{$this->div_no_ret_pen_id}' class='row " . ($this->model->retention == 0 && $this->model->detraction == 0 ? 'hide' : '') . "'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "no_ret_{$this->model->movement->currency}", array(
            'id' => $this->no_ret_field_id,
            'label' => "No Ret/Det. <span class='currency-symbol'></span>",
            'class' => "us-double{$this->tfixed} sian-commercial-checked-grid-2-no-ret",
            'step' => $this->tstep,
            'readonly' => true,
        ));
        echo "</div>";
        echo "</div>";
    }

    private function _renderSummary() {

        $editable = false;
        $visible_isc = false;
        $visible_perception = false;
        $currentRoute = $this->model->movement->route;
        $isEditableAdvanceRoute = $currentRoute === 'logistic/purchaseOrder';
        $isVisibleAdvanceRoute = $currentRoute === 'logistic/purchaseOrder';
        if ($this->summary_is_editable) {
            $editable = true;
            $visible_isc = true;
            $visible_perception = true;
        }
        echo CHtml::hiddenField('CommercialMovement[totals_editable]', $this->model->totals_editable, []);

        $input_advance = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_net_{$this->model->movement->currency}",
            'value_id' => $this->summary_advance_input_id,
            'name' => 'Totals[advance_net]',
            'class' => 'sian-commercial-checked-grid-2-total-advance-net',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);

        $input_advance_affected1 = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_affected1_{$this->model->movement->currency}",
            'value_id' => $this->summary_advance_affected1_input_id,
            'name' => 'Totals[advance_affected1]',
            'class' => 'sian-commercial-checked-grid-2-total-advance_affected1',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => false,
            'has_up_option' => false,
                ), true);

        $input_affected1 = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "affected1_{$this->model->movement->currency}",
            'value_id' => $this->summary_affected1_input_id,
            'name' => 'Totals[affected1]',
            'class' => 'sian-commercial-checked-grid-2-total-affected1',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => $editable,
            'has_up_option' => $editable,
            'max_attribute' => "max_affected1",
            'min_attribute' => "min_affected1",
                ), true);
        $input_crude = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "crude_{$this->model->movement->currency}",
            'value_id' => $this->summary_crude_input_id,
            'name' => 'Totals[crude]',
            'class' => 'sian-commercial-checked-grid-2-total-crude',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);

        $input_advance_affected = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_affected_{$this->model->movement->currency}",
            'value_id' => $this->summary_advance_affected_input_id,
            'name' => 'Totals[advance_affected]',
            'class' => 'sian-commercial-checked-grid-2-total-advance_affected',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => false,
            'has_up_option' => false,
                ), true);

        $input_affected = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "affected_{$this->model->movement->currency}",
            'value_id' => $this->summary_affected_input_id,
            'name' => 'Totals[affected]',
            'class' => 'sian-commercial-checked-grid-2-total-affected',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);

        $input_advance_inaffected = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_inaffected_{$this->model->movement->currency}",
            'value_id' => $this->summary_advance_inaffected_input_id,
            'name' => 'Totals[advance_inaffected]',
            'class' => 'sian-commercial-checked-grid-2-total-advance_inaffected',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => false,
            'has_up_option' => false,
            'max_attribute' => "max_inaffected",
            'min_attribute' => "min_inaffected",
                ), true);

        $input_inaffected = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "inaffected_{$this->model->movement->currency}",
            'value_id' => $this->summary_inaffected_input_id,
            'name' => 'Totals[inaffected]',
            'class' => 'sian-commercial-checked-grid-2-total-inaffected',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => $editable,
            'has_up_option' => $editable,
            'max_attribute' => "max_inaffected",
            'min_attribute' => "min_inaffected",
                ), true);

        $input_advance_nobill = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_nobill_{$this->model->movement->currency}",
            'value_id' => $this->summary_advance_nobill_input_id,
            'name' => 'Totals[advance_nobill]',
            'class' => 'sian-commercial-checked-grid-2-total-advance_nobill',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => $editable,
            'has_up_option' => $editable,
            'visible' => false,
            'max_attribute' => "max_nobill",
            'min_attribute' => "min_nobill",
                ), true);
        $input_nobill = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "nobill_{$this->model->movement->currency}",
            'value_id' => $this->summary_nobill_input_id,
            'name' => 'Totals[nobill]',
            'class' => 'sian-commercial-checked-grid-2-total-nobill',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => $editable,
            'has_up_option' => $editable,
            'max_attribute' => "max_nobill",
            'min_attribute' => "min_nobill",
                ), true);
        $input_export = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "export_{$this->model->movement->currency}",
            'value_id' => $this->summary_export_input_id,
            'name' => 'Totals[export]',
            'class' => 'sian-commercial-checked-grid-2-total-export',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);

        $input_advance_free = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_free_{$this->model->movement->currency}",
            'value_id' => $this->summary_advance_free_input_id,
            'name' => 'Totals[free]',
            'class' => 'sian-commercial-checked-grid-2-total-advance_free',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);

        $input_free = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "free_{$this->model->movement->currency}",
            'value_id' => $this->summary_free_input_id,
            'name' => 'Totals[free]',
            'class' => 'sian-commercial-checked-grid-2-total-free',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);

        $input_inet = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "inet_{$this->model->movement->currency}",
            'value_id' => $this->summary_inet_input_id,
            'name' => 'Totals[inet]',
            'class' => 'sian-commercial-checked-grid-2-total-inet',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);

        $input_nnet = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "nnet_{$this->model->movement->currency}",
            'value_id' => $this->summary_nnet_input_id,
            'name' => 'Totals[nnet]',
            'class' => 'sian-commercial-checked-grid-2-total-nnet',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_fnet = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "fnet_{$this->model->movement->currency}",
            'value_id' => $this->summary_fnet_input_id,
            'name' => 'Totals[fnet]',
            'class' => 'sian-commercial-checked-grid-2-total-fnet',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_net = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "net_{$this->model->movement->currency}",
            'value_id' => $this->summary_net_input_id,
            'name' => 'Totals[net]',
            'class' => 'sian-commercial-checked-grid-2-total-net',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $input_isc = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "isc_{$this->model->movement->currency}",
            'value_id' => $this->summary_isc_input_id,
            'name' => 'Totals[isc]',
            'class' => 'sian-commercial-checked-grid-2-total-isc',
            'prepend' => "<span class='currency-symbol'></span>",
            'visible' => $visible_isc,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $input_nigv = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "nigv_{$this->model->movement->currency}",
            'value_id' => $this->summary_nigv_input_id,
            'name' => 'Totals[nigv]',
            'class' => 'sian-commercial-checked-grid-2-total-nigv',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_figv = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "figv_{$this->model->movement->currency}",
            'value_id' => $this->summary_figv_input_id,
            'name' => 'Totals[figv]',
            'class' => 'sian-commercial-checked-grid-2-total-figv',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_igv = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "igv_{$this->model->movement->currency}",
            'value_id' => $this->summary_igv_input_id,
            'name' => 'Totals[igv]',
            'class' => 'sian-commercial-checked-grid-2-total-igv',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => $editable,
            'has_up_option' => $editable,
            'max_attribute' => "max_igv",
            'min_attribute' => "min_igv",
                ), true);
        $input_ntotal = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "ntotal_{$this->model->movement->currency}",
            'value_id' => $this->summary_ntotal_input_id,
            'name' => 'Totals[ntotal]',
            'class' => 'sian-commercial-checked-grid-2-total-ntotal',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_ftotal = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "ftotal_{$this->model->movement->currency}",
            'value_id' => $this->summary_ftotal_input_id,
            'name' => 'Totals[ftotal]',
            'class' => 'sian-commercial-checked-grid-2-total-ftotal',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_total = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "total_{$this->model->movement->currency}",
            'value_id' => $this->summary_total_input_id,
            'name' => 'Totals[total]',
            'class' => 'sian-commercial-checked-grid-2-total-total',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        if ($this->model->movement->direction == Scenario::DIRECTION_IN) {
            $input_perception = $this->widget('application.widgets.USNumberField', array(
                'model' => $this->model,
                'attribute' => "perception_{$this->model->movement->currency}",
                'value_id' => $this->summary_perception_input_id,
                'name' => 'Totals[perception]',
                'class' => 'sian-commercial-checked-grid-2-total-perception',
                'prepend' => "<span class='currency-symbol'></span>",
                'visible' => $visible_perception,
                'has_down_option' => false,
                'has_up_option' => false
                    ), true);
        } else {
            $input_perception = $this->widget('application.widgets.USNumberField', array(
                'model' => $this->model,
                'attribute' => "perception_{$this->model->movement->currency}",
                'value_id' => $this->summary_perception_input_id,
                'name' => 'Totals[perception]',
                'class' => 'sian-commercial-checked-grid-2-total-perception',
                'prepend' => "<span class='currency-symbol'></span>",
                'readonly_text' => true,
                'has_down_option' => $editable,
                'has_up_option' => $editable,
                'max_attribute' => "max_perception",
                'min_attribute' => "min_perception",
                    ), true);
        }
        $input_nreal = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "nreal_{$this->model->movement->currency}",
            'value_id' => $this->summary_nreal_input_id,
            'name' => 'Totals[nreal]',
            'class' => 'sian-commercial-checked-grid-2-total-nreal',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_freal = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "freal_{$this->model->movement->currency}",
            'value_id' => $this->summary_freal_input_id,
            'name' => 'Totals[freal]',
            'class' => 'sian-commercial-checked-grid-2-total-freal',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_real = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "real_{$this->model->movement->currency}",
            'value_id' => $this->summary_real_input_id,
            'name' => 'Totals[real]',
            'class' => 'sian-commercial-checked-grid-2-total-real',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $advance_percentage = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_percentage",
            'value_id' => $this->summary_advance_percentage_input_id,
            'name' => 'Totals[advance_percentage]',
            'class' => 'sian-commercial-checked-grid-2-advance_percentage',
            'prepend' => "<span>%</span>",
            'min_value' => 0,
            'max_value' => 100,
            'readonly_text' => !$isEditableAdvanceRoute,
            'has_down_option' => false,
            'has_up_option' => false,
            'visible' => $isVisibleAdvanceRoute,
                ), true);
        $advance_amount = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_amount",
            'value_id' => $this->summary_advance_amount_input_id,
            'name' => 'Totals[advance_amount]',
            'class' => 'sian-commercial-checked-grid-2-advance_amount',
            'prepend' => "<span class='currency-symbol'></span>",
            'min_value' => 0,
            'readonly_text' => !$isEditableAdvanceRoute,
            'has_down_option' => false,
            'has_up_option' => false,
            'visible' => $isVisibleAdvanceRoute,
                ), true);
        $advance_balance = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_balance",
            'value_id' => $this->summary_advance_balance_input_id,
            'name' => 'Totals[advance_balance]',
            'class' => 'sian-commercial-checked-grid-2-advance_balance',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false,
            'visible' => $isVisibleAdvanceRoute,
                ), true);
        //adjust fields
        $input_adjust_affected_loss = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "adjust_affected_loss_{$this->model->movement->currency}",
            'value_id' => $this->summary_adjust_affected_loss_input_id,
            'name' => 'Totals[adjust_affected_loss]',
            'class' => 'sian-commercial-checked-grid-2-total-adjust-affected-loss',
            'style_div' => "float:left;width:47.5%;",
            'prepend' => "<span class='fa fa-1x fa-plus'></span>",
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $input_adjust_affected_gain = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "adjust_affected_gain_{$this->model->movement->currency}",
            'value_id' => $this->summary_adjust_affected_gain_input_id,
            'name' => 'Totals[adjust_affected_gain]',
            'class' => 'sian-commercial-checked-grid-2-total-adjust-affected-gain',
            'prepend' => "<span class='fa fa-1x fa-minus'></span>",
            'style_div' => "float:left;width:47.5%;margin-left:5%;",
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $input_adjust_inaffected_loss = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "adjust_inaffected_loss_{$this->model->movement->currency}",
            'value_id' => $this->summary_adjust_inaffected_loss_input_id,
            'name' => 'Totals[adjust_inaffected_loss]',
            'class' => 'sian-commercial-checked-grid-2-total-adjust-inaffected-loss',
            'style_div' => "float:left;width:47.5%;",
            'prepend' => "<span class='fa fa-1x fa-plus'></span>",
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $input_adjust_inaffected_gain = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "adjust_inaffected_gain_{$this->model->movement->currency}",
            'value_id' => $this->summary_adjust_inaffected_gain_input_id,
            'name' => 'Totals[adjust_inaffected_gain]',
            'class' => 'sian-commercial-checked-grid-2-total-adjust-inaffected-gain',
            'prepend' => "<span class='fa fa-1x fa-minus'></span>",
            'style_div' => "float:left;width:47.5%;margin-left:5%;",
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $input_adjust_loss = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "adjust_loss_{$this->model->movement->currency}",
            'value_id' => $this->summary_adjust_loss_input_id,
            'name' => 'Totals[adjust_loss]',
            'class' => 'sian-commercial-checked-grid-2-total-adjust-loss',
            'visible' => false,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $input_adjust_gain = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "adjust_gain_{$this->model->movement->currency}",
            'value_id' => $this->summary_adjust_gain_input_id,
            'name' => 'Totals[adjust_gain]',
            'class' => 'sian-commercial-checked-grid-2-total-adjust-gain',
            'visible' => false,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);

        echo "<div id='{$this->summary_id}'>";
        echo "<div class='well pull-right extended-summary ' style='padding: 0px !important;'>";
        echo "<table class='table-condensed'>";
        echo "<tr><td><b>Anticipos:</b></td><td style='text-align:right'>{$input_advance}</td></tr>";
        echo "<tr><td style='width:35%'><b>Gravado:</b></td><td style='text-align:right; width:65%;'>{$input_crude}{$input_advance_affected1}{$input_affected1}{$input_affected}{$input_advance_affected}</td></tr>";
        echo "<tr><td><b>Inafecto:</b></td><td style='text-align:right'>{$input_inaffected}{$input_advance_inaffected}</td></tr>";
        echo "<tr><td><b>Exonerado:</b></td><td style='text-align:right'>{$input_nobill}{$input_advance_nobill}</td></tr>";
        echo "<tr><td><b>Exportación:</b></td><td style='text-align:right'>{$input_export}</td></tr>";
        echo "<tr><td><b>Gratuito:</b></td><td style='text-align:right'>{$input_free}{$input_advance_free}</td></tr>";
        echo "<tr><td><b>Subtotal:</b></td><td style='text-align:right'>{$input_inet}</td></tr>";
        echo "<tr><td><b>Valor Venta:</b></td><td style='text-align:right'>{$input_nnet}{$input_fnet}{$input_net}</td></tr>";
        echo "<tr><td><b>ISC:</b></td><td style='text-align:right'>{$input_isc}</td></tr>";
        echo "<tr><td><b>Ajuste Gravado:</b></td><td style='text-align:right'>{$input_adjust_affected_loss}{$input_adjust_affected_gain}</td></tr>";
        echo "<tr><td><b>IGV:</b></td><td style='text-align:right'>{$input_nigv}{$input_figv}{$input_igv}</td></tr>";
        echo "<tr><td><b>Ajuste No Grav.:</b></td><td style='text-align:right'>{$input_adjust_inaffected_loss}{$input_adjust_inaffected_gain}{$input_adjust_loss}{$input_adjust_gain}</td></tr>";
        echo "<tr><td><b>Total venta:</b></td><td style='text-align:right'>{$input_ntotal}{$input_ftotal}{$input_total}</td></tr>";
        echo "<tr><td><b>Percepción:</b></td><td style='text-align:right'>{$input_perception}</td></tr>";
        echo "<tr><td><b>Total a pagar:</b></td><td style='text-align:right'>{$input_nreal}{$input_freal}{$input_real}</td></tr>";
        if ($isVisibleAdvanceRoute) {
            echo "<tr><td><b>% Adelanto:</b></td><td style='text-align:right'>{$advance_percentage}</td></tr>";
            echo "<tr><td><b>Total Adelanto:</b></td><td style='text-align:right'>{$advance_amount}</td></tr>";
            echo "<tr><td><b>Saldo Adelanto:</b></td><td style='text-align:right'>{$advance_balance}</td></tr>";
        }
        echo "</table>";
        echo "</div>";
        echo "</div>";
    }

    private function _renderItems() {

        $b_preview_access = $this->preview_access;
        $o_form = $this->form;
        $s_stock_mode = $this->model->movement->stock_mode;
        $s_emission_date = $this->model->movement->emission_date;
        $i_exclude_id = $this->model->movement->kardex_unlock_exclude_id;
        $i_store_id = $this->model->movement->store_id;
        $a_igv_affections = $this->igv_affections;
        $a_igv_list = $this->igv_list;
        $s_mode = $this->mode; 

        $a_columns = [];

        array_push($a_columns, array(
            'header' => CHtml::checkBox("All", $this->getGlobalCheckBoxState(), array(
                'class' => 'sian-credit-note2-check',
                'onchange' => 'SIANCommercialCheckedGrid2GridCheck($(this));',
            )),
            'headerHtmlOptions' => array('style' => 'width:3%; text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) {
                return CHtml::checkBox("Checked[{$row->parent_item_id}]", $row->checked, array(
                    'class' => 'sian-commercial-checked-grid-2-item-check',
                    'onchange' => 'SIANCommercialCheckedGrid2RowCheck($(this));',
                ));
            },
        ));

        array_push($a_columns, array(
            'header' => '#',
            'headerHtmlOptions' => array('style' => 'width:2%;'),
            'type' => 'raw',
            'value' => function ($row) use (&$counter) {
                return "<span class='sian-commercial-checked-grid-2-item-index'>" . ( ++$counter) . "</span>";
            },
        ));

        if($s_mode == CommercialMovement::FORM_MODE_GROUP){
            array_push($a_columns, array(
                'header' => 'OC',
                'headerHtmlOptions' => array('style' => 'width:5%; text-align:left;'),
                'htmlOptions' => array('style' => 'text-align:left;'),
                'type' => 'raw',
                'value' => function ($row) {
                    $html =  Yii::app()->controller->widget('application.widgets.USLink', array(
                        'label' => $row->document,
                        'title' => 'Ver documento',
                        'route' => "/{$row->route}/view",
                        'params' => array(
                            'id' => $row->movement_id,
                        ),
                        'target' => $row->movement_id,
                        'visible' => true,
                    ), true);

                    $html .= CHtml::hiddenField(
                        "Item[{$row->parent_item_id}][movement_id]", 
                        $row->movement_id, 
                        array(
                            'class' => "sian-commercial-checked-grid-2-item-movement-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                        )
                    );
                    
                    $html .= CHtml::hiddenField(
                        "Item[{$row->parent_item_id}][warehouse_id]", 
                        $row->warehouse_id, 
                        array(
                            'class' => "sian-commercial-checked-grid-2-item-warehouse-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                        )
                    );
                    
                    return $html;
                },
            ));
        }
        

        array_push($a_columns, array(
            'header' => 'ID',
            'headerHtmlOptions' => array('style' => 'width:5%;'),
            'type' => 'raw',
            'value' => function ($row) {
                return $this->renderProductCell($row);
            },
        ));

        array_push($a_columns, array(
            'header' => 'Tipo',
            'headerHtmlOptions' => array('style' => 'width:3%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($o_form) {
                return $o_form->textFieldRow($row, 'product_type', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][product_type]",
                    'class' => 'sian-commercial-checked-grid-2-item-product-type',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:center'
                ));
            },
        ));

        $width = '21'; 
        if ($this->model->movement->validate_stock == 1) {
            $width = '14';
        } elseif ($s_mode == CommercialMovement::FORM_MODE_GROUP) {
            $width = '18';
        }

        array_push($a_columns, array(
            'header' => 'Producto',
            'headerHtmlOptions' => array('style' => 'width:' . $width . '%'),
            'type' => 'raw',
            'value' => function ($row) use ($o_form) {
                return $o_form->textFieldRow($row, 'product_name', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][product_name]",
                    'class' => 'sian-commercial-checked-grid-2-item-product-name sian-force-tooltip',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'title' => $row->product_name
                ));
            },
        ));

        array_push($a_columns, array(
            'header' => 'Afec. IGV',
            'headerHtmlOptions' => array('style' => 'width:5%;'.'text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($a_igv_affections) {
                $s_name = "Item[{$row->parent_item_id}][igv_affection]";
                $s_class = "sian-commercial-checked-grid-2-item-igv-affection";
                $b_readonly = !$this->edit_prices;
                $s_onchange = $b_readonly ? "" :
                        "let trId = $(this).closest('tr').attr('id');
                         SIANCommercialCheckedGrid2Calculate('{$this->id}'); 
                         SIANCommercialCheckedGrid2SetRowFieldsBehavior('{$this->id}', trId);";
                return $this->renderIGVAffectionSelect($row, $a_igv_affections, $s_name, $s_class, $b_readonly, !$row->checked, $s_onchange);
            },
        ));

        array_push($a_columns, array(
            'header' => '%IGV',
            'headerHtmlOptions' => array('style' => 'width:5%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($a_igv_list) {
                $s_name = "Item[{$row->parent_item_id}][igv_percentage]";
                $s_class = "sian-commercial-checked-grid-2-item-igv-percentage";
                $b_igv_readonly = (!$this->edit_prices || in_array($row->igv_affection, [
                            CommercialMovementProduct::IGV_AFFECTION_EXONERATED,
                            CommercialMovementProduct::IGV_AFFECTION_INAFFECTED,
                            CommercialMovementProduct::IGV_AFFECTION_GIFT])) ? 1 : 0;
                $b_readonly = !$this->edit_prices;
                $s_onchange = $b_readonly ? "" : "SIANCommercialCheckedGrid2Calculate('{$this->id}')";
                return $this->renderIGVListSelect($row, $a_igv_list, $s_name, $s_class, $b_igv_readonly, !$row->checked, $s_onchange);
            },
        ));

        array_push($a_columns, array(
            'header' => 'Perc?',
            'headerHtmlOptions' => array('style' => 'width:1%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) {
                return $this->controller->widget('application.widgets.USCheckBox', array(
                    'name' => "Item[{$row->parent_item_id}][perception_affected]",
                    'value' => $row->perception_affected,
                    'htmlOptions' => array(
                        'class' => 'sian-commercial-checked-grid-2-item-perception-affected',
                        'readonly' => true,
                        'disabled' => !$row->checked
                    )), true);
            },
        ));

        if ($this->model->movement->validate_stock == 1) {
            array_push($a_columns, array(
                'header' => 'Stock *',
                'headerHtmlOptions' => array('style' => 'width:7%;text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:right;'),
                'type' => 'raw',
                'value' => function ($row) use ($o_form, $s_stock_mode, $s_emission_date, $i_exclude_id, $i_store_id) {

                    if ($row->product_type === Product::TYPE_MERCHANDISE) {
                        return $this->widget('application.widgets.USLink', array(
                                    'route' => '/logistic/merchandise/explainCommercialStock',
                                    'label' => '0',
                                    'title' => '¿Por qué veo este stock?',
                                    'class' => 'form sian-commercial-checked-grid-2-item-mixed-stock',
                                    'data' => array(
                                        'id' => $row->product_id,
                                        'equivalence' => $row->equivalence,
                                        'stock_mode' => $s_stock_mode,
                                        'emission_date' => $s_emission_date,
                                        'exclude_id' => $i_exclude_id,
                                        'store_id' => $i_store_id
                                    ),
                                    'visible' => true
                                        ), true);
                    } else {
                        return '-';
                    }
                },
            ));
        }

        array_push($a_columns, array(
            'header' => 'Dispon',
            'headerHtmlOptions' => array('style' => 'width:5%; text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = "<span class='sian-commercial-checked-grid-2-item-max-pres-quantity'>{$row->max_pres_quantity}</span>";
                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_pres_quantity]", $row->max_pres_quantity, array(
                            'class' => 'sian-commercial-checked-grid-2-item-max-pres-quantity',
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));
                return $html;
            },
        ));

        array_push($a_columns, array(
            'header' => 'Cantidad',
            'headerHtmlOptions' => array('style' => 'width:7%'),
            'type' => 'raw',
            'value' => function ($row) use ($o_form) {

                return $o_form->numberFieldRow($row, 'pres_quantity', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][pres_quantity]",
                    'class' => "sian-commercial-checked-grid-2-item-pres-quantity " . ($row->allow_decimals == 1 ? "us-double2" : "us-double0"),
                    'min' => $row->allow_decimals == 1 ? 0.01 : 0,
                    'max' => round($row->max_unit_quantity / $row->equivalence, $row->allow_decimals == 1 ? 2 : 0),
                    'step' => $row->allow_decimals == 1 ? 0.01 : 0,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:center',
                    'onchange' => "SIANCommercialCheckedGrid2Calculate('{$this->id}')",
                ));
            },
        ));

        array_push($a_columns, array(
            'header' => 'Pres.',
            'headerHtmlOptions' => array('style' => 'width:7%'),
            'type' => 'raw',
            'value' => function ($row) {
                $s_name = "Item[{$row->parent_item_id}][equivalence]";
                $s_class = "sian-commercial-checked-grid-2-item-equivalence";
                return $this->renderPresentationCell($row, $s_name, $s_class, false, !$row->checked);
            },
            'footerHtmlOptions' => array('style' => 'text-align:right;'),
        ));

        array_push($a_columns, array('header' => 'Hidden',
            'headerHtmlOptions' => array('style' => 'display:none'),
            'htmlOptions' => array('style' => 'display:none'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) {

                $html = CHtml::hiddenField("Item[{$row->parent_item_id}][item_type_id]", $row->item_type_id, array('class' => "sian-commercial-checked-grid-2-item-item-type-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                if (isset($row['delivery_date'])) {
                    $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][delivery_date]", $row->delivery_date, array('class' => "sian-commercial-checked-grid-2-item-delivery-date",
                                'readonly' => true,
                                'disabled' => !$row->checked,
                    ));
                }

                if (isset($row['warehouse_id'])) {
                    $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][warehouse_id]", $row->warehouse_id, array('class' => "sian-commercial-checked-grid-2-item-warehouse-id",
                                'readonly' => true,
                                'disabled' => !$row->checked,
                    ));
                }

                if (isset($row['parent_product_type'])) {
                    $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][parent_product_type]", $row->parent_product_type, array('class' => "sian-commercial-checked-grid-2-item-parent-product-type",
                                'readonly' => true,
                                'disabled' => !$row->checked,
                    ));
                }

                if (isset($row['parent_product_type_original'])) {
                    $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][parent_product_type_original]", $row->parent_product_type_original, array('class' => "sian-commercial-checked-grid-2-item-parent-product-type-original",
                                'readonly' => true,
                                'disabled' => !$row->checked,
                    ));
                }

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][unit_stock]", $row->unit_stock, array('class' => "sian-commercial-checked-grid-2-item-unit-stock",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_unit_quantity]", $row->max_unit_quantity, array('class' => "sian-commercial-checked-grid-2-item-max-unit-quantity",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][original_quantity]", $row->original_quantity, array('class' => "sian-commercial-checked-grid-2-item-original-quantity",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][allow_decimals]", $row->allow_decimals, array('class' => "sian-commercial-checked-grid-2-item-allow-decimals",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][skip_cost_validation]", $row->skip_cost_validation, array('class' => "sian-commercial-checked-grid-2-item-skip-cost-validation",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][currency]", $row->currency, array('class' => "sian-commercial-checked-grid-2-item-currency",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][min_price_pen]", $row->min_price_pen, array('class' => "sian-commercial-checked-grid-2-item-min-price-pen",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][min_price_usd]", $row->min_price_usd, array('class' => "sian-commercial-checked-grid-2-item-min-price-usd",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][avg_price_pen]", $row->avg_price_pen, array('class' => "sian-commercial-checked-grid-2-item-avg-price-pen",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][avg_price_usd]", $row->avg_price_usd, array('class' => "sian-commercial-checked-grid-2-item-avg-price-usd",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_price_pen]", $row->max_price_pen, array('class' => "sian-commercial-checked-grid-2-item-max-price-pen",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_price_usd]", $row->max_price_usd, array('class' => "sian-commercial-checked-grid-2-item-max-price-usd",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_discount_pen]", $row->max_discount_pen, array('class' => "sian-commercial-checked-grid-2-item-max-discount-pen",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_discount_usd]", $row->max_discount_usd, array('class' => "sian-commercial-checked-grid-2-item-max-discount-usd",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][cost_pen]", $row->cost_pen, array('class' => "sian-commercial-checked-grid-2-item-cost-pen",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][cost_usd]", $row->cost_usd, array('class' => "sian-commercial-checked-grid-2-item-cost-usd",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                //Parents
                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][parent_item_id]", $row->parent_item_id, array('class' => "sian-commercial-checked-grid-2-item-parent-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][quantity_item_id]", $row->quantity_item_id, array('class' => "sian-commercial-checked-grid-2-item-quantity-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][total_item_id]", $row->total_item_id, array('class' => "sian-commercial-checked-grid-2-item-total-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                if (isset($row->total_item_both)) {
                    $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][total_item_both]", $row->total_item_both, array('class' => "sian-commercial-checked-grid-2-item-total-item-both",
                                'readonly' => true,
                                'disabled' => !$row->checked,
                    ));
                }

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][promotion_item_id]", $row->promotion_item_id, array('class' => "sian-commercial-checked-grid-2-item-promotion-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][promotion_gift_option_id]", $row->promotion_gift_option_id, array('class' => "sian-commercial-checked-grid-2-item-promotion-gift-option-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][include_igv]", $row->include_igv, array('class' => "sian-commercial-checked-grid-2-item-include-igv",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][force_igv]", $row->force_igv, array('class' => "sian-commercial-checked-grid-2-item-force-igv",
                    'readonly' => true,
                    'disabled' => !$row->checked,
                ));

                return $html;
            }
        ));

        array_push($a_columns, array(
            'header' => "Precio <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:7%;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) use ($o_form) {

                return $o_form->numberFieldRow($row, 'price', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][price]",
                    'class' => "sian-commercial-checked-grid-2-item-price",
                    'readonly' => !$this->edit_prices,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right',
                    'step' => $this->istep,
                    'onchange' => !$this->edit_prices ? "" : "SIANCommercialCheckedGrid2Calculate('{$this->id}')",
                ));
            },
        ));

        array_push($a_columns, array(
            'header' => "Descuento <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:7%;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) {
                return SIANForm::textFieldNonActive(false, "Item[{$row->parent_item_id}][discount]", $row->discount, array(
                    'class' => "sian-commercial-checked-grid-2-item-discount",
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right'
                ));
            },
            'footer' => '<b>TOTALES:</b>',
        ));

        array_push($a_columns, array('header' => "Subtotal <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:7%'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) {
                return SIANForm::textFieldNonActive(false, "Item[{$row->parent_item_id}][net]", 0, array(
                    'class' => "sian-commercial-checked-grid-2-item-net",
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right'
                ));
            },
            'footer' => SIANForm::textFieldNonActive(false, null, 0, array('class' => 'sian-commercial-checked-grid-2-items-net',
                'readonly' => true,
                'style' => 'text-align:right'
            ))
        ));

        array_push($a_columns, array('header' => "IGV <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:7%'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) {
                return SIANForm::textFieldNonActive(false, "Item[{$row->parent_item_id}][igv]", 0, array(
                    'class' => "sian-commercial-checked-grid-2-item-igv",
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right'
                ));
            },
            'footer' => SIANForm::textFieldNonActive(false, null, 0, array('class' => 'sian-commercial-checked-grid-2-items-igv',
                'readonly' => true,
                'style' => 'text-align:right'
            ))
        ));

        array_push($a_columns, array('header' => "Total <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:7%'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) {
                return SIANForm::textFieldNonActive(false, "Item[{$row->parent_item_id}][total]", 0, array(
                    'class' => "sian-commercial-checked-grid-2-item-total",
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right'
                ));
            },
            'footer' => SIANForm::textFieldNonActive(false, null, 0, array('class' => 'sian-commercial-checked-grid-2-items-total',
                'readonly' => true,
                'style' => 'text-align:right'
            ))
        ));

        array_push($a_columns, array('header' => 'Opc.',
            'headerHtmlOptions' => array('style' => 'width:3%; text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($b_preview_access) {
                return $this->_renderRowButtons($row, $b_preview_access);
            },
        ));

        $gridParams = array(
            'id' => $this->controller->getServerId(),
            'type' => 'condensed',
            'dataProvider' => $this->dataProvider,
            'enableSorting' => false,
            'selectableRows' => 0,
            'columns' => $a_columns,
            'template' => '{items}',
            'rowCssClassExpression' => '"sian-commercial-checked-grid-2-item " . ($data->checked == 1 ? "success" : "danger")',
            'htmlOptions' => [
                'class' => 'sian-commercial-checked-grid-2-table sian-commercial-checked-grid-2-item-table',
            ]
        );
        $this->widget('application.widgets.USGridView', $gridParams);

        if ($this->model->movement->validate_stock == 1) {
            echo "<p><b>(*)</b> El stock usado es el stock <b>{$this->model->movement->getStockModeLabel()}</b> a la fecha de emisión elegida " . (isset($this->model->movement->kardex_unlock_exclude_id) ? ' y para este movimiento en específico' : "" ) . "</p>";
        }
    }

    private function _renderSubItems($p_o_item) {

        $b_preview_access = $this->preview_access;
        $a_values = array_values($p_o_item->subItems);
        $a_igv_affections = $this->igv_affections;
        $a_igv_list = $this->igv_list;
        $s_stock_mode = $this->model->movement->stock_mode;
        $s_emission_date = $this->model->movement->emission_date;
        $i_exclude_id = $this->model->movement->kardex_unlock_exclude_id;
        $i_store_id = $this->model->movement->store_id;

        $o_dataProvider = new USArrayDataProvider($a_values, array(
            'keyField' => 'group_id', // PRIMARY KEY
            'pagination' => false,
            'model' => get_class($a_values[0]),
            'sort' => []//Al ser procedure, no se permite la ordenación
        ));

        $form = $this->form;
        $a_columns = [];
        $i_counter = 0;

        array_push($a_columns, array(
            'header' => '#',
            'headerHtmlOptions' => array('style' => 'width:2%;'),
            'footerHtmlOptions' => array('colspan' => ($this->model->movement->validate_stock == 1 ? 7 : 6)),
            'type' => 'raw',
            'value' => function ($row) use ($p_o_item, &$i_counter) {
                $row->index = $i_counter;
                return "<span class='sian-commercial-checked-grid-2-subitem-index'>" . ( ++$i_counter) . "</span>";
            },
            'footer' => CHtml::link("<span class='fa fa-lg fa-backward black'></span> Regresar a listado principal", Strings::LINK_TEXT, [
                'onclick' => "SIANCommercialCheckedGrid2BackToMainTable('{$this->id}', $(this), 100)"
            ])
        ));

        array_push($a_columns, array(
            'header' => 'ID',
            'headerHtmlOptions' => array('style' => 'width:5%;'),
            'type' => 'raw',
            'value' => function ($row) use ($form, $p_o_item) {
                return $form->textFieldRow($row, 'product_id', array(
                    'label' => false,
                    'name' => "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][product_id]",
                    'class' => 'sian-commercial-checked-grid-2-subitem-product-id',
                    'readonly' => true,
                    'disabled' => !$p_o_item->checked,
                    'style' => 'text-align:center'
                ));
            },
        ));

        array_push($a_columns, array(
            'header' => 'Tipo',
            'headerHtmlOptions' => array('style' => 'width:3%;'),
            'type' => 'raw',
            'value' => function ($row) use ($form, $p_o_item) {
                return $form->textFieldRow($row, 'product_type', array(
                    'label' => false,
                    'name' => "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][product_type]",
                    'class' => 'sian-commercial-checked-grid-2-subitem-product-type',
                    'readonly' => true,
                    'disabled' => !$p_o_item->checked,
                    'style' => 'text-align:center'
                ));
            },
        ));

        array_push($a_columns, array(
            'header' => 'Nombre',
            'headerHtmlOptions' => array('style' => 'width:' . ($this->model->movement->validate_stock == 1 ? '21' : '28') . '%;'),
            'type' => 'raw',
            'value' => function ($row) use ($form, $p_o_item) {
                return $form->textFieldRow($row, 'product_name', array(
                    'label' => false,
                    'name' => "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][product_name]",
                    'class' => 'sian-commercial-checked-grid-2-subitem-product-name sian-force-tooltip',
                    'readonly' => true,
                    'disabled' => !$p_o_item->checked,
                    'title' => $row->product_name,
                ));
            },
        ));

        array_push($a_columns, array(
            'header' => 'Afec. IGV',
            'headerHtmlOptions' => array('style' => 'width:8%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($p_o_item, $a_igv_affections) {
                $s_name = "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][igv_affection]";
                $s_class = 'sian-commercial-checked-grid-2-subitem-igv-affection';
                return $this->renderIGVAffectionSelect($row, $a_igv_affections, $s_name, $s_class, true, !$p_o_item->checked);
            },
            'footer' => '<b>TOTALES:</b>',
        ));

        array_push($a_columns, array(
            'header' => 'Perc?',
            'headerHtmlOptions' => array('style' => 'width:2%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($p_o_item) {
                return $this->controller->widget('application.widgets.USCheckBox', array(
                    'name' => "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][perception_affected]",
                    'value' => $row->perception_affected,
                    'htmlOptions' => array(
                        'class' => 'sian-commercial-checked-grid-2-subitem-perception-affected',
                        'readonly' => true,
                        'disabled' => !$p_o_item->checked
                    )), true);
            },
            'footer' => SIANForm::textFieldNonActive(false, null, 0, array('class' => 'sian-commercial-checked-grid-2-subitems-net',
                'readonly' => true,
                'style' => 'text-align:right'
            ))
        ));

        //Footer
        $s_net_footer = SIANForm::textFieldNonActive(false, null, 0, array('class' => 'sian-commercial-checked-grid-2-subitems-igv',
                    'readonly' => true,
                    'style' => 'text-align:right'
        ));

        $s_total_footer = SIANForm::textFieldNonActive(false, null, 0, array('class' => 'sian-commercial-checked-grid-2-subitems-total',
                    'readonly' => true,
                    'style' => 'text-align:right'
        ));

        if ($this->model->movement->validate_stock == 1) {
            array_push($a_columns, array(
                'header' => 'Stock *',
                'headerHtmlOptions' => array('style' => 'width:7%;text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:right;'),
                'type' => 'raw',
                'value' => function ($row) use ($s_stock_mode, $s_emission_date, $i_exclude_id, $i_store_id) {
                    return $this->widget('application.widgets.USLink', array(
                        'route' => '/logistic/merchandise/explainCommercialStock',
                        'label' => '0',
                        'title' => '¿Por qué veo este stock?',
                        'class' => 'form sian-commercial-checked-grid-2-subitem-mixed-stock',
                        'data' => array(
                            'id' => $row->product_id,
                            'equivalence' => $row->equivalence,
                            'stock_mode' => $s_stock_mode,
                            'emission_date' => $s_emission_date,
                            'exclude_id' => $i_exclude_id,
                            'store_id' => $i_store_id
                        ),
                        'visible' => true
                            ), true);
                },
                'footer' => $s_net_footer
            ));
        }


        array_push($a_columns, array(
            'header' => 'Cantidad',
            'headerHtmlOptions' => array('style' => 'width:7%'),
            'type' => 'raw',
            'value' => function ($row) use ($form, $p_o_item) {

                return $form->numberFieldRow($row, 'pres_quantity', array(
                    'label' => false,
                    'name' => "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][pres_quantity]",
                    'class' => "sian-commercial-checked-grid-2-subitem-pres-quantity",
                    'readonly' => true,
                    'disabled' => !$p_o_item->checked,
                    'style' => 'text-align:right',
                ));
            },
            'footer' => $this->model->movement->validate_stock == 1 ? $s_total_footer : $s_net_footer
        ));

        array_push($a_columns, array(
            'header' => 'Pres.',
            'headerHtmlOptions' => array('style' => 'width:7%'),
            'type' => 'raw',
            'value' => function ($row) use ($p_o_item) {
                $s_name = "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][equivalence]";
                $s_class = "sian-commercial-checked-grid-2-subitem-equivalence";
                return $this->renderPresentationCell($row, $s_name, $s_class, true, !$p_o_item->checked);
            },
            'footer' => $this->model->movement->validate_stock == 1 ? '' : $s_total_footer
        ));

        array_push($a_columns, array('header' => 'Hidden',
            'headerHtmlOptions' => array('style' => 'display:none'),
            'htmlOptions' => array('style' => 'display:none'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) use ($p_o_item) {

                $html = CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][item_type_id]", $row->item_type_id, array(
                            'class' => 'sian-commercial-checked-grid-2-subitem-item-type-id',
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                if (isset($row['warehouse_id'])) {
                    $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][warehouse_id]", $row->warehouse_id, array(
                                'class' => "sian-commercial-checked-grid-2-subitem-warehouse-id",
                                'readonly' => true,
                                'disabled' => !$p_o_item->checked,
                    ));
                }

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][unit_stock]", $row->unit_stock, array(
                            'class' => "sian-commercial-checked-grid-2-subitem-unit-stock",
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][original_quantity]", $row->original_quantity, array(
                            'class' => 'sian-commercial-checked-grid-2-subitem-original-quantity',
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][allow_decimals]", $row->allow_decimals, array(
                            'class' => 'sian-commercial-checked-grid-2-subitem-allow-decimals',
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][skip_cost_validation]", $row->skip_cost_validation, array(
                            'class' => 'sian-commercial-checked-grid-2-subitem-skip-cost-validation',
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][currency]", $row->currency, array(
                            'class' => 'sian-commercial-checked-grid-2-subitem-currency',
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][min_price_pen]", $row->min_price_pen, array(
                            'class' => "sian-commercial-checked-grid-2-subitem-min-price-pen",
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][min_price_usd]", $row->min_price_usd, array(
                            'class' => "sian-commercial-checked-grid-2-subitem-min-price-usd",
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][avg_price_pen]", $row->avg_price_pen, array(
                            'class' => "sian-commercial-checked-grid-2-subitem-avg-price-pen",
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][avg_price_usd]", $row->avg_price_usd, array(
                            'class' => "sian-commercial-checked-grid-2-subitem-avg-price-usd",
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][cost_pen]", $row->cost_pen, array(
                            'class' => 'sian-commercial-checked-grid-2-subitem-cost-pen',
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][cost_usd]", $row->cost_usd, array(
                            'class' => 'sian-commercial-checked-grid-2-subitem-cost-usd',
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                //Parents
                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][parent_item_id]", $row->parent_item_id, array(
                            'class' => 'sian-commercial-checked-grid-2-subitem-parent-item-id',
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][quantity_item_id]", $row->quantity_item_id, array(
                            'class' => 'sian-commercial-checked-grid-2-subitem-quantity-item-id',
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][total_item_id]", $row->total_item_id, array(
                            'class' => 'sian-commercial-checked-grid-2-subitem-total-item-id',
                            'readonly' => true,
                            'disabled' => !$p_o_item->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][include_igv]", $row->include_igv, array(
                    'class' => 'sian-commercial-checked-grid-2-subitem-include-igv',
                    'readonly' => true,
                    'disabled' => !$p_o_item->checked,
                ));
                
                $html .= CHtml::hiddenField("Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][force_igv]", $row->force_igv, array(
                    'class' => 'sian-commercial-checked-grid-2-subitem-force-igv',
                    'readonly' => true,
                    'disabled' => !$p_o_item->checked,
                ));

                return $html;
            },
        ));

        array_push($a_columns, array(
            'header' => "Precio <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:7%;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) use ($form, $p_o_item) {

                return $form->numberFieldRow($row, 'price', array(
                    'label' => false,
                    'name' => "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][price]",
                    'class' => "sian-commercial-checked-grid-2-subitem-price",
                    'readonly' => true,
                    'disabled' => !$p_o_item->checked,
                    'style' => 'text-align:right'
                ));
            },
        ));

        array_push($a_columns, array(
            'header' => "Descuento <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:7%;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) use ($p_o_item) {
                return SIANForm::textFieldNonActive(false, "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][discount]", $row->discount, array(
                    'class' => "sian-commercial-checked-grid-2-subitem-discount",
                    'readonly' => true,
                    'disabled' => !$p_o_item->checked,
                    'style' => 'text-align:right'
                ));
            },
        ));

        array_push($a_columns, array('header' => "Subtotal <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:7%'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) use ($p_o_item) {
                return SIANForm::textFieldNonActive(false, "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][net]", 0, array(
                    'class' => "sian-commercial-checked-grid-2-subitem-net",
                    'readonly' => true,
                    'disabled' => !$p_o_item->checked,
                    'style' => 'text-align:right'
                ));
            },
        ));

        array_push($a_columns, array('header' => "IGV <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:7%'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) use ($p_o_item) {
                return SIANForm::textFieldNonActive(false, "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][igv]", 0, array(
                    'class' => "sian-commercial-checked-grid-2-subitem-igv",
                    'readonly' => true,
                    'disabled' => !$p_o_item->checked,
                    'style' => 'text-align:right'
                ));
            },
        ));

        array_push($a_columns, array('header' => "Total <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:7%'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) use ($p_o_item) {
                return SIANForm::textFieldNonActive(false, "Item[{$p_o_item->parent_item_id}][subitems][{$row->index}][total]", 0, array(
                    'class' => "sian-commercial-checked-grid-2-subitem-total",
                    'readonly' => true,
                    'disabled' => !$p_o_item->checked,
                    'style' => 'text-align:right'
                ));
            },
        ));

        array_push($a_columns, array('header' => 'Opc.',
            'headerHtmlOptions' => array('style' => 'width:3%; text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center;'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) use ($b_preview_access) {
                return $this->_renderRowButtons($row, $b_preview_access);
            },
        ));

        $gridParams = array(
            'id' => 'subitems_' . $p_o_item->parent_item_id,
            'type' => 'condensed',
            'dataProvider' => $o_dataProvider,
            'enableSorting' => false,
            'selectableRows' => 0,
            'columns' => $a_columns,
            'template' => '{items}',
            'rowCssClassExpression' => '"' . ($p_o_item->checked == 1 ? "success" : "danger") . ' sian-commercial-checked-grid-2-subitem"',
            'htmlOptions' => [
                'class' => 'sian-commercial-checked-grid-2-table sian-commercial-checked-grid-2-subitem-table',
                'style' => "display:none;",
            ]
        );
        $this->widget('application.widgets.USGridView', $gridParams);
    }

    private function _renderRowButtons($row, $preview_access) {
        $s_html = '';
        if (in_array($row->product_type, [Product::TYPE_COMBO, Product::TYPE_GROUP])) {

            if ($row->getErrors('subItems')) {
                $s_color = 'red';
            } else {
                $s_color = 'black';
            }

            $s_html .= CHtml::link("<span class='fa fa-list fa-lg {$s_color}'></span>", Strings::LINK_TEXT, [
                        'title' => 'Ver ítems',
                        'onclick' => "SIANCommercialCheckedGrid2GoToSubTable('{$this->id}', $(this), 100)"
            ]);
        }

        //Si no es grupo entonces tiene preview
        if ($row->product_type !== Product::TYPE_GROUP || $row->product_type !== Product::TYPE_NEW) {

            $s_html .= Yii::app()->controller->widget('application.widgets.USLink', array(
                'icon' => 'fa fa-eye fa-lg',
                'route' => '/logistic/product/preview',
                'label' => '',
                'title' => 'Visualizar',
                'class' => 'form',
                'data' => array(
                    'id' => $row->product_id
                ),
                'visible' => $preview_access,
                    ), true);
        }
        if (isset($row['parent_product_type']) && $row['parent_product_type'] == Product::TYPE_NEW) {

            $s_html .= Yii::app()->controller->widget('application.widgets.USLink', array(
                'icon' => 'fa fa-plus fa-lg',
                'label' => '',
                'route' => '/logistic/product/assign',
                'title' => 'Asignar Producto',
                'class' => 'form',
                'data' => [
                    'id' => $this->id,
                    'local_storage_name' => $this->local_storage_name,
                    'row_id' => $row->id,
                    'types' => json_encode([Product::TYPE_MERCHANDISE, Product::TYPE_SERVICE]),
                    'widget' => 'SIANCommercialCheckedGrid2',
                ],
                'visible' => $this->preview_access,
                    ), true);
        }

        return $s_html;
    }

    private function _processItem(&$p_o_xitem, $p_a_xpresentation, $p_s_xmprice, $p_s_xaprice) {

        if ($p_o_xitem['parent_product_type'] == Product::TYPE_NEW) {
            $p_o_xitem->min_price_pen = 0;
            $p_o_xitem->min_price_usd = 0;
            $p_o_xitem->avg_price_pen = 0;
            $p_o_xitem->avg_price_usd = 0;
        } else {
            if ($p_a_xpresentation['currency'] == Currency::PEN) {
                $p_o_xitem->min_price_pen = $p_a_xpresentation[$p_s_xmprice];
                $p_o_xitem->min_price_usd = round($p_a_xpresentation[$p_s_xmprice] / $this->model->movement->exchange_rate, $this->ifixed);

                $p_o_xitem->avg_price_pen = $p_a_xpresentation[$p_s_xaprice];
                $p_o_xitem->avg_price_usd = round($p_a_xpresentation[$p_s_xaprice] / $this->model->movement->exchange_rate, $this->ifixed);
            } else {
                $p_o_xitem->min_price_pen = round($p_a_xpresentation[$p_s_xmprice] * $this->model->movement->exchange_rate, $this->ifixed);
                $p_o_xitem->min_price_usd = $p_a_xpresentation[$p_s_xmprice];

                $p_o_xitem->avg_price_pen = round($p_a_xpresentation[$p_s_xaprice] * $this->model->movement->exchange_rate, $this->ifixed);
                $p_o_xitem->avg_price_usd = $p_a_xpresentation[$p_s_xaprice];
            }
        }
    }

}
