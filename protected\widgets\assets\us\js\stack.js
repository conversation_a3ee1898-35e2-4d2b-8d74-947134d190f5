class USStack {
    
    constructor() {
        this.stack = new Array();          
    }
    
    push(element){
        this.stack.push(element);
    }
     
    pop(){
        var dato;
        if(this.stack.length > 0){
            dato =  this.stack[this.stack.length-1]
            this.stack.pop();
            return dato;
        }else{
            return null;
        }        
    }
    
    top(){
        if(this.stack.length > 0){
            return this.stack[this.stack.length-1]
        }else{
            return null;
        }
    }
    
    static hasElements(stack){ 
        return (stack.length > 0)? true:false;
    } 
}


