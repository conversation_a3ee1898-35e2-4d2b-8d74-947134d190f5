<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANAttachment
 *
 * <AUTHOR>
 */
class SIANAttachment {

    const TYPE_OFFICE = 'office';
    const TYPE_PDF = 'pdf';
    const TYPE_GDOCS = 'gdocs';
    const TYPE_TEXT = 'text';
    const TYPE_HTML = 'html';
    const TYPE_IMAGE = 'image';
    const TYPE_VIDEO = 'video';
    const TYPE_OTHER = 'other';

    public static function getAttachmentType($p_s_filepath) {

        $s_extension = pathinfo($p_s_filepath, PATHINFO_EXTENSION);

        if (preg_match('/(doc|docx)$/i', $s_extension)) {
            return self::TYPE_OFFICE;
        }
        if (preg_match('/(xls|xlsx)$/i', $s_extension)) {
            return self::TYPE_OFFICE;
        }
        if (preg_match('/(ppt|pptx)$/i', $s_extension)) {
            return self::TYPE_OFFICE;
        }
        if (preg_match('/(pdf)$/i', $s_extension)) {
            return self::TYPE_PDF;
        }
        if (preg_match('/(tif|ai|eps)$/i', $s_extension)) {
            return self::TYPE_GDOCS;
        }
        if (preg_match('/(txt|sql|eps)$/i', $s_extension)) {
            return self::TYPE_TEXT;
        }
        if (preg_match('/(html|htm)$/i', $s_extension)) {
            return self::TYPE_HTML;
        }
        if (preg_match('/(jpeg|jpg|png|bmp|gif)$/i', $s_extension)) {
            return self::TYPE_IMAGE;
        }
        if (preg_match('/(avi|mp4)$/i', $s_extension)) {
            return self::TYPE_VIDEO;
        }
        return self::TYPE_OTHER;
    }

    public static function getResource($p_s_folder, $p_s_key) {

        if (isset($p_s_folder, $p_s_key)) {
            //
            return Yii::app()->db->createCommand()
                            ->select([
                                "R.owner_id AS movement_id",
                                "R.title AS key",
                                "R.description AS filename",
                                "R.url AS filepath",
                            ])
                            ->from('resource R')
                            ->join('movement M', 'M.movement_id = R.owner_id AND M.movement_code = :movement_code', [
                                ':movement_code' => $p_s_folder,
                            ])
                            ->where('R.`owner` = :owner AND R.type = :type AND R.title = :title', [
                                ':owner' => Movement::OWNER,
                                ':type' => Resource::TYPE_ATTACHMENT,
                                ':title' => $p_s_key,
                            ])
                            ->queryRow();
        }

        return false;
    }

    public static function compactResources($p_i_movement_id, $p_sub_type) {

        $a_resources = self::_getResourcePks($p_i_movement_id, $p_sub_type);

        $i_count = 0;
        foreach ($a_resources as $i => $a_resource_pk) {
            //Si el número no corresponde
            if ((int) $a_resource_pk['resource_number'] !== ($i + 1)) {
                Resource::model()->updateByPk($a_resource_pk, [
                    'resource_number' => ($i + 1)
                ]);
            }
            $i_count++;
        }

        return $i_count;
    }

    private static function _getResourcePks($p_i_movement_id, $p_s_sub_type) {

        return Yii::app()->db->createCommand()
                        ->select([
                            "R.owner",
                            "R.owner_id",
                            "R.type",
                            "R.sub_type",
                            "R.resource_number",
                        ])
                        ->from('resource R')
                        ->where('R.`owner` = :owner AND R.owner_id = :owner_id AND R.type = :type AND R.sub_type = :sub_type', [
                            ':owner' => Movement::OWNER,
                            ':owner_id' => $p_i_movement_id,
                            ':type' => Resource::TYPE_ATTACHMENT,
                            ':sub_type' => $p_s_sub_type,
                        ])
                        ->order('R.resource_number')
                        ->queryAll();
    }

}
