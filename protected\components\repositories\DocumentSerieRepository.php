<?php

class DocumentSerieRepository implements IDocumentSerieRepository {

    /**
     * Retorna una única serie por defecto
     *
     * @param int $documentId
     * @param int $storeId
     * @return array|null
     */
    public function getFirstSerie($documentId, $storeId) {
        $command = $this->buildQuery($documentId, $storeId);
        return $command->queryRow();
    }

    /**
     * Retorna todas las series por defecto (por si alguna vez necesitas múltiples)
     *
     * @param int $documentId
     * @param int $storeId
     * @return array
     */
    public function getAllSeries($documentId, $storeId) {
        $command = $this->buildQuery($documentId, $storeId);
        return $command->queryAll();
    }

    /**
     * Construye la consulta SQL base
     *
     * @param int $documentId
     * @param int $storeId
     * @return CDbCommand
     */
    private function buildQuery($documentId, $storeId) {
        $conditions = "D.document_code = DS.document_code AND D.document_id = :document_id";
        $params = [':document_id' => $documentId];

        $command = Yii::app()->db->createCommand()
                ->select([
                    "DS.document_code",
                    "DS.document_serie",
                    "DS.document_correlative",
                ])
                ->from('document_serie DS')
                ->join('document D', $conditions, $params)
                ->where("DS.store_id = :store_id AND DS.store_default", [
            ':store_id' => $storeId
        ]);

        return $command;
    }

}
