<?php

class USCKEditor extends CWidget {

    public $id;
    public $form;
    public $model;
    public $cms = 'default';
    public $attribute;
    public $htmlOptions = [];
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        //
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
    }

    /**
     * Runs the widget.
     */
    public function run() {

        //Esta línea es esencial para crear el editor mendiante AJAX
        Yii::app()->clientScript->registerScript($this->id, "window.CKEDITOR_BASEPATH='" . Yii::app()->booster->_assetsUrl . "/js/ckeditor/';", CClientScript::POS_HEAD);

        echo $this->form->getForm()->ckEditorGroup($this->model, $this->attribute, array(
            'widgetOptions' => array(
                'id' => $this->id,
                'editorOptions' => array(
                    'fullpage' => 'js:true',
                    'width' => '100%',
                    'resize_maxWidth' => '100%',
                    'resize_minWidth' => '100%',
                    'filebrowserBrowseUrl' => Yii::app()->params['media_url'] . "/browse.php?type=files&cms={$this->cms}",
                    'filebrowserImageBrowseUrl' => Yii::app()->params['media_url'] . "/browse.php?type=images&cms={$this->cms}",
                    'filebrowserImageBrowseLinkUrl' => Yii::app()->params['media_url'] . "/browse.php?type=images&cms={$this->cms}",
                    'filebrowserImageUploadUrl' => Yii::app()->params['media_url'] . "/upload.php?type=images&cms={$this->cms}",
                    'filebrowserFlashBrowseUrl' => Yii::app()->params['media_url'] . "/browse.php?type=flash&cms={$this->cms}",
                    'filebrowserFlashUploadUrl' => Yii::app()->params['media_url'] . "/upload.php?type=flash&cms={$this->cms}",
                    'baseFloatZIndex' => 9000,
                )
            ),
            'wrapperHtmlOptions' => $this->htmlOptions,
        ));
    }

}
