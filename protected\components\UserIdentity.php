<?php

class UserIdentity extends USUserIdentity {

    private $_id;
    private $max_i = 2;

    public function authenticate() {

        $model = User::model()->with(array(
                    'person' => [
                        'select' => ['P.person_id', 'P.person_name', 'P.identification_type'],
                        'alias' => 'P',
                        'joinType' => 'join',
                        'with' => [
                            'seller' => [
                                'select' => ['S.seller_id'],
                                'alias' => 'S'
                            ]
                        ]
                    ],
                    'groupsBelong' => [
                        'with' => [
                            'parent' => [
                                'alias' => 'GBP',
                                'with' => [
                                    'userGroup' => [
                                        'select' => ['UGB.user_group_id', 'UGB.user_group_name'],
                                        'alias' => 'UGB',
                                        'on' => "UGB.status"
                                    ]
                                ]
                            ]
                        ],
                    ],
                    'groupsManage' => [
                        'with' => [
                            'parent' => [
                                'alias' => 'GMP',
                                'with' => [
                                    'userGroup' => [
                                        'select' => ['UGM.user_group_id', 'UGM.user_group_name'],
                                        'alias' => 'UGM',
                                        'on' => "UGM.status"
                                    ]
                                ]
                            ]
                        ],
                    ],
                    'groupsNotManage' => [
                        'with' => [
                            'parent' => [
                                'alias' => 'GNMP',
                                'with' => [
                                    'userGroup' => [
                                        'select' => ['UGNNM.user_group_id', 'UGNNM.user_group_name'],
                                        'alias' => 'UGNNM',
                                        'on' => "UGNNM.status"
                                    ]
                                ]
                            ]
                        ],
                    ],
                ))->findByAttributes([
            'username' => $this->username
                ], [
            'select' => ['U.user_id', 'U.username', 'U.password', 'U.hash', 'U.person_id', 'U.email_address', 'U.phone_number', 'U.`status`', 'U.`level`', 'U.remember_filters', 'U.restore_id', 'U.show_prices'],
            'alias' => 'U',
        ]);

        if (!isset($model) || $model->status == 0) {
            $this->errorCode = self::ERROR_USERNAME_INVALID;
        } else {
            if ($model->password !== md5($this->password)) {
                $this->errorCode = self::ERROR_PASSWORD_INVALID;
            } else {
                $this->_id = $model->username;

                // $this->setState('lastLoginTime', $user->lastLoginTime);
                $this->setState('user_id', $model->user_id);
                $this->setState('username', $model->username);
                $this->setState('person_id', $model->person_id);
                if ($model->person->getPersonType() === Person::TYPE_NATURAL) {
                    $this->setState('firstname', $model->person->firstname);
                    $this->setState('lastname', $model->person->paternal . ' ' . $model->person->maternal);
                } else {
                    $this->setState('firstname', $model->person->toString());
                    $this->setState('lastname', '');
                }
                $this->setState('hash', $model->hash);
                $this->setState('person_name', $model->person->toString());
                $this->setState('email_address', $model->email_address);
                $this->setState('phone_number', $model->phone_number);
                $this->setState('seller_id', isset($model->person->seller) ? $model->person->seller->seller_id : null);
                $this->setState('remember_filters', $model->remember_filters);
                $this->setState('level', $model->level);
                $this->setState('person_name', $model->person->toString());
                $this->setState('restore_id', $model->restore_id);
                $this->setState('show_prices', $model->show_prices);

                $a_groups_you_belong = [];
                $a_groups_you_manage = [];
                $a_groups_not_manage = [];

                foreach ($model->groupsBelong as $o_group) {
                    if (isset($o_group->parent->userGroup)) {
                        $a_groups_you_belong[$o_group->parent->userGroup->user_group_id] = $o_group->parent->userGroup->user_group_name;
                    }
                }
                foreach ($model->groupsManage as $o_group) {
                    if (isset($o_group->parent->userGroup)) {
                        $a_groups_you_manage[$o_group->parent->userGroup->user_group_id] = $o_group->parent->userGroup->user_group_name;
                    }
                }
                foreach ($model->groupsNotManage as $o_group) {
                    if (isset($o_group->parent->userGroup)) {
                        $a_groups_not_manage[$o_group->parent->userGroup->user_group_id] = $o_group->parent->userGroup->user_group_name;
                    }
                }
                $this->setState('groups_you_belong', $a_groups_you_belong);
                $this->setState('groups_you_manage', $a_groups_you_manage);
                $this->setState('groups_not_manage', $a_groups_not_manage);

                //Seteamos action
                $this->generateUrlActions();
                //Seteamos objetos
                $this->generateObjects();
                //Seteamos objetos confirmaciones
                $this->generateConfirmations();

                $this->errorCode = self::ERROR_NONE;
            }
        }

        return !$this->errorCode;
    }

    public function getId() {
        return $this->_id;
    }

    private function generateUrlActions() {
        $o_model = User::model()->with([
                    'actionObjs' => [
                        'select' => ['UA.module', 'UA.controller', 'UA.action', 'UA.`type`'],
                        'with' => [
                            'children' => [
                                'select' => ['UAC.module', 'UAC.controller', 'UAC.action', 'UAC.`type`'],
                                'alias' => 'UAC',
                            ]
                        ]
                    ]
                ])->findByAttributes([
            'username' => $this->username
                ], [
            'select' => ['U.username'],
            'alias' => 'U',
        ]);

        $this->setState('urlActions', $this->_generateUrlActions($o_model->actionObjs, 1));
    }

    private function _generateUrlActions($actionObjs, $i) {

        $items = [];
        foreach ($actionObjs as $actionObj) {

            //Permiso especial para explorar imágenes
            if ($actionObj->module == 'cms' && $actionObj->controller == 'explore' && $actionObj->action == 'index') {
                $this->setState('can_browse_server', true);
            }

            $items[] = array(
                'url' => Yii::app()->controller->createUrl("/{$actionObj->module}/{$actionObj->controller}/{$actionObj->action}"),
                'type' => $actionObj->type,
                'children' => $i < $this->max_i ? $this->_generateUrlActions($actionObj->children, $i + 1) : []
            );
        }
        return $items;
    }

    private function generateObjects() {

        $a_types = [
            OwnerPair::TYPE_USER_BUSINESS_UNIT,
            OwnerPair::TYPE_USER_STORE,
            OwnerPair::TYPE_USER_WAREHOUSE,
            OwnerPair::TYPE_USER_CASHBOX];

        $a_objects = Yii::app()->db->createCommand()->select([
                    "OP2.owner",
                    "OP2.owner_id",
                    "OP2.default"
                ])
                ->from('owner_pair OP1')
                ->join('owner_pair OP2', 'OP2.parent_id = OP1.pair_id')
                ->where(['IN', 'OP1.type', $a_types])
                ->andWhere("OP1.`owner` = :owner AND OP1.owner_id = :owner_id", [
                    ':owner' => User::OWNER,
                    ':owner_id' => $this->getState('user_id')
                ])
                ->queryAll();
        //
        $a_business_unit_ids = [];
        $a_store_ids = [];
        $a_warehouse_ids = [];
        $a_cashbox_ids = [];
        //
        $i_default_business_unit = null;
        $i_default_store = null;
        $i_default_warehouse = null;
        $i_default_cashbox = null;

        foreach ($a_objects as $a_object) {
            switch ($a_object['owner']) {
                case BusinessUnit::OWNER:
                    $a_business_unit_ids[] = (int) $a_object['owner_id'];
                    if ($a_object['default'] == 1) {
                        $i_default_business_unit = (int) $a_object['owner_id'];
                    }
                    break;
                case Store::OWNER:
                    $a_store_ids[] = (int) $a_object['owner_id'];
                    if ($a_object['default'] == 1) {
                        $i_default_store = (int) $a_object['owner_id'];
                    }
                    break;
                case Warehouse::OWNER:
                    $a_warehouse_ids[] = (int) $a_object['owner_id'];
                    if ($a_object['default'] == 1) {
                        $i_default_warehouse = (int) $a_object['owner_id'];
                    }
                    break;
                case Cashbox::OWNER:
                    $a_cashbox_ids[] = (int) $a_object['owner_id'];
                    if ($a_object['default'] == 1) {
                        $i_default_cashbox = (int) $a_object['owner_id'];
                    }
                    break;
            }
        }
        //
        $this->setState('business_unit_ids', $a_business_unit_ids);
        $this->setState('store_ids', $a_store_ids);
        $this->setState('warehouse_ids', $a_warehouse_ids);
        $this->setState('cashbox_ids', $a_cashbox_ids);
        //
        $this->setState('default_business_unit', $i_default_business_unit);
        $this->setState('default_store', $i_default_store);
        $this->setState('default_warehouse', $i_default_warehouse);
        $this->setState('default_cashbox', $i_default_cashbox);
    }

    private function generateConfirmations() {

        $a_confirmations = Yii::app()->db->createCommand()->select([
                    "S.scenario_id",
                    "S.route",
                    "M.value AS option_type",
                ])
                ->from('owner_pair OP1')
                ->join('owner_pair OP2', 'OP2.parent_id = OP1.pair_id')
                ->join('owner_pair OP3', 'OP3.parent_id = OP2.pair_id', [
                ])
                ->join('scenario S', 'S.scenario_id = OP2.owner_id')
                ->join('multitable M', 'M.multi_id = OP3.owner_id')
                ->where("OP1.type = :type AND OP1.`owner` = :owner AND OP1.owner_id = :owner_id", [
                    ':type' => OwnerPair::TYPE_USER_CONFIRMATION,
                    ':owner' => User::OWNER,
                    ':owner_id' => $this->getState('user_id'),
                ])
                ->queryAll();

        $this->setState('confirmations', $a_confirmations);
    }

    public static function canBrowseServer() {
        return parent::canBrowseServer() && Yii::app()->user->getState('can_browse_server');
    }

}
