<?php

/**
 * @preserve jQuery DateTimePicker plugin v2.4.1
 * @homepage http://xdsoft.net/jqplugins/datetimepicker/
 * (c) 2014, Chupurnov Valeriy.
 */
class USDatePickerFrom extends USDatePicker {

    public $fromModel;
    public $fromAttribute;

    /**
     * Initializes the widget.
     */
    public function init() {
        $this->options['minDate'] = $this->fromModel->{$this->fromAttribute};

        parent::init();

        Yii::app()->clientScript->registerScript($this->id . '_functions', "
        
        $('#{$this->id}').data('changeEmission', function(changeData){
            var currentDateInput = $('#{$this->id}');

            currentDateInput.datetimepicker({
                minDate: changeData.emission_date,
            });

            if(currentDateInput.val().length > 0 && $.datepicker.parseDate('" . Yii::app()->params['date_js_format'] . "', changeData.emission_date) > $.datepicker.parseDate('" . Yii::app()->params['date_js_format'] . "', currentDateInput.val()))
            {
                currentDateInput.val(changeData.emission_date).change();
                $.notify('Ha cambiado la {$this->model->getAttributeLabel($this->attribute)} por ser menor que la {$this->fromModel->getAttributeLabel($this->fromAttribute)}', {
                    className: 'error',
                    showDuration: 30,
                    hideDuration: 50,
                    autoHideDelay: 3000,
                });
            }
        });
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        parent::run();
    }

}
