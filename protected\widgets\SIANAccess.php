<?php

class SIANAccess extends CWidget {

    public $id;
    public $form;
    public $model;
    public $export_attribute = null;
    public $modal_id;
    public $modules = [];
    public $global_check_items = [];
    public $preffix = null;
    //PRIVATE
    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;

        if (!isset($this->preffix)) {
            throw new Exception('Debe especificar un prefijo');
        }

        if (!isset($this->model)) {
            throw new Exception('Debe especificar un modelo');
        }

        if (!method_exists($this->model, 'searchAction')) {
            throw new Exception("El modelo debe tener su correspondiente método 'searchAction'");
        }

        if (!isset($this->export_attribute)) {
            throw new Exception('Debe especificar el atributo que sirve para la exportación');
        }

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //PRIVATE
//        $this->account_code_input_id = $this->controller->getServerId();
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-access.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id, "

        //ACCESS
        var divObj = $('#{$this->id}');
        divObj.data('preffix', '{$this->preffix}');
        divObj.data('export_url', '{$this->controller->createUrl('/widget/exportAccess')}?key={$this->model->{$this->export_attribute}}');                

        $(document).ready(function() {                  
            SIANAccessInit('{$this->id}');
        });
        
        //Al exportar
        $('body').on('click', '#{$this->id}_export', function () {
            SIANAccessExport('{$this->id}');
        });
        
        //Evento al hacer click en un combobox
        $('body').on('click', '#{$this->id} input.{$this->preffix}-access', function () {
            SIANAccessCheck('{$this->id}', $(this));
        });
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Accesos',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-1 col-md-1 col-sm-3 col-xs-12'>";
        echo $this->_renderCheckBoxes();
        echo "</div>";
        echo "<div class='col-lg-11 col-md-11 col-sm-9 col-xs-12'>";
        echo $this->_renderModules($this->modules);
        echo CHtml::hiddenField("Actions_control", 1);
        echo "</div>";
        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12 text-right'>";
        echo $this->_renderButtons();
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

    private function _renderModules($p_a_modules) {
        $a_tabs = [];
        //for ($i = 0; $i < count($p_a_modules); $i++) {
        $i = 0;
        foreach ($p_a_modules as $module) {

            $a_tabs[] = [
                'id' => $this->controller->getServerId(),
                'active' => $i == 0,
                'label' => USString::htmlMultiline($module['title'], 30),
                'content' => $this->_renderModule($module)
            ];
            $i = $i + 1;
        }

        if (count($a_tabs) > 0) {
            return $this->widget('booster.widgets.TbTabs', [
                        'id' => $this->controller->getServerId(),
                        'type' => 'tabs',
                        'placement' => 'left',
                        'encodeLabel' => false,
                        'tabs' => $a_tabs,
                        'htmlOptions' => array(
                            'id' => $this->controller->getServerId(),
                        )
                            ], true);
        } else {
            return "<i>Por ahora no hay permisos que se puedan asignar directamente</i>";
        }
    }

    private function _renderMenus($p_a_menus) {
        $a_tabs = [];
        //for ($i = 0; $i < count($p_a_modules); $i++) {
        $i = 0;
        foreach ($p_a_menus as $menu) {

            $a_tabs[] = [
                'id' => $this->controller->getServerId(),
                'active' => $i == 0,
                'label' => USString::htmlMultiline($menu['title'], 30),
                'content' => $this->_renderMenu($menu)
            ];
            $i = $i + 1;
        }

        if (count($a_tabs) > 0) {
            return $this->widget('booster.widgets.TbTabs', [
                        'id' => $this->controller->getServerId(),
                        'type' => 'tabs',
                        'placement' => 'left',
                        'encodeLabel' => false,
                        'tabs' => $a_tabs,
                        'htmlOptions' => array(
                            'id' => $this->controller->getServerId(),
                        )
                            ], true);
        } else {
            return "<i>Por ahora no hay permisos que se puedan asignar directamente</i>";
        }
    }

    private function _renderModule($p_o_module) {
        $s_html = "<div class='row'>";
        $s_html .= "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-2'>";
        $s_html .= $this->_renderCheckBoxes($p_o_module['module']);
        $s_html .= "</div>";
        $s_html .= "<div class='col-lg-10 col-md-10 col-sm-10 col-xs-10'>";
        $s_html .= $this->_renderMenus($p_o_module['menus']);
        $s_html .= "</div>";
        $s_html .= "</div>";

        return $s_html;
    }

    private function _renderMenu($p_o_menu) {
        $s_html = "<div class='row'>";
        $s_html .= "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $s_html .= $this->_renderControllers($p_o_menu['controllers']);
        $s_html .= "</div>";
        $s_html .= "</div>";

        return $s_html;
    }

    private function _renderControllers($p_a_controllers) {
        $a_tabs = [];
        for ($i = 0; $i < count($p_a_controllers); $i++) {
            $a_tabs[] = array(
                'id' => $this->controller->getServerId(),
                'active' => $i == 0,
                'label' => USString::htmlMultiline($p_a_controllers[$i]->title, 30),
                'content' => $this->_renderController($p_a_controllers[$i])
            );
        }

        return $this->widget('booster.widgets.TbTabs', [
                    'id' => $this->controller->getServerId(),
                    'type' => 'tabs',
                    'placement' => 'left',
                    'encodeLabel' => false,
                    'tabs' => $a_tabs,
                    'htmlOptions' => array(
                        'id' => $this->controller->getServerId(),
                    )
                        ], true);
    }

    private function _renderController($p_o_controller) {
        $s_html = "<div class='row'>";
        $s_html .= "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $s_html .= $this->_renderCheckboxes($p_o_controller->module, $p_o_controller->controller);
        $s_html .= $this->_renderActions($p_o_controller->actionObjs);
        $s_html .= "</div>";
        $s_html .= "</div>";

        return $s_html;
    }

    private function _renderActions($p_a_actions) {
        $s_html = '';
        foreach ($p_a_actions as $o_action) {
            $s_html .= $this->_renderAction($o_action);
        }
        return $s_html;
    }

    private function _renderAction($p_o_action) {
        $a_data = [
            'name' => "Actions[{$p_o_action->module}][{$p_o_action->controller}][{$p_o_action->action}]",
            'checked' => is_numeric($this->model->searchAction($p_o_action->module, $p_o_action->controller, $p_o_action->action)),
            'onclick' => null,
            'module' => $p_o_action->module,
            'controller' => $p_o_action->controller,
            'action' => $p_o_action->action,
            'title' => $p_o_action->title,
            'description' => $p_o_action->description
        ];

        return $this->_renderCheckbox($a_data);
    }

    private function _renderCheckBoxes($p_s_module = null, $p_s_controller = null) {

        $a_checkboxes = [
            [
                'name' => null,
                'checked' => false,
                'module' => $p_s_module,
                'controller' => $p_s_controller,
                'action' => null,
                'title' => 'Todos',
            ]
        ];
        //Esto NO se renderiza cuando ambos están seteados
        if (!isset($p_s_module) || !isset($p_s_controller)) {
            foreach ($this->global_check_items as $s_action => $s_title) {
                $a_checkboxes[] = [
                    'name' => null,
                    'checked' => false,
                    'module' => $p_s_module,
                    'controller' => $p_s_controller,
                    'action' => $s_action,
                    'title' => $s_title,
                ];
            }
        }

        $s_html = '';
        foreach ($a_checkboxes as $a_checkbox) {
            $s_html .= $this->_renderCheckbox($a_checkbox);
        }

        return $s_html;
    }

    private function _renderCheckbox($p_a_data) {
        $s_id = $this->controller->getServerId();
        $s_content = CHtml::label(CHtml::checkBox($p_a_data['name'], $p_a_data['checked'], array(
                            'id' => $s_id,
                            'class' => "{$this->preffix}-access {$this->preffix}-{$p_a_data['module']}-{$p_a_data['controller']}-{$p_a_data['action']}",
                            'data-module' => $p_a_data['module'],
                            'data-controller' => $p_a_data['controller'],
                            'data-action' => $p_a_data['action'],
                        )) . $p_a_data['title'], $s_id, [
                    'title' => isset($p_a_data['description']) ? $p_a_data['description'] : null,
        ]);

        return CHtml::tag('div', array('class' => 'checkbox'), $s_content, true);
    }

    private function _renderButtons() {
        return $this->widget('booster.widgets.TbButtonGroup', array('buttons' => array(
                        [
                            'icon' => 'upload white',
                            'context' => 'success',
                            'label' => 'Importar',
                            'htmlOptions' => [
                                'id' => $this->id . '_import',
                                'onclick' => "$('#{$this->id}_file').click();"
                            ],
                        ],
                        [
                            'icon' => 'download',
                            'context' => 'default',
                            'label' => 'Exportar',
                            'htmlOptions' => [
                                'id' => $this->id . '_export',
                            ],
                        ],
                    )), true) .
                CHtml::fileField(null, null, array(
                    'id' => $this->id . '_file',
                    'class' => 'hide',
                )) .
                "<iframe id='{$this->id}_frame' src='' class='hide'></iframe>";
    }

}
