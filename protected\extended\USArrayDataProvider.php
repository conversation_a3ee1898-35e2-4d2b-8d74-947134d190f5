<?php

class USArrayDataProvider extends CArrayDataProvider {

    public $keyField;
    //En caso de setear un modelo para poder etiquetar las columnas
    public $model = null;
    public $fetchMode = PDO::FETCH_ASSOC;

    protected function fetchData() {

        $a_items = parent::fetchData();

        if ($this->fetchMode === PDO::FETCH_OBJ) {
            $data = [];
            foreach ($a_items as $a_item) {
                $data[] = (object) $a_item;
            }
            return $data;
        }

        return $a_items;
    }

    protected function fetchKeys() {
        $keys = [];
        foreach ($this->getData() as $i => $data) {


            $key = $this->keyField === null ? $data->getPrimaryKey() : $this->getPrimaryKey($data);
            $keys[$i] = is_array($key) ? implode(',', $key) : $key;
        }
        return $keys;
    }

    private function getPrimaryKey($data) {

        if (is_array($this->keyField)) {
            $key = [];

            foreach ($this->keyField as $field) {
                array_push($key, is_object($data) ? $data->{$field} : $data[$field]);
            }

            return $key;
        } else {
            return is_object($data) ? $data->{$this->keyField} : $data[$this->keyField];
        }
    }

}
