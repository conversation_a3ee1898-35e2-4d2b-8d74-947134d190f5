<?php

class S<PERSON>NUser extends CWidget {

    //CONST
    const MODE_1 = 1;
    const MODE_2 = 2;
    const MODE_3 = 3;

    //Var
    public $form;
    public $model;
    public $mode = 1;
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Cuenta de acceso',
            'headerIcon' => 'user'
        ));

        switch ($this->mode) {
            case self::MODE_1:
                echo "<div class='row'>";
                echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
                echo $this->renderUsername();
                echo "</div>";
                echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
                echo $this->renderPassword();
                echo "</div>";
                echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
                echo $this->renderPasswordRepeat();
                echo "</div>";
                echo "</div>";
                break;
            case self::MODE_2:
                echo "<div class='row'>";
                echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
                echo $this->renderOldPassword();
                echo "</div>";
                echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
                echo $this->renderPassword();
                echo "</div>";
                echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
                echo $this->renderPasswordRepeat();
                echo "</div>";
                echo "</div>";
                break;
            case self::MODE_3:
                echo "<div class='row'>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderPassword();
                echo "</div>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderPasswordRepeat();
                echo "</div>";
                echo "</div>";
                break;
        }

        $this->endWidget();
    }

    private function renderUsername() {
        return $this->form->textFieldRow($this->model, 'username', array(
                    'maxlength' => 80,
                    'placeholder' => $this->model->getAttributeLabel('username'),
        ));
    }

    private function renderPassword() {
        return $this->form->passwordFieldRow($this->model, 'password', array(
                    'placeholder' => $this->model->getAttributeLabel('password'),
        ));
    }

    private function renderPasswordRepeat() {
        return $this->form->passwordFieldRow($this->model, 'password_repeat', array(
                    'placeholder' => $this->model->getAttributeLabel('password_repeat'),
        ));
    }

    private function renderOldPassword() {
        return $this->form->passwordFieldRow($this->model, 'old_password', array(
                    'placeholder' => $this->model->getAttributeLabel('old_password'),
        ));
    }

}
