<?php

// class DocumentoEmision extends DocumentContract
class DocumentIssue extends DocumentContract
{
    protected $data;

    public function __construct()
    {
        // operacion: su valor siempre deberá ser "generar_comprobante" para enviar FACTURAS, BOLETAS, NOTAS DE CRÉDITO o DÉBITO
        $this->data['operacion'] = SIANFENubeFacT::$GENERATE_RECEIPT;
        $this->data['sunat_transaction'] = self::$VENTA_INTERNA;
        // Inicializando los valores posiblemente vacios
        $this->initializeValues();
        //Valores por defecto
        $this->setDateOfIssue(Date('d-m-Y')); // setFechaEmision
    }

    public function initializeValues()
    {
        $this->data['fecha_de_vencimiento'] = "";
        $this->data['tipo_de_cambio'] = "";
        $this->data['descuento_global'] = ""; // 0
        $this->data['total_descuento'] = "";
        $this->data['total_anticipo'] = "";
        $this->data['total_inafecta'] = "";
        $this->data['total_exonerada'] = "";
        $this->data['total_gratuita'] = "";
        $this->data['total_otros_cargos'] = "";
        $this->data['percepcion_tipo'] = 1; // 1 = PERCEPCIÓN VENTA INTERNA - TASA 2%
        $this->data['percepcion_base_imponible'] = "";
        $this->data['total_percepcion'] = "";
        $this->data['total_incluido_percepcion'] = "";
        $this->data['detraccion'] = false; /// chequear
        $this->data['observaciones'] = "";
        $this->data['documento_que_se_modifica_tipo'] = "";
        $this->data['documento_que_se_modifica_serie'] = "";
        $this->data['documento_que_se_modifica_numero'] = "";
        $this->data['tipo_de_nota_de_credito'] = "";
        $this->data['tipo_de_nota_de_debito'] = "";
        $this->data['enviar_automaticamente_a_la_sunat'] = true;
        $this->data['enviar_automaticamente_al_cliente'] = false;
        $this->data['codigo_unico'] = "";
        $this->data['condiciones_de_pago'] = "";
        $this->data['medio_de_pago'] = "";
        $this->data['placa_vehiculo'] = "";
        $this->data['orden_compra_servicio'] = "";
        $this->data['tabla_personalizada_codigo'] = "";
        $this->data['formato_de_pdf'] = "";
    }

    /**
     * ATRIBUTO: enviar_automaticamente_a_la_sunat
     * VALOR: 1 = false = FALSO (En minúsculas), true = VERDADERO (En minúsculas)
     * TIPO DE DATO: Boolean
     * REQUISITO: Condicional
     * LONGITUD: Hasta 5
     */
    public function setSendAutomaticallyToSunat($p_bool)
    {
        $this->data['enviar_automaticamente_a_la_sunat'] = $p_bool;
    }

    /**
     * ATRIBUTO: serie
     * VALOR: Empieza con "F" para FACTURAS y NOTAS ASOCIADAS.
     *   Empieza con "B" para BOLETAS DE VENTA y NOTAS ASOCIADAS
     *   Si está comunicando un comprobante emitido en contingencia, la serie debe empezar NO debe empezar con "F" ni con "B". Debería empezar con "0", ejemplo: "0001"
     * TIPO DE DATO: String
     * REQUISITO: Obligatorio
     * LONGITUD: 4 exactos
     */
    public function setSeries($p_s_serie)
    {
        $s_serie = isset($p_s_serie) ? $p_s_serie : "";
        if ($s_serie === "") {
            throw new Exception("La Serie es requerida");
        }
        $this->data['serie'] = $s_serie;
    }

    /**
     * ATRIBUTO: número
     * VALOR: Número correlativo del documento, sin ceros a la izquierda
     * TIPO DE DATO: Integer
     * REQUISITO: Obligatorio
     * LONGITUD: 1 hasta 8
     */
    public function setCorrelativeNumber($p_s_correlative_number)
    {
        $s_correlative_number = isset($p_s_correlative_number) ? $p_s_correlative_number : "";
        if ($s_correlative_number === "") {
            throw new Exception("El número de correlativo es requerido");
        }
        $this->data['numero'] = $s_correlative_number;
    }

    /**
     * ATRIBUTO: sunat_transaction
     * VALOR: La mayoría de veces se usa el 1, las demás son para tipos de operaciones muy especiales, 
     * no dudes en consultar con nosotros para más información: 1 = VENTA INTERNA, 2 = EXPORTACIÓN, 
     * 4 = VENTA INTERNA – ANTICIPOS, 29 = VENTAS NO DOMICILIADOS QUE NO CALIFICAN COMO EXPORTACIÓN., 
     * 30 = OPERACIÓN SUJETA A DETRACCIÓN., 33 = DETRACCIÓN - SERVICIOS DE TRANSPORTE CARGA , 
     * 34 = OPERACIÓN SUJETA A PERCEPCIÓN, 32 = DETRACCIÓN - SERVICIOS DE TRANSPORTE DE PASAJEROS., 31 = DETRACCIÓN - RECURSOS HIDROBIOLÓGICOS
     * TIPO DE DATO: Integer
     * REQUISITO: Obligatorio
     * LONGITUD: 1 hasta 2
     */
    public function setSunatTransaction($p_i_sunat_transaction)
    {
        $i_sunat_transaction = isset($p_i_sunat_transaction) ? $p_i_sunat_transaction : null;
        if (is_null($i_sunat_transaction)) {
            throw new Exception("El Tipo de transacción es requerido");
        }
        $this->data['sunat_transaction'] = $i_sunat_transaction;
    }

    /**
     * PARAMS: (cliente_tipo_de_documento, cliente_numero_de_documento, cliente_denominacion, cliente_direccion)
     * 'cliente_numero_de_documento': Ejemplo: RUC del CLIENTE, número de DNI, Etc.
     * 'cliente_denominacion': Ejemplo: Razón o nombre completo del CLIENTE
     * 'cliente_direccion': Dirección completa (OPCIONAL en caso de ser una BOLETA DE VENTA o NOTA ASOCIADA).
     * TIPOS DE DATO: (String, String, String, String)
     * REQUISITO: (Obligatorio, Obligatorio, Obligatorio, Obligatorio)
     * LONGITUD: (1 exacto, 1 hasta 15, 1 hasta 100,  1 hasta 100)
     */
    public function setCustomer($p_identification_type, $p_identification_number, $p_customer_name, $p_customer_address)
    {
        $this->setCustomerDocumentType($p_identification_type);
        $this->setCustomerDocumentNumber($p_identification_number);
        $this->setCustomerName($p_customer_name);
        $this->setCustomerAddress($p_customer_address);
    }

    /**
     * ATRIBUTO: cliente_tipo_de_documento
     * VALOR: 6 = RUC - REGISTRO ÚNICO DE CONTRIBUYENTE, 1 = DNI - DOC. NACIONAL DE IDENTIDAD, - = VARIOS - VENTAS MENORES A S/.700.00 Y OTROS, 
     *   4 = CARNET DE EXTRANJERÍA, 7 = PASAPORTE, A = CÉDULA DIPLOMÁTICA DE IDENTIDAD, 0 = NO DOMICILIADO, SIN RUC (EXPORTACIÓN)
     * TIPO DE DATO: String
     * REQUISITO: Obligatorio
     * LONGITUD: 1 exacto
     */
    public function setCustomerDocumentType($p_s_identification_type)
    {
        $s_identification_type = isset($p_s_identification_type) ? $p_s_identification_type : "";
        if ($s_identification_type === "") {
            throw new Exception("El tipo de documento cliente es obligatorio");
        }
        $this->data['cliente_tipo_de_documento'] = $s_identification_type;
    }

    /**
     * ATRIBUTO: cliente_numero_de_documento
     * VALOR: Ejemplo: RUC del CLIENTE, número de DNI, Etc.
     * TIPO DE DATO: String
     * REQUISITO: Obligatorio
     * LONGITUD: 1 hasta 15
     */
    public function setCustomerDocumentNumber($p_s_identification_number)
    {
        $s_identification_number = isset($p_s_identification_number) ? $p_s_identification_number : "";
        if ($s_identification_number === "") {
            throw new Exception("El número de documento cliente es obligatorio");
        }
        $this->data['cliente_numero_de_documento'] = $s_identification_number;
    }

    /**
     * ATRIBUTO: cliente_denominacion
     * VALOR: Ejemplo: Razón o nombre completo del CLIENTE
     * TIPO DE DATO: String
     * REQUISITO: Obligatorio
     * LONGITUD: 1 hasta 100
     */
    public function setCustomerName($p_s_customer_name)
    {
        $s_customer_name = isset($p_s_customer_name) ? $p_s_customer_name : "";
        if ($s_customer_name === "") {
            throw new Exception("El nombre del cliente es obligatorio");
        }
        $this->data['cliente_denominacion'] = $s_customer_name;
    }

    /**
     * ATRIBUTO: cliente_direccion
     * VALOR: Dirección completa (OPCIONAL en caso de ser una BOLETA DE VENTA o NOTA ASOCIADA).
     * TIPO DE DATO: String
     * REQUISITO: Obligatorio
     * LONGITUD: 1 hasta 100
     */
    public function setCustomerAddress($p_s_customer_address)
    {
        $s_customer_address = isset($p_s_customer_address) ? $p_s_customer_address : "";
        if ($s_customer_address === "") {
            throw new Exception("La dirección del cliente es obligatoria");
        }
        $this->data['cliente_direccion'] = $s_customer_address;
    }

    /**
     * ATRIBUTO: cliente_email
     * VALOR: Dirección de email debe ser válido.
     * TIPO DE DATO: String
     * REQUISITO: Opcional
     * LONGITUD: 1 hasta 250
     */
    public function addCustomerEmail($p_s_email)
    {
        $this->setSendAutomaticallyToClient(false);
        if (!isset($this->data['cliente_email'])) {
            $this->data['cliente_email'] = $p_s_email;
        } elseif (!isset($this->data['cliente_email_1'])) {
            $this->data['cliente_email_1'] = $p_s_email;
        } elseif (!isset($this->data['cliente_email_2'])) {
            $this->data['cliente_email_2'] = $p_s_email;
        } else {
            throw new \Exception('Solo puede agregar máximo 3 correo electrónicos.');
        }
    }

    /**
     * ATRIBUTO: fecha_de_emision
     * VALOR: Debe ser la fecha actual. Formato DD-MM-AAAA .Ejemplo: 10-05-2017
     * TIPO DE DATO: Date
     * REQUISITO: Obligatorio
     * LONGITUD: 10 exactos
     */
    public function setDateOfIssue($p_s_emission_date)
    {
        $s_emission_date = isset($p_s_emission_date) ? $p_s_emission_date : "";
        if ($s_emission_date === "") {
            throw new Exception("La Fecha de Emisión es obligatoria");
        } else {
            if (!preg_match('/[0-9]{2}\-[0-9]{2}\-[0-9]{4}/i', $s_emission_date)) {
                $emission_date_formated = strtr($s_emission_date, '/', '-');
                $this->data['fecha_de_emision'] = date("d-m-Y", strtotime($emission_date_formated));
            } else {
                $this->data['fecha_de_emision'] = $s_emission_date;
            }
        }
    }

    /**
     * ATRIBUTO: fecha_de_vencimiento
     * VALOR: Deber ser fecha posterior a la fecha de emisión
     * TIPO DE DATO: Date
     * REQUISITO: Opcional
     * LONGITUD: 10 exactos
     */
    public function setDueDate($p_s_date)
    {
        if (!preg_match('/[0-9]{2}\-[0-9]{2}\-[0-9]{4}/i', $p_s_date)) {
            $expiration_date_formated = strtr($p_s_date, '/', '-');
            $this->data['fecha_de_vencimiento'] = date("d-m-Y", strtotime($expiration_date_formated));

            if (!preg_match('/[0-9]{2}\-[0-9]{2}\-[0-9]{4}/i', $this->data['fecha_de_vencimiento'])) {
                throw new \Exception('El formato de fecha debe de ser DD-MM-AAAA.');
            }
        }
    }

    /**
     * ATRIBUTO: moneda
     * VALOR: De necesitar más monedas no dude en contactarse con nosotros.
     * 1 = SOLES", 2 = DÓLARES", 3 = EUROS", 4 = “LIBRA ESTERLINA”
     * TIPO DE DATO: Integer
     * REQUISITO: Obligatorio
     * LONGITUD: 1 exacto
     */
    public function setCurrency($s_currency)
    {
        $s_currency = isset($s_currency) ? $s_currency : "";
        if ($s_currency === "") {
            throw new Exception("La Moneda es obligatoria");
        }

        switch ($s_currency) {
            case Currency::PEN:
                $this->data['moneda'] = self::$SOL;
                break;
            case Currency::USD:
                $this->data['moneda'] = self::$DOLAR;
                break;
            case "EUROS":
                $this->data['moneda'] = self::$EURO;
                break;
            case "LIBRA ESTERLINA":
                $this->data['moneda'] = self::$LIBRA_ESTERLINA;
                break;
            default:
                throw new Exception("La Moneda '{$s_currency}' no es válida");
        }
    }

    /**
     * ATRIBUTO: tipo_de_cambio
     * VALOR: Ejemplo: 3.421
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 entero con 3 decimales
     */
    public function setExchangeRate($p_f_exchange_rate)
    {
        $this->data['tipo_de_cambio'] = $p_f_exchange_rate;
    }

    /**
     * ATRIBUTO: porcentaje_de_igv
     * VALOR: Ejemplo: 18.00
     * TIPO DE DATO: Numeric
     * REQUISITO: Obligatorio
     * LONGITUD: 1 hasta 2 enteros con 2 decimales
     */
    public function setIgvPercentage($p_f_igv_percent)
    {
        $f_igv_percent = isset($p_f_igv_percent) ? $p_f_igv_percent : null;
        if (is_null($f_igv_percent)) {
            throw new Exception("El Porcentaje de Igv es obligatorio");
        }
        $this->data['porcentaje_de_igv'] = $f_igv_percent;
    }

    /**
     * ATRIBUTO: descuento_global
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setGlobalDiscount($p_f_global_discount)
    {
        $this->data['descuento_global'] = $p_f_global_discount;
    }

    /**
     * ATRIBUTO: total_descuento
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotalDiscount($p_f_total_discount)
    {
        $this->data['total_descuento'] = $p_f_total_discount;
    }

    /**
     * ATRIBUTO: total_anticipo
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotalAdvance($p_f_total_advance)
    {
        $this->data['total_anticipo'] = $p_f_total_advance;
    }

    /**
     * ATRIBUTO: total_gravada
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotalTaxed($p_f_total_taxed)
    {
        $this->data['total_gravada'] = $p_f_total_taxed;
    }

    /**
     * ATRIBUTO: total_inafecta
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotalInaffected($p_f_total_unaffected)
    {
        $this->data['total_inafecta'] = $p_f_total_unaffected;
    }

    /**
     * ATRIBUTO: total_exonerada
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotalExonerated($p_f_total_exonerated)
    {
        $this->data['total_exonerada'] = $p_f_total_exonerated;
    }

    /**
     * ATRIBUTO: total_igv
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotalIGV($p_f_igv)
    {
        $this->data['total_igv'] = $p_f_igv;
    }

    /**
     * ATRIBUTO: total_gratuita
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotalFree($p_f_total_free)
    {
        $this->data['total_gratuita'] = $p_f_total_free;
    }

    /**
     * ATRIBUTO: total_otros_cargos
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotalOtherCharges($p_f_total_other_charges)
    {
        $this->data['total_otros_cargos'] = $p_f_total_other_charges;
    }

    /**
     * ATRIBUTO: total_isc
     * VALOR: 
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotalIsc($p_f_total_isc)
    {
        $this->data['total_isc'] = $p_f_total_isc;
    }

    /**
     * ATRIBUTO: total
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Obligatorio
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotal($p_f_total)
    {
        $f_total = isset($p_f_total) ? $p_f_total : null;
        if (is_null($f_total)) {
            throw new Exception("El Total es obligatorio");
        }
        $this->data['total'] = $f_total;
    }

    /**
     * ATRIBUTO: percepcion_tipo
     * VALOR: 
     *   1 = PERCEPCIÓN VENTA INTERNA - TASA 2%
     *   2 = PERCEPCIÓN ADQUISICIÓN DE COMBUSTIBLE-TASA 1%
     *   3 = PERCEPCIÓN REALIZADA AL AGENTE DE PERCEPCIÓN CON TASA ESPECIAL - TASA 0.5%
     * TIPO DE DATO: Integer
     * REQUISITO: Condicional
     * LONGITUD: 1 exacto
     */
    public function setPerceptionType($p_i_perception_type)
    {
        $this->data['percepcion_tipo'] = $p_i_perception_type;
    }

    /**
     * ATRIBUTO: percepcion_base_imponible
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setPerceptionTaxBase($p_f_perception_taxable_base)
    {
        $this->data['percepcion_base_imponible'] = $p_f_perception_taxable_base;
    }

    /**
     * ATRIBUTO: total_percepcion
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotalPerception($p_f_total_perception)
    {
        $this->data['total_percepcion'] = $p_f_total_perception;
    }

    /**
     * ATRIBUTO: total_incluido_percepcion
     * VALOR: Ejemplo: 1305.05
     * TIPO DE DATO: Numeric
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 12 enteros con 2 decimales
     */
    public function setTotalIncludedPerception($p_f_total_included_perception)
    {
        $this->data['total_incluido_percepcion'] = $p_f_total_included_perception;
    }

    /**
     * ATRIBUTO: enviar_automaticamente_al_cliente
     * VALOR: 1 = false = FALSO (En minúsculas), true = VERDADERO (En minúsculas)
     * TIPO DE DATO: Boolean
     * REQUISITO: Condicional
     * LONGITUD: Hasta 5
     */
    public function setSendAutomaticallyToClient($p_b_send_automatically_to_client)
    {
        // task: crear variable
        $this->data['enviar_automaticamente_al_cliente'] = $p_b_send_automatically_to_client;
    }

    /**
     * ATRIBUTO: codigo_unico
     * VALOR: Usarlo sólo si deseas que controlemos la generación de documentos. Código único generado y asignado por tu sistema. Por ejemplo puede estar compuesto por el tipo de documento, serie y número correlativo.
     * TIPO DE DATO: String
     * REQUISITO: Opcional
     * LONGITUD: 1 hasta 20
     */
    public function setUniqueCode($p_s_unique_code)
    {
        $this->data['codigo_unico'] = $p_s_unique_code;
    }

    /**
     * ATRIBUTO: condiciones_de_pago
     * VALOR: Ejemplo: CRÉDITO 15 DÍAS
     * TIPO DE DATO: String
     * REQUISITO: Opcional
     * LONGITUD: 1 hasta 250
     */
    public function setPaymentTerms($p_s_payment_conditions)
    {
        $this->data['condiciones_de_pago'] = $p_s_payment_conditions;
    }

    /**
     * ATRIBUTO: medio_de_pago
     * VALOR: Ejemplo: TARJETA VISA OP: 232231
     *   Nota: Si es al Crédito, se debe de usar “venta_al_credito”
     * TIPO DE DATO: String
     * REQUISITO: Opcional
     * LONGITUD: 1 hasta 250
     */
    public function setPaymentMethod($p_s_payment_method)
    {
        $this->data['medio_de_pago'] = $p_s_payment_method;
    }

    /**
     * ATRIBUTO: placa_vehiculo
     * VALOR: Ejemplo: ALF-321
     * TIPO DE DATO: String
     * REQUISITO: Opcional
     * LONGITUD: 1 hasta 8
     */
    public function setVehiclePlate($p_s_vehicle_plate)
    {
        $this->data['placa_vehiculo'] = $p_s_vehicle_plate;
    }

    /**
     * ATRIBUTO: orden_compra_servicio
     * VALOR: Ejemplo: 21344
     * TIPO DE DATO: String
     * REQUISITO: Opcional
     * LONGITUD: 1 hasta 20
     */
    public function setOrderPurchaseService($p_s_service_purchase_order)
    {
        $this->data['orden_compra_servicio'] = $p_s_service_purchase_order;
    }

    /**
     * ATRIBUTO: orden_compra_servicio
     * VALOR: Ejemplo: 21344
     * TIPO DE DATO: String
     * REQUISITO: Opcional
     * LONGITUD: 1 hasta 20
     */
    public function setCustomTablecode($p_s_service_purchase_order)
    {
        $this->data['orden_compra_servicio'] = $p_s_service_purchase_order;
    }

    /**
     * ATRIBUTO: detraccion
     * VALOR: false = FALSO (En minúsculas), true = VERDADERO (En minúsculas)
     * TIPO DE DATO: Boolean
     * REQUISITO: Condicional
     * LONGITUD: Hasta 5
     */
    public function setDetraction($p_b_detraccion)
    {
        $this->data['detraccion'] = $p_b_detraccion;
    }

    /**
     * ATRIBUTO: observaciones
     * VALOR: Texto de 0 hasta 1000 caracteres. Si se desea saltos de línea para la representación impresa o PDF usar <br>. 
     *   Ejemplo: XXXXX <br> YYYYYY
     * TIPO DE DATO: Text
     * REQUISITO: Opcional
     * LONGITUD: Hasta 5
     */
    public function setObservations($p_s_observations)
    {
        $s_observations = str_replace("\n", '<br>', $p_s_observations);
        $this->data['observaciones'] = $s_observations;
    }

    /**
     * PARAMS: ('unidad_de_medida','codigo', 'descripcion', 'cantidad', 'valor_unitario', 'precio_unitario', 'subtotal', 'tipo_de_igv', 'igv', 'total', 'descuento', $anticipoRegularizacion)
     */
    public function addItem(
        $p_unit_of_measurement, $p_code, $p_description, $p_quantity, $p_unit_value, $p_unit_price, 
        $p_subtotal, $p_type_igv, $p_igv, $p_total, $p_discount = null, $p_a_regularization_advance = null
    ) {
        $item = [
            'unidad_de_medida'  => $p_unit_of_measurement,
            'codigo'            => $p_code,
            'descripcion'       => $p_description,
            'cantidad'          => $p_quantity,
            'valor_unitario'    => $p_unit_value,
            'precio_unitario'   => $p_unit_price,
            'descuento'         => $p_discount,
            'subtotal'          => $p_subtotal,
            'tipo_de_igv'       => $p_type_igv,
            'igv'               => $p_igv,
            'total'             => $p_total
        ];

        if (is_array($p_a_regularization_advance) && isset($p_a_regularization_advance['serie']) && isset($p_a_regularization_advance['numero'])) {
            $item['anticipo_regularizacion'] = true;
            $item['anticipo_documento_serie'] = $p_a_regularization_advance['serie'];
            $item['anticipo_documento_numero'] = $p_a_regularization_advance['numero'];
        }

        // $item['codigo_producto_sunat'] = "10000000";

        if (!isset($this->data['items'])) {
            $this->data['items'] = [];
        }
        $this->data['items'][] = $item;
    }

    public function addFee($p_i_fee, $p_s_payment_date, $p_f_amount)
    {
        $fee = [
            'cuota'         => $p_i_fee,
            'fecha_de_pago' => $this->getFormattedDate($p_s_payment_date, "fecha de pago"),
            'importe'       => $p_f_amount
        ];

        if (!isset($this->data['venta_al_credito'])) {
            $this->data['venta_al_credito'] = [];
        }

        $this->data['venta_al_credito'][] = $fee;
    }

    public function getFormattedDate($p_s_date, $p_s_name_date = "fecha")
    {
        if (!preg_match('/[0-9]{2}\-[0-9]{2}\-[0-9]{4}/i', $p_s_date)) {
            $s_date_formated = strtr($p_s_date, '/', '-');
            $newDate = date("d-m-Y", strtotime($s_date_formated));

            if (!preg_match('/[0-9]{2}\-[0-9]{2}\-[0-9]{4}/i', $newDate)) {
                throw new \Exception("El formato de {$p_s_name_date} debe de ser DD-MM-AAAA.");
            }
            return $newDate;
        }
    }

    /**
     * ATRIBUTO: formato_de_pdf
     * VALOR: Formato de PDF que se desea generar para la representación, si se deja en blanco se genera el formato definido por defecto en NUBEFACT. Se puede elegir entre A4, A5 o TICKET.
     * TIPO DE DATO: String
     * REQUISITO: Opcional
     * LONGITUD: 2 hasta 5
     */
    public function setPDFformat($p_s_pdf_format)
    {
        $this->setRaw('formato_de_pdf', $p_s_pdf_format);
    }

    public function setRaw($key, $value)
    {
        $this->data[$key] = $value;
    }

    function getArray()
    {
        return $this->data;
    }

}
