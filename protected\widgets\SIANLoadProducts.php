<?php

class SIANLoadProducts extends CWidget {

    public $id;
    public $presentationMode;
    public $onLoadProduct = "";
    public $onLoadedProducts = "";
    public $colums = 2;
    //PRIVATE
    private $controller;
    private $input_search_attribute_id;
    private $input_file_id;
    private $loader_id;
    private $url_api_proccess_file;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->input_file_id = $this->id . $this->controller->getServerId();
        $this->loader_id = $this->id . $this->controller->getServerId();
        $this->input_search_attribute_id = $this->id . $this->controller->getServerId();
        $this->url_api_proccess_file = Yii::app()->params['url_api_sian'] . 'parse-excel';
        //Registramos script
        SIANAssets::registerScriptFile('js/sian-email.js');

        Yii::app()->clientScript->registerScript($this->id, "
        $(document).ready(function() {
        
        $('#{$this->input_file_id}').change(function() {
                var file = this.files[0];
                if (file) {
                    var fileName = file.name;
                    var fileExtension = fileName.split('.').pop().toLowerCase();
                    if (fileExtension === 'xls' || fileExtension === 'xlsx') {
                        var formData = new FormData();
                        formData.append('file', file);
                    
                        $('#{$this->loader_id}').css({
                            'width': '4rem',
                            'display': 'block'
                        });
                
                        var proccessData = null;
                        var successProccessData = false;
                        
                        const labelColumns = {
                            'Código de Barras': 'barcode',
                            'ID Producto': 'product_id',
                            'Pk': 'pk',
                            'Equivalencia': 'equivalence',
                            'Cantidad': 'pres_quantity',
                            'Precio Unitario': 'amount',
                            'FOB Total': 'tfob',
                            'Kilos': 'kilos',
                            'Flete': 'freight',
                            'Seguro': 'insurance'
                        };

                        var searchAttribute =  $('#{$this->input_search_attribute_id}').val();                     
                        
                        $.ajax({
                            url: '{$this->url_api_proccess_file}',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(response) {
                                window.active_ajax--;
                                successProccessData = response.success;
                                proccessData = response.data[0];
                
                                if (successProccessData && proccessData.length > 0) {
                                
                                    //Completamos las columnas Equivalence y Pk
                                    for (var i = 0; i < proccessData.length; i++) {                                       
                                        var product_id = proccessData[i]['ID Producto'];
                                        var equivalence = parseFloat(proccessData[i]['Equivalencia'] ? proccessData[i]['Equivalencia'] : 1).toFixed(2);
                                        proccessData[i]['Equivalencia'] = equivalence;
                                        proccessData[i]['Pk'] = product_id + '-' + equivalence;                                       
                                    }

                                    //Transformamos las keys del excel por las nuevas keys según 'labelColumns'
                                    var transformedData = proccessData.map(item => {
                                        var newItem = {};
                                        for (const originalKey in labelColumns) {
                                            const newKey = labelColumns[originalKey];
                                            if (originalKey in item) {
                                                newItem[newKey] = item[originalKey];
                                            }
                                        }
                                        return newItem;
                                    });
                                    
                                    //Convertimos a objeto con 'searchAttribute' como 'clave' 
                                    const fileData = transformedData.reduce((acc, item) => {
                                        if (acc[item[searchAttribute]]) {
                                            acc[item[searchAttribute]].push(item);
                                        } else {
                                            acc[item[searchAttribute]] = [item];
                                        }
                                        return acc;
                                    }, {});
                                    
                                    const keywords = Object.keys(fileData);
                                                    
                                    $.ajax({
                                        type: 'post',
                                        url: '{$this->controller->createUrl('/widget/getDpModel')}',
                                        data: {
                                            viewClass: 'DpAllProductPresentations',
                                            scenario: 'stock',
                                            attribute: searchAttribute,
                                            attribute_in: keywords,
                                            presentationMode: {$this->presentationMode}
                                        },
                                        beforeSend: function(xhr) {
                                            window.active_ajax++;
                                            $('div.ui-tooltip').remove();
                                        },
                                        success: function(odata) {
                                            window.active_ajax--;
                
                                            $('#{$this->loader_id}').css({
                                                'width': '4rem',
                                                'display': 'none'
                                            });
                
                                            if (odata.length > 0) {
                                            
                                                var itemsCharged = 0;
                                                
                                                var productsData = odata.reduce((acc, item) => {
                                                    acc[item[searchAttribute]] = item;
                                                    return acc;
                                                }, {});                                                
                                               
                                                for (const key in fileData) {
                                                
                                                    var itemProduct = productsData[key];
                                                    var itemsPk = fileData[key];
                                                         
                                                    Object.values(itemsPk).forEach(itemFile => {
                                                        if(itemProduct){
                                                            {$this->onLoadProduct}
                                                        }
                                                    });
                                                }   
                                                {$this->onLoadedProducts}
                                                alert('Se cargaron ' + itemsCharged + ' de ' + response.data[0].length);
                                            }else {
                                                alert('No se encontró ningún producto.');
                                            }
                                        },
                                        error: function(request, status, error) {
                                            bootbox.alert(us_message(request.responseText, 'error'));
                                            window.active_ajax--;
                                        },
                                        dataType: 'json'
                                    });
                                } else {
                                    alert('El archivo no tiene datos.');
                                }
                            },
                            beforeSend: function(xhr) {
                                window.active_ajax++;
                                $('div.ui-tooltip').remove();
                            },
                            error: function(xhr, status, error) {
                                alert('No se pudo procesar el archivo');
                                $('#{$this->loader_id}').css({
                                    'width': '4rem',
                                    'display': 'none'
                                });
                                window.active_ajax--;
                            }
                        });

                        $('#{$this->input_file_id}').val('');

                    } else {
                        // El archivo no tiene una extensión válida, muestra un mensaje de error o realiza alguna otra acción
                        alert('Por favor selecciona un archivo con extensión .xls o .xlsx');
                    }
                } else {
                    alert('No se seleccionó ningún archivo.');
                }
            });
         });
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<style>";
        echo "@keyframes spin { to { transform: rotate(360deg); } }";
        echo ".spin { transform-origin: center; animation: spin 2s linear infinite}";
        echo "</style>";
        echo "<div class = 'col-lg-{$this->colums} col-md-5 col-sm-12 col-xs-12' style ='display: flex; gap:1rem;'>";
        echo SIANForm::dropDownListNonActive('Criterio de Búsqueda', '', 'barcode', Product::getUploadSearchAttribute(), array(
            "id" => $this->input_search_attribute_id,
            'class' => 'form-control',
        ));
        echo "<div style ='display: flex; flex-direction: column;'>";
        echo CHtml::label('Subir', '', []);
        echo "<label class='btn btn-success'>";
        echo "<input type='file' id='{$this->input_file_id}'  accept='.xls, .xlsx' style='display : none' />";
        echo "<i class='fa fa-file-excel-o' aria-hidden='true'></i>";
        echo "</label>";
        echo "</div>";
        echo "<svg viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg' style='width: 4rem; display:none;' id='{$this->loader_id}'>";
        echo "<circle class='spin' cx='200' cy='200' fill='none' r='104' stroke-width='16' stroke='#E387FF' stroke-dasharray='417.5 700' stroke-linecap='round' />";
        echo "</svg>";
        echo "</div>";
    }

}
