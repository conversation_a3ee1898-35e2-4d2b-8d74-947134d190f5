<?php

class SIANResourceGrid extends CWidget {

    public $id;
    public $form;
    public $model;
    public $attribute;
    public $name;
    public $modal_id;
    public $cms = false;
    public $icon = 'list';
    public $type_id;
    public $url_id;
    public $add_button_id;
    public $remove_button_id;
    public $box = true;
    public $type_items;
    //PRIVATE
    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->type_id = isset($this->type_id) ? $this->type_id : $this->controller->getServerId();
        $this->url_id = isset($this->url_id) ? $this->url_id : $this->controller->getServerId();
        $this->add_button_id = isset($this->add_button_id) ? $this->add_button_id : $this->controller->getServerId();
        $this->remove_button_id = isset($this->remove_button_id) ? $this->remove_button_id : $this->controller->getServerId();
        //
        $a_array = [];

        foreach ($this->model->{$this->attribute} as $o_item) {
            $attributes = array();
            array_push($attributes, "title:'" . addslashes($o_item->title) . "'");
            array_push($attributes, "url:'{$o_item->url}'");
            array_push($attributes, "title_error:'" . addslashes($o_item->getError('title')) . "'");
            array_push($attributes, "url_error:'" . addslashes($o_item->getError('url')) . "'");

            array_push($a_array, '{' . implode(',', $attributes) . "}");
        }

        $a_array = '[' . implode(',', $a_array) . ']';

        SIANAssets::registerScriptFile('js/sian-resource-grid.js');

        Yii::app()->clientScript->registerScript($this->id, "

        var array = {$a_array};
        //DATA
        var tableObj = $('#{$this->id}');
        tableObj.data('count', 0);
        tableObj.data('name', '{$this->name}');
        tableObj.data('type_items', " . CJSON::encode($this->type_items) . ");

        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANResourceGridAddItem('{$this->id}', array[i]['title'], array[i]['url'], array[i]['title_error'], array[i]['url_error']);
            }
        }
        else
        {
            $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
        }
        
        SIANResourceGridUpdate('{$this->id}');
        
        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var title = $('#{$this->type_id}').val();
            var url = $('#{$this->url_id}').val();

            if (url.length === 0)
            {
                bootbox.alert(us_message('Especifique la dirección URL!', 'warning'));
                $('#{$this->url_id}').focus();
                return;
            }     

            SIANResourceGridAddItem('{$this->id}', title, url, '', '');
            SIANResourceGridUpdate('{$this->id}');    
            unfocusable();
            //CLEAR
            $('#{$this->remove_button_id}').click();
            //FOCUS
            $('#{$this->url_id}').focus();
                
        });
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->box) {
            $this->beginWidget('booster.widgets.TbPanel', array(
                'title' => $this->model->getAttributeLabel($this->attribute),
                'headerIcon' => "fa fa-{$this->icon} fa-lg",
                'htmlOptions' => array(
                    'class' => $this->model->hasErrors($this->attribute) ? 'us-error' : ''
                )
            ));
        }

        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo SIANForm::dropDownListNonActive('Tipo', null, null, $this->type_items, array(
            'id' => $this->type_id,
                ), true);
        echo "</div>";
        echo "<div class='col-lg-7 col-md-7 col-sm-7 col-xs-12'>";
        if ($this->cms === false) {
            echo SIANForm::textFieldNonActive('URL', null, null, array(
                'id' => $this->url_id,
                'append' => $this->controller->widget('application.widgets.USLink', array(
                    'id' => $this->remove_button_id,
                    'icon' => 'fa fa-lg fa-times',
                    'title' => 'Limpiar',
                    'onclick' => "$('#{$this->url_id}').val('').focus()"
                        ), true)
            ));
        } else {
            echo $this->widget("application.widgets.USKCFinder", array(
                'id' => $this->url_id,
                'form' => $this->form,
                'name' => null,
                'label' => 'Recursos',
                'parent_id' => $this->modal_id,
                'cms' => $this->cms,
                'remove_button_id' => $this->remove_button_id,
                    ), true);
        }

        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo CHtml::label('Agregar', $this->add_button_id, array(
        ));
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_button_id,
            'context' => 'primary',
            'icon' => 'fa fa-lg fa-plus white',
            'size' => 'default',
            'title' => 'Añadir',
            'block' => true,
        ));
        echo "</div>";
        echo "</div>";

        echo CHtml::label("<h4>{$this->model->getAttributeLabel($this->attribute)}</h4>", $this->id, array(
            'class' => $this->model->hasErrors($this->attribute) && !$this->box ? 'error' : ''
        ));

        echo "<table id='{$this->id}' class='table table-condensed table-hover " . ($this->model->hasErrors($this->attribute) && !$this->box ? 'us-error' : '') . "' data-modal_id='{$this->modal_id}'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th width='5%'>#</th>";
        echo "<th width='15%'>Tipo</th>";
        echo "<th width='60%'>URL</th>";
        echo "<th width='10%'>Orden</th>";
        echo "<th width='10%'>Vista</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody></tbody>";
        echo "<tfoot>";
        echo "</tfoot>";
        echo "</table>";

        if ($this->box) {
            $this->endWidget();
        }
    }

}
