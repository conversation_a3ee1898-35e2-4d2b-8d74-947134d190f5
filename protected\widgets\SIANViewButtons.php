<?php

class SIANViewButtons extends CWidget {

    public $id;
    public $ability;
    public $model;
    public $buttons = [];
    public $controller;
    public $params;
    public $size = 'normal';
    public $print_mode = false;
    public $is_confirmation = false;
    //private
    private $raw_printer_input_id;
    private $print_series_input_id;
    private $print_payments_input_id;
    private $print_mode_id;
    private $print_button_id;
    private $send_mail_button_id;
    private $export_button_id;
    private $download_button_id;
    private $alert = 'No se pudo actualizar el estado de la impresión de este documento de venta, por favor avise a Sistemas';
    private $format = '';

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->params = Yii::app()->params;
        //
        $this->id = $this->controller->getServerId();
        $this->raw_printer_input_id = $this->controller->getServerId();
        $this->print_series_input_id = $this->controller->getServerId();
        $this->print_payments_input_id = $this->controller->getServerId();
        $this->print_mode_id = $this->controller->getServerId();
        $this->print_button_id = $this->controller->getServerId();
        $this->send_mail_button_id = $this->controller->getServerId();
        $this->download_button_id = $this->controller->getServerId();
        //Format
        $this->format = $this->getFormat();
        //SCRIPTS
        Yii::app()->clientScript->registerScript('SIANViewButtons', "

        $('body').on('click', '.sian-view-button', function () {
            var id = $(this).attr('id');
            SIANViewButtonsDeactivateButton(id);
        });
        
        function SIANViewButtonsDeactivateButton(button_id)
        {
            var button = $('#' + button_id);
            button.prop('disabled', true);
            button.remove();
        }

        function SIANViewButtonsUpdatePrintCount()
        {
            $.ajax({
                type: 'post',
                url: '{$this->controller->createUrl('/movement/printed')}',
                data: {
                    movement_id: {$this->model->movement_id},
                },
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },                        
                success: function (data) {
                    if (data.printed === 0)
                    {
                        bootbox.alert(us_message('{$this->alert}', 'error'));
                    }
                    window.active_ajax--;
                },
                error: function (request, status, error) {
                    window.active_ajax--;
                    bootbox.alert(us_message('{$this->alert}', 'error'));
                },
                dataType: 'json'
            });
        }

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $buttons = [];

        if (!$this->is_confirmation) {
            $buttons[] = array(
                'context' => 'default',
                'icon' => 'fa fa-arrow-left fa-lg',
                'label' => 'Regresar',
                'url' => $this->controller->createUrl('index'),
                'size' => $this->size,
            );
        }

        //Renderizamos los botones
        foreach ($this->buttons as $ability => $button) {

            if (isset($button['no_ability'])) {
                $button['id'] = isset($button['id']) ? $button['id'] : $this->controller->getServerId();
                $button['visible'] = (isset($button['visible']) ? $button['visible'] : 1) && $this->controller->checkRoute($button['route']);
                $button['params'] = isset($button['params']) ? $button['params'] : [];
                $button['class'] = isset($button['class']) ? $button['class'] : null;
                $button['data'] = isset($button['data']) ? $button['data'] : [];
            } else {
                $button['id'] = isset($button['id']) ? $button['id'] : $this->controller->getServerId();
                $button['ability'] = isset($button['ability']) ? $button['ability'] : $this->ability;
                $button['visible'] = (isset($button['visible']) ? $button['visible'] : 1) && $button['ability']->{$button['field']} == 1 && $this->controller->checkRoute($button['route']);
                $button['params'] = isset($button['params']) ? $button['params'] : [];
                $button['class'] = isset($button['class']) ? $button['class'] : null;
                $button['data'] = isset($button['data']) ? $button['data'] : [];
            }

            if ($button['field'] == 'is_payable') {
                $button['visible'] = $button['visible'] && $this->model->advancedOption->lock_payment == 0;
            }

            $buttons[] = array(
                'id' => $button['id'],
                'context' => $button['context'],
                'icon' => $button['icon'],
                'label' => $button['label'],
                'url' => $this->controller->createUrl($button['route'], $button['params']),
                'size' => $this->size,
                'class' => $button['class'],
                'data' => $button['data'],
                'htmlOptions' => array(
                    'class' => 'sian-view-button',
                    'confirm' => isset($button['confirm']) ? $button['confirm'] : null,
                ),
                'visible' => $button['visible'],
            );
        }

        //Renderizar botón de cancelación
        if ($this->controller->route != "administration/confirmation/view") {
            if (isset($this->model->ability->confirm_id) && $this->model->ability->confirmation->user_request == Yii::app()->user->person_id) {
                $buttons[] = [
                    'field' => 'has_request',
                    'context' => 'danger',
                    'icon' => 'fa fa-bell-slash fa-lg white',
                    'size' => $this->size,
                    'label' => 'Cancelar Solicitud',
                    'confirm' => "Seguro de cancelar la solicitud para {$this->model->ability->confirmation->getOptionTypeRequestLabel() }?",
                    'url' => $this->controller->createUrl('cancelRequest', ['id' => $this->model->movement_id]),
                ];
            }
        }

        if (!isset($this->ability->confirm_id)) {

            if (isset($this->ability->is_printable) && $this->ability->is_printable) {

                $b_renderButtonPrint = true;

                if (isset($this->model->documentSerie)) {
                    switch ($this->model->documentSerie->printType->print_type_name) {
                        case PrintType::IMPRESION_MATRICIAL:
                        case PrintType::IMPRESION_NORMAL:
                            echo $this->renderHtmlForm();
                            break;
                        case PrintType::IMPRESION_TICKET:
                        case PrintType::IMPRESION_ELECTRONICO:
                            echo $this->renderTicketForm();
                            break;
                        case PrintType::IMPRESION_ELECTRONICO_A4:
                            $b_renderButtonPrint = false;
                            //No requiere impresión más que la del PDF que se descargarán echo $this->renderHtmlForm();
                            break;
                    }
                } else {
                    echo $this->renderHtmlForm();
                }

                if ($b_renderButtonPrint) {
                    //Renderizar botón de impresión
                    $buttons[] = array(
                        'id' => $this->print_button_id,
                        //'target' => $this->model->movement_id . '_print',
                        'context' => 'success',
                        'label' => "<span class='fa fa-lg fa-file white'></span> Imprimir",
                        'url' => Strings::LINK_TEXT,
                        'size' => $this->size,
                        'htmlOptions' => array(
                            'class' => 'sian-view-button',
                        ),
                    );
                }

                //Descargar PDF
                if ($this->model->document->is_electronic == 1 && $this->controller->route !== 'administration/confirmation/view') {

                    if ($this->model->route == 'warehouse/guide') {
                        $o_modelBilling = (new DpAccountingEBillingIndexTab7(DpAccountingEBillingIndexTab7::SCENARIO_ALL))->addEqualCondition('movement_id', $this->model->movement_id)->find();

                        if (in_array($o_modelBilling->state, [SentMovement::STATUS_PENDING, SentMovement::STATUS_ACCEPTED, SentMovement::STATUS_NOT_SENDED])) {
                            $buttons[] = array(
                                'id' => $this->download_button_id,
                                //'target' => $this->model->movement_id,
                                'context' => 'primary',
                                'label' => "<span class='fa fa-lg fa-download white'></span> Descargar CE",
                                //'url' => Strings::LINK_TEXT,
                                'url' => $this->controller->createUrl('getPdf', array('id' => $this->model->movement_id, 'type' => $this->model->type)),
                                'size' => $this->size,
                            );
                        }
                    } else {
                        $buttons[] = array(
                            'id' => $this->download_button_id,
                            //'target' => $this->model->movement_id,
                            'context' => 'primary',
                            'label' => "<span class='fa fa-lg fa-download white'></span> Descargar CE",
                            //'url' => Strings::LINK_TEXT,
                            'url' => $this->controller->createUrl('getPdf', array('id' => $this->model->movement_id, 'type' => $this->model->type)),
                            'size' => $this->size,
                        );
                    }
                }


                if ($this->controller->route !== 'administration/confirmation/view') {

                    $buttons[] = array(
                        'context' => 'default',
                        'icon' => 'fa fa-question-circle fa-lg',
                        'label' => 'Estado',
                        'url' => $this->controller->createUrl("ability"),
                        'size' => $this->size,
                        'class' => 'form',
                        'data' => [
                            'id' => $this->model->movement_id
                        ],
                        'htmlOptions' => array(
                        ),
                        'visible' => true,
                    );
                }


                if ($this->model->ability->is_advancing) {
                    if (in_array($this->controller->route, ['logistic/purchaseBill/view', 'logistic/purchaseOrder/view', 'logistic/prePurchaseOrder/view', 'warehouse/transformationOrder/view', 'accounting/operatingCost/view'])) {
                        $url = $this->controller->createUrl("advanced");
                        $buttons[] = array(
                            'context' => 'default',
                            'icon' => 'fa fa-check-circle fa-lg',
                            'label' => 'Opc. Avanzadas',
                            'url' => $url,
                            'size' => $this->size,
                            'class' => 'form',
                            'data' => [
                                'id' => $this->model->movement_id,
                                'redirect_url' => $this->controller->createUrl('view', ['id' => $this->model->movement_id]),
                            ],
                            'visible' => $this->model->ability->is_advancing
                        );
                    }
                }
            }
        }


        echo "<div id='{$this->id}'>";

        //echo $this->render();

        $this->widget('application.widgets.USButtons', array(
            'buttons' => $buttons
        ));

        if (!isset($this->ability->confirm_id)) {
            if (!USString::isBlank($this->format)) {
                echo $this->renderSend();
                echo $this->renderExport();
            }
        }

        echo "</div>";
    }

    private function renderHtmlForm() {

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        $('body').on('click', '#{$this->print_button_id}', function () {
            
            var baseUrl = '{$this->controller->createUrl('print', ['id' => $this->model->movement_id, 'format' => $this->format])}';

            " . ($this->print_mode ? "
                var print_mode = $('#{$this->print_mode_id}').val();
                baseUrl += '&print_mode=' + print_mode;
            " : '') . "

            var win = window.open(baseUrl, '{$this->model->movement_id}_print');
            win.focus();
            return false;
        });
        
        ", CClientScript::POS_END);

        $html = '';
        //Si es para impresión detallada
        if ($this->print_mode) {
            //FORM
            $html .= "<div class='row'>";
            $html .= "<div class='col-lg-5 col-md-4 col-sm-4 col-xs-1'></div>";
            $html .= "<div class='col-lg-2 col-md-4 col-sm-4 col-xs-10'>";
            $html .= SIANForm::dropDownListNonActive('Tipo de impresión:', null, null, [
                        MovementController::PRINT_MODE_NORMAL => 'Normal',
                        MovementController::PRINT_MODE_GROUPED => 'Agupado',
                            ], [
                        'id' => $this->print_mode_id,
            ]);
            $html .= "</div>";
            $html .= "</div>";
        }

        return $html;
    }

    private function renderTicketForm() {

        $this->widget('application.widgets.USQZ');

        $applet_id = $this->controller->getServerId();
        //SCRIPTS
        Yii::app()->clientScript->registerScript($applet_id, "

        var certUrl = '/" . Yii::app()->params['admin'] . "/files/cert/cert.txt';
        
        USQZConnect(certUrl, '{$this->controller->createUrl('/cert/signMessage')}', '{$this->raw_printer_input_id}');

        function SIANViewButtonsGetTicket(movement_id, raw_printer, print_series, print_payments, print_type)
        {
            var escpos = '';

            $.ajax({
                type: 'post',
                url: '{$this->controller->createUrl('ticket')}',
                async: false,
                data: {
                    movement_id: movement_id,
                    raw_printer: raw_printer,
                    print_series: print_series,
                    print_payments: print_payments,
                    print_type: print_type
                },
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },  
                success: function (data) {
                
                    if (data.code === " . USREST::CODE_SUCCESS . ")
                    {
                        escpos = data.escpos;
                    }
                    else
                    {
                        escpos = false;
                        bootbox.alert(us_message(data.message, 'error'));
                    }
                    window.active_ajax--;
                },
                error: function (request, status, error) {
                    escpos = false;
                    bootbox.alert(us_message(request.responseText, 'error'));
                    window.active_ajax--;
                },
                dataType: 'json'
            });

            return escpos;
        }

        $('body').on('click', '#{$this->print_button_id}', function () {
            
            var movement_id = " . (int) $this->model->movement_id . ";
            var raw_printer = $('#{$this->raw_printer_input_id}').val();
            var print_series = $('#{$this->print_series_input_id}').prop('checked') ? 1 : 0;
            var print_payments = $('#{$this->print_payments_input_id}').prop('checked') ? 1: 0;
            var print_type = '" . $this->model->documentSerie->printType->print_type_name . "';

//            " . (Yii::app()->params['environment'] === YII_ENVIRONMENT_DEVELOPMENT ? "
//            var text = SIANViewButtonsGetTicket(movement_id, raw_printer, print_series, print_payments, print_type);
//            bootbox.alert(us_message(text, 'warning'));
//            console.log(text);
//            return false;
//            " : "") . "
          
            if(raw_printer === null || raw_printer.length === 0)
            {
                bootbox.alert(us_message('Primero seleccione una impresora!', 'warning'));
                return false;
            }

            if (window.qz_ready) {

                var text = SIANViewButtonsGetTicket(movement_id, raw_printer, print_series, print_payments, print_type);

                if(text)
                {
                    //SIANViewButtonsDeactivateButton('{$this->print_button_id}');
                    USQZPrintESCPOS(text, 'SIANViewButtonsUpdatePrintCount');
                }
            }
            else
            {
                bootbox.alert(us_message('El plugin de impresión no cargó correctamente!', 'error'));
            }

            return false;
        });
        ", CClientScript::POS_END);

        //FORM
        $html = "<div class='row'>";
        $html .= "<div class='col-lg-4 col-md-2 col-sm-12 col-xs-12'></div>";
        $html .= "<div class='col-lg-1 col-md-2 col-sm-3 col-xs-6 text-right'>";
        $html .= SIANForm::checkBoxNonActive('Series', null, true, array(
                    'id' => $this->print_series_input_id,
                    'title' => 'Las series solo aparecerán si este movimiento tiene movimientos de almacén'
        ));
        $html .= "</div>";
        $html .= "<div class='col-lg-2 col-md-4 col-sm-6 col-xs-12'>";
        $html .= SIANForm::dropDownListNonActive('Punto de venta', null, null, [], array(
                    'id' => $this->raw_printer_input_id,
        ));
        $html .= "</div>";
        $html .= "<div class='col-lg-1 col-md-2 col-sm-3 col-xs-6 text-left'>";
        $html .= SIANForm::checkBoxNonActive('Pagos', null, $this->model->route === 'commercial/saleBill', array(
                    'id' => $this->print_payments_input_id,
                    'title' => 'Las pagos solo aparecerán si este movimiento tiene movimientos de caja/banco o aplicaciones'
        ));
        $html .= "</div>";
        $html .= "</div>";

        return $html;
    }

    private function renderSend() {

        $a_items = [
            [
                'label' => "<span class='fa fa-lg fa-file-pdf-o black'></span> A socio de negocio",
                'url' => $this->controller->createUrl('sendMail', array('id' => $this->model->movement_id,
                    'selected_option' => Movement::EMAIL_FOR_BUSSINES_PARTNERS)
                ),
            ]
        ];

        if ($this->model->scenario->auto_send_email == 1) {
            $a_items[] = [
                'label' => "<span class='fa fa-lg fa-file-pdf-o black'></span> A usuarios",
                'url' => $this->controller->createUrl('sendMail', array('id' => $this->model->movement_id,
                    'selected_option' => Movement::EMAIL_FOR_USERS)
                )
            ];
        }

        return $this->widget('application.widgets.USButtons', array(
                    'buttons' => [
                        [
                            'id' => $this->send_mail_button_id,
                            'context' => 'info',
                            'label' => "<span class='fa fa-send fa-lg white'></span> Enviar",
                            'size' => $this->size,
                            'visible' => (($this->model->status == 1) && $this->controller->checkRoute('sendMail')),
                            'items' => $a_items
                        ]
                    ]
                        ), true);
    }

    private function renderExport() {

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
            $('body').on('click', '#{$this->id} .sian-view-buttons-export', function () {

                var buttonObj = $(this);
                
                var type = buttonObj.attr('type');
                
                var baseUrl = '{$this->controller->createUrl('export', ['id' => $this->model->movement_id, 'format' => $this->format])}&type=' + type;

                " . ($this->print_mode ? "
                    var print_mode = $('#{$this->print_mode_id}').val();
                    baseUrl += '&print_mode=' + print_mode;
                " : '') . "

                var win = window.location.assign(baseUrl);
                return false;
            });

            ", CClientScript::POS_END);

        return $this->widget('application.widgets.USButtons', array(
                    'buttons' => [
                        [
                            'id' => $this->export_button_id,
                            'label' => "<span class='fa fa-lg fa-download white'></span> Exportar",
                            'context' => 'warning',
                            'size' => $this->size,
                            'items' => [
                                [
                                    'label' => "<span class='fa fa-lg fa-file-pdf-o black'></span> PDF",
                                    'url' => Strings::LINK_TEXT,
                                    'linkOptions' => [
                                        'type' => MovementController::EXPORT_PDF,
                                        'class' => 'sian-view-buttons-export',
                                    ],
                                ],
                                [
                                    'label' => "<span class='fa fa-lg fa-file-excel-o black'></span> Excel",
                                    'url' => Strings::LINK_TEXT,
                                    'linkOptions' => [
                                        'type' => MovementController::EXPORT_EXCEL,
                                        'class' => 'sian-view-buttons-export',
                                    ],
                                ],
                                [
                                    'label' => "<span class='fa fa-lg fa-file-word-o black'></span> Word",
                                    'url' => Strings::LINK_TEXT,
                                    'linkOptions' => [
                                        'type' => MovementController::EXPORT_WORD,
                                        'class' => 'sian-view-buttons-export',
                                    ],
                                ]
                            ]
                        ]
                    ]
                        ), true);
    }

    private function getFormat() {
        if (isset($this->model->documentSerie)) {
            return $this->model->documentSerie->format;
        } else {
            return $this->model->document->format;
        }
    }

}
