<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of USLog
 *
 * <AUTHOR>
 */
class USValidation {

    /**
     * Guarda un log
     * @param mixed $p_o_model Modelo del cual se guardarán sus errores
     * @return string Filepath del log
     */
    public static function save($p_o_model) {

        if ($p_o_model->hasErrors()) {

            $s_log_code = USTime::getDateCode();
            $s_class = get_class($p_o_model);
            $s_pk = is_array($p_o_model->primaryKey) ? implode('-', $p_o_model->primaryKey) : $p_o_model->primaryKey;
            //
            $a_data = [
                'skin' => Yii::app()->params['skin'],
                'server_name' => Util::getServerName(),
                'username' => Yii::app()->user->isGuest ? false : Yii::app()->user->username,
                'log_code' => $s_log_code,
                'date' => USTime::decodeDateCode($s_log_code, 'Y-m-d'),
                'time' => USTime::decodeDateCode($s_log_code, 'H:i:s'),
                'datetime' => USTime::decodeDateCode($s_log_code, 'Y-m-d H:i:s'),
                'class' => $s_class,
                'pk' => $s_pk,
                'errors' => $p_o_model->getRelatedErrors(),
                'request' => Util::getRequest(),
            ];

            if (Yii::app()->params['mongo_enabled']) {
                Yii::app()->mongo->insertOne('validationlog', $a_data);
            }
        }
    }

}
