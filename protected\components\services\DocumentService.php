<?php

class DocumentService {

    /**
     * @var IDocumentRepository
     */
    private $repository;

    public function __construct($repository) {
        $this->repository = $repository;
    }

    public function getDocumentForApi($scenarioId, $internal = null, $serialized = 1, $sunatCode = null, $documentCode = null, $personType = null, $default = null) {

        $document = $this->repository->getFirstDocument($scenarioId, $internal, $serialized, $sunatCode, $documentCode, $personType, $default);

        if (!$document) {
            USREST::sendResponse(USREST::CODE_NOT_FOUND, 'No se encuentra un documento adecuado para proceder.');
            Yii::app()->end();
        }

        return $document;
    }

    public function getDocumentForSystem($scenarioId, $internal = null, $serialized = 1, $sunatCode = null, $documentCode = null, $personType = null, $default = null) {

        $document = $this->repository->getFirstDocument($scenarioId, $internal, $serialized, $sunatCode, $documentCode, $personType, $default);

        if (!$document) {
            throw new Exception(USREST::CODE_NOT_FOUND . ' No se encuentra un documento adecuado para proceder.');
        }
        return $document;
    }

    public function getDefaultDocumentForSystem($scenarioId, $internal = null, $serialized = 1, $sunatCode = null, $documentCode = null, $personType = null, $default = null) {

        $documents = $this->repository->getAllDocuments($scenarioId, $internal, $serialized, $sunatCode, $documentCode, $personType, $default);

        $default = null;
        $first = null;

        foreach ($documents as $i => $doc) {
            if ($i === 0) {
                $first = $doc;
            }
            if (isset($doc['default']) && $doc['default'] == 1) {
                $default = $doc;
            }
        }

        if ($default)
            return $default;
        if ($first)
            return $first;

        throw new Exception(USREST::CODE_NOT_FOUND . ' No se encuentra un documento adecuado para proceder.');
    }

}
