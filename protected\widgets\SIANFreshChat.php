<?php

class SIANFreshChat extends CWidget {

    public $id;
    //PRIVATE
    private $controller;
    private $userObj;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->userObj = Yii::app()->user;
        //Registramos assets
        Yii::app()->clientScript->registerScriptFile('https://wchat.freshchat.com/js/widget.js', CClientScript::POS_END);
        SIANAssets::registerScriptFile('js/sian-fresh-chat.js');

        Yii::app()->clientScript->registerScript($this->id, "

               
        //EVENTOS
        $(document).ready(function()
        {   
            var divObj = $('#{$this->id}');
            divObj.data('restore_id', '{$this->userObj->getState('restore_id')}');
            divObj.data('skin', '" . Yii::app()->params['skin'] . "');
            divObj.data('username', '{$this->userObj->getState('username')}');
            divObj.data('firstname', '{$this->userObj->getState('firstname')}');
            divObj.data('lastname', '{$this->userObj->getState('lastname')}');
            divObj.data('email_address', '{$this->userObj->getState('email_address')}');
            divObj.data('phone_number', '{$this->userObj->getState('phone_number')}');
            divObj.data('update_url', '{$this->controller->createUrl("/site/updateRestoreId")}');
            divObj.data('host_url', '" . Yii::app()->params['domain'] . "');

            SIANFreshChatInit('{$this->id}');
        });
     
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        echo "<div id='{$this->id}'></div>";
    }

}
