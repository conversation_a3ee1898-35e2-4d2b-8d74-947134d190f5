function SIANCashboxBalanceVisible(div_id, visible)
{
    var divObj = $('#' + div_id);
    //Obtnemos
    if (visible)
    {
        divObj.show();
    } else
    {
        divObj.hide();
    }
}

function SIANCashboxBalanceAddItem(div_id, cashbox_balance_id, cashbox_id, cashbox_name, currency, currency_name, balance, errors) //no estas usando cashbox_id 
{
    //DIV
    var divObj = $('#' + div_id);
    var count = parseInt(divObj.data('count'));
    var removable = divObj.data('removable');
    var disabled = divObj.data('disabled');
    var readonly = divObj.data('readonly');
    var name = divObj.data('name');

    if (disabled)
    {
        return;
    }
    //ID
    var id = getLocalId();
    var reference_input_id = getLocalId();
    var reference_button_id = getLocalId();
    var popover_textarea_id = getLocalId();
    var popover_submit_id = getLocalId();
    var popover_cancel_id = getLocalId();

    var balance_id = getLocalId();

    //verificación de saldos
    var html = '<div id=\'' + id + '\' class=\'row sian-cashbox-balance-item\'>';

    html += '<div class=\'col-lg-8 col-md-12 col-sm-12 col-xs-12\'>';
    html += '<div class=\'form-group\'>';
    //Entidad Financiera    
    html += '<input type=\'hidden\' value=\'' + cashbox_balance_id + '\' class=\'sian-cashbox_balance-item-cashbox_balance_id\' name=\'' + name + '[items][' + id + '][cashbox_balance_id]\'>';
    html += '<input readonly =\'readonly\' type=\'text\' placeholder=\'Cta.Entidad financiera\' class=\'form-control sian-cashbox-balance-item-cashbox_name us-cleantext\' name=\'' + name + '[items][' + id + '][cashbox_name]\' value=\'' + (cashbox_name === null ? '' : cashbox_name) + '\' ' + (readonly ? 'readonly' : '') + ' ' + (disabled ? 'disabled' : '') + 'tabindex=\'-1\'>';
    html += '<input type=\'hidden\' value=\'' + cashbox_id + '\' class=\'sian-cashbox_balance-item-cashbox_id\' name=\'' + name + '[items][' + id + '][cashbox_id]\'>';
    html += '</div>';
    html += '</div>';

    //Moneda;
    html += '<div class=\'col-lg-2 col-md-4 col-sm-4 col-xs-12\'>';
    html += '<div class=\'form-group\'>';
    html += '<input readonly =\'readonly\' type=\'text\' placeholder=\'Moneda\' class=\'form-control sian-cashbox-balance-item-currency_name us-cleantext\' name=\'' + name + '[items][' + id + '][currency_name]\' value=\'' + (currency_name === null ? '' : currency_name) + '\' ' + (readonly ? 'readonly' : '') + ' ' + (disabled ? 'disabled' : '') + 'tabindex=\'-1\' style=\'text-align: center;\'>';
    html += '<input type=\'hidden\' value=\'' + currency + '\' class=\'sian-cashbox_balance-item-currency\' name=\'' + name + '[items][' + id + '][currency]\'>';
    html += '</div>';
    html += '</div>';

    //Saldo
    html += '<div class=\'col-lg-2 col-md-4 col-sm-4 col-xs-12\'>';
    html += '<div class=\'form-group\'>';
    html += '<div class=\'input-group\'>';
    html += '<input id=\'' + balance_id + '\' type=\'number\' class=\'form-control sian-cashbox-balance-item-balance us-cleantext\'  name=\'' + name + '[items][' + id + '][balance]\' type=\'text\' placeholder=\'Saldo\' value=\'' + (balance === null ? '0.00' : balance) + '\' ' + (readonly ? 'readonly' : '') + ' ' + (disabled ? 'disabled' : '') + 'style=\'text-align: right;\'>';
    html += '<span class=\'input-group-addon\'>';

    if (readonly == false)
    {
        if (removable)
        {
            html += ' <a onclick=\'SIANCashboxBalanceRemoveItem(\"' + div_id + '\",\"' + id + '\", false)\' title=\'Eliminar saldo\'><span class=\'fa fa-times fa-lg black\'></span></a>';
        }
    }
    html += '</span>';
    html += '</div>';
    html += '</div>';
    html += '</div>';// Cierra div.col-lg-2   

    html += '</div>'; // Cierra div.sian-cashbox-balance-item

    //COUNT DE ITEMS
    if (count === 0)
    {
        divObj.find('div.sian-cashbox-balance-items').html(html);
    } else
    {
        divObj.find('div.sian-cashbox-balance-items').append(html);
    }
    divObj.data('count', count + 1);

    var popover = '';
    popover += '<div class=\'row\'>';
    popover += '<div class=\'col-lg-12 col-md-12 col-sm-12 col-xs-12\'>';
    popover += '<textarea id=\'' + popover_textarea_id + '\' maxlength=50 rows=3></textarea>';
    popover += '</div>';
    popover += '</div>';
    popover += '<div class=\'row\'>';
    popover += '<div class=\'col-lg-6 col-md-6 col-sm-6 col-xs-6\'>';
    popover += '<button id=\'' + popover_submit_id + '\' type=\'button\' class=\'btn btn-primary btn-block\'><span class=\'fa fa-lg fa-check white\'></span></button>';
    popover += '</div>';
    popover += '<div class=\'col-lg-6 col-md-6 col-sm-6 col-xs-6\'>';
    popover += '<button id=\'' + popover_cancel_id + '\' type=\'button\' class=\'btn btn-default btn-block\'><span class=\'fa fa-lg fa-times black\'></span></button>';
    popover += '</div>';
    popover += '</div>';

    //JQUERY
    $('#' + reference_button_id).popover({
        placement: 'top',
        title: 'Especificar referencia',
        html: true,
        content: popover
    }).on('shown.bs.popover', function () {

        var currentDiv = $('#' + div_id);
        var current_disabled = currentDiv.data('disabled');

        if (current_disabled)
        {
            $('#' + reference_button_id).popover('hide');
        } else
        {
            var reference = $('#' + reference_input_id).val();
            $('#' + popover_textarea_id).val(reference).focus();
        }
    }).on('click', function () {
        $('#' + popover_submit_id).click(function () {
            var reference = $('#' + popover_textarea_id).val();
            $('#' + reference_input_id).val(reference);
            $('#' + reference_button_id).click();
        });
        $('#' + popover_cancel_id).click(function () {
            $('#' + reference_button_id).click();
        });
    });
}

function SIANCashboxBalanceRemoveItem(div_id, id, confirmation)
{
    if (confirmation ? confirm('¿Está seguro de eliminar este ítem?') : true)
    {
        var divObj = $('#' + div_id);
        var count = parseInt(divObj.data('count'));
        var zero_warning = divObj.data('zero_warning');

        $('#' + id).remove();

        if (count === 1)
        {
            divObj.find('div.sian-address-items').html(STRINGS_NO_DATA);
            //Si zero_warning está definido
            if (zero_warning.length > 0)
            {
                $.notify(zero_warning, 'error');
            }
        }

        //COUNT DE ITEMS
        divObj.data('count', count - 1);
    }
}