<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of USCockies
 *
 * <AUTHOR>
 */
class SIANCookies {

    public static function getCookie($name) {
        if (isset(Yii::app()->request->cookies[$name])) {
            return Yii::app()->request->cookies[$name]->value;
        }
        return null;
    }

    public static function setCookie($name, $value) {
        Yii::app()->request->cookies[$name] = new CHttpCookie($name, $value);
    }

    public static function getPageSize($grid_id) {
        $cookie = self::getCookie($grid_id);
        return isset($cookie) ? $cookie : 10;
    }

    public static function getPagination($grid_id) {
        $pagination = array(
            'pageSize' => 10
        );
        if (isset(Yii::app()->request->cookies[$grid_id])) {

            $pageSize = Yii::app()->request->cookies[$grid_id]->value;
            $pagination = ($pageSize == false ) ? false : array(
                'pageSize' => $pageSize
            );
        }
        return $pagination;
    }

}
