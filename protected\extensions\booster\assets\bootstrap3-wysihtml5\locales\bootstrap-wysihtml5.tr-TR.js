/**
 * Turkish translation for bootstrap-wysihtml5
 */
(function($){
    $.fn.wysihtml5.locale["tr-TR"] = {
        font_styles: {
            normal: "Normal",
            h1: "Başlık 1",
            h2: "Başlık 2",
            h3: "Başlık 3",
            h4: "Başlık 4",
            h5: "Başlık 5",
            h6: "Başlık 6"
        },
        emphasis: {
            bold: "Kalın",
            italic: "<PERSON><PERSON><PERSON>",
            underline: "Altı Çizili"
        },
        lists: {
            unordered: "Sırasız Liste",
            ordered: "Sıralı Liste",
            outdent: "Girintiyi Azalt",
            indent: "Girintiyi Arttır"
        },
        link: {
            insert: "<PERSON><PERSON>",
            cancel: "Vazgeç"
        },
        image: {
            insert: "<PERSON><PERSON>",
            cancel: "Vazgeç"
        },
        html: {
            edit: "HTML Göster"
        },
        colours: {
            black: "Siyah",
            silver: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            gray: "<PERSON><PERSON>",
            maroon: "<PERSON><PERSON><PERSON><PERSON>ü<PERSON>",
            red: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
            purple: "<PERSON><PERSON><PERSON>",
            green: "<PERSON><PERSON><PERSON>",
            olive: "<PERSON><PERSON><PERSON>",
            navy: "<PERSON><PERSON><PERSON>",
            blue: "<PERSON><PERSON>",
            orange: "<PERSON>run<PERSON>"
        }
    };
}(j<PERSON>uery));
