<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANUpload
 *
 * <AUTHOR>
 */
class SIANUpload {

    /**
     * Obtiene la carpeta de cargas para la empresa
     * @param string $p_s_cms Tipo de contenido
     * @return string Path de cargas
     */
    public static function getUploadDir($p_s_cms) {
        $s_upload_dir = Yii::app()->params['upload_dir'] . DIRECTORY_SEPARATOR . $p_s_cms;

        if (!is_dir($s_upload_dir)) {
            mkdir($s_upload_dir, 0755);
        }

        return $s_upload_dir;
    }

    /**
     * Obtiene la dirección URL de subidas
     * @param string $p_s_cms Tipo de contenido
     * @return type
     */
    public static function getUploadUrl($p_s_cms) {
        return Yii::app()->params['upload_url'] . '/' . $p_s_cms;
    }

}
