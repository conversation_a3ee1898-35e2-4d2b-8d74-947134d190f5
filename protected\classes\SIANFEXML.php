<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANFEXML
 *
 * <AUTHOR>
 */
class SIANFEXML {

    /**
     * http://orientacion.sunat.gob.pe/index.php/empresas-menu/comprobantes-de-pago-empresas/comprobantes-de-pago-electronicos-empresas/see-operador-de-servicios-electronicos/6949-10-manuales-y-guias
     * http://orientacion.sunat.gob.pe/index.php/empresas-menu/comprobantes-de-pago-empresas/comprobantes-de-pago-electronicos-empresas/see-operador-de-servicios-electronicos/7065-guias-y-manuales
     * http://www.sunat.gob.pe/legislacion/superin/2017/anexoVII-117-2017.pdf
     * 
     * @param type $p_i_movement_id
     * @return type
     */
    public static function getInvoiceHeader($p_i_movement_id) {
        $o_header = Yii::app()->db->createCommand()
                ->select([
                    'M.movement_id',
                    'D.sunat_code',
                    'O.operation_name',
                    'M.emission_date',
                    'M.register_date',
                    'M.expiration_date',
                    'M.exchange_rate',
                    'M.aux_person_id',
                    'M.observation',
                    "XP.identification_type",
                    "XP.identification_number",
                    "REPLACE(XP.person_name, ',', ' ') AS aux_person_name",
                    "UPPER(IFNULL(XA.address, '-')) as aux_person_address",
                    "IFNULL(XG.dist_name, '-') as aux_person_dist",
                    "IFNULL(XG.prov_name, '-') as aux_person_prov",
                    "IFNULL(XG.dept_name, '-') as aux_person_dept",
                    "XE.email_address as aux_person_email",
                    'M.document_serie',
                    'RIGHT(M.document_correlative, 8) AS document_correlative',
                    'UPPER(M.currency) AS currency',
                    'MP.document_serie AS parent_document_serie',
                    'RIGHT(MP.document_correlative, 8) AS parent_document_correlative',
                    'MP.route AS parent_route',
                    'DP.sunat_code AS parent_sunat_code',
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.crude_pen, CM.crude_usd), 2) AS crude",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.affected_pen, CM.affected_usd), 2) AS affected",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.inaffected_pen, CM.inaffected_usd), 2) AS inaffected",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.nobill_pen, CM.nobill_usd), 2) AS nobill",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.export_pen, CM.export_usd), 2) AS export",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.free_pen, CM.free_usd), 2) AS free",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.net_pen, CM.net_usd), 2) AS net",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.nnet_pen, CM.nnet_usd), 2) AS nnet",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.igv_pen, CM.igv_usd), 2) AS igv",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.total_pen, CM.total_usd), 2) AS total",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.perception_pen, CM.perception_usd), 2) AS perception",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.real_pen, CM.real_usd), 2) AS `real`",
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.no_ret_pen, CM.no_ret_usd), 2) AS `no_ret`",
                    "IF(CM.`condition` = '" . Condition::CREDIT . "', '" . SIANFE::PAYMENT_TERM_CREDIT . "', '" . SIANFE::PAYMENT_TERM_COUNTED . "') AS payment_term",
                    "IF(CM.`condition` = '" . Condition::CREDIT . "', ROUND(IF(M.currency = '" . Currency::PEN . "', CM.real_pen, CM.real_usd), 2), 0) AS `balance`",
                    '(GV.igv * 100) AS igv_percent',
                    'CM.detraction',
                    'MU.value AS detraction_code',
                    '(CM.detraction_percent * 100) AS detraction_percent',
                    "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.det_pen, CM.det_usd), 2) AS detraction_amount",
                    'C.description AS detraction_account',
                    'CM.free_transfer',
                    'CH.reason_code',
                    'CH.reason',
                    'CM.item_count',
                    //Dirección del vendedor
                    'AXP.address AS address_seller',
                    'AXP.reference AS reference_seller',
                    'GXP.dept_code AS dept_code_seller',
                    'GXP.prov_code AS prov_code_seller',
                    'GXP.dist_code AS dist_code_seller',
                    'GXP.dept_name AS dept_name_seller',
                    'GXP.prov_name AS prov_name_seller',
                    'GXP.dist_name AS dist_name_seller',
                    //Dirección del movimiento
                    'AOP.address',
                    'AOP.reference',
                    'GOP.dept_code',
                    'GOP.prov_code',
                    'GOP.dist_code',
                    'GOP.dept_name',
                    'GOP.prov_name',
                    'GOP.dist_name'
                ])
                ->from('movement M')
                ->join('operation O', "O.operation_code = M.operation_code")
                ->join('document D', "D.document_code = M.document_code")
                ->join('commercial_movement CM', 'CM.movement_id = M.movement_id')
                ->join('person XP', 'XP.person_id = M.aux_person_id')
                ->join('changelog CH', "CH.owner_id = M.movement_id AND CH.owner = '" . CommercialMovement::OWNER . "' AND CH.action = '" . Changelog::CREATE . "'")
                ->join('global_var GV', "GV.organization_id = 1")
                //Desde (domicilio fiscal del proveedor)
                ->leftJoin('address XA', "XA.`owner` = '" . Person::OWNER . "' AND XA.owner_id = XP.person_id AND XA.order = 1")
                ->leftJoin('geoloc XG', 'XG.dept_code = XA.dept_code AND XG.prov_code = XA.prov_code AND XG.dist_code = XA.dist_code')
                ->leftJoin('email XE', "XE.`owner` = '" . Person::OWNER . "' AND XE.owner_id = XP.person_id AND XE.order = 1")
                ->leftJoin('movement_link ML', "M.movement_id = ML.movement_id AND ML.parent_route = 'commercial/saleBill'")
                ->leftJoin('movement MP', 'MP.movement_id = ML.movement_parent_id')
                ->leftJoin('document DP', 'DP.document_code = MP.document_code')
                ->leftJoin('multitable MU', 'MU.multi_id = CM.detraction_code')
                ->leftJoin('cashbox C', 'C.is_detraction = 1')
                ->leftJoin('address AOP', "AOP.owner = '" . Movement::OWNER . "' AND AOP.owner_id = M.movement_id AND AOP.order = 1")
                ->leftJoin('geoloc GOP', "GOP.dept_code = AOP.dept_code AND GOP.prov_code = AOP.prov_code AND GOP.dist_code = AOP.dist_code")
                ->leftJoin('address AXP', "AXP.owner = '" . Person::OWNER . "' AND AXP.owner_id = M.aux_person_id AND AXP.order = 1")
                ->leftJoin('geoloc GXP', "GXP.dept_code = AXP.dept_code AND GXP.prov_code = AXP.prov_code AND GXP.dist_code = AXP.dist_code")
                ->where('M.`status`')
                ->andWhere('M.movement_id = :id', [':id' => $p_i_movement_id])
                ->queryRow();

        $o_header['no_data'] = $o_header['sunat_code'] == Document::CREDIT_NOTE &&
                isset($o_header['reason_code']) && $o_header['reason_code'] &&
                $o_header['reason_code'] == Multitable::CATALOG_9_CHANGE_EXPIRATION_DATE;

        return $o_header;
    }

    public static function getInvoiceDetail($p_a_header) {

        if ($p_a_header['item_count'] > 0) {

            $a_details = Yii::app()->db->createCommand()
                    ->select([
                        'I.item_number',
                        "I.product_id",
                        "I.product_name",
                        'I.product_type',
                        'I.pres_quantity',
                        'UPPER(M.currency) AS currency',
                        "CMP.vprice_{$p_a_header['currency']} AS vprice",
                        "CMP.sprice_{$p_a_header['currency']} AS sprice",
                        "CMP.rprice_{$p_a_header['currency']} AS rprice",
                        "ROUND(CMP.affected_{$p_a_header['currency']}, 2) AS affected",
                        "ROUND(CMP.inaffected_{$p_a_header['currency']}, 2) AS inaffected",
                        "ROUND(CMP.nobill_{$p_a_header['currency']}, 2) AS nobill",
                        "ROUND(CMP.export_{$p_a_header['currency']}, 2) AS export",
                        "ROUND(CMP.free_{$p_a_header['currency']}, 2) AS free",
                        "ROUND(CMP.nnet_{$p_a_header['currency']}, 2) AS nnet",
                        "ROUND(CMP.net_{$p_a_header['currency']}, 2) AS net",
                        "ROUND(CMP.igv_{$p_a_header['currency']}, 2) AS igv",
                        "ROUND(CMP.total_{$p_a_header['currency']}, 2) AS total",
                        "ROUND(CMP.perception_{$p_a_header['currency']}, 2) AS perception",
                        "ROUND(CMP.real_{$p_a_header['currency']}, 2) AS `real`",
                        "IFNULL(MT.value, '" . Presentation::DEFAULT_SERVICE_CODE . "') AS sunat_code",
                        '(GV.igv * 100) AS igv_percent',
                        'CMP.igv_affection',
                    ])
                    ->from('item I')
                    //Al parecer SUNAT si está aceptando ítems aun que no sea el 18% exacto, así que se puede enviar agrupado
                    ->join('commercial_movement_product CMP', 'CMP.item_id = I.item_id AND CMP.group_id IS NULL')//AGRUPADO
                    //Pero si diera error descomentar esto para enviar desagrupado 
                    //->join('commercial_movement_product CMP', 'CMP.item_id = I.item_id')//DESAGRUPADO
                    ->join('commercial_movement CM', 'CM.movement_id = CMP.movement_id')
                    ->join('movement M', 'M.movement_id = CMP.movement_id')
                    ->leftJoin('presentation PR', 'PR.product_id = I.product_id AND PR.equivalence = I.equivalence')
                    ->leftJoin('multitable MT', 'MT.multi_id = PR.measure_id')
                    ->join('global_var GV', 'GV.organization_id = 1')
                    ->where('I.movement_id = :movement_id', [
                        ':movement_id' => $p_a_header['movement_id']
                    ])
                    //Pero si diera error descomentar esto para enviar desagrupado 
                    //->andWhere("I.product_type IN ('" . Product::TYPE_MERCHANDISE . "', '" . Product::TYPE_SERVICE . "')")//DESAGRUPADO
                    ->queryAll();
        } else {
            $a_details = [
                [
                    'item_number' => 1,
                    'product_id' => 0,
                    'product_name' => $p_a_header['operation_name'],
                    'product_type' => Product::TYPE_GROUP,
                    'pres_quantity' => 1,
                    'currency' => $p_a_header['currency'],
                    'vprice' => $p_a_header['net'],
                    'sprice' => $p_a_header['total'],
                    'rprice' => $p_a_header['nnet'],
                    'affected' => $p_a_header['affected'],
                    'inaffected' => $p_a_header['inaffected'],
                    'nobill' => $p_a_header['nobill'],
                    'export' => $p_a_header['export'],
                    'free' => $p_a_header['free'],
                    'net' => $p_a_header['net'],
                    'igv' => $p_a_header['igv'],
                    'total' => $p_a_header['total'],
                    'perception' => $p_a_header['perception'],
                    'real' => $p_a_header['real'],
                    'sunat_code' => Presentation::DEFAULT_SERVICE_CODE,
                    'igv_percent' => Yii::app()->controller->getOrganization()->globalVar->igv * 100,
                    'igv_affection' => $p_a_header['inaffected'] > 0 ? CommercialMovementProduct::IGV_AFFECTION_AFFECTED : CommercialMovementProduct::IGV_AFFECTION_INAFFECTED
                ]
            ];
        }

        for ($i = 0; $i < count($a_details); $i++) {
            $a_details[$i]['no_data'] = $p_a_header['no_data'];
        }
        return $a_details;
    }

    /**
     * http://orientacion.sunat.gob.pe/index.php/empresas-menu/comprobantes-de-pago-empresas/comprobantes-de-pago-electronicos-empresas/see-operador-de-servicios-electronicos/6949-10-manuales-y-guias
     * http://orientacion.sunat.gob.pe/index.php/empresas-menu/comprobantes-de-pago-empresas/comprobantes-de-pago-electronicos-empresas/see-operador-de-servicios-electronicos/7065-guias-y-manuales
     * http://www.sunat.gob.pe/legislacion/superin/2017/anexoVII-117-2017.pdf
     * 
     * @param type $p_i_movement_id
     * @return type
     */
    private static function getGuideHeader($p_i_movement_id, $p_s_type) {

        switch ($p_s_type) {
//            case Scenario::TYPE_WAREHOUSE:
//                return Yii::app()->db->createCommand()
//                                ->select([
//                                    'M.movement_id',
//                                    'M.type',
//                                    'D.sunat_code',
//                                    'O.operation_name',
//                                    'M.emission_date',
//                                    'M.register_date',
//                                    'M.aux_person_id',
//                                    "XP.identification_type",
//                                    "XP.identification_number",
//                                    "REPLACE(XP.person_name, ',', ' ') AS aux_person_name",
//                                    'M.document_serie',
//                                    'RIGHT(M.document_correlative, 8) AS document_correlative',
//                                    'MU.value AS reason_transfer_code',
//                                    'MU.description AS reason_transfer_name',
//                                    'CH.reason_code',
//                                    'CH.reason',
//                                    "TXP.identification_type AS transport_company_type",
//                                    "TXP.identification_number AS transport_company_number",
//                                    "REPLACE(TXP.person_name, ',', ' ') AS transport_company_name",
//                                    'WM.transfer_start_date',
//                                    'WM.delivery_carrier_date',
//                                    'WM.total_weight',
//                                    'WM.number_packages',
//                                    'WM.transport_data',
//                                    'WM.munit_quantity',
//                                    //Dirección de origen
//                                    "CONCAT(OA.address, ' ', COALESCE(OA.reference, '')) AS origin_address",
//                                    "CONCAT(UOA.dept_code, UOA.prov_code, UOA.dist_code) AS origin_address_ubigeo",
//                                    'GOA.dept_name AS origin_address_dept_name',
//                                    'GOA.prov_name AS origin_address_prov_name',
//                                    'GOA.dist_name AS origin_address_dist_name',
//                                    //Dirección de destino
//                                    "CONCAT(DA.address, ' ', COALESCE(DA.reference, '')) AS delivery_address",
//                                    "CONCAT(UDA.dept_code, UDA.prov_code, UDA.dist_code) AS delivery_address_ubigeo",
//                                    'GDA.dept_name AS delivery_address_dept_name',
//                                    'GDA.prov_name AS delivery_address_prov_name',
//                                    'GDA.dist_name AS delivery_address_dist_name'
//                                ])
//                                ->from('movement M')
//                                ->join('operation O', "O.operation_code = M.operation_code")
//                                ->join('document D', "D.document_code = M.document_code")
//                                ->join('warehouse_movement WM', 'WM.movement_id = M.movement_id')
//                                ->join('person XP', 'XP.person_id = M.aux_person_id')
//                                ->leftJoin('person TXP', 'TXP.person_id = WM.transport_company_id')
//                                ->join('changelog CH', "CH.owner_id = M.movement_id AND CH.owner = '" . WarehouseMovement::OWNER . "' AND CH.action = '" . Changelog::CREATE . "'")
//                                ->leftJoin('multitable MU', 'MU.multi_id = WM.reason_transfer_id')
//                                ->leftJoin('address OA', "OA.owner = '" . Movement::OWNER . "' AND OA.owner_id = M.movement_id AND OA.order = 0")
//                                ->leftJoin('geoloc GOA', "GOA.dept_code = OA.dept_code AND GOA.prov_code = OA.prov_code AND GOA.dist_code = OA.dist_code")
//                                ->leftJoin('ubigeo UOA', "UOA.dept_name = GOA.dept_name AND UOA.prov_name = GOA.prov_name AND UOA.dist_name = GOA.dist_name")
//                                ->leftJoin('address DA', "DA.owner = '" . Movement::OWNER . "' AND DA.owner_id = M.movement_id AND DA.order = 1")
//                                ->leftJoin('geoloc GDA', "GDA.dept_code = DA.dept_code AND GDA.prov_code = DA.prov_code AND GDA.dist_code = DA.dist_code")
//                                ->leftJoin('ubigeo UDA', "UDA.dept_name = GDA.dept_name AND UDA.prov_name = GDA.prov_name AND UDA.dist_name = GDA.dist_name")
//                                ->where('M.`status`')
//                                ->andWhere('M.movement_id = :id', [':id' => $p_i_movement_id])
//                                ->queryRow();

            case Scenario::TYPE_PRODUCT_LIST:
                return Yii::app()->db->createCommand()
                                ->select([
                                    'M.movement_id',
                                    'M.type',
                                    'D.sunat_code',
                                    'O.operation_name',
                                    'M.emission_date',
                                    'M.register_date',
                                    'M.aux_person_id',
                                    'M.observation',
                                    "XP.identification_type",
                                    "XP.identification_number",
                                    "REPLACE(XP.person_name, ',', ' ') AS aux_person_name",
                                    "RP.identification_type AS buyer_identification_type",
                                    "RP.identification_number AS buyer_identification_number",
                                    "REPLACE(RP.person_name, ',', ' ') AS buyer_name",
                                    'M.document_serie',
                                    'RIGHT(M.document_correlative, 8) AS document_correlative',
                                    'MU.value AS reason_transfer_code',
                                    'MU.description AS reason_transfer_name',
                                    'CH.reason_code',
                                    'CH.reason',
                                    "TXP.identification_type AS transport_company_type",
                                    "TXP.identification_number AS transport_company_number",
                                    "REPLACE(TXP.person_name, ',', ' ') AS transport_company_name",
                                    'WM.transfer_start_date',
                                    'WM.delivery_carrier_date',
                                    'WM.total_weight',
                                    'WM.number_packages',
                                    'WM.transport_data',
                                    'WM.munit_quantity',
                                    //Documento origen
                                    "IF(MPP.movement_id IS NOT NULL, DPP.sunat_code, IF(MP.movement_id IS NOT NULL, DP.sunat_code, NULL)) AS parent_reference_sunat_code",
                                    "IF(MPP.movement_id IS NOT NULL, IF(DPP.sunat_code = '" . Document::FACTURA . "', 'Factura', 'Boleta'), IF(MP.movement_id IS NOT NULL, IF(DP.sunat_code = '" . Document::FACTURA . "', 'Factura', 'Boleta'), NULL)) AS parent_reference_type",
                                    "IF(MPP.movement_id IS NOT NULL, CONCAT(MPP.document_serie, '-', CAST(MPP.document_correlative AS INT)), IF(MP.movement_id IS NOT NULL, CONCAT(MP.document_serie, '-', CAST(MP.document_correlative AS INT)), NULL)) AS parent_reference_document",
                                    //Dirección de origen
                                    "CONCAT(OA.address, ' ', COALESCE(OA.reference, '')) AS origin_address",
                                    "CONCAT(UOA.dept_code, UOA.prov_code, UOA.dist_code) AS origin_address_ubigeo",
                                    'GOA.dept_name AS origin_address_dept_name',
                                    'GOA.prov_name AS origin_address_prov_name',
                                    'GOA.dist_name AS origin_address_dist_name',
                                    //Dirección de destino
                                    "CONCAT(DA.address, ' ', COALESCE(DA.reference, '')) AS delivery_address",
                                    "CONCAT(UDA.dept_code, UDA.prov_code, UDA.dist_code) AS delivery_address_ubigeo",
                                    'GDA.dept_name AS delivery_address_dept_name',
                                    'GDA.prov_name AS delivery_address_prov_name',
                                    'GDA.dist_name AS delivery_address_dist_name'
                                ])
                                ->from('movement M')
                                ->join('operation O', "O.operation_code = M.operation_code")
                                ->join('document D', "D.document_code = M.document_code")
                                ->join('product_list WM', "WM.owner_id = M.movement_id AND WM.owner = '" . Movement::OWNER . "'")
                                ->join('person XP', 'XP.person_id = M.aux_person_id')
                                ->join('changelog CH', "CH.owner_id = M.movement_id AND CH.owner = '" . ProductListMovement::OWNER . "' AND CH.action = '" . Changelog::CREATE . "'")
                                ->leftJoin('person RP', 'RP.person_id = WM.recipient_id')
                                ->leftJoin('person TXP', 'TXP.person_id = WM.transport_company_id')
                                ->leftJoin('multitable MU', 'MU.multi_id = WM.reason_transfer_id')
                                //Padre - SaleBill
                                ->leftJoin('movement_link ML', "ML.movement_id = M.movement_id AND ML.parent_route IN ('commercial/saleBill', 'warehouse/saleOut')")
                                ->leftJoin('movement MP', 'MP.movement_id = ML.movement_parent_id')
                                ->leftJoin('document DP', 'DP.document_code = MP.document_code')
                                //Padre - SaleBill
                                ->leftJoin('movement_link ML2', "ML2.movement_id = MP.movement_id AND ML2.parent_route IN ('commercial/saleBill')")
                                ->leftJoin('movement MPP', 'MPP.movement_id = ML2.movement_parent_id')
                                ->leftJoin('document DPP', 'DPP.document_code = MPP.document_code')
                                //
                                ->leftJoin('address OA', "OA.owner = '" . Movement::OWNER . "' AND OA.owner_id = M.movement_id AND OA.order = 0")
                                ->leftJoin('geoloc GOA', "GOA.dept_code = OA.dept_code AND GOA.prov_code = OA.prov_code AND GOA.dist_code = OA.dist_code")
                                ->leftJoin('ubigeo UOA', "UOA.dept_name = GOA.dept_name AND UOA.prov_name = GOA.prov_name AND UOA.dist_name = GOA.dist_name")
                                ->leftJoin('address DA', "DA.owner = '" . Movement::OWNER . "' AND DA.owner_id = M.movement_id AND DA.order = 1")
                                ->leftJoin('geoloc GDA', "GDA.dept_code = DA.dept_code AND GDA.prov_code = DA.prov_code AND GDA.dist_code = DA.dist_code")
                                ->leftJoin('ubigeo UDA', "UDA.dept_name = GDA.dept_name AND UDA.prov_name = GDA.prov_name AND UDA.dist_name = GDA.dist_name")
                                ->where('M.`status`')
                                ->andWhere('M.movement_id = :id', [':id' => $p_i_movement_id])
                                ->queryRow();
        }
    }

    private static function getGuideDetail($p_a_header, $p_s_type) {

        if ($p_a_header['munit_quantity'] > 0) {

            switch ($p_s_type) {

                case Scenario::TYPE_WAREHOUSE:
                    return Yii::app()->db->createCommand()
                                    ->select([
                                        'I.item_number',
                                        "I.product_id",
                                        "I.product_name",
                                        "I.description",
                                        'I.product_type',
                                        'I.pres_quantity',
                                        "IFNULL(MT.value, '" . Presentation::DEFAULT_SERVICE_CODE . "') AS sunat_code"
                                    ])
                                    ->from('item I')
                                    ->join('warehouse_merchandise WMP', 'WMP.item_id = I.item_id')
                                    ->join('warehouse_movement WM', 'WM.movement_id = WMP.movement_id')
                                    ->join('movement M', 'M.movement_id = WMP.movement_id')
                                    ->leftJoin('presentation PR', 'PR.product_id = I.product_id AND PR.equivalence = I.equivalence')
                                    ->leftJoin('multitable MT', 'MT.multi_id = PR.measure_id')
                                    ->where('I.movement_id = :movement_id', [
                                        ':movement_id' => $p_a_header['movement_id']
                                    ])
                                    ->queryAll();

                case Scenario::TYPE_PRODUCT_LIST:
                    return Yii::app()->db->createCommand()
                                    ->select([
                                        'I.item_number',
                                        "I.product_id",
                                        "I.product_name",
                                        "I.description",
                                        'I.product_type',
                                        'I.pres_quantity',
                                        "IFNULL(MT.value, '" . Presentation::DEFAULT_SERVICE_CODE . "') AS sunat_code"
                                    ])
                                    ->from('item I')
                                    ->join('product_list_item PLI', 'PLI.item_id = I.item_id')
                                    ->join('product_list PL', "PL.owner_id = PLI.owner_id AND PL.owner = PLI.owner AND PL.owner = '" . Movement::OWNER . "'")
                                    ->join('movement M', 'M.movement_id = PL.owner_id')
                                    ->leftJoin('presentation PR', 'PR.product_id = I.product_id AND PR.equivalence = I.equivalence')
                                    ->leftJoin('multitable MT', 'MT.multi_id = PR.measure_id')
                                    ->where('I.movement_id = :movement_id', [
                                        ':movement_id' => $p_a_header['movement_id']
                                    ])
                                    ->queryAll();
            }
        } else {
            return [
                [
                    'item_number' => 1,
                    'product_id' => 0,
                    'product_name' => $p_a_header['operation_name'],
                    'product_type' => Product::TYPE_GROUP,
                    'pres_quantity' => 1,
                    'sunat_code' => Presentation::DEFAULT_SERVICE_CODE,
                ]
            ];
        }
    }

    private static function getGuideShippers($p_i_movement_id, $p_s_type) {

        if ($p_s_type == Scenario::TYPE_WAREHOUSE) {

            $o_model = WarehouseMovement::model()->with([
                        'shipperParent' => [
                            'joinType' => 'join',
                            'with' => [
                                'children' => [
                                    'joinType' => 'join',
                                    'with' => [
                                        'shipper' => [
                                            'joinType' => 'join',
                                            'with' => [
                                                'person'
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ])->findByPK($p_i_movement_id);
        } else {
            $o_model = ProductListMovement::model()->with([
                        'shipperParent' => [
                            'joinType' => 'join',
                            'with' => [
                                'children' => [
                                    'joinType' => 'join',
                                    'with' => [
                                        'shipper' => [
                                            'joinType' => 'join',
                                            'with' => [
                                                'person'
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ])->findByPK(['owner_id' => $p_i_movement_id, 'owner' => Movement::OWNER]);
        }

        $a_shippers = [];

        if (isset($o_model)) {
            $a_shippers = $o_model->shipperParent->children;
        }
        return $a_shippers;
    }

    private static function getGuideTransportUnits($p_i_movement_id, $p_s_type) {
        if ($p_s_type == Scenario::TYPE_WAREHOUSE) {
            return Yii::app()->db->createCommand()
                            ->select([
                                'TU.plate',
                            ])
                            ->from('warehouse_movement WM')
                            ->join('owner_pair OP1', "OP1.owner_id = WM.movement_id AND OP1.type = '" . OwnerPair:: TYPE_MOVEMENT_TRANSPORT_UNIT . "' AND OP1.owner = '" . Movement::OWNER . "'")
                            ->join('owner_pair OP2', "OP2.parent_id = OP1.pair_id AND OP2.type = OP1.type AND OP2.owner = '" . TransportUnit::OWNER . "'")
                            ->join('transport_unit TU', "TU.transport_unit_id = OP2.owner_id")
                            ->andWhere('WM.movement_id = :id', [':id' => $p_i_movement_id])
                            ->queryAll();
        } else {
            return Yii::app()->db->createCommand()
                            ->select([
                                'TU.plate',
                            ])
                            ->from('product_list PL')
                            ->join('owner_pair OP1', "OP1.owner_id = PL.owner_id AND OP1.type = '" . OwnerPair:: TYPE_MOVEMENT_TRANSPORT_UNIT . "' AND OP1.owner = '" . Movement::OWNER . "'")
                            ->join('owner_pair OP2', "OP2.parent_id = OP1.pair_id AND OP2.type = OP1.type AND OP2.owner = '" . TransportUnit::OWNER . "'")
                            ->join('transport_unit TU', "TU.transport_unit_id = OP2.owner_id")
                            ->andWhere("PL.owner_id = :id AND PL.owner = '" . Movement::OWNER . "'", [':id' => $p_i_movement_id])
                            ->queryAll();
        }
    }

    private static function getInvoiceFees($p_i_movement_id) {
        return Yii::app()->db->createCommand()
                        ->select([
                            'UPPER(M.currency) AS fee_currency',
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.no_ret_pen, CM.no_ret_usd), 2) AS `fee_amount`",
                            'M.expiration_date AS fee_date',
                        ])
                        ->from('movement M')
                        ->join('commercial_movement CM', 'CM.movement_id = M.movement_id')
                        ->where('M.`status`')
                        ->andWhere('M.movement_id = :id', [':id' => $p_i_movement_id])
                        ->queryAll();
    }

    private static function getSummaryHeaders($p_a_movement_ids) {
        $a_headers = Yii::app()->db->createCommand()
                ->select([
                    'M.movement_id',
                    'M.emission_date',
                    'M.document_serie',
                    'RIGHT(M.document_correlative, 8) AS document_correlative',
                    'UPPER(M.currency) AS currency',
                    'D.sunat_code',
                    'M.status',
                    'CH.reason_code',
                    'CH.reason',
                    "IF(M.currency = '" . Currency::PEN . "', CM.affected_pen, CM.affected_usd) AS affected",
                    "IF(M.currency = '" . Currency::PEN . "', CM.inaffected_pen, CM.inaffected_usd) AS inaffected",
                    "IF(M.currency = '" . Currency::PEN . "', CM.nobill_pen, CM.nobill_usd) AS nobill",
                    "IF(M.currency = '" . Currency::PEN . "', CM.export_pen, CM.export_usd) AS export",
                    "IF(M.currency = '" . Currency::PEN . "', CM.free_pen, CM.free_usd) AS free",
                    "IF(M.currency = '" . Currency::PEN . "', CM.igv_pen, CM.igv_usd) AS igv",
                    "IF(M.currency = '" . Currency::PEN . "', CM.total_pen, CM.total_usd) AS total",
                    "IF(M.currency = '" . Currency::PEN . "', CM.perception_pen, CM.perception_usd) AS perception",
                    "IF(M.currency = '" . Currency::PEN . "', CM.real_pen, CM.real_usd) AS `real`",
                    'XP.identification_type',
                    'XP.identification_number',
                    'MP.document_serie AS parent_document_serie',
                    'RIGHT(MP.document_correlative, 8) AS parent_document_correlative',
                    'DP.sunat_code AS parent_sunat_code',
                    '(GV.igv * 100) AS igv_percent',
                    'CM.free_transfer',
                ])
                ->from('movement M')
                ->join('document D', 'D.document_code = M.document_code')
                ->join('person XP', 'XP.person_id = M.aux_person_id')
                ->join('commercial_movement CM', 'CM.movement_id = M.movement_id')
                ->join('changelog CH', "CH.owner_id = M.movement_id AND CH.owner = '" . CommercialMovement::OWNER . "' AND CH.action = '" . Changelog::CREATE . "'")
                ->join('global_var GV', "GV.organization_id = 1")
                ->leftJoin('movement_link ML', "ML.movement_id = M.movement_id AND ML.parent_route = 'commercial/saleBill'")
                ->leftJoin('movement MP', 'MP.movement_id = ML.movement_parent_id')
                ->leftJoin('document DP', 'DP.document_code = MP.document_code')
                ->where('M.movement_id IN (' . implode(',', $p_a_movement_ids) . ')')
                ->queryAll();

        for ($i = 0; $i < count($a_headers); $i++) {
            $a_headers[$i]['no_data'] = $a_headers[$i]['sunat_code'] == Document::CREDIT_NOTE &&
                    isset($a_headers[$i]['reason_code']) && $a_headers[$i]['reason_code'] &&
                    $a_headers[$i]['reason_code'] == Multitable::CATALOG_9_CHANGE_EXPIRATION_DATE;
        }
        return $a_headers;
    }

    private static function getSummaryLowHeaders($p_a_movement_ids) {
        return Yii::app()->db->createCommand()
                        ->select([
                            'M.document_serie',
                            'RIGHT(M.document_correlative, 8) AS document_correlative',
                            'D.sunat_code',
                            'M.emission_date',
                            'L.reason'
                        ])
                        ->from('movement M')
                        ->join('document D', "M.document_code = D.document_code AND D.person_type = '" . Document::PERSON_TYPE_JURIDICAL . "'")
                        ->join('commercial_movement CM', 'M.movement_id = CM.movement_id')
                        ->leftJoin('changelog L', "L.owner_id = CM.movement_id AND L.owner = '" . CommercialMovement::OWNER . "' AND L.action = '" . Changelog::ANNUL . "'")
                        ->where('M.status = 0')
                        ->andWhere('FIND_IN_SET(M.movement_id,:id)', array(':id' => implode(',', $p_a_movement_ids)))
                        ->queryAll();
    }

    private static function setNamespaces(array &$p_a_xml, $p_s_type, $p_s_sunat_code = null) {

        $p_a_xml['@attributes'] = [];

        switch ($p_s_type) {
            case SIANFE::TYPE_INVOICE:
            case SIANFE::TYPE_REMISSION_GUIDE:
                $s_schema = "";
                switch ($p_s_sunat_code) {
                    case Document::FACTURA:
                        $s_schema = 'Invoice-2';
                        break;
                    case Document::REMISSION_GUIDE:
                        $s_schema = 'DespatchAdvice-2';
                        break;
                    case Document::CREDIT_NOTE:
                    case Document::SPECIAL_CREDIT_NOTE:
                    case Document::NOT_DOMICILED_CREDIT_NOTE:
                        $s_schema = 'CreditNote-2';
                        break;
                    case Document::DEBIT_NOTE:
                    case Document::SPECIAL_DEBIT_NOTE:
                    case Document::NOT_DOMICILED_DEBIT_NOTE:
                        $s_schema = 'DebitNote-2';
                        break;
                    default:
                        throw new Exception('Debe especificar un código de SUNAT válido, cuando genera un tipo Invoice');
                }

                $p_a_xml['@attributes']['xmlns'] = "urn:oasis:names:specification:ubl:schema:xsd:" . $s_schema;
                $p_a_xml['@attributes']['xmlns:ccts'] = "urn:un:unece:uncefact:documentation:2";
                $p_a_xml['@attributes']['xmlns:qdt'] = "urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2";
                $p_a_xml['@attributes']['xmlns:udt'] = "urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2";
                $p_a_xml['@attributes']['xmlns:ds'] = "http://www.w3.org/2000/09/xmldsig#";
                $p_a_xml['@attributes']['xmlns:xsi'] = "http://www.w3.org/2001/XMLSchema-instance";
                break;
            case SIANFE::TYPE_SUMMARY:
                $p_a_xml['@attributes']['xmlns'] = "urn:sunat:names:specification:ubl:peru:schema:xsd:SummaryDocuments-1";
                $p_a_xml['@attributes']['xmlns:ds'] = "http://www.w3.org/2000/09/xmldsig#";
                $p_a_xml['@attributes']['xmlns:xsi'] = "http://www.w3.org/2001/XMLSchema-instance";
                break;
            case SIANFE::TYPE_SUMMARY_LOW:
                $p_a_xml['@attributes']['xmlns'] = "urn:sunat:names:specification:ubl:peru:schema:xsd:VoidedDocuments-1";
                $p_a_xml['@attributes']['xmlns:ds'] = "http://www.w3.org/2000/09/xmldsig#";
                $p_a_xml['@attributes']['xmlns:xsi'] = "http://www.w3.org/2001/XMLSchema-instance";
                break;
            case SIANFE::TYPE_SELF_BILLED_INVOICE:
                $p_a_xml['@attributes']['xmlns'] = "urn:oasis:names:specification:ubl:schema:xsd:SelfBilledInvoice-2";
                $p_a_xml['@attributes']['xmlns:udt'] = "urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2";
                $p_a_xml['@attributes']['xmlns:ds'] = "http://www.w3.org/2000/09/xmldsig#";
                break;
        }
        //Common
        $p_a_xml['@attributes']['xmlns:ext'] = "urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2";
        $p_a_xml['@attributes']['xmlns:cac'] = "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2";
        $p_a_xml['@attributes']['xmlns:cbc'] = "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2";
        $p_a_xml['@attributes']['xmlns:sac'] = "urn:sunat:names:specification:ubl:peru:schema:xsd:SunatAggregateComponents-1";
    }

    private static function getUBLExtensions() {
        $a_extensionContents = [];
        //Extension 1
        $a_extensionContent1 = [];
        $a_extensionContent1["ext:ExtensionContent"] = [];
        //extensionContent1["ext:ExtensionContent"]["ds:Signature"] = "";
        //AGregamos item 1
        $a_extensionContents[] = $a_extensionContent1;

        //Creamos ojeto final
        $a_ublExtensions = [];
        $a_ublExtensions["ext:UBLExtension"] = $a_extensionContents;

        return $a_ublExtensions;
    }

    private static function setCommonData(&$p_a_xml, $p_s_type, $p_a_header, $p_s_document_name) {

        //CUSTOMIZATION
        switch ($p_s_type) {
            case SIANFE::TYPE_INVOICE:
            case SIANFE::TYPE_SELF_BILLED_INVOICE:
                $p_a_xml["cbc:UBLVersionID"] = "2.1";
                $s_customizationID = "2.0";
                break;
            case SIANFE::TYPE_SUMMARY:
                $p_a_xml["cbc:UBLVersionID"] = "2.0";
                $s_customizationID = "1.1";
                break;
            case SIANFE::TYPE_SUMMARY_LOW:
                $p_a_xml["cbc:UBLVersionID"] = "2.0";
                $s_customizationID = "1.0";
                break;
            case SIANFE::TYPE_REMISSION_GUIDE:
                $p_a_xml["cbc:UBLVersionID"] = "2.1";
                $s_customizationID = "2.0";
                break;
        }

        $a_data = [];
        IF ($p_s_type != SIANFE::TYPE_REMISSION_GUIDE) {
            $a_data = [
                'schemeAgencyName' => 'PE:SUNAT'
            ];
        }

        $p_a_xml['cbc:CustomizationID'] = [];
        $p_a_xml['cbc:CustomizationID']['@attributes'] = $a_data;
        $p_a_xml['cbc:CustomizationID']['@value'] = $s_customizationID;
        //ID
        $p_a_xml['cbc:ID'] = $p_s_document_name;
        //Reference date
        if ($p_s_type == SIANFE::TYPE_SUMMARY || $p_s_type == SIANFE::TYPE_SUMMARY_LOW) {
            $p_a_xml['cbc:ReferenceDate'] = $p_a_header["emission_date"];
        }

        //ISSUEDATE
        $s_issue_date = null;
        $s_issue_time = null;

        switch ($p_s_type) {
            case SIANFE::TYPE_SELF_BILLED_INVOICE:
            case SIANFE::TYPE_INVOICE:
            case SIANFE::TYPE_REMISSION_GUIDE:
                $s_issue_date = $p_a_header["emission_date"];
                $d_register_date = DateTime::createFromFormat('Y-m-d H:i:s', $p_a_header["register_date"]);
                $s_issue_time = $d_register_date->format('H:i:s');
                break;
            case SIANFE::TYPE_SUMMARY:
            case SIANFE::TYPE_SUMMARY_LOW:
                $s_issue_date = SIANTime::today();
                break;
        }
        $p_a_xml['cbc:IssueDate'] = $s_issue_date;
        if ($p_s_type == SIANFE::TYPE_REMISSION_GUIDE) {
            $p_a_xml['cbc:IssueTime'] = $s_issue_time;
        }
        //Moneda
        if (in_array($p_s_type, [SIANFE::TYPE_INVOICE, SIANFE::TYPE_SELF_BILLED_INVOICE])) {
            //Sólo para facturas
            if (in_array($p_a_header['sunat_code'], [Document::FACTURA, Document::LIQUIDACION_COMPRA])) {
                $p_a_xml['cbc:InvoiceTypeCode'] = [];
                $p_a_xml['cbc:InvoiceTypeCode']['@attributes'] = [
                    'listAgencyName' => 'PE:SUNAT',
                    'listName' => 'Tipo de Documento',
                    'listURI' => 'urn:pe:gob:sunat:cpe:see:gem:catalogos:catalogo01',
                    'listID' => ($p_a_header['sunat_code'] == Document::LIQUIDACION_COMPRA ? SIANFECatalog::CATALOG_51_INTERNAL_PURCHASE : ($p_a_header['detraction'] == 1 ? SIANFECatalog::CATALOG_51_SALE_DETRACTION : SIANFECatalog::CATALOG_51_INTERNAL_SALE ) )
                ];
                $p_a_xml['cbc:InvoiceTypeCode']['@value'] = $p_a_header['sunat_code']; //Catalog 01
            }

            //Notas
            $p_a_xml['cbc:Note'] = [
                '@attributes' => [
                    'languageLocaleID' => SIANFECatalog::CATALOG_52_AMOUNT_TO_LETTERS,
                ],
                '@value' => Currency::amountToLetters($p_a_header['real'], strtolower($p_a_header['currency']))
            ];
            if ($p_a_header['detraction'] == 1) {
                $p_a_xml['cbc:Note'] = [
                    '@attributes' => [
                        'languageLocaleID' => SIANFECatalog::CATALOG_52_OPERATION_DETRACTION,
                    ],
                    '@value' => Strings::DETRACTION_STRING
                ];
            }
            if ($p_a_header['free_transfer'] == 1) {
                $p_a_xml['cbc:Note'] = [
                    '@attributes' => [
                        'languageLocaleID' => SIANFECatalog::CATALOG_52_FREE_TRANSFER,
                    ],
                    '@value' => Strings::FREE_TRANSFER_LONG
                ];
            }
            //Moneda, Catalog 2
            $p_a_xml['cbc:DocumentCurrencyCode'] = [];
            $p_a_xml['cbc:DocumentCurrencyCode']['@attributes'] = [
                'listID' => 'ISO 4217 Alpha',
                'listName' => 'Currency',
                'listAgencyName' => 'United Nations Economic Commission for Europe',
            ];
            $p_a_xml['cbc:DocumentCurrencyCode']['@value'] = $p_a_header['currency'];
        }
        if (in_array($p_s_type, [SIANFE::TYPE_REMISSION_GUIDE])) {
            $p_a_xml['cbc:DespatchAdviceTypeCode'] = $p_a_header['sunat_code'];
            //Notas
            $p_a_xml['cbc:Note'] = $p_a_header['observation'];
        }
    }

    private static function getSignature($p_s_type = "", $p_a_header = []) {

        $a_signature = [];
        switch ($p_s_type) {
            case SIANFE::TYPE_SELF_BILLED_INVOICE:
                $a_signature['cbc:ID'] = 'IDSignKG';
                break;
            case SIANFE::TYPE_REMISSION_GUIDE:
                $a_signature['cbc:ID'] = Yii::app()->controller->getOrganization()->person->identification_number;
                break;
            default :
                $a_signature['cbc:ID'] = 'IDSignSP';
                break;
        }
        $a_signature['cac:SignatoryParty']['cac:PartyIdentification']['cbc:ID'] = Yii::app()->controller->getOrganization()->person->identification_number;
        $a_signature['cac:SignatoryParty']['cac:PartyName']['cbc:Name'] = Yii::app()->controller->getOrganization()->person->person_name;
        //$a_signature['cac:DigitalSignatureAttachment']['cac:ExternalReference']['cbc:URI'] = ($p_s_type == SIANFE::TYPE_SELF_BILLED_INVOICE ? 'signatureKG' : '#SignatureSP' );
        switch ($p_s_type) {
            case SIANFE::TYPE_SELF_BILLED_INVOICE:
                $a_signature['cac:DigitalSignatureAttachment']['cac:ExternalReference']['cbc:URI'] = 'signatureKG';
                break;
            case SIANFE::TYPE_REMISSION_GUIDE:
                $a_signature['cac:DigitalSignatureAttachment']['cac:ExternalReference']['cbc:URI'] = Yii::app()->controller->getOrganization()->person->identification_number;
                break;
            default :
                $a_signature['cac:DigitalSignatureAttachment']['cac:ExternalReference']['cbc:URI'] = '#SignatureSP';
                break;
        }

        return $a_signature;
    }

    private static function getAccountingSupplierParty($p_s_type, $p_a_header = null) {

        $a_accountingParty = [];

        switch ($p_s_type) {
            case SIANFE::TYPE_INVOICE:
                $a_accountingParty['cac:Party'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@attributes'] = [
                    'schemeID' => Yii::app()->controller->getOrganization()->person->identification_type
                ];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@value'] = Yii::app()->controller->getOrganization()->person->identification_number;
                //
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cbc:RegistrationName'] = Yii::app()->controller->getOrganization()->person->person_name;
                //Local anexo
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cbc:AddressTypeCode'] = '0000';
                //
                break;
            case SIANFE::TYPE_SUMMARY:
            case SIANFE::TYPE_SUMMARY_LOW:
                $a_accountingParty['cbc:CustomerAssignedAccountID'] = Yii::app()->controller->getOrganization()->person->identification_number;
                $a_accountingParty['cbc:AdditionalAccountID'] = Yii::app()->controller->getOrganization()->person->identification_type;
                $a_accountingParty['cac:Party'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cbc:RegistrationName'] = Yii::app()->controller->getOrganization()->person->person_name;
                break;
            case SIANFE::TYPE_REMISSION_GUIDE:
                $a_accountingParty['cbc:CustomerAssignedAccountID'] = [];
                $a_accountingParty['cbc:CustomerAssignedAccountID']['@attributes'] = [
                    'schemeID' => Yii::app()->controller->getOrganization()->person->identification_type
                ];
                $a_accountingParty['cbc:CustomerAssignedAccountID']['@value'] = Yii::app()->controller->getOrganization()->person->identification_number;
                $a_accountingParty['cac:Party'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@attributes'] = [
                    'schemeID' => Yii::app()->controller->getOrganization()->person->identification_type,
                    'schemeName' => 'Documento de Identidad',
                    'schemeAgencyName' => 'PE:SUNAT',
                    'schemeURI' => 'urn:pe:gob:sunat:cpe:see:gem:catalogos:catalogo06'
                ];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@value'] = Yii::app()->controller->getOrganization()->person->identification_number;
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cbc:RegistrationName'] = Yii::app()->controller->getOrganization()->person->person_name;
                break;
            case SIANFE::TYPE_SELF_BILLED_INVOICE:
                $a_accountingParty['cac:Party'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@attributes'] = [
                    'schemeID' => $p_a_header['identification_type']
                ];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@value'] = $p_a_header['identification_number'];
                //
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cbc:RegistrationName'] = $p_a_header['aux_person_name'];
                //Local anexo
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cbc:ID'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cbc:ID']['@attributes'] = [
                    'schemeAgencyName' => "PE:INEI",
                    'schemeName' => "Ubigeos",
                ];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cbc:ID']['@value'] = $p_a_header['dept_code'] . $p_a_header['prov_code'] . $p_a_header['dist_code'];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cbc:AddressTypeCode'] = '01'; //Crear función que devuelva la estructura de la dirección
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cbc:CityName'] = $p_a_header['prov_name_seller'];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cbc:CountrySubentity'] = $p_a_header['dept_name_seller'];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cbc:District'] = $p_a_header['dist_name_seller'];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cac:AddressLine'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cac:AddressLine']['cbc:Line'] = $p_a_header['address_seller'];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cac:Country'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cac:Country']['cbc:IdentificationCode'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cac:Country']['cbc:IdentificationCode']['@attributes'] = [
                    'listID' => "ISO 3166-1",
                    'listAgencyName' => "United Nations Economic Commission for Europe",
                    'listName' => "Country"
                ];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cac:RegistrationAddress']['cac:Country']['cbc:IdentificationCode']['@value'] = "PE";

                //
                break;
        }

        return $a_accountingParty;
    }

    private static function getAccountingCustomerParty($p_s_type, $p_a_header = []) {
        $a_accountingParty = [];

        switch ($p_s_type) {
            case SIANFE::TYPE_INVOICE:
                $a_accountingParty['cac:Party'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@attributes'] = [
                    'schemeID' => $p_a_header['identification_type']
                ];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@value'] = $p_a_header['identification_number'];
                //
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cbc:RegistrationName'] = $p_a_header['aux_person_name'];
                break;
            case SIANFE::TYPE_SUMMARY:
                $a_accountingParty['cbc:CustomerAssignedAccountID'] = $p_a_header['identification_number'];
                $a_accountingParty['cbc:AdditionalAccountID'] = $p_a_header['identification_type'];
                break;
            case SIANFE::TYPE_REMISSION_GUIDE:
                $a_accountingParty['cac:Party'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@attributes'] = [
                    'schemeID' => $p_a_header['identification_type'],
                    'schemeName' => 'Documento de Identidad',
                    'schemeAgencyName' => 'PE:SUNAT',
                    'schemeURI' => 'urn:pe:gob:sunat:cpe:see:gem:catalogos:catalogo06'
                ];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@value'] = $p_a_header['identification_number'];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cbc:RegistrationName'] = $p_a_header['aux_person_name'];
                break;
            case SIANFE::TYPE_SELF_BILLED_INVOICE:
                $a_accountingParty['cac:Party'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@attributes'] = [
                    'schemeID' => Yii::app()->controller->getOrganization()->person->identification_type
                ];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@value'] = Yii::app()->controller->getOrganization()->person->identification_number;
                //
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cbc:RegistrationName'] = Yii::app()->controller->getOrganization()->person->person_name;
                break;
        }

        return $a_accountingParty;
    }

    private static function getAccountingBuyerParty($p_s_type, $p_a_header = null) {

        $a_accountingParty = [];

        switch ($p_s_type) {
            case SIANFE::TYPE_REMISSION_GUIDE:
                $a_accountingParty['cac:Party'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID'] = [];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@attributes'] = [
                    'schemeID' => $p_a_header['buyer_identification_type'],
                    'schemeName' => 'Documento de Identidad',
                    'schemeAgencyName' => 'PE:SUNAT',
                    'schemeURI' => 'urn:pe:gob:sunat:cpe:see:gem:catalogos:catalogo06'
                ];
                $a_accountingParty['cac:Party']['cac:PartyIdentification']['cbc:ID']['@value'] = $p_a_header['buyer_identification_number'];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity'] = [];
                $a_accountingParty['cac:Party']['cac:PartyLegalEntity']['cbc:RegistrationName'] = $p_a_header['buyer_name'];
                break;
        }

        return $a_accountingParty;
    }

    private static function getAdditionalDocumentReference($p_s_type, $p_a_header = null) {

        $a_documentReference = [];

        switch ($p_s_type) {
            case SIANFE::TYPE_REMISSION_GUIDE:
                $a_documentReference['cbc:ID'] = $p_a_header['parent_reference_document'];
                $a_documentReference['cbc:DocumentTypeCode'] = [];
                $a_documentReference['cbc:DocumentTypeCode']['@attributes'] = [
                    'listAgencyName' => 'PE:SUNAT',
                    'listName' => 'Documento relacionado al transporte',
                    'listURI' => 'urn:pe:gob:sunat:cpe:see:gem:catalogos:catalogo61',
                ];
                $a_documentReference['cbc:DocumentTypeCode']['@value'] = $p_a_header['parent_reference_sunat_code'];
                $a_documentReference['cbc:DocumentType'] = $p_a_header['parent_reference_type'];
                $a_documentReference['cac:IssuerParty'] = [];
                $a_documentReference['cac:IssuerParty']['cac:PartyIdentification']['cbc:ID'] = [];
                $a_documentReference['cac:IssuerParty']['cac:PartyIdentification']['cbc:ID']['@attributes'] = [
                    'schemeID' => Yii::app()->controller->getOrganization()->person->identification_type,
                    'schemeName' => 'Documento de Identidad',
                    'schemeAgencyName' => 'PE:SUNAT',
                    'schemeURI' => 'urn:pe:gob:sunat:cpe:see:gem:catalogos:catalogo06'
                ];
                $a_documentReference['cac:IssuerParty']['cac:PartyIdentification']['cbc:ID']['@value'] = Yii::app()->controller->getOrganization()->person->identification_number;
                break;
        }

        return $a_documentReference;
    }

    private static function getShipment($p_a_header, $p_a_shippers, $p_a_transport_units) {

        $s_transport_unit_plate = '';

        if (count($p_a_transport_units) > 0) {
            $s_transport_unit_plate = str_replace('-', '', $p_a_transport_units[0]['plate']);
        }

        $a_shipment = [];
        $a_shipment['cbc:ID'] = "SUNAT_Envio";
        $a_shipment['cbc:HandlingCode'] = $p_a_header['reason_transfer_code'];
        $a_shipment['cbc:HandlingInstructions'] = $p_a_header['reason_transfer_name'];
        $a_shipment['cbc:GrossWeightMeasure'] = [];
        $a_shipment['cbc:GrossWeightMeasure']['@attributes'] = [
            'unitCode' => 'KGM'
        ];
        $a_shipment['cbc:GrossWeightMeasure']['@value'] = $p_a_header['total_weight'];

        $a_shipment['cac:ShipmentStage'] = [];
        $a_shipment['cac:ShipmentStage']['cbc:TransportModeCode'] = [];
        $a_shipment['cac:ShipmentStage']['cbc:TransportModeCode']['@attributes'] = [
            'listName' => 'Modalidad de traslado',
            'listAgencyName' => 'PE:SUNAT',
            'listURI' => 'urn:pe:gob:sunat:cpe:see:gem:catalogos:catalogo18'
        ];
        $s_transport_mode_code = $p_a_header['transport_data'] == 1 ? SIANFECatalog::CATALOG_18_PRIVATE : SIANFECatalog::CATALOG_18_PUBLIC;
        $a_shipment['cac:ShipmentStage']['cbc:TransportModeCode']['@value'] = $s_transport_mode_code;

        $a_shipment['cac:ShipmentStage']['cac:TransitPeriod'] = [];
        $a_shipment['cac:ShipmentStage']['cac:TransitPeriod']['cbc:StartDate'] = $p_a_header['transfer_start_date'];

        switch ($s_transport_mode_code) {

            case SIANFECatalog::CATALOG_18_PUBLIC:

                //Compañia de traslado
                $a_shipment['cac:ShipmentStage']['cac:CarrierParty'] = [];
                $a_shipment['cac:ShipmentStage']['cac:CarrierParty'] ['cac:PartyIdentification'] = [];
                $a_shipment['cac:ShipmentStage']['cac:CarrierParty'] ['cac:PartyIdentification']['cbc:ID'] = [];
                $a_shipment['cac:ShipmentStage']['cac:CarrierParty'] ['cac:PartyIdentification']['cbc:ID'] ['@attributes'] = [
                    'schemeID' => $p_a_header['transport_company_type'],
                    'schemeName' => 'Documento de Identidad',
                    'schemeAgencyName' => 'PE:SUNAT',
                    'schemeURI' => 'urn:pe:gob:sunat:cpe:see:gem:catalogos:catalogo06'
                ];
                $a_shipment['cac:ShipmentStage']['cac:CarrierParty'] ['cac:PartyIdentification']['cbc:ID'] ['@value'] = $p_a_header['transport_company_number'];
                $a_shipment['cac:ShipmentStage']['cac:CarrierParty'] ['cac:PartyLegalEntity'] = [];
                $a_shipment['cac:ShipmentStage']['cac:CarrierParty'] ['cac:PartyLegalEntity']['cbc:RegistrationName'] = $p_a_header['transport_company_name'];
                break;

            case SIANFECatalog::CATALOG_18_PRIVATE:

                //Conductores
                if (count($p_a_shippers) > 0) {

                    $a_shippers = [];

                    foreach ($p_a_shippers as $o_shipper) {

                        $s_type_shipper = 'Secundario';
                        $a_shipper = [];

                        if ($o_shipper->default == 1) {
                            $s_type_shipper = 'Principal';
                        }
                        $a_shipper['cbc:ID'] = [];
                        $a_shipper['cbc:ID']['@attributes'] = [
                            'schemeID' => $o_shipper->shipper->person->identification_type,
                            'schemeName' => 'Documento de Identidad',
                            'schemeAgencyName' => 'PE:SUNAT',
                            'schemeURI' => 'urn:pe:gob:sunat:cpe:see:gem:catalogos:catalogo06'
                        ];
                        $a_shipper['cbc:ID']['@value'] = $o_shipper->shipper->person->identification_number;
                        $a_shipper['cbc:FirstName'] = $o_shipper->shipper->person->firstname;
                        $a_shipper['cbc:FamilyName'] = $o_shipper->shipper->person->paternal . ' ' . $o_shipper->shipper->person->maternal;
                        $a_shipper['cbc:JobTitle'] = $s_type_shipper;
                        $a_shipper['cac:IdentityDocumentReference'] = [];
                        $a_shipper['cac:IdentityDocumentReference']['cbc:ID'] = $o_shipper->shipper->license_number;
                        $a_shippers[] = $a_shipper;
                    }
                    $a_shipment['cac:ShipmentStage']['cac:DriverPerson'] = $a_shippers;
                }
                break;
        }

        $a_shipment['cac:Delivery'] = [];
        //
        $a_shipment['cac:Delivery']['cac:DeliveryAddress'] = [];
        $a_shipment['cac:Delivery']['cac:DeliveryAddress']['cbc:ID'] = [];
        $a_shipment['cac:Delivery']['cac:DeliveryAddress']['cbc:ID'] ['@attributes'] = [
            'schemeAgencyName' => 'PE:INEI',
            'schemeName' => 'Ubigeos',
        ];
        $a_shipment['cac:Delivery']['cac:DeliveryAddress']['cbc:ID'] ['@value'] = $p_a_header['delivery_address_ubigeo'];
        $a_shipment['cac:Delivery']['cac:DeliveryAddress']['cac:AddressLine'] = [];
        $a_shipment['cac:Delivery']['cac:DeliveryAddress']['cac:AddressLine']['cbc:Line'] = $p_a_header['delivery_address'];
        //
        $a_shipment['cac:Delivery']['cac:Despatch'] = [];
        $a_shipment['cac:Delivery']['cac:Despatch']['cac:DespatchAddress'] = [];
        $a_shipment['cac:Delivery']['cac:Despatch']['cac:DespatchAddress']['cbc:ID'] = [];
        $a_shipment['cac:Delivery']['cac:Despatch']['cac:DespatchAddress']['cbc:ID'] ['@attributes'] = [
            'schemeAgencyName' => 'PE:INEI',
            'schemeName' => 'Ubigeos',
        ];
        $a_shipment['cac:Delivery']['cac:Despatch']['cac:DespatchAddress']['cbc:ID'] ['@value'] = $p_a_header['origin_address_ubigeo'];
        $a_shipment['cac:Delivery']['cac:Despatch']['cac:DespatchAddress']['cac:AddressLine'] = [];
        $a_shipment['cac:Delivery']['cac:Despatch']['cac:DespatchAddress']['cac:AddressLine']['cbc:Line'] = $p_a_header['origin_address'];

        //Vehiculos
        $a_shipment['cac:TransportHandlingUnit'] = [];
        $a_shipment['cac:TransportHandlingUnit']['cac:TransportEquipment'] = [];
        $a_shipment['cac:TransportHandlingUnit']['cac:TransportEquipment']['cbc:ID'] = $s_transport_unit_plate;

        return $a_shipment;
    }

    private static function getAddress($p_a_header) {

        $a_address = [];
        $a_address['cac:DeliveryLocation'] = [];
        $a_address['cac:DeliveryLocation']['cbc:LocationTypeCode'] = '01'; //Dinamizar
        $a_address['cac:DeliveryLocation']['cac:Address'] = [];
        $a_address['cac:DeliveryLocation']['cac:Address']['cbc:ID'] = [];
        $a_address['cac:DeliveryLocation']['cac:Address']['cbc:ID']['@attributes'] = [
            'schemeID' => "PE:INEI",
            'schemeName' => "Ubigeos",
        ];
        $a_address['cac:DeliveryLocation']['cac:Address']['cbc:ID']['@value'] = $p_a_header['dept_code'] . $p_a_header['prov_code'] . $p_a_header['dist_code'];
        $a_address['cac:DeliveryLocation']['cac:Address']['cac:AddressLine'] = [];
        $a_address['cac:DeliveryLocation']['cac:Address']['cac:AddressLine']['cbc:Line'] = [];
        $a_address['cac:DeliveryLocation']['cac:Address']['cac:AddressLine']['cbc:Line']['@value'] = $p_a_header['address'];
        $a_address['cac:DeliveryLocation']['cac:Address']['cac:Country'] = [];
        $a_address['cac:DeliveryLocation']['cac:Address']['cac:Country']['cbc:IdentificationCode'] = [];
        $a_address['cac:DeliveryLocation']['cac:Address']['cac:Country']['cbc:IdentificationCode']['@attributes'] = [
            'listID' => "ISO 3166-1",
            'listAgencyName' => "United Nations Economic Commission for Europe",
            'listName' => "Country"
        ];
        $a_address['cac:DeliveryLocation']['cac:Address']['cac:Country']['cbc:IdentificationCode']['@value'] = "PE";

        return $a_address;
    }

    private static function getPaymentMeans($p_a_header) {
        $a_payment_means = [];
        //Si es factura o es crédito se admite cuotas
        //Primero
        $a_payment_mean = [];
        $a_payment_mean['cbc:ID'] = 'Detraccion';
        $a_payment_mean['cbc:PaymentMeansCode'] = '001';
//        $a_payment_mean['cbc:PaymentMeansCode']['@attributes'] = [
//            'listAgencyName' => "PE:SUNAT",
//            'listName' => "Medio de pago",
//            'listURI' => "urn:pe:gob:sunat:cpe:see:gem:catalogos:catalogo59"
//        ];
//        $a_payment_mean['cbc:PaymentMeansCode']['@value'] = '003';
        $a_payment_mean['cac:PayeeFinancialAccount'] = [];
        $a_payment_mean['cac:PayeeFinancialAccount'] = [
            'cbc:ID' => $p_a_header['detraction_account']
        ];
        //Agregamos
        $a_payment_means[] = $a_payment_mean;
        return $a_payment_means;
    }

    private static function getPaymentTerms($p_a_header, $p_a_fees) {
        $a_payment_terms = [];
        //Si hay detracción
        if ($p_a_header['detraction'] == 1) {
            $a_payment_term_detraction = [];
            $a_payment_term_detraction['cbc:ID'] = 'Detraccion';
            $a_payment_term_detraction['cbc:PaymentMeansID'] = $p_a_header['detraction_code'];
//            $a_payment_term_detraction['cbc:PaymentMeansID']['@attributes'] = [
//                'schemeName' => "SUNAT:Codigo de detraccion",
//                'schemeAgencyName' => "PE:SUNAT",
//                'schemeURI' => "urn:pe:gob:sunat:cpe:see:gem:catalogos:catalogo54"
//            ];
//            $a_payment_term_detraction['cbc:PaymentMeansID']['@value'] = $p_a_header['detraction_code'];
            $a_payment_term_detraction['cbc:PaymentPercent'] = $p_a_header['detraction_percent'];
            $a_payment_term_detraction['cbc:Amount'] = [];
            $a_payment_term_detraction['cbc:Amount']['@attributes'] = [
                'currencyID' => strtoupper(Currency::PEN)
            ];
            $a_payment_term_detraction['cbc:Amount']['@value'] = $p_a_header['detraction_amount'];
            $a_payment_terms[] = $a_payment_term_detraction;
        }
        //Si es factura o es crédito se admite cuotas
        //Primero
        $a_first = [];
        $a_first['cbc:ID'] = 'FormaPago';
        $a_first['cbc:PaymentMeansID'] = $p_a_header['no_data'] ? SIANFE::PAYMENT_TERM_CREDIT : $p_a_header['payment_term'];
        if ($p_a_header['payment_term'] == SIANFE::PAYMENT_TERM_CREDIT || $p_a_header['no_data']) {

            $d_total_amount_fees = 0;

            if ($p_a_header['balance'] > 0 || $p_a_header['no_data']) {
                foreach ($p_a_fees as $i => $a_fee) {
                    $d_total_amount_fees += $a_fee['fee_amount'];
                }
            }
            $a_first['cbc:Amount'] = [];
            $a_first['cbc:Amount']['@attributes'] = [
                'currencyID' => $p_a_header['currency']
            ];
            $a_first['cbc:Amount']['@value'] = $d_total_amount_fees;
        }
        //Agregamos
        $a_payment_terms[] = $a_first;
        //Si el balance es mayor que cero
        if ($p_a_header['balance'] > 0 || $p_a_header['no_data']) {
            foreach ($p_a_fees as $i => $a_fee) {
                $a_payment_term = [];
                $a_payment_term['cbc:ID'] = 'FormaPago';
                $a_payment_term['cbc:PaymentMeansID'] = 'Cuota' . str_pad($i + 1, 3, '0', STR_PAD_LEFT);
                $a_payment_term['cbc:Amount'] = [];
                $a_payment_term['cbc:Amount']['@attributes'] = [
                    'currencyID' => $a_fee['fee_currency']
                ];
                $a_payment_term['cbc:Amount']['@value'] = $a_fee['fee_amount'];
                $a_payment_term['cbc:PaymentDueDate'] = $a_fee['fee_date'];
                //Agregamos
                $a_payment_terms[] = $a_payment_term;
            }
        }
        //
        return $a_payment_terms;
    }

    private static function getTaxCategory($p_a_data, $s_05_code, $p_b_detail) {

        $s_05_duty_codes = SIANFECatalog::getCatalog05DutyCodes();
        $a_05_inter_codes = SIANFECatalog::getCatalog05InterCodes();
        $a_05_names = SIANFECatalog::getCatalog05Names();
        //TaxCategory
        $a_taxCategory = [];
        if ($p_b_detail) {
            $a_taxCategory['cbc:ID'] = $s_05_duty_codes[$s_05_code];
            if ($s_05_code == SIANFECatalog::CATALOG_05_1000) {
                $a_taxCategory['cbc:Percent'] = $p_a_data['igv_percent'];
            } else {
                $a_taxCategory['cbc:Percent'] = 0;
            }
            $a_taxCategory['cbc:TaxExemptionReasonCode'] = $p_a_data['igv_affection'];
        }
        //
        $a_taxCategory['cac:TaxScheme'] = [];
        $a_taxCategory['cac:TaxScheme']['cbc:ID'] = $s_05_code;
        $a_taxCategory['cac:TaxScheme']['cbc:Name'] = $a_05_names[$s_05_code];
        $a_taxCategory['cac:TaxScheme']['cbc:TaxTypeCode'] = $a_05_inter_codes[$s_05_code];

        return $a_taxCategory;
    }

    private static function getTaxSubTotal($p_a_data, $s_field, $s_05_code, $p_b_detail) {
        $a_subTotal = [];
        //TaxableAmount
        $a_subTotal['cbc:TaxableAmount'] = [];
        $a_subTotal['cbc:TaxableAmount']['@attributes'] = [
            'currencyID' => $p_a_data['currency']
        ];
        $a_subTotal['cbc:TaxableAmount']['@value'] = $p_a_data['no_data'] ? 0.00 : $p_a_data[$s_field];
        //TaxAmount
        $a_subTotal['cbc:TaxAmount'] = [];
        $a_subTotal['cbc:TaxAmount']['@attributes'] = [
            'currencyID' => $p_a_data['currency']
        ];
        if ($s_05_code == SIANFECatalog::CATALOG_05_1000) {
            $a_subTotal['cbc:TaxAmount']['@value'] = $p_a_data['no_data'] ? 0.00 : $p_a_data['igv'];
        } else {
            $a_subTotal['cbc:TaxAmount']['@value'] = 0;
        }
        //TaxCategory
        $a_subTotal['cac:TaxCategory'] = self::getTaxCategory($p_a_data, $s_05_code, $p_b_detail);

        return $a_subTotal;
    }

    /**
     * General el bloque de impuestos para invoices
     * @param array $p_a_data Data dónde están los montos
     * @param booleam $p_b_detail Indica si se debe agregar el tipo de afectación al IGV
     * @return array Bloque de impuestos
     */
    private static function getTaxTotal(array $p_a_data, $p_b_detail = false) {

        //CODES
        $a_map = SIANFECatalog::getCatalog05Map();
        //
        $a_taxTotal = [];

        $a_taxTotal['cbc:TaxAmount'] = [];
        $a_taxTotal['cbc:TaxAmount']['@attributes'] = [
            'currencyID' => $p_a_data['currency']
        ];
        $a_taxTotal['cbc:TaxAmount']['@value'] = $p_a_data['no_data'] ? 0.00 : $p_a_data['igv'];

        $a_subTotals = [];
        foreach ($a_map as $s_05_code => $s_field) {
            //Si es mayor que cero
            if ($p_a_data['no_data']) {
                $p_a_data[$s_field] = 0;
            }
            if ($p_a_data[$s_field] > 0 || $p_a_data['no_data']) {
                $a_subTotals[] = self::getTaxSubTotal($p_a_data, $s_field, $s_05_code, $p_b_detail);
            }
        }

        $a_taxTotal['cac:TaxSubtotal'] = $a_subTotals;
        //
        return $a_taxTotal;
    }

    /**
     * General el bloque de impuestos para resúmenes
     * @param array $p_a_data Data dónde están los montos
     * @return array Bloque de impuestos
     */
    private static function getSummaryTaxTotal(array $p_a_data) {

        //CODES
        $a_map = SIANFECatalog::getCatalog05Map();
        //
        $a_taxTotal = [];
        foreach ($a_map as $s_05_code => $s_field) {

            $a_taxTotalItem = [];

            $a_taxTotalItem['cbc:TaxAmount'] = [];
            $a_taxTotalItem['cbc:TaxAmount']['@attributes'] = [
                'currencyID' => $p_a_data['currency']
            ];
            if ($s_05_code == SIANFECatalog::CATALOG_05_1000) {
                $a_taxTotalItem['cbc:TaxAmount']['@value'] = $p_a_data['no_data'] ? 0 : $p_a_data['igv'];
            } else {
                $a_taxTotalItem['cbc:TaxAmount']['@value'] = 0;
            }
            if ($p_a_data['no_data']) {
                $p_a_data[$s_field] = 0;
            }
            $a_taxTotalItem['cac:TaxSubtotal'] = self::getTaxSubTotal($p_a_data, $s_field, $s_05_code, false);

            $a_taxTotal[] = $a_taxTotalItem;
        }
        //
        return $a_taxTotal;
    }

    private static function getLegalMonetaryItem($p_a_header, $p_s_field) {
        $a_item = [];
        $a_item["@attributes"] = [
            'currencyID' => $p_a_header['currency']
        ];
        $a_item["@value"] = $p_a_header['no_data'] ? 0 : $p_a_header[$p_s_field];
        return $a_item;
    }

    private static function getLegalMonetaryTotal($p_a_header) {

        $a_legalMonetaryTotal = [];
        //valor venta
        $a_legalMonetaryTotal['cbc:LineExtensionAmount'] = self::getLegalMonetaryItem($p_a_header, "net");
        //Precio  venta
        $a_legalMonetaryTotal['cbc:TaxInclusiveAmount'] = self::getLegalMonetaryItem($p_a_header, "total");
        //Total a pagar
        $a_legalMonetaryTotal['cbc:PayableAmount'] = self::getLegalMonetaryItem($p_a_header, "real");

        return $a_legalMonetaryTotal;
    }

    private static function getRequestedMonetaryTotal($p_a_header) {

        $a_requestedMonetaryTotal = [];
        //Otros cargos
        $a_requestedMonetaryTotal['cbc:ChargeTotalAmount'] = self::getLegalMonetaryItem($p_a_header, "perception");
        //Total a pagar
        $a_requestedMonetaryTotal['cbc:PayableAmount'] = self::getLegalMonetaryItem($p_a_header, "real");

        return $a_requestedMonetaryTotal;
    }

    private static function getBillingReference($p_a_header, $p_s_type) {

        $a_billingReferece = [];

        switch ($p_s_type) {
            case SIANFE::TYPE_INVOICE:
                switch ($p_a_header['sunat_code']) {
                    case Document::CREDIT_NOTE:
                    case Document::SPECIAL_CREDIT_NOTE:
                    case Document::NOT_DOMICILED_CREDIT_NOTE:
                        $a_billingReferece['cac:InvoiceDocumentReference']['cbc:ID'] = $p_a_header['parent_document_serie'] . '-' . $p_a_header['parent_document_correlative'];
                        $a_billingReferece['cac:InvoiceDocumentReference']['cbc:DocumentTypeCode'] = $p_a_header['parent_sunat_code'];
                        //$a_billingReferece['cac:CreditNoteDocumentReference']['cbc:ID'] = $p_a_header['parent_document_serie'] . '-' . $p_a_header['parent_document_correlative'];
                        //$a_billingReferece['cac:CreditNoteDocumentReference']['cbc:DocumentTypeCode'] = $p_a_header['parent_sunat_code'];
                        break;
                    case Document::DEBIT_NOTE:
                    case Document::SPECIAL_DEBIT_NOTE:
                    case Document::NOT_DOMICILED_DEBIT_NOTE:
                        //$a_billingReferece['cac:DebitNoteDocumentReference']['cbc:ID'] = $p_a_header['parent_document_serie'] . '-' . $p_a_header['parent_document_correlative'];
                        //$a_billingReferece['cac:DebitNoteDocumentReference']['cbc:DocumentTypeCode'] = $p_a_header['parent_sunat_code'];
                        $a_billingReferece['cac:InvoiceDocumentReference']['cbc:ID'] = $p_a_header['parent_document_serie'] . '-' . $p_a_header['parent_document_correlative'];
                        $a_billingReferece['cac:InvoiceDocumentReference']['cbc:DocumentTypeCode'] = $p_a_header['parent_sunat_code'];
                        break;
                    default:
                        throw new Exception('No soportado');
                }

                break;
            case SIANFE::TYPE_SUMMARY:
                $a_billingReferece['cac:InvoiceDocumentReference']['cbc:ID'] = $p_a_header['parent_document_serie'] . '-' . $p_a_header['parent_document_correlative'];
                $a_billingReferece['cac:InvoiceDocumentReference']['cbc:DocumentTypeCode'] = $p_a_header['parent_sunat_code'];
                break;
            default:
                throw new Exception('No soportado');
        }

        return $a_billingReferece;
    }

    private static function getDiscrepancyResponse($p_a_header) {

        $a_discrepancyResponse = [];

        $a_discrepancyResponse['cbc:ReferenceID'] = $p_a_header['parent_document_serie'] . '-' . $p_a_header['parent_document_correlative'];
        $a_discrepancyResponse['cbc:ResponseCode'] = $p_a_header['reason_code']; //Motivo
        $a_discrepancyResponse['cbc:Description'] = $p_a_header['reason'];

        return $a_discrepancyResponse;
    }

    private static function getBillingPayment(array $a_header) {

        $a_billingPayment = [];

        $a_map = SIANFECatalog::getCatalog11Map();

        foreach ($a_map as $s_code11 => $s_field) {
            //
            if ($a_header[$s_field] > 0) {
                $a_billingPayment[] = [
                    'cbc:PaidAmount' => [
                        '@attributes' => [
                            'currencyID' => $a_header['currency']
                        ],
                        '@value' => $a_header['no_data'] ? 0.00 : $a_header[$s_field]
                    ],
                    'cbc:InstructionID' => $s_code11,
                ];
            }
        }

        return $a_billingPayment;
    }

    public static function getInvoiceXML($p_i_movement_id) {

        $a_header = self::getInvoiceHeader($p_i_movement_id);
        $a_detail = self::getInvoiceDetail($a_header);
        $a_fees = [];
        if ($a_header['payment_term'] == SIANFE::PAYMENT_TERM_CREDIT) {
            $a_fees = self::getInvoiceFees($p_i_movement_id);
        }

        $s_type = SIANFE::TYPE_INVOICE;
        $s_document_name = $a_header["document_serie"] . "-" . $a_header["document_correlative"];

        $a_invoice = [];
        //Seteamos espacios de nombres
        self::setNamespaces($a_invoice, $s_type, $a_header['sunat_code']);
        //UBL Extensions
        $a_invoice["ext:UBLExtensions"] = self::getUBLExtensions();
        //Información en común
        self::setCommonData($a_invoice, $s_type, $a_header, $s_document_name);
        //Signature
        $a_invoice['cac:Signature'] = self::getSignature();
        //Suplier
        $a_invoice["cac:AccountingSupplierParty"] = self::getAccountingSupplierParty($s_type);
        //Customer
        $a_invoice["cac:AccountingCustomerParty"] = self::getAccountingCustomerParty($s_type, $a_header);
        if ($a_header['detraction'] == 1) {
            //PaymentMeans
            $a_invoice['cac:PaymentMeans'] = self::getPaymentMeans($a_header);
        }
        //PaymentTerms 
        $a_invoice['cac:PaymentTerms'] = self::getPaymentTerms($a_header, $a_fees);
        //Tax Total
        $a_invoice['cac:TaxTotal'] = self::getTaxTotal($a_header);
        //Total
        $a_invoice["cac:LegalMonetaryTotal"] = self::getLegalMonetaryTotal($a_header);
        //Líneas
        $a_lines = [];
        //Recorremos líneas
        foreach ($a_detail as $a_item) {

            $b_free = in_array($a_item['igv_affection'], [CommercialMovementProduct::IGV_AFFECTION_FREE, CommercialMovementProduct::IGV_AFFECTION_GIFT]);
            //
            $a_line = [];
            //
            $a_line['cbc:ID'] = $a_item['item_number'];
            //
            $a_line['cbc:InvoicedQuantity'] = [];
            $a_line['cbc:InvoicedQuantity']['@attributes'] = [
                'unitCode' => $a_item['sunat_code']
            ];
            $a_line['cbc:InvoicedQuantity']['@value'] = $a_item['pres_quantity'];
            //
            $a_line['cbc:LineExtensionAmount'] = [];
            $a_line['cbc:LineExtensionAmount']['@attributes'] = [
                'currencyID' => $a_header['currency'],
            ];
            $a_line['cbc:LineExtensionAmount']['@value'] = $a_item['nnet'];
            //
            $a_line['cac:PricingReference'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount']['@attributes'] = [
                'currencyID' => $a_header['currency']
            ];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount']['@value'] = $b_free ? $a_item['rprice'] : $a_item['sprice'];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceTypeCode'] = $b_free ? SIANFECatalog::CATALOG_16_REFERENCIAL_VALUE : SIANFECatalog::CATALOG_16_UNITARY_PRICE;
            //
            $a_line['cac:TaxTotal'] = self::getTaxTotal($a_item, true);
            //Descripcion
            $a_line['cac:Item'] = [];
            $a_line['cac:Item']['cbc:Description'] = USString::truncateText($a_item['product_name'], 250);
            $a_line['cac:Item']['cac:SellersItemIdentification'] = [];
            $a_line['cac:Item']['cac:SellersItemIdentification']['cbc:ID'] = ($a_item['product_type'] === Product::TYPE_GROUP ? '-' : $a_item['product_id']);
            //Valor Unitario por Item
            $a_line['cac:Price']['cbc:PriceAmount'] = [];
            $a_line['cac:Price']['cbc:PriceAmount']['@attributes'] = [
                'currencyID' => $a_header['currency']
            ];
            $a_line['cac:Price']['cbc:PriceAmount']['@value'] = $a_item['vprice'];
            //Agregamos al array de líneas
            $a_lines[] = $a_line;
        }
        //Agregamos líneas
        $a_invoice['cac:InvoiceLine'] = $a_lines;
        //Features
        $o_xml = Array2XML::createXML('Invoice', $a_invoice);
        $s_xml = $o_xml->saveXML();
        return $s_xml;
    }

    public static function getCreditNoteXML($p_i_movement_id) {

        $a_header = self::getInvoiceHeader($p_i_movement_id);
        $a_detail = self::getInvoiceDetail($a_header);
        $a_fees = [];
        if ($a_header['no_data']) {
            $a_fees = self::getInvoiceFees($p_i_movement_id);
        }

        $s_type = SIANFE::TYPE_INVOICE;
        $s_document_name = $a_header["document_serie"] . "-" . $a_header["document_correlative"];

        $a_creditNote = [];
        //Seteamos espacios de nombres
        self::setNamespaces($a_creditNote, $s_type, $a_header['sunat_code']);
        //UBL Extensions
        $a_creditNote["ext:UBLExtensions"] = self::getUBLExtensions();
        //Información en común
        self::setCommonData($a_creditNote, $s_type, $a_header, $s_document_name);
        //Discrepancy
        $a_creditNote['cac:DiscrepancyResponse'] = self::getDiscrepancyResponse($a_header);
        //Reference
        $a_creditNote['cac:BillingReference'] = self::getBillingReference($a_header, $s_type);
        //Signature
        $a_creditNote['cac:Signature'] = self::getSignature();
        //Suplier
        $a_creditNote["cac:AccountingSupplierParty"] = self::getAccountingSupplierParty($s_type);
        //Customer
        $a_creditNote["cac:AccountingCustomerParty"] = self::getAccountingCustomerParty($s_type, $a_header);
        if ($a_header['no_data']) {
            //PaymentTerms
            $a_creditNote['cac:PaymentTerms'] = self::getPaymentTerms($a_header, $a_fees);
        }
        //Tax Total
        $a_creditNote['cac:TaxTotal'] = self::getTaxTotal($a_header);
        //Total
        $a_creditNote["cac:LegalMonetaryTotal"] = self::getLegalMonetaryTotal($a_header);

        //ITEMS
        $a_lines = [];
        foreach ($a_detail as $a_item) {

            $b_free = in_array($a_item['igv_affection'], [CommercialMovementProduct::IGV_AFFECTION_FREE, CommercialMovementProduct::IGV_AFFECTION_GIFT]);
            //
            $a_line = [];
            //
            $a_line['cbc:ID'] = $a_item['item_number'];
            $a_line['cbc:CreditedQuantity'] = [
                '@attributes' => [
                    'unitCode' => $a_item['sunat_code']
                ],
                '@value' => $a_item['pres_quantity']
            ];
            //Valor venta
            $a_line['cbc:LineExtensionAmount'] = [];
            $a_line['cbc:LineExtensionAmount']['@attributes'] = [
                'currencyID' => $a_header['currency'],
            ];
            $a_line['cbc:LineExtensionAmount']['@value'] = $a_header['no_data'] ? 0.00 : $a_item['nnet'];
            //Valores Unitarios
            $a_line['cac:PricingReference'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount']['@attributes'] = [
                'currencyID' => $a_header['currency']
            ];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount']['@value'] = $a_header['no_data'] ? 0.00 : ( $b_free ? $a_item['rprice'] : $a_item['sprice']);
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceTypeCode'] = $b_free ? SIANFECatalog::CATALOG_16_REFERENCIAL_VALUE : SIANFECatalog::CATALOG_16_UNITARY_PRICE;
            //
            $a_line['cac:TaxTotal'] = self::getTaxTotal($a_item, true);
            //Descripcion
            $a_line['cac:Item'] = [];
            $a_line['cac:Item']['cbc:Description'] = USString::truncateText($a_item['product_name'], 250);
            $a_line['cac:Item']['cac:SellersItemIdentification'] = [];
            $a_line['cac:Item']['cac:SellersItemIdentification']['cbc:ID'] = ($a_item['product_type'] === Product::TYPE_GROUP ? '-' : $a_item['product_id']);
            //Valor Unitario por Item
            $a_line['cac:Price']['cbc:PriceAmount'] = [];
            $a_line['cac:Price']['cbc:PriceAmount']['@attributes'] = [
                'currencyID' => $a_header['currency']
            ];
            $a_line['cac:Price']['cbc:PriceAmount']['@value'] = $a_header['no_data'] ? 0.00 : $a_item['vprice'];
            //
            $a_lines[] = $a_line;

            if ($a_header['no_data']) {
                break;
            }
        }
        //Agregamos líneas
        $a_creditNote['cac:CreditNoteLine'] = $a_lines;

        $o_xml = Array2XML::createXML('CreditNote', $a_creditNote);
        $s_xml = $o_xml->saveXML();
        return $s_xml;
    }

    public static function getDebitNoteXML($p_i_movement_id) {

        $a_header = self::getInvoiceHeader($p_i_movement_id);
        $a_detail = self::getInvoiceDetail($a_header);

        $s_type = SIANFE::TYPE_INVOICE;
        $s_document_name = $a_header["document_serie"] . "-" . $a_header["document_correlative"];

        $a_debitNote = [];
        //Seteamos espacios de nombres
        self::setNamespaces($a_debitNote, $s_type, $a_header['sunat_code']);
        //UBL Extensions
        $a_debitNote["ext:UBLExtensions"] = self::getUBLExtensions();
        //Información en común
        self::setCommonData($a_debitNote, $s_type, $a_header, $s_document_name);
        //Discrepancy
        $a_debitNote['cac:DiscrepancyResponse'] = self::getDiscrepancyResponse($a_header);
        //Reference
        $a_debitNote['cac:BillingReference'] = self::getBillingReference($a_header, $s_type);
        //Signature
        $a_debitNote['cac:Signature'] = self::getSignature();
        //Suplier
        $a_debitNote["cac:AccountingSupplierParty"] = self::getAccountingSupplierParty($s_type);
        //Customer
        $a_debitNote["cac:AccountingCustomerParty"] = self::getAccountingCustomerParty($s_type, $a_header);
        //PaymentTerms (no necesario en ND al parecer)
        //Tax Total
        $a_debitNote['cac:TaxTotal'] = self::getTaxTotal($a_header);
        //Total
        $a_debitNote["cac:RequestedMonetaryTotal"] = self::getRequestedMonetaryTotal($a_header);
        //Líneas
        $a_lines = [];
        foreach ($a_detail as $a_item) {

            $b_free = in_array($a_item['igv_affection'], [CommercialMovementProduct::IGV_AFFECTION_FREE, CommercialMovementProduct::IGV_AFFECTION_GIFT]);
            //
            $a_line = [];
            //
            $a_line['cbc:ID'] = $a_item['item_number'];
            $a_line['cbc:DebitedQuantity'] = [
                '@attributes' => [
                    'unitCode' => $a_item['sunat_code']
                ],
                '@value' => $a_item['pres_quantity']
            ];
            $a_line['cbc:LineExtensionAmount'] = [];
            $a_line['cbc:LineExtensionAmount']['@attributes'] = [
                'currencyID' => $a_header['currency'],
            ];
            $a_line['cbc:LineExtensionAmount']['@value'] = $a_item['nnet'];
            //Valores Unitarios
            $a_line['cac:PricingReference'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount']['@attributes'] = [
                'currencyID' => $a_header['currency']
            ];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount']['@value'] = $b_free ? $a_item['rprice'] : $a_item['sprice'];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceTypeCode'] = $b_free ? SIANFECatalog::CATALOG_16_REFERENCIAL_VALUE : SIANFECatalog::CATALOG_16_UNITARY_PRICE;
            //
            $a_line['cac:TaxTotal'] = self::getTaxTotal($a_item, true);
            //Descripcion
            $a_line['cac:Item'] = [];
            $a_line['cac:Item']['cbc:Description'] = USString::truncateText($a_item['product_name'], 250);
            $a_line['cac:Item']['cac:SellersItemIdentification'] = [];
            $a_line['cac:Item']['cac:SellersItemIdentification']['cbc:ID'] = ($a_item['product_type'] === Product::TYPE_GROUP ? '-' : $a_item['product_id']);
            //Valor Unitario por Item
            $a_line['cac:Price']['cbc:PriceAmount'] = [];
            $a_line['cac:Price']['cbc:PriceAmount']['@attributes'] = [
                'currencyID' => $a_header['currency']
            ];
            $a_line['cac:Price']['cbc:PriceAmount']['@value'] = $a_item['vprice'];
            //
            $a_lines[] = $a_line;
        }
        $a_debitNote['cac:DebitNoteLine'] = $a_lines;

        $o_xml = Array2XML::createXML('DebitNote', $a_debitNote);
        $s_xml = $o_xml->saveXML();
        return $s_xml;
    }

    public static function getSummaryXML(array $p_a_movement_ids, $p_s_document_name) {

        $a_headers = self::getSummaryHeaders($p_a_movement_ids);
        $a_first = $a_headers[0];
        $s_type = SIANFE::TYPE_SUMMARY;

        $a_summaryDocuments = [];
        //Seteamos namespaces
        self::setNamespaces($a_summaryDocuments, $s_type);
        //Obtenemos extensiones
        $a_summaryDocuments['ext:UBLExtensions'] = self::getUBLExtensions();
        //Data en común
        self::setCommonData($a_summaryDocuments, $s_type, $a_first, $p_s_document_name);
        //Signature
        $a_summaryDocuments['cac:Signature'] = self::getSignature();
        //Supplier
        $a_summaryDocuments['cac:AccountingSupplierParty'] = self::getAccountingSupplierParty($s_type);
        //Líneas
        $a_lines = [];
        foreach ($a_headers as $i => $a_header) {

            $a_line = [];
            $a_line['cbc:LineID'] = ($i + 1);
            $a_line['cbc:DocumentTypeCode'] = $a_header['sunat_code'];
            $a_line['cbc:ID'] = $a_header['document_serie'] . '-' . $a_header['document_correlative'];
            //Datos adquiriente
            $a_line['cac:AccountingCustomerParty'] = self::getAccountingCustomerParty($s_type, $a_header);
            //Referencia
            if (Document::isModifier($a_header['sunat_code'])) {
                $a_line['cac:BillingReference'] = self::getBillingReference($a_header, $s_type);
            }
            //Percepción
            if ($a_header['perception'] > 0) {
                $a_line['sac:SUNATPerceptionSummaryDocumentReference'] = [
                    'sac:SUNATPerceptionSystemCode' => SIANFECatalog::CATALOG_22_INTERNAL,
                    'sac:SUNATPerceptionPercent' => Yii::app()->controller->getOrganization()->globalVar->perception,
                    'cbc:TotalInvoiceAmount' => [
                        '@attributes' => [
                            'currencyID' => $a_header['currency']
                        ],
                        '@value' => $a_header['no_data'] ? 0.00 : $a_header['perception']
                    ],
                    'sac:SUNATTotalCashed' => [
                        '@attributes' => [
                            'currencyID' => $a_header['currency']
                        ],
                        '@value' => $a_header['no_data'] ? 0.00 : $a_header['real']
                    ],
                    'cbc:TaxableAmount' => [
                        '@attributes' => [
                            'currencyID' => $a_header['currency']
                        ],
                        '@value' => $a_header['no_data'] ? 0.00 : $a_header['total']
                    ],
                ];
            }
            //Estado
            $a_line['cac:Status'] = [
                'cbc:ConditionCode' => $a_header['status'] == 1 ? SIANFECatalog::CATALOG_19_ADD : SIANFECatalog::CATALOG_19_ANNUL
            ];
            //Total
            $a_line['sac:TotalAmount'] = [
                '@attributes' => [
                    'currencyID' => $a_header['currency']
                ],
                '@value' => $a_header['no_data'] ? 0.00 : $a_header['real']
            ];
            //Billing
            $a_line['sac:BillingPayment'] = self::getBillingPayment($a_header);

            //AllowanceCharge (No soportado)
//            $a_line['cac:AllowanceCharge'] = [
//                [
//                    'cbc:ChargeIndicator' => true,
//                    'cbc:Amount' => [
//                        '@attributes' => [
//                            'currencyID' => strtoupper($item['currency'])
//                        ],
//                        '@value' => 0.00//Monto
//                    ],
//                ],
//            ];
            //TRIBUTOS
            $a_line['cac:TaxTotal'] = self::getSummaryTaxTotal($a_header);

            $a_lines[] = $a_line;
        }
        //Agregamos líneas
        $a_summaryDocuments['sac:SummaryDocumentsLine'] = $a_lines;

        $o_xml = Array2XML::createXML('SummaryDocuments', $a_summaryDocuments);
        $s_xml = $o_xml->saveXML();
        return $s_xml;
    }

    public static function getSummaryLowXML(array $p_a_movement_ids, $p_s_document_name) {

        $a_headers = self::getSummaryLowHeaders($p_a_movement_ids);
        $a_first = $a_headers[0];
        $s_type = SIANFE::TYPE_SUMMARY_LOW;

        $a_voidedDocuments = [];

        self::setNamespaces($a_voidedDocuments, $s_type);
        //Obtenemos extensiones
        $a_voidedDocuments['ext:UBLExtensions'] = self::getUBLExtensions();
        //Data en común
        self::setCommonData($a_voidedDocuments, $s_type, $a_first, $p_s_document_name);
        //Signature
        $a_voidedDocuments['cac:Signature'] = self::getSignature();
        //Supplier
        $a_voidedDocuments['cac:AccountingSupplierParty'] = self::getAccountingSupplierParty($s_type);
        //Líneas
        $a_line = [];
        foreach ($a_headers as $i => $item) {
            $a_line = [];
            $a_line['cbc:LineID'] = $i + 1;
            $a_line['cbc:DocumentTypeCode'] = $item['sunat_code'];
            $a_line['sac:DocumentSerialID'] = $item['document_serie'];
            $a_line['sac:DocumentNumberID'] = $item['document_correlative'];

            if (!Util::isBlank($item['reason'])) {
                $a_line['sac:VoidReasonDescription'] = $item['reason'];
            } else {
                $a_line['sac:VoidReasonDescription'] = "Error al generar documento";
            }
            $a_voidedDocuments['sac:VoidedDocumentsLine'][] = $a_line;
        }
        $o_xml = Array2XML::createXML('VoidedDocuments', $a_voidedDocuments);
        $s_xml = $o_xml->saveXML();

        return $s_xml;
    }

    public static function getSelfBilledInvoiceXML($p_i_movement_id) {

        $a_header = self::getInvoiceHeader($p_i_movement_id);
        $a_detail = self::getInvoiceDetail($a_header);

        $s_type = SIANFE::TYPE_SELF_BILLED_INVOICE;
        $s_document_name = $a_header["document_serie"] . "-" . $a_header["document_correlative"];

        $a_selfBilledInvoice = [];
        //Seteamos espacios de nombres
        self::setNamespaces($a_selfBilledInvoice, $s_type, $a_header['sunat_code']);
        //UBL Extensions
        $a_selfBilledInvoice["ext:UBLExtensions"] = self::getUBLExtensions();
        //Información en común
        self::setCommonData($a_selfBilledInvoice, $s_type, $a_header, $s_document_name);
        //Signature
        $a_selfBilledInvoice['cac:Signature'] = self::getSignature();
        //Customer
        $a_selfBilledInvoice["cac:AccountingCustomerParty"] = self::getAccountingCustomerParty($s_type, $a_header);
        //Suplier
        $a_selfBilledInvoice["cac:AccountingSupplierParty"] = self::getAccountingSupplierParty($s_type, $a_header);
        //Crear Delivery Terms
        $a_selfBilledInvoice['cac:DeliveryTerms'] = self::getAddress($a_header);
        //Tax Total
        $a_selfBilledInvoice['cac:TaxTotal'] = self::getTaxTotal($a_header);
        //Total
        $a_selfBilledInvoice["cac:LegalMonetaryTotal"] = self::getLegalMonetaryTotal($a_header);
        //Líneas
        $a_lines = [];
        //Recorremos líneas
        foreach ($a_detail as $a_item) {

            $b_free = in_array($a_item['igv_affection'], [CommercialMovementProduct::IGV_AFFECTION_FREE, CommercialMovementProduct::IGV_AFFECTION_GIFT]);
            //
            $a_line = [];
            //
            $a_line['cbc:ID'] = $a_item['item_number'];
            //
            $a_line['cbc:InvoicedQuantity'] = [];
            $a_line['cbc:InvoicedQuantity']['@attributes'] = [
                'unitCode' => $a_item['sunat_code']
            ];
            $a_line['cbc:InvoicedQuantity']['@value'] = $a_item['pres_quantity'];
            //
            $a_line['cbc:LineExtensionAmount'] = [];
            $a_line['cbc:LineExtensionAmount']['@attributes'] = [
                'currencyID' => $a_header['currency'],
            ];
            $a_line['cbc:LineExtensionAmount']['@value'] = $a_item['nnet'];
            //
            $a_line['cac:PricingReference'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount'] = [];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount']['@attributes'] = [
                'currencyID' => $a_header['currency']
            ];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceAmount']['@value'] = $b_free ? $a_item['rprice'] : $a_item['sprice'];
            $a_line['cac:PricingReference']['cac:AlternativeConditionPrice']['cbc:PriceTypeCode'] = $b_free ? SIANFECatalog::CATALOG_16_REFERENCIAL_VALUE : SIANFECatalog::CATALOG_16_UNITARY_PRICE;
            //
            $a_line['cac:TaxTotal'] = self::getTaxTotal($a_item, true);
            //Descripcion
            $a_line['cac:Item'] = [];
            $a_line['cac:Item']['cbc:Description'] = USString::truncateText($a_item['product_name'], 250);
            $a_line['cac:Item']['cac:SellersItemIdentification'] = [];
            $a_line['cac:Item']['cac:SellersItemIdentification']['cbc:ID'] = ($a_item['product_type'] === Product::TYPE_GROUP ? '-' : $a_item['product_id']);
            //Valor Unitario por Item
            $a_line['cac:Price']['cbc:PriceAmount'] = [];
            $a_line['cac:Price']['cbc:PriceAmount']['@attributes'] = [
                'currencyID' => $a_header['currency']
            ];
            $a_line['cac:Price']['cbc:PriceAmount']['@value'] = $a_item['vprice'];
            //Agregamos al array de líneas
            $a_lines[] = $a_line;
        }
        //Agregamos líneas
        $a_selfBilledInvoice['cac:InvoiceLine'] = $a_lines;
        //Features
        $o_xml = Array2XML::createXML('SelfBilledInvoice', $a_selfBilledInvoice);
        $s_xml = $o_xml->saveXML();
        return $s_xml;
    }

    public static function getRemissionGuideXML($p_i_movement_id) {

        $o_model = Movement::model()->findByPk($p_i_movement_id);
        $a_header = self::getGuideHeader($p_i_movement_id, $o_model->type);
        $a_detail = self::getGuideDetail($a_header, $o_model->type);
        $a_shippers = self::getGuideShippers($p_i_movement_id, $o_model->type);
        $a_transport_units = self::getGuideTransportUnits($p_i_movement_id, $o_model->type);

        $s_type = SIANFE::TYPE_REMISSION_GUIDE;
        $s_document_name = $a_header["document_serie"] . "-" . intval($a_header["document_correlative"]);

        $a_remissionGuide = [];
        //Seteamos espacios de nombres
        self::setNamespaces($a_remissionGuide, $s_type, $a_header['sunat_code']);
        //UBL Extensions
        $a_remissionGuide["ext:UBLExtensions"] = self::getUBLExtensions();
        //Información en común
        self::setCommonData($a_remissionGuide, $s_type, $a_header, $s_document_name);
        //Documento Referencia
        if (in_array($a_header['reason_transfer_code'], Multitable::REASON_TRANSFER_SALE)) {
            $a_remissionGuide["cac:AdditionalDocumentReference"] = self::getAdditionalDocumentReference($s_type, $a_header);
        }
        //Signature
        $a_remissionGuide['cac:Signature'] = self::getSignature(SIANFE::TYPE_REMISSION_GUIDE, $a_header);
        //Suplier
        $a_remissionGuide["cac:DespatchSupplierParty"] = self::getAccountingSupplierParty($s_type, $a_header);
        //Customer
        $a_remissionGuide["cac:DeliveryCustomerParty"] = self::getAccountingCustomerParty($s_type, $a_header);
        //
        if (in_array($a_header['reason_transfer_code'], Multitable::REASON_TRANSFER_SALE)) {
            $a_remissionGuide["cac:BuyerCustomerParty"] = self::getAccountingBuyerParty($s_type, $a_header);
        }
        //Crear Shipment
        $a_remissionGuide['cac:Shipment'] = self::getShipment($a_header, $a_shippers, $a_transport_units);
        //Líneas
        $a_lines = [];
        //Recorremos líneas
        foreach ($a_detail as $a_item) {

            $a_line = [];
            $a_line['cbc:ID'] = $a_item['item_number'];
            $a_line['cbc:DeliveredQuantity'] = [];
            $a_line['cbc:DeliveredQuantity']['@attributes'] = [
                'unitCode' => $a_item['sunat_code']
            ];
            $a_line['cbc:DeliveredQuantity']['@value'] = $a_item['pres_quantity'];
            $a_line['cac:OrderLineReference'] = [];
            $a_line['cac:OrderLineReference']['cbc:LineID'] = $a_item['item_number'];

            //Descripcion
            $a_line['cac:Item'] = [];
            $a_line['cac:Item']['cbc:Description'] = USString::truncateText($a_item['product_name'] . ' ' . $a_item['description'], 250);
            $a_line['cac:Item']['cac:SellersItemIdentification'] = [];
            $a_line['cac:Item']['cac:SellersItemIdentification']['cbc:ID'] = ($a_item['product_type'] === Product::TYPE_GROUP ? '-' : $a_item['product_id']);
            //$a_line['cac:Item']['cac:CommodityClassification'] = [];
            //$a_line['cac:Item']['cac:CommodityClassification']['cbc:ItemClassificationCode'] = '';
            //Agregamos al array de líneas
            $a_lines[] = $a_line;
        }
        //Agregamos líneas
        $a_remissionGuide['cac:DespatchLine'] = $a_lines;
        //Features
        $o_xml = Array2XML::createXML('DespatchAdvice', $a_remissionGuide);
        $s_xml = $o_xml->saveXML();
        return $s_xml;
    }

    public static function saveXML($p_s_xml, $p_s_pathname) {
        file_put_contents($p_s_pathname, $p_s_xml);
    }

    /**
     * Firma y retorna el valor resumen
     * @param string $p_s_filepath Archivo
     * @param integer $ord Orden
     * @return string Valor resumen
     */
    public static function signXML($p_s_filepath, $ord = 0) {

        require_once('ebilling/AdapterInterface.php');
        require_once('ebilling/XmlseclibsAdapter.php');
        require_once('ebilling/XMLSecurityKey.php');
        require_once('ebilling/XMLSecurityDSig.php');
        $objSign = new \Adapter\XmlseclibsAdapter(self::SIGNATURE);
        $pfx = file_get_contents(SIANFE::getCertificatePath() . Yii::app()->params['e_billing_certificate']);

        libxml_use_internal_errors(true);

        $o_xml = new DOMDocument();
        $o_xml->load($p_s_filepath);

        openssl_pkcs12_read($pfx, $key, Yii::app()->params['e_billing_certificate_pass']);
        $objSign->setDigestAlgorithm();
        $objSign->setPrivateKey($key["pkey"]);
        $objSign->setPublickey($key["cert"]);
        $objSign->addTransform(\Adapter\XmlseclibsAdapter::ENVELOPED);
        $objSign->sign($o_xml, $o_xml->getElementsByTagName('ExtensionContent')->item($ord));
        $o_xml->save($p_s_filepath);

        return $o_xml->getElementsByTagName('DigestValue')->item(0)->nodeValue;
    }

    /**
     * Obtiene el valor resumen de un documento generado
     * @param string $p_s_digest_value Valor resumen a analizar
     * @param integer $p_i_movement_id ID del movimiento
     * @param string $p_s_sunat_code Código de SUNAT del documento
     * @param string $p_s_path Ruta del docuemento XML
     * @param string $p_s_basename Nombre del archivo
     * @return string Valor resumen obtenido
     */
    public static function getDigestValue($p_s_digest_value, $p_i_movement_id, $p_s_sunat_code, $p_s_path, $p_s_basename) {
        //Revisamos si ya tiene el valor resumen generado
        if (!isset($p_s_digest_value)) {
            //Si no existe, creamos el formato XML 
            if (!file_exists($p_s_path . $p_s_basename . SIANFE::XML_EXTENSION)) {

                $s_cleanname = SIANFE::unsetRUC($p_s_basename);

                switch ($p_s_sunat_code) {
                    case Document::FACTURA:
                        $s_xml = self::getInvoiceXML($p_i_movement_id);
                        break;
                    case Document::BOLETA:
                        $s_xml = self::getSummaryXML([$p_i_movement_id], $s_cleanname);
                        break;
                    case Document::CREDIT_NOTE:
                    case Document::SPECIAL_CREDIT_NOTE:
                    case Document::NOT_DOMICILED_CREDIT_NOTE:
                        $s_xml = self::getCreditNoteXML($p_i_movement_id);
                        break;
                    case Document::DEBIT_NOTE:
                    case Document::SPECIAL_DEBIT_NOTE:
                    case Document::NOT_DOMICILED_DEBIT_NOTE:
                        $s_xml = self::getDebitNoteXML($p_i_movement_id);
                        break;
                    case Document::REMISSION_GUIDE:
                        $s_xml = self ::getRemissionGuideXML($p_i_movement_id);
                        break;
                    default :
                        $s_xml = '';
                }
                //Guardamos XML
                self::saveXML($s_xml, $p_s_path . $p_s_basename . SIANFE::XML_EXTENSION);
                //Firmamos
                $p_s_digest_value = self::signXML($p_s_path . $p_s_basename . SIANFE::XML_EXTENSION);
            } else {
                //Cargamos el XML
                $o_xml = new DOMDocument();
                $o_xml->load($p_s_path . $p_s_basename . SIANFE::XML_EXTENSION);
                $p_s_digest_value = $o_xml->getElementsByTagName('DigestValue')->item(0)->nodeValue;
            }

            //Guardamos valor resumen
            Movement::saveDigestValue($p_s_digest_value, [$p_i_movement_id]);

            return $p_s_digest_value;
        } else {
            return $p_s_digest_value;
        }
    }

}
