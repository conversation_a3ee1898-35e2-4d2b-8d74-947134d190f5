<?php

class USMap extends CWidget {

    public $id;
    public $title;
    public $lat = 0;
    public $lng = 0;
    public $zoom = 15;
    //Private
    private $controller;

    /**
     * Initializes the widget.
     */
    public function init() {
        $this->controller = Yii::app()->controller;
        //ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id, "

        var element = $('#{$this->id}');
        var location = new google.maps.LatLng({$this->lat}, {$this->lng});

        var mapOptions = {
            center: location,
            zoom: {$this->zoom},
            mapTypeId: google.maps.MapTypeId.ROADMAP
        };

        var map = new google.maps.Map(document.getElementById('{$this->id}'), mapOptions);
        var marker = new google.maps.Marker({
            map: map,
            title: '{$this->title}',
            position: location
        });

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}' class='us-map-container'></div>";
    }

}
