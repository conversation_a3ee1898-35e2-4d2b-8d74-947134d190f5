<?php

/**
 * No necesita scripts para cambios de moneda o de tipo de cambio
 */
class SIANLoan extends CWidget {

    public $id;
    public $form;
    public $model;
    public $readonly = false;
    public $fee_type_items;
    public $frequency_items;
    //CONTROL
    public $tfixed = 2;
    public $pfixed = 3;
    public $ifixed = 6;
    //IDS
    public $require_cashbox_input_id;
    public $cashbox_input_id;
    public $fee_type_input_id;
    public $fee_count_input_id;
    public $fee_frequency_input_id;
    public $amount_input_id;
    public $require_interest_input_id;
    //
    public $desgravamen_input_id;
    public $porte_input_id;
    public $itf_input_id;
    //
    public $tea_input_id;
    public $te_input_id;
    public $tcea_input_id;
    //PRIVATE
    private $controller;
    private $banks;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        //PUBLIC IDS
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->require_cashbox_input_id = isset($this->require_cashbox_input_id) ? $this->require_cashbox_input_id : $this->controller->getServerId();
        $this->cashbox_input_id = isset($this->cashbox_input_id) ? $this->cashbox_input_id : $this->controller->getServerId();
        $this->fee_type_input_id = isset($this->fee_type_input_id) ? $this->fee_type_input_id : $this->controller->getServerId();
        $this->fee_count_input_id = isset($this->fee_count_input_id) ? $this->fee_count_input_id : $this->controller->getServerId();
        $this->fee_frequency_input_id = isset($this->fee_frequency_input_id) ? $this->fee_frequency_input_id : $this->controller->getServerId();
        $this->amount_input_id = isset($this->amount_input_id) ? $this->amount_input_id : $this->controller->getServerId();
        $this->desgravamen_input_id = isset($this->desgravamen_input_id) ? $this->desgravamen_input_id : $this->controller->getServerId();
        $this->porte_input_id = isset($this->porte_input_id) ? $this->porte_input_id : $this->controller->getServerId();
        $this->itf_input_id = isset($this->itf_input_id) ? $this->itf_input_id : $this->controller->getServerId();
        $this->tea_input_id = isset($this->tea_input_id) ? $this->tea_input_id : $this->controller->getServerId();
        $this->te_input_id = isset($this->te_input_id) ? $this->te_input_id : $this->controller->getServerId();
        $this->tcea_input_id = isset($this->tcea_input_id) ? $this->tcea_input_id : $this->controller->getServerId();
        $this->require_interest_input_id = isset($this->require_interest_input_id) ? $this->require_interest_input_id : $this->controller->getServerId();
        //
        $this->banks = $this->controller->checkRoute('banks');
        //
        SIANAssets::registerScriptFile("other/jquery.datetimepicker/js/jquery.datetimepicker.js");
        SIANAssets::registerCssFile("other/jquery.datetimepicker/css/jquery.datetimepicker.css");
        SIANAssets::registerScriptFile('other/moment/moment.js');
        SIANAssets::registerScriptFile('other/excelFormulas/ExcelFormulas.js');
        SIANAssets::registerScriptFile('js/sian-loan.js');

        //ITEMS
        $fees = [];
        foreach ($this->model->tempItems as $fee) {
            $attributes = [];
            array_push($attributes, "expiration_date:'{$fee->expiration_date}'");
            array_push($attributes, "debt:{$fee->getValue('debt', $this->model->movement->currency)}");
            array_push($attributes, "factor:{$fee->getValue('factor', $this->model->movement->currency)}");
            array_push($attributes, "amortization:{$fee->getValue('amortization', $this->model->movement->currency, $this->pfixed)}");
            array_push($attributes, "interest:{$fee->getValue('interest', $this->model->movement->currency, $this->pfixed)}");
            array_push($attributes, "desgravamen:{$fee->getValue('desgravamen', $this->model->movement->currency)}");
            array_push($attributes, "oae_adm:{$fee->getValue('oae_adm', $this->model->movement->currency)}");
            array_push($attributes, "oae_financial:{$fee->getValue('oae_financial', $this->model->movement->currency)}");
            array_push($attributes, "oae_sales:{$fee->getValue('oae_sales', $this->model->movement->currency)}");
            array_push($attributes, "porte:{$fee->getValue('porte', $this->model->movement->currency)}");
            array_push($attributes, "financing:{$fee->getValue('financing', $this->model->movement->currency)}");
            array_push($attributes, "itf:{$fee->getValue('itf', $this->model->movement->currency)}");
            array_push($attributes, "fee:{$fee->getValue('fee', $this->model->movement->currency)}");
            array_push($attributes, "errors:" . json_encode($fee->getErrors()));
            array_push($fees, '{' . implode(',', $attributes) . "}");
        }
        $fees = '[' . implode(',', $fees) . ']';

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        
        var fees = {$fees};
        var readonly = " . json_encode($this->readonly) . ";

        var store_id = {$this->model->movement->store_id};
        var direction = '{$this->model->movement->scenario->direction}';
        var type = '" . Cashbox::ALL_TYPES . "';
        var banks = " . json_encode($this->banks) . ";
        var currency = '{$this->model->movement->currency}';
        var url = '{$this->controller->createUrl("/movement/getCashboxes")}';
        var cashbox_id = '{$this->model->cashbox_id}';

        SIANLoanInit('{$this->id}', store_id, cashbox_id, direction, type, banks, currency, url);
        //
        var tcea = SIANLoanReset('{$this->id}', '{$this->model->fee_type}', {$this->model->fee_count}, {$this->model->fee_frequency}, {$this->model->getValue('amortization')}, {$this->model->desgravamen_percent}, {$this->model->getValue('pporte')}, {$this->model->itf_percent}, {$this->model->te_percent}, {$this->ifixed}, {$this->pfixed}, {$this->tfixed}, '" . Yii::app()->params['date_php_format'] . "', fees, readonly);
        $('#{$this->te_input_id}').data('value', {$this->model->te_percent}).floatVal({$this->ifixed}, {$this->model->te_percent});
        $('#{$this->tcea_input_id}').floatVal({$this->pfixed}, tcea);
        $('#{$this->id}').data('available-fields', {});

        $('#{$this->id}').data('changeOperation', function(changeData){
            //Require
            var require_interest = false;
            var require_desgravamen = false;
            var require_porte = false;
            var require_itf = false;
            var objFields = {}; 
            
            $.each(changeData.dynamics, function( index, value ) {   
            
                if(value['field'] == 'interest'){
                    require_interest =  true;
                }                
                if(value['field'] == 'desgravamen'){
                    require_desgravamen =  true;
                }
                if(value['field'] == 'porte'){
                    require_porte =  true;
                }
                if(value['field'] == 'itf'){
                    require_itf =  true;
                }
                objFields[value['field']] = value['field'];
            });
            $('#{$this->id}').data('available-fields', JSON.stringify(objFields));
            SIANLoanColumns('{$this->id}');
                
            $('#{$this->desgravamen_input_id}').makeRequired(require_desgravamen);
            $('#{$this->porte_input_id}').makeRequired(require_porte);
            $('#{$this->itf_input_id}').makeRequired(require_itf);
            $('#{$this->desgravamen_input_id}').removeAttr('readonly');
            $('#{$this->porte_input_id}').removeAttr('readonly');
            $('#{$this->itf_input_id}').removeAttr('readonly');
            if(require_desgravamen == false){
                $('#{$this->desgravamen_input_id}').val(0.000);
                $('#{$this->desgravamen_input_id}').attr('readonly', true);
            }
            if(require_porte == false){
                $('#{$this->porte_input_id}').val(0.00);
                $('#{$this->porte_input_id}').attr('readonly', true);
            }
            if(require_itf == false){
                $('#{$this->itf_input_id}').val(0.000);
                $('#{$this->itf_input_id}').attr('readonly', true);
            }
            {$this->id}SIANLoanCalculate();
            
            $('#{$this->require_interest_input_id}').val(require_interest? 1: 0);
            $('#{$this->tea_input_id}').makeRequired(require_interest);
            $('#{$this->te_input_id}').makeRequired(require_interest);
            $('#{$this->tcea_input_id}').makeRequired(require_interest);
                        
            $('#{$this->require_cashbox_input_id}').val(changeData.has_cashbox? 1: 0);
            //Seteamos require
            $('#{$this->id}').data('require_cashbox', changeData.has_cashbox);
            //Si no requiere
            var cashboxInput = $('#{$this->cashbox_input_id}');
            if(changeData.has_cashbox)
            {
                var cashbox_id = $('#{$this->id}').data('cashbox_id');
            }
            else
            {
                cashboxInput.val(null);
                var cashbox_id = null;
            }
            SIANLoanUpdateCashboxes('{$this->id}', changeData.has_cashbox, cashboxInput, cashbox_id);
        }); 
        
        $('#{$this->id}').data('changeStore', function(changeData){
            $('#{$this->id}').data('store_id', changeData.store_id);
            var require_cashbox = $('#{$this->id}').data('require_cashbox');
            var cashboxInput = $('#{$this->cashbox_input_id}');
            var cashbox_id = $('#{$this->id}').data('cashbox_id');

            SIANLoanUpdateCashboxes('{$this->id}', require_cashbox, cashboxInput, cashbox_id);    
        }); 
        
        $('#{$this->id}').data('changeCurrency', function(currency){
            $('#{$this->id}').data('currency', currency);
            var require_cashbox = $('#{$this->id}').data('require_cashbox');
            var cashboxInput = $('#{$this->cashbox_input_id}');
            var cashbox_id = $('#{$this->id}').data('cashbox_id');

            SIANLoanUpdateCashboxes('{$this->id}', require_cashbox, cashboxInput, cashbox_id);      
        }); 
        
        $('body').on('change', '#{$this->cashbox_input_id}', function(e) {
            $('#{$this->id}').data('cashbox_id', cashbox_id);
        }); 
        
        function {$this->id}SIANLoanCalculate(){
            var fee_type = $('#{$this->fee_type_input_id}').val();
            var fee_frequency = $('#{$this->fee_frequency_input_id}').integer();
            var amount =  $('#{$this->amount_input_id}').floatVal({$this->tfixed});
            var desgravamen = $('#{$this->desgravamen_input_id}').floatVal({$this->pfixed});
            var porte = $('#{$this->porte_input_id}').floatVal({$this->tfixed});
            var itf = $('#{$this->itf_input_id}').floatVal({$this->pfixed});
            var te = $('#{$this->te_input_id}').data('value');
            var tcea = SIANLoanUpdate('{$this->id}', fee_type, fee_frequency, amount, desgravamen, porte, itf, te);
            $('#{$this->tcea_input_id}').floatVal({$this->pfixed}, tcea);
        }

        $('body').on('change', '#{$this->fee_count_input_id}', function(e) {
            var fee_type = $('#{$this->fee_type_input_id}').val();
            var fee_count = $('#{$this->fee_count_input_id}').integer();
            var fee_frequency = $('#{$this->fee_frequency_input_id}').integer();
            var amount =  $('#{$this->amount_input_id}').floatVal({$this->tfixed});
            var desgravamen = $('#{$this->desgravamen_input_id}').floatVal({$this->pfixed});
            var porte = $('#{$this->porte_input_id}').floatVal({$this->tfixed});
            var itf = $('#{$this->itf_input_id}').floatVal({$this->pfixed});
            var te = $('#{$this->te_input_id}').data('value');
            var readonly = " . json_encode($this->readonly) . ";
            var tcea = SIANLoanReset('{$this->id}', fee_type, fee_count, fee_frequency, amount, desgravamen, porte, itf, te, {$this->ifixed}, {$this->pfixed}, {$this->tfixed}, '" . Yii::app()->params['date_php_format'] . "', [], readonly);
            $('#{$this->tcea_input_id}').floatVal({$this->pfixed}, tcea);
        }); 
        
        $('body').on('change', '#{$this->fee_type_input_id}, #{$this->amount_input_id}, #{$this->desgravamen_input_id}, #{$this->porte_input_id}, #{$this->itf_input_id}, #{$this->te_input_id}', function(e) {
            {$this->id}SIANLoanCalculate();
        }); 
        
        $('body').on('change', 'input.sian-loan-item-amortization, input.sian-loan-item-interest, input.sian-loan-item-desgravamen, input.sian-loan-item-oae-adm, input.sian-loan-item-oae-financial, input.sian-loan-item-oae-sales, input.sian-loan-item-porte', function(e) {
            var fee_type = $('#{$this->fee_type_input_id}').val();
            var fee_frequency = $('#{$this->fee_frequency_input_id}').integer();
            var amount =  $('#{$this->amount_input_id}').floatVal({$this->tfixed});
            var desgravamen = $('#{$this->desgravamen_input_id}').floatVal({$this->pfixed});
            var porte = $('#{$this->porte_input_id}').floatVal({$this->tfixed});
            var itf = $('#{$this->itf_input_id}').floatVal({$this->pfixed});
            var te = $('#{$this->te_input_id}').data('value');
            var tcea = SIANLoanUpdate('{$this->id}', fee_type, fee_frequency, amount, desgravamen, porte, itf, te);
            $('#{$this->tcea_input_id}').floatVal({$this->pfixed}, tcea);
        }); 

        $('body').on('change', '#{$this->tea_input_id}, #{$this->fee_frequency_input_id}', function(e) {
            
            var tea = $('#{$this->tea_input_id}').floatVal({$this->tfixed});
            var frequency = $('#{$this->fee_frequency_input_id}').integer();
            var label = $('#{$this->fee_frequency_input_id} option:selected').text();
                
            var te = SIANLoanGetTE(tea, frequency);
            $('span.fee-frequency').text(label);
            $('#{$this->te_input_id}').floatVal({$this->ifixed}, te).data('value', te).change();
        }); 
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->renderPanel1();
        echo "</div>";
        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        $this->renderPanel2();
        echo "</div>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        $this->renderPanel3();
        echo "</div>";
        echo "</div>";

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->model->getAttributeLabel('fees'),
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => 'sian-loan ' . ($this->model->hasErrors('tempFees') ? 'us-error' : '')
            )
        ));
        echo CHtml::tag('table', array(
            'id' => $this->id,
            'class' => 'table table-condensed table-hover sian-loan-table',
            'data-readonly' => $this->readonly,
                ), $this->renderTable(), true);

        echo "<hr>";
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->textAreaRow($this->model->movement, 'observation', array(
            'rows' => 3,
            'maxlength' => 500,
            'required' => $this->model->movement->isObservationRequired()
        ));
        echo "</div>";
        echo "</div>";
        $this->endWidget();
    }

    private function renderPanel1() {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "Detalles de la operación financiera",
            'headerIcon' => 'list',
            'htmlOptions' => array(
            )
        ));
        //Init row
        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
         echo $this->form->textFieldRow($this->model, "loan_number", array(
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->hiddenField($this->model, 'require_cashbox', [
            'id' => $this->require_cashbox_input_id,
        ]);
        echo $this->form->dropDownListRow($this->model, 'cashbox_id', [], array(
            'id' => $this->cashbox_input_id,
            'label' => "{$this->model->getAttributeLabel('cashbox_id')} ({$this->model->movement->scenario->direction})",
            'empty' => Strings::SELECT_OPTION,
            'readonly' => $this->readonly,
            'required' => $this->model->require_cashbox,
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, 'fee_count', array(
            'id' => $this->fee_count_input_id,
            'min' => 1,
            'step' => 1,
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'fee_type', $this->fee_type_items, array(
            'id' => $this->fee_type_input_id,
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'fee_frequency', $this->frequency_items, array(
            'id' => $this->fee_frequency_input_id,
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "amount", array(
            'id' => $this->amount_input_id,
            'label' => "Monto",
            'value' => $this->model->getValue('amortization'),
            'min' => 0,
        ));
        echo "</div>";
        //end row
        echo "</div>";
        $this->endWidget();
    }

    private function renderPanel2() {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "Comisiones y gastos (x periodo)",
            'headerIcon' => 'list',
            'htmlOptions' => array(
            )
        ));
        //Init row
        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, 'desgravamen_percent', array(
            'id' => $this->desgravamen_input_id,
            'min' => 0,
            'step' => USMath::toStep($this->pfixed),
            'append' => '<b>%</b>',
            'class' => 'us-double3'
        ));
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "pporte", array(
            'id' => $this->porte_input_id,
            'label' => $this->model->getAttributeLabel("pporte_{$this->model->movement->currency}"),
            'value' => $this->model->getValue('pporte'),
            'min' => 0,
            'step' => USMath::toStep($this->pfixed),
            'class' => 'us-double2'
        ));
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, 'itf_percent', array(
            'id' => $this->itf_input_id,
            'min' => 0,
            'step' => USMath::toStep($this->pfixed),
            'append' => '<b>%</b>',
            'class' => 'us-double3'
        ));
        echo "</div>";
        //end row
        echo "</div>";
        $this->endWidget();
    }

    private function renderPanel3() {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "Detalles del Financiamiento",
            'headerIcon' => 'list',
            'htmlOptions' => array(
            )
        ));
        //Init row
        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->form->hiddenField($this->model, 'require_interest', [
            'id' => $this->require_interest_input_id,
        ]);
        echo $this->form->numberFieldRow($this->model, 'tea_percent', array(
            'id' => $this->tea_input_id,
            'min' => 0,
            'step' => USMath::toStep($this->pfixed),
            'append' => '<b>%</b>',
            'class' => 'us-double2'
        ));
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, 'te_percent', array(
            'id' => $this->te_input_id,
            'label' => $this->model->getAttributeLabel('te_percent') . " <span class='fee-frequency'>" . $this->model->getFrequencyLabel() . "</span>",
            'min' => 0,
            'step' => USMath::toStep($this->pfixed),
            'readonly' => true,
            'append' => '<b>%</b>',
        ));
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, 'tcea_percent', array(
            'id' => $this->tcea_input_id,
            'min' => 0,
            'step' => USMath::toStep($this->pfixed),
            'readonly' => true,
            'append' => '<b>%</b>',
        ));
        echo "</div>";
        //end row
        echo "</div>";
        $this->endWidget();
    }

    private function renderTable() {
        $html = '';
        $html .= "<thead>";
        $html .= "<tr>";
        $html .= "<th width='6%' class='sian-loan-fee-number'>{$this->model->getAttributeLabel('fees.fee_number')}</th>";
        $html .= "<th width='6%' class='sian-loan-expiration'>{$this->model->getAttributeLabel('fees.expiration_date')}</th>";
        $html .= "<th width='6%' class='sian-loan-debt' style='text-align:right;'>{$this->model->getAttributeLabel("fees.debt_{$this->model->movement->currency}")}</th>";
        $html .= "<th width='6%' class='sian-loan-factor' style='text-align:right;'>{$this->model->getAttributeLabel("fees.factor_{$this->model->movement->currency}")}</th>";
        $html .= "<th width='8%' class='sian-loan-dynamic sian-loan-amortization' style='text-align:right;'>{$this->model->getAttributeLabel("fees.amortization_{$this->model->movement->currency}")}</th>";
        $html .= "<th width='8%' class='sian-loan-dynamic sian-loan-interest' style='text-align:right;'>{$this->model->getAttributeLabel("fees.interest_{$this->model->movement->currency}")}</th>";
        $html .= "<th width='8%' class='sian-loan-dynamic sian-loan-desgravamen' style='text-align:right;'>{$this->model->getAttributeLabel("fees.desgravamen_{$this->model->movement->currency}")}</th>";
        $html .= "<th width='8%' class='sian-loan-dynamic sian-loan-oae_adm' style='text-align:right;'>{$this->model->getAttributeLabel("fees.oae_adm_{$this->model->movement->currency}")}</th>";
        $html .= "<th width='8%' class='sian-loan-dynamic sian-loan-oae_financial' style='text-align:right;'>{$this->model->getAttributeLabel("fees.oae_financial_{$this->model->movement->currency}")}</th>";
        $html .= "<th width='8%' class='sian-loan-dynamic sian-loan-oae_sales' style='text-align:right;'>{$this->model->getAttributeLabel("fees.oae_sales_{$this->model->movement->currency}")}</th>";
        $html .= "<th width='8%' class='sian-loan-dynamic sian-loan-porte' style='text-align:right;'>{$this->model->getAttributeLabel("fees.porte_{$this->model->movement->currency}")}</th>";
        $html .= "<th width='6%' class='sian-loan-financing' style='text-align:right;'>{$this->model->getAttributeLabel("fees.financing_{$this->model->movement->currency}")}</th>";
        $html .= "<th width='8%' class='sian-loan-dynamic sian-loan-itf' style='text-align:right;'>{$this->model->getAttributeLabel("fees.itf_{$this->model->movement->currency}")}</th>";
        $html .= "<th width='6%' class='sian-loan-fee' style='text-align:right;'>{$this->model->getAttributeLabel("fees.fee_{$this->model->movement->currency}")}</th>";
        $html .= "</tr>";
        $html .= "</thead>";
        $html .= "<tbody></tbody>";
        $html .= "<tfoot>";
        $html .= "<tr class='sian-loan-dynamic sian-loan-amortization'>";
        $html .= "<td colspan='4' class='sian-loan-footer' style='text-align:right;'><b>{$this->model->getAttributeLabel("amortization_{$this->model->movement->currency}")}</b></td>";
        $html .= "<td class='sian-loan-total-amortization' style='text-align:right;'></td>";
        $html .= "</tr>";
        $html .= "<tr class='sian-loan-dynamic sian-loan-interest'>";
        $html .= "<td colspan='4' class='sian-loan-footer' style='text-align:right;'><b>{$this->model->getAttributeLabel("interest_{$this->model->movement->currency}")}</b></td>";
        $html .= "<td class='sian-loan-total-interest' style='text-align:right;'></td>";
        $html .= "</tr>";
        $html .= "<tr class='sian-loan-dynamic sian-loan-desgravamen'>";
        $html .= "<td colspan='4' class='sian-loan-footer' style='text-align:right;'><b>{$this->model->getAttributeLabel("desgravamen_{$this->model->movement->currency}")}</b></td>";
        $html .= "<td class='sian-loan-total-desgravamen' style='text-align:right;'></td>";
        $html .= "</tr>";
        $html .= "<tr class='sian-loan-dynamic sian-loan-oae_adm'>";
        $html .= "<td colspan='4' class='sian-loan-footer' style='text-align:right;'><b>{$this->model->getAttributeLabel("oae_adm_{$this->model->movement->currency}")}</b></td>";
        $html .= "<td class='sian-loan-total-oae-adm' style='text-align:right;'></td>";
        $html .= "</tr>";
        $html .= "<tr class='sian-loan-dynamic sian-loan-oae_financial'>";
        $html .= "<td colspan='4' class='sian-loan-footer' style='text-align:right;'><b>{$this->model->getAttributeLabel("oae_financial_{$this->model->movement->currency}")}</b></td>";
        $html .= "<td class='sian-loan-total-oae-financial' style='text-align:right;'></td>";
        $html .= "</tr>";
        $html .= "<tr class='sian-loan-dynamic sian-loan-oae_sales'>";
        $html .= "<td colspan='4' class='sian-loan-footer' style='text-align:right;'><b>{$this->model->getAttributeLabel("oae_sales_{$this->model->movement->currency}")}</b></td>";
        $html .= "<td class='sian-loan-total-oae-sales' style='text-align:right;'></td>";
        $html .= "</tr>";
        $html .= "<tr class='sian-loan-dynamic sian-loan-porte'>";
        $html .= "<td colspan='4' class='sian-loan-footer' style='text-align:right;'><b>{$this->model->getAttributeLabel("porte_{$this->model->movement->currency}")}</b></td>";
        $html .= "<td class='sian-loan-total-porte' style='text-align:right;'></td>";
        $html .= "</tr>";
        $html .= "<tr class='sian-loan-dynamic sian-loan-itf'>";
        $html .= "<td colspan='4' class='sian-loan-footer' style='text-align:right;'><b>{$this->model->getAttributeLabel("itf_{$this->model->movement->currency}")}</b></td>";
        $html .= "<td class='sian-loan-total-itf' style='text-align:right;'></td>";
        $html .= "</tr>";
        $html .= "<tr class='sian-loan-real'>";
        $html .= "<td colspan='4' class='sian-loan-footer' style='border-top: 0px; text-align:right;'><b>{$this->model->getAttributeLabel("real_{$this->model->movement->currency}")}</b></td>";
        $html .= "<td class='sian-loan-total-fee' style='text-align:right; border-top: 0px;'></td>";
        $html .= "</tr>";
        $html .= "</tfoot>";
        return $html;
    }

}
