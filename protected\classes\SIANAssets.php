<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANAssets
 *
 * <AUTHOR>
 */
class SIANAssets {

    const REVISION = '20250226';

    public static function getModulesUrl() {
        return Yii::app()->assetManager->publish(Yii::getPathOfAlias('application.modules'));
    }

    public static function getBaseUrl() {
        return Yii::app()->assetManager->publish(Yii::getPathOfAlias('application.widgets.assets'));
    }

    public static function registerCssFile($path) {
        $baseUrl = self::getBaseUrl();
        Yii::app()->clientScript->registerCssFile("{$baseUrl}/{$path}?" . self::REVISION);
    }

    public static function registerScriptFile($path, $pos = CClientScript::POS_END) {
        $baseUrl = self::getBaseUrl();
        Yii::app()->clientScript->registerScriptFile("{$baseUrl}/{$path}?" . self::REVISION, $pos);
    }

    public static function registerModuleCssFile($file, $module, $controller) {
        $modulesUrl = self::getModulesUrl();
        Yii::app()->clientScript->registerCssFile("{$modulesUrl}/{$module}/views/{$controller}/css/{$file}?" . self::REVISION);
    }

    public static function registerModuleScriptFile($file, $module, $controller, $pos = CClientScript::POS_END) {
        $modulesUrl = self::getModulesUrl();
        Yii::app()->clientScript->registerScriptFile("{$modulesUrl}/{$module}/views/{$controller}/js/{$file}?" . self::REVISION, $pos);
    }

    public static function registerCoreCss() {

        //US CSS
        self::registerCssFile("us/css/default.css");
        self::registerCssFile("us/css/issues.css");
        //SIAN CSS
        self::registerCssFile("css/styles.css");

        if (Yii::app()->params['environment'] !== YII_ENVIRONMENT_PRODUCTION) {
            $environment = '';
            switch (Yii::app()->params['environment']) {
                case YII_ENVIRONMENT_STAGING:
                    $environment = 'staging';
                    break;
                case YII_ENVIRONMENT_TESTING:
                    $environment = 'testing';
                    break;
                case YII_ENVIRONMENT_DEVELOPMENT:
                    $environment = 'development';
                    break;
            }

            Yii::app()->clientScript->registerCss("SIANNavbar", "
            //body {
                //background-image: url(" . Yii::app()->params['admin_url'] . "/images/environment/{$environment}-90.png), url(" . Yii::app()->params['admin_url'] . "/images/environment/{$environment}+90.png);
                //background-repeat: repeat-y, repeat-y;
                //background-attachment: fixed, fixed;
                //background-position: left bottom, right bottom; 
            //}
            #development .img-1 {
                background-image: url(" . Yii::app()->params['admin_url'] . "/images/environment/{$environment}-90.png);
                background-repeat: no-repeat;
                background-repeat: repeat-y, repeat-y;
            }
            #development .img-2 {
                background-image: url(" . Yii::app()->params['admin_url'] . "/images/environment/{$environment}+90.png);
                background-repeat: no-repeat;
                background-repeat: repeat-y, repeat-y;
            }
        ");
        }
    }

    public static function registerCoreJs() {
        //DEPENDENCES
        self::registerScriptFile("other/jquery.yiigridview/jquery.yiigridview.js");
        self::registerScriptFile("other/jquery-ui/jquery-ui.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("other/bignumber/bignumber.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("other/dateformat/dateformat.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("other/jquery.cookie/jquery.cookie.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("other/lazyload/lazyload.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("other/waitingfor/waitingfor.js", CClientScript::POS_BEGIN);
        //US JS
        self::registerScriptFile("us/js/stack.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/math.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/ui.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/default.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/events.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/expressions.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/functions.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/us-functions.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/attributes.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/modal.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/link.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/explore.js", CClientScript::POS_BEGIN);
        self::registerScriptFile("us/js/social.js", CClientScript::POS_BEGIN);
        //Global functions JS
        self::registerScriptFile("js/global-functions.js", CClientScript::POS_BEGIN);

        //SCRIPTS
        Yii::app()->clientScript->registerScript('us_scripts', "
            
        jQuery.ajaxSetup({ cache: true });
            
        $(function() {
            $(document).tooltip({
                  hide: false
            });
            $(document).data('admin_url', '" . Yii::app()->params['admin_url'] . "');
        });

        function USCurrencyGetSymbol(currency)
        {
            switch(currency)
            {
                case '" . Currency::PEN . "':
                    return '" . Currency::getSymbol(Currency::PEN) . "';
                case '" . Currency::USD . "':
                    return '" . Currency::getSymbol(Currency::USD) . "';
                default:
                    bootbox.alert(us_message('No es un tipo de moneda válido', 'error'));
                break;
            }
        }
        
        function grand(h) {
           var iframe = $('iframe.us-frame');
           iframe.height(h);
        }
        
        function resizeFrame()
        {
            top.grand(document.body.scrollHeight);
        }
        
        //Currency
        const CURRENCY_PEN = '" . Currency::PEN . "';
        const CURRENCY_USD = '" . Currency::USD . "';
        //Payment Method
        const CASHBOX_MOVEMENT_PAYMENT_METHOD_CASH = '" . CashboxMovement::PAYMENT_METHOD_CASH . "';
        const CASHBOX_MOVEMENT_PAYMENT_METHOD_DEPOSIT = '" . CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT . "';
        const CASHBOX_MOVEMENT_PAYMENT_METHOD_CHECK = '" . CashboxMovement::PAYMENT_METHOD_BANK_CHECK . "';
        const CASHBOX_MOVEMENT_PAYMENT_METHOD_CREDIT_CARD = '" . CashboxMovement::PAYMENT_METHOD_CREDIT_CARD . "';
        //Igv Affected
        const COMMERCIAL_MOVEMENT_IGV_AFFECTION_AFFECTED = '" . CommercialMovementProduct::IGV_AFFECTION_AFFECTED . "'
        const COMMERCIAL_MOVEMENT_IGV_AFFECTION_EXONERATED = '" . CommercialMovementProduct::IGV_AFFECTION_EXONERATED . "'
        const COMMERCIAL_MOVEMENT_IGV_AFFECTION_INAFFECTED = '" . CommercialMovementProduct::IGV_AFFECTION_INAFFECTED . "'
        const COMMERCIAL_MOVEMENT_IGV_AFFECTED_GIFT = '" . CommercialMovementProduct::IGV_AFFECTION_GIFT . "'
        //Scenario
        const SCENARIO_DIRECTION_IN = '" . Scenario::DIRECTION_IN . "';
        const SCENARIO_DIRECTION_OUT = '" . Scenario::DIRECTION_OUT . "';
        const SCENARIO_TYPE_CASHBOX = '" . Scenario::TYPE_CASHBOX . "';
        const SCENARIO_TYPE_LOAN = '" . Scenario::TYPE_LOAN . "';
        //Document
        const DOCUMENT_CREDIT_NOTE = '" . Document::CREDIT_NOTE . "';
        const DOCUMENT_REMISSION_GUIDE = '" . Document::REMISSION_GUIDE . "';
        const DOCUMENT_OTHER = '" . Document::OTHER . "';
        //Product
        const PRODUCT_PRESENTATION_NIU = '" . Presentation::PRESENTATION_NIU . "';
        const PRODUCT_TYPE_MERCHANDISE = '" . Product::TYPE_MERCHANDISE . "';
        const PRODUCT_TYPE_SERVICE = '" . Product::TYPE_SERVICE . "';
        const PRODUCT_TYPE_COMBO = '" . Product::TYPE_COMBO . "';
        const PRODUCT_TYPE_GROUP = '" . Product::TYPE_GROUP . "';
        const PRODUCT_TYPE_NEW = '" . Product::TYPE_NEW . "';
        //Strings
        const STRINGS_ERROR_STOCK = '" . Strings::ERROR_STOCK . "';
        const STRINGS_ERROR_MIN_PRICE = '" . Strings::ERROR_MIN_PRICE . "';
        const STRINGS_ERROR_AVG_PRICE = '" . Strings::ERROR_AVG_PRICE . "';
        const STRINGS_DETRACTION_ERROR = '" . Strings::DETRACTION_ERROR . "';
        const STRINGS_NO_DATA = '" . Strings::NO_DATA . "';
        const STRINGS_NONE = '" . Strings::NONE . "';
        const STRINGS_SELECT_OPTION = '" . Strings::SELECT_OPTION . "';
        const STRINGS_LINK_TEXT = '" . Strings::LINK_TEXT . "';
        const STRINGS_WAITING_MESSAGE = '" . Strings::WAITING_MESSAGE . "';
        //COLUMNS
        const ENTRY_DEBIT_COLUMN = '" . Entry::DEBIT_COLUMN . "';
        const ENTRY_CREDIT_COLUMN = '" . Entry::CREDIT_COLUMN . "';
        //KARDEX
        const KARDEX_DECIMALS = '" . Kardex::KARDEX_DECIMALS . "';
        //OWNER
        const OWNER_ACCOUNT = '" . Account::OWNER . "';
        const OWNER_COMBINATION = '" . Combination::OWNER . "';
        const OWNER_LOCKER = '" . Locker::OWNER . "';
        const OWNER_ORGANIZATION = '" . Organization::OWNER . "';
        const OWNER_PERSON = '" . Person::OWNER . "';
        const OWNER_USER = '" . User::OWNER . "';
        const OWNER_SELLER = '" . Seller::OWNER . "';
        //PERSON_TYPE
        const PERSON_TYPE_ANY = '" . Document::PERSON_TYPE_ANY . "';
        //ACCOUNT WILDCARD
        const WILDCARD_CASHBOX = '" . Account::WILDCARD_CASHBOX . "'
        //WS
        const REST_CODE_SUCCESS = " . USREST::CODE_SUCCESS . ";
        //
        const ENTRY_MODE_NORMAL = " . SIANEntry::MODE_NORMAL . ";
        const ENTRY_MODE_PROVISION = " . SIANEntry::MODE_PROVISION . ";
        //ENVIRONMENTS
        const YII_ENVIRONMENT_PRODUCTION = " . YII_ENVIRONMENT_PRODUCTION . ";
        const YII_ENVIRONMENT_STAGING = " . YII_ENVIRONMENT_STAGING . ";
        const YII_ENVIRONMENT_TESTING = " . YII_ENVIRONMENT_TESTING . ";
        const YII_ENVIRONMENT_DEVELOPMENT = " . YII_ENVIRONMENT_DEVELOPMENT . ";
        //PARAMS
        const PARAMS_DATETIME_PHP_FORMAT = '" . Yii::app()->params['datetime_php_format'] . "';
        const PARAMS_DATE_PHP_FORMAT = '" . Yii::app()->params['date_php_format'] . "';
        const PARAMS_ENVIRONMENT = " . Yii::app()->params['environment'] . ";
        const PARAMS_ENVIRONMENT_REPORTS = " . Yii::app()->params['environment_reports'] . ";
        const PARAMS_SKIN = '" . Yii::app()->params['skin'] . "';
        //COST_LEVEL
        const COST_LEVEL_1 = '" . CostLevelItem::LEVEL_1 . "';
        const COST_LEVEL_2 = '" . CostLevelItem::LEVEL_2 . "';
        const COST_LEVEL_3 = '" . CostLevelItem::LEVEL_3 . "';
        const COST_LEVEL_4 = '" . CostLevelItem::LEVEL_4 . "';
        const COST_LEVEL_5 = '" . CostLevelItem::LEVEL_5 . "';
        //API USER
        const API_USER_TYPE_POS = '" . ApiUser::TYPE_POS . "';
        const API_USER_TYPE_KIOSK = '" . ApiUser::TYPE_KIOSK . "';
        const API_USER_TYPE_WEB = '" . ApiUser::TYPE_WEB . "';
        const API_USER_TYPE_LOCKER = '" . ApiUser::TYPE_LOCKER . "';
        //API USER VARIANT
        const API_USER_CASHBOX_VARIANT_TYPE_CARD = '" . ApiUserCashboxVariant::PAYMENT_METHOD_CARD . "';
        const API_USER_CASHBOX_VARIANT_CARD_TYPE_ANY = '" . ApiUserCashboxVariant::CARD_TYPE_ANY . "';
        const API_USER_CASHBOX_VARIANT_AGREEMENT = '" . ApiUserCashboxVariant::PAYMENT_METHOD_AGREEMENT . "';
        //LOCKER
        const LOCKER_ACCESS_TYPE_CLIENT = '" . LockerAccess::TYPE_CLIENT . "';
        const LOCKER_ACCESS_TYPE_DELIVERER = '" . LockerAccess::TYPE_DELIVERER . "';
        //ATTACHMENT TYPES
        const ATTACHMENT_TYPE_OFFICE = '" . SIANAttachment::TYPE_OFFICE . "';
        const ATTACHMENT_TYPE_PDF = '" . SIANAttachment::TYPE_PDF . "';
        const ATTACHMENT_TYPE_GDOCS = '" . SIANAttachment::TYPE_GDOCS . "';
        const ATTACHMENT_TYPE_TEXT = '" . SIANAttachment::TYPE_TEXT . "';
        const ATTACHMENT_TYPE_HTML = '" . SIANAttachment::TYPE_HTML . "';
        const ATTACHMENT_TYPE_IMAGE = '" . SIANAttachment::TYPE_IMAGE . "';
        const ATTACHMENT_TYPE_VIDEO = '" . SIANAttachment::TYPE_VIDEO . "';
        const ATTACHMENT_TYPE_OTHER = '" . SIANAttachment::TYPE_OTHER . "';
        //PAYROLL
        const REGIME_TYPE_PERIOD_MONTHLY = '" . Regime::TYPE_PERIOD_MONTHLY . "';
        const REGIME_TYPE_PERIOD_WEEKLY = '" . Regime::TYPE_PERIOD_WEEKLY . "';
        //
        const IGV_AFFECTION_AFFECTED = '" . CommercialMovementProduct::IGV_AFFECTION_AFFECTED . "';
        const IGV_AFFECTION_EXPORT = '" . CommercialMovementProduct::IGV_AFFECTION_EXPORT . "';
        const IGV_AFFECTION_FREE = '" . CommercialMovementProduct::IGV_AFFECTION_FREE . "';
        const IGV_AFFECTION_EXONERATED = '" . CommercialMovementProduct::IGV_AFFECTION_EXONERATED . "';
        const IGV_AFFECTION_INAFFECTED = '" . CommercialMovementProduct::IGV_AFFECTION_INAFFECTED . "';
        const IGV_AFFECTION_GIFT = '" . CommercialMovementProduct::IGV_AFFECTION_GIFT . "';
        //
        const ACCOUNT_DESTINY_TYPE_FIXED = '" . Account::DESTINY_TYPE_FIXED . "';
        //URLS
        const URL_SELECT2 = '" . Yii::app()->controller->createUrl("/widget/select2Items") . "';
        const URL_GET_COST_LEVELS = '" . Yii::app()->controller->createUrl("/widget/getCostLevels") . "';
        const URL_GET_COMBINATION = '" . Yii::app()->controller->createUrl("/widget/getCombination") . "';
        const URL_ITEM_WILDCARD = '" . Yii::app()->controller->createUrl("/accounting/account/loadWildcardData") . "';
        //
        ", CClientScript::POS_BEGIN);

        //EXTERNAL
//        if (!Yii::app()->params['ga_disabled']) {
//            Yii::app()->clientScript->registerScriptFile(USGoogleMap::getAPIUrl(true, null), CClientScript::POS_END);
//        }
    }

}
