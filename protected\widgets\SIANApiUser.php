<?php

class SIANApiUser extends CWidget {

    //Var
    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $type_items;
    public $seller_items = null;
    public $store_items = null;
    public $warehouse_items = null;
    public $cashbox_items = null;
    public $destination_cashbox_items = null;
    public $business_unit_items = null;
    public $default_price_items = null;
    public $person_items = null;
    //PRIVATE
    private $controller;
    private $realtime_input_id;
    private $manual_check_input_id;
    private $require_validation_input_id;
    private $aux_person_autocomplete_id;
    private $user_select_id;
    private $seller_select_id;
    private $show_variants = false;

    public function init() {
        $this->controller = Yii::app()->controller;
        //
        if (!isset($this->person_items)) {
            throw new Exception('Debe especificar la lista de usuarios');
        }
        if (!isset($this->seller_items)) {
            throw new Exception('Debe especificar la lista de vendedores');
        }
        //
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->realtime_input_id = $this->controller->getServerId();
        $this->manual_check_input_id = $this->controller->getServerId();
        $this->require_validation_input_id = $this->controller->getServerId();
        $this->aux_person_autocomplete_id = $this->controller->getServerId();
        $this->user_select_id = $this->controller->getServerId();
        $this->seller_select_id = $this->controller->getServerId();
        //
        $this->show_variants = in_array($this->model->scenario, [ApiUser::SCENARIO_CREATE, ApiUser::SCENARIO_UPDATE], true);
        //Recuperamos variantes
        $a_variants = [];
        if ($this->show_variants) {
            foreach ($this->model->tempVariants as $o_variant) {
                $a_variant = [];
                $a_variant['api_user_variant_id'] = isset($o_variant->api_user_variant_id) ? $o_variant->api_user_variant_id : '';
                $a_variant['variant_code'] = isset($o_variant->variant_code) ? $o_variant->variant_code : '';
                //nombre variante
                $a_variant['variant_name'] = isset($o_variant->variant_name) ? $o_variant->variant_name : '';
                $a_variant['default'] = isset($o_variant->default) ? $o_variant->default : 0;
                $a_variant['store_id'] = isset($o_variant->store_id) ? $o_variant->store_id : '';
                $a_variant['warehouse_id'] = isset($o_variant->warehouse_id) ? $o_variant->warehouse_id : '';
                $a_variant['supplies_warehouse_id'] = isset($o_variant->supplies_warehouse_id) ? $o_variant->supplies_warehouse_id : '';
                $a_variant['cashbox_id'] = isset($o_variant->cashbox_id) ? $o_variant->cashbox_id : '';
                $a_variant['destination_cashbox_id'] = isset($o_variant->destination_cashbox_id) ? $o_variant->destination_cashbox_id : '';
                $a_variant['business_unit_id'] = isset($o_variant->business_unit_id) ? $o_variant->business_unit_id : '';
                $a_variant['default_price'] = isset($o_variant->default_price) ? $o_variant->default_price : '';
                $a_variant['is_removable'] = isset($o_variant->is_removable) ? $o_variant->is_removable : 1;
                $a_variant['has_admin_variants'] = isset($o_variant->has_admin_variants) ? $o_variant->has_admin_variants : 0;
                $a_variant['has_cashbox_variants'] = isset($o_variant->has_cashbox_variants) ? $o_variant->has_cashbox_variants : 0;
                $a_variant['use_shift'] = isset($o_variant->use_shift) ? $o_variant->use_shift : 0;
                $a_variant['sales_without_stock'] = isset($o_variant->sales_without_stock) ? $o_variant->sales_without_stock : 0;

                $a_adminVariants = [];
                foreach ($o_variant->tempAdminVariants as $o_adminVariant) {
                    $a_adminVariant = [];
                    $a_adminVariant['api_user_admin_variant_id'] = isset($o_adminVariant->api_user_admin_variant_id) ? $o_adminVariant->api_user_admin_variant_id : '';
                    $a_adminVariant['person_id'] = isset($o_adminVariant->person_id) ? $o_adminVariant->person_id : '';
                    $a_adminVariant['email_address'] = isset($o_adminVariant->email_address) ? $o_adminVariant->email_address : '';
                    $a_adminVariant['phone_number'] = isset($o_adminVariant->phone_number) ? $o_adminVariant->phone_number : '';
                    $a_adminVariant['errors'] = $o_adminVariant->getAllErrors();

                    $a_adminVariants[] = $a_adminVariant;
                }

                $a_cashboxVariants = [];
                foreach ($o_variant->tempCashboxVariants as $o_cashboxVariant) {
                    $a_cashboxVariant = [];
                    $a_cashboxVariant['api_user_cashbox_variant_id'] = isset($o_cashboxVariant->api_user_cashbox_variant_id) ? $o_cashboxVariant->api_user_cashbox_variant_id : '';
                    $a_cashboxVariant['type'] = isset($o_cashboxVariant->type) ? $o_cashboxVariant->type : '';
                    $a_cashboxVariant['currency'] = isset($o_cashboxVariant->currency) ? $o_cashboxVariant->currency : '';
                    $a_cashboxVariant['card_type'] = isset($o_cashboxVariant->card_type) ? $o_cashboxVariant->card_type : '';
                    $a_cashboxVariant['agreement_id'] = isset($o_cashboxVariant->agreement_id) ? $o_cashboxVariant->agreement_id : '';
                    $a_cashboxVariant['cashbox_id'] = isset($o_cashboxVariant->cashbox_id) ? $o_cashboxVariant->cashbox_id : '';
                    $a_cashboxVariant['errors'] = $o_cashboxVariant->getAllErrors();

                    $a_cashboxVariants[] = $a_cashboxVariant;
                }

                $a_variant['adminVariants'] = $a_adminVariants;
                $a_variant['cashboxVariants'] = $a_cashboxVariants;
                $a_variant['errors'] = $o_variant->getAllErrors();
                //
                $a_variants[] = $a_variant;
            }
        }

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-api-user.js');

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        var divObj = $('#{$this->id}');
        divObj.data('count', 0);
        divObj.data('person_items', " . CJSON::encode($this->person_items) . ");
        divObj.data('store_items', " . CJSON::encode($this->store_items) . ");
        divObj.data('warehouse_items', " . CJSON::encode($this->warehouse_items) . ");
        divObj.data('cashbox_items', " . CJSON::encode($this->cashbox_items) . ");
        divObj.data('destination_cashbox_items', " . CJSON::encode($this->destination_cashbox_items) . ");
        divObj.data('business_unit_items', " . CJSON::encode($this->business_unit_items) . ");
        divObj.data('default_price_items', " . CJSON::encode($this->default_price_items) . ");
        divObj.data('type_items', " . CJSON::encode(ApiUserCashboxVariant::getTypeItems()) . ");
        divObj.data('currency_items', " . CJSON::encode(ApiUserCashboxVariant::getCurrencyItems()) . ");
        divObj.data('card_type_items', " . CJSON::encode(ApiUserCashboxVariant::getCardTypeItems()) . ");
        divObj.data('agreement_items', " . CJSON::encode((new DpAgreement())->getListData('agreement_id', 'agreement_name')) . ");
            

        $(document).ready(function() {  
            //
            SIANApiUserInit('{$this->id}', " . CJSON::encode($a_variants) . ");
            SIANApiUserChangeType('{$this->id}', '" . (isset($this->model->type) ? $this->model->type : '') . "');
        });
        
        $('body').on('click', '#{$this->id}_table input.sian-api-user-variant-default', function() {
            $('#{$this->id}_table input[type=radio]:checked').not(this).prop('checked', false);
        });

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Cuenta de acceso para RESTFul API',
            'headerIcon' => 'user',
            'htmlOptions' => [
                'id' => $this->id
            ]
        ));

        switch ($this->model->scenario) {
            case ApiUser::SCENARIO_CREATE:
                echo "<div class='row'>";
                echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
                echo $this->renderUsername();
                echo "</div>";
                echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
                echo $this->renderPassword();
                echo "</div>";
                echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
                echo $this->renderPasswordRepeat();
                echo "</div>";
                echo "</div>";
                //
                echo "<div class='row'>";
                echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
                echo $this->renderType();
                echo "</div>";
                echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-12'>";
                echo $this->renderRealtime();
                echo "</div>";
                echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-12'>";
                echo $this->renderStatus();
                echo "</div>";
                echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-12'>";
                echo $this->renderManualCheck();
                echo "</div>";
                echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-12'>";
                echo $this->renderRequireValidation();
                echo "</div>";
                echo "</div>";
                //
                echo "<div class='row'>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderAuxPerson();
                echo "</div>";
                echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
                echo $this->renderUser();
                echo "</div>";
                echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
                echo $this->renderSeller();
                echo "</div>";
                echo "</div>";
                //
                echo "<div class='row'>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderProductUrlPattern();
                echo "</div>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderPayUrlPattern();
                echo "</div>";
                echo "</div>";
                //
                echo "<div class='row'>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderPinDuration();
                echo "</div>";
                echo "</div>";
                break;
            case ApiUser::SCENARIO_UPDATE:
                echo "<div class='row'>";
                echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-12'>";
                echo $this->renderUsername();
                echo "</div>";
                echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-12'>";
                echo $this->renderType();
                echo "</div>";
                echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-12'>";
                echo $this->renderStatus();
                echo "</div>";
                echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-12'>";
                echo $this->renderManualCheck();
                echo "</div>";
                echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-12'>";
                echo $this->renderRealtime();
                echo "</div>";
                echo "<div class='col-lg-2 col-md-2 col-sm-4 col-xs-12'>";
                echo $this->renderRequireValidation();
                echo "</div>";
                echo "</div>";
                //
                echo "<div class='row'>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderAuxPerson();
                echo "</div>";
                echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
                echo $this->renderUser();
                echo "</div>";
                echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
                echo $this->renderSeller();
                echo "</div>";
                echo "</div>";
                //
                echo "<div class='row'>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderProductUrlPattern();
                echo "</div>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderPayUrlPattern();
                echo "</div>";
                echo "</div>";
                //
                echo "<div class='row'>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderPinDuration();
                echo "</div>";
                echo "</div>";
                break;
            case ApiUser::SCENARIO_PASSWORD:
                echo "<div class='row'>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderPassword();
                echo "</div>";
                echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
                echo $this->renderPasswordRepeat();
                echo "</div>";
                echo "</div>";
                break;
        }
        $this->endWidget();

        //Renderizamos variaciones
        if ($this->show_variants) {
            $this->beginWidget('booster.widgets.TbPanel', array(
                'title' => 'Variaciones',
                'headerIcon' => 'list',
                'htmlOptions' => [
                    'class' => $this->model->hasErrors('tempVariants') ? 'us-error' : ''
                ]
            ));
            echo $this->renderVariantTable();
            $this->endWidget();
        }
    }

    private function renderUsername() {
        return $this->form->textFieldRow($this->model, 'username', array(
                    'maxlength' => 80,
                    'placeholder' => $this->model->getAttributeLabel('username'),
        ));
    }

    private function renderAuxPerson() {
        return $this->widget('application.widgets.USAutocomplete', array(
                    'id' => $this->aux_person_autocomplete_id,
                    'model' => $this->model,
                    'attribute' => 'aux_person_id',
                    'parent_id' => $this->modal_id,
                    'view' => array(
                        'model' => 'DpBusinessPartner',
                        'scenario' => DpBusinessPartner::SCENARIO_API_USER_FORM,
                        'attributes' => array(
                            array('name' => 'person_id', 'width' => 0, 'types' => array('id', 'value'), 'hidden' => true),
                            array('name' => 'identification_type', 'width' => 5, 'search' => false),
                            array('name' => 'identification_number', 'width' => 15, 'types' => array('aux')),
                            array('name' => 'person_name', 'width' => 40, 'types' => array('text')),
                            array('name' => 'official_address', 'width' => 40, 'search' => false),
                        )
                    ),
                    'hint' => 'Reponsable de API, no influye en los movimientos creados',
                    'maintenance' => array(
                        'module' => 'systems',
                        'controller' => 'apiUser'
                    ),
                        ), true);
    }

    private function renderUser() {
        return $this->form->dropDownListRow($this->model, 'person_id', USArray::listData($this->person_items, 'person_id', 'person_name'), array(
                    'id' => $this->user_select_id,
                    'empty' => Strings::SELECT_OPTION,
                    'required' => $this->model->type === ApiUser::TYPE_WEB,
                    'disabled' => in_array($this->model->type, [ApiUser::TYPE_LOCKER, ApiUser::TYPE_POS, ApiUser::TYPE_KIOSK]),
                    'hint' => 'Al cual se le asignarán los movimientos que se creen mediante esta cuenta de API'
        ));
    }

    private function renderSeller() {
        return $this->form->dropDownListRow($this->model, 'seller_id', $this->seller_items, array(
                    'id' => $this->seller_select_id,
                    'empty' => Strings::SELECT_OPTION,
                    'required' => $this->model->type === ApiUser::TYPE_WEB && $this->controller->getOrganization()->globalVar->enable_sellers == 1,
                    'disabled' => in_array($this->model->type, [ApiUser::TYPE_LOCKER, ApiUser::TYPE_POS, ApiUser::TYPE_KIOSK]) || $this->controller->getOrganization()->globalVar->enable_sellers == 0,
                    'hint' => 'A la cual se le asignarán los movimientos que se creen mediante esta cuenta de API'
        ));
    }

    private function renderPassword() {
        return $this->form->passwordFieldRow($this->model, 'password', array(
                    'placeholder' => $this->model->getAttributeLabel('password'),
        ));
    }

    private function renderPasswordRepeat() {
        return $this->form->passwordFieldRow($this->model, 'password_repeat', array(
                    'placeholder' => $this->model->getAttributeLabel('password_repeat'),
        ));
    }

    private function renderOldPassword() {
        return $this->form->passwordFieldRow($this->model, 'old_password', array(
                    'placeholder' => $this->model->getAttributeLabel('old_password'),
        ));
    }

    private function renderType() {
        return $this->form->dropDownListRow($this->model, 'type', $this->type_items, array(
                    'empty' => Strings::SELECT_OPTION,
                    'readonly' => !$this->model->isNewRecord,
                    'onchange' => "                        
                        //Tiempo real
                        $('#{$this->realtime_input_id}').bootstrapSwitch('readonly', this.value !== '" . ApiUser::TYPE_POS . "');
                        $('#{$this->require_validation_input_id}').bootstrapSwitch('readonly', this.value !== '" . ApiUser::TYPE_POS . "');
                        $('#{$this->realtime_input_id}').prop('checked', true).change();
                        //Verificación manual
                        $('#{$this->manual_check_input_id}').bootstrapSwitch('readonly', this.value !== '" . ApiUser::TYPE_WEB . "');
                        if(this.value !== '" . ApiUser::TYPE_WEB . "')
                        {
                            $('#{$this->manual_check_input_id}').prop('checked', 0).change();
                        }
                            
                        $('#{$this->user_select_id}')
                            .makeRequired(this.value === '" . ApiUser::TYPE_WEB . "')
                            .prop('disabled', inArray(this.value, ['" . ApiUser::TYPE_LOCKER . "', '" . ApiUser::TYPE_POS . "', '" . ApiUser::TYPE_KIOSK . "']));
                            
                        " . ($this->controller->getOrganization()->globalVar->enable_sellers == 1 ? "
                            $('#{$this->seller_select_id}')
                                .makeRequired(this.value === '" . ApiUser::TYPE_WEB . "')
                                .prop('disabled', inArray(this.value, ['" . ApiUser::TYPE_LOCKER . "', '" . ApiUser::TYPE_POS . "', '" . ApiUser::TYPE_KIOSK . "']));
                        " : "") . "
                            
                        SIANApiUserChangeType('{$this->id}', this.value)
                        
                        "
        ));
    }

    private function renderStatus() {
        return $this->widget('application.widgets.USSwitch', array('model' => $this->model, 'attribute' => 'status'), true);
    }

    private function renderManualCheck() {
        return $this->widget('application.widgets.USSwitch', array(
                    'id' => $this->manual_check_input_id,
                    'model' => $this->model,
                    'attribute' => 'manual_check',
                    'readonly' => $this->model->type !== ApiUser::TYPE_WEB
                        ), true);
    }

    private function renderRequireValidation() {
        return $this->widget('application.widgets.USSwitch', array(
                    'id' => $this->require_validation_input_id,
                    'model' => $this->model,
                    'attribute' => 'require_validation',
                    'readonly' => $this->model->type !== ApiUser::TYPE_POS
                        ), true);
    }

    private function renderRealtime() {
        return $this->widget('application.widgets.USSwitch', array(
                    'id' => $this->realtime_input_id,
                    'model' => $this->model,
                    'attribute' => 'realtime',
                    'readonly' => $this->model->type !== ApiUser::TYPE_POS
                        ), true);
    }

    private function renderProductUrlPattern() {
        return $this->form->textFieldRow($this->model, 'product_url_pattern', array(
                    'maxlength' => 2048,
                    'placeholder' => $this->model->getAttributeLabel('product_url_pattern'),
                    'hint' => 'Comodines obligatorios: ' . ApiUser::URL_WILDCARD_PRODUCT_ID . ', opcionales: ' . ApiUser::URL_WILDCARD_PRODUCT_TYPE . ', ' . ApiUser::URL_WILDCARD_PRODUCT_ALIAS
        ));
    }

    private function renderPinDuration() {
        return $this->form->numberFieldRow($this->model, 'pin_duration', array(
                    'min' => 1,
                    'max' => 1440,
                    'hint' => 'En minutos'
        ));
    }

    private function renderPayUrlPattern() {
        return $this->form->textFieldRow($this->model, 'pay_url_pattern', array(
                    'maxlength' => 2048,
                    'placeholder' => $this->model->getAttributeLabel('pay_url_pattern'),
                    'hint' => 'Comodines: {{MOVEMENT_CODE}}'
        ));
    }

    private function renderVariantTable() {
        $s_html = "<table id='" . $this->id . "_table' class='table table-condensed table-hover'>";
        $s_html .= "<thead>";
        $s_html .= "<tr>";
        $s_html .= "<th width='12%' style='text-align:center' field='variant_code'>{$this->model->getAttributeLabel('variants.variant_code')}</th>";
        $s_html .= "<th width='10%' style='text-align:center' field='variant_name'>{$this->model->getAttributeLabel('variants.variant_name')}</th>";
        $s_html .= "<th width='2%'  style='text-align:center' field='default'>{$this->model->getAttributeLabel('variants.default')}</th>";
        $s_html .= "<th width='13%' style='text-align:center' field='store_id'>{$this->model->getAttributeLabel('variants.store_id')}</th>";
        $s_html .= "<th width='10%' style='text-align:center' field='warehouse_id'>{$this->model->getAttributeLabel('variants.warehouse_id')}</th>";
        $s_html .= "<th width='10%' style='text-align:center' field='supplies_warehouse_id'>{$this->model->getAttributeLabel('variants.supplies_warehouse_id')}</th>";
        $s_html .= "<th width='12%' style='text-align:center' field='cashbox_id'>{$this->model->getAttributeLabel('variants.cashbox_id')}</th>";
        $s_html .= "<th width='13%' style='text-align:center' field='destination_cashbox_id'>{$this->model->getAttributeLabel('variants.destination_cashbox_id')}</th>";
        $s_html .= "<th width='12%' style='text-align:center' field='business_unit_id'>{$this->model->getAttributeLabel('variants.business_unit_id')}</th>";
        $s_html .= "<th width='5%'  style='text-align:center' field='default_price'>{$this->model->getAttributeLabel('variants.default_price')}</th>";
        $s_html .= "<th width='10%'></th>";
        $s_html .= "</tr>";
        $s_html .= "</thead>";
        $s_html .= "<tbody class='sian-api-user-variant-tbody'>";
        $s_html .= "</tbody>";
        $s_html .= "<tfoot>";
        $s_html .= "<tr>";
        $s_html .= "<td>";
        $s_html .= CHtml::link("<span class='fa fa-lg fa-plus black'></span> Agregar variante", Strings::LINK_TEXT, array(
                    'title' => 'Agregar variante',
                    'onclick' => "
                        var divObj = $('#{$this->id}');
                        var xdefault = divObj.data('count') == 0;
                        SIANApiUserAddVariant('{$this->id}', '', '','', xdefault, '', '', '', '', '', '', '', [], [], 1, 0, 0, 0, 0, {});
                        SIANApiUserPaintVariants('{$this->id}');"
        ));
        $s_html .= "</td>";
        $s_html .= "<td colspan='5'>";
        $s_html .= '<i>Si no es obligatorio y no se asigna almacén o caja/banco, se usarán los predeterminados</i>';
        $s_html .= "</td>";
        $s_html .= "</tr>";
        $s_html .= "</tfoot>";
        $s_html .= "</table>";
        return $s_html;
    }

}
