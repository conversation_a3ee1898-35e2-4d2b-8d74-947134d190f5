(function(e){"use strict";var t=function(t,n){this.options=e.extend({},e.fn.editableform.defaults,n);this.$div=e(t);if(!this.options.scope){this.options.scope=this}};t.prototype={constructor:t,initInput:function(){this.input=this.options.input;this.value=this.input.str2value(this.options.value);this.input.prerender()},initTemplate:function(){this.$form=e(e.fn.editableform.template)},initButtons:function(){var t=this.$form.find(".editable-buttons");t.append(e.fn.editableform.buttons);if(this.options.showbuttons==="bottom"){t.addClass("editable-buttons-bottom")}},render:function(){this.$loading=e(e.fn.editableform.loading);this.$div.empty().append(this.$loading);this.initTemplate();if(this.options.showbuttons){this.initButtons()}else{this.$form.find(".editable-buttons").remove()}this.showLoading();this.isSaving=false;this.$div.triggerHandler("rendering");this.initInput();this.$form.find("div.editable-input").append(this.input.$tpl);this.$div.append(this.$form);e.when(this.input.render()).then(e.proxy(function(){if(!this.options.showbuttons){this.input.autosubmit()}this.$form.find(".editable-cancel").click(e.proxy(this.cancel,this));if(this.input.error){this.error(this.input.error);this.$form.find(".editable-submit").attr("disabled",true);this.input.$input.attr("disabled",true);this.$form.submit(function(e){e.preventDefault()})}else{this.error(false);this.input.$input.removeAttr("disabled");this.$form.find(".editable-submit").removeAttr("disabled");var t=this.value===null||this.value===undefined||this.value===""?this.options.defaultValue:this.value;this.input.value2input(t);this.$form.submit(e.proxy(this.submit,this))}this.$div.triggerHandler("rendered");this.showForm();if(this.input.postrender){this.input.postrender()}},this))},cancel:function(){this.$div.triggerHandler("cancel")},showLoading:function(){var e,t;if(this.$form){e=this.$form.outerWidth();t=this.$form.outerHeight();if(e){this.$loading.width(e)}if(t){this.$loading.height(t)}this.$form.hide()}else{e=this.$loading.parent().width();if(e){this.$loading.width(e)}}this.$loading.show()},showForm:function(e){this.$loading.hide();this.$form.show();if(e!==false){this.input.activate()}this.$div.triggerHandler("show")},error:function(t){var n=this.$form.find(".control-group"),r=this.$form.find(".editable-error-block"),i;if(t===false){n.removeClass(e.fn.editableform.errorGroupClass);r.removeClass(e.fn.editableform.errorBlockClass).empty().hide()}else{if(t){i=(""+t).split("\n");for(var s=0;s<i.length;s++){i[s]=e("<div>").text(i[s]).html()}t=i.join("<br>")}n.addClass(e.fn.editableform.errorGroupClass);r.addClass(e.fn.editableform.errorBlockClass).html(t).show()}},submit:function(t){t.stopPropagation();t.preventDefault();var n=this.input.input2value();var r=this.validate(n);if(e.type(r)==="object"&&r.newValue!==undefined){n=r.newValue;this.input.value2input(n);if(typeof r.msg==="string"){this.error(r.msg);this.showForm();return}}else if(r){this.error(r);this.showForm();return}if(!this.options.savenochange&&this.input.value2str(n)==this.input.value2str(this.value)){this.$div.triggerHandler("nochange");return}var i=this.input.value2submit(n);this.isSaving=true;e.when(this.save(i)).done(e.proxy(function(e){this.isSaving=false;var t=typeof this.options.success==="function"?this.options.success.call(this.options.scope,e,n):null;if(t===false){this.error(false);this.showForm(false);return}if(typeof t==="string"){this.error(t);this.showForm();return}if(t&&typeof t==="object"&&t.hasOwnProperty("newValue")){n=t.newValue}this.error(false);this.value=n;this.$div.triggerHandler("save",{newValue:n,submitValue:i,response:e})},this)).fail(e.proxy(function(e){this.isSaving=false;var t;if(typeof this.options.error==="function"){t=this.options.error.call(this.options.scope,e,n)}else{t=typeof e==="string"?e:e.responseText||e.statusText||"Unknown error!"}this.error(t);this.showForm()},this))},save:function(t){this.options.pk=e.fn.editableutils.tryParseJson(this.options.pk,true);var n=typeof this.options.pk==="function"?this.options.pk.call(this.options.scope):this.options.pk,r=!!(typeof this.options.url==="function"||this.options.url&&(this.options.send==="always"||this.options.send==="auto"&&n!==null&&n!==undefined)),i;if(r){this.showLoading();i={name:this.options.name||"",value:t,pk:n};if(typeof this.options.params==="function"){i=this.options.params.call(this.options.scope,i)}else{this.options.params=e.fn.editableutils.tryParseJson(this.options.params,true);e.extend(i,this.options.params)}if(typeof this.options.url==="function"){return this.options.url.call(this.options.scope,i)}else{return e.ajax(e.extend({url:this.options.url,data:i,type:"POST"},this.options.ajaxOptions))}}},validate:function(e){if(e===undefined){e=this.value}if(typeof this.options.validate==="function"){return this.options.validate.call(this.options.scope,e)}},option:function(e,t){if(e in this.options){this.options[e]=t}if(e==="value"){this.setValue(t)}},setValue:function(e,t){if(t){this.value=this.input.str2value(e)}else{this.value=e}if(this.$form&&this.$form.is(":visible")){this.input.value2input(this.value)}}};e.fn.editableform=function(n){var r=arguments;return this.each(function(){var i=e(this),s=i.data("editableform"),o=typeof n==="object"&&n;if(!s){i.data("editableform",s=new t(this,o))}if(typeof n==="string"){s[n].apply(s,Array.prototype.slice.call(r,1))}})};e.fn.editableform.Constructor=t;e.fn.editableform.defaults={type:"text",url:null,params:null,name:null,pk:null,value:null,defaultValue:null,send:"auto",validate:null,success:null,error:null,ajaxOptions:null,showbuttons:true,scope:null,savenochange:false};e.fn.editableform.template='<form class="form-inline editableform">'+'<div class="control-group">'+'<div><div class="editable-input"></div><div class="editable-buttons"></div></div>'+'<div class="editable-error-block"></div>'+"</div>"+"</form>";e.fn.editableform.loading='<div class="editableform-loading"></div>';e.fn.editableform.buttons='<button type="submit" class="editable-submit">ok</button>'+'<button type="button" class="editable-cancel">cancel</button>';e.fn.editableform.errorGroupClass=null;e.fn.editableform.errorBlockClass="editable-error";e.fn.editableform.engine="jquery"})(window.jQuery);(function(e){"use strict";e.fn.editableutils={inherit:function(e,t){var n=function(){};n.prototype=t.prototype;e.prototype=new n;e.prototype.constructor=e;e.superclass=t.prototype},setCursorPosition:function(e,t){if(e.setSelectionRange){e.setSelectionRange(t,t)}else if(e.createTextRange){var n=e.createTextRange();n.collapse(true);n.moveEnd("character",t);n.moveStart("character",t);n.select()}},tryParseJson:function(e,t){if(typeof e==="string"&&e.length&&e.match(/^[\{\[].*[\}\]]$/)){if(t){try{e=(new Function("return "+e))()}catch(n){}finally{return e}}else{e=(new Function("return "+e))()}}return e},sliceObj:function(t,n,r){var i,s,o={};if(!e.isArray(n)||!n.length){return o}for(var u=0;u<n.length;u++){i=n[u];if(t.hasOwnProperty(i)){o[i]=t[i]}if(r===true){continue}s=i.toLowerCase();if(t.hasOwnProperty(s)){o[i]=t[s]}}return o},getConfigData:function(t){var n={};e.each(t.data(),function(e,t){if(typeof t!=="object"||t&&typeof t==="object"&&(t.constructor===Object||t.constructor===Array)){n[e]=t}});return n},objectKeys:function(e){if(Object.keys){return Object.keys(e)}else{if(e!==Object(e)){throw new TypeError("Object.keys called on a non-object")}var t=[],n;for(n in e){if(Object.prototype.hasOwnProperty.call(e,n)){t.push(n)}}return t}},escape:function(t){return e("<div>").text(t).html()},itemsByValue:function(t,n,r){if(!n||t===null){return[]}if(typeof r!=="function"){var i=r||"value";r=function(e){return e[i]}}var s=e.isArray(t),o=[],u=this;e.each(n,function(n,i){if(i.children){o=o.concat(u.itemsByValue(t,i.children,r))}else{if(s){if(e.grep(t,function(e){return e==(i&&typeof i==="object"?r(i):i)}).length){o.push(i)}}else{var a=i&&typeof i==="object"?r(i):i;if(t==a){o.push(i)}}}});return o},createInput:function(t){var n,r,i,s=t.type;if(s==="date"){if(t.mode==="inline"){if(e.fn.editabletypes.datefield){s="datefield"}else if(e.fn.editabletypes.dateuifield){s="dateuifield"}}else{if(e.fn.editabletypes.date){s="date"}else if(e.fn.editabletypes.dateui){s="dateui"}}if(s==="date"&&!e.fn.editabletypes.date){s="combodate"}}if(s==="datetime"&&t.mode==="inline"){s="datetimefield"}if(s==="wysihtml5"&&!e.fn.editabletypes[s]){s="textarea"}if(typeof e.fn.editabletypes[s]==="function"){n=e.fn.editabletypes[s];r=this.sliceObj(t,this.objectKeys(n.defaults));i=new n(r);return i}else{e.error("Unknown type: "+s);return false}},supportsTransitions:function(){var e=document.body||document.documentElement,t=e.style,n="transition",r=["Moz","Webkit","Khtml","O","ms"];if(typeof t[n]==="string"){return true}n=n.charAt(0).toUpperCase()+n.substr(1);for(var i=0;i<r.length;i++){if(typeof t[r[i]+n]==="string"){return true}}return false}}})(window.jQuery);(function(e){"use strict";var t=function(e,t){this.init(e,t)};var n=function(e,t){this.init(e,t)};t.prototype={containerName:null,containerDataName:null,innerCss:null,containerClass:"editable-container editable-popup",defaults:{},init:function(n,r){this.$element=e(n);this.options=e.extend({},e.fn.editableContainer.defaults,r);this.splitOptions();this.formOptions.scope=this.$element[0];this.initContainer();this.delayedHide=false;this.$element.on("destroyed",e.proxy(function(){this.destroy()},this));if(!e(document).data("editable-handlers-attached")){e(document).on("keyup.editable",function(t){if(t.which===27){e(".editable-open").editableContainer("hide")}});e(document).on("click.editable",function(n){var r=e(n.target),i,s=[".editable-container",".ui-datepicker-header",".datepicker",".modal-backdrop",".bootstrap-wysihtml5-insert-image-modal",".bootstrap-wysihtml5-insert-link-modal"];if(!e.contains(document.documentElement,n.target)){return}if(r.is(document)){return}for(i=0;i<s.length;i++){if(r.is(s[i])||r.parents(s[i]).length){return}}t.prototype.closeOthers(n.target)});e(document).data("editable-handlers-attached",true)}},splitOptions:function(){this.containerOptions={};this.formOptions={};if(!e.fn[this.containerName]){throw new Error(this.containerName+" not found. Have you included corresponding js file?")}for(var t in this.options){if(t in this.defaults){this.containerOptions[t]=this.options[t]}else{this.formOptions[t]=this.options[t]}}},tip:function(){return this.container()?this.container().$tip:null},container:function(){var e;if(this.containerDataName){if(e=this.$element.data(this.containerDataName)){return e}}e=this.$element.data(this.containerName);return e},call:function(){this.$element[this.containerName].apply(this.$element,arguments)},initContainer:function(){this.call(this.containerOptions)},renderForm:function(){this.$form.editableform(this.formOptions).on({save:e.proxy(this.save,this),nochange:e.proxy(function(){this.hide("nochange")},this),cancel:e.proxy(function(){this.hide("cancel")},this),show:e.proxy(function(){if(this.delayedHide){this.hide(this.delayedHide.reason);this.delayedHide=false}else{this.setPosition()}},this),rendering:e.proxy(this.setPosition,this),resize:e.proxy(this.setPosition,this),rendered:e.proxy(function(){this.$element.triggerHandler("shown",e(this.options.scope).data("editable"))},this)}).editableform("render")},show:function(t){this.$element.addClass("editable-open");if(t!==false){this.closeOthers(this.$element[0])}this.innerShow();this.tip().addClass(this.containerClass);if(this.$form){}this.$form=e("<div>");if(this.tip().is(this.innerCss)){this.tip().append(this.$form)}else{this.tip().find(this.innerCss).append(this.$form)}this.renderForm()},hide:function(e){if(!this.tip()||!this.tip().is(":visible")||!this.$element.hasClass("editable-open")){return}if(this.$form.data("editableform").isSaving){this.delayedHide={reason:e};return}else{this.delayedHide=false}this.$element.removeClass("editable-open");this.innerHide();this.$element.triggerHandler("hidden",e||"manual")},innerShow:function(){},innerHide:function(){},toggle:function(e){if(this.container()&&this.tip()&&this.tip().is(":visible")){this.hide()}else{this.show(e)}},setPosition:function(){},save:function(e,t){this.$element.triggerHandler("save",t);this.hide("save")},option:function(e,t){this.options[e]=t;if(e in this.containerOptions){this.containerOptions[e]=t;this.setContainerOption(e,t)}else{this.formOptions[e]=t;if(this.$form){this.$form.editableform("option",e,t)}}},setContainerOption:function(e,t){this.call("option",e,t)},destroy:function(){this.hide();this.innerDestroy();this.$element.off("destroyed");this.$element.removeData("editableContainer")},innerDestroy:function(){},closeOthers:function(t){e(".editable-open").each(function(n,r){if(r===t||e(r).find(t).length){return}var i=e(r),s=i.data("editableContainer");if(!s){return}if(s.options.onblur==="cancel"){i.data("editableContainer").hide("onblur")}else if(s.options.onblur==="submit"){i.data("editableContainer").tip().find("form").submit()}})},activate:function(){if(this.tip&&this.tip().is(":visible")&&this.$form){this.$form.data("editableform").input.activate()}}};e.fn.editableContainer=function(r){var i=arguments;return this.each(function(){var s=e(this),o="editableContainer",u=s.data(o),a=typeof r==="object"&&r,f=a.mode==="inline"?n:t;if(!u){s.data(o,u=new f(this,a))}if(typeof r==="string"){u[r].apply(u,Array.prototype.slice.call(i,1))}})};e.fn.editableContainer.Popup=t;e.fn.editableContainer.Inline=n;e.fn.editableContainer.defaults={value:null,placement:"top",autohide:true,onblur:"cancel",anim:false,mode:"popup"};jQuery.event.special.destroyed={remove:function(e){if(e.handler){e.handler()}}}})(window.jQuery);(function(e){"use strict";e.extend(e.fn.editableContainer.Inline.prototype,e.fn.editableContainer.Popup.prototype,{containerName:"editableform",innerCss:".editable-inline",containerClass:"editable-container editable-inline",initContainer:function(){this.$tip=e("<span></span>");if(!this.options.anim){this.options.anim=0}},splitOptions:function(){this.containerOptions={};this.formOptions=this.options},tip:function(){return this.$tip},innerShow:function(){this.$element.hide();this.tip().insertAfter(this.$element).show()},innerHide:function(){this.$tip.hide(this.options.anim,e.proxy(function(){this.$element.show();this.innerDestroy()},this))},innerDestroy:function(){if(this.tip()){this.tip().empty().remove()}}})})(window.jQuery);(function(e){"use strict";var t=function(t,n){this.$element=e(t);this.options=e.extend({},e.fn.editable.defaults,n,e.fn.editableutils.getConfigData(this.$element));if(this.options.selector){this.initLive()}else{this.init()}if(this.options.highlight&&!e.fn.editableutils.supportsTransitions()){this.options.highlight=false}};t.prototype={constructor:t,init:function(){var t=false,n,r;this.options.name=this.options.name||this.$element.attr("id");this.options.scope=this.$element[0];this.input=e.fn.editableutils.createInput(this.options);if(!this.input){return}if(this.options.value===undefined||this.options.value===null){this.value=this.input.html2value(e.trim(this.$element.html()));t=true}else{this.options.value=e.fn.editableutils.tryParseJson(this.options.value,true);if(typeof this.options.value==="string"){this.value=this.input.str2value(this.options.value)}else{this.value=this.options.value}}this.$element.addClass("editable");if(this.input.type==="textarea"){this.$element.addClass("editable-pre-wrapped")}if(this.options.toggle!=="manual"){this.$element.addClass("editable-click");this.$element.on(this.options.toggle+".editable",e.proxy(function(e){if(!this.options.disabled){e.preventDefault()}if(this.options.toggle==="mouseenter"){this.show()}else{var t=this.options.toggle!=="click";this.toggle(t)}},this))}else{this.$element.attr("tabindex",-1)}if(typeof this.options.display==="function"){this.options.autotext="always"}switch(this.options.autotext){case"always":n=true;break;case"auto":n=!e.trim(this.$element.text()).length&&this.value!==null&&this.value!==undefined&&!t;break;default:n=false}e.when(n?this.render():true).then(e.proxy(function(){if(this.options.disabled){this.disable()}else{this.enable()}this.$element.triggerHandler("init",this)},this))},initLive:function(){var t=this.options.selector;this.options.selector=false;this.options.autotext="never";this.$element.on(this.options.toggle+".editable",t,e.proxy(function(t){var n=e(t.target);if(!n.data("editable")){if(n.hasClass(this.options.emptyclass)){n.empty()}n.editable(this.options).trigger(t)}},this))},render:function(e){if(this.options.display===false){return}if(this.input.value2htmlFinal){return this.input.value2html(this.value,this.$element[0],this.options.display,e)}else if(typeof this.options.display==="function"){return this.options.display.call(this.$element[0],this.value,e)}else{return this.input.value2html(this.value,this.$element[0])}},enable:function(){this.options.disabled=false;this.$element.removeClass("editable-disabled");this.handleEmpty(this.isEmpty);if(this.options.toggle!=="manual"){if(this.$element.attr("tabindex")==="-1"){this.$element.removeAttr("tabindex")}}},disable:function(){this.options.disabled=true;this.hide();this.$element.addClass("editable-disabled");this.handleEmpty(this.isEmpty);this.$element.attr("tabindex",-1)},toggleDisabled:function(){if(this.options.disabled){this.enable()}else{this.disable()}},option:function(t,n){if(t&&typeof t==="object"){e.each(t,e.proxy(function(t,n){this.option(e.trim(t),n)},this));return}this.options[t]=n;if(t==="disabled"){return n?this.disable():this.enable()}if(t==="value"){this.setValue(n)}if(this.container){this.container.option(t,n)}if(this.input.option){this.input.option(t,n)}},handleEmpty:function(t){if(this.options.display===false){return}if(t!==undefined){this.isEmpty=t}else{if(typeof this.input.isEmpty==="function"){this.isEmpty=this.input.isEmpty(this.$element)}else{this.isEmpty=e.trim(this.$element.html())===""}}if(!this.options.disabled){if(this.isEmpty){this.$element.html(this.options.emptytext);if(this.options.emptyclass){this.$element.addClass(this.options.emptyclass)}}else if(this.options.emptyclass){this.$element.removeClass(this.options.emptyclass)}}else{if(this.isEmpty){this.$element.empty();if(this.options.emptyclass){this.$element.removeClass(this.options.emptyclass)}}}},show:function(t){if(this.options.disabled){return}if(!this.container){var n=e.extend({},this.options,{value:this.value,input:this.input});this.$element.editableContainer(n);this.$element.on("save.internal",e.proxy(this.save,this));this.container=this.$element.data("editableContainer")}else if(this.container.tip().is(":visible")){return}this.container.show(t)},hide:function(){if(this.container){this.container.hide()}},toggle:function(e){if(this.container&&this.container.tip().is(":visible")){this.hide()}else{this.show(e)}},save:function(e,t){if(this.options.unsavedclass){var n=false;n=n||typeof this.options.url==="function";n=n||this.options.display===false;n=n||t.response!==undefined;n=n||this.options.savenochange&&this.input.value2str(this.value)!==this.input.value2str(t.newValue);if(n){this.$element.removeClass(this.options.unsavedclass)}else{this.$element.addClass(this.options.unsavedclass)}}if(this.options.highlight){var r=this.$element,i=r.css("background-color");r.css("background-color",this.options.highlight);setTimeout(function(){if(i==="transparent"){i=""}r.css("background-color",i);r.addClass("editable-bg-transition");setTimeout(function(){r.removeClass("editable-bg-transition")},1700)},10)}this.setValue(t.newValue,false,t.response)},validate:function(){if(typeof this.options.validate==="function"){return this.options.validate.call(this,this.value)}},setValue:function(t,n,r){if(n){this.value=this.input.str2value(t)}else{this.value=t}if(this.container){this.container.option("value",this.value)}e.when(this.render(r)).then(e.proxy(function(){this.handleEmpty()},this))},activate:function(){if(this.container){this.container.activate()}},destroy:function(){this.disable();if(this.container){this.container.destroy()}this.input.destroy();if(this.options.toggle!=="manual"){this.$element.removeClass("editable-click");this.$element.off(this.options.toggle+".editable")}this.$element.off("save.internal");this.$element.removeClass("editable editable-open editable-disabled");this.$element.removeData("editable")}};e.fn.editable=function(n){var r={},i=arguments,s="editable";switch(n){case"validate":this.each(function(){var t=e(this),n=t.data(s),i;if(n&&(i=n.validate())){r[n.options.name]=i}});return r;case"getValue":if(arguments.length===2&&arguments[1]===true){r=this.eq(0).data(s).value}else{this.each(function(){var t=e(this),n=t.data(s);if(n&&n.value!==undefined&&n.value!==null){r[n.options.name]=n.input.value2submit(n.value)}})}return r;case"submit":var o=arguments[1]||{},u=this,a=this.editable("validate");if(e.isEmptyObject(a)){var f={};if(u.length===1){var l=u.data("editable");var c={name:l.options.name||"",value:l.input.value2submit(l.value),pk:typeof l.options.pk==="function"?l.options.pk.call(l.options.scope):l.options.pk};if(typeof l.options.params==="function"){c=l.options.params.call(l.options.scope,c)}else{l.options.params=e.fn.editableutils.tryParseJson(l.options.params,true);e.extend(c,l.options.params)}f={url:l.options.url,data:c,type:"POST"};o.success=o.success||l.options.success;o.error=o.error||l.options.error}else{var h=this.editable("getValue");f={url:o.url,data:h,type:"POST"}}f.success=typeof o.success==="function"?function(e){o.success.call(u,e,o)}:e.noop;f.error=typeof o.error==="function"?function(){o.error.apply(u,arguments)}:e.noop;if(o.ajaxOptions){e.extend(f,o.ajaxOptions)}if(o.data){e.extend(f.data,o.data)}e.ajax(f)}else{if(typeof o.error==="function"){o.error.call(u,a)}}return this}return this.each(function(){var r=e(this),o=r.data(s),u=typeof n==="object"&&n;if(u&&u.selector){o=new t(this,u);return}if(!o){r.data(s,o=new t(this,u))}if(typeof n==="string"){o[n].apply(o,Array.prototype.slice.call(i,1))}})};e.fn.editable.defaults={type:"text",disabled:false,toggle:"click",emptytext:"Empty",autotext:"auto",value:null,display:null,emptyclass:"editable-empty",unsavedclass:"editable-unsaved",selector:null,highlight:"#FFFF80"}})(window.jQuery);(function(e){"use strict";e.fn.editabletypes={};var t=function(){};t.prototype={init:function(t,n,r){this.type=t;this.options=e.extend({},r,n)},prerender:function(){this.$tpl=e(this.options.tpl);this.$input=this.$tpl;this.$clear=null;this.error=null},render:function(){},value2html:function(t,n){e(n)[this.options.escape?"text":"html"](e.trim(t))},html2value:function(t){return e("<div>").html(t).text()},value2str:function(e){return e},str2value:function(e){return e},value2submit:function(e){return e},value2input:function(e){this.$input.val(e)},input2value:function(){return this.$input.val()},activate:function(){if(this.$input.is(":visible")){this.$input.focus()}},clear:function(){this.$input.val(null)},escape:function(t){return e("<div>").text(t).html()},autosubmit:function(){},destroy:function(){},setClass:function(){if(this.options.inputclass){this.$input.addClass(this.options.inputclass)}},setAttr:function(e){if(this.options[e]!==undefined&&this.options[e]!==null){this.$input.attr(e,this.options[e])}},option:function(e,t){this.options[e]=t}};t.defaults={tpl:"",inputclass:null,escape:true,scope:null,showbuttons:true};e.extend(e.fn.editabletypes,{abstractinput:t})})(window.jQuery);(function(e){"use strict";var t=function(e){};e.fn.editableutils.inherit(t,e.fn.editabletypes.abstractinput);e.extend(t.prototype,{render:function(){var t=e.Deferred();this.error=null;this.onSourceReady(function(){this.renderList();t.resolve()},function(){this.error=this.options.sourceError;t.resolve()});return t.promise()},html2value:function(e){return null},value2html:function(t,n,r,i){var s=e.Deferred(),o=function(){if(typeof r==="function"){r.call(n,t,this.sourceData,i)}else{this.value2htmlFinal(t,n)}s.resolve()};if(t===null){o.call(this)}else{this.onSourceReady(o,function(){s.resolve()})}return s.promise()},onSourceReady:function(t,n){var r;if(e.isFunction(this.options.source)){r=this.options.source.call(this.options.scope);this.sourceData=null}else{r=this.options.source}if(this.options.sourceCache&&e.isArray(this.sourceData)){t.call(this);return}try{r=e.fn.editableutils.tryParseJson(r,false)}catch(i){n.call(this);return}if(typeof r==="string"){if(this.options.sourceCache){var s=r,o;if(!e(document).data(s)){e(document).data(s,{})}o=e(document).data(s);if(o.loading===false&&o.sourceData){this.sourceData=o.sourceData;this.doPrepend();t.call(this);return}else if(o.loading===true){o.callbacks.push(e.proxy(function(){this.sourceData=o.sourceData;this.doPrepend();t.call(this)},this));o.err_callbacks.push(e.proxy(n,this));return}else{o.loading=true;o.callbacks=[];o.err_callbacks=[]}}var u=e.extend({url:r,type:"get",cache:false,dataType:"json",success:e.proxy(function(r){if(o){o.loading=false}this.sourceData=this.makeArray(r);if(e.isArray(this.sourceData)){if(o){o.sourceData=this.sourceData;e.each(o.callbacks,function(){this.call()})}this.doPrepend();t.call(this)}else{n.call(this);if(o){e.each(o.err_callbacks,function(){this.call()})}}},this),error:e.proxy(function(){n.call(this);if(o){o.loading=false;e.each(o.err_callbacks,function(){this.call()})}},this)},this.options.sourceOptions);e.ajax(u)}else{this.sourceData=this.makeArray(r);if(e.isArray(this.sourceData)){this.doPrepend();t.call(this)}else{n.call(this)}}},doPrepend:function(){if(this.options.prepend===null||this.options.prepend===undefined){return}if(!e.isArray(this.prependData)){if(e.isFunction(this.options.prepend)){this.options.prepend=this.options.prepend.call(this.options.scope)}this.options.prepend=e.fn.editableutils.tryParseJson(this.options.prepend,true);if(typeof this.options.prepend==="string"){this.options.prepend={"":this.options.prepend}}this.prependData=this.makeArray(this.options.prepend)}if(e.isArray(this.prependData)&&e.isArray(this.sourceData)){this.sourceData=this.prependData.concat(this.sourceData)}},renderList:function(){},value2htmlFinal:function(e,t){},makeArray:function(t){var n,r,i=[],s,o;if(!t||typeof t==="string"){return null}if(e.isArray(t)){o=function(e,t){r={value:e,text:t};if(n++>=2){return false}};for(var u=0;u<t.length;u++){s=t[u];if(typeof s==="object"){n=0;e.each(s,o);if(n===1){i.push(r)}else if(n>1){if(s.children){s.children=this.makeArray(s.children)}i.push(s)}}else{i.push({value:s,text:s})}}}else{e.each(t,function(e,t){i.push({value:e,text:t})})}return i},option:function(e,t){this.options[e]=t;if(e==="source"){this.sourceData=null}if(e==="prepend"){this.prependData=null}}});t.defaults=e.extend({},e.fn.editabletypes.abstractinput.defaults,{source:null,prepend:false,sourceError:"Error when loading list",sourceCache:true,sourceOptions:null});e.fn.editabletypes.list=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("text",e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.abstractinput);e.extend(t.prototype,{render:function(){this.renderClear();this.setClass();this.setAttr("placeholder")},activate:function(){if(this.$input.is(":visible")){this.$input.focus();e.fn.editableutils.setCursorPosition(this.$input.get(0),this.$input.val().length);if(this.toggleClear){this.toggleClear()}}},renderClear:function(){if(this.options.clear){this.$clear=e('<span class="editable-clear-x"></span>');this.$input.after(this.$clear).css("padding-right",24).keyup(e.proxy(function(t){if(~e.inArray(t.keyCode,[40,38,9,13,27])){return}clearTimeout(this.t);var n=this;this.t=setTimeout(function(){n.toggleClear(t)},100)},this)).parent().css("position","relative");this.$clear.click(e.proxy(this.clear,this))}},postrender:function(){},toggleClear:function(e){if(!this.$clear){return}var t=this.$input.val().length,n=this.$clear.is(":visible");if(t&&!n){this.$clear.show()}if(!t&&n){this.$clear.hide()}},clear:function(){this.$clear.hide();this.$input.val("").focus()}});t.defaults=e.extend({},e.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text">',placeholder:null,clear:true});e.fn.editabletypes.text=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("textarea",e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.abstractinput);e.extend(t.prototype,{render:function(){this.setClass();this.setAttr("placeholder");this.setAttr("rows");this.$input.keydown(function(t){if(t.ctrlKey&&t.which===13){e(this).closest("form").submit()}})},activate:function(){e.fn.editabletypes.text.prototype.activate.call(this)}});t.defaults=e.extend({},e.fn.editabletypes.abstractinput.defaults,{tpl:"<textarea></textarea>",inputclass:"input-large",placeholder:null,rows:7});e.fn.editabletypes.textarea=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("select",e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.list);e.extend(t.prototype,{renderList:function(){this.$input.empty();var t=function(n,r){var i;if(e.isArray(r)){for(var s=0;s<r.length;s++){i={};if(r[s].children){i.label=r[s].text;n.append(t(e("<optgroup>",i),r[s].children))}else{i.value=r[s].value;if(r[s].disabled){i.disabled=true}n.append(e("<option>",i).text(r[s].text))}}}return n};t(this.$input,this.sourceData);this.setClass();this.$input.on("keydown.editable",function(t){if(t.which===13){e(this).closest("form").submit()}})},value2htmlFinal:function(t,n){var r="",i=e.fn.editableutils.itemsByValue(t,this.sourceData);if(i.length){r=i[0].text}e.fn.editabletypes.abstractinput.prototype.value2html.call(this,r,n)},autosubmit:function(){this.$input.off("keydown.editable").on("change.editable",function(){e(this).closest("form").submit()})}});t.defaults=e.extend({},e.fn.editabletypes.list.defaults,{tpl:"<select></select>"});e.fn.editabletypes.select=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("checklist",e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.list);e.extend(t.prototype,{renderList:function(){var t,n;this.$tpl.empty();if(!e.isArray(this.sourceData)){return}for(var r=0;r<this.sourceData.length;r++){t=e("<label>").append(e("<input>",{type:"checkbox",value:this.sourceData[r].value})).append(e("<span>").text(" "+this.sourceData[r].text));e("<div>").append(t).appendTo(this.$tpl)}this.$input=this.$tpl.find('input[type="checkbox"]');this.setClass()},value2str:function(t){return e.isArray(t)?t.sort().join(e.trim(this.options.separator)):""},str2value:function(t){var n,r=null;if(typeof t==="string"&&t.length){n=new RegExp("\\s*"+e.trim(this.options.separator)+"\\s*");r=t.split(n)}else if(e.isArray(t)){r=t}else{r=[t]}return r},value2input:function(t){this.$input.prop("checked",false);if(e.isArray(t)&&t.length){this.$input.each(function(n,r){var i=e(r);e.each(t,function(e,t){if(i.val()==t){i.prop("checked",true)}})})}},input2value:function(){var t=[];this.$input.filter(":checked").each(function(n,r){t.push(e(r).val())});return t},value2htmlFinal:function(t,n){var r=[],i=e.fn.editableutils.itemsByValue(t,this.sourceData),s=this.options.escape;if(i.length){e.each(i,function(t,n){var i=s?e.fn.editableutils.escape(n.text):n.text;r.push(i)});e(n).html(r.join("<br>"))}else{e(n).empty()}},activate:function(){this.$input.first().focus()},autosubmit:function(){this.$input.on("keydown",function(t){if(t.which===13){e(this).closest("form").submit()}})}});t.defaults=e.extend({},e.fn.editabletypes.list.defaults,{tpl:'<div class="editable-checklist"></div>',inputclass:null,separator:","});e.fn.editabletypes.checklist=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("password",e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.text);e.extend(t.prototype,{value2html:function(t,n){if(t){e(n).text("[hidden]")}else{e(n).empty()}},html2value:function(e){return null}});t.defaults=e.extend({},e.fn.editabletypes.text.defaults,{tpl:'<input type="password">'});e.fn.editabletypes.password=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("email",e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.text);t.defaults=e.extend({},e.fn.editabletypes.text.defaults,{tpl:'<input type="email">'});e.fn.editabletypes.email=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("url",e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.text);t.defaults=e.extend({},e.fn.editabletypes.text.defaults,{tpl:'<input type="url">'});e.fn.editabletypes.url=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("tel",e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.text);t.defaults=e.extend({},e.fn.editabletypes.text.defaults,{tpl:'<input type="tel">'});e.fn.editabletypes.tel=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("number",e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.text);e.extend(t.prototype,{render:function(){t.superclass.render.call(this);this.setAttr("min");this.setAttr("max");this.setAttr("step")},postrender:function(){if(this.$clear){this.$clear.css({right:24})}}});t.defaults=e.extend({},e.fn.editabletypes.text.defaults,{tpl:'<input type="number">',inputclass:"input-mini",min:null,max:null,step:null});e.fn.editabletypes.number=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("range",e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.number);e.extend(t.prototype,{render:function(){this.$input=this.$tpl.filter("input");this.setClass();this.setAttr("min");this.setAttr("max");this.setAttr("step");this.$input.on("input",function(){e(this).siblings("output").text(e(this).val())})},activate:function(){this.$input.focus()}});t.defaults=e.extend({},e.fn.editabletypes.number.defaults,{tpl:'<input type="range"><output style="width: 30px; display: inline-block"></output>',inputclass:"input-medium"});e.fn.editabletypes.range=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("time",e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.abstractinput);e.extend(t.prototype,{render:function(){this.setClass()}});t.defaults=e.extend({},e.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="time">'});e.fn.editabletypes.time=t})(window.jQuery);(function(e){"use strict";var t=function(n){this.init("select2",n,t.defaults);n.select2=n.select2||{};this.sourceData=null;if(n.placeholder){n.select2.placeholder=n.placeholder}if(!n.select2.tags&&n.source){var r=n.source;if(e.isFunction(n.source)){r=n.source.call(n.scope)}if(typeof r==="string"){n.select2.ajax=n.select2.ajax||{};if(!n.select2.ajax.data){n.select2.ajax.data=function(e){return{query:e}}}if(!n.select2.ajax.results){n.select2.ajax.results=function(e){return{results:e}}}n.select2.ajax.url=r}else{this.sourceData=this.convertSource(r);n.select2.data=this.sourceData}}this.options.select2=e.extend({},t.defaults.select2,n.select2);this.isMultiple=this.options.select2.tags||this.options.select2.multiple;this.isRemote="ajax"in this.options.select2;this.idFunc=this.options.select2.id;if(typeof this.idFunc!=="function"){var i=this.idFunc||"id";this.idFunc=function(e){return e[i]}}this.formatSelection=this.options.select2.formatSelection;if(typeof this.formatSelection!=="function"){this.formatSelection=function(e){return e.text}}};e.fn.editableutils.inherit(t,e.fn.editabletypes.abstractinput);e.extend(t.prototype,{render:function(){this.setClass();if(this.isRemote){this.$input.on("select2-loaded",e.proxy(function(e){this.sourceData=e.items.results},this))}if(this.isMultiple){this.$input.on("change",function(){e(this).closest("form").parent().triggerHandler("resize")})}},value2html:function(n,r){var i="",s,o=this;if(this.options.select2.tags){s=n}else if(this.sourceData){s=e.fn.editableutils.itemsByValue(n,this.sourceData,this.idFunc)}else{}if(e.isArray(s)){i=[];e.each(s,function(e,t){i.push(t&&typeof t==="object"?o.formatSelection(t):t)})}else if(s){i=o.formatSelection(s)}i=e.isArray(i)?i.join(this.options.viewseparator):i;t.superclass.value2html.call(this,i,r)},html2value:function(e){return this.options.select2.tags?this.str2value(e,this.options.viewseparator):null},value2input:function(t){if(e.isArray(t)){t=t.join(this.getSeparator())}if(!this.$input.data("select2")){this.$input.val(t);this.$input.select2(this.options.select2)}else{this.$input.val(t).trigger("change",true)}if(this.isRemote&&!this.isMultiple&&!this.options.select2.initSelection){var n=this.options.select2.id,r=this.options.select2.formatSelection;if(!n&&!r){var i=e(this.options.scope);if(!i.data("editable").isEmpty){var s={id:t,text:i.text()};this.$input.select2("data",s)}}}},input2value:function(){return this.$input.select2("val")},str2value:function(t,n){if(typeof t!=="string"||!this.isMultiple){return t}n=n||this.getSeparator();var r,i,s;if(t===null||t.length<1){return null}r=t.split(n);for(i=0,s=r.length;i<s;i=i+1){r[i]=e.trim(r[i])}return r},autosubmit:function(){this.$input.on("change",function(t,n){if(!n){e(this).closest("form").submit()}})},getSeparator:function(){return this.options.select2.separator||e.fn.select2.defaults.separator},convertSource:function(t){if(e.isArray(t)&&t.length&&t[0].value!==undefined){for(var n=0;n<t.length;n++){if(t[n].value!==undefined){t[n].id=t[n].value;delete t[n].value}}}return t},destroy:function(){if(this.$input.data("select2")){this.$input.select2("destroy")}}});t.defaults=e.extend({},e.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="hidden">',select2:null,placeholder:null,source:null,viewseparator:", "});e.fn.editabletypes.select2=t})(window.jQuery);(function(e){var t=function(t,n){this.$element=e(t);if(!this.$element.is("input")){e.error("Combodate should be applied to INPUT element");return}this.options=e.extend({},e.fn.combodate.defaults,n,this.$element.data());this.init()};t.prototype={constructor:t,init:function(){this.map={day:["D","date"],month:["M","month"],year:["Y","year"],hour:["[Hh]","hours"],minute:["m","minutes"],second:["s","seconds"],ampm:["[Aa]",""]};this.$widget=e('<span class="combodate"></span>').html(this.getTemplate());this.initCombos();this.$widget.on("change","select",e.proxy(function(t){this.$element.val(this.getValue()).change();if(this.options.smartDays){if(e(t.target).is(".month")||e(t.target).is(".year")){this.fillCombo("day")}}},this));this.$widget.find("select").css("width","auto");this.$element.hide().after(this.$widget);this.setValue(this.$element.val()||this.options.value)},getTemplate:function(){var t=this.options.template;e.each(this.map,function(e,n){n=n[0];var r=new RegExp(n+"+"),i=n.length>1?n.substring(1,2):n;t=t.replace(r,"{"+i+"}")});t=t.replace(/ /g,"&nbsp;");e.each(this.map,function(e,n){n=n[0];var r=n.length>1?n.substring(1,2):n;t=t.replace("{"+r+"}",'<select class="'+e+'"></select>')});return t},initCombos:function(){for(var e in this.map){var t=this.$widget.find("."+e);this["$"+e]=t.length?t:null;this.fillCombo(e)}},fillCombo:function(e){var t=this["$"+e];if(!t){return}var n="fill"+e.charAt(0).toUpperCase()+e.slice(1);var r=this[n]();var i=t.val();t.empty();for(var s=0;s<r.length;s++){t.append('<option value="'+r[s][0]+'">'+r[s][1]+"</option>")}t.val(i)},fillCommon:function(e){var t=[],n;if(this.options.firstItem==="name"){n=moment.relativeTime||moment.langData()._relativeTime;var r=typeof n[e]==="function"?n[e](1,true,e,false):n[e];r=r.split(" ").reverse()[0];t.push(["",r])}else if(this.options.firstItem==="empty"){t.push(["",""])}return t},fillDay:function(){var e=this.fillCommon("d"),t,n,r=this.options.template.indexOf("DD")!==-1,i=31;if(this.options.smartDays&&this.$month&&this.$year){var s=parseInt(this.$month.val(),10);var o=parseInt(this.$year.val(),10);if(!isNaN(s)&&!isNaN(o)){i=moment([o,s]).daysInMonth()}}for(n=1;n<=i;n++){t=r?this.leadZero(n):n;e.push([n,t])}return e},fillMonth:function(){var e=this.fillCommon("M"),t,n,r=this.options.template.indexOf("MMMM")!==-1,i=this.options.template.indexOf("MMM")!==-1,s=this.options.template.indexOf("MM")!==-1;for(n=0;n<=11;n++){if(r){t=moment().date(1).month(n).format("MMMM")}else if(i){t=moment().date(1).month(n).format("MMM")}else if(s){t=this.leadZero(n+1)}else{t=n+1}e.push([n,t])}return e},fillYear:function(){var e=[],t,n,r=this.options.template.indexOf("YYYY")!==-1;for(n=this.options.maxYear;n>=this.options.minYear;n--){t=r?n:(n+"").substring(2);e[this.options.yearDescending?"push":"unshift"]([n,t])}e=this.fillCommon("y").concat(e);return e},fillHour:function(){var e=this.fillCommon("h"),t,n,r=this.options.template.indexOf("h")!==-1,i=this.options.template.indexOf("H")!==-1,s=this.options.template.toLowerCase().indexOf("hh")!==-1,o=r?1:0,u=r?12:23;for(n=o;n<=u;n++){t=s?this.leadZero(n):n;e.push([n,t])}return e},fillMinute:function(){var e=this.fillCommon("m"),t,n,r=this.options.template.indexOf("mm")!==-1;for(n=0;n<=59;n+=this.options.minuteStep){t=r?this.leadZero(n):n;e.push([n,t])}return e},fillSecond:function(){var e=this.fillCommon("s"),t,n,r=this.options.template.indexOf("ss")!==-1;for(n=0;n<=59;n+=this.options.secondStep){t=r?this.leadZero(n):n;e.push([n,t])}return e},fillAmpm:function(){var e=this.options.template.indexOf("a")!==-1,t=this.options.template.indexOf("A")!==-1,n=[["am",e?"am":"AM"],["pm",e?"pm":"PM"]];return n},getValue:function(t){var n,r={},i=this,s=false;e.each(this.map,function(e,t){if(e==="ampm"){return}var n=e==="day"?1:0;r[e]=i["$"+e]?parseInt(i["$"+e].val(),10):n;if(isNaN(r[e])){s=true;return false}});if(s){return""}if(this.$ampm){if(r.hour===12){r.hour=this.$ampm.val()==="am"?0:12}else{r.hour=this.$ampm.val()==="am"?r.hour:r.hour+12}}n=moment([r.year,r.month,r.day,r.hour,r.minute,r.second]);this.highlight(n);t=t===undefined?this.options.format:t;if(t===null){return n.isValid()?n:null}else{return n.isValid()?n.format(t):""}},setValue:function(t){function s(t,n){var r={};t.children("option").each(function(t,i){var s=e(i).attr("value"),o;if(s==="")return;o=Math.abs(s-n);if(typeof r.distance==="undefined"||o<r.distance){r={value:s,distance:o}}});return r.value}if(!t){return}var n=typeof t==="string"?moment(t,this.options.format):moment(t),r=this,i={};if(n.isValid()){e.each(this.map,function(e,t){if(e==="ampm"){return}i[e]=n[t[1]]()});if(this.$ampm){if(i.hour>=12){i.ampm="pm";if(i.hour>12){i.hour-=12}}else{i.ampm="am";if(i.hour===0){i.hour=12}}}e.each(i,function(e,t){if(r["$"+e]){if(e==="minute"&&r.options.minuteStep>1&&r.options.roundTime){t=s(r["$"+e],t)}if(e==="second"&&r.options.secondStep>1&&r.options.roundTime){t=s(r["$"+e],t)}r["$"+e].val(t)}});if(this.options.smartDays){this.fillCombo("day")}this.$element.val(n.format(this.options.format)).change()}},highlight:function(e){if(!e.isValid()){if(this.options.errorClass){this.$widget.addClass(this.options.errorClass)}else{if(!this.borderColor){this.borderColor=this.$widget.find("select").css("border-color")}this.$widget.find("select").css("border-color","red")}}else{if(this.options.errorClass){this.$widget.removeClass(this.options.errorClass)}else{this.$widget.find("select").css("border-color",this.borderColor)}}},leadZero:function(e){return e<=9?"0"+e:e},destroy:function(){this.$widget.remove();this.$element.removeData("combodate").show()}};e.fn.combodate=function(n){var r,i=Array.apply(null,arguments);i.shift();if(n==="getValue"&&this.length&&(r=this.eq(0).data("combodate"))){return r.getValue.apply(r,i)}return this.each(function(){var r=e(this),s=r.data("combodate"),o=typeof n=="object"&&n;if(!s){r.data("combodate",s=new t(this,o))}if(typeof n=="string"&&typeof s[n]=="function"){s[n].apply(s,i)}})};e.fn.combodate.defaults={format:"DD-MM-YYYY HH:mm",template:"D / MMM / YYYY   H : mm",value:null,minYear:1970,maxYear:2015,yearDescending:true,minuteStep:5,secondStep:1,firstItem:"empty",errorClass:null,roundTime:true,smartDays:false}})(window.jQuery);(function(e){"use strict";var t=function(n){this.init("combodate",n,t.defaults);if(!this.options.viewformat){this.options.viewformat=this.options.format}n.combodate=e.fn.editableutils.tryParseJson(n.combodate,true);this.options.combodate=e.extend({},t.defaults.combodate,n.combodate,{format:this.options.format,template:this.options.template})};e.fn.editableutils.inherit(t,e.fn.editabletypes.abstractinput);e.extend(t.prototype,{render:function(){this.$input.combodate(this.options.combodate);if(e.fn.editableform.engine==="bs3"){this.$input.siblings().find("select").addClass("form-control")}if(this.options.inputclass){this.$input.siblings().find("select").addClass(this.options.inputclass)}},value2html:function(e,n){var r=e?e.format(this.options.viewformat):"";t.superclass.value2html.call(this,r,n)},html2value:function(e){return e?moment(e,this.options.viewformat):null},value2str:function(e){return e?e.format(this.options.format):""},str2value:function(e){return e?moment(e,this.options.format):null},value2submit:function(e){return this.value2str(e)},value2input:function(e){this.$input.combodate("setValue",e)},input2value:function(){return this.$input.combodate("getValue",null)},activate:function(){this.$input.siblings(".combodate").find("select").eq(0).focus()},autosubmit:function(){}});t.defaults=e.extend({},e.fn.editabletypes.abstractinput.defaults,{tpl:'<input type="text">',inputclass:null,format:"YYYY-MM-DD",viewformat:null,template:"D / MMM / YYYY",combodate:null});e.fn.editabletypes.combodate=t})(window.jQuery);(function(e){"use strict";var t=e.fn.editableform.Constructor.prototype.initInput;e.extend(e.fn.editableform.Constructor.prototype,{initTemplate:function(){this.$form=e(e.fn.editableform.template);this.$form.find(".control-group").addClass("form-group");this.$form.find(".editable-error-block").addClass("help-block")},initInput:function(){t.apply(this);var n=this.input.options.inputclass===null||this.input.options.inputclass===false;var r="input-sm";var i="text,select,textarea,password,email,url,tel,number,range,time,typeaheadjs".split(",");if(~e.inArray(this.input.type,i)){this.input.$input.addClass("form-control");if(n){this.input.options.inputclass=r;this.input.$input.addClass(r)}}var s=this.$form.find(".editable-buttons");var o=n?[r]:this.input.options.inputclass.split(" ");for(var u=0;u<o.length;u++){if(o[u].toLowerCase()==="input-lg"){s.find("button").removeClass("btn-sm").addClass("btn-lg")}}}});e.fn.editableform.buttons='<button type="submit" class="btn btn-primary btn-sm editable-submit">'+'<i class="glyphicon glyphicon-ok"></i>'+"</button>"+'<button type="button" class="btn btn-default btn-sm editable-cancel">'+'<i class="glyphicon glyphicon-remove"></i>'+"</button>";e.fn.editableform.errorGroupClass="has-error";e.fn.editableform.errorBlockClass=null;e.fn.editableform.engine="bs3"})(window.jQuery);(function(e){"use strict";e.extend(e.fn.editableContainer.Popup.prototype,{containerName:"popover",containerDataName:"bs.popover",innerCss:".popover-content",defaults:e.fn.popover.Constructor.DEFAULTS,initContainer:function(){e.extend(this.containerOptions,{trigger:"manual",selector:false,content:" ",template:this.defaults.template});var t;if(this.$element.data("template")){t=this.$element.data("template");this.$element.removeData("template")}this.call(this.containerOptions);if(t){this.$element.data("template",t)}},innerShow:function(){this.call("show")},innerHide:function(){this.call("hide")},innerDestroy:function(){this.call("destroy")},setContainerOption:function(e,t){this.container().options[e]=t},setPosition:function(){(function(){var e=this.tip();var t=typeof this.options.placement=="function"?this.options.placement.call(this,e[0],this.$element[0]):this.options.placement;var n=/\s?auto?\s?/i;var r=n.test(t);if(r){t=t.replace(n,"")||"top"}var i=this.getPosition();var s=e[0].offsetWidth;var o=e[0].offsetHeight;if(r){var u=this.$element.parent();var a=t;var f=document.documentElement.scrollTop||document.body.scrollTop;var l=this.options.container=="body"?window.innerWidth:u.outerWidth();var c=this.options.container=="body"?window.innerHeight:u.outerHeight();var h=this.options.container=="body"?0:u.offset().left;t=t=="bottom"&&i.top+i.height+o-f>c?"top":t=="top"&&i.top-f-o<0?"bottom":t=="right"&&i.right+s>l?"left":t=="left"&&i.left-s<h?"right":t;e.removeClass(a).addClass(t)}var p=this.getCalculatedOffset(t,i,s,o);this.applyPlacement(p,t)}).call(this.container())}})})(window.jQuery);(function(e){"use strict";e.fn.bdatepicker=e.fn.datepicker.noConflict();if(!e.fn.datepicker){e.fn.datepicker=e.fn.bdatepicker}var t=function(e){this.init("date",e,t.defaults);this.initPicker(e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.abstractinput);e.extend(t.prototype,{initPicker:function(t,n){if(!this.options.viewformat){this.options.viewformat=this.options.format}t.datepicker=e.fn.editableutils.tryParseJson(t.datepicker,true);this.options.datepicker=e.extend({},n.datepicker,t.datepicker,{format:this.options.viewformat});this.options.datepicker.language=this.options.datepicker.language||"en";this.dpg=e.fn.bdatepicker.DPGlobal;this.parsedFormat=this.dpg.parseFormat(this.options.format);this.parsedViewFormat=this.dpg.parseFormat(this.options.viewformat)},render:function(){this.$input.bdatepicker(this.options.datepicker);if(this.options.clear){this.$clear=e('<a href="#"></a>').html(this.options.clear).click(e.proxy(function(e){e.preventDefault();e.stopPropagation();this.clear()},this));this.$tpl.parent().append(e('<div class="editable-clear">').append(this.$clear))}},value2html:function(e,n){var r=e?this.dpg.formatDate(e,this.parsedViewFormat,this.options.datepicker.language):"";t.superclass.value2html.call(this,r,n)},html2value:function(e){return this.parseDate(e,this.parsedViewFormat)},value2str:function(e){return e?this.dpg.formatDate(e,this.parsedFormat,this.options.datepicker.language):""},str2value:function(e){return this.parseDate(e,this.parsedFormat)},value2submit:function(e){return this.value2str(e)},value2input:function(e){this.$input.bdatepicker("update",e)},input2value:function(){return this.$input.data("datepicker").date},activate:function(){},clear:function(){this.$input.data("datepicker").date=null;this.$input.find(".active").removeClass("active");if(!this.options.showbuttons){this.$input.closest("form").submit()}},autosubmit:function(){this.$input.on("mouseup",".day",function(t){if(e(t.currentTarget).is(".old")||e(t.currentTarget).is(".new")){return}var n=e(this).closest("form");setTimeout(function(){n.submit()},200)})},parseDate:function(e,t){var n=null,r;if(e){n=this.dpg.parseDate(e,t,this.options.datepicker.language);if(typeof e==="string"){r=this.dpg.formatDate(n,t,this.options.datepicker.language);if(e!==r){n=null}}}return n}});t.defaults=e.extend({},e.fn.editabletypes.abstractinput.defaults,{tpl:'<div class="editable-date well"></div>',inputclass:null,format:"yyyy-mm-dd",viewformat:null,datepicker:{weekStart:0,startView:0,minViewMode:0,autoclose:false},clear:"&times; clear"});e.fn.editabletypes.date=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("datefield",e,t.defaults);this.initPicker(e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.date);e.extend(t.prototype,{render:function(){this.$input=this.$tpl.find("input");this.setClass();this.setAttr("placeholder");this.$tpl.bdatepicker(this.options.datepicker);this.$input.off("focus keydown");this.$input.keyup(e.proxy(function(){this.$tpl.removeData("date");this.$tpl.bdatepicker("update")},this))},value2input:function(e){this.$input.val(e?this.dpg.formatDate(e,this.parsedViewFormat,this.options.datepicker.language):"");this.$tpl.bdatepicker("update")},input2value:function(){return this.html2value(this.$input.val())},activate:function(){e.fn.editabletypes.text.prototype.activate.call(this)},autosubmit:function(){}});t.defaults=e.extend({},e.fn.editabletypes.date.defaults,{tpl:'<div class="input-append date"><input type="text"/><span class="add-on"><i class="icon-th"></i></span></div>',inputclass:"input-small",datepicker:{weekStart:0,startView:0,minViewMode:0,autoclose:true}});e.fn.editabletypes.datefield=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("datetime",e,t.defaults);this.initPicker(e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.abstractinput);e.extend(t.prototype,{initPicker:function(t,n){if(!this.options.viewformat){this.options.viewformat=this.options.format}t.datetimepicker=e.fn.editableutils.tryParseJson(t.datetimepicker,true);this.options.datetimepicker=e.extend({},n.datetimepicker,t.datetimepicker,{format:this.options.viewformat});this.options.datetimepicker.language=this.options.datetimepicker.language||"en";this.dpg=e.fn.datetimepicker.DPGlobal;this.parsedFormat=this.dpg.parseFormat(this.options.format,this.options.formatType);this.parsedViewFormat=this.dpg.parseFormat(this.options.viewformat,this.options.formatType)},render:function(){this.$input.datetimepicker(this.options.datetimepicker);this.$input.on("changeMode",function(t){var n=e(this).closest("form").parent();setTimeout(function(){n.triggerHandler("resize")},0)});if(this.options.clear){this.$clear=e('<a href="#"></a>').html(this.options.clear).click(e.proxy(function(e){e.preventDefault();e.stopPropagation();this.clear()},this));this.$tpl.parent().append(e('<div class="editable-clear">').append(this.$clear))}},value2html:function(e,n){var r=e?this.dpg.formatDate(this.toUTC(e),this.parsedViewFormat,this.options.datetimepicker.language,this.options.formatType):"";if(n){t.superclass.value2html.call(this,r,n)}else{return r}},html2value:function(e){var t=this.parseDate(e,this.parsedViewFormat);return t?this.fromUTC(t):null},value2str:function(e){return e?this.dpg.formatDate(this.toUTC(e),this.parsedFormat,this.options.datetimepicker.language,this.options.formatType):""},str2value:function(e){var t=this.parseDate(e,this.parsedFormat);return t?this.fromUTC(t):null},value2submit:function(e){return this.value2str(e)},value2input:function(e){if(e){this.$input.data("datetimepicker").setDate(e)}},input2value:function(){var e=this.$input.data("datetimepicker");return e.date?e.getDate():null},activate:function(){},clear:function(){this.$input.data("datetimepicker").date=null;this.$input.find(".active").removeClass("active");if(!this.options.showbuttons){this.$input.closest("form").submit()}},autosubmit:function(){this.$input.on("mouseup",".minute",function(t){var n=e(this).closest("form");setTimeout(function(){n.submit()},200)})},toUTC:function(e){return e?new Date(e.valueOf()-e.getTimezoneOffset()*6e4):e},fromUTC:function(e){return e?new Date(e.valueOf()+e.getTimezoneOffset()*6e4):e},parseDate:function(e,t){var n=null,r;if(e){n=this.dpg.parseDate(e,t,this.options.datetimepicker.language,this.options.formatType);if(typeof e==="string"){r=this.dpg.formatDate(n,t,this.options.datetimepicker.language,this.options.formatType);if(e!==r){n=null}}}return n}});t.defaults=e.extend({},e.fn.editabletypes.abstractinput.defaults,{tpl:'<div class="editable-date well"></div>',inputclass:null,format:"yyyy-mm-dd hh:ii",formatType:"standard",viewformat:null,datetimepicker:{todayHighlight:false,autoclose:false},clear:"&times; clear"});e.fn.editabletypes.datetime=t})(window.jQuery);(function(e){"use strict";var t=function(e){this.init("datetimefield",e,t.defaults);this.initPicker(e,t.defaults)};e.fn.editableutils.inherit(t,e.fn.editabletypes.datetime);e.extend(t.prototype,{render:function(){this.$input=this.$tpl.find("input");this.setClass();this.setAttr("placeholder");this.$tpl.datetimepicker(this.options.datetimepicker);this.$input.off("focus keydown");this.$input.keyup(e.proxy(function(){this.$tpl.removeData("date");this.$tpl.datetimepicker("update")},this))},value2input:function(e){this.$input.val(this.value2html(e));this.$tpl.datetimepicker("update")},input2value:function(){return this.html2value(this.$input.val())},activate:function(){e.fn.editabletypes.text.prototype.activate.call(this)},autosubmit:function(){}});t.defaults=e.extend({},e.fn.editabletypes.datetime.defaults,{tpl:'<div class="input-append date"><input type="text"/><span class="add-on"><i class="icon-th"></i></span></div>',inputclass:"input-medium",datetimepicker:{todayHighlight:false,autoclose:true}});e.fn.editabletypes.datetimefield=t})(window.jQuery)