function us_message(message, type) {

    type = typeof type !== 'undefined' ? type : 'warning';

    var html = '';

    html += '<div class = "row">';
    html += '<div class = "col-lg-2 col-md-2 col-sm-2 col-xs-2">';
    html += '<img src = "' + $(document).data('admin_url') + '/images/alert/' + type + '.png">';
    html += '</div>';
    html += '<div class = "col-lg-10 col-md-10 col-sm-10 col-xs-10">';
    html += message;
    html += '</div>';
    html += '</div>';

    return html;
}

$.fn.loadingBar = function (replace) {
    var loadingObj = $('<div class="text-center us-loading-bar"><img class="us-loading-bar" src="' + $(document).data('admin_url') + '/images/system/loading_bar.gif"></div>');
    if (replace)
    {
        this.html(loadingObj);
    }
    else
    {
        this.append(loadingObj);
    }
    return this;
};


