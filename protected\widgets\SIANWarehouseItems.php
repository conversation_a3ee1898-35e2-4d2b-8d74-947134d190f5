<?php

class SIANWarehouseItems extends CWidget {

    public $id;
    public $model;
    public $dataProvider;
    public $route = '/logistic/merchandise/preview';
    public $kardex_route = '/logistic/merchandise/kardex';
    public $serie_route = '/logistic/merchandise/serie';
    public $batch_movement_route = '/logistic/batchMovement/preview';
    public $title = 'Lista de mercaderías';
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //CONTROL
        if (is_null($this->model)) {
            throw new Exception("Debe especificar la instancia del modelo 'WarehouseMovement'!");
        }
        if (is_null($this->dataProvider)) {
            throw new Exception("Debe especificar el dataProvider!");
        }
        //
        $this->dataProvider->pagination = false;
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<h4>{$this->title}:</h4>";

        //Accesos
        $grid_id = $this->id;
        $route = $this->route;
        $kardex_route = $this->kardex_route;
        $serie_route = $this->serie_route;
        $batch_movement_route = $this->batch_movement_route;
        $access = $this->controller->checkRoute($route);
        $kardex_access = $this->controller->checkRoute($kardex_route);
        $serie_access = $this->controller->checkRoute($serie_route);
        $currency = $this->model->movement->currency;
        //GRID
        $columns = [];

        array_push($columns, array(
            'name' => 'item_number',
            'headerHtmlOptions' => array('style' => 'width:3%; text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center;'),
            'type' => 'raw',
            'value' => function($row) {
                return $row->item_number;
            },
        ));

        array_push($columns, array(
            'name' => 'product_id',
            'headerHtmlOptions' => array('style' => 'width:5%; text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function($row) {
                return $row->product_id;
            },
        ));

        array_push($columns, array(
            'name' => 'product_name',
            'headerHtmlOptions' => array('style' => 'width:35%;'),
            'type' => 'raw',
            'value' => function($row) use ($access, $route) {

                $s_preffix = "";
                if (isset($row->promotion_item_id)) {
                    $s_preffix = "<i title='En promoción' class='fa fa-lg fa-star blue'></i> ";
                } elseif (isset($row->promotion_gift_option_id)) {
                    $s_preffix = "<i title='Regalo' class='fa fa-lg fa-gift blue'></i> ";
                }

                $label = $row->product_name;

                if(isset($row->is_waste) && $row->is_waste == 1) {
                    $label = $label . " (MERMA)"; 
                }

                return $s_preffix . Yii::app()->controller->widget('application.widgets.USLink', array(
                            'route' => $route,
                            'label' => $label,
                            'title' => 'Ver producto',
                            'class' => 'form',
                            'data' => array(
                                'id' => $row->product_id
                            ),
                            'visible' => $access,
                                ), true);
            },
        ));

        array_push($columns, array(
            'name' => 'pres_quantity',
            'headerHtmlOptions' => array('style' => 'width:10%;text-align:right;'),
            'htmlOptions' => array('style' => 'text-align:right'),
            'type' => 'raw',
            'value' => function($row) {
                return $row->pres_quantity;
            },
        ));

        array_push($columns, array(
            'name' => 'measure_name',
            'headerHtmlOptions' => array('style' => 'width:10%;text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function($row) {
                return $row->measure_name;
            },
        ));

        array_push($columns, array(
            'name' => "cost_{$currency}",
            'headerHtmlOptions' => array('style' => 'width:10%;text-align:right;'),
            'htmlOptions' => array('style' => 'text-align:right;'),
            'type' => 'raw',
            'value' => function($row) {
                return $row->displayValue('cost', $row->currency, WarehouseMovement::IFIXED);
            },
        ));

        array_push($columns, array(
            'name' => "net_{$currency}",
            'headerHtmlOptions' => array('style' => 'width:10%; text-align:right;'),
            'htmlOptions' => array('style' => 'text-align:right'),
            'type' => 'raw',
            'value' => function($row) {
                return $row->displayValue('net', $row->currency);
            },
        ));

        array_push($columns, array(
            'name' => 'series',
            'headerHtmlOptions' => array('style' => 'width:15%; text-align:right;'),
            'htmlOptions' => array('style' => 'text-align:right'),
            'footerHtmlOptions' => array('style' => 'text-align:right'),
            'type' => 'raw',
            'value' => function($row) use ($serie_access) {
                if (count($row->series) > 0) {
                    $html = "";
                    foreach ($row->series as $serie) {
                        $html .= "<p>";
                        $html .= Yii::app()->controller->widget('application.widgets.USLink', array(
                            'route' => '/logistic/merchandise/serie',
                            'label' => $serie,
                            'title' => 'Ver movimientos de la serie',
                            'params' => array(
                                'id' => $row->product_id,
                                'serie' => $serie,
                            ),
                            'target' => $row->product_id . '-' . $serie,
                            'visible' => $serie_access,
                                ), true);
                        $html .= "</p>";
                    }

                    return $html;
                } else {
                    return Strings::NONE;
                }
            },
        ));


        array_push($columns, array(
            'header' => 'Opciones',
            'headerHtmlOptions' => array('style' => 'width:6%;text-align:center;'),
            'type' => 'raw',
            'htmlOptions' => array('style' => 'text-align:center'),
            'value' => function($row) use ($grid_id, $kardex_route, $kardex_access, $batch_movement_route) {

                return Yii::app()->controller->widget('application.widgets.USGridButtons', array(
                            'id' => $row->product_id,
                            'grid_id' => $grid_id,
                            'buttons' => array(
                                array('icon' => 'fa fa-retweet fa-lg', 'title' => 'Ver kardex', 'route' => $kardex_route, 'params' => array('warehouse_id' => $row->warehouse_id), 'target' => $row->movement_id . "_" . $row->product_id, 'visible' => $kardex_access),
                                array('icon' => 'fa fa-calendar fa-lg', 'class' => 'form', 'title' => 'Ver Lotes', 'route' => $batch_movement_route, 'params' => array('id' => $row->item_id), 'visible' => ($row->use_batch == 1)),
                            )), true);
            },
        ));

        $gridParams = array(
            'id' => $this->id,
            'type' => 'hover condensed',
            'dataProvider' => $this->dataProvider,
            'enableSorting' => true,
            'selectableRows' => 0,
            'columns' => $columns,
            'template' => '{items}',
            'nullDisplay' => Strings::NONE,
        );

        $this->widget('application.widgets.USGridView', $gridParams);

        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12 us-detail'>";
        echo "<table style='table-layout: fixed'>";
        echo "<tr><td><strong>{$this->model->movement->getAttributeLabel('observation')}</strong></td></tr>";
        echo "<tr><td>" . USString::ifBlank($this->model->movement->observation, Strings::NONE) . "</td></tr>";
        echo "</table>";
        echo "</div>";
        echo "</div>";
    }

}
