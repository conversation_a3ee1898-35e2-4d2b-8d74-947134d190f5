<?php

class USQZ extends CWidget {

    public $id;
    public $test = false;
//PRIVATE
    private $controller;
    private $clientScript;
    private $assets_url;

    public function init() {
//ID
        $this->controller = Yii::app()->controller;
        $this->clientScript = Yii::app()->clientScript;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        $this->assets_url = Yii::app()->assetManager->publish(Yii::getPathOfAlias('application.widgets.assets.other.qz'));
        SIANAssets::registerScriptFile('other/qz/js/dependencies/rsvp-3.1.0.min.js');
        SIANAssets::registerScriptFile('other/qz/js/dependencies/sha-256.min.js');
        SIANAssets::registerScriptFile('other/qz/js/qz-tray.js');

        //Si es TEST
        if ($this->test) {
            $this->clientScript->registerScript('USQZ_image', "
            $(document).ready(function() { 
                window.qz_assets_url = '{$this->assets_url}';
            });
            ", CClientScript::POS_END);
            SIANAssets::registerScriptFile('other/qz/js/additional/jquery-1.11.3.min.js');
            SIANAssets::registerScriptFile('other/qz/js/additional/bootstrap.min.js');
            SIANAssets::registerScriptFile('other/qz/js/sample.js');
            SIANAssets::registerCssFile('other/qz/css/font-awesome.min.css');
            SIANAssets::registerCssFile('other/qz/css/bootstrap.min.css');
            SIANAssets::registerCssFile('other/qz/css/style.css');
        } else {
            SIANAssets::registerScriptFile('other/qz/js/us-qz.js');

            $this->clientScript->registerScript('USQZ', "
            $(document).ready(function() {
                window.qz_ready = false;
                window.qz_dropdown_id = '';
                window.qz_printer = '';
            });
        ", CClientScript::POS_END);
        }
    }

    /**
     * Runs the widget.
     */
    public function run() {
        if ($this->test) {
            echo '<div id="qz-alert" style="position: fixed; width: 60%; margin: 0 4% 0 36%; z-index: 900;"></div>';
            echo '<div id="qz-pin" style="position: fixed; width: 30%; margin: 0 66% 0 4%; z-index: 900;"></div>';
            echo '<div id="' . $this->id . '" class="container" role="main">';
            echo '<div class="row">';
            echo '<h1 id="title" class="page-header">QZ Tray v<span id="qz-version">0</span></h1>';
            echo '</div>';
            echo '<div class="row spread">';
            echo '<div class="col-md-4">';
            echo '<div id="qz-connection" class="panel panel-default">';
            echo '<div class="panel-heading">';
            echo '<button class="close tip" data-toggle="tooltip" title="Launch QZ" id="launch" href="#" onclick="launchQZ();" style="display: none;">';
            echo '<i class="fa fa-external-link"></i>';
            echo '</button>';
            echo '<h3 class="panel-title">';
            echo 'Connection: <span id="qz-status" class="text-muted" style="font-weight: bold;">Unknown</span>';
            echo '</h3>';
            echo '</div>';
            echo '<div class="panel-body">';
            echo '<div class="btn-toolbar">';
            echo '<div class="btn-group" role="group">';
            echo '<button type="button" class="btn btn-success" onclick="startConnection();">Connect</button>';
            echo '<button type="button" class="btn btn-warning" onclick="endConnection();">Disconnect</button>';
            echo '</div>';
            echo '<button type="button" class="btn btn-info" onclick="listNetworkInfo();">List Network Info</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<hr />';
            echo '<div class="panel panel-primary">';
            echo '<div class="panel-heading">';
            echo '<h3 class="panel-title">Printer</h3>';
            echo '</div>';
            echo '<div class="panel-body">';
            echo '<div class="form-group">';
            echo '<label for="printerSearch">Search:</label>';
            echo '<input type="text" id="printerSearch" value="zebra" class="form-control" />';
            echo '</div>';
            echo '<div class="form-group">';
            echo '<div class="btn-group" role="group">';
            echo '<button type="button" class="btn btn-default btn-sm" onclick="findPrinter($(\'#printerSearch\').val(), true);">Find Printer</button>';
            echo '<button type="button" class="btn btn-default btn-sm" onclick="findDefaultPrinter(true);">Find Default Printer</button>';
            echo '<button type="button" class="btn btn-default btn-sm" onclick="findPrinters();">Find All Printers</button>';
            echo '</div>';
            echo '</div>';
            echo '<hr />';
            echo '<div class="form-group">';
            echo '<label>Current printer:</label>';
            echo '<div id="configPrinter">NONE</div>';
            echo '</div>';
            echo '<div class="form-group">';
            echo '<div class="btn-group" role="group">';
            echo '<button type="button" class="btn btn-default btn-sm" onclick="setPrinter($(\'#printerSearch\').val());">Set To Search</button>';
            echo '<button type="button" class="btn btn-default btn-sm" data-toggle="modal" data-target="#askFileModal">Set To File</button>';
            echo '<button type="button" class="btn btn-default btn-sm" data-toggle="modal" data-target="#askHostModal">Set To Host</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div class="col-md-8">';
            echo '<ul class="nav nav-tabs" role="tablist">';
            echo '<li id="rawTab" role="presentation" class="active"><a href="#rawContent" role="tab" data-toggle="tab">Raw Printing</a></li>';
            echo '<li id="pxlTab" role="presentation"><a href="#pxlContent" role="tab" data-toggle="tab">Pixel Printing</a></li>';
            echo '<li id="serialTab" role="presentation"><a href="#serialContent" role="tab" data-toggle="tab">Serial</a></li>';
            echo '<li id="usbTab" role="presentation"><a href="#usbContent" role="tab" data-toggle="tab">USB</a></li>';
            echo '<li id="hidTab" role="presentation"><a href="#hidContent" role="tab" data-toggle="tab">HID</a></li>';
            echo '</ul>';
            echo '</div>';
            echo '<div class="tab-content">';
            echo '<div id="rawContent" class="tab-pane active col-md-8">';
            echo '<h3>Raw Printing</h3>';
            echo '<div class="row">';
            echo '<div class="col-md-12">';
            echo '<a href="https://qz.io/wiki/What-is-Raw-Printing" target="new">What is Raw Printing?</a>';
            echo '<span style="float: right;">';
            echo '<a href="javascript:findPrinter(\'Zebra\', true);">Zebra</a> |';
            echo '<a href="javascript:findPrinter(\'ZDesigner\', true);">ZDesigner</a> |';
            echo '<a href="javascript:findPrinter(\'Epson\', true);">Epson</a> |';
            echo '<a href="javascript:findPrinter(\'Citizen\', true);">Citizen</a> |';
            echo '<a href="javascript:findPrinter(\'Star\', true);">Star</a>';
            echo '</span>';
            echo '</div>';
            echo '</div>';
            echo '<hr />';
            echo '<div class="row">';
            echo '<div class="col-md-6">';
            echo '<div class="form-group">';
            echo '<div>';
            echo '<label>Print Sample To Type</label>';
            echo '</div>';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-default" onclick="printEPL();">EPL</button>';
            echo '<button type="button" class="btn btn-default" onclick="printZPL();">ZPL</button>';
            echo '<button type="button" class="btn btn-default" onclick="printEvolis();">Evolis</button>';
            echo '<button type="button" class="btn btn-default" onclick="printESCPOS();">ESC/POS</button>';
            echo '<button type="button" class="btn btn-default" onclick="printEPCL();">EPCL (Zebra Card Printer)</button>';
            echo '</div>';
            echo '</div>';
            echo '<div class="form-group">';
            echo '<div>';
            echo '<label>Print Data</label>';
            echo '</div>';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-default" onclick="printBase64();">Base64</button>';
            echo '<button type="button" class="btn btn-default" onclick="printXML();">XML</button>';
            echo '<button type="button" class="btn btn-default" onclick="printHex();">Hex</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div class="col-md-6">';
            echo '<div class="form-group">';
            echo '<div>';
            echo '<label>Print From File</label>';
            echo '</div>';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-default" onclick="printFile(\'zpl_sample.txt\');">zpl_sample.txt</button>';
            echo '<button type="button" class="btn btn-default" onclick="printFile(\'fgl_sample.txt\');">fgl_sample.txt</button>';
            echo '<button type="button" class="btn btn-default" onclick="printFile(\'epl_sample.txt\');">epl_sample.txt</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div class="row" style="margin-top: 1em;">';
            echo '<div class="col-md-12">';
            echo '<div class="panel panel-default">';
            echo '<div class="panel-heading">';
            echo '<h4 class="panel-title">Options</h4>';
            echo '</div>';
            echo '<div class="panel-body">';
            echo '<div class="row">';
            echo '<div class="col-md-6">';
            echo '<div class="form-group form-inline">';
            echo '<label for="rawEncoding">Encoding</label>';
            echo '<input type="text" id="rawEncoding" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="rawEndOfDoc">End Of Doc</label>';
            echo '<input type="text" id="rawEndOfDoc" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="rawPerSpool">Per Spool</label>';
            echo '<input type="number" id="rawPerSpool" class="form-control pull-right" />';
            echo '</div>';
            echo '</div>';
            echo '<div class="col-md-6">';
            echo '<div class="form-group form-inline">';
            echo '<label for="rawAltPrinting">Alternate Printing</label>';
            echo '<input type="checkbox" id="rawAltPrinting" class="pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="rawCopies">Copies</label>';
            echo '<input type="number" id="rawCopies" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label class="tip" for="rawJobName" data-toggle="tooltip" title="Job title as it appears in print queue">';
            echo 'Job Name';
            echo '</label>';
            echo '<input type="text" id="rawJobName" class="form-control pull-right" />';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<hr />';
            echo '<div class="row">';
            echo '<div class="col-md-12">';
            echo '<button type="button" class="btn btn-danger pull-right" onclick="resetRawOptions();">Reset</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div id="pxlContent" class="tab-pane col-md-8">';
            echo '<h3>Pixel Printing</h3>';
            echo '<div class="row">';
            echo '<div class="col-md-12">';
            echo '<a href="https://qz.io/wiki/2.0-pixel-printing" target="new">What is Pixel Printing?</a>';
            echo '<span style="float: right;">';
            echo '<a href="javascript:findPrinter(\'XPS\', true);">Microsoft XPS</a> |';
            echo '<a href="javascript:findPrinter(\'PDF\', true);">PDF</a>';
            echo '</span>';
            echo '</div>';
            echo '</div>';
            echo '<hr />';
            echo '<div class="row">';
            echo '<div class="col-md-12">';
            echo '<div class="form-group">';
            echo '<button type="button" class="btn btn-default" onclick="printHTML();">Print HTML</button>';
            echo '<button type="button" class="btn btn-default" onclick="printPDF();">Print PDF</button>';
            echo '<button type="button" class="btn btn-default" onclick="printImage();">Print Image</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div class="row" style="margin-top: 1em;">';
            echo '<div class="col-md-12">';
            echo '<div class="panel panel-default">';
            echo '<div class="panel-heading">';
            echo '<h4 class="panel-title">Options</h4>';
            echo '</div>';
            echo '<div class="panel-body">';
            echo '<div class="row">';
            echo '<div class="col-md-6">';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlColorType">Color Type</label>';
            echo '<select id="pxlColorType" class="form-control pull-right">';
            echo '<option value="color">Color</option>';
            echo '<option value="grayscale">Grayscale</option>';
            echo '<option value="blackwhite">Black & White</option>';
            echo '</select>';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlCopies">Copies</label>';
            echo '<input type="number" id="pxlCopies" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlDensity" class="tip" data-toggle="tooltip"';
            echo 'title="DPI, DPCM, or DPMM depending on units specified">Density';
            echo '</label>';
            echo '<input type="number" id="pxlDensity" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlDuplex"> Duplex</label>';
            echo '<input type="checkbox" id="pxlDuplex" class="pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlInterpolation">Interpolation</label>';
            echo '<select id="pxlInterpolation" class="form-control pull-right">';
            echo '<option value="">Default</option>';
            echo '<option value="bicubic">Bicubic</option>';
            echo '<option value="bilinear">Bilinear</option>';
            echo '<option value="nearest-neighbor">Nearest Neighbor</option>';
            echo '</select>';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label class="tip" for="pxlJobName" data-toggle="tooltip" title="Job title as it appears in print queue">';
            echo 'Job Name';
            echo '</label>';
            echo '<input type="text" id="pxlJobName" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlLegacy">Legacy Printing</label>';
            echo '<input type="checkbox" id="pxlLegacy" class="pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlOrientation">Orientation</label>';
            echo '<select id="pxlOrientation" class="form-control pull-right">';
            echo '<option value="">Default</option>';
            echo '<option value="portrait">Portrait</option>';
            echo '<option value="landscape">Landscape</option>';
            echo '<option value="reverse-landscape">Landscape - Reverse</option>';
            echo '</select>';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlPaperThickness">Paper<br />Thickness</label>';
            echo '<input disabled type="number" step="any" id="pxlPaperThickness" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlPrinterTray">Printer Tray</label>';
            echo '<input disabled type="text" id="pxlPrinterTray" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlRasterize">Rasterize</label>';
            echo '<input type="checkbox" id="pxlRasterize" class="pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlRotation">Rotation</label>';
            echo '<input type="number" step="any" id="pxlRotation" class="form-control pull-right" />';
            echo '</div>';
            echo '</div>';
            echo '<div class="col-md-6">';
            echo '<div class="form-group">';
            echo '<label>Units</label>';
            echo '<div>';
            echo '<label>';
            echo '<input type="radio" name="pxlUnits" id="pxlUnitsIN" value="in" />';
            echo 'IN';
            echo '</label>';
            echo '<label>';
            echo '<input type="radio" name="pxlUnits" id="pxlUnitsMM" value="mm" />';
            echo 'MM';
            echo '</label>';
            echo '<label>';
            echo '<input type="radio" name="pxlUnits" id="pxlUnitsCM" value="cm" />';
            echo 'CM';
            echo '</label>';
            echo '</div>';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlScale">Scale Content:</label>';
            echo '<input type="checkbox" id="pxlScale" class="pull-right" />';
            echo '</div>';
            echo '<div class="form-group">';
            echo '<label for="pxlMargins" class="tip" data-toggle="tooltip" title="In relation to units specified">Margins</label>';
            echo '(';
            echo '<label for="pxlMarginsActive" class="inline">Individual:</label>';
            echo '<input type="checkbox" id="pxlMarginsActive" onclick="checkMarginsActive();">';
            echo ')';
            echo '<input type="number" step="any" id="pxlMargins" class="form-control" />';
            echo '</div>';
            echo '<div class="inline" id="pxlMarginsGroup">';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlMarginsTop">&nbsp; Top:</label>';
            echo '<input type="number" step="any" id="pxlMarginsTop" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlMarginsRight">&nbsp; Right:</label>';
            echo '<input type="number" step="any" id="pxlMarginsRight" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlMarginsBottom">&nbsp; Bottom:</label>';
            echo '<input type="number" step="any" id="pxlMarginsBottom" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlMarginsLeft">&nbsp; Left:</label>';
            echo '<input type="number" step="any" id="pxlMarginsLeft" class="form-control pull-right" />';
            echo '</div>';
            echo '</div>';
            echo '<div class="form-group">';
            echo '<label class="inline">Size</label>';
            echo '(';
            echo '<label for="pxlSizeActive" class="inline">Enable:</label>';
            echo '<input type="checkbox" id="pxlSizeActive" onclick="checkSizeActive();" />';
            echo ')';
            echo '</div>';
            echo '<div class="inline" id="pxlSizeGroup">';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlSizeWidth" class="tip" data-toggle="tooltip" title="In relation to units specified">';
            echo '&nbsp; Width:';
            echo '</label>';
            echo '<input type="number" step="any" id="pxlSizeWidth" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="pxlSizeHeight" class="tip" data-toggle="tooltip" title="In relation to units specified">';
            echo '&nbsp; Height:';
            echo '</label>';
            echo '<input type="number" step="any" id="pxlSizeHeight" class="form-control pull-right" />';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<hr />';
            echo '<div class="row">';
            echo '<div class="col-md-12">';
            echo '<button type="button" class="btn btn-danger pull-right" onclick="resetPixelOptions();">Reset</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div id="serialContent" class="tab-pane col-md-8">';
            echo '<h3>Serial</h3>';
            echo '<hr />';
            echo '<div class="row">';
            echo '<div class="col-md-12">';
            echo '<div class="btn-toolbar">';
            echo '<button type="button" class="btn btn-info" onclick="listSerialPorts();">List Ports</button>';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-success" onclick="openSerialPort();">Open Port</button>';
            echo '<button type="button" class="btn btn-warning" onclick="closeSerialPort();">Close Port</button>';
            echo '</div>';
            echo '<button type="button" class="btn btn-default" onclick="sendSerialData();">Send Command</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div class="row" style="margin-top: 1em;">';
            echo '<div class="col-md-12">';
            echo '<div class="panel panel-default">';
            echo '<div class="panel-heading">';
            echo '<h4 class="panel-title">Options</h4>';
            echo '</div>';
            echo '<div class="panel-body">';
            echo '<div class="row">';
            echo '<div class="col-md-6">';
            echo '<div class="form-group form-inline">';
            echo '<label for="serialPort">Port</label>';
            echo '<input type="text" id="serialPort" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="serialCmd">Command</label>';
            echo '<input type="text" id="serialCmd" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="serialStart">Response Start</label>';
            echo '<input type="text" id="serialStart" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="serialEnd">Response End</label>';
            echo '<input type="text" id="serialEnd" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<!-- Overrides response Start/End fields -->';
            echo '<label for="serialWidth">Response Length</label>';
            echo '<input type="number" id="serialWidth" class="form-control pull-right" />';
            echo '</div>';
            echo '</div>';
            echo '<div class="col-md-6">';
            echo '<div class="form-group form-inline">';
            echo '<label for="serialBaud">Baud Rate</label>';
            echo '<input type="number" id="serialBaud" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="serialData">Data Bits</label>';
            echo '<input type="number" id="serialData" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="serialStop">Stop Bits</label>';
            echo '<input type="number" id="serialStop" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="serialParity">Parity</label>';
            echo '<input type="text" id="serialParity" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="serialFlow">Flow Control</label>';
            echo '<input type="text" id="serialFlow" class="form-control pull-right" />';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<hr />';
            echo '<div class="row">';
            echo '<div class="col-md-12">';
            echo '<button type="button" class="btn btn-danger pull-right" onclick="resetSerialOptions();">Reset</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div id="usbContent" class="tab-pane col-md-8">';
            echo '<h3>USB</h3>';
            echo '<hr />';
            echo '<div class="row">';
            echo '<div class="col-md-12">';
            echo '<div class="form-group">';
            echo '<div class="btn-toolbar">';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-info" onclick="listUsbDevices();">List Devices</button>';
            echo '<button type="button" class="btn btn-info" onclick="listUsbDeviceInterfaces();">List Interfaces</button>';
            echo '<button type="button" class="btn btn-info" onclick="listUsbInterfaceEndpoints();">List Endpoints</button>';
            echo '</div>';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-info" onclick="checkUsbDevice()">Check Claimed</button>';
            echo '<button type="button" class="btn btn-success" onclick="claimUsbDevice()">Claim Device</button>';
            echo '<button type="button" class="btn btn-warning" onclick="releaseUsbDevice()">Release Device</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div class="form-group">';
            echo '<div class="btn-toolbar">';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-default" onclick="sendUsbData()">Send Data</button>';
            echo '<button type="button" class="btn btn-default" onclick="readUsbData()">Read Data</button>';
            echo '</div>';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-default" onclick="openUsbStream()">Open Stream</button>';
            echo '<button type="button" class="btn btn-default" onclick="closeUsbStream()">Close Stream</button>';
            echo '</div>';
            echo '<div class="btn-group" data-toggle="buttons">';
            echo '<label id="usbRawRadio" class="btn btn-default active">';
            echo '<input type="radio" autocomplete="off" checked>Raw';
            echo '</label>';
            echo '<label id="usbWeightRadio" class="btn btn-default">';
            echo '<input type="radio" autocomplete="off">Weight';
            echo '</label>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div class="row" style="margin-top: 1em;">';
            echo '<div class="col-md-12">';
            echo '<div class="panel panel-default">';
            echo '<div class="panel-heading">';
            echo '<h4 class="panel-title">Options</h4>';
            echo '</div>';
            echo '<div class="panel-body">';
            echo '<div class="row">';
            echo '<div class="col-md-6">';
            echo '<div class="form-group form-inline">';
            echo '<label for="usbVendor">Vendor ID</label>';
            echo '<input type="text" id="usbVendor" class="form-control pull-right" onblur="formatHexInput(\'usbVendor\')" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="usbProduct">Product ID</label>';
            echo '<input type="text" id="usbProduct" class="form-control pull-right" onblur="formatHexInput(\'usbProduct\')" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="usbInterface">Device Interface</label>';
            echo '<input type="text" id="usbInterface" class="form-control pull-right" onblur="formatHexInput(\'usbInterface\')" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="usbEndpoint">Interface Endpoint</label>';
            echo '<input type="text" id="usbEndpoint" class="form-control pull-right" onblur="formatHexInput(\'usbEndpoint\')" />';
            echo '</div>';
            echo '</div>';
            echo '<div class="col-md-6">';
            echo '<div class="form-group form-inline">';
            echo '<label for="usbData">Send Data</label>';
            echo '<input type="text" id="usbData" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="usbResponse">Read size</label>';
            echo '<input type="text" id="usbResponse" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="usbStream" class="tip" data-toggle="tooltip" title="Streaming Only: In milliseconds">Stream Interval</label>';
            echo '<input type="text" id="usbStream" class="form-control pull-right" />';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<hr />';
            echo '<div class="row">';
            echo '<div class="col-md-12">';
            echo '<button type="button" class="btn btn-danger pull-right" onclick="resetUsbOptions();">Reset</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div id="hidContent" class="tab-pane col-md-8">';
            echo '<h3>HID</h3>';
            echo '<hr />';
            echo '<div class="row">';
            echo '<div class="col-md-12">';
            echo '<div class="form-group">';
            echo '<div class="btn-toolbar">';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-info" onclick="listHidDevices();">List Devices</button>';
            echo '</div>';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-info" onclick="checkHidDevice()">Check Claimed</button>';
            echo '<button type="button" class="btn btn-success" onclick="claimHidDevice()">Claim Device</button>';
            echo '<button type="button" class="btn btn-warning" onclick="releaseHidDevice()">Release Device</button>';
            echo '</div>';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-default" onclick="startHidListen()">Listen for Events</button>';
            echo '<button type="button" class="btn btn-default" onclick="stopHidListen()">Stop Listening</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div class="form-group">';
            echo '<div class="btn-toolbar">';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-default" onclick="sendHidData()">Send Data</button>';
            echo '<button type="button" class="btn btn-default" onclick="readHidData()">Read Data</button>';
            echo '</div>';
            echo '<div class="btn-group">';
            echo '<button type="button" class="btn btn-default" onclick="openHidStream()">Open Stream</button>';
            echo '<button type="button" class="btn btn-default" onclick="closeHidStream()">Close Stream</button>';
            echo '</div>';
            echo '<div class="btn-group" data-toggle="buttons">';
            echo '<label id="hidRawRadio" class="btn btn-default active">';
            echo '<input type="radio" autocomplete="off" checked>Raw';
            echo '</label>';
            echo '<label id="hidWeightRadio" class="btn btn-default">';
            echo '<input type="radio" autocomplete="off">Weight';
            echo '</label>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div class="row" style="margin-top: 1em;">';
            echo '<div class="col-md-12">';
            echo '<div class="panel panel-default">';
            echo '<div class="panel-heading">';
            echo '<h4 class="panel-title">Options</h4>';
            echo '</div>';
            echo '<div class="panel-body">';
            echo '<div class="row">';
            echo '<div class="col-md-6">';
            echo '<div class="form-group form-inline">';
            echo '<label for="hidVendor">Vendor ID</label>';
            echo '<input type="text" id="hidVendor" class="form-control pull-right" onblur="formatHexInput(\'hidVendor\')" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="hidProduct">Product ID</label>';
            echo '<input type="text" id="hidProduct" class="form-control pull-right" onblur="formatHexInput(\'hidProduct\')" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="hidUsagePage" class="tip" data-toggle="tooltip" title="Optional: For devices that expose multiple endpoints">Usage Page</label>';
            echo '<input type="text" id="hidUsagePage" class="form-control pull-right" onblur="formatHexInput(\'hidUsagePage\')" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="hidSerial" class="tip" data-toggle="tooltip" title="Optional: For distinguishing between identical devices">Serial Number</label>';
            echo '<input type="text" id="hidSerial" class="form-control pull-right" />';
            echo '</div>';
            echo '</div>';
            echo '<div class="col-md-6">';
            echo '<div class="form-group form-inline">';
            echo '<label for="hidData">Send Data</label>';
            echo '<input type="text" id="hidData" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="hidReport">Report Id</label>';
            echo '<input type="text" id="hidReport" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="hidResponse">Read size</label>';
            echo '<input type="text" id="hidResponse" class="form-control pull-right" />';
            echo '</div>';
            echo '<div class="form-group form-inline">';
            echo '<label for="hidStream" class="tip" data-toggle="tooltip" title="Streaming Only: In milliseconds">Stream Interval</label>';
            echo '<input type="text" id="hidStream" class="form-control pull-right" />';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<hr />';
            echo '<div class="row">';
            echo '<div class="col-md-12">';
            echo '<button type="button" class="btn btn-danger pull-right" onclick="resetHidOptions();">Reset</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div class="modal fade" id="askFileModal" role="dialog">';
            echo '<div class="modal-dialog modal-sm" role="document">';
            echo '<div class="modal-content">';
            echo '<div class="modal-body">';
            echo '<div class="form-group">';
            echo '<label for="askFile">File:</label>';
            echo '<input type="text" id="askFile" class="form-control" value="C:\tmp\example-file.txt" />';
            echo '</div>';
            echo '</div>';
            echo '<div class="modal-footer">';
            echo '<button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>';
            echo '<button type="button" class="btn btn-primary" onclick="setPrintFile();">Set</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '<div class="modal fade" id="askHostModal" role="dialog">';
            echo '<div class="modal-dialog modal-sm" role="document">';
            echo '<div class="modal-content">';
            echo '<div class="modal-body">';
            echo '<div class="form-group">';
            echo '<label for="askHost">Host:</label>';
            echo '<input type="text" id="askHost" class="form-control" value="*************" />';
            echo '</div>';
            echo '<div class="form-group">';
            echo '<label for="askPort">Port:</label>';
            echo '<input type="text" id="askPort" class="form-control" value="9100" />';
            echo '</div>';
            echo '</div>';
            echo '<div class="modal-footer">';
            echo '<button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>';
            echo '<button type="button" class="btn btn-primary" onclick="setPrintHost();">Set</button>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
        }
    }

}
