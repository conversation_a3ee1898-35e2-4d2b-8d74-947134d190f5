<?php

class SIANMerchandisePrices extends CWidget {

    public $id;
    public $grid_id;
    public $form;
    public $model;
    //
    public $advanced_id;
    public $modal_id = null;
    public $min_margin_id;
    public $avg_margin_id;
    public $web_margin_id;
    public $measure_id = null;
    public $measure_items = [];
    public $custom_measure_items = [];
    public $currency_items = [];
    public $store_items = [];
    //PRIVATE
    private $controller;
    private $price_mode_id;
    private $cost_input_id;
    private $currency_input_id;

    public function init() {
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->grid_id = isset($this->grid_id) ? $this->grid_id : $this->controller->getServerId();
        $this->currency_input_id = $this->controller->getServerId();
        $this->advanced_id = $this->controller->getServerId();
        $this->min_margin_id = $this->controller->getServerId();
        $this->avg_margin_id = $this->controller->getServerId();
        $this->web_margin_id = $this->controller->getServerId();
        $this->price_mode_id = $this->controller->getServerId();
        $this->cost_input_id = $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-merchandise-prices.js');

        Yii::app()->clientScript->registerScript($this->id, "
        
        $(document).ready(function() { 
            $('#{$this->id}').data('product_id', '{$this->model->product_id}');
            $('#{$this->id}').data('check_url', '{$this->controller->createUrl('/logistic/merchandise/check')}');
            $('#{$this->id}').data('submit_id', '{$this->form->submit_id}');
        });
        
        $('body').on('click', '#{$this->id} input.select-on-check', function(e) {

            var tableObj = $('#{$this->id}');
            var checkboxObj = $(this);
           
            var rowObj = checkboxObj.closest('tr');
            var min_margin = tableObj.find('input.sian-merchandise-prices-min-margin').floatVal(2);
            var avg_margin = tableObj.find('input.sian-merchandise-prices-avg-margin').floatVal(2);
            var web_margin = tableObj.find('input.sian-merchandise-prices-web-margin').floatVal(2);
            var equivalence = tableObj.find('input.sian-merchandise-prices-item-equivalence').floatVal(2);
            var cost = parseFloat(rowObj.find('input.sian-merchandise-prices-item-cost').val());

            SIANPresentationStoreUpdatePrices('{$this->grid_id}', min_margin, avg_margin, web_margin, cost / equivalence);
                
            if(cost == 0)
            {
                $.notify('No hay costo en este almacén, se recomienda elegir otro', {
                   className: 'warn',
                   showDuration: 50,
                   hideDuration: 50,
                   autoHideDelay: 5000
                });         
            }
        });
        
        $('#{$this->id}').data('changeAdvance', function(advanced){

            $('#{$this->advanced_id}').val(advanced);
            
            if(advanced == 1)
            {
                $('.sian-advanced').show(100);
            }
            else
            {
                $('.sian-advanced').hide(100);
            }

            //Llamamos a advanced de presentation
            SIANPresentationStoreChangeAdvance('{$this->grid_id}', advanced);
        });
        
        //Llamamos
        $('#{$this->id}').data('changeAdvance')({$this->model->product->advanced});
        SIANPresentationStoreChangePriceMode('{$this->grid_id}', {$this->model->product->price_mode});                

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        echo "<div id='{$this->id}'>";
        //
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->renderSwitches();
        echo "</div>";
        echo "</div>";
        //
        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-6'>";
        $this->renderMargins();
        $this->renderLastBuyCost();
        echo "</div>";
        echo "<div class='col-lg-8 col-md-8 col-sm-8 col-xs-6'>";
        $this->renderCosts();
        echo "</div>";
        echo "</div>";

        //
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->renderPresentation();
        echo "</div>";
        echo "</div>";
        //
        echo "</div>";
    }

    private function renderPresentation() {
        $this->widget('application.widgets.SIANPresentationStore', array(
            'id' => $this->grid_id,
            'form' => $this->form,
            'model' => $this->model->product,
            'measure_id' => $this->measure_id,
            'measure_items' => $this->measure_items,
            'custom_measure_items' => $this->custom_measure_items,
            'min_margin' => $this->model->subline->min_margin,
            'avg_margin' => $this->model->subline->avg_margin,
            'web_margin' => $this->model->subline->web_margin,
            'cost' => 0,
            'unique_price' => false,
            'store_items' => $this->store_items,
        ));
    }

    private function renderLastBuyCost() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Últimos costos de compra',
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
            )
        ));

        $this->widget('booster.widgets.TbDetailView', array(
            'data' => $this->model,
            'attributes' => array(
                'lpcost_pen',
                'ilpcost_pen',
                'lpcost_usd',
                'ilpcost_usd',
            ),
        ));

        echo "<br>";
        echo "<b>Advertencia:</b> Considerar que este costo no incluye fletes u otros.";

        $this->endWidget();
    }

    private function renderMargins() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Márgenes de la sublínea',
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
            )
        ));
        $this->widget('booster.widgets.TbDetailView', array(
            'data' => $this->model->subline,
            'attributes' => array(
                'subline_name',
                'min_margin',
                'avg_margin',
                'web_margin',
            ),
        ));

        echo CHtml::hiddenField("min_margin", $this->model->subline->min_margin, [
            'class' => 'sian-merchandise-prices-min-margin'
        ]);

        echo CHtml::hiddenField("avg_margin", $this->model->subline->avg_margin, [
            'class' => 'sian-merchandise-prices-avg-margin'
        ]);

        echo CHtml::hiddenField("web_margin", $this->model->subline->web_margin, [
            'class' => 'sian-merchandise-prices-web-margin'
        ]);

        $this->endWidget();
    }

    private function renderCosts() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Costos por almacén (elija uno)',
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
            )
        ));

        $stocksDataProvider = (new SpGetStocks)->setParams([
                    'stock_context' => SpGetStocks::STOCK_CONTEXT_WAREHOUSE,
                    'presentation_mode' => SpGetStocks::PRESENTATION_MODE_DEFAULT,
                    'scope_mode' => SpGetStocks::SCOPE_MODE_COMMERCIAL,
                    'product_type' => Product::TYPE_MERCHANDISE,
                    'product_id' => $this->model->product_id,
                    'warehouse_id' => null,
                    'store_id' => null,
                    'store_default' => 1
                ])->getDataProvider(array('warehouse_id'));

        $modal_id = $this->modal_id;

        //GRID
        $columns = [];

        $access1 = $this->controller->checkRoute('/logistic/warehouse/preview');

        array_push($columns, array(
            'class' => 'CCheckBoxColumn'
        ));

        array_push($columns, array(
            'name' => 'warehouse_name',
            'type' => 'raw',
            'value' => function ($row) use ($access1, $modal_id) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                    'route' => '/logistic/warehouse/preview',
                    'label' => $row->warehouse_name,
                    'title' => 'Ver almacén',
                    'class' => 'form',
                    'data' => array(
                        'id' => $row->warehouse_id,
                        'parent_id' => $modal_id,
                    ),
                    'visible' => $access1
                        ), true);
            }
        ));

        array_push($columns, array(
            'name' => 'mixed_stock',
            'headerHtmlOptions' => array('style' => 'text-align:right'),
            'htmlOptions' => array('style' => 'text-align:right'),
            'type' => 'raw',
            'value' => function ($row) {
                return $row->mixed_stock;
            }
        ));

        array_push($columns, array(
            'name' => 'measure_name',
            'type' => 'raw'
        ));

        array_push($columns, array(
            'header' => 'Hidden',
            'headerHtmlOptions' => array('style' => 'display:none'),
            'htmlOptions' => array('style' => 'display:none'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = '';
                $html .= CHtml::hiddenField("equivalence", $row->equivalence, array(
                            'class' => "sian-merchandise-prices-item-equivalence",
                            'readonly' => true,
                ));
                $html .= CHtml::hiddenField("cost", $row->cost, array(
                            'class' => "sian-merchandise-prices-item-cost",
                            'readonly' => true,
                ));
                return $html;
            },
        ));

        array_push($columns, array(
            'name' => 'cost',
            'headerHtmlOptions' => array('style' => 'text-align:right'),
            'htmlOptions' => array('style' => 'text-align:right'),
            'type' => 'raw',
            'value' => function ($row) {
                return Currency::getSymbol(Currency::PEN) . ' ' . number_format($row->cost, Kardex::KARDEX_DECIMALS);
            }
        ));

        array_push($columns, array(
            'name' => 'icost',
            'headerHtmlOptions' => array('style' => 'text-align:right'),
            'htmlOptions' => array('style' => 'text-align:right'),
            'type' => 'raw',
            'value' => function ($row) {
                return Currency::getSymbol(Currency::PEN) . ' ' . number_format($row->icost, Kardex::KARDEX_DECIMALS);
            }
        ));

        $gridParams = array(
            'id' => $this->id . "",
            'type' => 'hover condensed',
            'dataProvider' => $stocksDataProvider,
            'selectableRows' => 1,
            'columns' => $columns,
            'nullDisplay' => Strings::NONE,
            'template' => '{items}'
        );

        $this->widget('application.widgets.USGridView', $gridParams);
        echo "</br>";
        echo "<em>(*) Costos y stocks expresados en la unidad de medida por defecto</em>";

        $this->endWidget();
    }

    private function renderSwitches() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Configuración',
            'headerIcon' => 'wrench',
            'htmlOptions' => array(
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-4 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->product, 'price_currency', $this->currency_items, ['id' => $this->currency_input_id,
            'onchange' => " var currency = $(this).val();
                            $('#{$this->grid_id}').data('changeCurrency')(currency);"]);
        echo '</div>';
        echo "<div class='col-lg-2 col-md-4 col-sm-6 col-xs-12'>";
        echo SIANForm::textFieldNonActive('TC', 'exchangeRate', Yii::app()->controller->getLastExchange(), ['readonly' => 1]);
        echo '</div>';
        echo "<div class='col-lg-2 col-md-4 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model->product, 'uref_cost_' . $this->controller->getOrganization()->globalVar->display_currency, array(
            'id' => $this->cost_input_id,
            'min' => 0,
            'step' => 0.01,
            'class' => 'us-double3',
            'hint' => '* Unitario.',
            'required' => !$this->model->product->skip_cost_validation
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-4 col-sm-6 col-xs-6'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model->product,
            'attribute' => 'skip_cost_validation',
            'switchChange' => "$('#{$this->cost_input_id}').makeRequired(!this.checked);"
                ), true);
        echo "</div>";
        echo "<div class='col-lg-2 col-md-4 col-sm-6 col-xs-6'>";
        $this->widget('application.widgets.USSwitch', array(
            'id' => $this->price_mode_id,
            'model' => $this->model->product,
            'attribute' => 'price_mode',
            'switchChange' => "SIANPresentationStoreChangePriceMode('{$this->grid_id}', (this.checked ? 1 : 0));"
        ));
        echo "</div>";
        echo '</div>';

        $this->endWidget();
    }

}
