<?php

class SIANBatchMovement extends CWidget {

    //CONST
    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $attribute;
    public $a_items = [];
    public $title = 'Listado de Lotes';
    public $hint = false;
    public $disabled = false;
    public $readonly = true;
    public $class_panel = '';
    public $direction;
    //
    public $product_id;
    public $allow_decimals = 0;
    public $warehouse_id;
    public $date;
    public $parent_row_id;
    public $parent_pres_quantity;
    public $parent_equivalence;
    public $order;
    public $local_storage_name;
    public $function_local_storage = 1;
    public $batch_detail_origin = [];
    //PRIVATE
    private $controller;
    //CONTROLES
    private $autocomplete_id;
    private $batch_aux_id;
    private $batch_id_auto_id;
    private $batch_code_auto_id;
    private $expiration_date_auto_id;
    private $unit_stock_auto_id;
    private $pres_stock_auto_id;
    private $pres_stock_label_auto_id;
    //
    private $create_batch_id;
    private $update_batch_id;

    public function init() {
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //CONTROLES
        $this->autocomplete_id = $this->controller->getServerId();
        $this->batch_aux_id = $this->controller->getServerId();
        $this->batch_id_auto_id = $this->controller->getServerId();
        $this->batch_code_auto_id = $this->controller->getServerId();
        $this->expiration_date_auto_id = $this->controller->getServerId();
        $this->unit_stock_auto_id = $this->controller->getServerId();
        $this->pres_stock_auto_id = $this->controller->getServerId();
        $this->pres_stock_label_auto_id = $this->controller->getServerId();
        //
        $this->create_batch_id = $this->controller->getServerId();
        $this->update_batch_id = $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-batch-movement.js');
        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id, "

        //COUNT
        var divObj = $('#{$this->id}');
        divObj.data('count', 0);
        divObj.data('readonly', " . CJSON::encode($this->readonly) . ");
        divObj.data('parent_row_id', '{$this->parent_row_id}');
        divObj.data('parent_pres_quantity', {$this->parent_pres_quantity});
        divObj.data('allow_decimals', {$this->allow_decimals});            
        divObj.data('local_storage_name', '{$this->local_storage_name}');
        divObj.data('function_local_storage', {$this->function_local_storage});
        var a_product_batchs = [];
            
        " . (
                ($this->function_local_storage == 1) ?
                        "
        let datos = localStorage.getItem('{$this->local_storage_name}');
        var batch_products = JSON.parse(datos); 
        if(batch_products != null){
            a_product_batchs = batch_products['{$this->parent_row_id}'];   
        }          
        " :
                        " 
        a_product_batchs = " . CJSON::encode($this->batch_detail_origin) . ";
        ") . "
                       
        var i_count = Object.keys(a_product_batchs).length;
        
        if (i_count > 0)
        {
            var pres_quantity_total = '{$this->parent_pres_quantity}';
            $.each(a_product_batchs, function (index, itemObj) {
                SIANBatchMovementAddItem('{$this->id}', itemObj['batch_id'], itemObj['batch_code'], itemObj['expiration_date'], itemObj['unit_stock'], itemObj['pres_stock'], itemObj['pres_stock_label'], itemObj['pres_quantity']);                                                           
            });          
        }else{          
            $('#{$this->id}_table tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
        }
        SIANBatchMovementGetIds('{$this->id}', '{$this->parent_row_id}');

        //boton agregar
        function {$this->id}SIANBatchMovementAdd(){        
            
            var row_id = $('#{$this->id}').data('row_id');
            var batch_id = $('#{$this->batch_id_auto_id}').val();                               
            var batch_code = $('#{$this->batch_code_auto_id}').val();                 
            var expiration_date = $('#{$this->expiration_date_auto_id}').val();
            var unit_stock = $('#{$this->unit_stock_auto_id}').val();  
            var pres_stock = $('#{$this->pres_stock_auto_id}').val();  
            var pres_stock_label = $('#{$this->pres_stock_label_auto_id}').val();  
            var pres_quantity = 0; 
            
            if (batch_id.length === 0)
            {
                $('#{$this->batch_aux_id}').focus();
                return;
            }
            
            SIANBatchMovementAddItem('{$this->id}', batch_id, batch_code, expiration_date, unit_stock, pres_stock, pres_stock_label, pres_quantity);                
            USAutocompleteReset('{$this->autocomplete_id}');       
            SIANBatchMovementGetIds('{$this->id}', '{$this->parent_row_id}');
            
            $('#{$this->batch_id_auto_id}').val('');                               
            $('#{$this->batch_code_auto_id}').val('');                 
            $('#{$this->expiration_date_auto_id}').val('');
            $('#{$this->unit_stock_auto_id}').val('');  
            $('#{$this->pres_stock_auto_id}').val('');  
            $('#{$this->pres_stock_label_auto_id}').val('');  
        }
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'id' => $this->id,
            'title' => $this->title,
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id . '_products_panel'
            )
        ));

        echo "<div class='row sian-batch-movement-search'>";
        echo "<div class='col-lg-10 col-md-10 col-sm-12 col-xs-12'>";

        $a_attributes = [
            array('name' => 'batch_id', 'width' => 10, 'types' => array('value', 'id'), 'not_in' => "window.sianBatchMovementIds['{$this->parent_row_id}']"), //
            array('name' => 'batch_code', 'width' => 15, 'types' => array('aux')),
            array('name' => 'product_id', 'hidden' => true, 'in' => "[{$this->product_id}]"),
            array('name' => 'product_name', 'width' => 45, 'types' => array('text')),
            array('name' => 'unit_stock', 'hidden' => true),
            array('name' => 'pres_stock', 'hidden' => true)
        ];

        if ($this->readonly) {
            $a_attributes[] = array('name' => 'pres_stock_label', 'width' => 15);
            $a_attributes[] = array('name' => 'expiration_date', 'width' => 15);
        } else {
            $a_attributes[] = array('name' => 'pres_stock_label', 'width' => 15, 'hidden' => true);
            $a_attributes[] = array('name' => 'expiration_date', 'width' => 30);
        }

        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->autocomplete_id,
            'aux_id' => $this->batch_aux_id,
            'focus' => false,
            'label' => 'Lotes',
            'parent_id' => $this->modal_id,
            'view' => array(
                'model' => 'DpBatchs',
                'scenario' => $this->direction == BatchMovement::DIRECTION_OUT ? DpBatchs::SCENARIO_WIDGET_SEARCH_OUT : DpBatchs::SCENARIO_WIDGET_SEARCH_IN,
                'attributes' => $a_attributes,
                'params' => "{
                        xequivalence: {$this->parent_equivalence},
                        xwarehouse_id: {$this->warehouse_id},
                        xdate: '{$this->date}'
                }"
            ),
            'readonly' => $this->readonly,
            'maintenance' => array(
                'module' => 'logistic',
                'controller' => 'batch',
                'buttons' => array(
                    'preview' => [],
                    'create' => [
                        'id' => $this->create_batch_id,
                        'data' => [
                            'warehouse_id' => $this->warehouse_id,
                            'product_id' => $this->product_id
                        ]
                    ],
                    'update' => [
                        'id' => $this->update_batch_id,
                        'data' => [
                            'warehouse_id' => $this->warehouse_id,
                            'product_id' => $this->product_id
                        ]
                    ],
                    'delete' => [],
                ),
            ),
            'onselect' => "                    
                    $('#{$this->batch_id_auto_id}').val(batch_id);
                    $('#{$this->batch_code_auto_id}').val(batch_code);
                    $('#{$this->expiration_date_auto_id}').val(expiration_date); 
                    $('#{$this->unit_stock_auto_id}').val(unit_stock);
                    $('#{$this->pres_stock_auto_id}').val(pres_stock);
                    $('#{$this->pres_stock_label_auto_id}').val(pres_stock_label);
                    {$this->id}SIANBatchMovementAdd();                       
                ",
        ));
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->batch_id_auto_id,
        ));
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->batch_code_auto_id,
        ));
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->unit_stock_auto_id,
        ));
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->pres_stock_auto_id,
        ));
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->pres_stock_label_auto_id,
        ));
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->expiration_date_auto_id,
        ));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
        echo "<br>";
        echo "<h4 style=\"text-align: center;\"><b>Cantidad Total:    {$this->parent_pres_quantity}</b></h4>";
        echo "</div>";
        echo "</div>";

        echo "<hr>";

        echo "<div class='row sian-batch-movement-items'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->_renderProductTable();
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

    private function _renderProductTable() {
        $title = 'F.';
        switch (Yii::app()->controller->getOrganization()->globalVar->use_batch) {
            case GlobalVar::BATCH_USE_BATCH_ENTRY_DATE:
                $title = 'F. Ingreso';
                break;
            case GlobalVar::BATCH_USE_BATCH_EXPIRATION_DATE:
                $title = 'F. Vencimiento';
                break;
        }
        $s_html = "<table id='{$this->id}_table' class='products_table items table table-hover table-condensed'>";
        $s_html .= "<thead>";
        $s_html .= "<tr>";
        $s_html .= "<th width='10%'><b>ID</b></th>";
        $s_html .= "<th width='" . ($this->readonly || $this->function_local_storage == 0 ? '30' : '55') . "%'><b>Código Lote</b></th>";
        $s_html .= "<th width='25%'" . ($this->readonly || $this->function_local_storage == 0 ? '' : 'style=\'display: none;\'') . "><b>Stock</b></th>";
        $s_html .= "<th width='20%'><b>{$title}</b></th>";
        $s_html .= "<th width='15%'><b>Cantidad</b></th>";
        $s_html .= "</tr>";
        $s_html .= "</thead>";
        $s_html .= "<tbody>";
        $s_html .= "</tbody>";
        $s_html .= "</table>";

        return $s_html;
    }

}
