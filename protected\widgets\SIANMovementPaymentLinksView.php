<?php

class SIANMovementPaymentLinksView extends CWidget {

    public $id;
    public $dataProvider;
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //
        $this->dataProvider->pagination = false;
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<h4>Links de Pago:</h4>";

        $columns = [];
        $counter = 0;

        array_push($columns, array(
            'header' => 'Item',
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function($row) use (&$counter) {
                return ++$counter;
            },
        ));

        array_push($columns, array(
            'header' => 'Desde',
            'name' => 'api_user_id',
            'type' => 'raw',
            'value' => function($row) {
                return $row->apiUser->username;
            },
        ));

        array_push($columns, array(
            'header' => 'Link',
            'name' => 'link',
            'type' => 'raw',
            'value' => function($row) {
                return CHtml::link($row->link, $row->link, array(
                            'target' => '_blank'
                ));
            }
        ));

        $gridParams = array(
            'id' => $this->id,
            'type' => 'hover condensed',
            'dataProvider' => $this->dataProvider,
            'enableSorting' => true,
            'selectableRows' => 0,
            'columns' => $columns,
            'template' => '{items}',
            'nullDisplay' => Strings::NONE,
        );
        $this->widget('application.widgets.USGridView', $gridParams);
    }

}
