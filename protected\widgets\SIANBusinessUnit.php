<?php

class SIANBusinessUnit extends CWidget {

    //CONST
    public $id;
    public $form;
    public $model;
    public $items = [];
    public $readonly = false;
    public $disabled = false;
    public $required = false;
    public $onChange = '';
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        SIANAssets::registerScriptFile('js/sian-business-unit.js');

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        var inputObj = $('#{$this->id}');
        inputObj.data('default_business_unit_id', " . (Yii::app()->user->hasState('default_business_unit') ? Yii::app()->user->getState('default_business_unit') : 'null') . ");
        inputObj.data('parent_business_unit_id', " . (isset($this->model->parent_business_unit_id) ? $this->model->parent_business_unit_id : 'null') . ");
                
        //Evento load porque se ejecuta cuando ya todo cargó
        $(document).ready(function() {
               
            var items = " . CJSON::encode($this->items) . ";
            SIANBusinessUnitFill('{$this->id}', items, '{$this->model->business_unit_id}');
        });
        
        $('#{$this->id}').data('changeStore', function(changeData){
            //Seteamos unidad de negocio si la tienda una
            if(isset(changeData.business_unit_id))
            {
                var previous = $('#{$this->id}').val();
                $('#{$this->id}').val(changeData.business_unit_id);
                //Notificamos si hubo cambio
                if(previous !== changeData.business_unit_id)
                {
                    $('#{$this->id}').notify('Se cambió la unidad de negocio a la asociada a la tienda', {
                        className: 'info',
                        showDuration: 30,
                        hideDuration: 50,
                        autoHideDelay: 4000,
                    }); 
                }

            }       
        }); 
        
        $('body').on('change', '#{$this->id}', function(e) {
            
            var selectObj = $(this);
            var optionObj = selectObj.find('option:selected');

            var changeData = {
                business_unit_id: optionObj.val(),
                combination_id: optionObj.data('combination_id'),
                combination_name: optionObj.data('combination_name'),
                levels: optionObj.data('levels'),
            };
            {$this->onChange}
        });

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo $this->form->dropDownListRow($this->model, 'business_unit_id', [], array(
            'id' => $this->id,
            'required' => $this->required,
            'empty' => Strings::SELECT_OPTION,
            'readonly' => $this->readonly,
            'disabled' => $this->disabled,
        ));
    }

}
