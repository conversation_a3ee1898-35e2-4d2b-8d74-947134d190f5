
function SIANAssistanceSummaryAddItem(div_id, week_id, month_id, year_id, regime_id, readonly, person_id, person_name, work_days, work_days_calc, absences, work_hours, extrahours1, extrahours2, night_hours, holiday_hours, lateness, subItems, errors)
{
    //TABLE    
    var table = $('#' + div_id + '_table');
    var count = parseInt(table.data('count'));
    var type_period = $('#' + div_id).data('type_period');
    //ID
    var id = getLocalId();
    //HTML
    var row = '';

    row += '<tr id=\'' + id + '\' class=\'sian-assistance-item\'>';

    row += '<td class=\'form-group\'>';
    row += '<span class=\'sian-assistance-item-order\'></span>';
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['person_id'] ? 'has-error' : '') + '\'>';
    row += '<input class=\'form-control sian-assistance-item-person-id\' type=\'text\' name=\'AssistanceSummaryEmployee[' + id + '][person_id]\' value=\'' + person_id + '\' readonly=\'true\'  >';
    if (errors['person_id'])
    {
        row += '<span class=\'help-block error\'>' + errors['name'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['name'] ? 'has-error' : '') + '\'>';
    row += '<input class=\'form-control sian-assistance-item-name\' type=\'text\' name=\'AssistanceSummaryEmployee[' + id + '][person_name]\' value=\'' + person_name + '\' readonly=\'true\'  >';
    if (errors['name'])
    {
        row += '<span class=\'help-block error\'>' + errors['name'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['work_days'] ? 'has-error' : '') + '\' ' + (type_period !== REGIME_TYPE_PERIOD_MONTHLY ? 'style="display: none;"' : '') + '>';
    row += '<input style="text-align: center;" class=\'form-control sian-assistance-item-work-days\' type=\'text\' name=\'AssistanceSummaryEmployee[' + id + '][work_days]\' value=\'' + work_days + '\' readonly=\'true\'  >';
    if (errors['work_days'])
    {
        row += '<span class=\'help-block error\'>' + errors['work_days'] + '</span>';
    }
    row += '</td>';
    row += '<td class=\'form-group ' + (errors['absences'] ? 'has-error' : '') + '\'' + (type_period !== REGIME_TYPE_PERIOD_MONTHLY ? 'style="display: none;"' : '') + '>';
    row += '<input style="text-align: right;" class=\'form-control sian-assistance-item-absences input-number\' maxlength="2" type=\'number\' min="0" max="' + work_days + '" name=\'AssistanceSummaryEmployee[' + id + '][absences]\' value=\'' + absences + '\' ' + (readonly == 1 ? 'readonly=\'true\'' : '') + ' >';
    if (errors['absences'])
    {
        row += '<span class=\'help-block error\'>' + errors['absences'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['work_hours'] ? 'has-error' : '') + '\'' + (type_period !== REGIME_TYPE_PERIOD_WEEKLY ? 'style="display: none;"' : '') + '>';
    row += '<input style="text-align: right;" class=\'form-control sian-assistance-item-work_hours input-number\' maxlength="3" type=\'number\'  min="0" max="150" name=\'AssistanceSummaryEmployee[' + id + '][work_hours]\' value=\'' + work_hours + '\'  ' + (readonly == 1 ? 'readonly=\'true\'' : '') + ' >';
    if (errors['work_hours'])
    {
        row += '<span class=\'help-block error\'>' + errors['work_hours'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['extrahours1'] ? 'has-error' : '') + '\'>';
    row += '<input style="text-align: right;" class=\'form-control sian-assistance-item-extrahours1 input-number\' maxlength="3" type=\'number\'  min="0" max="60" name=\'AssistanceSummaryEmployee[' + id + '][extrahours1]\' value=\'' + extrahours1 + '\' ' + (readonly == 1 ? 'readonly=\'true\'' : '') + ' >';
    if (errors['extrahours1'])
    {
        row += '<span class=\'help-block error\'>' + errors['extrahours1'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['extrahours2'] ? 'has-error' : '') + '\'>';
    row += '<input style="text-align: right;" class=\'form-control sian-assistance-item-extrahours2 input-number\' maxlength="3" type=\'number\'  min="0" max="150" name=\'AssistanceSummaryEmployee[' + id + '][extrahours2]\' value=\'' + extrahours2 + '\'  ' + (readonly == 1 ? 'readonly=\'true\'' : '') + ' >';
    if (errors['extrahours2'])
    {
        row += '<span class=\'help-block error\'>' + errors['extrahours2'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['night_hours'] ? 'has-error' : '') + '\'' + (type_period !== REGIME_TYPE_PERIOD_WEEKLY ? 'style="display: none;"' : '') + '>';
    row += '<input style="text-align: right;" class=\'form-control sian-assistance-item-nighthours input-number\' maxlength="3" type=\'number\'  min="0" max="150" name=\'AssistanceSummaryEmployee[' + id + '][night_hours]\' value=\'' + night_hours + '\'  ' + (readonly == 1 ? 'readonly=\'true\'' : '') + ' >';
    if (errors['night_hours'])
    {
        row += '<span class=\'help-block error\'>' + errors['night_hours'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['holiday_hours'] ? 'has-error' : '') + '\'' + (type_period !== REGIME_TYPE_PERIOD_WEEKLY ? 'style="display: none;"' : '') + '>';
    row += '<input style="text-align: right;" class=\'form-control sian-assistance-item-holiday_hours input-number\' maxlength="3" type=\'number\'  min="0" max="150" name=\'AssistanceSummaryEmployee[' + id + '][holiday_hours]\' value=\'' + holiday_hours + '\'  ' + (readonly == 1 ? 'readonly=\'true\'' : '') + '>';
    if (errors['holiday_hours'])
    {
        row += '<span class=\'help-block error\'>' + errors['holiday_hours'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['lateness'] ? 'has-error' : '') + '\'' + (type_period !== REGIME_TYPE_PERIOD_MONTHLY ? 'style="display: none;"' : '') + '>';
    row += '<input style="text-align: right;" class=\'form-control sian-assistance-item-lateness input-number\' maxlength="3" type=\'number\' min="0"  max="14400" name=\'AssistanceSummaryEmployee[' + id + '][lateness]\' value=\'' + lateness + '\' ' + (readonly == 1 ? 'readonly=\'true\'' : '') + ' >';
    if (errors['lateness'])
    {
        row += '<span class=\'help-block error\'>' + errors['lateness'] + '</span>';
    }
    row += '</td>';

    var xevent = 'onclick=\'SIANAssistanceSummaryGoToDates("' + div_id + '", "' + id + '_table", "Detalle Diario de ' + person_name + '", 100);\' ';
    var xicon = (errors['assistanceSummaryDate'] ? 'red' : 'black');

    row += '<td>';
    row += '<a ' + xevent + ' title=\'Ver Detalle\'><span class=\'fa fa-lg fa-list ' + xicon + '\'></span></a>';
    row += '<a onclick = "SIANAssistanceSummaryRemoveItem(\'' + table.prop("id") + '\', \'' + id + '\', true,\'' + week_id + '\',\'' + month_id + '\',\'' + year_id + '\',\'' + regime_id + '\')" title=\'Borrar Trabajador\' class=\'sdc-remove-item\' style=\'padding-right:4px;\' ><span class=\'fa fa-lg fa-times black\'></span></a>';
    row += '</td>';

    row += '<td class=\'form-group\'  style=\'visibility: hidden; width=0px;\'>';
    row += '<input type=\'hidden\' style=\'width=0px;\' class=\'form-control sian-assistance-item-work-days-calc\'  name=\'AssistanceSummaryEmployee[' + id + '][work_days_calc]\' value=\'' + work_days_calc + '\' readonly=\'true\'  >';
    row += '</td>';

    row += '</tr>';

    //COUNT DE ITEMS
    if (count === 0)
    {
        table.find('tbody').html(row);
    } else
    {
        table.find('tbody').append(row);
    }
    table.data('count', count + 1);
    SIANAssistanceSummaryUpdate(table.prop("id"));
    SIANAssistanceSummaryAddGroup(div_id, id, subItems);
}

function SIANAssistanceSummaryLoadAll(div_id, url, msg_OK, week_input_id, month_input_id, year_input_id, regime_input_id, readonly, person_id) {

    var table_id = div_id + '_table';
    var year = $('#' + year_input_id).val();
    var month = $('#' + month_input_id).val();
    var regime_id = $('#' + regime_input_id).val();

    $.ajax({
        type: 'post',
        url: url,
        async: false,
        data: {month: month, year: year, person_id: person_id, regime_id: regime_id},
        beforeSend: function (xhr) {
            window.active_ajax++;
            //Ocultamos los tooltip
            $('div.ui-tooltip').remove();
        },
        success: function (data) {

            if (data.code === msg_OK)
            {
                for (var i = 0; i < data.data.length; i++)
                {
                    SIANAssistanceSummaryAddItem(div_id,
                            week_input_id,
                            month_input_id,
                            year_input_id,
                            regime_input_id,
                            readonly,
                            data.data[i]['person_id'],
                            data.data[i]['person_name'],
                            data.data[i]['work_days_month'],
                            data.data[i]['work_days_calc'],
                            0, 0, 0,
                            0, 0, 0, 0, [],
                            []);
                }
                SIANAssistanceSummaryUpdate(table_id);
            } else
            {
                bootbox.alert(us_message(data.message, 'error'));
            }
            window.active_ajax--;
        },
        error: function (request, status, error) { // if error occured
            bootbox.alert(us_message(request.responseText, 'error'));
            window.active_ajax--;
        },
        dataType: 'json'
    });
}

function SIANAssistanceSummaryUpdate(table_id) {

    var table = $('#' + table_id);

    table.find('tbody tr.sian-assistance-item').each(function (index) {
        var row = $(this);
        row.find('span.sian-assistance-item-order').text(index + 1);
    });
}

function SIANAssistanceSummaryReadOnly(id, readonly) {

    var element = $('#' + id);

    if (element.hasAttr('readonly')) {
        if (!readonly) {
            element.removeAttr('readonly');
        }
    } else {
        if (readonly) {
            element.attr('readonly', 'readonly');
        }
    }
}

function SIANAssistanceSummaryDaysInMonth(month, year) {
    return new Date(year || new Date().getFullYear(), month, 0).getDate();
}
// ------------------------------------------------------------------------------------------------------------------
// ------------------------------------------------------------------------------------------------------------------
// ------------------------------------------------------------------------------------------------------------------
// ------------------------------------------------------------------------------------------------------------------

function SIANAssistanceSummaryAddGroup(div_id, row_main_id, subItems)
{
    var divObj = $('#' + div_id);
    var dates = divObj.data('datesInPeriod');

    //Generamos tabla   
    var sub_table_id = SIANAssistanceSummaryAddTable(div_id, row_main_id, []);
    //agregamos los items Fecha a la tabla   
    if (subItems.length > 0) {
        $.each(subItems, function (key, value) {
            SIANAssistanceSummaryDateAddItem(div_id, row_main_id, dates[value['date']]['day_name'], value['date'], dates[value['date']]['is_holiday'],
                    value['work_hours'], value['extrahours'], value['night_hours'], value['holiday_hours'], value['lateness'], value['absence'], value['type_absence'], value['errors'], sub_table_id);
        });
    } else {
        $.each(dates, function (key, value) {
            SIANAssistanceSummaryDateAddItem(div_id, row_main_id, value['day_name'], key, value['is_holiday'],
                    0, 0, 0, 0, 0, 0, '', [], sub_table_id);
        });
    }
    SIANAssistanceSummaryDateUpdateAmounts(sub_table_id);
}

function SIANAssistanceSummaryAddTable(div_id, row_main_id, errors)
{
    var divObj = $('#' + div_id);
    var type_period = divObj.data('type_period');
    var div_tables_id = divObj.data('div_tables_id');
    var sub_table_id = row_main_id + '_table';
    var summary_id = getLocalId();

    var html = '<table id=\'' + sub_table_id + '\' class=\'table table-condensed table-hover\' style="display:none">';
    html += '<thead>';
    html += '<tr>';
    html += '<th width=\'10%\'>Día</th>';
    html += '<th width=\'15%\'>Fecha</th>';
    html += '<th width=\'10%\' ' + (type_period != REGIME_TYPE_PERIOD_WEEKLY ? 'style="display:none;"' : '') + '>Hrs Trabajadas</th>';
    html += '<th width=\'10%\'>Hrs Extra</th>';
    html += '<th width=\'10%\'' + (type_period != REGIME_TYPE_PERIOD_WEEKLY ? 'style="display:none;"' : '') + '>Hrs Nocturnas</th>';
    html += '<th width=\'10%\'' + (type_period != REGIME_TYPE_PERIOD_WEEKLY ? 'style="display:none;"' : '') + '>Hrs D. Descanso</th>';
    html += '<th width=\'10%\'' + (type_period != REGIME_TYPE_PERIOD_MONTHLY ? 'style="display:none;"' : '') + '>Tardanza (min)</th>';
    html += '<th width=\'5%\'>Inasistencia</th>';
    html += '<th width=\'30%\'>Motivo</th>';
    html += '</tr>';
    html += '</thead>';
    html += '<tbody></tbody>';
    html += '<tfoot>';
    html += '<tr id="' + summary_id + '">';


    html += '<td class=\'form-group\'>';
    html += '<label></label>';
    html += '</td>';

    html += '<td class=\'form-group\'>';
    html += '<label>TOTALES:</label>';
    html += '</td>';

    html += '<td class=\'form-group\'' + (type_period != REGIME_TYPE_PERIOD_WEEKLY ? 'style="display:none;"' : '') + '>';
    html += '<label type=\'text\' style=\'text-align:right;\' class=\'sian-assistance-summary-date-total-work-hours pull-right\'  readonly>0.0</label>';
    html += '</td>';

    html += '<td class=\'form-group\'>';
    html += '<label type=\'text\' style=\'text-align:right\' class=\'sian-assistance-summary-date-total-extrahours pull-right\' readonly>0.0</label>';
    html += '<input type=\'hidden\' style=\'text-align:right\' class=\'sian-assistance-summary-date-total-extrahours1 pull-right\' value=\'0.0\'>';
    html += '<input type=\'hidden\' style=\'text-align:right\' class=\'sian-assistance-summary-date-total-extrahours2 pull-right\' value=\'0.0\'>';
    html += '</td>';

    html += '<td class=\'form-group\'' + (type_period != REGIME_TYPE_PERIOD_WEEKLY ? 'style="display:none;"' : '') + '>';
    html += '<label type=\'text\' style=\'text-align:right;\' class=\'sian-assistance-summary-date-total-night-hours pull-right\' readonly>0.0</label>';
    html += '</td>';

    html += '<td class=\'form-group\'' + (type_period != REGIME_TYPE_PERIOD_WEEKLY ? 'style="display:none;"' : '') + '>';
    html += '<label type=\'text\' style=\'text-align:right;\' class=\'sian-assistance-summary-date-total-holiday-hours pull-right\' readonly>0.0</label>';
    html += '</td>';

    html += '<td class=\'form-group\'' + (type_period != REGIME_TYPE_PERIOD_MONTHLY ? 'style="display:none;"' : '') + '>';
    html += '<label type=\'text\' style=\'text-align:right;\' class=\'sian-assistance-summary-date-total-lateness pull-right\' readonly>0.0</label>';
    html += '</td>';

    html += '<td class=\'form-group\' style="text-align: center;' + (type_period != REGIME_TYPE_PERIOD_MONTHLY ? 'visibility:hidden;' : '') + '">';
    html += '<label type=\'text\' class=\'sian-assistance-summary-date-total-absences\' readonly>0</label>';
    html += '</td>';

    html += '<td class=\'form-group\'>';
    html += '</td>';

    html += '</tr>';
    html += '</tfoot>';
    html += '</table>';

    $('#' + div_tables_id).append(html);

    var tableObj = $('#' + sub_table_id);
    tableObj.data('count', 0);
    tableObj.data('instance_id', row_main_id);
    tableObj.data('errors', errors);
    tableObj.data('summary_id', summary_id);

    SIANAssistanceSummaryDateUpdateAmounts(sub_table_id);
    return sub_table_id;
}

function SIANAssistanceSummaryDateAddItem(div_id, table_id, day_name, date, is_holiday, work_hours, extrahours, night_hours, holiday_hours, lateness, absence, type_absence, errors, sub_table_id)
{
    //DIV
    var divObj = $('#' + div_id);
    var type_period = divObj.data("type_period");
    var type_absence_list = divObj.data('typeAbsencesList');
    //TABLE
    var tableObj = $('#' + sub_table_id);
    var count = parseInt(tableObj.data('count'));
    var instance_id = tableObj.data('instance_id');
    //ID
    var row_id = getLocalId();

    var day_holiday = false;
    if (is_holiday == 1 || day_name == 'Domingo') {
        day_holiday = true;
    }

    var row = '';
    row += '<tr id=\'' + row_id + '\' class=\'sian-assistance-summary-date us-sortable-item\'>';

    row += '<td class=\'form-group\'>';
    row += '<label name=\'day_name\'  ' + (day_holiday ? ' style=\'color:red;font-size:10px;\'' : '') + '>' + day_name + '</label>';
    row += '</td>';

    row += '<td class=\'form-group\'>';
    row += '<input type=\'text\' class=\'form-control\' ' + (day_holiday ? ' style=\'color:red;\'' : '') + ' name=\'AssistanceSummaryEmployee[' + instance_id + '][subitems][' + row_id + '][date]\' value=\'' + date + '\' readonly>';
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['work_hours'] ? 'has-error' : '') + '\'' + (type_period != REGIME_TYPE_PERIOD_WEEKLY ? 'style="display:none;"' : '') + '\'>';
    row += '<input type=\'number\' class=\'form-control sian-assistance-summary-date-work-hours\' style=\'text-align:right;\' name=\'AssistanceSummaryEmployee[' + instance_id + '][subitems][' + row_id + '][work_hours]\' step=1  min=0  value=\'' + work_hours + '\'  onchange=\'SIANAssistanceSummaryDateUpdateAmounts(\"' + table_id + '_table\")\' ' + (day_holiday ? 'readonly' : '') + '>';
    if (errors['work_hours']) {
        row += '<span class=\'help-block error\'>' + errors['work_hours'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['extrahours'] ? 'has-error' : '') + '\'>';
    row += '<input type=\'number\' style=\'text-align:right\' class=\'form-control sian-assistance-summary-date-extrahours\' name=\'AssistanceSummaryEmployee[' + instance_id + '][subitems][' + row_id + '][extrahours]\' step=1  min=0  value=\'' + extrahours + '\'  onchange=\'SIANAssistanceSummaryDateUpdateAmounts(\"' + table_id + '_table\")\' ' + (day_holiday ? 'readonly' : '') + '>';
    if (errors['extrahours']) {
        row += '<span class=\'help-block error\'>' + errors['extrahours'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['night_hours'] ? 'has-error' : '') + '\'' + (type_period != REGIME_TYPE_PERIOD_WEEKLY ? 'style="display:none;"' : '') + '\'>';
    row += '<input type=\'number\' style=\'text-align:right;\' class=\'form-control sian-assistance-summary-date-night-hours\' name=\'AssistanceSummaryEmployee[' + instance_id + '][subitems][' + row_id + '][night_hours]\' step=1  min=0  value=\'' + night_hours + '\'  onchange=\'SIANAssistanceSummaryDateUpdateAmounts(\"' + table_id + '_table\")\' ' + (day_holiday ? 'readonly' : '') + '>';
    if (errors['night_hours']) {
        row += '<span class=\'help-block error\'>' + errors['night_hours'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['holiday_hours'] ? 'has-error' : '') + '\'' + (type_period != REGIME_TYPE_PERIOD_WEEKLY ? 'style="display:none;"' : '') + '\'>';
    row += '<input type=\'number\' style=\'text-align:right;\' class=\'form-control sian-assistance-summary-date-holiday-hours\' name=\'AssistanceSummaryEmployee[' + instance_id + '][subitems][' + row_id + '][holiday_hours]\' step=1  min=0  value=\'' + holiday_hours + '\'  onchange=\'SIANAssistanceSummaryDateUpdateAmounts(\"' + table_id + '_table\")\' ' + (day_holiday ? '' : 'readonly') + '>';
    if (errors['holiday_hours']) {
        row += '<span class=\'help-block error\'>' + errors['holiday_hours'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['lateness'] ? 'has-error' : '') + '\'' + (type_period != REGIME_TYPE_PERIOD_MONTHLY ? 'style="display:none;"' : '') + '\'>';
    row += '<input type=\'number\' style=\'text-align:right;\' class=\'form-control sian-assistance-summary-date-lateness\' name=\'AssistanceSummaryEmployee[' + instance_id + '][subitems][' + row_id + '][lateness]\' step=1  min=0  value=\'' + lateness + '\'  onchange=\'SIANAssistanceSummaryDateUpdateAmounts(\"' + table_id + '_table\")\' ' + (day_holiday ? 'readonly' : '') + '>';
    if (errors['lateness']) {
        row += '<span class=\'help-block error\'>' + errors['lateness'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group\' style="text-align: center;">';
    row += '<input type=\'hidden\'   value="0" class=\'form-control sian-assistance-summary-date-absence\' name=\'AssistanceSummaryEmployee[' + instance_id + '][subitems][' + row_id + '][absence]\'>';
    row += '<input type=\'checkbox\' value="1" class=\'form-control sian-assistance-summary-date-absence-checkbox\' ' + (absence == 1 ? (' checked') : '') + ' onchange=\'SIANAssistanceSummaryDateUpdateAmounts(\"' + table_id + '_table\")\' name=\'AssistanceSummaryEmployee[' + instance_id + '][subitems][' + row_id + '][absence]\'>';
    row += '</td>';

    row += '<td class=\'form-group\'>';
    row += '<select  class=\'form-control sian-assistance-summary-date-type-absence\' name=\'AssistanceSummaryEmployee[' + instance_id + '][subitems][' + row_id + '][type_absence]\'' + (absence == 1 ? '' : 'readonly') + '>' + getOptionsHtml(type_absence_list, type_absence, true, true) + '</select>';
    row += '</td>';

    row += '</tr>';

    //COUNT DE ITEMS
    if (count === 0)
    {
        tableObj.find('tbody').html(row);
    } else
    {
        tableObj.find('tbody').append(row);
    }
    tableObj.data('count', count + 1);
}

function SIANAssistanceSummaryGoToDates(div_id, sub_table_id, title, duration)
{
    var divObj = $('#' + div_id);
    var submit_id = divObj.data('submit_id');
    var cancel_id = divObj.data('cancel_id');
    var tableObj = $('#' + div_id + '_table');
    var title_id = divObj.data('title_id');

    //Ocultamos tabla principal
    tableObj.hide('slide', {}, duration, function () {

        //Seteamos título
        if (title.length !== 0)
        {
            $('#' + title_id).text(title);
        }

        SIANAssistanceSummaryShowTable(div_id, sub_table_id, 0);
        SIANAssistanceSummaryDateUpdateAmounts(sub_table_id);
    });

    //Desactivamos botón submit
    $('#' + submit_id).prop('disabled', true);
    $('#' + cancel_id).prop('disabled', true);
    $(document).tooltip("destroy").tooltip({
        hide: false
    });
}

function SIANAssistanceSummaryShowTable(div_id, table_id, duration)
{
    var divObj = $('#' + div_id);
    var back_link_id = divObj.data('back_link_id');
    var row_control_id = divObj.data('row_control_id');
    //Seteamos tabla activa
    divObj.data('table_id', table_id);
    divObj.find('table:not(#' + table_id + ')').hide('slide', {}, duration);

    var tableObj = $('#' + table_id);
    tableObj.delay(duration).show('slide', {}, duration);

    $('#' + back_link_id).prop("style", "visibility:visible");
    $('#' + row_control_id).prop("style", "display:none");
    //Obtenemos errores
//    var errors = tableObj.data('errors');
//
//    if (errors['tempItems'])
//    {
//        divObj.addClass('us-error');
//    } else
//    {
//        divObj.removeClass('us-error');
//    }
}

function SIANAssistanceSummaryBackToMain(div_id, duration)
{
    var divObj = $('#' + div_id);
    var tableObj = $('#' + div_id + '_table');
    var table_active = divObj.data('table_id');
    var submit_id = divObj.data('submit_id');
    var cancel_id = divObj.data('cancel_id');
    var back_link_id = divObj.data('back_link_id');
    var title_id = divObj.data('title_id');
    var row_control_id = divObj.data('row_control_id');

    var tableActiveObj = $('#' + table_active);
    var instance_id = tableActiveObj.data('instance_id');
    var summary_id = tableActiveObj.data('summary_id');
    var summary = $('#' + summary_id);

    tableObj.find('tr#' + instance_id).find('input.sian-assistance-item-work_hours').val(summary.find('label.sian-assistance-summary-date-total-work-hours').text());
    tableObj.find('tr#' + instance_id).find('input.sian-assistance-item-extrahours1').val(summary.find('input.sian-assistance-summary-date-total-extrahours1').val());
    tableObj.find('tr#' + instance_id).find('input.sian-assistance-item-extrahours2').val(summary.find('input.sian-assistance-summary-date-total-extrahours2').val());
    tableObj.find('tr#' + instance_id).find('input.sian-assistance-item-nighthours').val(summary.find('label.sian-assistance-summary-date-total-night-hours').text());
    tableObj.find('tr#' + instance_id).find('input.sian-assistance-item-holiday_hours').val(summary.find('label.sian-assistance-summary-date-total-holiday-hours').text());
    tableObj.find('tr#' + instance_id).find('input.sian-assistance-item-lateness').val(summary.find('label.sian-assistance-summary-date-total-lateness').text());
    tableObj.find('tr#' + instance_id).find('input.sian-assistance-item-absences').val(summary.find('label.sian-assistance-summary-date-total-absences').text());

    var absencesTypeCount = 0;
    $.each(tableActiveObj.find('tr'), function (key, item) {
        if ($(this).find('input.sian-assistance-summary-date-absence-checkbox').prop('checked')) {

            if ($(this).find('select.sian-assistance-summary-date-type-absence').val() == '') {
                $(this).find('select.sian-assistance-summary-date-type-absence').parent().addClass('has-error');
                $(this).find('select.sian-assistance-summary-date-type-absence').parent().append('<div class="help-block error">Debe elegir un motivo.</div>');
                absencesTypeCount++;
            }
        }
    });

    if (absencesTypeCount > 0) {
        bootbox.alert(us_message('Debe completar los motivos de inasistencias!', 'warning'));
    } else {
        tableActiveObj.hide('slide', {}, duration, function () {
            tableObj.delay(duration).show('slide', {}, duration);
        });

        $.each(tableActiveObj.find('select.sian-assistance-summary-date-type-absence'), function (key, item) {

            if ($(this).parent().hasClass('has-error')) {
                $(this).parent().removeClass('has-error');
                $(this).parent().find('div').remove();
            }
        });

        $('#' + back_link_id).prop("style", "visibility:hidden");
        $('#' + row_control_id).prop("style", "display:block");
        $('#' + submit_id).prop('disabled', false);
        $('#' + cancel_id).prop('disabled', false);
        $('#' + title_id).text('');
    }
}

function SIANAssistanceSummaryDateUpdateAmounts(table_id)
{
    var twork_hours = 0;
    var textrahours = 0;
    var textrahours1 = 0;
    var textrahours2 = 0;
    var tnight_hours = 0;
    var tholiday_hours = 0;
    var tlateness = 0;
    var tabsences = 0;

    var tableObj = $('#' + table_id);
    var summary_id = tableObj.data('summary_id');
    var summary = $('#' + summary_id);


    tableObj.find('tr.sian-assistance-summary-date').each(function (index) {

        var item = $(this);
        var work_hours = item.find('input.sian-assistance-summary-date-work-hours').double2();
        var extrahours = item.find('input.sian-assistance-summary-date-extrahours').double2();
        var night_hours = item.find('input.sian-assistance-summary-date-night-hours').double2();
        var holiday_hours = item.find('input.sian-assistance-summary-date-holiday-hours').double2();
        var lateness = item.find('input.sian-assistance-summary-date-lateness').double2();
        var absence = item.find('input.sian-assistance-summary-date-absence-checkbox').is(':checked') ? 1 : 0;

        twork_hours = USMath.plus(twork_hours, work_hours);
        if (extrahours > 2) {
            textrahours1 = USMath.plus(textrahours1, 2);
            textrahours2 = USMath.plus(textrahours2, extrahours - 2);
        } else {
            textrahours1 = USMath.plus(textrahours1, extrahours);
        }
        textrahours = USMath.plus(textrahours, extrahours);
        tnight_hours = USMath.plus(tnight_hours, night_hours);
        tholiday_hours = USMath.plus(tholiday_hours, holiday_hours);
        tlateness = USMath.plus(tlateness, lateness);
        tabsences = USMath.plus(tabsences, absence);
    });
    summary.find('label.sian-assistance-summary-date-total-work-hours').floatText(2, twork_hours);
    summary.find('label.sian-assistance-summary-date-total-extrahours').floatText(2, textrahours);
    summary.find('input.sian-assistance-summary-date-total-extrahours1').val(textrahours1);
    summary.find('input.sian-assistance-summary-date-total-extrahours2').val(textrahours2);
    summary.find('label.sian-assistance-summary-date-total-night-hours').floatText(2, tnight_hours);
    summary.find('label.sian-assistance-summary-date-total-holiday-hours').floatText(2, tholiday_hours);
    summary.find('label.sian-assistance-summary-date-total-lateness').floatText(0, tlateness);
    summary.find('label.sian-assistance-summary-date-total-absences').floatText(0, tabsences);
}

function SIANAssitanceSummaryRemoveTable(table_id, confirmation)
{
    if (confirmation ? confirm('¿Está seguro de eliminar esta instancia?') : true)
    {
        $('#' + table_id).remove();
    }
}