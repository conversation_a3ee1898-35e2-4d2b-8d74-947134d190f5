<?php

class USModalForm extends SIANForm {

    public $footer_html;
    public $before_submit = 'true';

    public function init() {

        parent::init();
        //
        Yii::app()->clientScript->registerScript($this->controller->getServerId() . '_basic_scripts', "   
        
        $('#{$this->form->id}').find('input:first,textarea:first,select:first').bind('keydown', function(e) {

            if(e.keyCode == 9 && e.shiftKey)
            {
                $('#{$this->cancel_id}').focus();
                return false;
            }
        });

        $('#{$this->cancel_id}').bind('keydown', function(e) {
            if(e.keyCode == 9 && !e.shiftKey)
            {
                $('#{$this->form->id}').find('input[type=text],textarea,select').filter(':visible:first').focus();
                return false;
            }
        });
        ", CClientScript::POS_END);
        //LLamamos al script de submit
        $this->submitScript();
    }

    public function run() {
        parent::run();
    }

    public function footer() {

        echo "<div class='row'>";
        echo "<div class='col-lg-8 col-md-8 col-sm-12 col-xs-12 text-left'>";
        echo $this->footer_html;
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";

        $buttons = array(
            array(
                'label' => $this->submit_label,
                'icon' => $this->submit_icon,
                'buttonType' => 'submit',
                'context' => 'primary',
                'htmlOptions' => array(
                    'id' => $this->submit_id,
                )
        ));


        if ($this->cancel) {
            $buttons[] = array(
                'icon' => 'fa fa-lg fa-ban black',
                'label' => 'Cerrar',
                'htmlOptions' => array(
                    'id' => $this->cancel_id,
                    'data-dismiss' => 'modal'
                )
            );
        }

        $this->widget('booster.widgets.TbButtonGroup', array('buttons' => $buttons));

        echo "</div>";
        echo "</div>";
    }

    protected function getSubmitUrl() {
        return Yii::app()->request->url;
    }

    protected function submitScript() {
        Yii::app()->clientScript->registerScript($this->controller->getServerId() . '_submit_script', "   

        $('body').on('click', '#{$this->submit_id}', function() {

            if(window.active_ajax == 0)
            {
                $(this).prop('disabled', true);

                if({$this->before_submit})
                {
                    if ( typeof CKEDITOR !== 'undefined' ) {
                        for (instance in CKEDITOR.instances)
                        {
                            //var value = CKEDITOR.instances[instance].getData();
                            //$('#' + instance ).val(value);
                            CKEDITOR.instances[instance].updateElement()
                        }
                    }

                    waitingDialog.show(STRINGS_WAITING_MESSAGE);

                    $.ajax({
                        type: 'post',
                        url: '{$this->getSubmitUrl()}',
                        data: $('#{$this->getForm()->id}').serialize(),
                        success: function (data) {

                            waitingDialog.hide();

                            if(data.success)
                            {
                                $('#' + data.modal_id).data('data', data);
                                $('#' + data.modal_id).data('processData', function(data){

                                    //Procesamos la respuesta
                                    switch(data.type)
                                    {
                                        case 'grid':
                                            $.fn.yiiGridView.update(data.element_id);
                                        break;
                                        case 'jqGrid':
                                            $('#' + data.element_id).trigger('reloadGrid');
                                        break;
                                        case 'primaryKey':
                                            $('#' + data.element_id).val(data.primaryKey).focus().keydown();
                                        break;
                                        case 'string':
                                            $('#' + data.element_id).val(data.toString);
                                        break;
                                        case 'select':
                                            $('#' + data.element_id).html(data.content_html);
                                            $('#' + data.element_id).val(data.primaryKey);
                                            $('#' + data.element_id).change();
                                        break;
                                        case 'html':
                                            $('#' + data.element_id).html(data.content_html);
                                        break;
                                        case 'message':
                                            bootbox.alert(us_message(data.content_html, data.element_id));
                                        break;
                                        case 'redirect':
                                            window.location = data.content_html;
                                        break;
                                        default:
                                            $('#' + data.element_id).data(data.type)(data);
                                        break;
                                    }

                                    if (typeof data.message !== 'undefined')
                                    {
                                        bootbox.alert(us_message(data.message, 'success'));
                                    }

                                });
                                //Cerramos el modal
                                $('#' + data.modal_id).modal('hide');
                            }
                            else
                            {
                                $('#' + data.modal_id).empty().html(data.content_html);
                                $('#' + data.modal_id).find('input.error,textarea.error,select.error').filter(':visible:first').focus();
                            }
                        },
                        error: function(request, status, error) { // if error occured

                            waitingDialog.hide();

                            var s_message = processError(request.responseText);
                            bootbox.alert(us_message(s_message, 'error'));
                            
                            " . (Yii::app()->params['environment'] === YII_ENVIRONMENT_DEVELOPMENT ? "
                            $(this).prop('disabled', false);
                            " : "") . "
                        }, 
                        dataType:'json'
                    });
                    
                    {$this->getSyncQuery()}
                }
                else
                {
                    $(this).prop('disabled', false);
                }
            }
            else
            {
                bootbox.alert(us_message('Hay algún proceso ejecutandose, vuelva a intentarlo', 'warning'));
            }    

            return false;
        });
        ", CClientScript::POS_END);
    }

    protected function getSyncQuery() {
        return '';
    }

}
