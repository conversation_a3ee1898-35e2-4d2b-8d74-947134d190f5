<?xml version="1.0" encoding="UTF-8"?>
<!-- ====================================================================== -->
<!-- ===== Unit Code Codelist Schema Module ===== -->
<!-- ====================================================================== -->
<!--
  	Module of		Unit Code Codelist
  	Agency: 		UN/CEFACT	
  	Version:		1.1	
  	Last change: 	14 January 2005
  	
  	Copyright (C) UN/CEFACT (2006). All Rights Reserved.

	This document and translations of it may be copied and furnished to others, 
	and derivative works that comment on or otherwise explain it or assist 
	in its implementation may be prepared, copied, published and distributed, 
	in whole or in part, without restriction of any kind, provided that the 
	above copyright notice and this paragraph are included on all such copies 
	and derivative works. However, this document itself may not be modified in 
	any way, such as by removing the copyright notice or references to 
	UN/CEFACT, except as needed for the purpose of developing UN/CEFACT 
	specifications, in which case the procedures for copyrights defined in the 
	UN/CEFACT Intellectual Property Rights document must be followed, or as 
	required to translate it into languages other than English.

	The limited permissions granted above are perpetual and will not be revoked 
	by UN/CEFACT or its successors or assigns.

	This document and the information contained herein is provided on an "AS IS"
	basis and UN/CEFACT DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING 
	BUT NOT LIMITED TO ANY WARRANTY THAT THE USE OF THE INFORMATION HEREIN WILL 
	NOT INFRINGE ANY RIGHTS OR ANY IMPLIED WARRANTIES OF MERCHANTABILITY OR 
	FITNESS FOR A PARTICULAR PURPOSE.
-->
<xsd:schema targetNamespace="urn:un:unece:uncefact:codelist:specification:66411:2001" 
xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
xmlns:ccts="urn:un:unece:uncefact:documentation:2"
xmlns:clm66411="urn:un:unece:uncefact:codelist:specification:66411:2001" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xsd:simpleType name="UnitCodeContentType">
		<xsd:restriction base="xsd:token">
      <xsd:enumeration value="04">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>small spray</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="05">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lift</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="08">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>heat lot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="10">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>group</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="11">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>outfit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="13">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ration</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="14">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>shot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="15">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>stick</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="16">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred fifteen kg drum</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="17">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred lb drum</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="18">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>fiftyfive gallon (US) drum</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="19">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tank truck</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="20">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>twenty foot container</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="21">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>forty foot container</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="22">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decilitre per gram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="23">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram per cubic centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="24">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>theoretical pound</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="25">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram per square centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="26">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>actual ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="27">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>theoretical ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="28">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="29">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per thousand square feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="30">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>horse power day per air dry metric ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="31">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>catch weight</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="32">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per air dry metric ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="33">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilopascal square metres per gram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="34">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilopascals per millimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="35">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millilitres per square centimetre second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="36">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic feet per minute per square foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="37">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ounce per square foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="38">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ounces per square foot per 0,01 inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="40">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millilitre per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="41">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millilitre per minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="43">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>super bulk bag</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="44">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>fivehundred kg bulk bag</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="45">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>threehundred kg bulk bag</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="46">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>fifty lb bulk bag</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="47">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>fifty lb bag</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="48">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bulk car load</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="53">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>theoretical kilograms</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="54">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>theoretical tonne</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="56">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>sitas</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="57">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mesh</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="58">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>net kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="59">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>part per million</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="60">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>percent weight</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="61">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>part per billion (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="62">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>percent per 1000 hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="63">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>failure rate in time</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="64">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per square inch, gauge</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="66">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>oersted</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="69">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>test specific scale</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="71">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>volt ampere per pound</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="72">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>watt per pound</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="73">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere tum per centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="74">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millipascal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="76">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gauss</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="77">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milli-inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="78">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogauss</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="80">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pounds per square inch absolute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="81">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>henry</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="84">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilopound per square inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="85">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>foot pound-force</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="87">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per cubic foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="89">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>poise</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="90">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>Saybold universal second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="91">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>stokes</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="92">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>calorie per cubic centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="93">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>calorie per gram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="94">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>curl unit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="95">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>twenty thousand gallon (US) tankcar</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="96">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ten thousand gallon (US) tankcar</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="97">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ten kg drum</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="98">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>fifteen kg drum</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1A">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>car mile</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1B">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>car count</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1C">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>locomotive count</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1D">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>caboose count</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1E">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>empty car</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1F">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>train mile</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1G">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>fuel usage gallon (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1H">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>caboose mile</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1I">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>fixed rate</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1J">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ton mile</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1K">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>locomotive mile</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1L">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>total car count</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1M">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>total car mile</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="1X">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>quarter mile</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2A">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>radian per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2B">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>radian per second squared</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2C">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>rÃ¶ntgen</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2I">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>British thermal unit per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2J">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic centimetre per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2K">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic foot per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2L">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic foot per minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2M">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>centimetre per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2N">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decibel</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2P">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilobyte</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2Q">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilobecquerel</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2R">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilocurie</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2U">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megagram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2V">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megagram per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2W">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2X">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>metre per minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2Y">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millirÃ¶ntgen</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="2Z">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millivolt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="3B">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megajoule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="3C">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>manmonth</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="3E">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per pound of product</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="3G">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per piece of product</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="3H">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per kilogram of product</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="3I">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per piece of product</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4A">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bobbin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4B">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cap</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4C">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>centistokes</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4E">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>twenty pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4G">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microlitre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4H">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>micrometre (micron)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4K">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milliampere</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4L">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megabyte</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4M">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milligram per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4N">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megabecquerel</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4O">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microfarad</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4P">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>newton per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4Q">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ounce inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4R">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ounce foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4T">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>picofarad</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4U">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4W">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ton(US) per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="4X">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilolitre per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5A">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>barrel per minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5B">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>batch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5C">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gallon(US) per thousand</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5E">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>MMSCF/day</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5F">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pounds per thousand</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5G">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pump</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5H">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>stage</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5I">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>standard cubic foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5J">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hydraulic horse power</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5K">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>count per minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5P">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>seismic level</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="5Q">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>seismic line</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>15  C calorie</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A10">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere square metre per joule second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A11">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>Ã¥ngstrÃ¶m</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A12">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>astronomical unit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A13">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>attojoule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A14">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>barn</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A15">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>barn per electron volt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A16">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>barn per steradian electron volt,</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A17">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>barn per sterdian</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A18">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>becquerel per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A19">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>becquerel per metre cubed</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere per centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A20">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>British thermal unit per second square foot degree Rankin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A21">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>British thermal unit per pound degree Rankin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A22">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>British thermal unit per second foot degree Rankin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A23">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>British thermal unit per hour square foot degree Rankin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A24">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>candela per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A25">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cheval vapeur</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A26">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A27">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb metre squared per volt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A28">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb per cubic centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A29">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere per millimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A30">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb per cubic millimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A31">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb per kilogram second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A32">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A33">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb per square centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A34">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A35">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb per square millimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A36">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic centimetre per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A37">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic decimetre per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A38">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic metre per coulomb</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A39">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic metre per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A4">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere per square centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A40">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic metre per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A41">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A42">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>curie per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A43">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>deadweight tonnage</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A44">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decalitre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A45">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decametre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A47">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decitex</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A48">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>degree Rankin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A49">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>denier</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A5">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A50">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dyn second per cubic centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A51">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dyne second per centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A52">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dyne second per centimetre to the fifth</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A53">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>electronvolt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A54">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>electronvolt  per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A55">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>electronvolt square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A56">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>electronvolt square metre per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A57">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>erg</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A58">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>erg per centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A6">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere per square metre kelvin squared</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A60">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>erg per cubic centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A61">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>erg per gram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A62">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>erg per gram second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A63">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>erg per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A64">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>erg per second square centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A65">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>erg per square centimetre second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A66">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>erg square centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A67">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>erg square centimetre per gram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A68">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>exajoule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A69">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>farad per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A7">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere per square millimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A70">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>femtojoule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A71">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>femtometre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A73">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>foot per second squared</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A74">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>foot pound-force per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A75">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>freight ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A76">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A77">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>Gaussian CGS unit of displacement</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A78">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>Gaussian CGS unit of electic current</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A79">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>Gaussian CGS unit of electric charge</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A8">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A80">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>Gaussian CGS unit of electric field strength</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A81">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>Gaussian CGS unit of electric polarization</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A82">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>Gaussian CGS unit of electric potential</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A83">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>Gaussian CGS unit of magnetization</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A84">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gigacoulomb per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A85">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gigaelectronvolt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A86">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gigahertz</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A87">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gigaohm</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A88">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gigaohm metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A89">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gigapascal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A9">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>rate</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A90">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gigawatt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A91">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gon</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonyms  A91 = grade</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A93">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A94">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A95">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gray</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A96">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gray per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A97">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hectopascal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="A98">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>henry per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ball</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bulk pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ACR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>acre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>byte</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>additional minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>average minute per call</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AJ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cop</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>fathom</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>access line</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampoule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AMH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AMP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ampere</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ANN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>year</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>aluminium pound only</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="APZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>troy ounce or apothecaries&apos; ounce</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>anti-hemophilic factor (AHF) unit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>suppository</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ARE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>are</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>assortment</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ASM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>alcoholic strength by mass</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ASU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>alcoholic strength by volume</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ATM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>standard atmosphere</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ATT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>technical atmosphere</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AV">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>capsule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AW">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>powder filled vial</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AY">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>assembly</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="AZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>British thermal unit per pound</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B0">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>Btu per cubic foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>barrel (US) per day</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B11">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule per kilogram kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B12">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B13">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule per square metre</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  B13 = joule per metre squared</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B14">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule per metre to the fourth power</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B15">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B16">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule per mole kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B18">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bunk</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B20">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule square metre per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B21">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kelvin per watt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B22">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kiloampere</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B23">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kiloampere per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B24">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kiloampere per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B25">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilobecquerel per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B26">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilocoulomb</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B27">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilocoulomb per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B28">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilocoulomb per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B29">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kiloelectronvolt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>batting pound</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B31">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram metre per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B32">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram metre squared</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B33">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram metre squared per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B34">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per cubic decimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B35">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per litre</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  B35 = kilogram per litre of product</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B36">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thermochemical calorie per gram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B37">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram-force</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B38">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram-force metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B39">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram-force metre per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B4">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>barrel, imperial</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B40">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram-force per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B41">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilojoule per kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B42">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilojoule per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B43">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilojoule per kilogram kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B44">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilojoule per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B45">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilomole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B46">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilomole per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B47">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilonewton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B48">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilonewton metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B49">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kiloohm</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B5">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>billet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B50">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kiloohm metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B51">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilopond</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B52">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilosecond</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B53">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilosiemens</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B54">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilosiemens per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B55">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilovolt per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B56">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kiloweber per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B57">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>light year</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B58">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>litre per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B59">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lumen hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B6">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bun</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B60">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lumen per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B61">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lumen per watt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B62">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lumen second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B63">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lux hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B64">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lux second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B65">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>maxwell</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B66">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megaampere per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B67">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megabecquerel per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B69">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megacoulomb per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B7">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cycle</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B70">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megacoulomb per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B71">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megaelectronvolt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B72">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megagram per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B73">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>meganewton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B74">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>meganewton metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B75">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megaohm</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B76">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megaohm metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B77">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megasiemens per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B78">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megavolt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B79">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megavolt per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B8">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B81">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal metre squared reciprocal second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B83">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>metre to the fourth power</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B84">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microampere</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B85">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microbar</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B86">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microcoulomb</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B87">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microcoulomb per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B88">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microcoulomb per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B89">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microfarad per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B9">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>batt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B90">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microhenry</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B91">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microhenry per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B92">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>micronewton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B93">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>micronewton metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B94">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microohm</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B95">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microohm metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B96">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>micropascal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B97">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microradian</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B98">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microsecond</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="B99">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microsiemens</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BAR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bar</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>base box</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>board</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bundle</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BFT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>board foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bag</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>brush</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BHP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>brake horse power</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BIL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>trillion (US)</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  BIL = billion (EUR)</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BJ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bucket</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>basket</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bale</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BLD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dry barrel (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BLL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>barrel (US) (petroleum etc.)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bottle</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred board feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BQL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>becquerel</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bar</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bolt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BTU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>British thermal unit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BUA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bushel  (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BUI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bushel (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BW">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>base weight</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BX">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>box</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="BZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>million BTUs</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C0">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>call</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>composite product pound (total weight)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C10">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millifarad</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C11">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milligal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C12">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milligram per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C13">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milligray</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C14">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millihenry</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C15">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millijoule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C16">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millimetre per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C17">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millimetre squared per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C18">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millimole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C19">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mole per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>carset</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C20">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millinewton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C22">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millinewton per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C23">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milliohm metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C24">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millipascal second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C25">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milliradian</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C26">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millisecond</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C27">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millisiemens</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C28">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millisievert</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C29">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millitesla</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microvolt per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C30">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millivolt per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C31">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milliwatt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C32">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milliwatt per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C33">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milliweber</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C34">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C35">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mole per cubic decimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C36">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mole per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C38">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mole per litre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C39">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nanoampere</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C4">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>carload</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C40">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nanocoulomb</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C41">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nanofarad</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C42">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nanofarad per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C43">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nanohenry</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C44">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nanohenry per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C45">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nanometre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C46">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nanoohm metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C47">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nanosecond</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C48">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nanotesla</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C49">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nanowatt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C5">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cost</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C50">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>neper</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C51">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>neper per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C52">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>picometre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C53">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>newton metre second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C54">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>newton metre squared  kilogram squared</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C55">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>newton per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C56">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>newton per square millimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C57">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>newton second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C58">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>newton second per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C59">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>octave</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C6">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cell</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C60">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ohm centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C61">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ohm metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C62">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>one</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C63">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>parsec</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C64">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pascal per kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C65">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pascal second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C66">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pascal second per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C67">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pascal second per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C68">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>petajoule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C69">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>phon</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C7">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>centipoise</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C70">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>picoampere</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C71">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>picocoulomb</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C72">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>picofarad per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C73">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>picohenry</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C75">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>picowatt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C76">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>picowatt per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C77">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound gage</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C78">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound-force</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C8">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millicoulomb per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C80">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>rad</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C81">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>radian</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C82">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>radian meter squared per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C83">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>radian metre squared per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C84">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>radian per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C85">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal â€ ngstr&quot;m</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C86">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C87">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal cubic metre per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C88">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal electron volt per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C89">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal henry</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C9">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coil group</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C90">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal joule per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C91">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal kelvin or kelvin to the power minus one</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C92">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C93">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal square metre</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  C93 = reciprocal metre squared</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C94">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C95">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C96">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal pascal or pascal to the power minus one</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C97">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C98">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal second per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="C99">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal second per metre squared</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>can</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CCT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>carrying capacity in metric ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CDL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>candela</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CEL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>degree Celsius</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CEN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>card</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CGM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>centigram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>container</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CJ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cone</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>connector</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CKG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coil</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CLF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred leave</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CLT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>centilitre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CMK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CMQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CMT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CNP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CNT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cental (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>carboy</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="COU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>coulomb</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cartridge</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>crate</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>case</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>carton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CTM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>metric carat</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cup</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CUR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>curie</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CV">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cover</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CWA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred pounds (cwt)/hundred weight (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CWI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred weight (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CY">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cylinder</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="CZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>combo</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal second per steradian</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D10">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>siemens per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D12">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>siemens square metre per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D13">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>sievert</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D14">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand linear yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D15">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>sone</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D16">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square centimetre per erg</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D17">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square centimetre per steradian erg</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D18">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>metre kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D19">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square metre kelvin per watt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reciprocal second per steradian metre squared</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D20">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square metre per joule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D21">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square metre per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D22">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square metre per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D23">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pen gram (protein)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D24">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square metre per steradian</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D25">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square metre per steradian joule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D26">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square metre per volt second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D27">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>steradian</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D28">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>syphon</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D29">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>terahertz</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D30">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>terajoule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D31">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>terawatt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D32">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>terawatt hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D33">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tesla</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D34">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tex</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D35">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thermochemical calorie</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D37">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thermochemical calorie per gram kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D38">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thermochemical calorie per second centimetre kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D39">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thermochemical calorie per second square centimetre kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D40">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand litre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D41">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tonne per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D42">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tropical year</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D43">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>unified atomic mass unit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D44">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>var</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D45">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>volt  squared per kelvin squared</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D46">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>volt - ampere</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D47">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>volt per centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D48">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>volt per kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D49">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millivolt per kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D5">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per square centimeter</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D50">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>volt per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D51">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>volt per millimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D52">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>watt per kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D53">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>watt per metre kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D54">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>watt per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D55">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>watt per square metre kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D56">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>watt per square metre kelvin to the fourth power</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D57">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>watt per steradian</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D58">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>watt per steradian square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D59">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>weber per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D6">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>rÃ¶ntgen per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D60">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>weber per millimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D61">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D62">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D63">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>book</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D64">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>block</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D65">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>round</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D66">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cassette</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D67">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dollar per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D69">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>inch to the fourth power</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D7">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>sandwich</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D70">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>International Table (IT) calorie</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D71">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>International Table (IT) calorie per second centimetre kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D72">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>International Table (IT) calorie per second square centimetre kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D73">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D74">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per mole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D75">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>International Table (IT) calorie per gram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D76">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>International Table (IT) calorie per gram kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D77">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megacoulomb</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D79">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>beam</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D8">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>draize score</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D80">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microwatt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D81">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microtesla</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D82">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microvolt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D83">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millinewton metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D85">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microwatt per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D86">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millicoulomb</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D87">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millimole per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D88">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millicoulomb per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D89">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millicoulomb per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D9">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dyne per square centimeter</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D90">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic metre (net)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D91">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>rem</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D92">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>band</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D93">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>second per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D94">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>second per radian cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D95">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule per gram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D96">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound gross</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D97">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pallet/unit load</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D98">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mass pound</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="D99">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>sleeve</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DAA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decare</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DAD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ten day</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DAY">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>day</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dry pound</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>disk (disc)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>degree</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>deal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DEC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decade</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decigram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dispenser</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DJ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decagram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DLT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decilitre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DMK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square decimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DMQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic decimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DMT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decinewton metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DPC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dozen piece</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DPR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dozen pair</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DPT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>displacement tonnage</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>data record</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>drum</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DRA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dram (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DRI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dram (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DRL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dozen roll</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DRM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>drachm (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>display</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dry ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DTN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>decitonne</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  DTN = centner, metric
Synonym  DTN = quintal, metric</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dyne</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DWT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pennyweight</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DX">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dyne per centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DY">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>directory book</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DZN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dozen</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="DZP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dozen pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="E2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>belt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="E3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>trailer</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="E4">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gross kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="E5">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>metric long ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="EA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>each</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="EB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>electronic mail box</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="EC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>each per month</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="EP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>eleven pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="EQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>equivalent gallon</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="EV">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>envelope</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="F1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand cubic feet per day</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="F9">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>fibre per cubic centimetre of air</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FAH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>degree Fahrenheit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FAR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>farad</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>field</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand cubic feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>million particle per cubic foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>track foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>transdermal patch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>micromole</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>flake ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>million cubic feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FOT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per square foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>foot per minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>foot per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FTK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="FTQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="G2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>US gallon per minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="G3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>Imperial gallon per minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="G7">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microfiche sheet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gallon (US) per day</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GBQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gigabecquerel</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram per 100 gram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gross barrel</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per gallon (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram per metre (gram per 100 centimetres)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GFI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram of fissile isotope</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GGR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>great gross</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>half gallon (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GIA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gill (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GII">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gill (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GJ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram per millilitre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram per litre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GLD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dry gallon (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GLI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gallon (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GLL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gallon (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gross gallon</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milligrams per square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milligram per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microgram per cubic meter</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GRM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GRN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>grain</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GRO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gross</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GRT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gross register ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gross ton</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  GT = metric gross ton</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GV">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gigajoule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GW">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gallon per thousand cubic feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GWH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gigawatt hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GY">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gross yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="GZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>gage system</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="H1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>half page - electronic</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="H2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>half litre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hank</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HAR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hectare</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HBA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hectobar</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HBX">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred boxe</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred count</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>half dozen</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundredth of a carat</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HGM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hectogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred cubic feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred sheet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HIU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred international unit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HJ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>metric horse power</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred feet (linear)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HLT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hectolitre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mile per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HMQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>million cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HMT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hectometre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>conventional millimetre of mercury</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred troy ounce</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>conventional millimetre of water</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HPA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hectolitre of pure alcohol</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred square feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>half hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HTZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hertz</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HUR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="HY">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="IA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>inch pound (pound inch)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="IC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>count per inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="IE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>person</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="IF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>inches of water</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="II">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>column inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="IL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>inch per minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="IM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>impression</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="INQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>inch cubed</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="IP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>insurance policy</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="IT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>count per centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="IU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>inch per second (linear speed)</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  IU = inch per second (vibration)</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="IV">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>inch per second squared (acceleration)</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  IV = inch per second squared (vibration acceleration)</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="J2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="JB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>jumbo</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="JE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule per kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="JG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>jug</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="JK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megajoule per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="JM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megajoule per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="JO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joint</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="JOU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>joule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="JR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>jar</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="K1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilowatt demand</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="K2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilovolt ampere reactive demand</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="K3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilovolt ampere reactive hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="K5">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilovolt ampere (reactive)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="K6">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilolitre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cake</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilocharacter</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KBA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilobar</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram decimal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KEL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kelvin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilopacket</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>keg</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KGM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KGS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KHZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilohertz</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per millimetre width</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KJ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilosegment</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KJO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilojoule</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KMH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilometre per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KMK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square kilometre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KMQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram per cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KNI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram of nitrogen</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KNS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram named substance</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KNT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>knot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milliequivalence caustic potash per gram of product</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KPA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilopascal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KPH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram of potassium hydroxide (caustic potash)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KPO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram of potassium oxide</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KPP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram of phosphorus pentoxide (phosphoric anhydride)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilorÃ¶ntgen</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand pound per square inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KSD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram of substance 90 % dry</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KSH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram of sodium hydroxide (caustic soda)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KTM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilometre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KTN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilotonne</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KUR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilogram of uranium</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KVA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilovolt - ampere</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KVR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilovar</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KVT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilovolt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KW">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilograms per millimeter</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KWH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilowatt hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KWT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kilowatt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="KX">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millilitre per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="L2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>litre per minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per cubic inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LBR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym LBR = pound decimal</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LBT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>troy pound (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>linear centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>litre per day</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lite</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LEF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>leaf</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>linear foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>labour hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>linear inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LJ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>large spray</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>link</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>linear metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>length</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>liquid pound</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LPA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>litre of pure alcohol</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>layer</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lump sum</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LTN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ton (UK) or longton (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LTR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>litre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LUM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lumen</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LUX">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lux</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LX">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>linear yard per pound</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="LY">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>linear yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="M0">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>magnetic tape</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="M1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milligrams per litre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="M4">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>monetary value</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="M5">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microcurie</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="M7">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>micro-inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="M9">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>million Btu per 1000 cubic feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>machine per unit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MAL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mega litre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MAM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megametre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MAW">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megawatt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MBE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand standard brick equivalent</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MBF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand board feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MBR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millibar</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>microgram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MCU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millicurie</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>air dry metric ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milligram per square foot per side</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MGM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milligram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MHZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megahertz</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MIK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square mile</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MIL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MIN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MIO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>million</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MIU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>million international unit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milligram per square inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MLD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milliard</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  MLD = billion (US)</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MLT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millilitre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MMK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square millimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MMQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic millimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MMT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MON">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>month</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MPA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megapascal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MQH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic metre per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MQS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic metre per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MSK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>metre per second squared</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mat</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MTK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MTQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic metre</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  MTQ = metre cubed</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MTR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MTS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>metre per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MV">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of mults</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MVA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megavolt - ampere</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="MWH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>megawatt hour (1000 kW.h)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="N1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pen calorie</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="N2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of lines</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="N3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>print point</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>milligram per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NAR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of  articles</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>barge</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NBB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of bobbins</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>car</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NCL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of cells</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ND">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>net barrel</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>net litre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NEW">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>newton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>message</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>net gallon (us)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>message hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>net imperial gallon</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NIU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of international units</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NJ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of screens</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>load</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NMI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nautical mile</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NMP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of packs</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>train</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NPL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of parcels</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NPR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of pairs</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NPT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of parts</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mho</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>micromho</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NRL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>number of rolls</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>net ton</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  NT = metric net ton</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NTT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>net register ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>newton metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NV">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>vehicle</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NX">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>part per thousand</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="NY">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per air dry metric ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="OA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>panel</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="OHM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ohm</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ON">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ounce per square yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ONZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ounce</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="OP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>two pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="OT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>overtime hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="OZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ounce av</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="OZA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>fluid ounce (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="OZI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>fluid ounce (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="P0">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>page - electronic</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="P1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>percent</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="P2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="P3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>three pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="P4">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>four pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="P5">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>five pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="P6">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>six pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="P7">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>seven pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="P8">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>eight pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="P9">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>nine pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>packet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PAL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pascal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pair inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pad</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound equivalent</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pallet (lift)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>plate</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PGL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>proof gallon</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pitch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>package</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  PK = pack</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pail</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound percentage</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound net</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per inch of length</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>page per inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pair</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound-force per square inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pint (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PTD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dry pint (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PTI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pint  (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PTL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>liquid pint (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tray / tray pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PV">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>half pint (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PW">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per inch of width</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PY">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>peck dry (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="PZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>peck dry (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="Q3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>meal</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>page - facsimile</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QAN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>quarter (of a year)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>page - hardcopy</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>quarter dozen</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>quarter hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>quarter kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>quire</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>quart (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QTD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>dry quart (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QTI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>quart (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QTL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>liquid quart (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="QTR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>quarter (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="R1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pica</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="R4">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>calorie</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="R9">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand cubic metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>rack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>rod</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ring</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>running or operating hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>roll metric measure</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reel</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ream</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ream metric measure</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>roll</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>pound per ream</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RPM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>revolutions per minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RPS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>revolutions per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>reset</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>revenue ton mile</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="RU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>run</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="S3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square foot per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="S4">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square metre per second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="S5">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>sixty fourths of an inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="S6">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>session</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="S7">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>storage unit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="S8">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>standard advertising unit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>sack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SAN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>half year (6 months)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SCO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>score</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SCR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>scruple</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>solid pound</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>section</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SEC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>second</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SET">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>set</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>segment</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SHT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>shipping ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SIE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>siemens</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>split tanktruck</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>slipsheet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SMI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mile (statute mile)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square rod</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SO">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>spool</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>shelf package</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>strip</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>sheet  metric measure</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SST">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>short standard (7200 matches)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ST">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>sheet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="STI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>stone (UK)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="STN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ton (US) or short ton (UK/US)</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  STN = net ton (2000 lb)</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SV">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>skid</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SW">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>skein</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="SX">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>shipment</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="T0">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>telecommunication line in service</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="T1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand pound gross</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="T3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand piece</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="T4">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand bag</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="T5">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand casing</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="T6">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand gallon (US)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="T7">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand impression</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="T8">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand linear inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tenth cubic foot</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TAH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>kiloampere hour (thousand ampere hour)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>truckload</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>therm</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tote</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ten square yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand square inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TJ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand square centimetre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tank, rectangular</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand feet (linear)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TN">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tin</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TNE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tonne (metric ton)</ccts:CodeName>
            <ccts:CodeDescription>Remarks by GEFEG mbH:
Synonym  TNE = metric ton</ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ten pack</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TPR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ten pair</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TQD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand cubic metre per day</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ten square feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TRL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>trillion (EUR)</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand square feet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TSD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tonne of substance 90 % dry</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TSH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ton of steam per hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand linear metre</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TU">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tube</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TV">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TW">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>thousand sheet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="TY">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tank, cylindrical</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="U1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>treatment</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="U2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tablet</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="UA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>torr</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="UB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>telecommunication  line in service average</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="UC">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>telecommunication port</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="UD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tenth minute</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="UE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>tenth hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="UF">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>usage per telecommunication line average</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="UH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ten thousand yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="UM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>million unit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="VA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>volt ampere per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="VI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>vial</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="VLT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>volt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="VQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>bulk</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="VS">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>visit</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="W2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>wet kilo</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="W4">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>two week</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WA">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>watt per kilogram</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>wet pound</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WCD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cord</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>wet ton</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WEB">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>weber</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WEE">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>week</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WG">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>wine gallon</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WH">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>wheel</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WHR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>watt hour</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WI">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>weight per square inch</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WM">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>working month</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WR">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>wrap</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WSD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>standard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WTT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>watt</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="WW">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>millilitre of water</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="X1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>chain</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="YDK">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>square yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="YDQ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cubic yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="YL">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hundred linear yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="YRD">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="YT">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>ten yard</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="Z1">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lift van</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="Z2">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>chest</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="Z3">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>cask</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="Z4">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>hogshead</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="Z5">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>lug</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="Z6">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>conference point</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="Z8">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>newspage agate line</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ZP">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>page</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
      <xsd:enumeration value="ZZ">
        <xsd:annotation>
          <xsd:documentation>
            <ccts:CodeName>mutually defined</ccts:CodeName>
            <ccts:CodeDescription></ccts:CodeDescription>
          </xsd:documentation>
        </xsd:annotation>
      </xsd:enumeration>
		</xsd:restriction>
	</xsd:simpleType>
</xsd:schema>
