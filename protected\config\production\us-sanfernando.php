<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'sanfernando.siansystem.com/admin';
$domain = 'https://sanfernando.siansystem.com';
$domain2 = 'http://sanfernando.siansystem.com';
$report_domain = 'rsanfernando.siansystem.com';
$org = 'sanfernando';
$database_server = '69.10.37.246';
$database_name = 'sanfernando';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = 'sanfernando.p12';
$e_billing_certificate_pass = 'dHjiUbXEG8hDH658';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20481918783BILLING1',
        'password' => 'Sanfernando2'
    ],
];
$smtp_username = '<EMAIL>';
$smtp_password = 'Ag52nBN&AJBS5qR&1';
$environment_reports = YII_ENVIRONMENT_PRODUCTION;
$environment = YII_ENVIRONMENT_PRODUCTION;
