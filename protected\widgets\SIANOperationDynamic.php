<?php

class SIANOperationDynamic extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $column_items;
    public $field_items;
    //PRIVATE
    private $controller;
    private $entry_number_input_id;
    private $column_input_id;
    private $field_input_id;
    private $formula_input_id;
    private $fee_input_id;
    private $account_auto_id;
    private $add_button_id;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->modal_id = isset($this->modal_id) ? $this->modal_id : $this->controller->getServerId();
        //PRIVATE
        $this->entry_number_input_id = $this->controller->getServerId();
        $this->column_input_id = $this->controller->getServerId();
        $this->formula_input_id = $this->controller->getServerId();
        $this->field_input_id = $this->controller->getServerId();
        $this->fee_input_id = $this->controller->getServerId();
        $this->account_auto_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();

        //ITEMS
        $a_dynamic_items = [];

        foreach ($this->model->tempDynamics as $dynamic) {
            $a_dynamic_items[] = [
                'entry_number' => $dynamic->entry_number,
                'column' => $dynamic->column,
                'field' => $dynamic->field,
                'account_code' => $dynamic->account_code,
                'account_name' => $dynamic->account_name,
                'formula' => isset($dynamic->formula) ? $dynamic->formula : '',
                'fee' => $dynamic->fee == 1 ? 1 : 0,
                'errors' => $dynamic->getErrors()
            ];
        }

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-operation-dynamic.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        $(document).ready(function() {
            //ACCESS
            $('#{$this->id}').data('view-access', " . json_encode($this->controller->checkRoute($this->getRoute())) . ");
            //URL
            $('#{$this->id}').data('view-url', '{$this->controller->createUrl($this->getRoute())}');
            //COUNT
            $('#{$this->id}').data('column_items', " . CJSON::encode($this->column_items) . ");
            $('#{$this->id}').data('field_items', " . CJSON::encode($this->field_items) . ");
            $('#{$this->id}').data('count', 0);
            //MODAL
            $('#{$this->id}').data('modal_id', '{$this->modal_id}');

            //MOVIMIENTOS
            var array = " . CJSON::encode($a_dynamic_items) . ";

            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {
                    SIANOperationDynamicAddItem('{$this->id}', '{$this->model->type}', array[i]['entry_number'], array[i]['column'], array[i]['field'], array[i]['formula'], array[i]['account_code'], array[i]['account_name'], array[i]['fee'], array[i]['errors']);
                }
            }
            else
            {
                $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
            }
           
            SIANOperationDynamicUpdate('{$this->id}');
            unfocusable();
            
            //Para hacer reordenable las filas
            SIANOperationDynamicSortable('{$this->id}');
     
            $('#{$this->field_input_id}').focus();
        });
        
        $('select.sian-operation-dynamic-item-field').change(function (){
            var element = $(this);
            element.parents('tr.sian-operation-dynamic-item').find('input.sian-operation-dynamic-account-formula').prop('disabled', element.val() !== 'formula');
        });
        
        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var entry_number = $('#{$this->entry_number_input_id}').val();
            var column = $('#{$this->column_input_id}').val();
            var field = $('#{$this->field_input_id}').val();
            var account_code = USAutocompleteField('{$this->account_auto_id}', 'value');
            var account_name = USAutocompleteField('{$this->account_auto_id}', 'text');
            var formula = $('#{$this->formula_input_id}').val();
            var fee = $('#{$this->fee_input_id}').prop('checked');

            if(column && field)
            {                
                if(field == 'formula'){
                    if(!formula){
                        bootbox.alert(us_message('Ingrese una Formula!', 'warning'));
                        USAutocompleteFocus('{$this->formula_input_id}');
                        return false;
                    }
                }
                if(!account_code)
                {
                    bootbox.alert(us_message('Debe elegir una cuenta!', 'warning'));
                    USAutocompleteFocus('{$this->account_auto_id}');
                    return false;
                }
                SIANOperationDynamicAddItem('{$this->id}', '{$this->model->type}', entry_number, column, field, formula, account_code, account_name, fee, []); 
                
                USAutocompleteReset('{$this->account_auto_id}');                    
                SIANOperationDynamicUpdate('{$this->id}');
                $('#{$this->fee_input_id}').prop('checked', false);
                unfocusable();
            }
        });  

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array('title' => $this->model->getAttributeLabel('tempDynamics'),
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempDynamics') ? 'us-error' : ''
            )
        ));
        echo "<div class='row'>";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
        echo SIANForm::numberFieldNonActive($this->model->getAttributeLabel('dynamics.entry_number'), null, 1, array(
            'id' => $this->entry_number_input_id,
        ));
        echo "</div>";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
        echo SIANForm::dropDownListNonActive($this->model->getAttributeLabel('dynamics.column'), null, null, $this->column_items, array(
            'id' => $this->column_input_id,
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-1 col-sm-1 col-xs-12'>";
        echo SIANForm::dropDownListNonActive($this->model->getAttributeLabel('dynamics.field'), null, null, $this->field_items, array(
            'id' => $this->field_input_id,
            'onchange' => "$('#{$this->formula_input_id}').prop('disabled', this.value !== 'formula')"
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-1 col-sm-1 col-xs-12  divformula'>";
        echo SIANForm::textFieldNonActive($this->model->getAttributeLabel('dynamics.formula'), null, null, array(
            'id' => $this->formula_input_id,
            'placeholder' => $this->model->getAttributeLabel('dynamics.formula'),
            'disabled' => true,
        ));
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm4 col-xs-12'>";
        $o_dpAccountScenario = $this->model->type == Scenario::TYPE_DYNAMIC ? DpAccount::SCENARIO_DYNAMIC : DpAccount::SCENARIO_NORMAL;
        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->account_auto_id,
            'label' => 'Seleccionar cuenta/objeto',
            'name' => null,
            'view' => array(
                'model' => 'DpAccount',
                'scenario' => $o_dpAccountScenario,
                'attributes' => array(
                    array('name' => 'account_id', 'hidden' => true, 'types' => array('id')),
                    array('name' => 'account_code', 'width' => 30, 'types' => array('value', 'aux')),
                    array('name' => 'account_name', 'width' => 50, 'types' => array('text')),
                    array('name' => 'level', 'width' => 10),
                    array('name' => 'element_code', 'width' => 10),
                ),
                'params' => "{
                                internal:{$this->model->internal}                                
                            }",
            ),
            'maintenance' => array(
                'module' => 'accounting',
                'controller' => 'account',
            ),
            'parent_id' => $this->modal_id
        ));
        echo "</div>";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
        echo SIANForm::checkBoxNonActive($this->model->getAttributeLabel('dynamics.fee'), null, false, array(
            'id' => $this->fee_input_id,
            'readonly' => $this->model->type !== Scenario::TYPE_LOAN
        ));
        echo "</div>";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
        echo CHtml::label('Agregar', $this->add_button_id, []);
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_button_id,
            'block' => true,
            'context' => 'primary',
            'icon' => 'fa fa-lg fa-plus white',
            'size' => 'default',
            'title' => 'Añadir'
        ));
        echo "</div>";
        echo "</div>";
        echo "<hr>";
        echo "<table id='{$this->id}' class='table table-condensed table-hover' type='" . $this->model->type . "'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th width='5%'>#</th>";
        echo "<th width='10%'>{$this->model->getAttributeLabel('dynamics.entry_number')}</th>";
        echo "<th width='20%'>{$this->model->getAttributeLabel('dynamics.column')}</th>";
        echo "<th width='20%'>{$this->model->getAttributeLabel('dynamics.field')}</th>";
        echo "<th width='20%'>{$this->model->getAttributeLabel('dynamics.formula')}</th>";
        echo "<th width='15%'>{$this->model->getAttributeLabel('dynamics.account_code')}</th>";
        echo "<th width='5%'>{$this->model->getAttributeLabel('dynamics.fee')}</th>";
        echo "<th width='5%'>Operaciones</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody></tbody>";
        echo "<tfoot>";
        echo "</tfoot>";
        echo "</table>";

        echo "<br>";
        echo "<p><b>TIP:</b> Pase el cursor por encima de la cuenta para ver el nombre de la misma.</p>";

        $this->endWidget();
    }

    public function getRoute() {
        return '/accounting/account/preview';
    }

}
