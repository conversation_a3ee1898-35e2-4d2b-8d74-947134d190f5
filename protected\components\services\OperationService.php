<?php

class OperationService {

    /**
     * @var OperationRepositoryInterface
     */
    private $repository;

    /**
     * @param OperationRepositoryInterface $repository
     */
    public function __construct($repository) {
        $this->repository = $repository;
    }

    /**
     * @param int $scenarioId
     * @return array
     */
    public function getOperationForApi($scenarioId, $internal = null, $operationCode = null, $default = null) {

        $operation = $this->repository->getFirstOperation($scenarioId, $internal, $operationCode, $default);

        if (!$operation) {
            USREST::sendResponse(USREST::CODE_NOT_FOUND, 'No se encuentra una operación adecuada para proceder.');
            Yii::app()->end();
        }

        return $operation;
    }

    /**
     * @param int $scenarioId
     * @param int|null $internal
     * @return array
     * @throws Exception
     */
    public function getDefaultOperationForSystem($scenarioId, $internal = null, $operationCode = null, $default = null) {

        $operations = $this->repository->getAllOperations($scenarioId, $internal, $operationCode, $default);

        $default = null;
        $first = null;

        foreach ($operations as $i => $op) {
            if ($i === 0) {
                $first = $op;
            }
            if (isset($op['default']) && $op['default'] == 1) {
                $default = $op;
            }
        }

        if ($default)
            return $default;
        if ($first)
            return $first;

        throw new Exception(USREST::CODE_NOT_FOUND . ' No se encuentra una operación adecuada para proceder.');
    }

}
