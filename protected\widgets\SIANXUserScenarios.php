<?php

class SIANXUserScenarios extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $modules = [];
    public $person_items = null;
    public $seller_items = null;
    public $email_type_items = null;
    public $preffix = null;
    public $empty_placeholder = Strings::SELECT_OPTION;
    //PRIVATE
    private $controller;
    private $include_sellers = false;

    public function init() {

        $this->controller = Yii::app()->controller;

        //Control
        $this->include_sellers = $this->controller->getOrganization()->globalVar->enable_sellers == 1;

        if (!isset($this->preffix)) {
            throw new Exception('Debe especificar un prefijo');
        }

        if (!isset($this->model)) {
            throw new Exception('Debe especificar un modelo');
        }

        if (!method_exists($this->model, 'searchScenario')) {
            throw new Exception("El modelo debe tener su correspondiente método 'searchScenario'");
        }

        if (!isset($this->person_items)) {
            throw new Exception('Debe especificar el listado de personas');
        }

        if ($this->include_sellers && !isset($this->seller_items)) {
            throw new Exception('Debe especificar el listado de vendedores');
        }

        if (!isset($this->email_type_items)) {
            throw new Exception('Debe especificar el listado de tipos de correo');
        }

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //PRIVATE
//        $this->account_code_input_id = $this->controller->getServerId();
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-x-user-scenarios.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id, "
            
        $(document).ready(function() {                  
            //ACCESS
            var divObj = $('#{$this->id}');
            divObj.data('preffix', '{$this->preffix}');
            divObj.data('include_sellers', " . CJSON::encode($this->include_sellers) . ");                
        });
        
        $('body').on('click', '#{$this->id} input.{$this->preffix}-scenarios', function () {
            var element = $(this);
            SIANXUserScenariosTrigger('{$this->id}', element);
        });
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Accesos',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-1 col-md-1 col-sm-3 col-xs-12'>";
        echo $this->_renderCheckBoxes();
        echo "</div>";
        echo "<div class='col-lg-11 col-md-11 col-sm-9 col-xs-12'>";
        echo $this->_renderModules($this->modules);
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

    private function _renderModules($p_a_modules) {
        $a_tabs = [];
        for ($i = 0; $i < count($p_a_modules); $i++) {
            $a_tabs[] = [
                'id' => $this->controller->getServerId(),
                'active' => $i == 0,
                'label' => USString::htmlMultiline($this->modules[$i]->title, 30),
                'content' => $this->_renderModule($p_a_modules[$i])
            ];
        }

        if (count($a_tabs) > 0) {
            return $this->widget('booster.widgets.TbTabs', [
                        'id' => $this->controller->getServerId(),
                        'type' => 'tabs',
                        'placement' => 'left',
                        'encodeLabel' => false,
                        'tabs' => $a_tabs,
                        'htmlOptions' => array(
                            'id' => $this->controller->getServerId(),
                        )
                            ], true);
        } else {
            return "<i>Por ahora no hay permisos que se puedan asignar directamente</i>";
        }
    }

    private function _renderModule($p_o_module) {
        $s_html = "<div class='row'>";
        $s_html .= "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-2'>";
        $s_html .= $this->_renderCheckBoxes($p_o_module->module);
        $s_html .= "</div>";
        $s_html .= "<div class='col-lg-10 col-md-10 col-sm-10 col-xs-10'>";
        $s_html .= $this->_renderScenarios($p_o_module->scenarios);
        $s_html .= "</div>";
        $s_html .= "</div>";

        return $s_html;
    }

    private function _renderScenarios($p_a_scenarios) {
        $a_tabs = [];
        for ($i = 0; $i < count($p_a_scenarios); $i++) {
            $a_tabs[] = array(
                'id' => $this->controller->getServerId(),
                'active' => $i == 0,
                'label' => USString::htmlMultiline($p_a_scenarios[$i]->title, 30),
                'content' => $this->_renderScenario($p_a_scenarios[$i])
            );
        }

        return $this->widget('booster.widgets.TbTabs', [
                    'id' => $this->controller->getServerId(),
                    'type' => 'tabs',
                    'placement' => 'left',
                    'encodeLabel' => false,
                    'tabs' => $a_tabs,
                    'htmlOptions' => array(
                        'id' => $this->controller->getServerId(),
                    )
                        ], true);
    }

    private function _renderScenario($p_o_scenario) {

        $a_scenario = $this->model->searchScenario($p_o_scenario->module, $p_o_scenario->scenario_id);

        $a_emails = [];
        //Si no hay usuario seleccionado será el usuario por defecto definido en el modelo
        if ($a_scenario === false) {
            $i_person_id = $this->model->person_id;
            $i_seller_id = null;
        } else {
            $i_person_id = $a_scenario['person_id'];
            $i_seller_id = $a_scenario['seller_id'];

            //Si se incluyen emails
            foreach ($a_scenario['email_addresses'] as $s_email_address) {
                $o_email = new Email();
                $o_email->email_address = $s_email_address;
                $a_emails[] = $o_email;
            }
        }

        $a_data = [
            'name' => null,
            'checked' => ($a_scenario !== false),
            'onclick' => null,
            'module' => $p_o_scenario->module,
            'scenario_id' => $p_o_scenario->scenario_id,
            'title' => 'Sobreescibir',
        ];

        $s_html = "<div class='row'>";
        $s_html .= "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $s_html .= $this->_renderCheckbox($a_data);
        $s_html .= $this->_renderPanel($p_o_scenario, $a_scenario, $i_person_id, $i_seller_id);
        $s_html .= $this->_renderEmails($p_o_scenario, $a_scenario, $a_emails);
        $s_html .= "</div>";
        $s_html .= "</div>";

        return $s_html;
    }

    private function _renderPanel($p_o_scenario, $p_a_scenario, $p_i_person_id, $p_i_seller_id) {

        $s_html = '';

        $s_html .= SIANForm::dropDownListNonActive('Usuario por defecto', "Scenarios[{$p_o_scenario->module}][{$p_o_scenario->scenario_id}][person_id]", $p_i_person_id, $this->person_items, [
                    'class' => "{$this->preffix}-scenarios {$this->preffix}-{$p_o_scenario->module}-{$p_o_scenario->scenario_id}",
                    'data-module' => $p_o_scenario->module,
                    'data-scenario_id' => $p_o_scenario->scenario_id,
                    'data-owner' => User::OWNER,
                    'disabled' => $p_a_scenario === false,
                    'empty' => $this->empty_placeholder,
        ]);

        if ($this->include_sellers) {
            //Obtenemos scenarios que tienen relación con vendedores        
            $a_scenario_ids = $this->controller->getSellerScenarioIds();

            if (in_array($p_o_scenario->scenario_id, $a_scenario_ids)) {
                $s_html .= SIANForm::dropDownListNonActive('Vendedor por defecto', "Scenarios[{$p_o_scenario->module}][{$p_o_scenario->scenario_id}][seller_id]", $p_i_seller_id, $this->seller_items, [
                            'class' => "{$this->preffix}-scenarios {$this->preffix}-{$p_o_scenario->module}-{$p_o_scenario->scenario_id}",
                            'data-module' => $p_o_scenario->module,
                            'data-scenario_id' => $p_o_scenario->scenario_id,
                            'data-owner' => Seller::OWNER,
                            'disabled' => $p_a_scenario === false,
                            'empty' => $this->empty_placeholder,
                ]);
            }
        }

        return $this->widget('booster.widgets.TbPanel', array(
                    'title' => 'Relación con otros objetos',
                    'headerIcon' => 'list',
                    'htmlOptions' => array(
                    ),
                    'content' => $s_html
                        ), true);
    }

    private function _renderEmails($p_o_scenario, $p_a_scenario, $p_a_emails) {
        return $this->widget('SIANEmail', array(
                    'items' => $p_a_emails,
                    'types' => $this->email_type_items,
                    'advanced' => 0,
                    'title' => 'Notificar a',
                    'hint' => "Cuando se cree un movimiento de este tipo. No tiene efecto si el API es de tipo 'POS'",
                    'name_preffix' => "Scenarios[{$p_o_scenario->module}][{$p_o_scenario->scenario_id}][emails]",
                    'class' => "{$this->preffix}-scenarios {$this->preffix}-{$p_o_scenario->module}-{$p_o_scenario->scenario_id}",
                    'disabled' => $p_a_scenario === false,
                    'data' => [
                        'module' => $p_o_scenario->module,
                        'scenario_id' => $p_o_scenario->scenario_id,
                    ]
                        ), true);
    }

    private function _renderCheckBoxes($p_s_module = null) {

        $a_checkboxes = [
            [
                'name' => null,
                'checked' => false,
                'module' => $p_s_module,
                'scenario_id' => null,
                'title' => 'Todos',
            ]
        ];

        $s_html = '';
        foreach ($a_checkboxes as $a_checkbox) {
            $s_html .= $this->_renderCheckbox($a_checkbox);
        }

        return $s_html;
    }

    private function _renderCheckbox($p_a_data) {
        $s_id = $this->controller->getServerId();
        $s_content = CHtml::label(CHtml::checkBox($p_a_data['name'], $p_a_data['checked'], array(
                            'id' => $s_id,
                            'class' => "{$this->preffix}-scenarios {$this->preffix}-{$p_a_data['module']}-{$p_a_data['scenario_id']}",
                            'data-module' => $p_a_data['module'],
                            'data-scenario_id' => $p_a_data['scenario_id'],
                        )) . $p_a_data['title'], $s_id);

        return CHtml::tag('div', array('class' => 'checkbox'), $s_content, true);
    }

}
