/**
 * Greek translation for bootstrap-wysihtml5
 */
(function($){
    $.fn.wysihtml5.locale["el-GR"] = {
        font_styles: {
              normal: "Απλό κείμενο",
              h1: "Κεφαλίδα 1",
              h2: "Κεφαλίδα 2",
              h3: "Κεφαλίδα 3",	
              h4: "Κεφαλίδα 4",	
              h5: "Κεφαλίδα 5",	
              h6: "Κεφαλίδα 6"	
		},
        emphasis: {
              bold: "B",
              italic: "I",
              underline: "U"
        },
        lists: {
              unordered: "Λίστα με κουκκίδες",
              ordered: "Αριθμημένη λίστα",
              outdent: "Μείωση εσοχής",
              indent: "Αύξηση εσοχής"
        },
        link: {
              insert: "Εισαγωγή Συνδέσμου",
              cancel: "Άκυρο",
              target: "Άνοιγμα συνδέσμου σε νέο παράθυρο"
        },
        image: {
              insert: "Εισαγωγή Εικόνας",
              cancel: "Άκυρο"
        },
        html: {
            edit: "Επεξεργασία HTML"
        },
        colours: {
            black: "Μαύρο",
            silver: "Ασημί",
            gray: "Γκρι",
            maroon: "Καφέ",
            red: "Κόκκινο",
            purple: "Μωβ",
            green: "Πράσινο",
            olive: "Λαδί",
            navy: "Βαθύ Μπλε",
            blue: "Μπλε",
            orange: "Πορτοκαλί"
        }
    };
}(jQuery));
