<?php

class SIANAccount extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $owner_items;
    public $destiny_type_items;
    public $minimumInputLength = 3;
    //PRIVATE
    private $controller;
    private $account_code_input_id;
    private $owner_input_id;
    private $owner_container_idx;
    private $owner_span_idx;
    private $destiny_type_id;
    private $sian_owner_pair_id;
    private $owner;

    public function init() {

        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //PRIVATE
        $this->account_code_input_id = $this->controller->getServerId();
        $this->destiny_type_id = $this->controller->getServerId();
        $this->owner_input_id = $this->controller->getServerId();
        $this->owner_container_idx = $this->controller->getServerId();
        $this->owner_span_idx = $this->controller->getServerId();
        $this->sian_owner_pair_id = $this->controller->getServerId();

        //PRIVATE
        $this->owner = Account::OWNER;

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-account.js');
        SIANAssets::registerScriptFile('js/us-span.js');
        SIANAssets::registerScriptFile('other/select2/js/select2.min.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
        SIANAssets::registerScriptFile('js/us-select2.js');
        SIANAssets::registerScriptFile('js/us-cost-level.js');
        //
        SIANAssets::registerCssFile('other/select2/css/select2.css');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id, "
            
        var divObj = $('#{$this->id}');
        //ACCESS
        divObj.data('view-access', " . json_encode($this->controller->checkRoute($this->getRoute())) . ");
        //URL
        divObj.data('view-url', '{$this->controller->createUrl($this->getRoute())}');
        //ID
        divObj.data('sian_owner_pair_id', '{$this->sian_owner_pair_id}');
        //Owner
        divObj.data('owner', '" . Account::OWNER . "');
        //
        divObj.data('" . Person::OWNER . "-dp', '{$this->getDp(Person::OWNER)}');
        //
        divObj.data('" . Person::OWNER . "-scenario', '{$this->getScenario(Person::OWNER)}');

        $(document).ready(function() {                  
            var spanHtml = USSpanInit('{$this->owner_span_idx}', '', '{$this->model->owner_id}', '{$this->model->owner_name}', 'Account[owner_id]', 'Account[owner_name]', '{$this->model->getAttributeLabel('owner_id')}', [], '{$this->model->getError('owner_id')}', " . CJSON::encode(in_array($this->model->owner, [Combination::OWNER, Organization::OWNER])) . ", " . CJSON::encode(in_array($this->model->owner, [Organization::OWNER, Warehouse::OWNER])) . ");
            $('#{$this->owner_container_idx}').html(spanHtml);
        });
        
        $('body').on('click', '#{$this->owner_span_idx}', function(e) {

            var owner = $('#{$this->owner_input_id}').val();
           
            var dp = $('#{$this->id}').data(owner + '-dp');
            var scenario = $('#{$this->id}').data(owner + '-scenario');
                    
            switch(owner)
            {
                case '" . Combination::OWNER . "':
                    USCostLevelInit('{$this->owner_span_idx}', 1, 2, 400);
                break;
                default:
                    USSelect2Init('{$this->owner_span_idx}', dp, scenario, [], {$this->minimumInputLength});
                break;
            }
        });
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Básico',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-5 col-md-5 col-sm-5 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model,
            'attribute' => 'account_parent',
            'parent_id' => $this->modal_id,
            'view' => array(
                'model' => 'DpAccount',
                'scenario' => DpAccount::SCENARIO_PARENT,
                'attributes' => array(
                    array(
                        'name' => 'account_id',
                        'hidden' => true,
                        'types' => array('id'),
                    ),
                    array(
                        'name' => 'account_code',
                        'width' => 20,
                        'types' => array('value', 'aux'),
                        'update' => "$('#{$this->account_code_input_id}').val(account_code);"
                    ),
                    array(
                        'name' => 'account_name',
                        'width' => 50,
                        'types' => array('text')
                    ),
                    array(
                        'name' => 'level',
                        'width' => 10,
                    ),
                    array(
                        'name' => 'system_label',
                        'width' => 10,
                    ),
                    array(
                        'name' => 'element_code',
                        'width' => 10,
                    ),
                    array(
                        'name' => 'owner',
                        'hidden' => true,
                        'update' => "$('#{$this->owner_input_id}').val(owner).change();",
                        'search' => false,
                    ),
                    array(
                        'name' => 'destiny_ids',
                        'hidden' => true,
                        'search' => false,
                    ),
                    array(
                        'name' => 'destiny_names',
                        'hidden' => true,
                        'search' => false,
                    ),
                    array(
                        'name' => 'destiny_defaults',
                        'hidden' => true,
                        'search' => false,
                    ),
                )
            ),
            'readonly' => !$this->model->isNewRecord,
            'onselect' => "SIANAccountLoadDestinies('{$this->id}', destiny_ids, destiny_names, destiny_defaults);",
            'onreset' => "$('#{$this->account_code_input_id}').val(null);",
            'maintenance' => array(
                'module' => $this->controller->module->id,
                'controller' => $this->controller->id,
            )
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo $this->form->textFieldRow($this->model, 'account_code', array(
            'id' => $this->account_code_input_id,
            'maxlength' => 7,
            'readonly' => !$this->model->isNewRecord
        ));

        echo "</div>";
        echo "<div class='col-lg-5 col-md-5 col-sm-5 col-xs-12'>";
        echo $this->form->textFieldRow($this->model, 'account_name', array(
            'maxlength' => 100,
            'readonly' => ($this->model->system)
        ));
        echo "</div>";
        echo "</div>";
        //
        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-2 col-sm-5 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'owner', $this->owner_items, array(
            'id' => $this->owner_input_id,
            'onchange' => "
                //Cambiamos DP
                var readonlyArray = ['" . Organization::OWNER . "', '" . Warehouse::OWNER . "'];
                var requiredArray = ['" . Organization::OWNER . "', '" . Combination::OWNER . "'];
                //
                USSpanSetReadonly('{$this->owner_span_idx}', readonlyArray.includes(this.value));
                USSpanSetRequired('{$this->owner_span_idx}', requiredArray.includes(this.value));
                //Limpiamos
                USSpanClear('{$this->owner_span_idx}');
                //Si es organización, la seteamos
                if(this.value === '" . Organization::OWNER . "'){
                    USSpanSet('{$this->owner_span_idx}', '{$this->controller->getOrganization()->organization_id}', '{$this->controller->getOrganization()->organization_id} - {$this->controller->getOrganization()->name}');
                }
            "
        ));
        echo "</div>";
        echo "<div id='{$this->owner_container_idx}' class='col-lg-3 col-md-3 col-sm-5 col-xs-12'>";
        echo "</div>";
        echo "<div class='col-lg-7 col-md-7 col-sm-5 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model,
            'attribute' => 'counterpart',
            'parent_id' => $this->modal_id,
            'view' => array(
                'model' => 'DpAccount',
                'scenario' => DpAccount::SCENARIO_COUNTERPART,
                'attributes' => array(
                    array(
                        'name' => 'account_id',
                        'hidden' => true,
                        'types' => array('id'),
                    ),
                    array(
                        'name' => 'account_code',
                        'width' => 20,
                        'types' => array('value', 'aux'),
                    ),
                    array(
                        'name' => 'account_name',
                        'width' => 50,
                        'types' => array('text')
                    ),
                    array(
                        'name' => 'owner',
                        'width' => 10,
                    ),
                    array(
                        'name' => 'system_label',
                        'width' => 10,
                    ),
                    array(
                        'name' => 'element_code',
                        'width' => 10,
                    ),
                    array(
                        'name' => 'owner',
                        'hidden' => true,
                        'search' => false,
                    )
                )
            ),
            'readonly' => count($this->model->counterPartOf) > 0,
        ));
        echo "</div>";
        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model,
            'attribute' => 'reverse_account',
            'parent_id' => $this->modal_id,
            'view' => array(
                'model' => 'DpAccount',
                'scenario' => DpAccount::SCENARIO_USABLE,
                'attributes' => array(
                    array(
                        'name' => 'account_id',
                        'hidden' => true,
                        'types' => array('id'),
                    ),
                    array(
                        'name' => 'account_code',
                        'width' => 20,
                        'types' => array('value', 'aux'),
                    ),
                    array(
                        'name' => 'account_name',
                        'width' => 50,
                        'types' => array('text')
                    ),
                    array(
                        'name' => 'owner',
                        'width' => 10,
                    ),
                    array(
                        'name' => 'system_label',
                        'width' => 10,
                    ),
                    array(
                        'name' => 'element_code',
                        'width' => 10,
                    ),
                    array(
                        'name' => 'owner',
                        'hidden' => true,
                        'search' => false,
                    )
                )
            ),
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'destiny_type', $this->destiny_type_items, array(
            'id' => $this->destiny_type_id
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'status',
            'disabled' => ($this->model->is_usable == 0),
                ), true);
        echo "</div>";
        echo "</div>";

        $this->endWidget();

        $this->widget('SIANOwnerPair', array(
            'id' => $this->sian_owner_pair_id,
            'model' => $this->model,
            'viewClass' => 'DpOwnerChildren',
            'viewScenario' => DpOwnerChildren::SCENARIO_DESTINIES_FOR_ACCOUNT,
            'extraViewAttributes' => [
                [
                    'name' => 'level',
                    'width' => 10,
                ],
                [
                    'name' => 'element_code',
                    'width' => 10,
                ],
                [
                    'name' => 'counterpart',
                    'width' => 15,
                ]
            ],
            'owner' => $this->owner,
            'input_name' => 'AccountDestiny',
            'property' => 'tempDestinies',
            'title' => $this->model->getAttributeLabel('tempDestinies') . ' (Cuentas con contrapartida)',
            
        ));
    }

    private function getRoute() {
        return '/accounting/account/preview';
    }

    private function getDp($owner) {

        switch ($owner) {
            case Organization::OWNER:
                return 'DpOrganization';
            case Person::OWNER:
                return 'DpBusinessPartner';
        }
    }

    private function getScenario($owner) {
        switch ($owner) {
            case Organization::OWNER:
                return '';
            case Person::OWNER:
                return DpBusinessPartner::SCENARIO_PEOPLE;
        }
    }

}
