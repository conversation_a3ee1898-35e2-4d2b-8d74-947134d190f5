<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of USCriteria
 *
 * <AUTHOR>
 */
class USCriteria extends CDbCriteria {

    public function compare($column, $value, $partialMatch = false, $operator = 'AND', $escape = true) {

        if (is_array($value)) {
            parent::compare($column, $value, $partialMatch, $operator, $escape);
        } else {
            if (!USString::isBlank($value)) {
                if ($partialMatch) {
                    $value = str_replace(' ', '%', $value);
                    $this->addCondition("{$column} like '%{$value}%'", $operator);
                } else {
                    $this->addCondition("{$column} = '{$value}'", $operator);
                }
            }
        }
    }

    /**
     * Verifica si una fecha esta entre otras dos
     * @param type $column Columna a comparar
     * @param type $begin_value Fecha de inicio
     * @param type $end_value Fecha de fin
     * @param type $reverse Si es FALSE indica que la fecha está en formato SQL
     * @param type $operator Por defecto AND
     */
    public function dateBetween($column, $begin_value, $end_value, $reverse = false, $operator = 'AND') {

        $format = $reverse ? Yii::app()->params['date_php_format'] : Yii::app()->params['date_sql_format'];
        $conditions = [];

        $begin = DateTime::createFromFormat($format, $begin_value);
        if ($begin != null) {
            $begin_value = $begin->format(Yii::app()->params['date_sql_format']);
            $conditions[] = "date({$column}) >= '{$begin_value}'";
        }

        //END
        $end = DateTime::createFromFormat($format, $end_value);
        if ($end != null) {
//            $end->modify('+1 day');
            $end_value = $end->format(Yii::app()->params['date_sql_format']);
            $conditions[] = "date({$column}) <= '{$end_value}'";
        }

        if (count($conditions) > 0) {
            $this->addCondition('(' . implode(' AND ', $conditions) . ')', $operator);
        }
    }

}
