<?php

class SIANMultipleLinks extends CWidget {

    public $id;
    public $model;
    public $dataProvider;
    public $links_type = 1;
    public $show_observations = true;
    public $parent_routes = [];
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //
        $this->dataProvider->pagination = false;
    }

    /**
     * Runs the widget.
     */
    public function run() {

        switch ($this->links_type) {
            case 1:
                echo "<h4>Lista de movimientos aplicados:</h4>";
                break;
            case 2:
                echo "<h4>Lista de movimientos origen:</h4>";
                break;
        }

        //ACCESS
        $access1 = $this->controller->checkRoute('/accounting/operation/preview');
        $access2 = $this->controller->checkActionRoutes(['view' => $this->parent_routes]);
        $access3 = $this->controller->checkRoute('/administration/person/preview');
        //GRID
        $columns = [];
        $counter = 0;

        array_push($columns, array(
            'header' => 'Item',
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) use (&$counter) {
                return ++$counter;
            },
        ));

        array_push($columns, array(
            'name' => 'operation_code',
            'type' => 'raw',
            'value' => function ($row) use ($access1) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                    'route' => "/accounting/operation/preview",
                    'label' => $row->operation_name,
                    'title' => 'Ver operación',
                    'class' => 'form',
                    'data' => array(
                        'id' => $row->operation_code
                    ),
                    'visible' => $access1,
                        ), true);
            },
        ));

        array_push($columns, array(
            'name' => 'document',
            'type' => 'raw',
            'value' => function ($row) use ($access2) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                    'route' => "/{$row->route}/view",
                    'label' => $row->document,
                    'title' => 'Ver documento',
                    'params' => array(
                        'id' => $row->movement_id
                    ),
                    'target' => $row->movement_id,
                    'visible' => $access2["/{$row->route}/view"],
                        ), true);
            },
        ));

        array_push($columns, array(
            'name' => 'aux_person_id',
            'type' => 'raw',
            'value' => function ($row) use ($access3) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                    'route' => "/administration/person/preview",
                    'label' => $row->aux_person_name,
                    'title' => 'Ver socio de negocio',
                    'class' => 'form',
                    'data' => array(
                        'id' => $row->aux_person_id
                    ),
                    'visible' => $access3,
                        ), true);
            },
        ));

        array_push($columns, array(
            'name' => 'emission_date',
            'type' => 'raw',
        ));

        array_push($columns, array(
            'header' => 'Monto ' . Currency::getSymbol($this->model->movement->currency),
            'headerHtmlOptions' => array('style' => 'text-align:right'),
            'htmlOptions' => array('style' => 'text-align:right'),
            'type' => 'raw',
            'value' => function ($row) {
                return $row->amount;
            },
        ));

        $gridParams = array(
            'id' => $this->id,
            'type' => 'hover condensed',
            'dataProvider' => $this->dataProvider,
            'enableSorting' => true,
            'selectableRows' => 0,
            'columns' => $columns,
            'template' => '{items}',
            'nullDisplay' => Strings::NONE,
        );
        $this->widget('application.widgets.USGridView', $gridParams);

        $total = 0;

        switch ($this->model->movement->type) {
            case Scenario::TYPE_COMMERCIAL:
            case Scenario::TYPE_CASHBOX:
                $total = $this->model->displayValue('real');
                break;
            case Scenario::TYPE_MULTIPLE:
                $total = $this->model->displayValue('amount');
                break;
            case Scenario::TYPE_WAREHOUSE:
                $total = $this->model->displayValue('net');
                break;
        }

        echo "<br>";
        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-6 us-detail'>";
        if ($this->show_observations) {
            echo "<table style='table-layout: fixed'>";
            echo "<tr><td><strong>Observaciones</strong></td></tr>";
            echo "<tr><td>" . USString::ifBlank($this->model->movement->observation, Strings::NONE) . "</td></tr>";
            echo "</table>";
        }
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-4'></div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-2 us-detail'>";
        echo "<table>";
        echo "<tr><td colspan='2'><h4>Montos ({$this->model->movement->getCurrencySymbol()})</h4></td></tr>";
        echo "<tr><td><strong>TOTAL:</strong></td><td class='pull-right'>{$total}</td></tr>";
        echo "</table>";
        echo "</div>";
        echo "</div>";
    }

}
