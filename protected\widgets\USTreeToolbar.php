<?php

class US<PERSON>reeToolbar extends CWidget {

    public $id;
    public $title;
    public $module;
    public $controller;
    public $tree_id;
    public $buttons;

    public function init() {
        $this->buttons = isset($this->buttons) ? $this->buttons : array(
            array('context' => 'primary', 'icon' => 'fa fa-lg fa-plus white', 'class' => 'form', 'label' => "Crear", 'title' => 'Crear', 'action' => 'create'),
        );
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->id = isset($this->id) ? $this->id : $this->owner->getServerId();

        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-3'>";
        echo "<b style='font-size:16px;'>{$this->title}</b>";
        echo "</div>";
        echo "<div class='col-lg-9 col-md-9 col-sm-9 col-xs-9'>";

        foreach ($this->buttons as $button) {

            $buttons = [];

            if (USArray::isAssoc($button)) {
                array_push($buttons, $this->normalizeButton($button));
            } else {
                foreach ($button as $subbutton) {
                    array_push($buttons, $this->normalizeButton($subbutton));
                }
            }

            echo $this->widget('application.widgets.USButtons', array('buttons' => $buttons), true);
        }

        echo "</div>";
        echo "</div>";
    }

    private function normalizeButton(array $button) {
        $button['route'] = "/{$this->module}/{$this->controller}/{$button['action']}";
        $button['data'] = isset($button['data']) ? $button['data'] : [];
        $button['data']['type'] = 'html';
        $button['data']['element_id'] = $this->tree_id;
        $button['disabled'] = isset($button['disabled']) ? $button['disabled'] : false;

        return $button;
    }

}
