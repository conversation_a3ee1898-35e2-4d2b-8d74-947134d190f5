<?php

class SIANPhone extends CWidget {

    public $id;
    public $model;
    public $types;
    public $advanced = 1;
    public $class_panel = '';
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        $phoneItems = [];

        foreach ($this->model->tempPhones as $item) {
            $attributes = [];
            array_push($attributes, "phone_number:'{$item->phone_number}'");
            array_push($attributes, "phone_type:'{$item->phone_type}'");
            array_push($attributes, "phone_number_error:'{$item->getError('phone_number')}'");
            array_push($attributes, "phone_type_error:'{$item->getError('phone_type')}'");
            array_push($phoneItems, '{' . implode(',', $attributes) . "}");
        }

        $phoneItems = '[' . implode(',', $phoneItems) . ']';


        Yii::app()->clientScript->registerScript($this->id, "

        window.phone_type_options = " . CJSON::encode($this->types) . ";
          
        var array = {$phoneItems};
           
        //COUNT
        $('#{$this->id}').data('count', 0);
            
        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANPhoneAddItem('{$this->id}', array[i]['phone_number'], array[i]['phone_type'], array[i]['phone_number_error'], array[i]['phone_type_error']);
            }
        }
        else
        {
            $('#{$this->id}').html('<p>" . Strings::NO_DATA . "</p>');
        }
        ");

        Yii::app()->clientScript->registerScript("SIANPhone", "
            
        function SIANPhoneAddItem(div_id, phone_number, phone_type, phone_number_error, phone_type_error)
        {
            //DIV
            var div = $('#' + div_id);
            var count = parseInt(div.data('count'));
            var advanced = div.data('advanced');

            //ID
            var id = getLocalId();
            
            var html = '<div id=\'' + id + '\' class=\'row\'>';
            
            html += '<div class=\'col-lg-4 col-md-4 col-sm-4 col-xs-12 sian-advanced\' ' + (advanced === '0' ? 'style=\'display: none;\'' : '') + '>';
            html += '<div class=\'form-group ' + (phone_type_error.length > 0 ? 'has-error' : '') + '\'>';
            html += '<select class=\'form-control\' name=\'Phone[' + id + '][phone_type]\'>' + getOptionsHtml(window.phone_type_options, phone_type, false) + '</select>';
            if (phone_type_error.length > 0)
                html += '<span class=\'help-block error\'>' + phone_type_error + '</span>';
            html += '</div>';
            html += '</div>';

            html += '<div class=\'' + (advanced === '0' ? 'col-lg-12 col-md-12 col-sm-12 col-xs-12' : 'col-lg-8 col-md-8 col-sm-8 col-xs-12') + ' sian-basic\' data-basic_span=\'col-lg-12 col-md-12 col-sm-12 col-xs-12\' data-advanced_span=\'col-lg-8 col-md-8 col-sm-8 col-xs-12\'>';
            html += '<div class=\'form-group ' + (phone_number_error.length > 0 ? 'has-error' : '') + '\'>';
            html += '<div class=\'input-group\'>';
            html += '<input class=\'form-control us-phone\' maxlength=\'15\' name=\'Phone[' + id + '][phone_number]\' type=\'tel\' placeholder=\'Teléfono\' value=\'' + phone_number + '\'>';
            html += '<span class=\'input-group-addon\'>';
            html += '<a onclick=\'SIANPhoneRemoveItem(\"' + div_id + '\",\"' + id + '\", false)\' title=\'Eliminar teléfono\'><span class=\'fa fa-times fa-lg black\'></span></a>';
            html += '</span>';
            html += '</div>';
            if (phone_number_error.length > 0)
                html += '<span class=\'help-block error\'>' + phone_number_error + '</span>';
            html += '</div>';
            html += '</div>';
            
            html += '</div>';
            
            //COUNT DE ITEMS
            if(count === 0)
            {
                div.html(html);
            }
            else
            {
                div.append(html);
            }
            div.data('count', count + 1);
           
            return html;
        }
        
        function SIANPhoneRemoveItem(div_id, id, confirmation)
        {
            if(confirmation ? confirm('¿Está seguro de eliminar este ítem?') : true)
            {
                var div = $('#' + div_id);
                var count = parseInt(div.data('count'));

                $('#' + id).remove();


                if(count === 1)
                {
                    div.html('<p>" . Strings::NO_DATA . "</p>');
                }

                //COUNT DE ITEMS
                div.data('count', count - 1);
            }
        }
            
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Teléfonos',
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
                'class' => $this->class_panel . ' ' . ( $this->model->hasErrors('tempPhones') ? 'us-error' : '')
            )
        ));

        echo "<div id='{$this->id}' data-advanced='{$this->advanced}'></div>";
        echo CHtml::link("<span class='fa fa-plus fa-lg black'></span> Agregar nuevo", null, array(
            'onclick' => "SIANPhoneAddItem('{$this->id}', '', 'Otro', '', '')",
            'title' => 'Agregar'
        ));
        $this->endWidget();
    }

}
