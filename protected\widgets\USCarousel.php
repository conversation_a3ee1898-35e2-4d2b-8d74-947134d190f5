<?php

/**
 * Documentación
 * http://miromannino.github.io/Justified-Gallery/getting-started/
 * http://www.jacklmoore.com/colorbox/
 */
class USCarousel extends CWidget {

    public $id;
    public $slide = true;
    public $pause = false;
    public $interval = 5000;
    public $displayPrevAndNext = true;
    public $items;
    //PRIVATE
    private $controller;

    public function init() {
        //ID
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $options = array(
            'interval' => $this->interval,
        );

        if ($this->pause) {
            $options['pause'] = 'hover';
        }

        $this->widget('booster.widgets.TbCarousel', array(
            'id' => $this->id,
            'slide' => $this->slide ? true : false,
            'options' => $options,
            'displayPrevAndNext' => $this->displayPrevAndNext,
            'items' => $this->items
        ));
    }

}
