<?php

class USLink extends CWidget {

    public $id;
    public $route = null;
    public $url = null;
    public $target = null;
    public $icon = null;
    public $subicons = [];
    public $color = 'black';
    public $label = null;
    public $title;
    public $class;
    public $data = null;
    public $jsonData = [];
    public $onclick = null;
    public $params = null;
    public $visible = null;
    public $confirm = null;
    public $nullDisplay = Strings::NONE;
    public $limit = false;
    //PRIVATE
    private $controller;
    private $text;

    /**
     * Initializes the widget.
     */
    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        $this->params = isset($this->params) ? $this->params : [];
        $this->data = isset($this->data) ? $this->data : [];

        //Implode value if is a array
        foreach ($this->params as $key => $value) {
            $this->params[$key] = is_array($value) ? implode("-", $value) : $value;
        }
        //Por cada data
        foreach ($this->data as $key => $value) {
            $this->data[$key] = is_array($value) ? implode("-", $value) : $value;
        }
        //Por cada jsonData
        foreach ($this->jsonData as $key => $value) {
            $this->data[$key] = CJSON::encode($value);
        }

        //LIMIT
        if ($this->limit && $this->limit <= strlen($this->label)) {
            $this->title = $this->label;
            $this->label = mb_substr($this->label, 0, $this->limit, 'UTF-8') . '...';
        }

        //URL
        $this->url = isset($this->route) ? $this->getUrl() : $this->url;
        if (is_null($this->visible)) {
            $this->visible = isset($this->route) ? $this->controller->checkRoute($this->route) : true;
        }

        $subicons = "";
        foreach ($this->subicons as $subicon) {
            $subicons .= "<i class='{$subicon}'></i>";
        }
        $this->text = trim((isset($this->icon) ? "<span class='{$this->icon} " . ($this->visible ? $this->color : 'light-grey') . "'>{$subicons}</span> " : "") . (isset($this->label) ? $this->label : ""));

        //Assets
        SIANAssets::registerScriptFile('js/us-link.js');
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if (strlen($this->text) > 0) {

            if (isset($this->class)) {

                //Add necessary information if not exist
                $this->data['modal_id'] = isset($this->data['modal_id']) ? $this->data['modal_id'] : $this->controller->getServerId();

                $htmlOptions = array(
                    'id' => $this->id,
                    'class' => $this->class,
                    'title' => $this->title,
                    'url' => $this->url,
                    'confirm' => $this->confirm,
                    'disabled' => !$this->visible,
                    'onclick' => $this->onclick,
                );

                foreach ($this->data as $key => $value) {
                    $htmlOptions["data-{$key}"] = $value;
                }

                echo CHtml::link($this->text, null, $htmlOptions);
            } else {
                echo CHtml::link($this->text, ($this->visible ? $this->url : null), array(
                    'id' => $this->id,
                    'class' => $this->class,
                    'title' => $this->title,
                    'target' => $this->target,
                    'confirm' => $this->confirm,
                    'disabled' => !$this->visible,
                    'onclick' => $this->onclick,
                ));
            }
        } else {
            echo $this->nullDisplay;
        }
    }

    /**
     * Retorna la URL
     * @return string URL
     */
    private function getUrl() {
        return $this->controller->createUrl($this->route, $this->params);
    }

}
