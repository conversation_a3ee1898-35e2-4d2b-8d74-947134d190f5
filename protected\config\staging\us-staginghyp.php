<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'staginghyp.siansystem.com/admin';
$domain = 'https://staginghyp.siansystem.com';
$report_domain = 'rstaging.siansystem.com';
$org = 'hyp';
$us = 'us_staging';
$database_server = '69.10.37.246';
$database_name = 'hyp_staging';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = true;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'hyp.p12';
$e_billing_certificate_pass = 'ybVc5TBGwHCz2ztF';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20482117041BILLING8',
        'password' => 'billingHYP19'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_STAGING;
$environment = YII_ENVIRONMENT_STAGING;
