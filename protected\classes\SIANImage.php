<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of ImagesConfig
 *
 * <AUTHOR>
 */
class SIANImage {

    public static function getLazyImg($url, $title = null, $htmlOptions = []) {

        if (isset($htmlOptions['class'])) {
            $htmlOptions['class'] .= ' lazy';
        } else {
            $htmlOptions['class'] = 'lazy';
        }

        $htmlOptions['data-original'] = $url;
        $htmlOptions['title'] = $title;


        return CHtml::image(Yii::app()->params ['admin_url'] . "/images/system/loading.gif", $title, $htmlOptions);
    }

    private static function getImg($url, $title = null, $htmlOptions = []) {

        $htmlOptions['title'] = $title;

        return CHtml::image($url, $title, $htmlOptions);
    }

    public static function getImage($url, $lazy = true, $title = null, $htmlOptions = []) {
        if ($lazy) {
            return self::getLazyImg($url, $title, $htmlOptions);
        } else {
            return self::getImg($url, $title, $htmlOptions);
        }
    }

    public static function getMediumImage($url = null, $cms = 'default', $lazy = true, $title = null, $htmlOptions = []) {
        $url = self::getMediumUrl($url, $cms);
        return self::getImage($url, $lazy, $title, $htmlOptions);
    }

    public static function getThumbImage($url = null, $cms = 'default', $lazy = true, $title = null, $htmlOptions = []) {
        $url = self::getThumbUrl($url, $cms);
        return self::getImage($url, $lazy, $title, $htmlOptions);
    }

    public static function getMediumUrl($url = null, $cms = 'default') {
        if (!USString::isBlank($url)) {
            $suffix = substr($url, strlen(Yii::app()->params['org_url'] . "/upload/{$cms}"));
            return Yii::app()->params['org_url'] . "/upload/{$cms}/.medium" . $suffix;
        } else {
            return self::getNoImageUrl();
        }
    }

    public static function getThumbUrl($url = null, $cms = 'default') {
        if (isset($url)) {
            $suffix = substr($url, strlen(Yii::app()->params['org_url'] . "/upload/{$cms}"));
            return Yii::app()->params['org_url'] . "/upload/{$cms}/.thumbs" . $suffix;
        } else {
            return self::getNoImageUrl('thumb');
        }
    }

    public static function getNoImageUrl($size = 'medium') {
        return Yii::app()->params ['admin_url'] . "/images/system/no-image-{$size}.jpg";
    }

    public static function saveImages($imageName, $imagePath, $cms = 'default', $extension = null) {
        $imageName = str_replace(" ", "", $imageName);

        if (isset($extension))
            $imageName .= ".$extension";
        else {
            $path_parts = pathinfo($imagePath);
            $imageName .= $path_parts ['extension'];
        }

        $thumb = "/upload/{$cms}/.thumbs/images/{$imageName}";
        self::saveImage($imagePath, Yii::app()->params ['org_dir'] . $thumb, Yii::app()->params['media_config'][$cms]['thumb_width'], Yii::app()->params['media_config'][$cms]['thumb_height']);

        $medium = "/upload/{$cms}/.medium/images/$imageName";
        self::saveImage($imagePath, Yii::app()->params ['org_dir'] . $medium, Yii::app()->params['media_config'][$cms]['medium_width'], Yii::app()->params['media_config'][$cms]['medium_height']);

        $normal = "/upload/{$cms}/images/{$imageName}";
        self::saveImage($imagePath, Yii::app()->params ['org_dir'] . $normal);
        return Yii::app()->params ['org_url'] . $normal;
    }

    public static function saveImage($p_s_filepath, $p_s_savepath, $p_i_width = null, $p_i_height = null, $p_b_optimize = true) {
        $a_type = getimagesize($p_s_filepath);
        $s_type = $a_type['mime'];

        $b_result = false;
        switch ($s_type) {
            case "image/png" :
                $o_image = imagecreatefrompng($p_s_filepath);
                $o_image = self::reduceImage($o_image, $p_i_width, $p_i_height);
                $b_result = imagepng($o_image, $p_s_savepath, 0);
                break;
            case "image/jpeg" :
                $o_image = imagecreatefromjpeg($p_s_filepath);
                $o_image = self::reduceImage($o_image, $p_i_width, $p_i_height);
                $b_result = imagejpeg($o_image, $p_s_savepath, 100);
                break;
            case "image/gif" :
                $o_image = imagecreatefromgif($p_s_filepath);
                $o_image = self::reduceImage($o_image, $p_i_width, $p_i_height);
                $b_result = imagegif($o_image, $p_s_savepath);
                break;
            default :
                break;
        }
        //Vemos si se optimizará
        if ($b_result && $p_b_optimize) {
            USImage::optimize($p_s_savepath);
        }
        //
        return $b_result;
    }

    public static function reduceImage($p_o_image, $p_i_width = null, $p_i_height = null) {

        $p_i_width = isset($p_i_width) ? $p_i_width : imagesx($p_o_image);
        $p_i_height = isset($p_i_height) ? $p_i_height : imagesy($p_o_image);

        if (imagesx($p_o_image) > imagesy($p_o_image)) {
            $p_i_height = (imagesy($p_o_image) * $p_i_width) / imagesx($p_o_image);
        } else {
            $p_i_width = (imagesx($p_o_image) * $p_i_height) / imagesy($p_o_image);
        }
//        if (isset($width))
//            $height = (imagesy($image) * $width) / imagesx($image);
//        else {
//            $width = imagesx($image);
//            $height = imagesy($image);
//        }
        $o_new_image = imagecreatetruecolor($p_i_width, $p_i_height); // new wigth and height
        imagealphablending($o_new_image, false);
        imagesavealpha($o_new_image, true);
        imagecopyresampled($o_new_image, $p_o_image, 0, 0, 0, 0, $p_i_width, $p_i_height, imagesx($p_o_image), imagesy($p_o_image));
        $p_o_image = $o_new_image;

        // saving
        imagealphablending($p_o_image, false);
        imagesavealpha($p_o_image, true);

        return $p_o_image;
    }

    public static function getAlertImage($type = 'success') {
        return CHtml::image(Yii::app()->params['admin_url'] . "/images/alert/" . $type . ".png");
    }

    /**
     * Obtiene el directorio absoluto de la imagen
     * @param string $p_s_url URL de la imagen
     * @return mixed Directorio de la imagen o FALSE si la URL estaba en blanco
     */
    public static function getPathFromUrl($p_s_url, $p_s_cms = 'default') {
        if (!USString::isBlank($p_s_url)) {
            $s_suffix = substr($p_s_url, strlen(Yii::app()->params['org_url'] . "/upload/{$p_s_cms}"));
            $s_suffix = str_replace("/", DIRECTORY_SEPARATOR, $s_suffix);
            $s_suffix = USString::removeRepeated($s_suffix, DIRECTORY_SEPARATOR);
            return urldecode(Yii::app()->params['org_dir'] . DIRECTORY_SEPARATOR . 'upload' . DIRECTORY_SEPARATOR . $p_s_cms . DIRECTORY_SEPARATOR . $s_suffix);
        } else {
            return false;
        }
    }

    /**
     * Obtiene la URL absoluta de la imagen
     * @param string $p_s_filepath Directorio de la imagen
     * @return mixed ULR de la imagen o FALSE si el directorio estaba en blanco
     */
    public static function getUrlFromPath($p_s_filepath) {
        if (!USString::isBlank($p_s_filepath)) {
            $s_suffix = substr($p_s_filepath, strlen(Yii::app()->params['org_dir'] . DIRECTORY_SEPARATOR . 'upload'));
            $s_suffix = str_replace(DIRECTORY_SEPARATOR, "/", $s_suffix);
            $s_suffix = USString::removeRepeated($s_suffix, '/');
            return urlencode(Yii::app()->params['org_url'] . '/upload/' . $s_suffix);
        } else {
            return false;
        }
    }

    public static function getSignLogos() {

        $s_path = Yii::app()->params['org_dir'] . "/images/sign";
        $s_preffix = Yii::app()->params['org_url'] . "/images/sign";
        $a_logos = scandir($s_path);

        $a_files = [];
        //Agregamos el logo de la empresa
        $a_files[Yii::app()->params['org_url'] . '/images/logos/logo.png'] = 'logo.png';

        foreach ($a_logos as $s_logo) {
            //Si la carpeta no es un retroceso
            if ($s_logo !== '.' && $s_logo !== '..') {
                $s_filepath = $s_path . DIRECTORY_SEPARATOR . $s_logo;
                //Si NO es un directorio
                if (!is_dir($s_filepath)) {
                    //Obtenemos extensión
                    $s_extension = pathinfo($s_filepath, PATHINFO_EXTENSION);
                    //Solo agregamos las extensiones permitidas
                    if (in_array($s_extension, ['png', 'jpg', 'jpeg', 'bmp', 'gif'])) {
                        $a_files[$s_preffix . DIRECTORY_SEPARATOR . $s_logo] = $s_logo;
                    }
                }
            }
        }

        return $a_files;
    }

}
