<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of USGridView
 *
 * <AUTHOR>
 */
Yii::import('booster.widgets.TbGridView');

class USGridView extends TbGridView {

    public $responsiveTable = true;
    public $form;

    public function init() {
        parent::init();
        //
        SIANAssets::registerCssFile('css/us-grid-view.css');
        SIANAssets::registerScriptFile('js/us-grid-view.js');
    }

    protected function initColumns() {

        if ($this->dataProvider instanceof USSQLDataProvider || $this->dataProvider instanceof USArrayDataProvider) {
            foreach ($this->columns as $i => $column) {
                if (is_array($column) && isset($column['name']) && isset($this->dataProvider->model) && !isset($column['header'])) {
                    $this->columns[$i]['header'] = $this->dataProvider->model->getAttributeLabel($column['name']);
                }
                if (isset($this->filter) && isset($column['name'])) {

                    if (!isset($column['filter'])) {
                        $this->columns[$i]['filter'] = $this->form->textField($this->filter, $column['name'], []);
                    } else {
                        if (is_bool($column['filter']) && $column['filter']) {
                            $this->columns[$i]['filter'] = $this->form->dropDownList($this->filter, $column['name'], Util::getBooleanListData(), array(
                                'empty' => Strings::ANY,
                            ));
                        }
                        if (is_string($column['filter']) && $column['filter'] == 'dateRange') {
                            $this->columns[$i]['filter'] = $this->widget('application.widgets.USDateRangePicker', array(
                                'form' => $this->form,
                                'model' => $this->filter,
                                'attribute' => $column['name'],
                                'options' => array(
                                    'maxDate' => SIANTime::formatDate(),
                                    'format' => 'DD/MM/YYYY',
                                ),
                                'label' => false,
                                'icon' => false,
                                    ), true);
                        }
                    }
                }
            }
        }
        if (isset($this->filter)) {
            $this->afterAjaxUpdate = "function() {
                            jQuery('.us-datepicker').each(function() {
                                var input = jQuery(this);
                                if (input.data('dateRangePicker')) {
                                    input.data('dateRangePicker').destroy();
                                    input.removeData('dateRangePicker');
                                }
                                input.dateRangePicker({
                                    separator: ' a ',
                                    format: 'DD/MM/YYYY'
                                }).unbind('datepicker-close').bind('datepicker-apply', function() {   
                                    input.trigger({
                                        type: 'keydown',
                                        keyCode: 13,
                                        which: 13
                                    });
                                });
                            });
                        setTimeout(resizeFooter, 250);
                    }";
        }

        return parent::initColumns();
    }

    /**
     * Se sobreescribe función para tabla responsiva
     */
    protected function writeResponsiveCss() {
        $cnt = 1;
        $labels = '';
        foreach ($this->columns as $column) {
            /** @var TbDataColumn $column */
            ob_start();
            $column->renderHeaderCell();
            $raw = ob_get_clean();
            //Vemos si hay alguna columna múltiple
            $o_dom = new DOMDocument();
            $o_dom->loadHTML('<?xml encoding="utf-8" ?>' . $raw);
            foreach ($o_dom->getElementsByTagName('th') as $node) {
                $name = $node->textContent;
                $labels .= "#$this->id td:nth-of-type($cnt):before { content: '{$name}'; }\n";
                $cnt++;
            }

//                $name = strip_tags($raw);
//
//                $labels .= "#$this->id td:nth-of-type($cnt):before { content: '{$name}'; }\n";
//                $cnt++;
        }
//(min-device-width: 768px) and (max-device-width: 1024px) 
        $css = <<<EOD
@media
	only screen and (max-width: 700px) {

		/* Force table to not be like tables anymore */
		#{$this->id} table,#{$this->id} thead,#{$this->id} tbody,#{$this->id} th,#{$this->id} td,#{$this->id} tr {
			display: block;
		}

		/* Hide table headers (but not display: none;, for accessibility) */
		#{$this->id} thead tr {
			position: absolute;
			top: -9999px;
			left: -9999px;
		}

		#{$this->id} tr { border: 1px solid #ccc; }

		#{$this->id} td {
			/* Behave  like a "row" */
			border: none;
			border-bottom: 1px solid #eee;
			position: relative;
			padding-left: 50%;
                        text-align: center;
		}

		#{$this->id} td:before {
			/* Now like a table header */
			position: absolute;
			/* Top/left values mimic padding */
			top: 6px;
			left: 6px;
			width: 45%;
                        text-align: left;
                        font-weight: bold;
			padding-right: 10px;
			white-space: nowrap;
		}
		.grid-view .button-column {
			text-align: left;
			width:auto;
		}
		/*
		Label the data
		*/
		{$labels}
	}
EOD;
        Yii::app()->clientScript->registerCss(__CLASS__ . '#' . $this->id, $css);
    }

}
