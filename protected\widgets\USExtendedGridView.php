<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of USGridView
 *
 * <AUTHOR>
 */
Yii::import('booster.widgets.TbExtendedGridView');

class USExtendedGridView extends TbExtendedGridView {

    public $responsiveTable = true;
    public $showHeader = true;

    public function init() {
        parent::init();
    }

    protected function initColumns() {

        if ($this->dataProvider instanceof USSQLDataProvider || $this->dataProvider instanceof USArrayDataProvider) {
            foreach ($this->columns as $i => $column) {
                if (is_array($column) && isset($column['name']) && isset($this->dataProvider->model) && !isset($column['header'])) {
                    $this->columns[$i]['header'] = $this->dataProvider->model->getAttributeLabel($column['name']);
                }
            }
        }

        return parent::initColumns();
    }

    /**
     * Se sobreescribe función para tabla responsiva
     */
    protected function writeResponsiveCss() {
        $cnt = 1;
        $labels = '';
        foreach ($this->columns as $column) {
            /** @var TbDataColumn $column */
            ob_start();
            $column->renderHeaderCell();
            $raw = ob_get_clean();
            //Vemos si hay alguna columna múltiple
            $o_dom = new DOMDocument();
            $o_dom->loadHTML('<?xml encoding="utf-8" ?>' . $raw);
            foreach ($o_dom->getElementsByTagName('th') as $node) {
                $name = $node->textContent;
                $labels .= "#$this->id td:nth-of-type($cnt):before { content: '{$name}'; }\n";
                $cnt++;
            }
//                $name = strip_tags($raw);
//
//                $labels .= "#$this->id td:nth-of-type($cnt):before { content: '{$name}'; }\n";
//                $cnt++;
        }
        $header = "";
        if (!$this->showHeader) {
            $header .= "#$this->id table thead { display: none; }\n";
            $header .= "#$this->id table tbody tr:nth-child(1) td { border: 0px; }\n";
        }
        $header .= "#$this->id .grid-view { padding-top: 0px; }\n";

        $css = <<<EOD
                {$header}
@media
	only screen and (max-width: 760px),
	(min-device-width: 768px) and (max-device-width: 1024px)  {

		/* Force table to not be like tables anymore */
		#{$this->id} table,#{$this->id} thead,#{$this->id} tbody,#{$this->id} th,#{$this->id} td,#{$this->id} tr {
			display: block;
		}

		/* Hide table headers (but not display: none;, for accessibility) */
		#{$this->id} thead tr {
			position: absolute;
			top: -9999px;
			left: -9999px;
		}

		#{$this->id} tr { border: 1px solid #ccc; }

		#{$this->id} td {
			/* Behave  like a "row" */
			border: none;
			border-bottom: 1px solid #eee;
			position: relative;
			padding-left: 50%;
                        text-align: center;
		}

		#{$this->id} td:before {
			/* Now like a table header */
			position: absolute;
			/* Top/left values mimic padding */
			top: 6px;
			left: 6px;
			width: 45%;
                        text-align: left;
                        font-weight: bold;
			padding-right: 10px;
			white-space: nowrap;
		}
		.grid-view .button-column {
			text-align: left;
			width:auto;
		}
		/*
		Label the data
		*/
		{$labels}
	}
EOD;
        Yii::app()->clientScript->registerCss(__CLASS__ . '#' . $this->id, $css);
    }

}
