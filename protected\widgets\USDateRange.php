<?php

class <PERSON>ateRange extends CWidget {

    public $form = null;
    public $begin_id = null;
    public $end_id = null;
    public $begin_name = null;
    public $end_name = null;
    public $begin_label = null;
    public $end_label = null;
    public $begin_value = null;
    public $end_value = null;
    public $now = false;
    public $model = null;
    public $begin_attribute = null;
    public $end_attribute = null;
    public $beginOptions = [];
    public $endOptions = [];
    public $beginHtmlOptions = [];
    public $endHtmlOptions = [];
    public $grid = false;
    public $readonly = false;
    //PRIVATE
    private $controller;

    /**
     * Initializes the widget.
     */
    public function init() {

        $this->controller = Yii::app()->controller;
        $this->begin_id = isset($this->begin_id) ? $this->begin_id : $this->controller->getServerId();
        $this->end_id = isset($this->end_id) ? $this->end_id : $this->controller->getServerId();

        //BEGIN
        $this->beginOptions['format'] = isset($this->beginOptions['format']) ? $this->beginOptions['format'] : Yii::app()->params['date_php_format'];
        $this->beginOptions['formatDate'] = isset($this->beginOptions['formatDate']) ? $this->beginOptions['formatDate'] : Yii::app()->params['date_php_format'];

        $this->beginOptions['timepicker'] = isset($this->beginOptions['timepicker']) ? $this->beginOptions['timepicker'] : false;
        $this->beginOptions['onShow'] = "function(ct){
            this.setOptions({
                maxDate: $('#{$this->end_id}').val() ? $('#{$this->end_id}').val() : false,
                //maxTime: $('#{$this->end_id}').val() ? $('#{$this->end_id}').val() : false
            })
        }";
        //END
        $this->endOptions['format'] = isset($this->endOptions['format']) ? $this->endOptions['format'] : Yii::app()->params['date_php_format'];
        $this->endOptions['formatDate'] = isset($this->endOptions['formatDate']) ? $this->endOptions['formatDate'] : Yii::app()->params['date_php_format'];

        $this->endOptions['timepicker'] = isset($this->endOptions['timepicker']) ? $this->endOptions['timepicker'] : false;
        $this->endOptions['onShow'] = "function(ct){
            this.setOptions({
                minDate: $('#{$this->begin_id}').val() ? $('#{$this->begin_id}').val() : false,
                //minTime: $('#{$this->begin_id}').val() ? $('#{$this->begin_id}').val() : false
            })
        }";
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->grid) {
            echo "<div class='row'>";
            echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-6'>";
        }

        $this->widget('application.widgets.USDateTimePicker', array(
            'id' => $this->begin_id,
            'form' => $this->form,
            'model' => $this->model,
            'attribute' => $this->begin_attribute,
            'name' => $this->begin_name,
            'label' => $this->begin_label,
            'value' => $this->begin_value,
            'options' => $this->beginOptions,
            'htmlOptions' => $this->beginHtmlOptions,
        ));

        if ($this->grid) {
            echo "</div>";
            echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-6'>";
        }

        $this->widget('application.widgets.USDateTimePicker', array(
            'id' => $this->end_id,
            'form' => $this->form,
            'model' => $this->model,
            'attribute' => $this->end_attribute,
            'name' => $this->end_name,
            'label' => $this->end_label,
            'value' => $this->end_value,
            'options' => $this->endOptions,
            'htmlOptions' => $this->endHtmlOptions,
        ));

        if ($this->grid) {
            echo "</div>";
            echo "</div>";
        }
    }

}
