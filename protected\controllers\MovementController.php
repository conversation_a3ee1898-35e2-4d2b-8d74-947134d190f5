<?php

class MovementController extends SIANController {

    use EscPosControllerTrait;
    use MovementControllerTrait;
    use EbillingControllerTrait;

    public $plural = "Movimientos";
    public $singular = "movimiento";
    public $modelClass = 'Movement';
    public $viewClass = '';
    public $viewScenario = 'search';
    public $viewButtons = true;

    const EXPORT_HTML = 'html';
    const EXPORT_WORD = 'docx';
    const EXPORT_PDF = 'pdf';
    const EXPORT_EXCEL = 'xls';
    const PRINT_MODE_NORMAL = 0;
    const PRINT_MODE_GROUPED = 1;

    public function accessRules() {
        return array(
            array(
                'allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('loadOperations', 'loadOperationData', 'loadDocuments', 'loadSeries', 'loadCorrelative', 'loadExchange', 'loadCommercialStocks', 'loadCostsAndStocks', 'printed', 'getWarehouses', 'getCashboxes', 'getBalance', 'getPresentations', 'getCommercialItems', 'getReferences'),
                'users' => array('@'),
            ),
            array(
                'deny', // deny all users
                'users' => array('*'),
            ),
        );
    }

    public function actionUpgrade($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null) {

        $this->title = "Elija qué quiere crear";

        $routes = isset($_GET['routes']) ? $_GET['routes'] : null;
        $access = isset($_GET['access']) ? $_GET['access'] : null;

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array(
                'parent_id' => $parent_id,
                'modal_id' => $modal_id
            )
                ), true, true);
        $content_html = $this->renderPartial("application.views.widget._upgrade", array(
            'data' => array(
                'modal_id' => $this->getServerId(),
                'type' => $type,
                'element_id' => $element_id,
                'parent_id' => $parent_id,
                'routes' => $routes,
                'access' => $access,
                'id' => $id,
            )
                ), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html
        ));
    }

    public function actionLoadExchange() {
        try {
            $exchange_column = $_POST['exchange_column'];
            $emission_date = $_POST['emission_date'];

            $m_date = SIANTime::formatDate($emission_date, true, true);

            if ($m_date) {
                echo CJSON::encode([
                    'code' => USREST::CODE_SUCCESS,
                    'message' => 'Success',
                    'data' => [
                        'exchange_rate' => ExchangeRate::getExchangeRate($exchange_column, $m_date),
                    ]
                ]);
            } else {
                echo CJSON::encode([
                    'code' => USREST::CODE_BAD_REQUEST,
                    'message' => "Fecha inválida: {$emission_date}",
                ]);
            }
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function actionGetReferences($id) {

        $o_model = Movement::model()->findByPk($id);

        //Referencias
        $referencesDataProvider = (new SpGetMovementReferences())->setParams([
                    'movement_id' => $id,
                ])->getDataProvider(['movement_id']);

        $this->render("application.views.widget._references", array(
            'data' => array(
                'referencesDataProvider' => $referencesDataProvider,
                'reference_routes' => $this->getReferences($o_model->route),
            )
        ));
    }

    public function actionGetDocuments() {

        $result = array(
            'code' => USREST::CODE_SUCCESS,
        );

        try {
            $route = $_POST['route'];

            $data = Yii::app()->db->createCommand()
                    ->select('D.document_code as id, D.document_name as text')
                    ->from('document D')
                    ->join('document_scenario DS', 'DS.document_code = D.document_code')
                    ->where("D.`status` and DS.route = :route", array(':route' => $route))
                    ->order('D.document_name')
                    ->queryAll();

            $result['data'] = $data;
        } catch (CDbException $ex) {
            $result['code'] = USREST::CODE_INTERNAL_SERVER_ERROR;
            $result['msg'] = $ex->errorInfo[2];
        } catch (Exception $ex) {
            $result['code'] = USREST::CODE_INTERNAL_SERVER_ERROR;
            $result['msg'] = $ex->getMessage();
        }

        echo CJSON::encode($result);
    }

    public function actionGetOperations() {

        $result = array(
            'code' => USREST::CODE_SUCCESS,
        );

        try {
            $route = $_POST['route'];

            $data = Yii::app()->db->createCommand()
                    ->select('O.operation_code as id, O.operation_name as text')
                    ->from('operation O')
                    ->join('operation_scenario OS', 'OS.operation_code = O.operation_code')
                    ->where("O.`status` and OS.route = :route", array(':route' => $route))
                    ->queryAll();

            $result['data'] = $data;
        } catch (CDbException $ex) {
            $result['code'] = USREST::CODE_INTERNAL_SERVER_ERROR;
            $result['msg'] = $ex->errorInfo[2];
        } catch (Exception $ex) {
            $result['code'] = USREST::CODE_INTERNAL_SERVER_ERROR;
            $result['msg'] = $ex->getMessage();
        }

        echo CJSON::encode($result);
    }

    public function actionGetStores() {

        $result = array(
            'code' => USREST::CODE_SUCCESS,
        );

        try {
            $data = Yii::app()->db->createCommand()
                    ->select('S.store_id as id, S.store_name as text')
                    ->from('store S')
                    ->where('S.`status`')
                    ->queryAll();

            $result['data'] = $data;
        } catch (CDbException $ex) {
            $result['code'] = USREST::CODE_INTERNAL_SERVER_ERROR;
            $result['msg'] = $ex->errorInfo[2];
        } catch (Exception $ex) {
            $result['code'] = USREST::CODE_INTERNAL_SERVER_ERROR;
            $result['msg'] = $ex->getMessage();
        }

        echo CJSON::encode($result);
    }

    public function actionLoadSeries() {

        try {
            $i_store = $_POST['store'];
            $s_document_code = $_POST['document_code'];
            $s_document_serie = isset($_POST['document_serie']) && strlen($_POST['document_serie']) > 0 ? $_POST['document_serie'] : null;
            $i_document_correlative = isset($_POST['document_correlative']) && strlen($_POST['document_correlative']) > 0 ? $_POST['document_correlative'] : null;

            echo CJSON::encode($this->_loadSeries($i_store, $s_document_code, $s_document_serie, $i_document_correlative));
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    private function _loadSeries($p_i_store_id, $p_s_document_code, $p_s_document_serie = null, $p_s_document_correlative = null) {

        $o_documentObj = Document::model()->with([
                    'documentSeries' => [
                        'alias' => 'DS',
                        'select' => [
                            'DS.document_serie',
                            'DS.document_serie_name',
                            'DS.min_correlative',
                            'DS.document_correlative',
                            'DS.max_correlative'
                        ],
                        'on' => "DS.`status` and DS.store_id = :store_id",
                        'params' => [
                            ':store_id' => $p_i_store_id
                        ]
                    ]
                ])->findByPk([
            'document_code' => $p_s_document_code
                ], [
            'alias' => 'D',
            'select' => [
                'D.serialized',
                'D.sunat_code'
            ]
        ]);

        if (is_null($o_documentObj)) {
            return array(
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => 'No hay documentos disponibles, comuníquese con sistemas.'
            );
        }

        $a_serieItems = [];

        //Si el documento es serializado
        if ($o_documentObj->serialized) {

            //Verificamos si hay series
            if (count($o_documentObj->documentSeries) > 0) {
                //Si existe movimiento
                if (isset($p_s_document_serie, $p_s_document_correlative)) {
                    //Obtenemos el objeto serie, que seria el correspondiente a l serie por defecto (si existe) o la primera del arreglo
                    $o_documentSerieObj = $this->getSerie($o_documentObj->documentSeries, $p_s_document_serie);
                    //Verificamos si NO es la misma 
                    if ($o_documentSerieObj->document_serie != $p_s_document_serie) {
                        $p_s_document_serie = $o_documentSerieObj->document_serie;
                        $p_s_document_correlative = $o_documentSerieObj->document_correlative;
                    }
                    $i_min_correlative = $o_documentSerieObj->min_correlative;
                    $i_max_correlative = $o_documentSerieObj->max_correlative;
                } else {
                    //Obtenemos la primera serie
                    $o_documentSerieObj = $this->getSerie($o_documentObj->documentSeries);
                    $p_s_document_serie = $o_documentSerieObj->document_serie;
                    $p_s_document_correlative = $o_documentSerieObj->document_correlative;
                    $i_min_correlative = $o_documentSerieObj->min_correlative;
                    $i_max_correlative = $o_documentSerieObj->max_correlative;
                }

                foreach ($o_documentObj->documentSeries as $modelSerie) {
                    $a_serieItems[] = array(
                        'document_serie' => $modelSerie->document_serie,
                        'document_serie_name' => "{$modelSerie->document_serie} - {$modelSerie->document_serie_name}",
                    );
                }
                $a_range = Movement::getDateRange($p_s_document_code, $o_documentSerieObj->document_serie, $p_s_document_correlative);
                if ($a_range['code'] === USREST::CODE_SUCCESS) {
                    $s_minDate = $a_range['data']['minDate'];
                    $s_maxDate = $a_range['data']['maxDate'];
                } else {
                    return array(
                        'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                        'message' => $a_range['message']
                    );
                }
            } else {
                return array(
                    'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                    'message' => 'No se han definido series para este documento, por favor comuníquese con contabilidad.'
                );
            }
        } else {
            $i_min_correlative = 1;
            $i_max_correlative = 9999999999;
            $s_minDate = SIANTime::formatDate('1970-01-01');
            $s_maxDate = SIANTime::formatDate();
        }

        return array(
            'code' => USREST::CODE_SUCCESS,
            'message' => 'Success',
            'data' => array(
                'sunat_code' => $o_documentObj->sunat_code,
                'serialized' => $o_documentObj->serialized,
                'serieItems' => $a_serieItems,
                'document_serie' => $p_s_document_serie,
                'min_correlative' => $i_min_correlative,
                'document_correlative' => $p_s_document_correlative,
                'max_correlative' => $i_max_correlative,
                'minDate' => $s_minDate,
                'maxDate' => $s_maxDate,
            )
        );
    }

    public function actionLoadOperations() {
        $s_route = $_POST['route'];
        //$i_require_project = $_POST['require_project'];
        $s_type = $_POST['type'];
        $s_parent_is_internal = isset($_POST['parent_is_internal']) ? $_POST['parent_is_internal'] : null;
        $i_target_movement_id = isset($_POST['target_movement_id']) ? $_POST['target_movement_id'] : null;
        $s_scope = isset($_POST['scope']) ? $_POST['scope'] : 'NONE';

        try {
            $a_params = [
                'route' => $s_route,
            ];
            if (isset($s_parent_is_internal)) {
                $a_params['internal'] = $s_parent_is_internal;
            }
            $o_model = (new DpScenarioOperation())
                    ->setAttributes($a_params)
                    ->addFindInSetCondition('scopes', $s_scope);

            //Filtros especiales
            if ($s_type === Scenario::TYPE_DYNAMIC) {
                $i_min = $this->checkRoute("{$s_route}/has_no_dynamic") ? 0 : 1;
                $o_model->addMathCondition('entry_count', $i_min, '>=');
            }

            $a_operations = $o_model->findAll();

            $b_has_reversal = false;
            //Verificamos si hay operaciones que son reversiones
            foreach ($a_operations as $o_operation) {
                if ($o_operation->is_reversal == 1) {
                    $b_has_reversal = true;
                    break;
                }
            }

            //Si hay reversiones llamamos a la operacion del movimiento objetivo
            $a_target_movement = null;
            if ($b_has_reversal && isset($i_target_movement_id)) {
                $a_target_movement = Yii::app()->db->createCommand()->select([
                            "M.type",
                            "AM.entry_count",
                        ])
                        ->from('movement M')
                        ->join('accounting_movement AM', 'AM.movement_id = M.movement_id')
                        ->where("M.movement_id = :movement_id", [
                            ':movement_id' => $i_target_movement_id
                        ])
                        ->queryRow();
            }

            //Recorremos y agregamos
            $a_normalized_items = [];
            foreach ($a_operations as $o_operation) {
                //Si es una reversión sólo puede ser del mismo tipo
                if ($o_operation->is_reversal == 1) {
                    //Si la operación padre es del mismo tipo y tiene dinámica
                    if (isset($a_target_movement) && $s_type === $a_target_movement['type'] && $a_target_movement['entry_count'] > 0) {
                        $a_normalized = $o_operation->attributes;
                        $a_normalized_items[] = $a_normalized;
                    }
                } else {
                    $a_normalized_items[] = $o_operation->attributes;
                }
            }

            echo CJSON::encode([
                'code' => USREST::CODE_SUCCESS,
                'operations' => $a_normalized_items,
            ]);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function actionLoadOperationData() {
        $i_is_reversal = $_POST['is_reversal'];
        $s_operation_code = $_POST['operation_code'];
        $i_target_movement_id = isset($_POST['target_movement_id']) ? $_POST['target_movement_id'] : null;
        $i_entry_id1 = isset($_POST['entry_id1']) ? $_POST['entry_id1'] : null;
        $i_entry_id2 = isset($_POST['entry_id2']) ? $_POST['entry_id2'] : null;

        //VER SI ACA SE OBTIENEN LAS CUENTAS, O ANTES SE OBTIENEN
        try {
            //Si es reversión y hay código de operación
            if ($i_is_reversal == 1 && isset($i_target_movement_id)) {
                $s_mode = SpGetDynamic::MODE_REVERSE;
                $m_param_id = $i_target_movement_id;
            } else {
                $s_mode = SpGetDynamic::MODE_NORMAL;
                $m_param_id = $s_operation_code;
            }

            $a_oDynamics = (new SpGetDynamic())->setParams(array(
                        'mode' => $s_mode,
                        'param_id' => $m_param_id,
                        'cashbox_id' => NULL,
                        'entry_id1' => $i_entry_id1,
                        'entry_id2' => $i_entry_id2,
                    ))->findAll();

            $b_has_cashbox = false;
            $a_dynamics = [];
            $a_account_independent_wildcards = Account::getWildCardIndependent();
            $a_account_inherit_wildcards = Account::getWildCardInherit();
            //$a_combinations = [];
            $a_all_combination_ids = [];
            $a_combination_fields = [];
            $a_independent_wildcards = [];
            $a_inherit_wildcards = [];
            $a_wildcard_association = [];
            foreach ($a_oDynamics as $o_dynamic) {

                $a_combination_ids = [];

                //Buscamos si tiene Cashbox
                if ($o_dynamic->account_code === Account::WILDCARD_CASHBOX) {
                    $b_has_cashbox = true;
                }
                //Comodines para tipo de item
                if (in_array($o_dynamic->account_code, $a_account_independent_wildcards)) {
                    $a_independent_wildcards[] = $o_dynamic->account_code;
                    $a_wildcard_association[$o_dynamic->account_code] = $o_dynamic->field;
                }
                if (in_array($o_dynamic->account_code, $a_account_inherit_wildcards)) {
                    $a_inherit_wildcards[] = $o_dynamic->account_code;
                    $a_wildcard_association[$o_dynamic->account_code] = $o_dynamic->field;
                }


                //Si es de tipo elegible
                if ($o_dynamic->destiny_type === Account::DESTINY_TYPE_SELECTABLE) {
                    //Fields que tienen CC
                    if ($o_dynamic->has_combination == 1) {
                        $a_combination_fields[] = $o_dynamic->field;
                    }
                    //Buscamos si tiene centro de costo en los destinos
                    foreach ($o_dynamic->destinies as $o_destiny) {
                        if ($o_destiny->owner === Combination::OWNER) {
                            //Combinaciones por operación
                            $a_all_combination_ids[] = $o_destiny->owner_id;
                            //Combinaciones por dinámica
                            $a_combination_ids[] = $o_destiny->owner_id;
                        }
                    }
                }

                $a_dynamics[] = [
                    'entry_number' => $o_dynamic->entry_number,
                    'column' => $o_dynamic->column,
                    'account_code' => $o_dynamic->account_code,
                    'field' => $o_dynamic->field,
                    'owner' => $o_dynamic->owner,
                    'owner_id' => $o_dynamic->owner_id,
                    'owner_name' => $o_dynamic->owner_name,
                    'destiny_type' => $o_dynamic->destiny_type,
                    'has_combination' => $o_dynamic->has_combination,
                    'combination_ids' => $a_combination_ids,
                ];
            }
            // USArray::usortString($a_combinations, 'combination_name');
            $a_data = [
                'has_cashbox' => $b_has_cashbox,
                'has_combination' => count($a_all_combination_ids) > 0,
                'combination_ids' => $a_all_combination_ids,
                'combination_fields' => USArray::array_unique($a_combination_fields),
                'independent_wildcards' => $a_independent_wildcards,
                'inherit_wildcards' => $a_inherit_wildcards,
                'wildcard_association' => $a_wildcard_association,
                'dynamics' => $a_dynamics,
            ];
            echo CJSON::encode([
                'code' => USREST::CODE_SUCCESS,
                'data' => $a_data,
            ]);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function actionLoadDocuments() {
        $s_route = $_POST['route'];
        $s_person_type = $_POST['person_type'];
        $s_scope = isset($_POST['scope']) ? $_POST['scope'] : 'NONE';
        $s_parent_is_internal = isset($_POST['parent_is_internal']) ? $_POST['parent_is_internal'] : null;
        $s_from_exchange = isset($_POST['from_exchange']) ? $_POST['from_exchange'] : null;
        $s_sunat_code = isset($_POST['sunat_code']) ? $_POST['sunat_code'] : null;

        try {
            $a_params = [
                'route' => $s_route,
                'person_type' => $s_person_type,
                'not_serialized' => $this->checkRoute("{$s_route}/not_serialized"),
            ];

            if (isset($s_sunat_code)) {
                $a_params['sunat_code'] = $s_sunat_code;
            }

            if (isset($s_parent_is_internal) || isset($s_from_exchange)) {

                if ($s_from_exchange == 1) {
                    $a_params['is_internal'] = 0;
                } else {
                    $a_params['is_internal'] = $s_parent_is_internal;
                }
            }

            $a_documents = (new DpScenarioDocument())
                    ->setAttributes($a_params)->addFindInSetCondition('scopes', $s_scope)
                    ->findAll();

            echo CJSON::encode([
                'code' => USREST::CODE_SUCCESS,
                'documents' => $a_documents,
            ]);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function actionLoadCorrelative() {

        $s_document_code = $_POST['document_code'];
        $s_document_serie = $_POST['document_serie'];
        //
        $o_documentSerieObj = DocumentSerie::model()->findByPk([
            'document_code' => $s_document_code,
            'document_serie' => $s_document_serie,
                ], [
            'alias' => 'DS',
            'select' => [
                'DS.min_correlative',
                'DS.document_correlative',
                'DS.max_correlative'
            ]
        ]);
        //Verificamos si la serie
        if (isset($o_documentSerieObj)) {
            //
            $a_range = Movement::getDateRange($s_document_code, $s_document_serie, $o_documentSerieObj->document_correlative);
            //
            if ($a_range['code'] === USREST::CODE_SUCCESS) {
                echo CJSON::encode([
                    'code' => USREST::CODE_SUCCESS,
                    'min_correlative' => $o_documentSerieObj->min_correlative,
                    'document_correlative' => $o_documentSerieObj->document_correlative,
                    'max_correlative' => $o_documentSerieObj->max_correlative,
                    'minDate' => $a_range['data']['minDate'],
                    'maxDate' => $a_range['data']['maxDate'],
                ]);
            } else {
                echo CJSON::encode([
                    'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                    'message' => $a_range['message']
                ]);
            }
        } else {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => 'Hubo un error al cargar el correlativo, por favor envié una captura a sistemas.'
            ]);
        }
    }

    /**
     * Devuelve la primera serie o la especificada
     * @param array $documentSeries Array de Series
     * @param mixed $document_serie Serie del documento
     * @return DocumentSerie Serie del documento
     */
    private function getSerie($documentSeries, $document_serie = null) {

        if (count($documentSeries) > 0) {

            foreach ($documentSeries as $documentSerie) {
                if ($documentSerie->document_serie == $document_serie) {
                    return $documentSerie;
                }
            }

            return $documentSeries[0];
        }

        return null;
    }

    /**
     * Obtiene el correlativo de la serie enviada
     * @param DocumentSerie $documentSerie Objeto Serie
     * @param integer $document_correlative Correlativo
     * @return integer Correlativo del documento
     */
    private function getCorrelative($documentSerie, $document_correlative) {

        if (isset($documentSerie)) {
            return $documentSerie->document_correlative;
        }

        return $document_correlative;
    }

    /**
     * Genera una lista desplegable de Series
     * @param mixed $id ID de la lista desplegable
     * @param mixed $value Valor seleccionado
     * @param array $items Items de la lista
     * @param boolean $readonly Indicador de Sólo Lectura
     * @return mixed Html de la lista desplegable
     */
    private function generateSerieDropDownList($id, $value, $items, $readonly) {
        return CHtml::dropDownList("Movement[document_serie]", $value, $items, array(
                    'id' => $id,
                    'class' => 'form-control',
                    'readonly' => $readonly
        ));
    }

    /**
     * Genera una campo de texto
     * @param mixed $value Serie del documento
     * @param boolean $readonly Indicador de Sólo Lectura
     * @return mixed Html de la lista dsplegable
     */
    private function generateSerieFieldtext($value, $readonly) {
        return CHtml::textField("Movement[document_serie]", $value, array(
                    'class' => 'form-control',
                    'placeholder' => 'Ingrese una serie',
                    'readonly' => $readonly,
                    'maxLength' => 4
        ));
    }

    private function generateCorrelativeFieldText($id, $min, $current, $max, $readonly) {
        $html = '';
        $html .= CHtml::numberField('Movement[min_correlative]', $min, array(
                    'readonly' => true,
        ));
        $html .= CHtml::numberField("Movement[document_correlative]", $current, array(
                    'id' => $id,
                    'class' => 'form-control us-double0',
                    'placeholder' => 'Ingrese un correlativo',
                    'readonly' => $readonly == 1,
                    'max' => $max,
                    'min' => $min,
        ));
        $html .= CHtml::numberField('Movement[max_correlative]', $max, array(
                    'readonly' => true,
        ));
        return $html;
    }

    public function actionLoadCommercialStocks() {
        try {
            //Valores
            $i_stock_mode = $_POST['stock_mode'];
            $a_pks = $_POST['pks'];
            $s_emission_date = $_POST['emission_date'];
            $i_exclude_id = isset($_POST['exclude_id']) ? $_POST['exclude_id'] : null;
            $i_store_id = isset($_POST['store_id']) ? $_POST['store_id'] : null;
            //
            $a_data = DpAllProductPresentations::getAssocStocks(DpAllProductPresentations::SCENARIO_WITH_STOCK, $i_stock_mode, $a_pks, $s_emission_date, $i_exclude_id, $i_store_id);
            //
            echo CJSON::encode($a_data);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function actionLoadCostsAndStocks() {
        try {
            //Valores
            $i_stock_mode = $_POST['stock_mode'];
            $a_pks = $_POST['pks'];
            $i_warehouse_id = isset($_POST['warehouse_id']) ? $_POST['warehouse_id'] : null;
            $s_emission_date = $_POST['emission_date'];
            $i_exclude_id = isset($_POST['exclude_id']) ? $_POST['exclude_id'] : null;
            $i_get_ubications = isset($_POST['get_ubications']) ? $_POST['get_ubications'] : 0;

            $a_models = (new DpAllMerchandisePresentations(DpAllMerchandisePresentations::SCENARIO_WITH_STOCK))
                    ->setAttributes([
                        'stock_mode' => $i_stock_mode,
                        'warehouse_id' => !USString::isBlank($i_warehouse_id) ? $i_warehouse_id : $this->getDefaultWarehouseId(),
                        'date' => $s_emission_date,
                        'exclude_id' => $i_exclude_id,
                        'get_ubications' => $i_get_ubications,
                        'disable_filter_type' => 1
                    ])->addInCondition('pk', $a_pks)
                    ->findAll();

            $a_data = [];

            foreach ($a_models as $o_model) {

                $a_data[$o_model->pk] = array(
                    'product_id' => $o_model->product_id,
                    'unit_stock' => $o_model->unit_stock,
                    'unit_cost' => $o_model->unit_cost,
                    'equivalence' => $o_model->equivalence,
                    'stock' => $o_model->stock,
                    'mixed_stock' => $o_model->mixed_stock,
                    'currency' => Currency::PEN,
                    'cost' => $o_model->cost,
                    'uref_cost_pen' => $o_model->uref_cost_pen,
                    'uref_cost_usd' => $o_model->uref_cost_usd,
                    'ulp_cost_pen' => $o_model->ulp_cost_pen,
                    'ulp_cost_usd' => $o_model->ulp_cost_usd,
                    'ubications' => $o_model->ubications
                );
            }

            echo CJSON::encode($a_data);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function actionPrint($id, $format, $print_mode = self::PRINT_MODE_NORMAL) {

        $this->layout = '//layouts/print';
        //Verificamos si el movimiento puede ser impreso
        $s_viewClass = $this->viewClass;
        $o_model = new $s_viewClass;

        try {
            $o_model->getAbility($id, 'is_printable');
        } catch (Exception $ex) {
            $this->_processError($id, $ex);
        }

        try {
            //Iniciamos la transacción
            $this->beginTransaction($o_model);
            //Actualizamos el contador de impresión;
            Movement::updatePrintCount($id);
            $formatData = $this->getFormatData($id, $format, self::EXPORT_HTML, $print_mode);
            //Renderizamos la vista
            $s_view = $this->render($formatData['view'], $formatData['data'], true);
            //Mostramos la vista
            echo $s_view;
            //Confirmamos la transacción
            $this->commitTransaction($o_model);
        } catch (CDbException $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($o_model);
            throw $ex;
        } catch (Exception $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($o_model);
            throw $ex;
        }
    }

    public function actionExport($id, $format, $type, $print_mode = self::PRINT_MODE_NORMAL) {
        $formatData = $this->getFormatData($id, $format, $type, $print_mode);
        $prefix_path = Yii::app()->params['files_dir'] . "/export/{$type}/";
        if (is_object($formatData['data']['header']) && is_subclass_of($formatData['data']['header'], 'CActiveRecord')) {
            $filename = $formatData['data']['header']->toString() . '.' . $type;
        } else if (isset($formatData['data']['header']['document'])) {
            $filename = $formatData['data']['header']['document'] . '.' . $type;
        } else {
            throw new Exception('Comunicarse con Sistemas - Error al obtener nombre del documento');
        }

        $filepath = $prefix_path . $filename;

        switch ($type) {
            case self::EXPORT_PDF:

                $this->convertToPdf($formatData['view'], $formatData['data'], $filepath, true);

                header('Content-Type: application/force-download');
                header('Content-Disposition: attachment; filename=' . $filename);
                header('Content-Transfer-Encoding: binary');
                header('Content-Length: ' . filesize($filepath));

                readfile($filepath);
                break;
            case self::EXPORT_WORD:
                //File parts
                $path_parts = pathinfo($filepath);
                //Creamos el documento
                $html = $this->renderPartial($formatData['view'], $formatData['data'], true, false);

                USWord::convertHtml($html, $path_parts['filename']);

                break;
            case self::EXPORT_EXCEL:
                header("Content-type: application/vnd.ms-excel");
                header("Content-Disposition: attachment;Filename={$filename}");

                echo "<html>";
                echo "<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">";
                echo "<body>";
                echo $this->renderPartial($formatData['view'], $formatData['data'], true, false);
                echo "</body>";
                echo "</html>";

                break;

            default:
                throw new Exception('No es un tipo de exportación válido!');
        }
    }

    public function actionPrinted() {

        $b_result = false;
        //
        if (isset($_POST['movement_id'])) {

            try {
                //Iniciamos la transacción
                $this->beginTransaction();
                $b_result = Movement::updatePrintCount($_POST['movement_id']);
                //Confirmamos la transacción
                $this->commitTransaction();
            } catch (CDbException $ex) {
                //Anulamos la transacción
                $this->rollbackTransaction();
                throw $ex;
            } catch (Exception $ex) {
                //Anulamos la transacción
                $this->rollbackTransaction();
                throw $ex;
            }
        }
        //
        echo CJSON::encode(array(
            'printed' => $b_result
        ));
    }

    public function actionLog($id, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null) {

        //VIEW
        $viewClass = $this->viewClass;
        (new $viewClass)->getAbility($id, 'is_loggable');

        $model = $this->loadLog($id);

        $this->title = "Registrar log/evento para {$model->toString()}";

        //Activamos validación de logs
        $model->movement->enableLogs();

        $modal_html = "";
        $content_html = "";
        $success = false;

        if (Yii::app()->request->isPostRequest) {

            //Logs
            if (isset($_POST['MovementLog'])) {
                foreach ($_POST['MovementLog'] as $attributes) {
                    $model->movement->addMovementLog($attributes);
                }
            }

            if ($model->movement->validate(array('tempMovementLogs'))) {

                try {
                    $this->beginTransaction($model);
                    //Guardamos logs
                    $model->movement->saveLogs();
                    //Ability
                    Ability::generateAbility($id, [], [], null);
                    //Changelog
                    $a_changes = [];
                    foreach ($model->movement->tempMovementLogs as $o_log) {
                        $a_changes[] = $o_log->getChanges();
                    }
                    $model->simple(Changelog::OTHER, Changelog::DEFAULT_REASON_CODE, '', [
                        'movement' => [
                            'tempMovementLogs' => $a_changes
                        ]
                    ]);

                    $this->commitTransaction($model);

                    $success = true;
                } catch (CDbException $ex) {
                    //Anulamos la transacción
                    $this->rollbackTransaction($model);
                    throw new Exception($ex->errorInfo[2]);
                } catch (Exception $ex) {
                    //Anulamos la transacción
                    $this->rollbackTransaction($model);
                    throw $ex;
                }
            }
        } else {
            $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('parent_id' => $parent_id, 'modal_id' => $modal_id)), true, true);

            $model->movement->readMovementLogs();
        }

        if (!$success) {
            $content_html = $this->renderPartial('//movement/_log', array(
                'data' => array(
                    'modal_id' => $modal_id,
                    'model' => $model,
                    'person_items' => (new DpUser(DpUser::SCENARIO_INTERNAL))->getListData('person_id', 'person_name'),
                )
                    ), true, true);
        } else {
            $message = Strings::SUCCESS_OPERATION;
            switch ($type) {
                case 'grid':
                    $content_html = '';
                    break;
                case 'redirect':
                    Yii::app()->user->setFlash('success', $message);
                    $content_html = $redirect_url;
                    break;
            }
        }

        echo CJSON::encode(array(
            'success' => $success,
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html,
            'element_id' => $element_id,
            'type' => $type,
        ));
    }

    public function actionLink($id, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null) {

        $o_movement = Movement::model()->findByPk(array('movement_id' => $id));
        $o_model = new PaymentLink();
        $o_model->movement_id = $id;

        $this->title = "Registrar link de pago para {$o_model->toString()}";

        $modal_html = "";
        $content_html = "";
        $success = false;

        if (Yii::app()->request->isPostRequest) {

            if (isset($_POST['PaymentLink'])) {
                $o_model->attributes = $_POST['PaymentLink'];
            }

            if ($o_model->validate()) {

                try {
                    $this->beginTransaction($o_model);
                    $o_model->insert();
                    $this->commitTransaction($o_model);
                    $success = true;
                } catch (CDbException $ex) {
                    //Anulamos la transacción
                    $this->rollbackTransaction($o_model);
                    throw new Exception($ex->errorInfo[2]);
                } catch (Exception $ex) {
                    //Anulamos la transacción
                    $this->rollbackTransaction($o_model);
                    throw $ex;
                }
            }
        } else {
            $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('parent_id' => $parent_id, 'modal_id' => $modal_id)), true, true);
        }

        if (!$success) {
            $a_api_user_items = (new DpApiUser(DpApiUser::SCENARIO_PAY_PATTERN))->findAll();
            $content_html = $this->renderPartial('//movement/_payLink', array(
                'data' => array(
                    'modal_id' => $modal_id,
                    'model' => $o_model,
                    'movement_code' => $o_movement->movement_code,
                    'api_user_items' => $a_api_user_items
                )
                    ), true, true);
        } else {
            $message = Strings::SUCCESS_OPERATION;
            switch ($type) {
                case 'grid':
                    $content_html = '';
                    break;
                case 'redirect':
                    Yii::app()->user->setFlash('success', $message);
                    $content_html = $redirect_url;
                    break;
            }
        }

        echo CJSON::encode(array(
            'success' => $success,
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html,
            'element_id' => $element_id,
            'type' => $type,
        ));
    }

    public function actionNull($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null, $confirm = 0) {

        //Si esta marcado el indicador de confirmación
        if ($confirm) {
            try {
                $model = new Changelog();

                $modal_html = "";
                $content_html = "";
                $success = false;

                if (isset($_POST['Changelog'])) {

                    $model->attributes = $_POST['Changelog'];

                    if ($model->validate(array('reason_code', 'reason'))) {
                        $this->_null($id, $model->getAttribute('reason_code'), $model->getAttribute('reason'));
                        Yii::app()->user->setFlash('success', Strings::SUCCESS_OPERATION);
                        $success = true;
                    }
                } else {
                    $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('parent_id' => $parent_id, 'modal_id' => $modal_id)), true, true);
                }

                if (!$success) {
                    $content_html = $this->renderPartial('//movement/_null', array(
                        'data' => array(
                            'modal_id' => $modal_id,
                            'model' => $model,
                            'reason_items' => (new DpMultitable(Multitable::CATALOG_9))->getListData('value', 'description'),
                        )
                            ), true, true);
                } else {
                    $content_html = $redirect_url;
                }

                echo CJSON::encode(array(
                    'success' => $success,
                    'modal_id' => $modal_id,
                    'modal_html' => $modal_html,
                    'content_html' => $content_html,
                    'element_id' => $element_id,
                    'type' => $type,
                ));
            } catch (CDbException $ex) {
                $this->_processError($id, $ex);
            } catch (CHttpException $ex) {
                $this->_processError($id, $ex);
            } catch (Exception $ex) {
                $this->_processError($id, $ex);
            }
        } else {

            try {
                $this->_null($id);

                if (Yii::app()->request->isAjaxRequest) {
                    $content_html = $this->afterSuccess($element_id, $type);

                    echo CJSON::encode(array(
                        'type' => $type,
                        'element_id' => $element_id,
                        'content_html' => $content_html
                    ));
                } else {
                    Yii::app()->user->setFlash('success', Strings::SUCCESS_OPERATION);
                    $this->redirect(array('view', 'id' => $id));
                }
            } catch (CDbException $ex) {
                $this->_processError($id, $ex);
            } catch (CHttpException $ex) {
                $this->_processError($id, $ex);
            } catch (Exception $ex) {
                $this->_processError($id, $ex);
            }
        }
    }

    public function actionRemove($id, $type = null, $element_id = null) {
        try {
            $this->_remove($id);

            if (Yii::app()->request->isAjaxRequest) {
                $content_html = $this->afterSuccess($element_id, $type);

                echo CJSON::encode(array(
                    'type' => $type,
                    'element_id' => $element_id,
                    'content_html' => $content_html
                ));
            } else {
                Yii::app()->user->setFlash('success', Strings::SUCCESS_OPERATION);
                $this->redirect(array('index'));
            }
        } catch (CDbException $ex) {
            $this->_processError($id, $ex);
        } catch (CHttpException $ex) {
            $this->_processError($id, $ex);
        } catch (Exception $ex) {
            $this->_processError($id, $ex);
        }
    }

    public function actionDiscard($id, $type = null, $element_id = null) {
        try {
            $payment_schedule_dates = PaymentScheduleDate::findByMovement($id);

            if (count($payment_schedule_dates) == 0) {

                $this->_discard($id);

                if (Yii::app()->request->isAjaxRequest) {
                    $content_html = $this->afterSuccess($element_id, $type);

                    echo CJSON::encode(array(
                        'type' => $type,
                        'element_id' => $element_id,
                        'content_html' => $content_html
                    ));
                } else {
                    Yii::app()->user->setFlash('success', Strings::SUCCESS_OPERATION);
                    $this->redirect(array('view', 'id' => $id));
                }
            } else {

                $a_dates = [];
                $s_dates = "";

                foreach ($payment_schedule_dates as $payment_schedule_date) {
                    if (!in_array($payment_schedule_date->date, $a_dates)) {
                        $a_dates[] = $payment_schedule_date->date;
                        $s_dates .= " " . SIANTime::displayDate($payment_schedule_date->date);
                    }
                }

                Yii::app()->user->setFlash('error', 'Hay ' . count($payment_schedule_dates) . ' programacion(es) de pago(s) activa(s) para el documento para' . $s_dates);
                $this->redirect(array('view', 'id' => $id));
            }
        } catch (CDbException $ex) {
            $this->_processError($id, $ex);
        } catch (CHttpException $ex) {
            $this->_processError($id, $ex);
        } catch (Exception $ex) {
            $this->_processError($id, $ex);
        }
    }

    protected function filterIndex(&$p_o_model) {
        //No tiene permiso para operar sobre cualquier movimiento?
        if (!$this->checkRoute('any_movement')) {
            if ($this->checkRoute('any_group_movement')) {

                $a_person_ids = User::getPeopleYouManage();
                $a_person_ids[] = Yii::app()->user->getState('person_id');
                $p_o_model->addInCondition('person_id', $a_person_ids);
            } else {
                $p_o_model->addEqualCondition('person_id', Yii::app()->user->getState('person_id'));
            }
        }
        //Tiendas 
        $p_o_model->addInCondition('store_id', $this->getStoreIds());
        $p_o_model->status = 1;
    }

    public function actionAdvanced($id, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null) {

        (new $this->viewClass)->getAbility($id, 'is_advancing');

        $model = $this->_getMovement($id);

        $this->title = $title = "Cambiar opciones avanzadas para '" . $model->toString() . "'";

        $modal_html = "";
        $content_html = "";
        $success = false;

        if (isset($_POST['AdvancedOption'])) {

            $model->movement->advancedOption->attributes = $_POST['AdvancedOption'];

            $b_save_movement = false;
            if (isset($_POST['Movement'])) {
                $model->movement->custom_category = $_POST['Movement']['custom_category'];
                $b_save_movement = true;
            }

            try {
                //Iniciamos la transacción
                $this->beginTransaction($model->movement->advancedOption);

                $model->movement->advancedOption->updateByPk(array(
                    'movement_id' => $model->movement->advancedOption->movement_id,
                        ), array(
                    'skip_check_debt' => $model->movement->advancedOption->skip_check_debt,
                    'skip_check_later' => $model->movement->advancedOption->skip_check_later,
                    'no_income' => $model->movement->advancedOption->no_income,
                    'omit_deactivation' => $model->movement->advancedOption->omit_deactivation,
                    'allow_sale_below_cost' => $model->movement->advancedOption->allow_sale_below_cost,
                    'allow_sale_below_min_price' => $model->movement->advancedOption->allow_sale_below_min_price,
                    'allow_dispatch_below_cost' => $model->movement->advancedOption->allow_dispatch_below_cost,
                    'allow_pay' => $model->movement->advancedOption->allow_pay,
                    'close_items' => $model->movement->advancedOption->close_items,
                    'lock_payment' => $model->movement->advancedOption->lock_payment
                ));

                $a_changes = [
                    'advancedOption' => [
                        'skip_check_debt' => [
                            'previous' => $model->movement->advancedOption->oldAttributes['skip_check_debt'],
                            'current' => $model->movement->advancedOption->skip_check_debt
                        ],
                        'skip_check_later' => [
                            'previous' => $model->movement->advancedOption->oldAttributes['skip_check_later'],
                            'current' => $model->movement->advancedOption->skip_check_later
                        ],
                        'no_income' => [
                            'previous' => $model->movement->advancedOption->oldAttributes['no_income'],
                            'current' => $model->movement->advancedOption->no_income
                        ],
                        'omit_deactivation' => [
                            'previous' => $model->movement->advancedOption->oldAttributes['omit_deactivation'],
                            'current' => $model->movement->advancedOption->omit_deactivation
                        ],
                        'allow_sale_below_cost' => [
                            'previous' => $model->movement->advancedOption->oldAttributes['allow_sale_below_cost'],
                            'current' => $model->movement->advancedOption->allow_sale_below_cost
                        ],
                        'allow_sale_below_min_price' => [
                            'previous' => $model->movement->advancedOption->oldAttributes['allow_sale_below_min_price'],
                            'current' => $model->movement->advancedOption->allow_sale_below_min_price
                        ],
                        'allow_dispatch_below_cost' => [
                            'previous' => $model->movement->advancedOption->oldAttributes['allow_dispatch_below_cost'],
                            'current' => $model->movement->advancedOption->allow_dispatch_below_cost
                        ],
                        'allow_pay' => [
                            'previous' => $model->movement->advancedOption->oldAttributes['allow_pay'],
                            'current' => $model->movement->advancedOption->allow_pay
                        ],
                        'close_items' => [
                            'previous' => $model->movement->advancedOption->oldAttributes['close_items'],
                            'current' => $model->movement->advancedOption->close_items
                        ],
                        'lock_payment' => [
                            'previous' => $model->movement->advancedOption->oldAttributes['lock_payment'],
                            'current' => $model->movement->advancedOption->lock_payment
                        ]
                    ]
                ];
                $s_reason = "";
                $a_movement_children = [];
                if ($model->movement->advancedOption->oldAttributes['allow_pay'] != $model->movement->advancedOption->allow_pay) {
                    $s_reason .= 'Permitir pago cambia a ' . $model->movement->advancedOption->allow_pay . '.';
                };

                if ($model->movement->advancedOption->oldAttributes['lock_payment'] != $model->movement->advancedOption->lock_payment) {
                    $s_reason .= 'Bloquear pagos cambia a ' . $model->movement->advancedOption->lock_payment . '.';
                };
                if ($model->movement->advancedOption->oldAttributes['close_items'] != $model->movement->advancedOption->close_items) {

                    (new SpCloseMovementWithItems())->setParams([
                        'xmovement_id' => $model->movement->movement_id,
                        'xclose' => $model->movement->advancedOption->close_items
                    ])->execute();

                    $a_movement_children = MovementLink::model()->findAllByAttributes(["status" => 1, "movement_parent_id" => $model->movement_id]);
                    $s_reason = 'Cerrar items cambia a ' . $model->movement->advancedOption->close_items . '.';
                };

                if ($b_save_movement) {
                    $model->movement->updateByPk(array(
                        'movement_id' => $model->movement->movement_id,
                            ), array(
                        'custom_category' => $model->movement->custom_category,
                    ));
                    $a_changes ['custom_category'] = [
                        'previous' => $model->movement->oldAttributes['custom_category'],
                        'current' => $model->movement->custom_category
                    ];
                }
                //Changelog
                $model->simple(Changelog::OTHER, Changelog::DEFAULT_REASON_CODE, $s_reason, [
                    'movement' => $a_changes
                ]);
                //Actualizamos ability
                Ability::generateAbility($model->movement_id, [], [], null);
                foreach ($a_movement_children as $child) {
                    Ability::generateAbility($child->movement_id, [], [], null);
                }
                //
                $success = true;

                if ($model->movement->route == 'warehouse/transformationOrder' &&
                        $model->movement->advancedOption->oldAttributes['close_items'] == 0 &&
                        $model->movement->advancedOption->close_items == 1) {

                    $o_LinkTransformationOut = MovementLink::model()->findByAttributes(
                            [
                                "movement_parent_id" => $model->movement_id,
                                "route" => 'warehouse/transformationOut',
                                "status" => 1
                            ]
                    );

                    if (isset($o_LinkTransformationOut)) {
                        //Aqui se realiza el ingreso por transformación
                        $o_transformationOut = WarehouseMovement::model()->with(
                                        [
                                            'movement' => [
                                                'alias' => 'MH',
                                                'joinType' => 'join',
                                                'with' => [
                                                    'extraInfo' => [
                                                        'alias' => 'EI',
                                                        'with' => [
                                                            'warehouse',
                                                        ]
                                                    ]
                                                ]
                                            ]
                                        ]
                                )->findByPk($o_LinkTransformationOut->movement_id);
                        $person_id = Yii::app()->user->getState('person_id');
                        $warehouse_id = $o_transformationOut->movement->extraInfo->warehouse->warehouse_id;
                        $exchange_rate = ExchangeRate::getLastExchangeRate();
                        $this->_createTransformationIn($o_transformationOut, $person_id, $warehouse_id, $exchange_rate);

                        (new SpGeneratePostAbility())->setParams([
                            'mode' => SpGeneratePostAbility::MODE_ID,
                            'params' => $model->movement->movement_id,
                            'movement_list' => '',
                            'entry_group_list' => ''
                        ])->execute();
                    }
                }
                //Confirmamos la transacción
                $this->commitTransaction($model->movement->advancedOption);
            } catch (Exception $ex) {
                //Anulamos la transacción
                $this->rollbackTransaction($model->movement->advancedOption);
                throw $ex;
            }
        } else {
            $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('parent_id' => $parent_id, 'modal_id' => $modal_id)), true, true);
        }

        if (!$success) {
            $content_html = $this->renderPartial('//movement/_advanced', array(
                'data' => array(
                    'model' => $model,
                    'modal_id' => $modal_id,
                )
                    ), true, true);
            echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $model);
        } else {
            if (isset($redirect_url)) {
                $message = Strings::SUCCESS_OPERATION;
                Yii::app()->user->setFlash('success', $message);
                $type = 'redirect';
                $content_html = $redirect_url;
                echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $model);
            } else {
                $content_html = $this->afterSuccess($element_id, $type);
                echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $model);
            }
        }
    }

    public function actionTicket() {

        header('Content-type: application/json');

        if (isset($_POST['movement_id'], $_POST['raw_printer'], $_POST['print_type'])) {

            //La impresión de ticket es la única dónde se debe imprimir en orden
            if ($_POST['print_type'] === PrintType::IMPRESION_TICKET) {
                try {
                    //Cargamos estado de impresión del anterior ticket
                    (new SpGetPreviousPrintedTicket())->setParams(array('movement_id' => $_POST['movement_id']))->find();
                } catch (CDbException $ex) {
                    echo CJSON::encode(array(
                        'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                        'message' => $ex->errorInfo[2],
                    ));
                    return;
                }
            }

            $i_print_series = isset($_POST['print_series']) ? $_POST['print_series'] : 0;
            $i_print_payments = isset($_POST['print_payments']) ? $_POST['print_payments'] : 0;

            //Cargamos el ticket de la BD
            $o_model = $this->getRAWModel('movement_id', $_POST['movement_id']);

            $o_rawPrinter = RawPrinter::model()->findByAttributes([
                'raw_printer_name' => $_POST['raw_printer'],
                'owner' => Store::OWNER,
                'owner_id' => $o_model->movement->store_id,
            ]);

            if (isset($o_rawPrinter)) {
                if (Yii::app()->controller->getOrganization()->globalVar->business_line_type == GlobalVar::BUSINESS_LINE_RESTAURANT) {
                    $escpos = $this->getRAWShortDesign($o_model, [
                        'type' => $o_rawPrinter->type,
                        'columns' => $o_rawPrinter->columns,
                        'line_spacing' => $o_rawPrinter->getLineSpacingValue(),
                        'print_mode' => $o_rawPrinter->getDefaultPrintMode(),
                        'raw_printer_serie' => $o_rawPrinter->raw_printer_serie,
                        'authorization_number' => $o_rawPrinter->authorization_number,
                        'finish_mode' => $o_rawPrinter->finish_mode,
                        'open_cashbox' => $o_rawPrinter->open_cashbox,
                        'print_series' => $i_print_series,
                        'print_payments' => $i_print_payments,
                        'qz' => 1
                    ]);
                } else {
                    $escpos = $this->getRAWDesign($o_model, [
                        'type' => $o_rawPrinter->type,
                        'columns' => $o_rawPrinter->columns,
                        'line_spacing' => $o_rawPrinter->getLineSpacingValue(),
                        'print_mode' => $o_rawPrinter->getDefaultPrintMode(),
                        'raw_printer_serie' => $o_rawPrinter->raw_printer_serie,
                        'authorization_number' => $o_rawPrinter->authorization_number,
                        'finish_mode' => $o_rawPrinter->finish_mode,
                        'open_cashbox' => $o_rawPrinter->open_cashbox,
                        'print_series' => $i_print_series,
                        'print_payments' => $i_print_payments,
                        'qz' => 1
                    ]);
                }


                echo CJSON::encode(array(
                    'code' => USREST::CODE_SUCCESS,
                    'message' => 'success',
                    'escpos' => $escpos,
                ));
            } else {
                echo CJSON::encode(array(
                    'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                    'message' => "La impresora no está registrada en {$o_model->movement->store->toString()}. Comuníquese  con el administrador del sistema.",
                ));
            }
        } else {
            throw new Exception('Asegúrese de enviar por POST el ID del movimiento, el nombre de la impresora, y el tipo de impresión!');
        }
    }

    public function actionGetWarehouses() {

        $i_store_id = isset($_POST['store_id']) ? $_POST['store_id'] : null;
        $i_incoming = isset($_POST['incoming']) ? $_POST['incoming'] : null;
        $i_outgoing = isset($_POST['outgoing']) ? $_POST['outgoing'] : null;
        $i_commercial_treatment = isset($_POST['commercial_treatment']) ? $_POST['commercial_treatment'] : null;
        $i_default = isset($_POST['default']) ? $_POST['default'] : null;
        $i_warehouse_id = isset($_POST['warehouse_id']) ? $_POST['warehouse_id'] : null;
        $i_only_allowed_warehouses = isset($_POST['only_allowed_warehouses']) ? $_POST['only_allowed_warehouses'] : null;
        $i_check_warehouses = isset($_POST['check_warehouses']) ? $_POST['check_warehouses'] : null;
        $a_warehouse_types = isset($_POST['warehouse_types']) ? $_POST['warehouse_types'] : null;
        $i_internal = isset($_POST['is_internal']) ? $_POST['is_internal'] : null;
        //
        $a_models = (new DpWarehouse(DpWarehouse::SCENARIO_WAREHOUSE_ID))->findAll();

        $message = '';
        $foundGeneral = false;
        $items = [];
        foreach ($a_models as $o_model) {

            $foundCurrent = $o_model->warehouse_id == $i_warehouse_id;

            $valid = true;

            //Pertenencia a la tienda
            if (isset($i_store_id)) {
                if ($o_model->store_id == $i_store_id) {
                    $valid = $valid && true;
                } else {
                    if ($foundCurrent) {
                        $message = "{$o_model->warehouse_name} no pertenece a la tienda selecionada!";
                    }
                    $valid = $valid && false;
                }
            }

            //Entrada
            if (isset($i_incoming)) {
                if ($o_model->incoming == 1) {
                    $valid = $valid && true;
                } else {
                    if ($foundCurrent) {
                        $message = "{$o_model->warehouse_name} no permite entradas!";
                    }
                    $valid = $valid && false;
                }
            }

            //Salida
            if (isset($i_outgoing)) {
                if ($o_model->outgoing == 1) {
                    $valid = $valid && true;
                } else {
                    if ($foundCurrent) {
                        $message = "{$o_model->warehouse_name} no permite salidas!";
                    }
                    $valid = $valid && false;
                }
            }

            //Tipo consignación
            if (isset($a_warehouse_types) && count($a_warehouse_types) > 0) {
                if (in_array($o_model->warehouse_type, $a_warehouse_types)) {
                    $valid = $valid && true;
                } else {
                    if ($foundCurrent) {
                        $message = "{$o_model->warehouse_name} no puede ser elegido!";
                    }
                    $valid = $valid && false;
                }
            }

            //Trato comercial
            if (isset($i_commercial_treatment)) {
                if ($o_model->commercial_treatment == 1) {
                    $valid = $valid && true;
                } else {
                    if ($foundCurrent) {
                        $message = "{$o_model->warehouse_name} no es de trato comercial!";
                    }
                    $valid = $valid && false;
                }
            }

            if (isset($i_internal)) {
                if ($o_model->internal == $i_internal)
                    $valid = $valid && true;
                else
                    $valid = $valid && false;
            }

            //Almacén por defecto
            if (isset($i_default)) {
                if ($o_model->default == 1) {
                    $valid = $valid && true;
                } else {
                    if ($foundCurrent) {
                        $message = "{$o_model->warehouse_name} no es por defecto!";
                    }
                    $valid = $valid && false;
                }
            }

            //Si el almacen pertenece a alguna de las tiendas
            if ($i_only_allowed_warehouses == 1) {
                if ((isset($o_model->business_unit_id) && $this->checkBusinessUnit($o_model->business_unit_id)) || $this->checkStore($o_model->store_id)) {
                    $valid = $valid && true;
                } else {
                    if ($foundCurrent) {
                        $message = "No tiene permiso para usar {$o_model->warehouse_name}!";
                    }
                    $valid = $valid && false;
                }
            }

            //Si tiene acceso al almacén
            if ($i_check_warehouses == 1) {
                if ($this->checkWarehouse($o_model->warehouse_id)) {
                    $valid = $valid && true;
                } else {
                    if ($foundCurrent) {
                        $message = "No tiene permiso para usar {$o_model->warehouse_name}!";
                    }
                    $valid = $valid && false;
                }
            }


            if ($valid) {
                $items[] = array(
                    'warehouse_id' => $o_model->warehouse_id,
                    'warehouse_name' => $o_model->warehouse_name,
                    'physical' => $o_model->physical,
                    'level_quantity' => $o_model->level_quantity,
                );
            }

            if ($foundCurrent) {
                $foundGeneral = true;
            }
        }

        echo CJSON::encode(array(
            'found' => $foundGeneral,
            'items' => $items,
            'message' => $message,
        ));
    }

    public function actionGetCashboxes() {

        $store_id = $_POST['store_id'];
        $direction = $_POST['direction'];
        $banks = $_POST['banks'];
        $movement_type = $_POST['movement_type'];
        $currency = $_POST['currency'];
        $cashbox_id = isset($_POST['cashbox_id']) ? $_POST['cashbox_id'] : null;
        $is_detraction = isset($_POST['is_detraction']) ? $_POST['is_detraction'] : null;
        $is_internal = isset($_POST['is_internal']) ? $_POST['is_internal'] : null;
        //
        $a_models = Cashbox::getItems();

        $message = '';
        $foundGeneral = false;
        $items = [];
        foreach ($a_models as $o_model) {

            $foundCurrent = $o_model->cashbox_id == $cashbox_id;

            $valid = true;

            //Pertenencia a la tienda
            if ($o_model->store_id == $store_id || USString::isBlank($o_model->store_id)) {
                $valid = $valid && true;
            } else {
                if ($foundCurrent) {
                    $message = "{$o_model->cashbox_name} no pertenece a la tienda seleccionada!";
                }
                $valid = $valid && false;
            }
            //Bancos
            if ($banks || $o_model->type == Cashbox::TYPE_CASHBOX) {
                $valid = $valid && true;
            } else {
                if ($foundCurrent) {
                    $message = "{$o_model->cashbox_name} es banco y Ud. no tiene permisos para bancos!";
                }
                $valid = $valid && false;
            }
            //Tipo de movimiento
            if ($movement_type == Cashbox::ALL_TYPES || in_array($movement_type, $o_model->movement_type)) {
                $valid = $valid && true;
            } else {
                if ($foundCurrent) {
                    $message = "{$o_model->cashbox_name} no es válida para {$movement_type}!";
                }
                $valid = $valid && false;
            }
            //Moneda
            if ($currency == $o_model->currency) {
                $valid = $valid && true;
            } else {
                if ($foundCurrent) {
                    $message = "{$o_model->cashbox_name} no es válida para la moneda seleccionada!";
                }
                $valid = $valid && false;
            }

            //Entrada
            if ($direction == Scenario::DIRECTION_IN) {
                if ($o_model->incoming) {
                    $valid = $valid && true;
                } else {
                    if ($foundCurrent) {
                        $message = "{$o_model->cashbox_name} no permite entradas!";
                    }
                    $valid = $valid && false;
                }
            }
            //Salida
            if ($direction == Scenario::DIRECTION_OUT) {
                if ($o_model->outgoing) {
                    $valid = $valid && true;
                } else {
                    if ($foundCurrent) {
                        $message = "{$o_model->cashbox_name} no permite salidas!";
                    }
                    $valid = $valid && false;
                }
            }
            if (isset($is_detraction)) {
                if ($o_model->is_detraction == $is_detraction)
                    $valid = $valid && true;
                else
                    $valid = $valid && false;
            }
            //Objeto
            if ($this->checkCashbox($o_model->cashbox_id)) {
                $valid = $valid && true;
            } else {
                if ($foundCurrent) {
                    $message = "No tiene permiso para usar {$o_model->cashbox_name}!";
                }
                $valid = $valid && false;
            }
            if (isset($is_internal)) {
                if ($o_model->internal == $is_internal)
                    $valid = $valid && true;
                else {
                    $foundCurrent = false;
                    $valid = $valid && false;
                }
            }

            if ($valid) {
                $items[] = array(
                    'cashbox_id' => $o_model->cashbox_id,
                    'cashbox_name' => $o_model->cashbox_name,
                    'var_fee' => $o_model->var_fee,
                    'fix_fee' => $o_model->fix_fee,
                );
            }

            if ($foundCurrent) {
                $foundGeneral = true;
            }
        }

        echo CJSON::encode(array(
            'found' => $foundGeneral,
            'items' => $items,
            'message' => $message,
        ));
    }

    public function actionAbility($id, $modal_id = null, $parent_id = null) {

        $model = $this->loadAbility($id);

        $this->title = "Visualizando detalle de habilitación para {$this->singular} '{$model->toString()}'";

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array(
                'parent_id' => $parent_id,
                'modal_id' => $modal_id
            )
                ), true, true);
        $content_html = $this->renderPartial("//movement/_ability", array(
            'data' => array(
                'model' => $model,
                'modal_id' => $modal_id
            )
                ), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html
        ));
    }

    public function actionEntryGroupAbility($id, $modal_id = null, $parent_id = null) {

        $model = $this->loadEntryGroupAbility($id);

        $this->title = "Visualizando detalle de habilitación para cuenta(s) '{$model->toString()}'";

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array(
                'parent_id' => $parent_id,
                'modal_id' => $modal_id
            )
                ), true, true);
        $content_html = $this->renderPartial("//movement/_entry_group_ability", array(
            'data' => array(
                'model' => $model,
                'modal_id' => $modal_id
            )
                ), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html
        ));
    }

    /**
     * Obtiene el modelo para anular o eliminar
     * @param integer $p_i_id ID Movimiento
     * @param boolean $p_b_all Indicador que determina si se incluirá todas las relaciones
     * @return object Movement
     */
    private function _getMovement($p_i_id, $p_b_all = true) {

        $s_modelClass = $this->modelClass;

        if ($s_modelClass === get_class(new ProductListMovement)) {
            $a_pk = [
                'owner' => Movement::OWNER,
                'owner_id' => $p_i_id
            ];
        } else {
            $a_pk = [
                'movement_id' => $p_i_id
            ];
        }

        //Todas las relaciones?
        if ($p_b_all) {
            $a_with = [
                'accountingMovement' => [
                    'select' => ['AM.movement_id'],
                    'alias' => 'AM',
                    'with' => [
                        'entries' => [
                            'select' => ['E.entry_id', 'E.account_code', 'E.owner', 'E.owner_id'],
                            'alias' => 'E',
                            'with' => [
                                'entryLinks' => [
                                    'select' => ['EI.entry_parent_id'],
                                    'alias' => 'EI'
                                ],
                                'entryChildLinks' => [
                                    'select' => ['ECL.entry_parent_id'],
                                    'alias' => 'ECL'
                                ]
                            ],
                        ]
                    ]
                ],
                'linkParent' => [
                    'select' => ['MLP.movement_parent_id'],
                    'alias' => 'MLP',
                    'with' => [
                        'movementParent' => [
                            'select' => ['MP.movement_id', 'MP.route', 'MP.emission_date', 'MP.register_date'],
                            'alias' => 'MP',
                            'with' => [
                                'advancedOption' => [
                                    'alias' => 'AOP',
                                ],
                                'linkParent' => [
                                    'select' => ['MLPP.movement_parent_id'],
                                    'alias' => 'MLPP',
                                    'with' => [
                                        'movementParent' => [
                                            'select' => ['MPP.movement_id', 'MPP.route', 'MPP.emission_date', 'MPP.register_date'],
                                            'alias' => 'MPP',
                                            'with' => [
                                                'advancedOption' => [
                                                    'alias' => 'AOPP',
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'linkParents' => [
                    'select' => ['ML.movement_parent_id'],
                    'alias' => 'ML',
                ],
                'linkChild' => array(
                    'alias' => 'LCH',
                    'on' => "LCH.default_ability IS NULL AND LCH.route = 'warehouse/buyInOrder' AND LCH.status",
                ),
                'ability' => [
                    'select' => ['A.movement_id', 'A.is_editable', 'A.is_nullable', 'A.is_removable', 'A.edit_skip_check_debt', 'A.edit_skip_check_later', 'A.edit_no_income', 'A.edit_omit_deactivation', 'A.edit_allow_sale_below_cost', 'A.edit_allow_sale_below_min_price', 'A.edit_allow_dispatch_below_cost', 'A.edit_allow_pay', 'A.edit_close_items', 'A.edit_custom_category'],
                    'alias' => 'A',
                ],
                'advancedOption',
                'scenario' => [
                    'select' => ['S.pay_directly', 'S.apply_directly', 'S.not_scheduled_to_pay', 'S.not_scheduled_to_redeem', 'S.affect_credit'],
                    'alias' => 'S',
                    'with' => [
                        'accountingFile' => [
                            'select' => ['AF.accounting_file_id'],
                            'alias' => 'AF',
                        ]
                    ]
                ],
                'document' => [
                    'select' => [
                        'D.document_code', 'D.sunat_code', 'D.is_electronic', 'D.person_type'
                    ],
                    'alias' => 'D'
                ],
                'operation' => [
                    'select' => [
                        'O.require_conformity'
                    ],
                    'alias' => 'O'
                ],
                //Envío
                'lastSent' => [
                    'select' => [
                        'code',
                    ],
                ]
            ];
        } else {
            $a_with = [];
        }


        return $s_modelClass::model()->with([
                    'movement' => [
                        'select' => ['M.movement_id', 'M.movement_code', 'M.document_code', 'M.document_serie', 'M.document_correlative', 'M.route', 'M.`status`', 'M.type', 'M.emission_date', 'M.currency', 'M.attachment_count', 'M.conformity_service_count', 'M.from_exchange', 'M.exchanged', 'M.store_id', 'M.custom_category'],
                        'alias' => 'M',
                        'with' => $a_with
                    ],
                ])->findByPk($a_pk, [
                    'alias' => 'X',
        ]);
    }

    private function loadLog($id) {

        $s_modelClass = $this->modelClass;

        if ($s_modelClass === get_class(new ProductListMovement)) {
            $a_pk = [
                'owner' => Movement::OWNER,
                'owner_id' => $id
            ];
        } else {
            $a_pk = [
                'movement_id' => $id
            ];
        }

        return $s_modelClass::model()->with([
                    'movement' => [
                        'select' => ['M.movement_id', 'M.document_code', 'M.document_serie', 'M.document_correlative', 'M.route', 'M.`status`', 'M.type', 'M.currency'],
                        'alias' => 'M',
                        'with' => [
                            'ability' => [
                                'select' => ['A.is_loggable'],
                                'alias' => 'A'
                            ],
                            'scenario' => [
                                'select' => ['S.allow_logs'],
                                'alias' => 'S',
                            ],
                            'logs' => [
                                'alias' => 'MLI',
                            ]
                        ]
                    ],
                ])->findByPk($a_pk, [
                    'alias' => 'X',
        ]);
    }

    private function _null($id, $reason_code = Changelog::DEFAULT_REASON_CODE, $reason = '', $b_null_parent = false) {

        //VIEW
        $viewClass = $this->viewClass;
        (new $viewClass)->getAbility($id, 'is_nullable');
        //MODEL
        $model = $this->_getMovement($id);
        try {
            //IDS
            $a_movement_item_data = null;
            if (in_array($model->movement->type, [Scenario::TYPE_COMMERCIAL, Scenario::TYPE_WAREHOUSE, Scenario::TYPE_PRODUCT_LIST])) {
                $a_movement_item_data = Movement::getItemData($id);
            }
            //Iniciamos la transacción
            $this->beginTransaction($model);
            // SI VIENE DESDE UNA OSD/ OC y TIENE PADRE y es un prePay y transferenceOut
            if (isset($model->movement->linkParent->movementParent->movement_id) &&
                    ($model->movement->route == 'treasury/prePay' || $model->movement->route == 'treasury/transferenceOut')
            ) {
                try {

                    $a_parent = CommercialMovement::model()->findbyPk($model->movement->linkParent->movementParent->movement_id); // $model->movement->linkParent->movementParent;

                    if ($a_parent) {
                        if ($a_parent->movement->route == 'logistic/purchaseOrder' && $model->movement->route == 'treasury/prePay') {
                            $s_currency = $a_parent->movement->currency;
                            $f_amount = $model['total_' . $s_currency];
                            $a_parent->advance_balance = $a_parent->advance_balance + $f_amount;
                            $a_parent->update();
                        }
                    } else {
                        $a_parent = ProductListMovement::model()->findByPk(array('owner' => Movement::OWNER, 'owner_id' => $model->movement->linkParent->movementParent->movement_id));
                        if ($a_parent && $a_parent->movement->route == 'treasury/cashOutOrder' && $model->movement->route == 'treasury/prePay') {
                            $s_currency = $a_parent->movement->currency;
                            $total_currency = 'total_' . $s_currency;
                            $f_amount = $model[$total_currency];

                            if ($s_currency == Currency::PEN) {
                                $a_parent->balance_pen = $a_parent->balance_pen + $f_amount;
                                $a_parent->balance_usd = $a_parent->balance_pen / $a_parent->movement->exchange_rate;
                            } else {
                                $a_parent->balance_usd = $a_parent->balance_usd + $f_amount;
                                $a_parent->balance_pen = $a_parent->balance_usd * $a_parent->movement->exchange_rate;
                            }

                            ProductListMovement::model()->updateAll(
                                    ['balance_pen' => $a_parent->balance_pen, 'balance_usd' => $a_parent->balance_usd],
                                    'owner = :owner AND owner_id = :owner_id',
                                    [':owner' => Movement::OWNER, ':owner_id' => $a_parent->movement_id]
                            );
                        }
                    }
                } catch (CDbException $ex) {
                    $this->rollbackTransaction($model);
                    throw new Exception($ex);
                }
            }

            if ($model->movement->route == 'treasury/cashOutOrder' || $model->movement->route == 'logistic/purchaseOrder') {
                $a_paymentScheduleDetail = PaymentScheduleDetail::model()->findAll([
                    'condition' => 'PSD.movement_id = :movement_id',
                    'params' => [
                        ':movement_id' => $id
                    ],
                    'alias' => 'PSD',
                    'select' => 'payment_schedule_detail_id'
                ]);

                if (count($a_paymentScheduleDetail) > 0) {

                    foreach ($a_paymentScheduleDetail as $detail) {
                        $s_payment_schedule_ids .= $detail->payment_schedule_detail_id . ',';
                    }

                    $s_payment_schedule_ids = rtrim($s_payment_schedule_ids, ',');
                    PaymentScheduleDate::model()->updateAll(['state' => PaymentScheduleDate::STATE_CANCELED], [
                        'condition' => "payment_schedule_detail_id IN ({$s_payment_schedule_ids})"
                    ]);
                }
            }

            //Actualizamos
            $rows = $model->movement->updateAll(array('status' => 0), [
                'condition' => "movement_id = {$id}",
            ]);

            if (in_array($model->movement->route, ['logistic/purchaseOrder', 'warehouse/domesticUseOrder', 'warehouse/transferenceOrder', 'warehouse/civilWorkUseOrder', 'warehouse/buyInOrder']) &&
                    isset($model->movement->linkParent) &&
                    $model->movement->linkParent->movementParent->advancedOption->close_items == 1) {

                (new SpCloseMovementWithItems())->setParams([
                    'xmovement_id' => $model->movement->linkParent->movementParent->advancedOption->movement_id,
                    'xclose' => 0
                ])->execute();
                $model->movement->linkParent->movementParent->advancedOption->updateByPk(array(
                    'movement_id' => $model->movement->linkParent->movementParent->advancedOption->movement_id,
                        ), array(
                    'close_items' => 0
                ));
            }

            //STR SI LOS ITEMS TIENEN PADRES DESDE NIA O GRP
            if (in_array($model->movement->route, ['warehouse/transformationOut'])) {
                if (isset($model->movement->linkParent)) {
                    $a_parent_item_ids = ItemLink::getParentItemsByChildrenMovement($id, $model->movement->linkParent->movementParent->movement_id);

                    $placeholders = [];
                    $params = [];

                    foreach ($a_parent_item_ids as $index => $id) {
                        $key = ":item_id_$index";
                        $placeholders[] = $key;
                        $params[$key] = $id;
                    }

                    $condition = 'item_id IN (' . implode(',', $placeholders) . ')';

                    Item::model()->updateAll(
                            ['is_transformed' => 0],
                            $condition,
                            $params
                    );
                }
            }
            if (in_array($model->movement->route, ['warehouse/transformationIn'])) {

                $parent_id = $model->movement->linkParent->movementParent->movement_id;
                $o_order = MovementLink::model()->findByAttributes(
                        [
                            'movement_id' => $parent_id,
                            'parent_route' => 'warehouse/transformationOrder',
                            'status' => 1
                        ]
                );
                if (isset($o_order)) {
                    (new SpGeneratePostAbility())->setParams([
                        'mode' => SpGeneratePostAbility::MODE_ID,
                        'params' => $o_order->movement_parent_id,
                        'movement_list' => '',
                        'entry_group_list' => ''
                    ])->execute();
                }
            }
            //Si es un ingreso del flujo OC - OI - NIA
            if ($model->movement->route == 'warehouse/buyIn' && isset($model->movement->linkParent->movementParent->linkParent) && $model->movement->linkParent->movementParent->route == 'warehouse/buyInOrder') {

                if ($model->movement->linkParent->movementParent->linkParent->movementParent->advancedOption->close_items == 1) {
                    (new SpCloseMovementWithItems())->setParams([
                        'xmovement_id' => $model->movement->linkParent->movementParent->linkParent->movementParent->movement_id,
                        'xclose' => 0
                    ])->execute();
                    $model->movement->linkParent->movementParent->advancedOption->updateByPk(array(
                        'movement_id' => $model->movement->linkParent->movementParent->linkParent->movementParent->movement_id,
                            ), array(
                        'close_items' => 0
                    ));
                }
            }

            if ($model->movement->route == 'warehouse/purchaseBill' && count($model->movement->linkChildren) > 0) {
                foreach ($model->movement->linkChildren as $link) {
                    Ability::updateValue(['is_applied' => 0, $link->movement_id]);
                }
            }

            if ($model->movement->type == Scenario::TYPE_WAREHOUSE && trim($a_movement_item_data['item_ids']) != "") {
                //Actualizamos entry_count
                BatchMovement::model()->updateAll(['status' => 0], [
                    'condition' => "item_id IN ({$a_movement_item_data['item_ids']})"
                ]);
            }

            if ($model->movement->type == Scenario::TYPE_CASHBOX) {
                //Actualizamos entry_count
                PaymentScheduleDate::model()->updateAll(['state' => PaymentScheduleDate::STATE_OPEN, 'payment_movement_id' => null], [
                    'condition' => "payment_movement_id = {$model->movement->movement_id}"
                ]);
            }

            //Se afectó filas
            if ($rows > 0) {
                //Actualiza kardex?
                $kardex_transaction_id = null;
                if (isset($a_movement_item_data) && $a_movement_item_data['affect_kardex'] == 1) {
                    //Kardex
                    $kardex_transaction_id = (new SpRecalculateKardexAndStock())->setParams(array(
                                'product_id' => $a_movement_item_data['merchandise_ids'],
                                'warehouse_id' => $a_movement_item_data['warehouse_ids'],
                                'date' => SIANTime::formatDate($model->movement->emission_date, true)
                            ))->getScalar();
                    //Stocks
                    (new SpRecalculateMerchandiseMaster)->setParams(array(
                        'product_id' => $a_movement_item_data['merchandise_ids'],
                        'warehouse_id' => $a_movement_item_data['warehouse_ids']
                    ))->execute();
                    //Promotion
                    if ($a_movement_item_data['affect_promo'] == 1) {
                        (new SpRecalculatePromotionStock)->setParams(array(
                            'movement_id' => $id,
                        ))->execute();
                    }
                    //Web enabled
                    Merchandise::generateAbility(SpGenerateMerchandiseAbility::MODE_MERCHANDISE, $a_movement_item_data['merchandise_ids']);
                }
                //Actualizamos ability (debe llamarse después del kardex)
                Ability::generateAbility($model->movement_id, [], [], $kardex_transaction_id);
                //Actualiza ultimo costo
                if (isset($a_movement_item_data) && $a_movement_item_data['is_purchase'] == 1) {
                    (new SpRecalculateLastCost)->setParams(array(
                        'product_ids' => $a_movement_item_data['product_ids']
                    ))->execute();
                }
                //Actualiza información crediticia?
                if (isset($model->movement->accountingMovement)) {
                    SpRecalculateCreditInformation::loopEntries($model->movement->accountingMovement->entries);
                }
                if ($model->movement->from_exchange == 1) {

                    $a_parent_models = MovementLink::model()->with('movementParent')->findAllByAttributes(['movement_id' => $model->movement_id]);
                    $a_parent_ids = [];

                    foreach ($a_parent_models as $o_parent) {
                        if ($o_parent->movementParent->internal == 1) {
                            $a_parent_ids[] = $o_parent->movementParent->movement_id;
                        }
                    }

                    if (count($a_parent_ids) > 0) {
                        Movement::model()->updateAll(array(
                            'exchanged' => 0
                                ), array(
                            'condition' => "movement_id IN (" . implode(",", $a_parent_ids) . ")",
                        ));
                    }
                }
                //Si es electrónico
                $b_sent_annulment_to_api = false;
                if ($model->movement->document->is_electronic) {
                    $a_sent_movement = [
                        [
                            'type' => SentMovement::TYPE_ANNUL,
                            'status' => SentMovement::STATUS_NOT_SENDED
                        ]
                    ];
                    //Si el último envío fue un rechazo debe anularse y ya no mandarse como baja
                    if ($model->movement->lastSent->code == SentMovement::CODE_7) {
                        $a_sent_movement[] = [
                            'type' => $model->movement->document->person_type == Document::PERSON_TYPE_JURIDICAL ? SentMovement::TYPE_INVOICE : SentMovement::TYPE_SUMMARY,
                            'status' => SentMovement::STATUS_ANNULLED
                        ];
                    }
                    //
                    SentMovement::saveMovement($model->movement, $a_sent_movement);

                    // Anulacion nubefact
                    if (Yii::app()->params['e_billing_ose'] === YII_OSE_NUBEFACT) {
                        $o_document = Document::model()->findByAttributes(['document_code' => $model->movement->document_code]);

                        // Traer la tienda para obtener la ruta (url) y el token
                        $o_store = Store::model()->findByPk(array('store_id' => $model->movement->store_id));
                        // Valido la url y el token de la API
                        if (isset($o_store['api_path']) && isset($o_store['api_token'])) {
                            // `consultar_anulacion` de COMPROBANTE DE PAGO ELECTRONICO (CPE)
                            $nubefact_operacion = SIANFENubeFacT::$CONSULT_ANNULMENT;
                            if ($o_document->sunat_code === Document::REMISSION_GUIDE) {
                                // `consultar_guia` de COMPROBANTE DE PAGO ELECTRONICO (CPE)
                                $nubefact_operacion = SIANFENubeFacT::$CONSULT_GUIDE;
                            }
                            $a_response_check_cpe = SIANFENubeFacT::checkCPE($o_store['api_path'], $o_store['api_token'], $nubefact_operacion, $o_document->sunat_code, $model->movement->document_serie, $model->movement->document_correlative);
                            if (isset($a_response_check_cpe['errors'])) {
                                // codigo' === 24 => Documento no existe
                                if ($a_response_check_cpe['codigo'] === Nubefact::ERROR_CODE_24) {
                                    $b_sent_annulment_to_api = true;
                                } else {
                                    $msj_error = "ERROR CÓD. " . $a_response_check_cpe['codigo'] . "en `CONSULTA DE COMUNICACIÓN DE BAJA`: " . $a_response_check_cpe['errors'] . ".";
                                    throw new Exception($msj_error, 0);
                                }
                            }
                            // si `$b_sent_annulment_to_api` es `false`, no enviamos la anulación, nos saltamos al siguiente pasa para anular
                            if (!$b_sent_annulment_to_api) {
                                goto EndAnnulment;
                            }
                            // Procedemos con la anulación
                            $reason_annulment = "ANULACION DE LA OPERACION";
                            $a_data_document = SIANFENubeFacT::getAnnulment($model->movement, $o_document, $reason_annulment);
                            $responseJson = SIANFENubeFacT::sendDocument($o_store['api_path'], $o_store['api_token'], $a_data_document);
                            $response_api = json_decode($responseJson, true);
                            $a_sent_movement = [];
                            $a_data = [];
                            $a_response = [];
                            if (isset($response_api['errors'])) {
                                $msj_error = "Error " . $response_api['codigo'] . ": " . $response_api['errors'] . ".";
                                throw new Exception($msj_error, 0);
                            } else {
                                // Si la solicitud fue exitosa
                                $a_sent_movement['movement_id'] = $model->movement_id;
                                $a_sent_movement['reference'] = isset($response_api['sunat_ticket_numero']) ? $response_api['sunat_ticket_numero'] : "";
                                $a_sent_movement['type'] = SentMovement::TYPE_SUMMARY_LOW;
                                $a_sent_movement['status'] = SIANFENubeFacT::getStatusSentMovement($response_api);
                                $a_sent_movement['status_code'] = is_null($response_api['sunat_responsecode']) ? "" : $response_api['sunat_responsecode'];
                                $a_sent_movement['ose'] = Yii::app()->params['e_billing_ose'];
                                $a_sent_movement['response'] = is_null($response_api['sunat_description']) ? "" : $response_api['sunat_description'];
                                $a_sent_movement['basename'] = SIANFENubeFacT::getBasenameSentMovement($model->movement, $o_document);
                                $a_sent_movement['order'] = SIANFENubeFacT::getOrderSentMovement($model->movement_id);
                                $a_sent_movement['last'] = 1;
                                $a_sent_movement['pdf_link'] = isset($response_api['enlace_del_pdf']) ? $response_api['enlace_del_pdf'] : "";
                                $a_sent_movement['xml_link'] = isset($response_api['enlace_del_xml']) ? $response_api['enlace_del_xml'] : "";
                                $a_sent_movement['cdr_link'] = isset($response_api['enlace_del_cdr']) ? $response_api['enlace_del_cdr'] : "";
                                // Actualizar `sunat_sent`
                                $model->movement->simple_update = true;
                                $model->movement->sunat_sent = $response_api['aceptada_por_sunat'] ? 1 : 0;
                                $model->movement->update(['sunat_sent']);

                                $a_response['code'] = SIANFENubeFacT::getCodeSentMovement($model->movement, $a_sent_movement['status'], $a_sent_movement['type']);

                                array_push($a_data, $a_sent_movement);
                                $a_response['data'] = $a_data;
                                //Guardar en SentMovement
                                SentMovement::saveResponse($a_response, SentMovement::TYPE_SUMMARY_LOW);
                            }
                        } else {
                            $msj_error = "La url y el token no existen. Verifique su API.";
                            throw new Exception($msj_error, 0);
                        }
                    }
                }

                EndAnnulment:
                //Changelog
                $model->simple(Changelog::ANNUL, $reason_code, $reason, [
                    'movement' => [
                        'status' => [
                            'previous' => 1,
                            'current' => 0
                        ]
                    ]
                ]);

                //Confirmamos la transacción
                $this->commitTransaction($model);
                // si `$b_sent_annulment_to_api` es `true`, quiere decir que se envió la anulación a la API
                if ($b_sent_annulment_to_api) {
                    // descargar archivos de nubefact, y guardarlos en el proyecto
                    $a_flashes = [];
                    SIANFENubeFacT::downloadElectronicInvoicingFiles($a_flashes, $model->movement);
                    if (count($a_flashes) > 0) {
                        $this->showFlashes($a_flashes);
                    }
                }
            } else {
                $this->rollbackTransaction($model);
                throw new CHttpException(403, 'No se anuló el movimiento. Consulte con sistemas');
            }
        } catch (CDbException $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($model);
            throw new Exception($ex->errorInfo[2]);
        } catch (Exception $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($model);
            if ($ex->getCode() === 0) {
                $a_flashes = ['warning' => ["<p>" . $ex->getMessage() . "</p>"]];
                $this->showFlashes($a_flashes);
            } else {
                throw $ex;
            }
        }
    }

    private function _remove($id) {

        //VIEW
        $viewClass = $this->viewClass;
        (new $viewClass())->getAbility($id, 'is_removable');
        //MODEL
        $model = $this->_getMovement($id);

        try {
            //Condiciones
            $a_movement_item_data = null;
            if (in_array($model->movement->type, [Scenario::TYPE_COMMERCIAL, Scenario::TYPE_WAREHOUSE, Scenario::TYPE_PRODUCT_LIST])) {
                $a_movement_item_data = Movement::getItemData($id);
            }
            //Iniciamos la transacción
            $this->beginTransaction($model);
            //Eliminamos
            $rows = $model->movement->deleteAll([
                'condition' => "movement_id = {$id}",
            ]);
            //Se eliminó alguna fila
            if ($rows > 0) {
                //Actualiza kardex?
                $kardex_transaction_id = null;
                if (isset($a_movement_item_data) && $a_movement_item_data['affect_kardex'] == 1) {
                    //Kardex
                    $kardex_transaction_id = (new SpRecalculateKardexAndStock())->setParams(array(
                                'product_id' => $a_movement_item_data['merchandise_ids'],
                                'warehouse_id' => $a_movement_item_data['warehouse_ids'],
                                'date' => SIANTime::formatDate($model->movement->emission_date, true)
                            ))->getScalar();
                    //Stocks
                    (new SpRecalculateMerchandiseMaster)->setParams(array(
                        'product_id' => $a_movement_item_data['merchandise_ids'],
                        'warehouse_id' => $a_movement_item_data['warehouse_ids']
                    ))->execute();
                    //Promotion
                    if ($a_movement_item_data['affect_promo'] == 1) {
                        (new SpRecalculatePromotionStock)->setParams(array(
                            'movement_id' => $id,
                        ))->execute();
                    }
                    //Web enabled
                    Merchandise::generateAbility(SpGenerateMerchandiseAbility::MODE_MERCHANDISE, $a_movement_item_data['merchandise_ids']);
                }
                //Actualizamos ability (debe llamarse después del kardex)
                Ability::generateAbility($model->movement_id, $model->movement->linkParents, $model->movement->accountable() ? $model->movement->accountingMovement->entries : [], $kardex_transaction_id);
                //Actualiza ultimo costo
                if (isset($a_movement_item_data) && $a_movement_item_data['is_purchase'] == 1) {
                    (new SpRecalculateLastCost)->setParams(array(
                        'product_ids' => $a_movement_item_data['product_ids']
                    ))->execute();
                }
                //Actualiza información crediticia?
                if (isset($model->movement->accountingMovement)) {
                    SpRecalculateCreditInformation::loopEntries($model->movement->accountingMovement->entries);
                }
                //Changelog (no genera detalle de changelog)
                $model->simple(Changelog::DELETE);
                //Confirmamos la transacción
                $this->commitTransaction($model);
            } else {
                $this->rollbackTransaction($model);
                throw new CHttpException(403, 'No se eliminó el movimiento. Consulte con sistemas');
            }
        } catch (CDbException $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($model);
            throw new Exception($ex->errorInfo[2]);
        } catch (Exception $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($model);
            throw $ex;
        }
    }

    private function _discard($id) {
        //VIEW
        $viewClass = $this->viewClass;
        $ability = (new $viewClass())->getAbility($id, 'is_discardable');
        //MODEL
        $this->modelClass = $ability->type . 'Movement';

        $model = $this->_getMovement($id);

        try {
            //IDS
            $a_movement_item_data = null;
            if (in_array($model->movement->type, [Scenario::TYPE_COMMERCIAL, Scenario::TYPE_WAREHOUSE, Scenario::TYPE_PRODUCT_LIST])) {
                $a_movement_item_data = Movement::getItemData($id);
            }
            //Iniciamos la transacción
            $this->beginTransaction($model);
            //Actualizamos
            $rows = $model->movement->accountingMovement->updateAll(array(
                'accounting_file_id' => null,
                'year' => null,
                'period' => null,
                'day' => null,
                'correlative' => null,
                'checked' => 0
                    ), array(
                'condition' => "movement_id = {$id}",
            ));
            //Si se descarta se reinicia el pay_confirmed y apply_confirmed según los campos del scenario
            Ability::model()->updateAll([
                'pay_confirmed' => $model->movement->scenario->pay_directly,
                'apply_confirmed' => $model->movement->scenario->apply_directly
                    ], [
                'condition' => "movement_id = {$id}",
            ]);
            //Se afectó filas
            if ($rows > 0) {
                //Actualizamos ability
                Ability::generateAbility($model->movement_id, [], [], null);
                //Actualiza ultimo costo si es compra y está seteada la opción para setar último costo al provisionar
                if (isset($a_movement_item_data) && $a_movement_item_data['is_purchase'] && $this->getOrganization()->globalVar->set_last_cost_at_provision == 1) {
                    (new SpRecalculateLastCost)->setParams(array(
                        'product_ids' => $a_movement_item_data['product_ids']
                    ))->execute();
                }
                //Actualiza información crediticia?
                if (isset($model->movement->accountingMovement)) {
                    SpRecalculateCreditInformation::loopEntries($model->movement->accountingMovement->entries);
                }
                //Changelog
                $model->simple(Changelog::OTHER, Changelog::DEFAULT_REASON_CODE, '', [
                    'movement' => [
                        'accountingMovement' => [
                            'checked' => [
                                'previous' => 1,
                                'current' => 0
                            ]
                        ]
                    ]
                ]);

                //Confirmamos la transacción
                $this->commitTransaction($model);
            } else {
                $this->rollbackTransaction($model);
                throw new CHttpException(403, 'No se descartó el movimiento. Consulte con sistemas');
            }
        } catch (CDbException $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($model);
            throw new Exception($ex->errorInfo[2]);
        } catch (Exception $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($model);
            throw $ex;
        }
    }

    public function actionGetPdf($id, $type) {

        $a_pdf_data = [];

        switch ($type) {
            case Scenario::TYPE_COMMERCIAL:
                $a_pdf_data = SIANFE::getPDFCommercialData($id);
                break;
            case Scenario::TYPE_PRODUCT_LIST:
            case Scenario::TYPE_WAREHOUSE:
                //Valida primero
                $o_modelBilling = (new DpAccountingEBillingIndexTab7(DpAccountingEBillingIndexTab7::SCENARIO_ALL))->addEqualCondition('movement_id', $id)->find();
                $s_message = "";
                if (!$o_modelBilling) {
                    Yii::app()->user->setFlash('error', 'No se encontró el registro del documento electrónico.');
                    $this->redirect(['view', 'id' => $id]);
                    Yii::app()->end();
                }
//
                if ($o_modelBilling->state == SentMovement::STATUS_NOT_SENDED) {
                    try {
                        $this->processGuide($id);
                    } catch (Exception $ex) {
                        $s_message = 'Envio:' . $ex->getMessage();
                    }
                    $o_modelBilling = (new DpAccountingEBillingIndexTab7(DpAccountingEBillingIndexTab7::SCENARIO_ALL))
                            ->addEqualCondition('movement_id', $id)
                            ->find();
                }

                if ($o_modelBilling->state == SentMovement::STATUS_PENDING) {
                    $i_status = 0;
                    $s_message = "";
                    $a_flashes = [];
                    try {
                        $this->getStatusCdr($o_modelBilling->send_id, $i_status, $s_message, $a_flashes);
                    } catch (Exception $ex) {
                        $s_message = 'Consulta:' . $ex->getMessage();
                    }
                    $o_modelBilling = (new DpAccountingEBillingIndexTab7(DpAccountingEBillingIndexTab7::SCENARIO_ALL))->addEqualCondition('movement_id', $id)->find();
                }

                if ($o_modelBilling->state != SentMovement::STATUS_ACCEPTED) {
                    Yii::app()->user->setFlash('warning',
                            'El documento no ha sido aceptado por SUNAT. No es posible descargar el PDF.' . $s_message);
                    $this->redirect(['view', 'id' => $id]);
                    Yii::app()->end();
                }
                $a_pdf_data = SIANFE::getPDFGuideData($id, $type);
                break;
        }

        if (Yii::app()->params['e_billing_ose'] === YII_OSE_NUBEFACT) {
            if (!file_exists($a_pdf_data['filepath'])) {
                $response = SIANFile::download($a_pdf_data['file_pdf_link'], $a_pdf_data['filepath']);
                if ($response === false) {
                    $s_message = "<p>No se pudo descargar el archivo pdf.</p>";
                    Yii::app()->user->setFlash('warning', $s_message);
                }
            }
        } else {
            switch ($type) {
                case Scenario::TYPE_COMMERCIAL:
                    $this->convertToPdf('//movement/_electronic_movement', $a_pdf_data, $a_pdf_data['filepath'], true);
                    break;
                case Scenario::TYPE_PRODUCT_LIST:
                case Scenario::TYPE_WAREHOUSE:
                    $this->convertToPdf('//movement/_electronic_warehouse_movement', $a_pdf_data, $a_pdf_data['filepath'], true);
                    break;
            }
        }

        header('Content-Type: application/force-download');
        header('Content-Disposition: attachment; filename=' . $a_pdf_data['filename']);
        header('Content-Transfer-Encoding: binary');
        header('Content-Length: ' . filesize($a_pdf_data['filepath']));

        readfile($a_pdf_data['filepath']);
    }

    public function getCashboxes() {
        return Yii::app()->db->createCommand()
                        ->select([
                            "C.cashbox_id",
                            "A.account_name as cashbox_name",
                            "C.currency"
                        ])
                        ->from('cashbox C')
                        ->join('account A', 'A.account_code = C.account_code')
                        ->where("C.type = '" . Cashbox::TYPE_BANK . "' and C.show_in_print and C.`status`", [])
                        ->order("A.account_name ASC")
                        ->queryAll();
    }

    private function getFormatData($id, $format, $type = self::EXPORT_HTML, $print_mode = self::PRINT_MODE_NORMAL) {
        //Obtenemos carpeta de imprimibles
        $s_alias = 'application.views.print';
        $s_path = Yii::getPathOfAlias($s_alias);

        //Verificamos si existe el archivo de impresión
        $s_sub_path = $this->getOrganization()->organization_code;
        //$s_sub_path = "cs";
        $s_filepath = $s_path . DIRECTORY_SEPARATOR . $s_sub_path . DIRECTORY_SEPARATOR . $format . ".php";

        //Verificamos si no existe el formato
        if (!file_exists($s_filepath)) {
            //Si no existe buscamos en COMMON
            $s_sub_path = 'common';
            $s_filepath = $s_path . DIRECTORY_SEPARATOR . $s_sub_path . DIRECTORY_SEPARATOR . $format . ".php";
            if (!file_exists($s_filepath)) {
                //Si no hay formato se redireccionará a la vista principal indicando el mensaje de error
                $this->_processError($id, new Exception('No existe el formato de impresión indicado. Comuníquese con el área de sistemas'));
            }
        }

        //Default
        $header = [];
        $a_detail = [];
        switch ($format) {
            case self::F_GUIA_REMISION_TRANSPORTISTA:
                $header = Yii::app()->db->createCommand()->select([
                                    //Documento
                                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) AS document",
                                    'M.movement_id',
                                    'M.document_serie as document_serie',
                                    'RIGHT(M.document_correlative, 8) as document_correlative',
                                    'M.emission_date',
                                    'M.digest_value',
                                    'M.status',
                                    'M.currency',
                                    "D.sunat_code",
                                    "D.document_name",
                                    "D.person_type",
                                    'M.observation',
                                    //Destinatario
                                    'P.identification_number AS recipient_identification_number',
                                    'P.person_name AS recipient_name',
                                    //Direcciones
                                    "IF(OA.address IS NOT NULL, CONCAT(OA.address, ' (', OG.dist_name, ' - ', OG.prov_name, ' - ', OG.dept_name,')'), '-') AS originAddress",
                                    "IF(DA.address IS NOT NULL, CONCAT(DA.address, ' (', DG.dist_name, ' - ', DG.prov_name, ' - ', DG.dept_name,')'), '-') AS delivery_address",
                                    //Datos de traslado
                                    'MU.description AS reason_transfer',
                                    'PL.reason_transfer_description',
                                    'PL.transfer_start_date',
                                    'PL.delivery_carrier_date',
                                    'PL.number_packages',
                                    'PL.total_weight',
                                    'IF(transport_data = 1,\'TRANSPORTE PRIVADO\',\'TRANSPORTE PÚBLICO\') AS transport_type',
                                    //Datos de trasportista
                                    'PT.identification_type AS company_identification_type',
                                    'PT.identification_number AS company_identification_number',
                                    'PT.person_name AS company_name',
                                    //Datos del conductor
                                    'PS.identification_type AS shipper_identification_type',
                                    'PS.identification_number AS shipper_identification_number',
                                    "REPLACE(PS.person_name, ',', ' ') as shipper_name",
                                    'SH.license_number',
                                    //Datos de trasporte
                                    'UT.plate',
                                    "LSM.basename",
                                    "LSM.pdf_link"
                                ])
                                ->from('product_list PL')
                                ->join('movement M', 'PL.owner_id = M.movement_id')
                                ->join('document D', 'D.document_code = M.document_code')
                                ->leftJoin('person P', 'PL.recipient_id = P.person_id')
                                ->leftJoin('multitable MU', 'MU.multi_id = PL.reason_transfer_id')
                                //Direcciones
                                ->leftJoin('address OA', "OA.owner = '" . Movement::OWNER . "' AND OA.owner_id = M.movement_id AND OA.order = 0")//Dirección de origen
                                ->leftJoin('geoloc OG', "OG.dept_code = OA.dept_code and OG.prov_code = OA.prov_code and OG.dist_code = OA.dist_code")
                                ->leftJoin('address DA', "DA.owner = '" . Movement::OWNER . "' and DA.owner_id = M.movement_id and DA.`order` = 1")//Dirección  de Entrega
                                ->leftJoin('geoloc DG', "DG.dept_code = DA.dept_code and DG.prov_code = DA.prov_code and DG.dist_code = DA.dist_code")
                                //Transportista
                                ->leftjoin('person PT', 'PT.person_id = PL.transport_company_id')
                                //Conductor
                                ->leftJoin('owner_pair OPPSH', "OPPSH.type = '" . OwnerPair::TYPE_MOVEMENT_SHIPPER . "' AND OPPSH.`owner` = '" . Movement::OWNER . "' AND OPPSH.owner_id = M.movement_id")
                                ->leftJoin('owner_pair OPSH', "OPSH.type = OPPSH.type AND OPSH.`owner` = '" . Shipper::OWNER . "' AND OPSH.parent_id = OPPSH.pair_id AND OPSH.order = 1")
                                ->leftJoin('shipper SH', "SH.shipper_id = OPSH.owner_id")
                                ->leftJoin('person PS', "PS.person_id = SH.person_id")
                                //Transporte
                                ->leftJoin('owner_pair OPPUT', "OPPUT.type = '" . OwnerPair::TYPE_MOVEMENT_TRANSPORT_UNIT . "' AND OPPUT.`owner` = '" . Movement::OWNER . "' AND OPPUT.owner_id = M.movement_id")
                                ->leftJoin('owner_pair OPUT', "OPUT.type = OPPSH.type AND OPUT.`owner` = '" . TransportUnit::OWNER . "' AND OPUT.parent_id = OPPUT.pair_id AND OPUT.order = 1")
                                ->leftJoin('transport_unit UT', "UT.transport_unit_id = OPUT.owner_id")
                                //
                                ->leftJoin('sent_movement LSM', 'LSM.movement_id = M.movement_id AND LSM.`last`')
                                ->where("PL.owner = '" . Movement::OWNER . "' AND PL.owner_id = :owner_id", [
                                    ":owner_id" => $id
                                ])->queryRow();

                $a_detail = Yii::app()->db->createCommand()->select([
                            "I.item_id",
                            "I.product_type",
                            "I.product_id",
                            "I.pres_quantity",
                            "IFNULL(PR.measure_name, '" . Yii::app()->controller->getDefaultMerchandiseMeasure()->abbreviation . "') AS measure_name",
                            "UPPER(I.product_name) AS product_name",
                        ])
                        ->from('item I')
                        ->join('global_var GV', 'GV.organization_id = 1')
                        ->leftJoin('presentation PR', 'PR.product_id = I.product_id AND PR.equivalence = I.equivalence')
                        ->where('I.movement_id = :movement_id', [
                            ':movement_id' => $id
                        ])
                        ->queryAll();
                break;
            case self::F_DESPACHO:
            case self::F_GUIA_REMISION:
            case self::F_GUIA_REMISION_01:
            case self::F_INGRESO_ALMACEN:
            case self::F_SALIDA_ALMACEN:

                //HEADER
                switch ("{$this->module->id}/{$this->id}") {
                    case 'warehouse/buyIn':
                        $header = Yii::app()->db->createCommand()
                                ->select([
                                    "M.document_serie",
                                    "M.document_correlative",
                                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                                    "S.title as scenario",
                                    "S.direction",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as delivery_date",
                                    "IF(M.`status`, 'SI', 'NO') as is_active",
                                    'M.observation',
                                    //FROM
                                    "REPLACE(P.person_name, ',', ' ') as from_person_name",
                                    "P.identification_type as from_identification_type",
                                    "P.identification_number as from_identification_number",
                                    "UPPER(WA.address) as from_address",
                                    "WG.dist_name as from_dist",
                                    "WG.prov_name as from_prov",
                                    "WG.dept_name as from_dept",
                                    //TO
                                    "REPLACE(P.person_name, ',', ' ') as to_person_name",
                                    "P.identification_type as to_identification_type",
                                    "P.identification_number as to_identification_number",
                                    "UPPER(WA.address) as to_address",
                                    "WG.dist_name as to_dist",
                                    "WG.prov_name as to_prov",
                                    "WG.dept_name as to_dept",
                                    //Usuario
                                    "U.person_id as user_person_id",
                                    "REPLACE(U.person_name, ',', ' ') as user_person_name",
                                    "M.print_count",
                                    "REPLACE(PC.person_name, ',', ' ') as company_name",
                                    "PC.identification_number as company_identification_number",
                                    "CONCAT_WS('-', MP.document_code, MP.document_serie, MP.document_correlative) as parent_document",
                                ])
                                ->from('warehouse_movement WM')
                                ->join('movement M', 'M.movement_id = WM.movement_id')
                                //Organización
                                ->join('organization O', '')
                                ->join('person P', 'P.person_id = O.person_id')
                                //Usuario
                                ->join('person U', 'U.person_id = M.person_id')
                                //Escenario
                                ->join('scenario S', 'S.route = M.route')
                                //Desde/Hasta
                                ->join('warehouse W', 'W.warehouse_id = WM.warehouse_id')
                                ->join('address WA', "WA.`owner` = '" . Warehouse::OWNER . "' AND WA.owner_id = WM.warehouse_id")
                                ->join('geoloc WG', 'WG.dept_code = WA.dept_code AND WG.prov_code = WA.prov_code AND WG.dist_code = WA.dist_code')
                                //Información extra
                                ->leftJoin('person PC', "PC.`person_id` = WM.transport_company_id")
                                ->leftJoin('movement_link MLP', "MLP.movement_id = M.movement_id AND MLP.default_ability = '" . ScenarioControl::IS_DISPATCHABLE . "'") //Link al padre
                                ->leftJoin('movement MP', 'MP.movement_id = MLP.movement_parent_id') //Link al padre
                                ->where('M.movement_id = :movement_id', array(':movement_id' => $id))
                                ->queryRow();
                        break;
                    case 'warehouse/productReturnIn':
                        $header = Yii::app()->db->createCommand()
                                ->select([
                                    //CABECERA
                                    "M.document_serie",
                                    "M.document_correlative",
                                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                                    "S.title as scenario",
                                    "S.direction",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as delivery_date",
                                    "IF(M.`status`, 'SI', 'NO') as is_active",
                                    'M.observation',
                                    //FROM
                                    "REPLACE(XP.person_name, ',', ' ') as from_person_name",
                                    "XP.identification_type as from_identification_type",
                                    "XP.identification_number as from_identification_number",
                                    "UPPER(IFNULL(XA.address, '-')) as from_address",
                                    "IFNULL(XG.dist_name, '-') as from_dist",
                                    "IFNULL(XG.prov_name, '-') as from_prov",
                                    "IFNULL(XG.dept_name, '-') as from_dept",
                                    //TO
                                    "REPLACE(P.person_name, ',', ' ') as to_person_name",
                                    "P.identification_type as to_identification_type",
                                    "P.identification_number as to_identification_number",
                                    "UPPER(WA.address) as to_address",
                                    "WG.dist_name as to_dist",
                                    "WG.prov_name as to_prov",
                                    "WG.dept_name as to_dept",
                                    //Usuario
                                    "U.person_id as user_person_id",
                                    "REPLACE(U.person_name, ',', ' ') as user_person_name",
                                    //
                                    "CONCAT_WS('-', MP2.document_code, MP2.document_serie, MP2.document_correlative) as purchase_order",
                                    "CONCAT_WS('-', MP.document_code, MP.document_serie, MP.document_correlative) as parent_document",
                                    "DATE_FORMAT(MP.emission_date, '%d/%m/%Y') as parent_emission_date",
                                    "M.print_count"
                                ])
                                ->from('warehouse_movement WM')
                                ->join('movement M', 'M.movement_id = WM.movement_id') //Movement
                                ->join('person XP', 'XP.person_id = M.aux_person_id') //Proveedor
                                ->join('organization O', '')
                                ->join('person P', 'P.person_id = O.person_id') //Usuario
                                ->join('scenario S', 'S.route = M.route') //Link al padre
                                ->join('movement_link MLP', "MLP.movement_id = M.movement_id AND MLP.default_ability = '" . ScenarioControl::IS_DISPATCHABLE . "'") //Link a la factura de compra
                                ->join('movement MP', 'MP.movement_id = MLP.movement_parent_id') //Link a la factura de compra
                                ->join('movement_link MLP2', "MLP2.movement_id = MP.movement_id AND MLP2.default_ability = '" . ScenarioControl::IS_UPGRADEABLE . "'") //Link a la orden de compra
                                ->join('movement MP2', 'MP2.movement_id = MLP2.movement_parent_id') //Link a la orden de compra
                                //Hasta (almacén de ingreso)
                                ->join('warehouse W', 'W.warehouse_id = WM.warehouse_id')
                                ->join('address WA', "WA.`owner` = '" . Warehouse::OWNER . "' AND WA.owner_id = WM.warehouse_id")
                                ->join('geoloc WG', 'WG.dept_code = WA.dept_code AND WG.prov_code = WA.prov_code AND WG.dist_code = WA.dist_code')
                                //Desde (domicilio fiscal del proveedor)
                                ->leftJoin('address XA', "XA.`owner` = '" . Person::OWNER . "' AND XA.owner_id = XP.person_id")
                                ->leftJoin('geoloc XG', 'XG.dept_code = XA.dept_code AND XG.prov_code = XA.prov_code AND XG.dist_code = XA.dist_code')
                                //Usuario
                                ->join('person U', 'U.person_id = M.person_id')
                                //
                                ->where('M.movement_id = :movement_id', array(':movement_id' => $id))
                                ->queryRow();
                        break;
                    case 'warehouse/domesticUse':
                        $header = Yii::app()->db->createCommand()
                                ->select([
                                    "M.document_serie",
                                    "M.document_correlative",
                                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as delivery_date",
                                    "S.title as scenario",
                                    "S.direction",
                                    "IF(M.`status`, 'SI', 'NO') as is_active",
                                    'M.observation',
                                    //Usuario
                                    "U.person_id as user_person_id",
                                    "REPLACE(U.person_name, ',', ' ') as user_person_name",
                                    //FROM
                                    "REPLACE(P.person_name, ',', ' ') as from_person_name",
                                    "P.identification_type as from_identification_type",
                                    "P.identification_number as from_identification_number",
                                    "UPPER(WA.address) as from_address",
                                    "WG.dist_name as from_dist",
                                    "WG.prov_name as from_prov",
                                    "WG.dept_name as from_dept",
                                    //TO
                                    "REPLACE(XP.person_name, ',', ' ') as to_person_name",
                                    "XP.identification_type as to_identification_type",
                                    "XP.identification_number as to_identification_number",
                                    "UPPER(WA.address) as to_address",
                                    "WG.dist_name as to_dist",
                                    "WG.prov_name as to_prov",
                                    "WG.dept_name as to_dept",
                                    "M.print_count",
                                    "REPLACE(PC.person_name, ',', ' ') as company_name",
                                    "PC.identification_number as company_identification_number",
                                ])
                                ->from('warehouse_movement WM')
                                ->join('movement M', 'M.movement_id = WM.movement_id')
                                //usuario
                                ->join('person U', 'U.person_id = M.person_id')
                                //Socio de negocio
                                ->join('person XP', 'XP.person_id = M.aux_person_id')
                                //Organización
                                ->join('organization O', '')
                                ->join('person P', 'P.person_id = O.person_id')
                                //Scenario
                                ->join('scenario S', 'S.route = M.route')
                                //Desde/Hacia
                                ->join('warehouse W', 'W.warehouse_id = WM.warehouse_id')
                                ->join('address WA', "WA.`owner` = '" . Warehouse::OWNER . "' AND WA.owner_id = WM.warehouse_id")
                                ->join('geoloc WG', 'WG.dept_code = WA.dept_code AND WG.prov_code = WA.prov_code AND WG.dist_code = WA.dist_code')
                                //Información extra
                                ->leftJoin('extra_info EI', 'EI.movement_id = M.movement_id')
                                ->leftJoin('person PC', "PC.`person_id` = WM.transport_company_id")
                                ->where('M.movement_id = :movement_id', array(':movement_id' => $id))
                                ->queryRow();
                        break;
                    case 'warehouse/civilWorkUse':
                        $header = Yii::app()->db->createCommand()
                                ->select([
                                    "M.document_serie",
                                    "M.document_correlative",
                                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as delivery_date",
                                    "S.title as scenario",
                                    "S.direction",
                                    "IF(M.`status`, 'SI', 'NO') as is_active",
                                    'M.observation',
                                    //Usuario
                                    "U.person_id as user_person_id",
                                    "REPLACE(U.person_name, ',', ' ') as user_person_name",
                                    //FROM
                                    "REPLACE(P.person_name, ',', ' ') as from_person_name",
                                    "P.identification_type as from_identification_type",
                                    "P.identification_number as from_identification_number",
                                    "UPPER(WA.address) as from_address",
                                    "WG.dist_name as from_dist",
                                    "WG.prov_name as from_prov",
                                    "WG.dept_name as from_dept",
                                    //TO
                                    "REPLACE(XP.person_name, ',', ' ') as to_person_name",
                                    "XP.identification_type as to_identification_type",
                                    "XP.identification_number as to_identification_number",
                                    "UPPER(WA.address) as to_address",
                                    "WG.dist_name as to_dist",
                                    "WG.prov_name as to_prov",
                                    "WG.dept_name as to_dept",
                                    "M.print_count",
                                    "REPLACE(PC.person_name, ',', ' ') as company_name",
                                    "PC.identification_number as company_identification_number",
                                    //TO
                                    "REPLACE(PIC.person_name, ',', ' ') as pic_person_name",
                                    "(CASE PIC.identification_type WHEN '1' THEN 'DNI' WHEN '6' THEN 'RUC' ELSE PC.identification_type END) as pic_identification_name",
                                    "PIC.identification_number as pic_identification_number",
                                    "REPLACE(UP.person_name, ',', ' ') as order_person_name",
                                    "CONCAT_WS('-', MP.document_code, MP.document_serie, MP.document_correlative) as parent_document",
                                ])
                                ->from('warehouse_movement WM')
                                ->join('movement M', 'M.movement_id = WM.movement_id')
                                //usuario
                                ->join('person U', 'U.person_id = M.person_id')
                                //Socio de negocio
                                ->join('person XP', 'XP.person_id = M.aux_person_id')
                                //Organización
                                ->join('organization O', '')
                                ->join('person P', 'P.person_id = O.person_id')
                                //Scenario
                                ->join('scenario S', 'S.route = M.route')
                                ->join('movement_link MLP', "MLP.movement_id = M.movement_id AND MLP.default_ability = '" . ScenarioControl::IS_DISPATCHABLE . "'") //Link al padre
                                ->join('movement MP', 'MP.movement_id = MLP.movement_parent_id') //Link al padre
                                ->join('person UP', 'UP.person_id = MP.person_id')
                                //Desde/Hacia
                                ->join('warehouse W', 'W.warehouse_id = WM.warehouse_id')
                                ->join('address WA', "WA.`owner` = '" . Warehouse::OWNER . "' AND WA.owner_id = WM.warehouse_id")
                                ->join('geoloc WG', 'WG.dept_code = WA.dept_code AND WG.prov_code = WA.prov_code AND WG.dist_code = WA.dist_code')
                                ->leftJoin('person PIC', 'PIC.person_id = M.person_in_charge_id')
                                //Información extra
                                ->leftJoin('extra_info EI', 'EI.movement_id = M.movement_id')
                                ->leftJoin('person PC', "PC.`person_id` = WM.transport_company_id")
                                ->where('M.movement_id = :movement_id', array(':movement_id' => $id))
                                ->queryRow();
                        break;
                    case 'warehouse/saleOut':
                    case 'warehouse/productReturnOut':
                        $header = Yii::app()->db->createCommand()
                                ->select([
                                    "M.document_serie",
                                    "M.document_correlative",
                                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date",
                                    "DATE_FORMAT(IFNULL(EI.delivery_date, M.emission_date), '%d/%m/%Y') as delivery_date",
                                    "S.title as scenario",
                                    "S.direction",
                                    "IF(M.`status`, 'SI', 'NO') as is_active",
                                    'M.observation',
                                    'D.document_name as document_name',
                                    //Datos del traslado
                                    'MU.description AS reason_transfer',
                                    'WM.transfer_start_date',
                                    'WM.delivery_carrier_date',
                                    'WM.number_packages',
                                    'WM.total_weight',
                                    'IF(transport_data = 1,\'TRANSPORTE PRIVADO\',\'TRANSPORTE PÚBLICO\') as transport_type',
                                    //Datos del transportista
                                    'PT.identification_type as company_identification_type',
                                    'PT.identification_number as company_identification_number',
                                    'PT.person_name as company_name',
                                    //Conductor
                                    'PS.identification_type as shipper_identification_type',
                                    'PS.identification_number as shipper_identification_number',
                                    "REPLACE(PS.person_name, ',', ' ') as shipper_name",
                                    'SH.license_number',
                                    //Datos de transporte
                                    'UT.plate',
                                    //Destinatario
                                    'XP.identification_number as recipient_identification_number',
                                    "REPLACE(XP.person_name, ',', ' ') as recipient_name",
                                    //Usuario
                                    "U.person_id as user_person_id",
                                    "REPLACE(U.person_name, ',', ' ') as user_person_name",
                                    //FROM
                                    "REPLACE(P.person_name, ',', ' ') as from_person_name",
                                    "P.identification_type as from_identification_type",
                                    "P.identification_number as from_identification_number",
                                    "IF(MA0.address IS NULL, UPPER(WA.address), UPPER(MA0.address)) as from_address",
                                    "IF(MA0.address IS NULL, WG.dist_name, MG0.dist_name) as from_dist",
                                    "IF(MA0.address IS NULL, WG.prov_name, MG0.dist_name) as from_prov",
                                    "IF(MA0.address IS NULL, WG.dept_name, MG0.dist_name) as from_dept",
                                    //TO
                                    "REPLACE(XP.person_name, ',', ' ') as to_person_name",
                                    "XP.identification_type as to_identification_type",
                                    "XP.identification_number as to_identification_number",
                                    "UPPER(IFNULL(MA1.address, IFNULL(XA.address, '-'))) as to_address",
                                    "IFNULL(MG1.dist_name, IFNULL(XG.dist_name, '-')) as to_dist",
                                    "IFNULL(MG1.prov_name, IFNULL(XG.prov_name, '-')) as to_prov",
                                    "IFNULL(MG1.dept_name, IFNULL(XG.dept_name, '-')) as to_dept",
                                    //
                                    "IFNULL(EI.purchase_order, '-') as purchase_order",
                                    "CONCAT_WS('-', MP.document_code, MP.document_serie, MP.document_correlative) as parent_document",
                                    "DATE_FORMAT(MP.emission_date, '%d/%m/%Y') as parent_emission_date",
                                    "REPLACE(PC.person_name, ',', ' ') as company_name",
                                    "PC.identification_number as company_identification_number",
                                    "REPLACE(REG_OD.person_name, ',', ' ') as registrador_name",
                                    "M.print_count"
                                ])
                                ->from('warehouse_movement WM')
                                ->join('movement M', 'M.movement_id = WM.movement_id')
                                //Nombre documento
                                ->join('document D', 'D.document_code = M.document_code')
                                //Socio de negocio
                                ->join('person XP', 'XP.person_id = M.aux_person_id')
                                //Organización
                                ->join('organization O', '')
                                ->join('person P', 'P.person_id = O.person_id')
                                //Usuario
                                ->join('person U', 'U.person_id = M.person_id')
                                //Scenario
                                ->join('scenario S', 'S.route = M.route')
                                ->join('movement_link MLP', "MLP.movement_id = M.movement_id AND MLP.default_ability = '" . ScenarioControl::IS_DISPATCHABLE . "'") //Link al padre
                                ->join('movement MP', 'MP.movement_id = MLP.movement_parent_id') //Link al padre
                                //Desde Almacen
                                ->join('warehouse W', 'W.warehouse_id = WM.warehouse_id')
                                ->join('address WA', "WA.`owner` = '" . Warehouse::OWNER . "' AND WA.owner_id = WM.warehouse_id")
                                ->join('geoloc WG', 'WG.dept_code = WA.dept_code AND WG.prov_code = WA.prov_code AND WG.dist_code = WA.dist_code')
                                //Transportista
                                ->leftjoin('person PT', 'PT.person_id = WM.transport_company_id')
                                //Conductor
                                ->leftJoin('owner_pair OPPSH', "OPPSH.type = '" . OwnerPair::TYPE_MOVEMENT_SHIPPER . "' AND OPPSH.`owner` = '" . Movement::OWNER . "' AND OPPSH.owner_id = M.movement_id")
                                ->leftJoin('owner_pair OPSH', "OPSH.type = OPPSH.type AND OPSH.`owner` = '" . Shipper::OWNER . "' AND OPSH.parent_id = OPPSH.pair_id AND OPSH.order = 1")
                                ->leftJoin('shipper SH', "SH.shipper_id = OPSH.owner_id")
                                ->leftJoin('person PS', "PS.person_id = SH.person_id")
                                //Transporte
                                ->leftJoin('owner_pair OPPUT', "OPPUT.type = '" . OwnerPair::TYPE_MOVEMENT_TRANSPORT_UNIT . "' AND OPPUT.`owner` = '" . Movement::OWNER . "' AND OPPUT.owner_id = M.movement_id")
                                ->leftJoin('owner_pair OPUT', "OPUT.type = OPPSH.type AND OPUT.`owner` = '" . TransportUnit::OWNER . "' AND OPUT.parent_id = OPPUT.pair_id AND OPUT.order = 1")
                                ->leftJoin('transport_unit UT', "UT.transport_unit_id = OPUT.owner_id")
                                //Desripción
                                ->leftJoin('multitable MU', 'MU.multi_id = WM.reason_transfer_id')
                                //Dirección 0
                                ->leftJoin('address MA0', "MA0.`owner` = '" . Movement::OWNER . "' AND MA0.owner_id = WM.movement_id AND MA0.order = 0")
                                ->leftJoin('geoloc MG0', 'MG0.dept_code = MA0.dept_code AND MG0.prov_code = MA0.prov_code AND MG0.dist_code = MA0.dist_code')
                                //Dirección 1
                                ->leftJoin('address MA1', "MA1.`owner` = '" . Movement::OWNER . "' AND MA1.owner_id = WM.movement_id AND MA1.order = 1")
                                ->leftJoin('geoloc MG1', 'MG1.dept_code = MA1.dept_code AND MG1.prov_code = MA1.prov_code AND MG1.dist_code = MA1.dist_code')
                                //Domicilio fiscal
                                ->leftJoin('address XA', "XA.`owner` = '" . Person::OWNER . "' AND XA.owner_id = XP.person_id")
                                ->leftJoin('geoloc XG', 'XG.dept_code = XA.dept_code AND XG.prov_code = XA.prov_code AND XG.dist_code = XA.dist_code')
                                //Información extra
                                ->leftJoin('extra_info EI', 'EI.movement_id = M.movement_id')
                                ->leftJoin('person PC', "PC.`person_id` = WM.transport_company_id")
                                //Despachador
                                ->leftJoin('person REG_OD', 'REG_OD.person_id = MP.person_id') // Registrador de la OD                              
                                ->where('M.movement_id = :movement_id', array(':movement_id' => $id))
                                ->queryRow();
                        break;
                    case 'warehouse/transferenceIn':
                        $header = Yii::app()->db->createCommand()
                                ->select([
                                    "M.document_serie",
                                    "M.document_correlative",
                                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                                    "S.title as scenario",
                                    "S.direction",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date",
                                    "DATE_FORMAT(MP.emission_date, '%d/%m/%Y') as delivery_date",
                                    "U.person_id as user_person_id",
                                    "REPLACE(U.person_name, ',', ' ') as user_person_name",
                                    "IF(M.`status`, 'SI', 'NO') as is_active",
                                    'M.observation',
                                    //FROM
                                    "REPLACE(P.person_name, ',', ' ') as from_person_name",
                                    "P.identification_type as from_identification_type",
                                    "P.identification_number as from_identification_number",
                                    "WO.warehouse_name as from_warehouse_name",
                                    "UPPER(WOA.address) as from_address",
                                    "WOG.dist_name as from_dist",
                                    "WOG.prov_name as from_prov",
                                    "WOG.dept_name as from_dept",
                                    //TO
                                    "REPLACE(P.person_name, ',', ' ') as to_person_name",
                                    "P.identification_type as to_identification_type",
                                    "P.identification_number as to_identification_number",
                                    "WI.warehouse_name as to_warehouse_name",
                                    "UPPER(WIA.address) as to_address",
                                    "WIG.dist_name as to_dist",
                                    "WIG.prov_name as to_prov",
                                    "WIG.dept_name as to_dept",
                                    //Transferencia de salida
                                    "CONCAT_WS('-', MP.document_code, MP.document_serie, MP.document_correlative) as parent_document",
                                    "M.print_count",
                                    "REPLACE(PC.person_name, ',', ' ') as company_name",
                                    "PC.identification_number as company_identification_number",
                                ])
                                ->from('warehouse_movement WM')
                                ->join('movement M', 'M.movement_id = WM.movement_id')
                                //Organización
                                ->join('organization O', '')
                                ->join('person P', 'P.person_id = O.person_id')
                                //Usuario
                                ->join('person U', 'U.person_id = M.person_id')
                                //Scenario
                                ->join('scenario S', 'S.route = M.route')
                                //Desde
                                ->join('warehouse WI', 'WI.warehouse_id = WM.warehouse_id')
                                ->join('address WIA', "WIA.`owner` = '" . Warehouse::OWNER . "' AND WIA.owner_id = WI.warehouse_id")
                                ->join('geoloc WIG', 'WIG.dept_code = WIA.dept_code AND WIG.prov_code = WIA.prov_code AND WIG.dist_code = WIA.dist_code')
                                //Hasta
                                ->join('movement_link ML', "ML.movement_id = M.movement_id AND ML.parent_route = 'warehouse/transferenceOut'")
                                ->join('movement MP', 'MP.movement_id = ML.movement_parent_id')
                                ->join('warehouse_movement WMP', 'WMP.movement_id = MP.movement_id')
                                ->join('warehouse WO', 'WO.warehouse_id = WMP.warehouse_id')
                                ->join('address WOA', "WOA.`owner` = '" . Warehouse::OWNER . "' AND WOA.owner_id = WO.warehouse_id")
                                ->join('geoloc WOG', 'WOG.dept_code = WOA.dept_code AND WOG.prov_code = WOA.prov_code AND WOG.dist_code = WOA.dist_code')
                                //Domicilio fiscal
                                //Información extra
                                ->leftJoin('extra_info EI', 'EI.movement_id = M.movement_id')
                                ->leftJoin('person PC', "PC.`person_id` = WM.transport_company_id")
                                ->where('M.movement_id = :movement_id', array(':movement_id' => $id))
                                ->queryRow();
                        break;
                    case 'warehouse/transferenceOut':
                        $header = Yii::app()->db->createCommand()
                                ->select([
                                    "M.document_serie",
                                    "M.document_correlative",
                                    "D.document_name AS document_name",
                                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date",
                                    "DATE_FORMAT(IFNULL(EI.delivery_date, M.emission_date), '%d/%m/%Y') as delivery_date",
                                    "S.title as scenario",
                                    "S.direction",
                                    "IF(M.`status`, 'SI', 'NO') as is_active",
                                    'M.observation',
                                    //Datos de traslado
                                    'WM.transfer_start_date',
                                    'WM.delivery_carrier_date',
                                    'WM.number_packages',
                                    'WM.total_weight',
                                    'MU.description AS reason_transfer',
                                    'IF(transport_data = 1,\'TRANSPORTE PRIVADO\',\'TRANSPORTE PÚBLICO\') as transport_type',
                                    //Datos del transportista
                                    'PT.identification_type as company_identification_type',
                                    'PT.identification_number as company_identification_number',
                                    'PT.person_name as company_name',
                                    //Conductor
                                    'PS.identification_type as shipper_identification_type',
                                    'PS.identification_number as shipper_identification_number',
                                    "REPLACE(PS.person_name, ',', ' ') as shipper_name",
                                    'SH.license_number',
                                    //Datos de transporte
                                    'UT.plate',
                                    //Usuario
                                    "U.person_id as user_person_id",
                                    "REPLACE(U.person_name, ',', ' ') as user_person_name",
                                    //FROM
                                    "REPLACE(P.person_name, ',', ' ') as from_person_name",
                                    "P.identification_type as from_identification_type",
                                    "P.identification_number as from_identification_number",
                                    "WO.warehouse_name as from_warehouse_name",
                                    "UPPER(WOA.address) as from_address",
                                    "WOG.dist_name as from_dist",
                                    "WOG.prov_name as from_prov",
                                    "WOG.dept_name as from_dept",
                                    //TO
                                    "REPLACE(P.person_name, ',', ' ') as to_person_name",
                                    "P.identification_type as to_identification_type",
                                    "P.identification_number as to_identification_number",
                                    "WI.warehouse_name as to_warehouse_name",
                                    //Address
                                    "UPPER(IF(M.aux_person_id = O.person_id, WIA.address, IF(TOA.owner_id IS NOT NULL, TOA.address, XPA.address))) as to_address",
                                    "IF(M.aux_person_id = O.person_id, WIG.dist_name, IF(TOA.owner_id IS NOT NULL, TOG.dist_name, XPG.dist_name)) as to_dist",
                                    "IF(M.aux_person_id = O.person_id, WIG.prov_name, IF(TOA.owner_id IS NOT NULL, TOG.prov_name, XPG.prov_name)) as to_prov",
                                    "IF(M.aux_person_id = O.person_id, WIG.dept_name, IF(TOA.owner_id IS NOT NULL, TOG.dept_name, XPG.dept_name)) as to_dept",
                                    "M.print_count",
                                    "REPLACE(PC.person_name, ',', ' ') as company_name",
                                    "PC.identification_number as company_identification_number",
                                ])
                                ->from('warehouse_movement WM')
                                ->join('movement M', 'M.movement_id = WM.movement_id')
                                //Organización
                                ->join('organization O', '')
                                //Desripción
                                ->leftJoin('multitable MU', 'MU.multi_id = WM.reason_transfer_id')
                                //Nombre documento
                                ->join('document D', 'D.document_code = M.document_code')
                                //Socio de Negocio
                                ->join('person P', 'P.person_id = M.aux_person_id')
                                ->join('scenario S', 'S.route = M.route')
                                //Usuario
                                ->join('person U', 'U.person_id = M.person_id')
                                //Desde
                                ->join('warehouse WO', 'WO.warehouse_id = WM.warehouse_id')
                                ->join('address WOA', "WOA.`owner` = '" . Warehouse::OWNER . "' AND WOA.owner_id = WO.warehouse_id")
                                ->join('geoloc WOG', 'WOG.dept_code = WOA.dept_code AND WOG.prov_code = WOA.prov_code AND WOG.dist_code = WOA.dist_code')
                                //Hasta
                                ->join('extra_info EI', 'EI.movement_id = M.movement_id')
                                ->join('warehouse WI', 'WI.warehouse_id = EI.warehouse_id')
                                //Transportista
                                ->leftjoin('person PT', 'PT.person_id = WM.transport_company_id')
                                //Conductor
                                ->leftJoin('owner_pair OPPSH', "OPPSH.type = '" . OwnerPair::TYPE_MOVEMENT_SHIPPER . "' AND OPPSH.`owner` = '" . Movement::OWNER . "' AND OPPSH.owner_id = M.movement_id")
                                ->leftJoin('owner_pair OPSH', "OPSH.type = OPPSH.type AND OPSH.`owner` = '" . Shipper::OWNER . "' AND OPSH.parent_id = OPPSH.pair_id AND OPSH.order = 1")
                                ->leftJoin('shipper SH', "SH.shipper_id = OPSH.owner_id")
                                ->leftJoin('person PS', "PS.person_id = SH.person_id")
                                //Transporte
                                ->leftJoin('owner_pair OPPUT', "OPPUT.type = '" . OwnerPair::TYPE_MOVEMENT_TRANSPORT_UNIT . "' AND OPPUT.`owner` = '" . Movement::OWNER . "' AND OPPUT.owner_id = M.movement_id")
                                ->leftJoin('owner_pair OPUT', "OPUT.type = OPPSH.type AND OPUT.`owner` = '" . TransportUnit::OWNER . "' AND OPUT.parent_id = OPPUT.pair_id AND OPUT.order = 1")
                                ->leftJoin('transport_unit UT', "UT.transport_unit_id = OPUT.owner_id")
                                //WarehouseIn Address 
                                ->join('address WIA', "WIA.`owner` = '" . Warehouse::OWNER . "' AND WIA.owner_id = WI.warehouse_id and WIA.order = 1")
                                ->join('geoloc WIG', 'WIG.dept_code = WIA.dept_code AND WIG.prov_code = WIA.prov_code AND WIG.dist_code = WIA.dist_code')
                                //AuxPerson Address                            
                                ->leftjoin('address XPA', "XPA.`owner` = '" . Person::OWNER . "' AND XPA.owner_id = M.aux_person_id and XPA.order = 1")
                                ->leftjoin('geoloc XPG', 'XPG.dept_code = XPA.dept_code AND XPG.prov_code = XPA.prov_code AND XPG.dist_code = XPA.dist_code')
                                //TransferenceOut Address                            
                                ->leftjoin('address TOA', "TOA.`owner` = '" . Movement::OWNER . "' AND TOA.owner_id = M.movement_id  and TOA.order = 1")
                                ->leftjoin('geoloc TOG', 'TOG.dept_code = TOA.dept_code AND TOG.prov_code = TOA.prov_code AND TOG.dist_code = TOA.dist_code')
                                //Información extra                                
                                ->leftJoin('person PC', "PC.`person_id` = WM.transport_company_id")
                                ->where('M.movement_id = :movement_id', array(':movement_id' => $id))
                                ->queryRow();
                        break;
                    default:
                        $header = Yii::app()->db->createCommand()
                                ->select([
                                    "M.document_serie",
                                    "M.document_correlative",
                                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                                    "S.title as scenario",
                                    "S.direction",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as delivery_date",
                                    "IF(M.`status`, 'SI', 'NO') as is_active",
                                    'M.observation',
                                    //FROM
                                    "REPLACE(P.person_name, ',', ' ') as from_person_name",
                                    "P.identification_type as from_identification_type",
                                    "P.identification_number as from_identification_number",
                                    "UPPER(WA.address) as from_address",
                                    "WG.dist_name as from_dist",
                                    "WG.prov_name as from_prov",
                                    "WG.dept_name as from_dept",
                                    //TO
                                    "REPLACE(P.person_name, ',', ' ') as to_person_name",
                                    "P.identification_type as to_identification_type",
                                    "P.identification_number as to_identification_number",
                                    "UPPER(WA.address) as to_address",
                                    "WG.dist_name as to_dist",
                                    "WG.prov_name as to_prov",
                                    "WG.dept_name as to_dept",
                                    //Usuario
                                    "U.person_id as user_person_id",
                                    "REPLACE(U.person_name, ',', ' ') as user_person_name",
                                    "M.print_count",
                                    "REPLACE(PC.person_name, ',', ' ') as company_name",
                                    "PC.identification_number as company_identification_number",
                                ])
                                ->from('warehouse_movement WM')
                                ->join('movement M', 'M.movement_id = WM.movement_id')
                                //Organización
                                ->join('organization O', '')
                                ->join('person P', 'P.person_id = O.person_id')
                                //Usuario
                                ->join('person U', 'U.person_id = M.person_id')
                                //Escenario
                                ->join('scenario S', 'S.route = M.route')
                                //Desde/Hasta
                                ->join('warehouse W', 'W.warehouse_id = WM.warehouse_id')
                                ->join('address WA', "WA.`owner` = '" . Warehouse::OWNER . "' AND WA.owner_id = WM.warehouse_id")
                                ->join('geoloc WG', 'WG.dept_code = WA.dept_code AND WG.prov_code = WA.prov_code AND WG.dist_code = WA.dist_code')
                                //Información extra
                                ->leftJoin('person PC', "PC.`person_id` = WM.transport_company_id")
                                ->where('M.movement_id = :movement_id', array(':movement_id' => $id))
                                ->queryRow();
                        break;
                }
                $header['from_identification_name'] = USIdentificationValidator::getLabel($header['from_identification_type']);
                $header['to_identification_name'] = USIdentificationValidator::getLabel($header['to_identification_type']);
                $header['current_datetime'] = SIANTime::formatDateTime();

                //DETALLE
                switch ("{$this->module->id}/{$this->id}") {
                    case 'warehouse/buyIn':
                        $a_detail = Yii::app()->db->createCommand()
                                ->select([
                                    "IW.product_id",
                                    "PR.measure_name",
                                    "IC.pres_quantity as sale_quantity",
                                    "IW.pres_quantity as dispatch_quantity",
                                    "ROUND(IC.unit_balance / PR.equivalence, 1) as rest_quantity",
                                    "UPPER(IW.product_name) as product_name",
                                    "GROUP_CONCAT(WS.serie SEPARATOR ', ') as series",
                                    "IW.allow_decimals",
                                    "IW.item_number"
                                ])
                                ->from('item IW')
                                ->join('presentation PR', 'PR.product_id = IW.product_id AND PR.equivalence = IW.equivalence')
                                ->join('item_link IL', "IL.item_id = IW.item_id AND IL.unit_quantity > 0") //Link al padre
                                ->join('item IC', "IC.item_id = IL.parent_item_id")
                                ->leftJoin('warehouse_serie WS', 'WS.item_id = IW.item_id')
                                ->where('IW.movement_id = :movement_id', [
                                    ':movement_id' => $id
                                ])
                                ->group('IW.item_id')
                                ->order("IW.item_number ASC")
                                ->queryAll();
                        break;
                    case 'warehouse/saleOut':

                        if ($print_mode == self::PRINT_MODE_GROUPED) {
                            $s_subquery = (new DpIntegralGroups)->setAttributes([
                                        'movement_id' => $id
                                    ])->getSql();
                        } else {
                            $s_subquery = (new DpIntegralGroups)->setAttributes([
                                        'movement_id' => $id
                                    ])->getNullSql();
                        }

                        $a_detail = Yii::app()->db->createCommand(
                                        "
                                    SELECT 
                                        IF(X.item_id IS NULL, IW.item_id, X.item_id) AS item_id,
                                        IF(X.item_id IS NULL, IW.product_id, '---') AS product_id,
                                        IF(X.item_id IS NULL, PR.measure_name, X.measure_name) AS measure_name,
                                        IF(X.item_id IS NULL, IC.pres_quantity, X.sale_quantity) AS sale_quantity,
                                        IF(X.item_id IS NULL, IW.pres_quantity, X.dispatch_quantity) AS dispatch_quantity,
                                        ROUND(IF(X.item_id IS NULL, IC.unit_balance, X.rest_quantity) / PR.equivalence, 1) AS rest_quantity,
                                        UPPER(IF(X.item_id IS NULL, IW.product_name, X.product_name)) as product_name, 
                                        IW.allow_decimals,
                                        GROUP_CONCAT(WS.serie SEPARATOR ', ') as series, 
                                        IF(X.item_id IS NULL, IW.item_number, MAX(X.item_number)) AS item_number
                                FROM item IW
                                JOIN presentation PR ON PR.product_id = IW.product_id AND PR.equivalence = IW.equivalence
                                JOIN item_link IL ON IL.item_id = IW.item_id AND IL.unit_quantity > 0
                                JOIN item IC ON IC.item_id = IL.parent_item_id
                                LEFT JOIN (
                                    {$s_subquery}
                                ) X ON FIND_IN_SET(IW.item_id, X.item_ids)
                                LEFT JOIN warehouse_serie WS ON WS.item_id = IW.item_id
                                WHERE IW.movement_id = :movement_id
                                GROUP BY 1
                                ORDER BY IW.item_number ASC"
                                )->bindValues([
                                    ':movement_id' => $id
                                ])->queryAll();

                        break;
                    default:
                        $a_detail = Yii::app()->db->createCommand()
                                ->select([
                                    "IW.product_id",
                                    "PR.measure_name",
                                    "IW.pres_quantity as sale_quantity",
                                    "IW.pres_quantity as dispatch_quantity",
                                    "(0) as rest_quantity",
                                    "UPPER(IW.product_name) as product_name",
                                    "IW.allow_decimals",
                                    "GROUP_CONCAT(WS.serie SEPARATOR ', ') as series",
                                    "IW.item_number"
                                ])
                                ->from('item IW')
                                ->join('presentation PR', 'PR.product_id = IW.product_id AND PR.equivalence = IW.equivalence')
                                ->leftJoin('warehouse_serie WS', 'WS.item_id = IW.item_id')
                                ->where('IW.movement_id = :movement_id', array(':movement_id' => $id))
                                ->group('IW.item_id')
                                ->order("IW.item_number ASC")
                                ->queryAll();
                        break;
                }
                break;
            //formato impresión despacho proyecto
            case self::F_DESPACHO_PROYECTO:
                //HEADER
                switch ("{$this->module->id}/{$this->id}") {
                    case 'warehouse/civilWorkUse':
                        $header = Yii::app()->db->createCommand()
                                ->select([
                                    "M.document_serie",
                                    "M.document_correlative",
                                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date",
                                    "DATE_FORMAT(IFNULL(EI.delivery_date, MP.emission_date), '%d/%m/%Y') as delivery_date",
                                    "S.title as scenario",
                                    "S.direction",
                                    "IF(M.`status`, 'SI', 'NO') as is_active",
                                    'M.observation',
                                    'D.document_name as document_name',
                                    //Datos del traslado
                                    'MU.description AS reason_transfer',
                                    'WM.transfer_start_date',
                                    'WM.delivery_carrier_date',
                                    'WM.number_packages',
                                    'WM.total_weight',
                                    'IF(transport_data = 1,\'TRANSPORTE PRIVADO\',\'TRANSPORTE PÚBLICO\') as transport_type',
                                    //Datos del transportista
                                    'PT.identification_type as company_identification_type',
                                    'PT.identification_number as company_identification_number',
                                    'PT.person_name as company_name',
                                    //Conductor
                                    'PS.identification_type as shipper_identification_type',
                                    'PS.identification_number as shipper_identification_number',
                                    "REPLACE(PS.person_name, ',', ' ') as shipper_name",
                                    'SH.license_number',
                                    //Datos de transporte
                                    'UT.plate',
                                    //Destinatario
                                    'XP.identification_number as recipient_identification_number',
                                    "REPLACE(XP.person_name, ',', ' ') as recipient_name",
                                    //Usuario
                                    "U.person_id as user_person_id",
                                    "REPLACE(U.person_name, ',', ' ') as user_person_name",
                                    //Almacén de Salida
                                    "W.warehouse_name as warehouse_name",
                                    //Entregado a 
                                    'CP.identification_type as person_in_charge_type',
                                    'CP.identification_number as person_in_charge_number',
                                    "REPLACE(CP.person_name, ',', ' ') as person_in_charge_name",
                                    //Proyecto
                                    "PJ.short_name as short_name",
                                    //FROM
                                    "REPLACE(P.person_name, ',', ' ') as from_person_name",
                                    "P.identification_type as from_identification_type",
                                    "P.identification_number as from_identification_number",
                                    "IF(MA0.address IS NULL, UPPER(WA.address), UPPER(MA0.address)) as from_address",
                                    "IF(MA0.address IS NULL, WG.dist_name, MG0.dist_name) as from_dist",
                                    "IF(MA0.address IS NULL, WG.prov_name, MG0.dist_name) as from_prov",
                                    "IF(MA0.address IS NULL, WG.dept_name, MG0.dist_name) as from_dept",
                                    //TO
                                    "REPLACE(XP.person_name, ',', ' ') as to_person_name",
                                    "XP.identification_type as to_identification_type",
                                    "XP.identification_number as to_identification_number",
                                    "UPPER(IFNULL(MA1.address, IFNULL(XA.address, '-'))) as to_address",
                                    "IFNULL(MG1.dist_name, IFNULL(XG.dist_name, '-')) as to_dist",
                                    "IFNULL(MG1.prov_name, IFNULL(XG.prov_name, '-')) as to_prov",
                                    "IFNULL(MG1.dept_name, IFNULL(XG.dept_name, '-')) as to_dept",
                                    //
                                    "IFNULL(EI.purchase_order, '-') as purchase_order",
                                    "CONCAT_WS('-', MP.document_code, MP.document_serie, MP.document_correlative) as parent_document",
                                    "DATE_FORMAT(MP.emission_date, '%d/%m/%Y') as parent_emission_date",
                                    "REPLACE(PC.person_name, ',', ' ') as company_name",
                                    "PC.identification_number as company_identification_number",
                                    "M.print_count"
                                ])
                                ->from('warehouse_movement WM')
                                ->join('movement M', 'M.movement_id = WM.movement_id')
                                //Nombre documento
                                ->join('document D', 'D.document_code = M.document_code')
                                //Socio de negocio
                                ->join('person XP', 'XP.person_id = M.aux_person_id')
                                //Entregado a
                                ->join('person CP', 'CP.person_id = M.person_in_charge_id')
                                //Organización
                                ->join('organization O', '')
                                ->join('person P', 'P.person_id = O.person_id')
                                //Usuario
                                ->join('person U', 'U.person_id = M.person_id')
                                //Scenario
                                ->join('scenario S', 'S.route = M.route')
                                ->join('movement_link MLP', "MLP.movement_id = M.movement_id AND MLP.default_ability = '" . ScenarioControl::IS_DISPATCHABLE . "'") //Link al padre
                                ->join('movement MP', 'MP.movement_id = MLP.movement_parent_id') //Link al padre
                                //Desde Almacen
                                ->join('warehouse W', 'W.warehouse_id = WM.warehouse_id')
                                ->join('address WA', "WA.`owner` = '" . Warehouse::OWNER . "' AND WA.owner_id = WM.warehouse_id")
                                ->join('geoloc WG', 'WG.dept_code = WA.dept_code AND WG.prov_code = WA.prov_code AND WG.dist_code = WA.dist_code')
                                //Proyecto
                                ->join('project PJ', 'PJ.project_id = M.project_id')
                                //Transportista
                                ->leftjoin('person PT', 'PT.person_id = WM.transport_company_id')
                                //Conductor
                                ->leftJoin('owner_pair OPPSH', "OPPSH.type = '" . OwnerPair::TYPE_MOVEMENT_SHIPPER . "' AND OPPSH.`owner` = '" . Movement::OWNER . "' AND OPPSH.owner_id = M.movement_id")
                                ->leftJoin('owner_pair OPSH', "OPSH.type = OPPSH.type AND OPSH.`owner` = '" . Shipper::OWNER . "' AND OPSH.parent_id = OPPSH.pair_id AND OPSH.order = 1")
                                ->leftJoin('shipper SH', "SH.shipper_id = OPSH.owner_id")
                                ->leftJoin('person PS', "PS.person_id = SH.person_id")
                                //Transporte
                                ->leftJoin('owner_pair OPPUT', "OPPUT.type = '" . OwnerPair::TYPE_MOVEMENT_TRANSPORT_UNIT . "' AND OPPUT.`owner` = '" . Movement::OWNER . "' AND OPPUT.owner_id = M.movement_id")
                                ->leftJoin('owner_pair OPUT', "OPUT.type = OPPSH.type AND OPUT.`owner` = '" . TransportUnit::OWNER . "' AND OPUT.parent_id = OPPUT.pair_id AND OPUT.order = 1")
                                ->leftJoin('transport_unit UT', "UT.transport_unit_id = OPUT.owner_id")
                                //Desripción
                                ->leftJoin('multitable MU', 'MU.multi_id = WM.reason_transfer_id')
                                //Dirección 0
                                ->leftJoin('address MA0', "MA0.`owner` = '" . Movement::OWNER . "' AND MA0.owner_id = WM.movement_id AND MA0.order = 0")
                                ->leftJoin('geoloc MG0', 'MG0.dept_code = MA0.dept_code AND MG0.prov_code = MA0.prov_code AND MG0.dist_code = MA0.dist_code')
                                //Dirección 1
                                ->leftJoin('address MA1', "MA1.`owner` = '" . Movement::OWNER . "' AND MA1.owner_id = WM.movement_id AND MA1.order = 1")
                                ->leftJoin('geoloc MG1', 'MG1.dept_code = MA1.dept_code AND MG1.prov_code = MA1.prov_code AND MG1.dist_code = MA1.dist_code')
                                //Domicilio fiscal
                                ->leftJoin('address XA', "XA.`owner` = '" . Person::OWNER . "' AND XA.owner_id = XP.person_id")
                                ->leftJoin('geoloc XG', 'XG.dept_code = XA.dept_code AND XG.prov_code = XA.prov_code AND XG.dist_code = XA.dist_code')
                                //Información extra
                                ->leftJoin('extra_info EI', 'EI.movement_id = MP.movement_id')
                                ->leftJoin('person PC', "PC.`person_id` = WM.transport_company_id")
                                ->where('M.movement_id = :movement_id', array(':movement_id' => $id))
                                ->queryRow();
                        //detalle
                        if ($print_mode == self::PRINT_MODE_GROUPED) {
                            $s_subquery = (new DpIntegralGroups)->setAttributes([
                                        'movement_id' => $id
                                    ])->getSql();
                        } else {
                            $s_subquery = (new DpIntegralGroups)->setAttributes([
                                        'movement_id' => $id
                                    ])->getNullSql();
                        }

                        $a_detail = Yii::app()->db->createCommand(
                                        "
                                    SELECT 
                                        IF(X.item_id IS NULL, IW.item_id, X.item_id) AS item_id,
                                        IF(X.item_id IS NULL, IW.product_id, '---') AS product_id,
                                        IF(X.item_id IS NULL, PR.measure_name, X.measure_name) AS measure_name,
                                        IF(X.item_id IS NULL, IC.pres_quantity, X.sale_quantity) AS sale_quantity,
                                        IF(X.item_id IS NULL, IW.pres_quantity, X.dispatch_quantity) AS dispatch_quantity,
                                        ROUND(IF(X.item_id IS NULL, IC.unit_balance, X.rest_quantity) / PR.equivalence, 1) AS rest_quantity,
                                        UPPER(IF(X.item_id IS NULL, IW.product_name, X.product_name)) as product_name, 
                                        IW.allow_decimals,
                                        GROUP_CONCAT(WS.serie SEPARATOR ', ') as series, 
                                        IF(X.item_id IS NULL, IW.item_number, MAX(X.item_number)) AS item_number
                                FROM item IW
                                JOIN presentation PR ON PR.product_id = IW.product_id AND PR.equivalence = IW.equivalence
                                JOIN item_link IL ON IL.item_id = IW.item_id AND IL.unit_quantity > 0
                                JOIN item IC ON IC.item_id = IL.parent_item_id
                                LEFT JOIN (
                                    {$s_subquery}
                                ) X ON FIND_IN_SET(IW.item_id, X.item_ids)
                                LEFT JOIN warehouse_serie WS ON WS.item_id = IW.item_id
                                WHERE IW.movement_id = :movement_id
                                GROUP BY 1
                                ORDER BY IW.item_number ASC"
                                )->bindValues([
                                    ':movement_id' => $id
                                ])->queryAll();
                        $header['from_identification_name'] = USIdentificationValidator::getLabel($header['from_identification_type']);
                        $header['to_identification_name'] = USIdentificationValidator::getLabel($header['to_identification_type']);
                        $header['current_datetime'] = SIANTime::formatDateTime();
                        break;
                }
                break;
            case self::F_ORDEN_VENTA:
            case self::F_COTIZACION_VENTA:
            case self::F_BOLETA:
            case self::F_BOLETA_02:
            case self::F_BOLETA_10:
            case self::F_FACTURA:
            case self::F_FACTURA_08:
            case self::F_FACTURA_01:
            case self::F_FACTURA_03:
            case self::F_FACTURA_09:
            case self::F_FACTURA_10:
            case self::F_FELECTRONICA:
            case self::F_RECIBO_INGRESO:
                $header = Yii::app()->db->createCommand()->select([
                            "ST.store_name",
                            "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) AS document",
                            "M.document_serie, M.document_correlative, REPLACE(XP.person_name, ',', ' ') AS aux_person_name",
                            "XP.identification_number",
                            "XPP.phone_number",
                            "IF(XPA.address IS NOT NULL, CONCAT(XPA.address, ' (', XPG.dist_name, ' - ', XPG.prov_name, ' - ', XPG.dept_name,')'), '-') AS address",
                            "IF(MA.address IS NOT NULL, CONCAT(MA.address, ' (', MG.dist_name, ' - ', MG.prov_name, ' - ', MG.dept_name,')'), '-') AS delivery_address",
                            "MA.reference as delivery_reference",
                            "IFNULL(PP.person_id, P.person_id) AS person_id",
                            "REPLACE(COALESCE(SPP.person_name, PP.person_name, P.person_name), ',', ' ') AS person_name",
                            "COALESCE(SP.phone_type,VP.phone_type, PPP.phone_type) AS phone_type_seller",
                            "COALESCE(SP.phone_number, VP.phone_number, PPP.phone_number) AS phone_number_seller",
                            "STP.description AS seller_type_name",
                            "TRIM(LEADING '0' FROM MP.document_correlative) AS parent_correlative",
                            "CONCAT_WS('-', MP.document_code, MP.document_serie, MP.document_correlative) AS parent_document",
                            "UPPER(CASE CM.`condition` WHEN '" . Condition::CREDIT . "' THEN CONCAT(CM.`condition`, ' DE ', CM.credit_days, ' DIAS') ELSE '" . Condition::COUNTED . "' END) AS `condition`",
                            "M.currency",
                            "M.digest_value",
                            "DATE_FORMAT(M.emission_date, '%d/%m/%Y') AS emission_date",
                            "DATE_FORMAT(M.expiration_date, '%d/%m/%Y') AS expiration_date",
                            "M.observation",
                            "EI.purchase_order",
                            "DATE_FORMAT(M.emission_date, '%d') AS day",
                            "DATE_FORMAT(M.emission_date, '%m') AS month",
                            "DATE_FORMAT(M.emission_date, '%Y') AS year",
                            "DATE_FORMAT(M.emission_date, '%y') AS short_year",
                            "RIGHT(DATE_FORMAT(M.emission_date, '%y'), 1) AS onedigit_year",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.affected_pen, CM.affected_usd), 2) AS affected",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.inaffected_pen, CM.inaffected_usd), 2) AS inaffected",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.nobill_pen, CM.nobill_usd), 2) AS nobill",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.export_pen, CM.export_usd), 2) AS export",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.free_pen, CM.free_usd), 2) AS free",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.net_pen, CM.net_usd), 2) AS net",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.igv_pen, CM.igv_usd), 2) AS igv",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.total_pen, CM.total_usd), 2) AS total",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.perception_pen, CM.perception_usd), 2) AS perception",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.real_pen, CM.real_usd), 2) AS `real`",
                            "M.print_count",
                            "(M.expiration_date - M.emission_date) + 1 AS days",
                            "EI.movement_name",
                            "CM.sales_specifications"
                        ])
                        ->from('commercial_movement CM')
                        ->join('movement M', 'M.movement_id = CM.movement_id') //Movement
                        ->join('person XP', 'XP.person_id = M.aux_person_id') //Socio de negocio
                        ->join('person P', 'P.person_id = M.person_id') //Facturador
                        ->leftJoin('movement_link MLP', "MLP.movement_id = M.movement_id") //Link al padre
                        ->leftJoin('movement MP', 'MP.movement_id = MLP.movement_parent_id') //Link al padre
                        ->leftJoin('person PP', 'PP.person_id = MP.person_id') //Usuario de OV como Vendedor 
                        ->leftJoin('seller S', 'S.seller_id = CM.seller_id') //Vendedor 
                        ->leftJoin('person SPP', 'SPP.person_id = S.person_id') //Vendedor 
                        ->leftJoin('multitable STP', 'STP.multi_id = S.seller_type_id') //Vendedor
                        ->leftJoin('phone XPP', "XPP.owner = '" . Person::OWNER . "' and XPP.owner_id = XP.person_id and XPP.`order` = 1") //Phone-SocioNegocio
                        ->leftJoin('phone PPP', "PPP.owner = '" . Person::OWNER . "' and PPP.owner_id = P.person_id and PPP.`order` = 1") //Phone-Facturador
                        ->leftJoin('phone VP', "VP.owner = '" . Person::OWNER . "' and VP.owner_id = PP.person_id and VP.`order` = 1") //Phone-Vendedor
                        ->leftJoin('phone SP', "SP.owner = '" . Person::OWNER . "' and SP.owner_id = SPP.person_id and SP.`order` = 1") //Phone-Vendedor
                        ->leftJoin('address XPA', "XPA.owner = '" . Person::OWNER . "' and XPA.owner_id = XP.person_id and XPA.`order` = 1") //Domicilio fiscal
                        ->leftJoin('geoloc XPG', "XPG.dept_code = XPA.dept_code and XPG.prov_code = XPA.prov_code and XPG.dist_code = XPA.dist_code") //Domicilio fiscal
                        ->leftJoin('address MA', "MA.owner = '" . Movement::OWNER . "' and MA.owner_id = M.movement_id and MA.`order` = 1") //Dirección  de Entrega
                        ->leftJoin('geoloc MG', "MG.dept_code = MA.dept_code and MG.prov_code = MA.prov_code and MG.dist_code = MA.dist_code") //Dirección de entrega
                        ->leftJoin('extra_info EI', 'EI.movement_id = M.movement_id')
                        ->leftJoin('store ST', 'ST.store_id = M.store_id')
                        ->where('M.movement_id = :movement_id', array(':movement_id' => $id))
                        ->group('M.movement_id')
                        ->queryRow();
                //
                $s_free_mult = "(CMP.igv_affection NOT IN ('" . CommercialMovementProduct::IGV_AFFECTION_FREE . "', '" . CommercialMovementProduct::IGV_AFFECTION_GIFT . "'))";
                $a_detail = Yii::app()->db->createCommand()
                        ->select([
                            "I.item_id AS item_id",
                            "I.product_id",
                            "I.product_type",
                            "UPPER(I.product_name) as product_name",
                            "IF(PR.custom_measure_id IS NULL, IFNULL(MU1.description, '{$this->getDefaultMerchandiseMeasure()->description}'), MU2.description) AS measure_name",
                            "I.pres_quantity",
                            "REPLACE(P.description, '\n', '<br/>') as description",
                            "CMP.expiration",
                            "ROUND(CMP.cprice_{$header['currency']} * {$s_free_mult}, 4) as cprice",
                            "ROUND(CMP.cdiscount_{$header['currency']} * {$s_free_mult}, 4) as cdiscount",
                            "ROUND(CMP.price_{$header['currency']} * {$s_free_mult}, 4) as price",
                            "ROUND(CMP.discount_{$header['currency']} * {$s_free_mult}, 4) as discount",
                            "ROUND(CMP.vprice_{$header['currency']}, 4) AS vprice",
                            "ROUND(CMP.sprice_{$header['currency']}, 4) AS sprice",
                            "ROUND(CMP.affected_{$header['currency']}, 4) as affected",
                            "ROUND(CMP.inaffected_{$header['currency']}, 4) as inaffected",
                            "ROUND(CMP.nobill_{$header['currency']}, 4) as nobill",
                            "ROUND(CMP.export_{$header['currency']}, 4) as export",
                            "ROUND(CMP.free_{$header['currency']}, 4) as free",
                            "ROUND(CMP.net_{$header['currency']}, 4) as net",
                            "ROUND(CMP.igv_{$header['currency']}, 4) as igv",
                            "ROUND(CMP.total_{$header['currency']}, 4) as total",
                            "CMP.include_igv",
                            "I.allow_decimals",
                            "CMP.igv_affection",
                            "CMP.group_id AS group_id",
                        ])
                        ->from('item I')
                        ->join('commercial_movement_product CMP', 'CMP.item_id = I.item_id AND CMP.group_id IS NULL')
                        ->leftJoin('presentation PR', 'PR.product_id = I.product_id AND PR.equivalence = I.equivalence')
                        ->leftJoin('product P', 'P.product_id = PR.product_id')
                        ->leftJoin('multitable MU1', 'MU1.multi_id = PR.measure_id')
                        ->leftJoin('multitable MU2', 'MU2.multi_id = PR.custom_measure_id')
                        ->join('global_var GV', 'GV.organization_id = 1')
                        ->where('I.movement_id = :movement_id', array(':movement_id' => $id))
                        ->order("I.item_number ASC")
                        ->queryAll();

                if ($format == self::F_ORDEN_VENTA) {
                    CommercialMovementProduct::setSubItems($a_detail, $id);
                }
                //Extras
                $header['text_money'] = Currency::amountToLetters($header['real'], $header['currency']);
                $header['currency_name'] = strtoupper(Currency::getName($header['currency']));
                $header['currency_symbol'] = Currency::getSymbol($header['currency']);
                $header['month_name'] = USTime::getMonthName((int) $header['month']);
                break;
            case self::F_PEDIDO_COMPRA:
            case self::F_ORDEN_SALIDA_DINERO:
            case self::F_ORDEN_CONSIGNACION:
            case self::F_ORDEN_CONSUMO:
            case self::F_ORDEN_CONSUMO_OBRA:
            case self::F_ORDEN_TRANSFORMACION:
                $header = Yii::app()->db->createCommand()->select([
                            //CABECERA
                            "M.document_serie",
                            "M.document_correlative",
                            "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                            "S.title as scenario",
                            "S.direction",
                            "M.exchange_rate as TC",
                            "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date",
                            "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as delivery_date",
                            "IF(M.`status`, 'SI', 'NO') as is_active",
                            'M.observation',
                            //Proyecto
                            "PJ.short_name as short_name",
                            //FROM
                            "REPLACE(XP.person_name, ',', ' ') as from_person_name",
                            "XP.identification_type as from_identification_type",
                            "XP.identification_number as from_identification_number",
                            //TO
                            "REPLACE(P.person_name, ',', ' ') as person_name",
                            "P.identification_type as identification_type",
                            "P.identification_number as identification_number",
                            //TO
                            "REPLACE(PC.person_name, ',', ' ') as pic_person_name",
                            "(CASE PC.identification_type WHEN '1' THEN 'DNI' WHEN '6' THEN 'RUC' ELSE PC.identification_type END) as pic_identification_name",
                            "PC.identification_number as pic_identification_number",
                            "M.print_count",
                            //TRANSFORMATION RESULT
                            "TI.product_id",
                            "PD.product_name",
                            "PR.measure_name",
                            "TI.pres_quantity",
                            "W.warehouse_name",
                            "COALESCE(REPLACE(P2.person_name, ',', ' '),'-') AS editor",
                            "IF(M.currency = 'pen', PL.total_pen, PL.total_usd) AS total",
                            "PL.requirement_type",
                            "IF(PL.requirement_type = 'refund', '" . ProductListMovement::LABEL_TYPE_REFUND . "', '" . ProductListMovement::LABEL_TYPE_PRE_PAY . "') AS requirement_name",
                            "C.cashbox_name",
                            "DATE_FORMAT(EI.delivery_date, '%d/%m/%Y') AS order_date",
                            "DATE_FORMAT(EI.liquidation_date, '%d/%m/%Y') AS liquidation_date",
                        ])
                        ->from('product_list PL')
                        ->join('movement M', 'M.movement_id = PL.owner_id') //Movement
                        ->join('person XP', 'XP.person_id = M.aux_person_id') //Solicitante
                        ->join('person P', 'P.person_id = M.person_id') //Registrador
                        ->join('scenario S', 'S.route = M.route') //Link al padre
                        //Proyecto
                        ->leftJoin('project PJ', 'PJ.project_id = M.project_id')
                        ->leftJoin('extra_info EI', 'EI.movement_id = M.movement_id')
                        ->leftJoin('cashbox C', 'C.cashbox_id = EI.cashbox_id')
                        ->leftJoin('transformation_result T', 'T.movement_id = M.movement_id')
                        ->leftJoin('item TI', 'TI.item_id = T.item_id')
                        ->leftJoin('presentation PR', 'PR.product_id = TI.product_id AND PR.equivalence = TI.equivalence')
                        ->leftJoin('product PD', 'PD.product_id = PR.product_id')
                        ->leftJoin('warehouse W', 'W.warehouse_id = EI.warehouse_id')
                        ->leftJoin('changelog CH', "CH.`owner` = '" . ProductListMovement::OWNER . "' AND CH.owner_id = M.movement_id and CH.action ='" . Changelog::UPDATE . "' and CH.last_by_object_action")
                        ->leftJoin('person P2', 'P2.person_id = CH.person_id')
                        ->leftJoin('person PC', 'PC.person_id = M.person_in_charge_id')
                        ->where('PL.owner_id = :owner_id AND PL.owner = :owner', array(':owner_id' => $id, ':owner' => Movement::OWNER))
                        ->queryRow();
                //
                $a_detail = Yii::app()->db->createCommand()
                        ->select([
                            "IF(I.product_type_original = '" . Product::TYPE_NEW . "', '-', I.product_id) AS product_id",
                            "IF(I.product_type_original = '" . Product::TYPE_NEW . "', '" . Presentation::PRESENTATION_NIU . "', PR.measure_name) AS measure_name",
                            "I.pres_quantity as sale_quantity",
                            "UPPER(IF(I.product_type_original = '" . Product::TYPE_NEW . "', I.description, I.product_name)) as product_name",
                            "I.allow_decimals",
                            "I.item_number",
                            "IF(M.currency = 'pen', PLI.amount_pen, PLI.amount_usd) AS amount",
                            "IF(M.currency = 'pen', PLI.total_pen, PLI.total_usd) AS total"
                        ])
                        ->from('product_list_item PLI')
                        ->join('product_list PL', 'PL.owner_id = PLI.owner_id AND PL.owner = PLI.owner')
                        ->join('movement M', 'M.movement_id = PL.owner_id')
                        ->join('item I', 'I.item_id = PLI.item_id')
                        ->leftJoin('presentation PR', 'PR.product_id = I.product_id AND PR.equivalence = I.equivalence')
                        ->join('global_var GV', 'GV.organization_id = 1')
                        ->where('PLI.owner_id = :owner_id AND PLI.owner = :owner ', array(':owner_id' => $id, ':owner' => Movement::OWNER))
                        ->order("I.item_number ASC")
                        ->queryAll();
                $header['current_datetime'] = SIANTime::formatDateTime();
                $header['from_identification_name'] = USIdentificationValidator::getLabel($header['from_identification_type']);
                break;
            case self::F_GASTOS_MOVILIDAD:
                $header = MobilityMovement::model()->with(array(
                            'movement' => array(
                                'alias' => 'M',
                                'with' => array(
                                    'person' => array(
                                        'alias' => 'E'
                                    )
                                )
                            ),
                            'mobilityPeople' => array(
                                'alias' => 'MP',
                                'with' => array(
                                    'person' => array(
                                        'alias' => 'P',
                                        'with' => array(
                                            'employee' => array(
                                                'alias' => 'EM',
                                                'with' => array(
                                                    'station' => array(
                                                        'alias' => 'S',
                                                        'with' => array(
                                                            'area' => array(
                                                                'alias' => 'A',
                                                            )
                                                        )
                                                    )
                                                )
                                            )
                                        )
                                    ),
                                    'items' => array(
                                        'alias' => 'MPI'
                                    ),
                                )
                            ),
                        ))->findByPk(array($this->modelPk => $id));
            //CREACION JBP
            case self::F_ORDEN_DESPACHO:
                $header = Yii::app()->db->createCommand()->select([
                            "ST.store_name",
                            "M.document_serie",
                            "M.document_correlative",
                            "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                            "S.title as scenario",
                            "M.exchange_rate as TC",
                            "DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date",
                            'M.observation',
                            "REPLACE(XP.person_name, ',', ' ') as from_person_name",
                            "XP.identification_type as from_identification_type",
                            "XP.identification_number as from_identification_number",
                            "REPLACE(P.person_name, ',', ' ') as registrador_name",
                            "P.identification_type as identification_type",
                            "P.identification_number as identification_number",
                            "REPLACE(COALESCE(SPP.person_name, PP.person_name, P.person_name), ',', ' ') AS vendedor_name",
                            "REPLACE(PC.person_name, ',', ' ') as pic_person_name",
                            "(CASE PC.identification_type WHEN '1' THEN 'DNI' WHEN '6' THEN 'RUC' ELSE PC.identification_type END) as pic_identification_name",
                            "PC.identification_number as pic_identification_number",
                            "M.print_count",
                            "PR.barcode",
                            "TI.product_id",
                            "PD.product_name",
                            "PR.measure_name",
                            "TI.pres_quantity",
                            "W.warehouse_name",
                            "COALESCE(REPLACE(P2.person_name, ',', ' '),'-') AS editor",
                            "IF(M.currency = 'pen', PL.total_pen, PL.total_usd) AS total",
                            "CONCAT_WS('-', MP.document_code, MP.document_serie, MP.document_correlative) as parent_document",
                            "EI_PARENT.delivery_date",
                            "EI_PARENT.purchase_order",
                            "EI_PARENT.contact_identification_number",
                            "EI_PARENT.contact_name",
                            "EI_PARENT.contact_phone",
                            "EI_PARENT.contact_email",
                            "EI_PARENT.shipping_type",
                            "EI_PARENT.transport_company_id",
                            "PRT.person_name as transport_company_name",
                            "IF(MA.address IS NOT NULL, CONCAT(MA.address, ' (', MG.dist_name, ' - ', MG.prov_name, ' - ', MG.dept_name,')'), '-') AS delivery_address",
                            "MA.reference as delivery_reference",
                            "REPLACE(PP.person_name, ',', ' ') as parent_registrador_name",
                        ])
                        ->from('product_list PL')
                        ->join('movement M', 'M.movement_id = PL.owner_id')
                        ->join('store ST', 'ST.store_id = M.store_id')
                        ->join('person XP', 'XP.person_id = M.aux_person_id')
                        ->join('person P', 'P.person_id = M.person_id')
                        ->join('scenario S', 'S.route = M.route')
                        ->leftJoin('extra_info EI', 'EI.movement_id = M.movement_id')
                        ->leftJoin('transformation_result T', 'T.movement_id = M.movement_id')
                        ->leftJoin('item TI', 'TI.item_id = T.item_id')
                        ->leftJoin('presentation PR', 'PR.product_id = TI.product_id AND PR.equivalence = TI.equivalence')
                        ->leftJoin('product PD', 'PD.product_id = PR.product_id')
                        ->leftJoin('warehouse W', 'W.warehouse_id = EI.warehouse_id')
                        ->leftJoin('changelog C', "C.`owner` = '" . ProductListMovement::OWNER . "' AND C.owner_id = M.movement_id and C.action ='" . Changelog::UPDATE . "' and C.last_by_object_action")
                        ->leftJoin('person P2', 'P2.person_id = C.person_id')
                        ->leftJoin('person PC', 'PC.person_id = M.person_in_charge_id')
                        ->leftJoin('movement_link ML', 'ML.movement_id = M.movement_id')
                        ->leftJoin('movement MP', 'MP.movement_id = ML.movement_parent_id')
                        ->leftJoin('extra_info EI_PARENT', 'EI_PARENT.movement_id = MP.movement_id')
                        ->leftJoin('commercial_movement CM', 'CM.movement_id = MP.movement_id')
                        ->leftJoin('seller SEL', 'SEL.seller_id = CM.seller_id')
                        ->leftJoin('person SPP', 'SPP.person_id = SEL.person_id')
                        ->leftJoin('person PP', 'PP.person_id = MP.person_id')
                        ->leftJoin('person PRT', 'PRT.person_id = EI_PARENT.transport_company_id')
                        ->leftJoin('address MA', "MA.owner = '" . Movement::OWNER . "' and MA.owner_id = M.movement_id and MA.`order` = 1")
                        ->leftJoin('geoloc MG', "MG.dept_code = MA.dept_code and MG.prov_code = MA.prov_code and MG.dist_code = MA.dist_code")
                        ->where('PL.owner_id = :owner_id AND PL.owner = :owner', array(':owner_id' => $id, ':owner' => Movement::OWNER))
                        ->queryRow();
                $a_detail = Yii::app()->db->createCommand()
                        ->select([
                            "IF(I.product_type_original = '" . Product::TYPE_NEW . "', '-', I.product_id) AS product_id",
                            "IF(I.product_type_original = '" . Product::TYPE_NEW . "', '" . Presentation::PRESENTATION_NIU . "', PR.measure_name) AS measure_name",
                            "I.pres_quantity as sale_quantity",
                            "UPPER(IF(I.product_type_original = '" . Product::TYPE_NEW . "', I.description, I.product_name)) as product_name",
                            "I.allow_decimals",
                            "I.item_number",
                            "IF(M.currency = 'pen', PLI.amount_pen, PLI.amount_usd) AS amount",
                            "IF(M.currency = 'pen', PLI.total_pen, PLI.total_usd) AS total",
                            "PR.barcode",
                        ])
                        ->from('product_list_item PLI')
                        ->join('product_list PL', 'PL.owner_id = PLI.owner_id AND PL.owner = PLI.owner')
                        ->join('movement M', 'M.movement_id = PL.owner_id')
                        ->join('item I', 'I.item_id = PLI.item_id')
                        ->leftJoin('presentation PR', 'PR.product_id = I.product_id AND PR.equivalence = I.equivalence')
                        ->join('global_var GV', 'GV.organization_id = 1')
                        ->where('PLI.owner_id = :owner_id AND PLI.owner = :owner ', array(':owner_id' => $id, ':owner' => Movement::OWNER))
                        ->order("I.item_number ASC")
                        ->queryAll();
                $header['current_datetime'] = SIANTime::formatDateTime();
                $header['from_identification_name'] = USIdentificationValidator::getLabel($header['from_identification_type']);
                break;
            //CULMINACION DE LA CREACIÓN
            case self::F_INGRESO_CAJA:
                $header = Yii::app()->db->createCommand()->select([
                            //CABECERA
                            "M.movement_id",
                            "M.emission_date",
                            "day(M.emission_date) as emission_day",
                            "month(M.emission_date) as emission_month",
                            "year(M.emission_date) as emission_year",
                            "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                            "CONCAT_WS('-', MP.document_code, MP.document_serie, MP.document_correlative) as parent_document",
                            "REPLACE(XP.person_name,',',' ') AS from_person_name",
                            "A.account_name AS to_cashbox_name",
                            "M.currency",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', E.amount_pen, E.amount_usd), 2) AS amount",
                            "ROUND(IFNULL(IF(M.currency = '" . Currency::PEN . "', EP.balance_pen, EP.balance_usd), 0), 2) AS balance",
                            "IF(M.currency = '" . Currency::PEN . "', CM.cash_pen, CM.cash_usd) AS cash",
                            "IF(M.currency = '" . Currency::PEN . "', CM.cash_pen, CM.cash_usd)-IF(M.currency = '" . Currency::PEN . "', CM.total_pen, CM.total_usd) AS vuelto",
                            "M.observation",
                            "M.print_count"
                        ])
                        ->from('entry E')
                        ->join('movement M', "M.movement_id = E.movement_id")
                        ->join("account A", "A.account_code = E.account_code")
                        ->join("person XP", "XP.person_id = E.owner_id")
                        ->leftJoin("entry_link EL", "EL.entry_id = E.entry_id")
                        ->leftJoin("entry EP", "EP.entry_id = EL.entry_parent_id")
                        ->leftJoin("movement MP", "MP.movement_id = EP.movement_id")
                        ->leftJoin("cashbox_movement CM", "CM.movement_id = M.movement_id")
                        ->where("E.movement_id = :movement_id AND E.`column` = :column", [':movement_id' => $id, 'column' => Entry::CREDIT_COLUMN])
                        ->queryRow();
                $header['text_money'] = Currency::amountToLetters($header['amount'], $header['currency']);
                $header['currency_name'] = strtoupper(Currency::getName($header['currency']));
                $header['currency_symbol'] = Currency::getSymbol($header['currency']);
                $header['month_name'] = USTime::getMonthName((int) $header['emission_month']);
                break;
            case self::F_SALIDA_CAJA:
                $header = Yii::app()->db->createCommand()->select([
                            "M.movement_id",
                            "M.emission_date",
                            "day(M.emission_date) as emission_day",
                            "month(M.emission_date) as emission_month",
                            "year(M.emission_date) as emission_year",
                            "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document",
                            "CONCAT_WS('-', MP.document_code, MP.document_serie, MP.document_correlative) as parent_document",
                            "REPLACE(XP.person_name,',',' ') AS to_person_name",
                            "A.account_name AS from_cashbox_name",
                            "M.currency",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', E.amount_pen, E.amount_usd), 2) AS amount",
                            "ROUND(IFNULL(IF(M.currency = '" . Currency::PEN . "', EP.balance_pen, EP.balance_usd), 0), 2) AS balance",
                            "M.observation",
                            "M.print_count"
                        ])
                        ->from('entry E')
                        ->join('movement M', "M.movement_id = E.movement_id")
                        ->join("account A", "A.account_code = E.account_code")
                        ->join("person XP", "XP.person_id = E.owner_id")
                        ->leftJoin("entry_link EL", "EL.entry_id = E.entry_id")
                        ->leftJoin("entry EP", "EP.entry_id = EL.entry_parent_id")
                        ->leftJoin("movement MP", "MP.movement_id = EP.movement_id")
                        ->where("E.movement_id = :movement_id AND E.`column` = :column", [':movement_id' => $id, 'column' => Entry::DEBIT_COLUMN])
                        ->queryRow();
                $header['text_money'] = Currency::amountToLetters($header['amount'], $header['currency']);
                $header['currency_symbol'] = Currency::getSymbol($header['currency']);
                $header['month_name'] = USTime::getMonthName((int) $header['emission_month']);
                break;
            case self::F_SALIDA_MASIVA_CAJA:
                $header = Yii::app()->db->createCommand()->select([
                            "M.document_code",
                            "M.document_serie",
                            "M.movement_id",
                            "M.emission_date",
                            "day(M.emission_date) as emission_day",
                            "month(M.emission_date) as emission_month",
                            "year(M.emission_date) as emission_year",
                            "CB.cashbox_name",
                            "M.currency",
                            "IF(M.currency = '" . Currency::PEN . "', '" . Currency::PEN_NAME . "', '" . Currency::USD_NAME . "') AS currency_type",
                            "CM.type as pay_type",
                            "CM.reference_number",
                            "CM.type",
                            "CM.credit_card",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.real_pen, CM.real_usd), 2) AS amount_total",
                            "ROUND(IFNULL(IF(M.currency = '" . Currency::PEN . "', EP.balance_pen, EP.balance_usd), 0), 2) AS balance",
                            "M.observation",
                            "M.print_count"
                        ])
                        ->from('entry E')
                        ->join('movement M', "M.movement_id = E.movement_id")
                        ->join("account A", "A.account_code = E.account_code")
                        ->join("cashbox_movement CM", "CM.movement_id=M.movement_id")
                        ->join("cashbox CB", "CB.cashbox_id=CM.cashbox_id")
                        ->leftJoin("entry_link EL", "EL.entry_id = E.entry_id")
                        ->leftJoin("entry EP", "EP.entry_id = EL.entry_parent_id")
                        ->where("E.movement_id = :movement_id AND E.`column` = :column", [':movement_id' => $id, 'column' => Entry::DEBIT_COLUMN])
                        ->queryRow();

                $header['currency_symbol'] = Currency::getSymbol($header['currency']);
                $header['month_name'] = USTime::getMonthName((int) $header['emission_month']);

                //detalle salida masiva de caja
                $a_detail = (new SpGetMultipleLinks())->setParams([
                            'mode' => SpGetMultipleLinks::MODE_TO_PAY,
                            'movement_id' => $id,
                            'is_massive' => 1
                        ])->FindAll();
                break;
            case self::F_INGRESO_MASIVO_CAJA:
                $header = Yii::app()->db->createCommand()->select([
                            "M.document_code",
                            "M.document_serie",
                            "M.movement_id",
                            "M.emission_date",
                            "day(M.emission_date) as emission_day",
                            "month(M.emission_date) as emission_month",
                            "year(M.emission_date) as emission_year",
                            "CB.cashbox_name",
                            "M.currency",
                            "IF(M.currency = '" . Currency::PEN . "', '" . Currency::PEN_NAME . "', '" . Currency::USD_NAME . "') AS currency_type",
                            "CM.type as pay_type",
                            "CM.reference_number",
                            "CM.type",
                            "CM.credit_card",
                            "ROUND(IF(M.currency = '" . Currency::PEN . "', CM.real_pen, CM.real_usd), 2) AS amount_total",
                            "ROUND(IFNULL(IF(M.currency = '" . Currency::PEN . "', EP.balance_pen, EP.balance_usd), 0), 2) AS balance",
                            "M.observation",
                            "M.print_count"
                        ])
                        ->from('entry E')
                        ->join('movement M', "M.movement_id = E.movement_id")
                        ->join("account A", "A.account_code = E.account_code")
                        ->join("cashbox_movement CM", "CM.movement_id=M.movement_id")
                        ->join("cashbox CB", "CB.cashbox_id=CM.cashbox_id")
                        ->leftJoin("entry_link EL", "EL.entry_id = E.entry_id")
                        ->leftJoin("entry EP", "EP.entry_id = EL.entry_parent_id")
                        ->where("E.movement_id = :movement_id AND E.`column` = :column", [':movement_id' => $id, 'column' => Entry::DEBIT_COLUMN])
                        ->queryRow();

                $header['currency_symbol'] = Currency::getSymbol($header['currency']);
                $header['month_name'] = USTime::getMonthName((int) $header['emission_month']);

                //detalle ingreso masivo de caja
                $a_detail = (new SpGetMultipleLinks())->setParams([
                            'mode' => SpGetMultipleLinks::MODE_TO_COLLECT,
                            'movement_id' => $id,
                            'is_massive' => 1
                        ])->FindAll();
                break;
            case self::F_NOTA_CREDITO_01:
                break;
            case self::F_NOTA_DEBITO:
                break;
            case self::F_ORDEN_COMPRA:
                //Obtenemos header
                $header = Yii::app()->db->createCommand()->select([
                                    "M.movement_id",
                                    "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) AS document",
                                    "DATE_FORMAT(M.emission_date, '%d/%m/%Y') AS purchase_order_date",
                                    "REPLACE(PE.person_name, ',', ' ') AS buyer",
                                    "CONCAT_WS(' - ', " . USIdentificationValidator::getSQLCase('PP.identification_type') . ", PP.identification_number) AS provider_document",
                                    "REPLACE(PP.person_name, ',', ' ') AS provider_name",
                                    "CONCAT(A.address, ' (', G.dist_name, ' - ', G.prov_name, ' - ', G.dept_name, ')') AS provider_address",
                                    "PH.phone_number AS provider_phone",
                                    "E.delivery_date",
                                    "E.contact_name",
                                    "E.contact_email",
                                    "E.contact_phone",
                                    "CONCAT(MA.address, ' (', MG.dist_name, ' - ', MG.prov_name, ' - ', MG.dept_name, ')') AS delivery_address",
                                    "CONCAT(WA.address, ' (', WG.dist_name, ' - ', WG.prov_name, ' - ', WG.dept_name, ')') AS our_address",
                                    "CONCAT(CM.`condition`, (CASE CM.`condition` WHEN '" . Condition::CREDIT . "' THEN concat(' (', CM.credit_days, ' días)') ELSE '' END), ' en ', CASE M.currency WHEN '" . Currency::PEN . "' THEN 'soles' ELSE 'dólares' end) AS `condition`",
                                    "CM.include_igv",
                                    "(CASE CM.include_igv WHEN 1 THEN 'Si' ELSE 'No' END) AS include_igv_label",
                                    "CM.merchandise_count",
                                    "(CASE WHEN M.currency = '" . Currency::PEN . "' THEN CM.net_pen ELSE CM.net_usd END) AS net",
                                    "(CASE WHEN M.currency = '" . Currency::PEN . "' THEN CM.igv_pen ELSE CM.igv_usd END) AS igv",
                                    "(CASE WHEN M.currency = '" . Currency::PEN . "' THEN CM.total_pen ELSE CM.total_usd END) AS total",
                                    "CONCAT_WS(' - ', GO.dist_name, GO.prov_name, GO.dept_name) AS geoloc_name",
                                    "IFNULL(M.observation, '') AS observations",
                                    "M.print_count",
                                    "PJ.project_name as project_name",
                                    "COALESCE((SELECT REPLACE(P2.person_name, ',', ' ') FROM changelog C
                                    LEFT JOIN person P2 ON P2.person_id = C.person_id 
                                    WHERE C.`owner` = '" . CommercialMovement::OWNER . "' AND C.owner_id = M.movement_id and C.action ='" . Changelog::UPDATE . "' and C.last_by_object_action),'-') AS editor"
                                ])
                                ->from("movement M")
                                ->join("person PE", "PE.person_id = M.person_id")
                                ->join("commercial_movement CM", "CM.movement_id = M.movement_id")
                                ->join("extra_info E", "E.movement_id = CM.movement_id")
                                ->join("organization O", "")
                                ->join("address AO", "AO.owner_id = O.person_id AND `owner` = '" . Person::OWNER . "' AND AO.`order` = 1")
                                ->join("geoloc GO", "GO.dept_code = AO.dept_code AND GO.prov_code = AO.prov_code AND GO.dist_code = AO.dist_code")
                                ->leftJoin("address WA", "WA.`owner` = '" . Warehouse::OWNER . "' AND WA.owner_id = E.Warehouse_id AND WA.`order` = 1")
                                ->leftJoin("geoloc WG", "WG.dept_code = WA.dept_code AND WG.prov_code = WA.prov_code AND WG.dist_code = WA.dist_code")
                                ->join("person PP", "PP.person_id = M.aux_person_id")
                                ->leftJoin("phone PH", "PH.`owner` = '" . Person::OWNER . "' AND PH.owner_id = PP.person_id AND PH.`order` = 1")
                                ->leftJoin("address A", "A.`owner` = '" . Person::OWNER . "' AND A.owner_id = PP.person_id AND A.`order` = 1")
                                ->leftJoin("geoloc G", "G.dept_code = A.dept_code AND G.prov_code = A.prov_code AND G.dist_code = A.dist_code")
                                ->leftJoin("address MA", "MA.`owner`= 'Movement' AND MA.owner_id = M.movement_id AND MA.`order` = 1")
                                ->leftJoin("geoloc MG", "MG.dept_code = MA.dept_code AND MG.prov_code = MA.prov_code AND MG.dist_code = MA.dist_code")
                                ->leftJoin("project PJ", "PJ.project_id = M.project_id")
                                ->where("M.movement_id = :movement_id", [
                                    'movement_id' => $id,
                                ])->queryRow();
                $header['current_datetime'] = SIANTime::formatDateTime();
                //
                $a_detail = Yii::app()->db->createCommand()->select([
                            "I.movement_id",
                            "I.product_id",
                            "I.product_name",
                            "I.allow_decimals",
                            "P.description",
                            "IFNULL(PR.measure_name, '{$this->getDefaultMerchandiseMeasure()->abbreviation}') AS measure_name",
                            "I.pres_quantity AS quantity",
                            "(CASE WHEN M.currency = '" . Currency::PEN . "' THEN CMP.cprice_pen ELSE CMP.price_usd END) AS cprice",
                            "(CASE WHEN M.currency = '" . Currency::PEN . "' THEN CMP.cdiscount_pen ELSE CMP.discount_usd END) AS cdiscount",
                            "(CASE WHEN M.currency = '" . Currency::PEN . "' THEN CMP.price_pen ELSE CMP.price_usd END) AS price",
                            "(CASE WHEN M.currency = '" . Currency::PEN . "' THEN CMP.discount_pen ELSE CMP.discount_usd END) AS discount", "(CASE WHEN M.currency = '" . Currency::PEN . "' THEN CMP.net_pen ELSE CMP.net_usd END) AS net",
                            "(CASE WHEN M.currency = '" . Currency::PEN . "' THEN CMP.igv_pen ELSE CMP.igv_usd END) AS igv",
                            "(CASE WHEN M.currency = '" . Currency::PEN . "' THEN CMP.real_pen ELSE CMP.real_usd END) AS total"
                        ])
                        ->from("item I")
                        ->join("commercial_movement_product CMP", "CMP.item_id = I.item_id")
                        ->join("commercial_movement CM", "CM.movement_id = CMP.movement_id")
                        ->join("movement M", "M.movement_id = CM.movement_id")
                        ->join("global_var G", "G.organization_id = 1")
                        ->leftJoin("presentation PR", "PR.product_id = I.product_id AND PR.equivalence = I.equivalence")
                        ->leftJoin("product P", "P.product_id = I.product_id")
                        ->where("M.movement_id = :movement_id", [
                            'movement_id' => $id,
                        ])
                        ->order('I.item_number')
                        ->queryAll();
                break;
            case self::F_ORDEN_SERVICIO:
                $header = Yii::app()->db->createCommand()->select([
                            "M.movement_id",
                            "M.route",
                            "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) AS document",
                            "REPLACE(PE.person_name, ',', ' ') AS employee_name,PC.identification_number AS client_identification_number",
                            "REPLACE(PC.person_name, ',', ' ') AS client_name",
                            "IF(AD.address IS NOT NULL, CONCAT(AD.address, ' (', ADG.dist_name, ' - ', ADG.prov_name, ' - ', ADG.dept_name,')'), '-') as client_address",
                            "(SELECT GROUP_CONCAT(CONCAT_WS(': ', PH.phone_type, PH.phone_number) SEPARATOR ' | ') FROM phone PH WHERE PH.owner = '" . Person::OWNER . "' AND PH.owner_id = PC.person_id) AS phone",
                            "DATE_FORMAT(M.emission_date, '%d/%m/%Y') AS emission_date",
                            "UPPER(SO.type) as type",
                            "UPPER(SO.product) as product",
                            "SO.quantity",
                            "UPPER(SO.model) as model",
                            "UPPER(SO.mark) as mark",
                            "UPPER(REPLACE(SO.serie, '\n', ' | ')) as serie",
                            "SO.service_status",
                            "M.print_count",
                            "PT.phone_number"
                        ])
                        ->from('movement M')
                        ->join("person PE", "PE.person_id = M.person_id")
                        ->join("person PC", "PC.person_id = M.aux_person_id")
                        ->join("service_order SO", "SO.movement_id = M.movement_id")
                        ->leftJoin("address AD", "AD.owner_id = PC.person_id AND AD.owner = '" . Person::OWNER . "' AND AD.order = 1 ")
                        ->leftJoin('geoloc ADG', "ADG.dept_code = AD.dept_code and ADG.prov_code = AD.prov_code and ADG.dist_code = AD.dist_code")
                        ->leftJoin('phone PT', "PT.owner_id= PE.person_id and PT.`owner` ='" . Person::OWNER . "' AND PT.order = 1")
                        ->where("M.movement_id = :movement_id", array(':movement_id' => $id))
                        ->queryRow();

                $a_detail = Yii::app()->db->createCommand()->select([
                            "MLT.movement_log_type_id",
                            "UPPER(MLT.long_title) as long_title",
                            "ML.register_date",
                            "UPPER(ML.description) as description"
                        ])
                        ->from('movement M')
                        ->join("movement_log ML", "ML.movement_id = M.movement_id")
                        ->join("movement_log_type MLT", "MLT.movement_log_type_id = ML.movement_log_type_id AND SUBSTR(required, 3, 1) = 1")
                        ->where("M.movement_id = :movement_id", array(':movement_id' => $id))
                        ->order("MLT.order, ML.register_date")
                        ->queryAll();

                $header['currentDate'] = SIANTime::formatDateTime();

                break;
            case self::F_VOUCHER_CONTABLE:
                $header = Yii::app()->db->createCommand()->select([
                            "M.movement_id",
                            "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) AS document",
                            "DATE_FORMAT(M.emission_date, '%d/%m/%Y') AS emission_date",
                            "AM.gloss AS gloss",
                            "REPLACE(PE.person_name,',',' ') AS employee_name",
                            "M.print_count"
                        ])
                        ->from('movement M')
                        ->join("person PE", "PE.person_id = M.person_id")
                        ->join("accounting_movement AM", "AM.movement_id = M.movement_id")
                        ->where("M.movement_id = :movement_id", array(':movement_id' => $id))
                        ->queryRow();

                $a_detail = Yii::app()->db->createCommand()->select([
                            "E.movement_id AS movement_id",
                            "E.account_code AS account_code",
                            "E.detail AS detail",
                            "E.amount_pen AS amount_pen",
                            "IF((E.`column` = '" . Entry::DEBIT_COLUMN . "'),'+','-') AS sign"
                        ])
                        ->from('entry E')
                        ->where("E.movement_id = :movement_id", array(':movement_id' => $id))
                        ->queryAll();
                break;
            case self::F_PAYROLL:
                $header = Yii::app()->db->createCommand()->select([
                            "M.movement_id",
                            "CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) AS document",
                            "DATE_FORMAT(M.emission_date, '%d/%m/%Y') AS emission_date",
                            "REPLACE(PE.person_name,',',' ') AS employee_name",
                            "AM.gloss AS gloss",
                            "M.print_count",
                            "R.description as regime_name",
                            "SU.year",
                            "SU.period",
                            "SU.week"
                        ])
                        ->from('movement M')
                        ->join("payroll_movement PM", "PM.movement_id = M.movement_id")
                        ->join("assistance_summary SU", "SU.assistance_summary_id = PM.assistance_summary_id")
                        ->join("person PE", "PE.person_id = M.person_id")
                        ->join("dictionary D", "PM.dictionary_id= D.dictionary_id")
                        ->join("regime R", "R.regime_id= D.regime_id")
                        ->join("accounting_movement AM", "AM.movement_id = M.movement_id")
                        ->where("M.movement_id = :movement_id", array(':movement_id' => $id))
                        ->queryRow();
                $header['current_datetime'] = SIANTime::formatDateTime();

                $a_detail = Yii::app()->db->createCommand()->select([
                            "D.regime_id",
                            "PM.dictionary_id",
                            "PMH.combination_id",
                            "CC.combination_name",
                            "PMD.concept_id",
                            "C.short_name",
                            "C.order",
                            "ROUND(SUM(PMD.amount),2) as amount",
                            "PMH.human_category_id",
                            "HC.human_category_name",
                            "PMH.person_id",
                            "PE.identification_number",
                            "REPLACE(PE.person_name,',',' ') as person_name",
                            "PS.pension_system_name",
                            "PMH.salary",
                            "PMH.children"
                        ])
                        ->from('payroll_movement PM')
                        ->join("movement MO", "PM.movement_id = MO.movement_id")
                        ->join("dictionary D", "PM.dictionary_id= D.dictionary_id")
                        ->join("assistance_summary SU", "PM.assistance_summary_id = SU.assistance_summary_id")
                        ->join("payroll_movement_header PMH", "PMH.movement_id = PM.movement_id")
                        ->join("payroll_movement_detail PMD", "PMD.movement_id = PMH.movement_id AND PMD.person_id = PMH.person_id")
                        ->join("concept C", "C.concept_id = PMD.concept_id AND C.is_printable = 1 ")
                        ->leftJoin("employee E", "PMH.person_id = E.person_id")
                        ->leftJoin("person PE", "E.person_id = PE .person_id")
                        ->leftJoin("pension_system PS", "PMH.pension_system_id=PS.pension_system_id")
                        ->leftJoin("combination CC", "PMH.combination_id = CC.combination_id")
                        ->leftJoin("human_category HC", "PMH.human_category_id = HC.human_category_id")
                        ->where("PM.movement_id = :movement_id", array(':movement_id' => $id))
                        ->group("person_id, concept_id")
                        ->order("person_id ASC, C.order ASC")
                        ->queryAll();
                break;
            case self::F_COMPROBANTE_RETENCION:
                $retention_percentage = GlobalVar::getRetentionPercentage();
                $no_retention_percentage = 1 - $retention_percentage;
                //Obtenemos header
                $header = Yii::app()->db->createCommand()
                        ->select([
                            "M.movement_id AS movement_id",
                            "M.document_code AS document_code",
                            "M.document_serie AS document_serie",
                            "M.currency AS currency",
                            "M.document_correlative AS document_correlative",
                            "S.scenario_id AS scenario_id",
                            "M.route AS route",
                            "M.person_id AS person_id",
                            "M.aux_person_id AS aux_person_id",
                            "REPLACE(XP.person_name, ',', ' ') AS aux_person_name",
                            "XP.identification_number AS identification_number",
                            "DATE_FORMAT(CONCAT_WS('-', AM.`year`, AM.period, AM.`day`), '%Y-%m-%d') AS register_date",
                            "DATE_FORMAT(M.emission_date, '%d/%m/%Y') AS emission_date",
                            "M.currency AS currency",
                            "IFNULL(IF(M.currency = 'pen', MM.amount_pen, MM.amount_usd), 0) AS total",
                            "M.store_id AS store_id",
                            "M.status AS status",
                        ])
                        ->from("movement M")
                        ->join("multiple_movement MM", "MM.movement_id = M.movement_id")
                        ->join("scenario S", "S.route = M.route")
                        ->join("accounting_movement AM", "AM.movement_id = M.movement_id")
                        ->join("person XP", "XP.person_id = M.aux_person_id")
                        ->where("M.route = :route AND M.movement_id = :movement_id", [
                            ':route' => 'accounting/providerWithholding',
                            ':movement_id' => $id,
                        ])
                        ->group("M.movement_id")
                        ->order("M.movement_id DESC")
                        ->queryRow();

                $header['current_datetime'] = SIANTime::formatDateTime();
                $header['total_payed'] = $header['total_to_pay'] * $no_retention_percentage;
                $header['percentage_retention'] = $retention_percentage * 100;
                $header['document'] = 'Comprobante de Retención';
                $header['currency_symbol'] = $header['currency'] == Currency::PEN ? Currency::PEN_SYMBOL : Currency::USD_SYMBOL;
                $header['org_name'] = Yii::app()->controller->getOrganization()->person->person_name;
                $header['org_address'] = Yii::app()->controller->getOrganization()->person->officialAddress->address;
                $header['ruc'] = Yii::app()->controller->getOrganization()->person->identification_number;

                $a_detail_result = (new SpGetMultipleLinks())->setParams([
                            'mode' => SpGetMultipleLinks::MODE_TO_PAY,
                            'movement_id' => $id,
                        ])->findAll();

                $a_detail = [];

                $total_ret = 0;
                $total_payed = 0;

                foreach ($a_detail_result as $o_detail) {
                    $movementDetail = $this->getBillDetail($o_detail->movement_id);
                    $total_bill = $movementDetail['total_pen'];
                    if ($movementDetail->currency === Currency::USD) {
                        $total_bill = $movementDetail['total_usd'];
                    }
                    $total_to_pay = ($o_detail->amount / $retention_percentage);
                    $amount_payed = $total_to_pay * $no_retention_percentage;
                    $a_detail[] = [
                        'document_name' => $movementDetail['document_name'],
                        'document_serie' => $movementDetail['document_serie'],
                        'document_correlative' => $movementDetail['document_correlative'],
                        'emission_date' => $movementDetail['emission_date'],
                        'total_bill' => $total_bill,
                        'n' => $this->getMovementOrder($o_detail->movement_id, $id),
                        'total_to_pay' => $total_to_pay,
                        'percentage' => $retention_percentage * 100,
                        'ret_amount' => $o_detail->amount,
                        'total_payed' => $amount_payed
                    ];

                    $total_payed += $amount_payed;
                    $total_ret += $o_detail->amount;
                }

                $header['total_ret'] = $total_ret;
                $header['total_payed'] = $total_payed;
                break;
            default:
                $this->_processError($id, new Exception('No existe el formato de impresión indicado. Comuníquese con el área de sistemas'));
                break;
        }

        //Renderizamos la vista
        return [
            'view' => "{$s_alias}.{$s_sub_path}.{$format}",
            'data' => [
                'type' => $type,
                'header' => $header,
                'detail' => $a_detail,
                'stores' => Store::get(Store::MODE_PRINT)
            ]
        ];
    }

    public function getBillDetail($movementId) {
        $sql = "
            SELECT 
                D.document_name, 
                M.document_serie, 
                M.document_correlative,
                DATE_FORMAT(M.emission_date, '%d/%m/%Y') as emission_date,
                CM.total_pen, 
                CM.total_usd
            FROM movement AS M
            STRAIGHT_JOIN document AS D ON D.document_code = M.document_code
            STRAIGHT_JOIN commercial_movement AS CM ON CM.movement_id = M.movement_id
            WHERE M.movement_id = :movement_id
        ";
        $command = Yii::app()->db->createCommand($sql);
        $command->bindParam(':movement_id', $movementId);
        return $command->queryRow();
    }

    public static function getMovementOrder($movementParentId, $movementId) {
        $sql = "
            SELECT n
            FROM (
                SELECT 
                    ROW_NUMBER() OVER (ORDER BY M.register_date ASC) AS n,
                    M.movement_id
                FROM movement_link ML
                STRAIGHT_JOIN movement AS M ON M.movement_id = ML.movement_id
                WHERE 
                    ML.movement_parent_id = :movement_parent_id AND 
                    ML.route IN ('accounting/providerWithholding')
            ) AS ranked_movements
            WHERE movement_id = :movement_id
        ";
        $command = Yii::app()->db->createCommand($sql);
        $command->bindParam(':movement_parent_id', $movementParentId, PDO::PARAM_INT);
        $command->bindParam(':movement_id', $movementId, PDO::PARAM_INT);
        return $command->queryScalar();
    }

    public function actionRequestEdit($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null, $reason = 0, $checked = 0, $who_confirm = 0, $user_request_id = null) {
        $this->_mainRequest($id, $modal_id, $parent_id, $type, $element_id, $redirect_url, $reason, $checked, 'request_edit', $who_confirm, $user_request_id);
    }

    public function actionRequestNull($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null, $reason = 0, $checked = 0, $who_confirm = 0, $user_request_id = null) {
        $this->_mainRequest($id, $modal_id, $parent_id, $type, $element_id, $redirect_url, $reason, $checked, 'request_null', $who_confirm, $user_request_id);
    }

    public function actionRequestRemove($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null, $reason = 0, $checked = 0, $who_confirm = 0, $user_request_id = null) {
        $this->_mainRequest($id, $modal_id, $parent_id, $type, $element_id, $redirect_url, $reason, $checked, 'request_remove', $who_confirm, $user_request_id);
    }

    public function actionRequestDispatch($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null, $reason = 0, $checked = 0, $who_confirm = 0, $user_request_id = null) {
        $this->_mainRequest($id, $modal_id, $parent_id, $type, $element_id, $redirect_url, $reason, $checked, 'request_dispatch', $who_confirm, $user_request_id);
    }

    public function actionRequestPay($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null, $reason = 0, $checked = 0, $who_confirm = 0, $user_request_id = null) {
        $this->_mainRequest($id, $modal_id, $parent_id, $type, $element_id, $redirect_url, $reason, $checked, 'request_pay', $who_confirm, $user_request_id);
    }

    public function actionRequestApply($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null, $reason = 0, $checked = 0, $who_confirm = 0, $user_request_id = null) {
        $this->_mainRequest($id, $modal_id, $parent_id, $type, $element_id, $redirect_url, $reason, $checked, 'request_apply', $who_confirm, $user_request_id);
    }

    public function actionRequestUpgrade($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null, $reason = 0, $checked = 0, $who_confirm = 0, $user_request_id = null) {
        $this->_mainRequest($id, $modal_id, $parent_id, $type, $element_id, $redirect_url, $reason, $checked, 'request_upgrade', $who_confirm, $user_request_id);
    }

    public function actionCancelRequest($id) {
        $s_modelClass = $this->modelClass;

        if ($s_modelClass === get_class(new ProductListMovement)) {
            $a_pk = [
                'owner' => Movement::OWNER,
                'owner_id' => $id
            ];
        } else {
            $a_pk = [
                'movement_id' => $id
            ];
        }

        $model = $s_modelClass::model()->with([
                    'movement' => [
                        'select' => [
                            'M.movement_id',
                            'M.document_code',
                            'M.document_serie',
                            'M.document_correlative',
                            'M.route',
                            'M.currency',
                        ],
                        'alias' => 'M',
                        'with' => [
                            'ability' => [
                                'alias' => 'A',
                                'joinType' => 'join',
                                'with' => [
                                    'confirmation' => [
                                        'alias' => 'C',
                                        'joinType' => 'join',
                                    ],
                                ]
                            ],
                        ]
                    ],
                ])->findByPk($a_pk);

        if (isset($model->movement->ability)) {

            try {

                //Iniciamos la transacción
                $this->beginTransaction($model);

                $model->movement->ability->updateAll(array('confirm_id' => NULL), array(
                    'condition' => "movement_id = :movement_id",
                    'params' => [
                        ':movement_id' => $id
                    ]
                ));

                $confirm_id = $model->movement->ability->confirmation->confirm_id;

                $model->movement->ability->confirmation->updateAll(array(
                    'user_cancel' => Yii::app()->user->person_id,
                    'cancel_date' => SIANTime::now(),
                    'state' => Confirmation::STATE_CANCELED
                        ), array(
                    'condition' => "confirm_id = {$confirm_id}",
                ));
                //Guardamos changelog
                $model->simple(Changelog::CANCEL_REQUEST, Changelog::DEFAULT_REASON_CODE, '', [
                    'movement' => [
                        'ability' => [
                            'confirm_id' => [
                                'previous' => $confirm_id,
                                'current' => null
                            ]
                        ]
                    ]
                ]);
                //Generamos ability
                Ability::generateAbility($model->movement_id, [], [], null);
                //Confirmamos la transacción
                $this->commitTransaction($model);
            } catch (CDbException $ex) {
                //Anulamos la transacción
                $this->rollbackTransaction($model);
                throw new Exception($ex->errorInfo[2]);
            } catch (Exception $ex) {
                //Anulamos la transacción
                $this->rollbackTransaction($model);
                throw $ex;
            }
            $this->redirect(array('view', 'id' => $model->movement_id));
        } else {
            Yii::app()->user->setFlash('error', "Esta opción ya no esta disponible!");
            $this->redirect(array('index'));
        }
    }

    protected function _mainRequest($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $redirect_url = null, $reason = 0, $checked = 0, $field = null, $who_confirm = 0, $user_request_id = null) {

        if ($checked == 0) {
            echo CJSON::encode($this->_checkRejects($id, $field));
        } else {
            $labels = [
                'request_edit' => 'Solicitar edición',
                'request_dispatch' => 'Solicitar despacho/ingreso/devolución',
                'request_pay' => 'Solicitar cobro/pago/aplicación',
                'request_null' => 'Solicitar anulación',
                'request_remove' => 'Solicitar eliminación',
                'request_upgrade' => 'Solicitar facturación/orden'
            ];

            $s_modelClass = $this->modelClass;

            if ($s_modelClass === get_class(new ProductListMovement)) {
                $a_pk = [
                    'owner' => Movement::OWNER,
                    'owner_id' => $id
                ];
            } else {
                $a_pk = [
                    'movement_id' => $id
                ];
            }

            $model = $s_modelClass::model()->with([
                        'movement' => [
                            'select' => [
                                'M.movement_id',
                                'M.document_code',
                                'M.document_serie',
                                'M.document_correlative',
                                'M.route',
                                'M.currency',
                            ],
                            'alias' => 'M',
                            'with' => [
                                'accountingMovement' => [
                                    'select' => ['AM.movement_id'],
                                    'alias' => 'AM',
                                ],
                                'ability' => [
                                    'select' => ['A.confirm_id'],
                                    'alias' => 'A',
                                ],
                                'scenario' => [
                                    'select' => ['S.confirmation_mode'],
                                    'alias' => 'S',
                                ],
                            ]
                        ],
                    ])->findByPk($a_pk);

            //Verificamos si no hay otra solicitud pendiente
            if (isset($model->movement->ability->confirm_id)) {
                Yii::app()->user->setFlash('error', "Ya hay una solicitud pendiente para este movimiento. Por favor espere su confirmación.");
                $this->redirect(array('view', 'id' => $model->movement_id));
            }

            $confirmation = new Confirmation();
            $confirmation->option_type = Confirmation::getOptionType($field);
            $confirmation->movement_id = $id;
            $confirmation->state = Confirmation::STATE_PENDIENT;
            $confirmation->user_request = Yii::app()->user->person_id;
            $confirmation->request_date = SIANTime::now();

            if ($model->movement->scenario->confirmation_mode == Scenario::CONFIRMATION_MODE_GROUPS) {

                $a_groups_ids = [];

                if (isset($user_request_id)) {
                    $confirmation->user_request = $user_request_id;
                    $a_groups_not_manage = User::getGroupsNotManage($user_request_id);
                } else {
                    $a_groups_not_manage = Yii::app()->user->getState('groups_not_manage');
                }

                //Se retiran los grupos donde el usuario que registra es sólo supervisor
                $a_groups_supervises = User::getGroupsSupervises(Yii::app()->user->getState('person_id'));
                foreach ($a_groups_supervises as $id => $group_supervises) {
                    unset($a_groups_not_manage[$id]);
                }

                $a_groups_ids = array_keys($a_groups_not_manage);
                if (count($a_groups_ids) == 1) {
                    $confirmation->user_group_confirm = $a_groups_ids [0];
                }
            }
            //Si esta marcado el indicador de confirmación que registra motivo o selecciona quien confirma
            if ($reason == 1 || $who_confirm == 1) {
                try {
                    $modal_html = "";
                    $content_html = "";
                    $success = false;

                    $this->title = "{$labels[$field]} para '{$model->toString()}'";

                    if (isset($_POST['Confirmation'])) {

                        $confirmation->attributes = $_POST['Confirmation'];

                        if ($confirmation->validate()) {
                            $this->_request($confirmation, $model, $field);
                            $success = true;
                        }
                    } else {
                        $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array(
                                'parent_id' => $parent_id,
                                'modal_id' => $modal_id
                            )), true, true);
                    }

                    $message = Strings::SUCCESS_REQUEST;
                    if (!$success) {
                        $content_html = $this->renderPartial('application.modules.administration.views.confirmation._request', array(
                            'data' => [
                                'model' => $confirmation,
                                'reason' => $reason,
                                'who_confirm' => $a_groups_not_manage
                            ]
                                ), true, true);
                    } else {
                        $content_html = $redirect_url;
                        Yii::app()->user->setFlash('success', $message);
                    }

                    echo CJSON::encode(array(
                        'success' => $success,
                        'modal_id' => $modal_id,
                        'modal_html' => $modal_html,
                        'content_html' => $content_html,
                        'element_id' => $element_id,
                        'type' => $type,
                        'message' => $message
                    ));
                } catch (CDbException $ex) {
                    $this->_processError($id, $ex);
                } catch (CHttpException $ex) {
                    $this->_processError($id, $ex);
                } catch (Exception $ex) {
                    $this->_processError($id, $ex);
                }
            } else {

                try {

                    $this->_request($confirmation, $model, $field);

                    $message = Strings::SUCCESS_REQUEST;
                    switch ($type) {
                        case 'grid':
                            $content_html = '';
                            break;
                        case 'redirect':
                            Yii::app()->user->setFlash('success', $message);
                            $content_html = $redirect_url;
                            break;
                    }

                    echo CJSON::encode(array(
                        'type' => $type,
                        'element_id' => $element_id,
                        'content_html' => $content_html,
                        'message' => $message
                    ));
                } catch (CDbException $ex) {
                    $this->_processError($id, $ex);
                } catch (CHttpException $ex) {
                    $this->_processError($id, $ex);
                } catch (Exception $ex) {
                    $this->_processError($id, $ex);
                }
            }
        }
    }

    private function _checkRejects($id, $field) {

        $confirmation = new Confirmation();
        $confirmation->option_type = Confirmation::getOptionType($field);

        $result = Yii::app()->db->createCommand()->select([
                    "M.movement_id",
                    "C.confirm_date",
                    "C.user_confirm",
                    "C.reason",
                    "C.response_reason",
                    "R.person_id",
                    "REPLACE(R.person_name,',',' ') as user_request",
                    "P.person_id",
                    "REPLACE(P.person_name,',',' ') as user_confirm"
                ])
                ->from('movement M')
                ->join("confirmation C", "C.movement_id = M.movement_id AND C.option_type = '" . Confirmation::getOptionType($field) . "' and C.state = '" . Confirmation::STATE_REJECTED . "'")
                ->join("person P", "C.user_confirm = P .person_id")
                ->join("person R", "C.user_request = R .person_id")
                ->where("M.movement_id = :movement_id", array(':movement_id' => $id))
                ->order("C.confirm_date DESC")
                ->limit("1")
                ->queryRow();

        $has_rejected = isset($result['movement_id']);
        $content_html = "";

        if ($has_rejected) {
            $content_html = $this->renderPartial('application.modules.administration.views.confirmation._warning', array(
                'data' => array(
                    'user_request' => $result['user_request'],
                    'reason' => $result['reason'],
                    'user_confirm' => $result['user_confirm'],
                    'confirm_date' => $result['confirm_date'],
                    'response_reason' => $result['response_reason'],
                    'type' => $confirmation->getOptionTypeRequestLabel()
                )
                    ), true, true);
        }

        return [
            'show_confirm' => $has_rejected,
            'confirm_message' => $content_html,
        ];
    }

    private function _request($confirmation, $model, $field) {

        try {
            //Iniciamos la transacción
            $this->beginTransaction($confirmation);
            $confirmation->insert();
            $model->movement->ability->updateAll(array('confirm_id' => $confirmation->confirm_id), array(
                'condition' => "movement_id = {$model->movement_id}",
            ));
            //Guardamos changelog
            $model->simple(Changelog::REQUEST, Changelog::DEFAULT_REASON_CODE, USString::ifBlank($confirmation->reason, ''), [
                'movement' => [
                    'ability' => [
                        'confirm_id' => [
                            'previous' => null,
                            'current' => $confirmation->confirm_id
                        ]
                    ]
                ]
            ]);
            //Generamos ability
            Ability::generateAbility($model->movement_id, [], [], null);
            //Confirmamos la transacción
            $this->commitTransaction($confirmation);
        } catch (CDbException $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($confirmation);
            throw new Exception($ex->errorInfo[2]);
        } catch (Exception $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($confirmation);
            throw $ex;
        }
    }

    protected function _processError($id, $ex) {

        if (Yii::app()->request->isAjaxRequest) {
            echo CJSON::encode(array(
                'type' => 'message',
                'element_id' => 'error',
                'content_html' => $ex->getMessage()
            ));
        } else {
            Yii::app()->user->setFlash('error', $ex->getMessage());
            //Generar un log aquí
            $this->redirect(array('view', 'id' => $id));
        }
    }

    /**
     * Setea los layout y flases
     * @param Movement $model Instancia de Movement
     * @param string $layout Layout para view
     */
    protected function setLayoutAndFlashes($model, $layout) {
        if (isset($layout)) {
            $this->layout = $layout;
        } else {
            $this->layout = "//layouts/view";

            if ($this->module->id !== 'administration' && $this->id != 'confirmation') {
                //Advertencia de provisión
                if ($model->status && isset($model->accountingMovement) && $model->accountingMovement->checked == 0) {
                    Yii::app()->user->setFlash('warning', Strings::SHOULD_BE_PROVISIONED);
                }

                if ($model->status && $model->advancedOption->lock_payment == 1) {
                    Yii::app()->user->setFlash('error', Strings::LOCK_PAYMEMNT);
                }

                if (isset($model->ability->confirm_id)) {
                    Yii::app()->user->setFlash('warning', Strings::PENDING_REQUEST . " de " . $model->ability->confirmation->getOptionTypeRequestLabel() . ". Comuníquese con un administrador para que responda la solicitud.");
                }
            }
        }
    }

    public function loadAbility($id) {
        return Movement::model()->with([
                    'scenario' => [
                        'select' => ['S.title', 'S.route', 'S.edit_directly', 'S.dispatch_directly', 'S.pay_directly', 'S.apply_directly', 'S.null_directly', 'S.remove_directly', 'S.accounting_file_id', 'S.not_scheduled_to_pay', 'S.not_scheduled_to_redeem', 'S.allow_logs'],
                        'alias' => 'S',
                        'with' => [
                            'payableControl' => [
                                'select' => ['PC.parent_route', 'PC.route'],
                                'with' => [
                                    'scenario' => [
                                        'select' => ['PCS.route'],
                                        'alias' => 'PCS'
                                    ]
                                ]
                            ],
                            'applicableControl' => [
                                'select' => ['AC.parent_route', 'AC.route'],
                                'with' => [
                                    'scenario' => [
                                        'select' => ['ACS.route'],
                                        'alias' => 'ACS'
                                    ]
                                ]
                            ],
                            'dispatchableControl' => [
                                'select' => ['DC.parent_route', 'DC.route'],
                                'with' => [
                                    'scenario' => [
                                        'select' => ['DCS.route'],
                                        'alias' => 'DCS'
                                    ]
                                ]
                            ],
                            'overloadableControl' => [
                                'select' => ['OC.parent_route', 'OC.route'],
                                'with' => [
                                    'scenario' => [
                                        'select' => ['OSC.route'],
                                        'alias' => 'OSC'
                                    ]
                                ]
                            ],
                            'upgradeableControl' => [
                                'select' => ['UC.parent_route', 'UC.route'],
                                'with' => [
                                    'scenario' => [
                                        'select' => ['USC.route'],
                                        'alias' => 'USC'
                                    ]
                                ]
                            ],
                            'accountingFile' => [
                                'select' => ['AF.autochecked'],
                                'alias' => 'AF',
                            ]
                        ]
                    ],
                    'operation' => [
                        'select' => ['O.operation_code', 'O.operation_name'],
                        'alias' => 'O',
                    ],
                    'document' => [
                        'select' => ['D.document_code', 'D.document_name'],
                        'alias' => 'D',
                    ],
                    'children' => [
                        'select' => ['MC.movement_id', 'MC.document_code', 'MC.document_serie', 'MC.document_correlative', 'MC.emission_date', 'MC.route', 'MC.`status`'],
                        'alias' => 'MC',
                        'with' => [
                            'operation' => [
                                'select' => ['MCO.operation_name'],
                                'alias' => 'MCO',
                            ]
                        ]
                    ],
                    'parents' => [
                        'select' => ['MP.movement_id', 'MP.document_code', 'MP.document_serie', 'MP.document_correlative', 'MP.emission_date', 'MP.route'],
                        'alias' => 'MP',
                        'with' => [
                            'operation' => [
                                'select' => ['MPO.operation_name'],
                                'alias' => 'MPO',
                            ],
                            'ability' => [
                                'select' => ['PA.is_creditable', 'PA.confirm_id'],
                                'alias' => 'PA',
                                'with' => [
                                    'skill2'
                                ]
                            ],
                        ]
                    ],
                    'person' => [
                        'select' => ['P.person_name', 'P.identification_type'],
                        'alias' => 'P',
                    ],
                    'ability' => [
                        'select' => ['A.is_dispatched', 'A.is_paid', 'A.is_editable', 'A.is_provisionable', 'A.is_discardable', 'A.is_entry_modifiable', 'A.is_dispatchable', 'A.is_creditable', 'A.is_payable', 'A.is_redeemable', 'A.is_applicable', 'A.is_overloadable', 'A.is_upgradeable', 'A.is_clonable', 'A.is_nullable', 'A.is_removable', 'A.is_printable', 'A.is_loggable', 'A.confirm_id'],
                        'alias' => 'A',
                        'with' => [
                            'skill'
                        ]
                    ],
                ])->findByPk([
                    'movement_id' => $id
                        ], [
                    'select' => ['M.movement_id', 'M.document_code', 'M.document_serie', 'M.document_correlative', 'M.emission_date', 'M.register_date', 'M.observation', 'M.`status`', 'M.route']
        ]);
    }

    public function loadEntryGroupAbility($id) {
        return EntryGroup::model()->with([
                    'movement' => [
                        'select' => ['M.movement_id', 'M.document_code', 'M.document_serie', 'M.document_correlative', 'M.emission_date', 'M.register_date', 'M.observation', 'M.`status`', 'M.route'],
                        'alias' => 'M',
                        'with' => [
                            'scenario' => [
                                'select' => ['S.title', 'S.route', 'S.edit_directly', 'S.dispatch_directly', 'S.pay_directly', 'S.apply_directly', 'S.null_directly', 'S.remove_directly', 'S.accounting_file_id', 'S.not_scheduled_to_pay', 'S.not_scheduled_to_redeem', 'S.allow_logs'],
                                'alias' => 'S',
                                'with' => [
                                    'payableControl' => [
                                        'select' => ['PC.parent_route', 'PC.route'],
                                        'with' => [
                                            'scenario' => [
                                                'select' => ['PCS.route'],
                                                'alias' => 'PCS'
                                            ]
                                        ]
                                    ],
                                    'applicableControl' => [
                                        'select' => ['AC.parent_route', 'AC.route'],
                                        'with' => [
                                            'scenario' => [
                                                'select' => ['ACS.route'],
                                                'alias' => 'ACS'
                                            ]
                                        ]
                                    ],
                                ]
                            ],
                            'ability' => [
                                'select' => [
                                    'movement_id',
                                    'confirm_id'
                                ],
                                'alias' => 'MA',
                            ]
                        ]
                    ],
                    'skill'
                ])->findByAttributes([
                    'pk' => $id
                        ], [
                    'select' => ['EG.movement_id', 'EG.account_code', 'EG.is_payable', 'EG.is_redeemable', 'EG.is_applicable', 'EG.is_schedulable'],
                    'alias' => 'EG'
        ]);
    }

    public function actionAutomaticEntry() {
        try {

            $route = $_POST['route'];

            if ($route == 'accounting/opening') {
                $year = $_POST['year'];
                $a_result = (new SpGetOpeningEntry())->setParams(array('year' => $year - 1))->findAll();
                echo CJSON::encode($a_result);
            } else if (in_array($route, array('accounting/adjust', 'accounting/closing', 'accounting/closingBalance'))) {

                $dynamics = $_POST['dynamics'];
                $isClosingBalanceAccount = $_POST['isClosingBalanceAccount'];
                $period = $_POST['period'];
                $year = $_POST['year'];

                $a_result = [];
                $a_balance_account = [];
                $a_balance_entry = [];

                if (count($dynamics) > 0) {
                    //Cada Asiento
                    $a_accounts = [];
                    foreach ($dynamics as $item) {
                        if ($item['field'] == 'counterpart') {
                            $a_accounts[] = $item['account_code'];
                        }
                    }
                    $a_counterparts = [];
                    if (count($a_accounts) > 0) {
                        $criteria = new CDbCriteria();
                        $criteria->select = array('account_code', "account_name");
                        $criteria->compare('account_code', $a_accounts);

                        $a_counterparts = CHtml::listData(Account::model()->findAll($criteria), 'account_code', 'account_name');
                    }

                    foreach ($dynamics as $item) {

                        $account_code = $item['account_code'];

                        if ($item['field'] == 'balanceAccount') {

                            $sp_result = (new SpGetAccountBalanceByRelation())->setParams(array(
                                        'year' => $year,
                                        'period' => $period,
                                        'account_code' => $account_code,
                                        'with_relation' => $isClosingBalanceAccount,
                                        'include_period' => 1
                                    ))->findAll();

                            foreach ($sp_result as $result) {
                                self::setBalance($a_balance_account, $result["account_code"], $result["balance"], $result["account_name"]);
                            }

                            if (count($sp_result) > 0) {

                                foreach ($sp_result as $result) {

                                    $amount_eval = $result["balance"];
                                    if ($amount_eval <> 0) {
                                        $column = $amount_eval > 0 ? Entry::CREDIT_COLUMN : Entry::DEBIT_COLUMN;
                                        $amount = abs($amount_eval);
                                        $amount_eval = $amount_eval * -1;
                                        $row_entry = array(
                                            'entry_number' => $item['entry_number'],
                                            'column' => $column,
                                            'field' => $item['field'],
                                            'owner' => $result["owner"],
                                            'owner_id' => $result["owner_id"],
                                            'owner_name' => $result["owner_name"],
                                            'account_code' => $result['account_code'],
                                            'account_name' => $result['account_name'],
                                            'amount' => $amount,
                                            'has_combination' => 0, //no debe pedir centro de costos porque la cuentas no generan destinos
                                        );
                                        array_push($a_result, $row_entry);
                                        self::setBalance($a_balance_account, $result["account_code"], $amount_eval, $result["account_name"]);
                                        self::setBalance($a_balance_entry, $item['entry_number'], $amount_eval, '');
                                    }
                                }
                            } else {

                                if (array_key_exists($account_code, $a_balance_account)) {

                                    $amount_eval = $a_balance_account[$account_code]['amount'];
                                    if ($amount_eval <> 0) {

                                        $column = $amount_eval > 0 ? Entry::CREDIT_COLUMN : Entry::DEBIT_COLUMN;
                                        $amount = abs($amount_eval);
                                        $amount_eval = $amount_eval * -1;
                                        $row_entry = array(
                                            'entry_number' => $item['entry_number'],
                                            'column' => $column,
                                            'field' => $item['field'],
                                            'owner' => $item['owner'],
                                            'owner_id' => $item['owner'] == Organization::OWNER ? Yii::app()->controller->getOrganization()->organization_id : NULL,
                                            'owner_name' => $item['owner_name'],
                                            'account_code' => $account_code,
                                            'account_name' => $a_balance_account[$account_code]['name'],
                                            'amount' => $amount,
                                            'has_combination' => 0, //no debe pedir centro de costos porque la cuentas no generan destinos
                                        );
                                        array_push($a_result, $row_entry);
                                        self::setBalance($a_balance_account, $account_code, $amount_eval, $a_balance_account[$account_code]['name']);
                                        self::setBalance($a_balance_entry, $item['entry_number'], $amount_eval, '');
                                    }
                                }
                            }
                        } else if ($item['field'] == 'counterpart') {

                            if (array_key_exists($item['entry_number'], $a_balance_entry)) {

                                $amount_eval = $a_balance_entry[$item['entry_number']]['amount'];
                                if ($amount_eval <> 0) {
                                    $column = $amount_eval > 0 ? Entry::CREDIT_COLUMN : Entry::DEBIT_COLUMN;
                                    $amount = abs($amount_eval);
                                    $amount_eval = $amount_eval * -1;
                                    $row_entry = array(
                                        'entry_number' => $item['entry_number'],
                                        'column' => $column,
                                        'field' => $item['field'],
                                        'owner' => $item['owner'],
                                        'owner_id' => $item['owner'] == Organization::OWNER ? Yii::app()->controller->getOrganization()->organization_id : NULL,
                                        'owner_name' => $item['owner_name'],
                                        'account_code' => $account_code,
                                        'account_name' => $a_counterparts[$account_code],
                                        'amount' => $amount,
                                        'has_combination' => 0, //no debe pedir centro de costos porque la cuentas no generan destinos
                                    );
                                    array_push($a_result, $row_entry);
                                    self::setBalance($a_balance_account, $account_code, $amount_eval, $a_counterparts[$account_code]);
                                    self::setBalance($a_balance_entry, $item['entry_number'], $amount_eval, '');
                                }
                            }
                        }
                    }
                }
                echo CJSON::encode($a_result);
            }
        } catch (Exception $ex) {
            USLog::save($ex);
            throw $ex;
        }
    }

    public function setBalance(&$a_balance, $key, $amount, $account_name) {
        if (array_key_exists($key, $a_balance)) {
            $a_balance[$key]['amount'] = $a_balance[$key]['amount'] + $amount;
        } else {
            $a_balance[$key]['amount'] = $amount;
            $a_balance[$key]['name'] = $account_name;
        }
    }

    public function specifyScenario($p_o_model, $p_s_parent_route = null, $p_s_person_type = Document::PERSON_TYPE_ANY) {
        try {
            $b_skip_stock_validation = $this->checkRoute('skip_stock_validation');
            //Llamamos a la función del modelo
            $p_o_model->specifyScenario($this->module->id, $this->id, $p_s_parent_route, $p_s_person_type, $b_skip_stock_validation);
        } catch (Exception $ex) {
            //Seteamos error
            Yii::app()->user->setFlash('error', $ex->getMessage());
            //Redireccionamos
            if ($p_o_model->isNewRecord) {
                $this->redirect(array('index'));
            } else {
                $this->redirect(array('view', 'id' => $p_o_model->movement_id));
            }
        }
    }

    public function warnAdvancedOptions($p_o_model) {
        //Si tiene opciones avanzadas
        if (isset($p_o_model->advancedOption)) {
            $a_warn = [];
            $p_attributes = $p_o_model->advancedOption->attributes;
            unset($p_attributes['movement_id']);

            foreach ($p_attributes as $s_attribute => $m_value) {
                if ($p_o_model->advancedOption->{$s_attribute} == 1) {
                    $a_warn[] = '- ' . $p_o_model->advancedOption->getAttributeLabel($s_attribute);
                }
            }

            if (count($a_warn) > 0) {
                Yii::app()->user->setFlash('warning', 'Dependiendo de los cambios que realice, podría llegar a perder las siguientes opciones avanzadas:<br>' . implode('<br>', $a_warn));
            }
        }
    }

    public function actionGetPresentations() {
        try {
            $i_mode = $_POST['mode'];
            $a_product_ids = $_POST['product_ids'];
            $s_currency = $_POST['currency'];
            $i_get_cost = isset($_POST['get_cost']) ? $_POST['get_cost'] : 0;
            $i_store_id = isset($_POST['store_id']) ? $_POST['store_id'] : null;
            $s_date = isset($_POST['date']) ? $_POST['date'] : null;
            $i_exclude_id = isset($_POST['exclude_id']) ? $_POST['exclude_id'] : null;
            //
            if (count($a_product_ids) > 0) {
                $a_data = SpGetProductPresentations::getAssociative($i_mode, $a_product_ids, $s_currency, $i_get_cost, $i_store_id, $s_date, $i_exclude_id);
                echo CJSON::encode($a_data);
            } else {
                throw new Exception('Debe especificar al menos un producto para cargar las presentaciones.');
            }
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    public function actionGetCommercialItems($id, $modal_id = null, $parent_id = null) {

        $model = CommercialMovementProduct::model()->with([
                    'itemObj' => [
                        'alias' => 'I',
                        'with' => [
                            'presentation' => [
                                'select' => ['product_id', 'equivalence'],
                                'alias' => 'PR',
                                'with' => [
                                    'product' => [
                                        'select' => ['product_id', 'product_type', 'allow_decimals'],
                                        'alias' => 'P'
                                    ]
                                ]
                            ],
                        ]
                    ],
                    //CommercialMovement
                    'commercialMovement' => [
                        'alias' => 'CM',
                        'with' => [
                            'movement' => [
                                'alias' => 'M'
                            ]
                        ]
                    ]
                ])->findByPk([
            'item_id' => $id
        ]);

        $this->title = "Visualizando ítems de '{$model->toString()}'";

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array(
                'parent_id' => $parent_id,
                'modal_id' => $modal_id
            )
                ), true, true);

        //Items
        $itemsDataProvider = (new SpGetCommercialItems())->setParams([
                    'mode' => SpGetCommercialItems::MODE_CHILD,
                    'scope' => SpGetCommercialItems::SCOPE_INTERNAL,
                    'id' => $id,
                ])->getDataProvider(['product_id', 'equivalence']);

        $content_html = $this->renderPartial("//widget/_commercial_subitems", array(
            'data' => array(
                'model' => $model,
                'itemsDataProvider' => $itemsDataProvider,
                'modal_id' => $modal_id
            )
                ), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html
        ));
    }

    protected function loadCommercial($id) {
        return CommercialMovement::model()->with(array(
                    //Movimiento
                    'movement' => array(
                        'alias' => 'M',
                        'joinType' => 'join',
                        'with' => array(
                            //Scenario
                            'scenario' => [
                                'alias' => 'S',
                            ],
                            //Usuario
                            'person' => array(
                                'alias' => 'PE'
                            ),
                            //Socio de negocio
                            'auxPerson' => array(
                                'alias' => 'XP',
                                'with' => [
                                    'bankAccount'
                                ]
                            ),
                            'documentSerie' => array(
                                'alias' => 'DS',
                                'with' => array(
                                    'printType' => array(
                                        'alias' => 'PT',
                                    ),
                                ),
                            ),
                            'document' => array(
                                'alias' => 'D',
                            ),
                            'addresses',
                            'ability' => [
                                'select' => ['AB.movement_id', 'AB.confirm_id'],
                                'alias' => 'AB',
                                'with' => array(
                                    'confirmation' => array(
                                        'select' => ['C.option_type', 'C.confirm_id'],
                                        'alias' => 'C'
                                    )
                                )
                            ],
                            'accountingMovement' => [
                                'alias' => 'AM'
                            ],
                            'advancedOption' => [
                                'alias' => 'AO',
                            ],
                            'extraInfo' => [
                                'alias' => 'EI',
                                'with' => [
                                    'shippingType'
                                ]
                            ],
                            'businessUnit' => [
                                'alias' => 'BU',
                                'with' => [
                                    'combination' => [
                                        'alias' => 'BUCC'
                                    ]
                                ]
                            ],
                            'payments',
                        )
                    ),
                    //ITEMS
                    'items' => array(
                        'alias' => 'I',
                        'on' => 'I.group_id IS NULL',
                        'select' => 'I.*, M.store_id AS store_id',
                        'with' => array(
                            'itemObj' => [
                                'alias' => 'II',
                                'with' => [
                                    'presentation' => array(
                                        'alias' => 'PR',
                                        'with' => array(
                                            'product' => array(
                                                'alias' => 'P',
                                                'with' => [
                                                    'prices' => [
                                                        'alias' => 'PRI',
                                                        'with' => [
                                                            'stores.children.store',
                                                            'pricePresentations' => [
                                                                'alias' => 'PRIP',
                                                                'select' => ['PRIP.*', 'P.price_currency AS currency'],
                                                                'on' => 'PRIP.equivalence = PR.equivalence',
                                                            ]
                                                        ],
                                                    ]
                                                ]
                                            )
                                        )
                                    ),
                                ]
                            ],
                            //En caso de ser grupo
                            'items' => [
                                'alias' => 'GI',
                                'with' => [
                                    'itemObj' => [
                                        'alias' => 'GII',
                                        'with' => [
                                            'presentation' => array(
                                                'alias' => 'GPR',
                                                'with' => array(
                                                    'product' => array(
                                                        'alias' => 'GP'
                                                    )
                                                )
                                            ),
                                        ]
                                    ],
                                ]
                            ],
                            //En caso sea dua
                            'duaProduct' => [
                                'alias' => 'DP',
                            ]
                        ),
                    )
                ))->findByPk(array('movement_id' => $id), array(
                    'order' => 'II.item_number, GII.item_number',
        ));
    }

    protected function loadCommercials($a_ids) {
        return CommercialMovement::model()->with(array(
                    //Movimiento
                    'movement' => array(
                        'alias' => 'M',
                        'joinType' => 'join',
                        'with' => array(
                            //Scenario
                            'scenario' => [
                                'alias' => 'S',
                            ],
                            //Usuario
                            'person' => array(
                                'alias' => 'PE'
                            ),
                            //Socio de negocio
                            'auxPerson' => array(
                                'alias' => 'XP',
                            ),
                            'documentSerie' => array(
                                'alias' => 'DS',
                                'with' => array(
                                    'printType' => array(
                                        'alias' => 'PT',
                                    ),
                                ),
                            ),
                            'document' => array(
                                'alias' => 'D',
                            ),
                            'addresses',
                            'ability' => [
                                'select' => ['AB.movement_id', 'AB.confirm_id'],
                                'alias' => 'AB',
                                'with' => array(
                                    'confirmation' => array(
                                        'select' => ['C.option_type', 'C.confirm_id'],
                                        'alias' => 'C'
                                    )
                                )
                            ],
                            'accountingMovement' => [
                                'alias' => 'AM'
                            ],
                            'advancedOption' => [
                                'alias' => 'AO',
                            ],
                            'extraInfo' => [
                                'alias' => 'EI',
                            ],
                            'businessUnit' => [
                                'alias' => 'BU',
                                'with' => [
                                    'combination' => [
                                        'alias' => 'BUCC'
                                    ]
                                ]
                            ]
                        )
                    ),
                    //ITEMS
                    'items' => array(
                        'alias' => 'I',
                        'on' => 'I.group_id IS NULL',
                        'select' => 'I.*, M.store_id AS store_id',
                        'with' => array(
                            'itemObj' => [
                                'alias' => 'II',
                                'with' => [
                                    'presentation' => array(
                                        'alias' => 'PR',
                                        'with' => array(
                                            'product' => array(
                                                'alias' => 'P',
                                                'with' => [
                                                    'prices' => [
                                                        'alias' => 'PRI',
                                                        'with' => [
                                                            'stores.children.store',
                                                            'pricePresentations' => [
                                                                'alias' => 'PRIP',
                                                                'select' => ['PRIP.*', 'P.price_currency AS currency'],
                                                                'on' => 'PRIP.equivalence = PR.equivalence',
                                                            ]
                                                        ],
                                                    ]
                                                ]
                                            )
                                        )
                                    ),
                                ]
                            ]
                            ,
                            //En caso de ser grupo
                            'items' => [
                                'alias' => 'GI',
                                'with' => [
                                    'itemObj' => [
                                        'alias' => 'GII',
                                        'with' => [
                                            'presentation' => array(
                                                'alias' => 'GPR',
                                                'with' => array(
                                                    'product' => array(
                                                        'alias' => 'GP'
                                                    )
                                                )
                                            ),
                                        ]
                                    ],
                                ]
                            ],
                            //En caso sea dua
                            'duaProduct' => [
                                'alias' => 'DP',
                            ]
                        )
                    )
                ))->findAllByAttributes(array('movement_id' => $a_ids), array(
                    'order' => 'II.item_number, GII.item_number',
        ));
    }

    protected function loadOrder($id) {
        return CommercialMovement::model()->with(array(
                    //Movimiento
                    'movement' => array(
                        'alias' => 'M',
                        'joinType' => 'join',
                        'with' => array(
                            //Scenario
                            'scenario' => [
                                'alias' => 'S',
                            ],
                            //Usuario
                            'person' => array(
                                'alias' => 'PE'
                            ),
                            //Socio de negocio
                            'auxPerson' => array(
                                'alias' => 'XP',
                            ),
                            'documentSerie' => array(
                                'alias' => 'DS',
                                'with' => array(
                                    'printType' => array(
                                        'alias' => 'PT',
                                    ),
                                ),
                            ),
                            'document' => array(
                                'alias' => 'D',
                            ),
                            'ability' => [
                                'select' => ['AB.movement_id', 'AB.confirm_id'],
                                'alias' => 'AB',
                                'with' => array(
                                    'confirmation' => array(
                                        'select' => ['C.option_type', 'C.confirm_id'],
                                        'alias' => 'C'
                                    )
                                )
                            ],
                            'accountingMovement' => [
                                'alias' => 'AM'
                            ],
                            'advancedOption' => [
                                'alias' => 'AO',
                            ],
                            'extraInfo' => [
                                'alias' => 'EI',
                            ]
                        )
                    )
                ))->findByPk(array('movement_id' => $id), []);
    }

    protected function loadServiceOrder($id) {
        return CommercialMovement::model()->with(array(
                    //Movimiento
                    'movement' => array(
                        'alias' => 'M',
                        'joinType' => 'join',
                        'with' => array(
                            //Scenario
                            'scenario' => [
                                'alias' => 'S',
                            ],
                            //Usuario
                            'person' => array(
                                'alias' => 'PE'
                            ),
                            //Socio de negocio
                            'auxPerson' => array(
                                'alias' => 'XP',
                            ),
                            'documentSerie' => array(
                                'alias' => 'DS',
                                'with' => array(
                                    'printType' => array(
                                        'alias' => 'PT',
                                    ),
                                ),
                            ),
                            'document' => array(
                                'alias' => 'D',
                            ),
                            'ability' => [
                                'select' => ['AB.movement_id', 'AB.confirm_id'],
                                'alias' => 'AB',
                                'with' => array(
                                    'confirmation' => array(
                                        'select' => ['C.option_type', 'C.confirm_id'],
                                        'alias' => 'C'
                                    )
                                )
                            ],
                            'accountingMovement' => [
                                'alias' => 'AM'
                            ],
                            'advancedOption' => [
                                'alias' => 'AO',
                            ],
                            'extraInfo' => [
                                'alias' => 'EI',
                            ],
                            'logs' => [
                                'alias' => 'MLI',
                            ],
                        )
                    ),
                    //ITEMS
                    'items' => array(
                        'alias' => 'I',
                        'on' => 'I.group_id IS NULL',
                        'with' => array(
                            'itemObj' => [
                                'alias' => 'II',
                                'with' => [
                                    'presentation' => array(
                                        'alias' => 'PR',
                                        'with' => array(
                                            'product' => array(
                                                'alias' => 'P'
                                            )
                                        )
                                    ),
                                ]
                            ],
                            //En caso de ser grupo
                            'items' => [
                                'alias' => 'GI',
                                'with' => [
                                    'itemObj' => [
                                        'alias' => 'GII',
                                        'with' => [
                                            'presentation' => array(
                                                'alias' => 'GPR',
                                                'with' => array(
                                                    'product' => array(
                                                        'alias' => 'GP'
                                                    )
                                                )
                                            ),
                                        ]
                                    ],
                                ]
                            ]
                        ),
                    ),
                    //Service Order si tuviera
                    'serviceOrder' => [
                        'alias' => 'SO',
                    ]
                ))->findByPk(array('movement_id' => $id), []);
    }

    public function loadMobility($id) {
        return MobilityMovement::model()->with(array(
                    'movement' => array(
                        'alias' => 'M',
                        'with' => array(
                            'document' => array(
                                'alias' => 'D',
                            ),
                            'accountingMovement' => array(
                                'alias' => 'AM'
                            ),
                            'advancedOption',
                            'extraInfo',
                            'businessUnit' => [
                                'alias' => 'BU',
                                'with' => [
                                    'combination' => [
                                        'alias' => 'BUCC'
                                    ]
                                ]
                            ]
                        )
                    ),
                    'mobilityPeople' => array(
                        'alias' => 'MP',
                        'with' => array(
                            'person' => array(
                                'alias' => 'P',
                                'with' => array(
                                    'employee' => array(
                                        'alias' => 'E',
                                        'with' => array(
                                            'station' => array(
                                                'alias' => 'S',
                                                'with' => array(
                                                    'area' => array(
                                                        'alias' => 'A',
                                                        'with' => [
                                                            'combination' => [
                                                                'alias' => 'ACC',
                                                            ]
                                                        ]
                                                    )
                                                )
                                            )
                                        )
                                    )
                                )
                            ),
                            'combination' => [
                                'alias' => 'CC',
                            ],
                            'items' => array(
                                'alias' => 'MPI'
                            ),
                        )
                    ),
                ))->findByPk(array('movement_id' => $id));
    }

    public function loadWarehouse($id) {
        return WarehouseMovement::model()->with(array(
                    'movement' => array(
                        'alias' => 'M',
                        'with' => array(
                            'scenario' => array(
                                'alias' => 'S',
                            ),
                            'linkParent' => [
                                'alias' => 'LP',
                                'with' => [
                                    'warehouseParent' => [
                                        'alias' => 'WP',
                                        'with' => [
                                            'movement' => [
                                                'alias' => 'WPM'
                                            ]
                                        ]
                                    ]
                                ]
                            ],
                            'advancedOption',
                            'addresses.geoloc',
                            'extraInfo' => [
                                'alias' => 'EI'
                            ]
                        )
                    ),
                    'company' => array(
                        'alias' => 'COM'
                    ),
                    'reasonTransfer',
                    'shipperParent' => array(
                        'with' => array(
                            'children' => [
                                'alias' => 'CHS',
                                'with' => [
                                    'shipper'
                                ]
                            ]
                        )
                    ),
                    'transportUnitParent' => array(
                        'with' => array(
                            'children' => [
                                'alias' => 'CHTU',
                                'with' => [
                                    'transportUnit'
                                ]
                            ]
                        )
                    ),
                    //ITEMS
                    'items' => [
                        'alias' => 'I',
                        'with' => [
                            'itemObj' => [
                                'alias' => 'II',
                                'with' => [
                                    'presentation' => array(
                                        'alias' => 'PR',
                                        'with' => array(
                                            'product' => array(
                                                'alias' => 'P',
                                                'with' => [
                                                    'merchandise'
                                                ]
                                            )
                                        )
                                    ),
                                ]
                            ],
                            'series' => [
                                'alias' => 'WS'
                            ]
                        ],
                    ],
                    'warehouse' => [
                        'alias' => 'W',
                        'with' => [
                            'store' => [
                                'alias' => 'ST'
                            ]
                        ]
                    ]
                ))->findByPk(array('movement_id' => $id), array(
                    'alias' => 'WM',
                    'order' => 'II.item_number',
        ));
    }

    public function loadProductList($id) {
        return ProductListMovement::model()->with(array(
                    //Movimiento
                    'movement' => [
                        'alias' => 'M',
                        'with' => [
                            //Empleado
                            'person' => array(
                                'alias' => 'PE'
                            ),
                            //Receptor
                            'auxPerson' => array(
                                'alias' => 'PX',
                                'with' => array(
                                    'user' => array(
                                        'alias' => 'PXU',
                                    ),
                                    'employee' => array(
                                        'alias' => 'EM'
                                    ),
                                )
                            ),
                            'documentSerie' => array(
                                'alias' => 'DS',
                                'with' => array(
                                    'printType' => array(
                                        'alias' => 'PT',
                                    ),
                                ),
                            ),
                            'document' => array(
                                'alias' => 'D',
                            ),
                            'addresses' => array(
                                'alias' => 'MA',
                                'with' => array(
                                    'geoloc' => array(
                                        'alias' => 'MAG'
                                    )
                                )
                            ),
                            'accountingMovement' => array(
                                'alias' => 'AM',
                                'with' => array(
                                    'accountingDay' => array(
                                        'alias' => 'AD',
                                    ),
                                    'accountingFile' => array(
                                        'alias' => 'AF',
                                    )
                                )
                            ),
                            'ability' => [
                                'select' => ['AB.movement_id', 'AB.confirm_id'],
                                'alias' => 'AB',
                                'with' => array(
                                    'confirmation' => array(
                                        'select' => ['C.option_type', 'C.confirm_id'],
                                        'alias' => 'C'
                                    )
                                )
                            ],
                            'advancedOption' => [
                                'alias' => 'AO'
                            ],
                            'extraInfo' => [
                                'select' => ['movement_id', 'warehouse_id', 'aux_warehouse_id', 'kardex_rlock', 'kardex_clock', 'dispatch_document_code', 'transport_company_id', 'shipping_type', 'contact_identification_number', 'contact_name', 'contact_email', 'contact_phone'],
                                'alias' => 'EI',
                                'with' => [
                                    'combination' => [
                                        'select' => ['combination_id', 'combination_name']
                                    ],
                                    'warehouse' => [
                                        'alias' => 'EIW',
                                        'with' => [
                                            'store' => [
                                                'alias' => 'EIS'
                                            ]
                                        ]
                                    ],
                                    'auxWarehouse' => [
                                        'select' => ['warehouse_name', 'store_id'],
                                        'alias' => 'EIXW',
                                    ]
                                ]
                            ],
                            'businessUnit' => [
                                'alias' => 'BU',
                                'with' => [
                                    'combination' => [
                                        'alias' => 'BUCC'
                                    ]
                                ]
                            ],
                            'linkParent'
                        ]
                    ],
                    'items' => [
                        'alias' => 'PLI',
                        'with' => [
                            'itemObj' => [
                                'alias' => 'PLII',
                                'with' => [
                                    'presentation' => [
                                        'alias' => 'PLIPR',
                                        'with' => [
                                            'product' => [
                                                'alias' => 'PLIP',
                                            ],
                                        ]
                                    ],
                                    'combination' => [
                                        'alias' => 'CC'
                                    ]
                                ]
                            ],
                        ]
                    ],
                    'transformationResults' => [
                        'alias' => 'TR',
                        'with' => [
                            'itemObj' => [
                                'alias' => 'TRI',
                                'with' => [
                                    'presentation' => [
                                        'alias' => 'PR',
                                        'with' => [
                                            'product' => [
                                                'alias' => 'P',
                                            ]
                                        ]
                                    ]
                                ]
                            ],
                        ]
                    ],
                    'company' => array(
                        'alias' => 'COM'
                    ),
                    'reasonTransfer',
                    'shipperParent' => array(
                        'with' => array(
                            'children' => [
                                'alias' => 'CHS',
                                'with' => [
                                    'shipper'
                                ]
                            ]
                        )
                    ),
                    'transportUnitParent' => array(
                        'with' => array(
                            'children' => [
                                'alias' => 'CHTU',
                                'with' => [
                                    'transportUnit'
                                ]
                            ]
                        )
                    ),
                ))->findByPk(array('owner' => Movement::OWNER, 'owner_id' => $id), array(
                    'alias' => 'PL',
                    'order' => 'PLII.item_number',
                        )
        );
    }

    public function loadProductListTemplate($id) {
        return ProductListMovement::model()->with(array(
                    'movement' => [
                        'alias' => 'M',
                        'with' => [
                            //Empleado
                            'person' => array(
                                'alias' => 'PER'
                            ),
                            //Receptor
                            'auxPerson' => array(
                                'alias' => 'PX',
                                'with' => array(
                                    'user' => array(
                                        'alias' => 'PXU',
                                    ),
                                    'employee' => array(
                                        'alias' => 'EM',
                                        'with' => array(
                                            'station' => array(
                                                'alias' => 'PXS',
                                                'with' => array(
                                                    'area' => array(
                                                        'alias' => 'PXA'
                                                    ),
                                                )
                                            ),
                                        )
                                    )
                                )
                            ),
                            'documentSerie' => array(
                                'alias' => 'DS',
                                'with' => array(
                                    'printType' => array(
                                        'alias' => 'PT',
                                    ),
                                ),
                            ),
                            'document' => array(
                                'alias' => 'D',
                            ),
                            'accountingMovement' => array(
                                'alias' => 'AM',
                                'with' => array(
                                    'accountingDay' => array(
                                        'alias' => 'AD',
                                    ),
                                    'accountingFile' => array(
                                        'alias' => 'AF',
                                    )
                                )
                            ),
                            'ability' => [
                                'select' => ['AB.movement_id', 'AB.confirm_id'],
                                'alias' => 'AB',
                                'with' => array(
                                    'confirmation' => array(
                                        'select' => ['C.option_type', 'C.confirm_id', 'C.user_request'],
                                        'alias' => 'C'
                                    )
                                )
                            ],
                            'businessUnit' => [
                                'alias' => 'BU',
                                'with' => [
                                    'combination' => [
                                        'alias' => 'BUCC'
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'transformationResults' => [
                        'alias' => 'TR',
                        'with' => [
                            'itemObj' => [
                                'alias' => 'TRI',
                                'with' => [
                                    'presentation' => [
                                        'alias' => 'PR',
                                        'with' => [
                                            'product' => [
                                                'alias' => 'P',
                                            ]
                                        ]
                                    ]
                                ]
                            ],
                        ]
                    ],
                ))->findByPk([
                    'owner' => Movement::OWNER,
                    'owner_id' => $id
                        ], array(
                    'alias' => 'PL',
        ));
    }

    public function loadLoan($id) {
        return LoanMovement::model()->with(array(
                    'movement' => array(
                        'alias' => 'M',
                        'with' => array(
                            'document' => [
                                'alias' => 'D',
                            ],
                            'accountingMovement' => array(
                                'alias' => 'AM',
                            ),
                            'extraInfo' => [
                                'alias' => 'EI',
                                'with' => [
                                    'combination'
                                ]
                            ],
                            'advancedOption'
                        )
                    ),
                    //Items
                    'fees' => array(
                        'alias' => 'F',
                    ),
                ))->findByPk(array('movement_id' => $id), array(
                    'order' => 'F.fee_number',
        ));
    }

    public function loadDynamic($id) {
        return DynamicMovement::model()->with(array(
                    'movement' => [
                        'alias' => 'M',
                        'with' => [
                            'addresses',
                            'document',
                            'accountingMovement' => array(
                                'alias' => 'AM',
                                'with' => array(
                                    'entries' => array(
                                        'alias' => 'E',
                                        //'on' => "!E.destiny AND IF(M.currency = '" . Currency::PEN . "', E.amount_pen, E.amount_usd) > 0", //Sólo las cuentas que no son destino
                                        'on' => "!E.destiny AND (E.dynamic_account IS NULL OR E.dynamic_account NOT IN ('" . Account::WILDCARD_MISSING . "', '" . Account::WILDCARD_SURPLUS . "'))",
                                        'with' => array(
                                            'account' => [
                                                'select' => ['account_name', 'owner', 'has_combination', 'destiny_type'],
                                                'alias' => 'A',
                                            ],
                                            'combination' => [
                                                'select' => ['combination_id', 'combination_name'],
                                                'alias' => 'CC',
                                            ],
                                            'ownerName' => [
                                                'select' => ['OWN.owner_aux', 'OWN.owner_name'],
                                                'alias' => 'OWN',
                                            ]
                                        )
                                    )
                                )
                            ),
                            'extraInfo' => [
                                'alias' => 'EI'
                            ]
                        ]
                    ],
                ))->findByPk(array('movement_id' => $id), [
                    'alias' => 'DM',
                    'order' => 'E.entry_number, E.`order`'
        ]);
    }

    public function actionViewAttachments() {

        try {
            $a_movement_code = isset($_POST['movement_code']) ? $_POST['movement_code'] : null;
            $s_sub_path = isset($_POST['sub_path']) ? $_POST['sub_path'] : null;
            $s_property_count = isset($_POST['property_count']) ? $_POST['property_count'] : null;
            $s_movement_code = implode("','", $a_movement_code);

            $a_params = [
                ':owner' => Movement::OWNER,
                ':type' => Resource::TYPE_ATTACHMENT
            ];
            if (isset($s_sub_path)) {
                $a_params["sub_type"] = $s_sub_path;
            } else {
                $a_params["sub_type"] = Resource::SUB_TYPE_DEFAULT;
            }
            if (isset($s_movement_code)) {
                $a_resources = Yii::app()->db->createCommand()->select([
                            "R.title AS key",
                            "R.description AS filename",
                            "R.url AS filepath",
                            "R.width",
                            "R.height",
                            "M.movement_id",
                            "M.movement_code"
                        ])
                        ->from('resource R')
                        ->join('movement M', 'M.movement_id = R.owner_id AND M.movement_code IN (\'' . implode('\',\'', $a_movement_code) . '\')')
                        ->where("R.`owner` = :owner AND R.type = :type AND R.sub_type = :sub_type", $a_params)
                        ->queryAll();

                $a_attachments = [];
                //URL es en realidad el filepath
                foreach ($a_resources as $a_resource) {
                    //Verificamos si el archivo existe físicamente
                    if (file_exists($a_resource['filepath'])) {

                        $s_type = SIANAttachment::getAttachmentType($a_resource['filepath']);
                        $s_content = '';
                        //Si es un archivo de text
                        if (in_array($s_type, ['text', 'html'])) {
                            $s_content = file_get_contents($a_resource['filepath']);
                        }

                        $a_attachments[] = [
                            'type' => $s_type,
                            'key' => $a_resource['key'],
                            'preview_url' => Yii::app()->params['files_url'] . '/attachments/' . $a_resource['movement_code'] . '/' . (isset($s_sub_path) ? $s_sub_path . '/' : '') . $a_resource['filename'],
                            'caption' => $a_resource['filename'],
                            'content' => $s_content,
                            'size' => filesize($a_resource['filepath']),
                            'width' => isset($a_resource['width']) ? $a_resource['width'] . 'px' : null,
                            'height' => isset($a_resource['height']) ? $a_resource['height'] . 'px' : null,
                            'downloadUrl' => $this->createUrl('downloadAttachments') . '?folder=' . $a_resource['movement_code'] . '&key=' . $a_resource['key'],
                            'url' => $this->createUrl('deleteAttachments') . '?folder=' . $a_resource['movement_code'] . '&key=' . $a_resource['key'] . '&property_count=' . $s_property_count . '&sub_type=' . $s_sub_path,
                        ];
                    } else {
                        $a_attachments[] = [
                            'type' => SIANAttachment::TYPE_IMAGE,
                            'key' => $a_resource['key'],
                            'preview_url' => Yii::app()->params['admin_url'] . "/images/attachment/404.jpg",
                            'caption' => 'Archivo no encontrado',
                            'content' => '',
                            'size' => 0,
                            'width' => null,
                            'height' => null,
                            'downloadUrl' => false,
                            'url' => $this->createUrl('deleteAttachments') . '?folder=' . $a_resource['movement_code'] . '&key=' . $a_resource['key'] . '&property_count=' . $s_property_count . '&sub_type=' . $s_sub_path,
                        ];
                    }
                }

                echo CJSON::encode(array(
                    'code' => USREST::CODE_SUCCESS,
                    'message' => 'Success',
                    'attachments' => $a_attachments
                ));
            } else {
                echo CJSON::encode(array(
                    'code' => USREST::CODE_BAD_REQUEST,
                    'message' => 'No se ha especificado el código del movimiento',
                ));
            }
        } catch (CDbException $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => $ex->getMessage()
            ]);
        } catch (Exception $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => $ex->getMessage()
            ]);
        }
    }

    public function actionSortAttachments() {

        try {
            $i_movement_id = isset($_POST['movement_id']) ? $_POST['movement_id'] : null;
            $a_keys = isset($_POST['keys']) ? $_POST['keys'] : null;
            $s_property_count = isset($_POST['property_count']) ? $_POST['property_count'] : null;
            //
            if (isset($i_movement_id, $a_keys)) {
                //Obtenemos movement
                $o_model = $this->_getMovement($i_movement_id, false);
                //Comparamos si coincide la cantidad de adjuntos
                if ($o_model->movement->{$s_property_count} == count($a_keys)) {
                    //Actualizamos en BD, sólo si hay más de un adjunto
                    if ($o_model->movement->{$s_property_count} > 1) {
                        $this->_sortAttachments($o_model, $a_keys);
                    }
                    //
                    echo CJSON::encode(array(
                        'code' => USREST::CODE_SUCCESS,
                        'message' => 'Success',
                    ));
                } else {
                    echo CJSON::encode(array(
                        'code' => USREST::CODE_BAD_REQUEST,
                        'message' => 'La lista de adjuntos no coincide con la lista de claves enviada.',
                    ));
                }
            } else {
                echo CJSON::encode(array(
                    'code' => USREST::CODE_BAD_REQUEST,
                    'message' => 'Debe especificar el ID del movimiento y los códigos de los adjuntos',
                ));
            }
        } catch (CDbException $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => $ex->getMessage()
            ]);
        } catch (Exception $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => $ex->getMessage()
            ]);
        }
    }

    /**
     * Se debe crear la carpeta attachments en files
     */
    public function actionUploadAttachments() {

        header('Content-Type: application/json');

        $a_preview = [];
        $a_configs = [];
        $a_resources = [];
        $a_errors = [];

        try {

            $s_input = 'MovementAttachment'; // the input name for the fileinput plugin

            if (empty($_FILES[$s_input])) {
                return [];
            }

            //Variables
            $s_key = USTime::getDateCode();

            //Files
            $a_files = $_FILES[$s_input];

            $a_allowed_extensions = ['jpg', 'jpeg', 'png', 'pdf', 'xls', 'xlsx', 'xlsm', 'doc', 'docx', 'xml'];
            // Validar cada archivo individualmente
            for ($i = 0; $i < count($a_files['name']); $i++) {
                $s_file_name = $a_files['name'][$i];
                // Obtener la extensión del archivo
                $s_file_extension = strtolower(pathinfo($s_file_name, PATHINFO_EXTENSION));
                // Validar la extensión
                if (!in_array($s_file_extension, $a_allowed_extensions)) {
                    $a_errors[] = $s_file_name . ' archivo inválido. Sólo se permiten archivos con extensiones JPG, JPEG, PNG, DOC, DOCX, XLS, XLSX, XLSM, XML y PDF.';
                    goto Finish;
                }
            }

            $i_movement_id = isset($_POST['movement_id']) ? $_POST['movement_id'] : null;
            $s_movement_code = isset($_POST['movement_code']) ? $_POST['movement_code'] : null;
            $s_sub_path = isset($_POST['sub_path']) ? $_POST['sub_path'] : null;
            $s_property_count = isset($_POST['property_count']) ? $_POST['property_count'] : null;
            $s_limit = isset($_POST['limit']) ? $_POST['limit'] : null;
            //Generamos path
            $s_path = Yii::app()->params['files_dir'] . DIRECTORY_SEPARATOR . 'attachments' . DIRECTORY_SEPARATOR;
            //Obtenemos modelo
            $o_model = $this->_getMovement($i_movement_id, false);
            //Verificamos si el movimiento está activo
            if ($o_model->movement->status == 0) {
                $a_errors[] = 'No se puede adjutar archivos a un movimiento anulado.';
                goto Finish;
            }
            //Verificamos la cantidad de adjuntos
            $i_limit = isset($s_limit) ? intval($s_limit) : $this->getOrganization()->globalVar->max_attachments;
            if ($o_model->movement->{$s_property_count} + count($a_files['name']) > $i_limit) {
                $a_errors[] = 'El máximo de archivos permitidos es ' . $i_limit;
                goto Finish;
            }
            //Creamos carpeta si no existe
            if (!file_exists($s_path . $s_movement_code)) {
                mkdir($s_path . $s_movement_code, 0755);
            }
            if (isset($s_sub_path)) {
                if (!file_exists($s_path . $s_movement_code . DIRECTORY_SEPARATOR . $s_sub_path)) {
                    mkdir($s_path . $s_movement_code . DIRECTORY_SEPARATOR . $s_sub_path, 0755);
                }
            }

            //Verificamos si el archivo existe
            $a_normalized = [];
            for ($i = 0; $i < count($a_files['name']); $i++) {
                //Obtenemos
                $s_tmp_filepath = $a_files['tmp_name'][$i];
                $s_filename = $a_files['name'][$i];
                $i_filesize = $a_files['size'][$i];
                //Make sure we have a file path
                if ($s_tmp_filepath != "") {
                    //Seteamos nueva ruta
                    $s_new_filepath = $s_path . $s_movement_code . DIRECTORY_SEPARATOR . (isset($s_sub_path) ? $s_sub_path . DIRECTORY_SEPARATOR : '') . $s_filename;
                    $s_new_url = Yii::app()->params['files_url'] . '/attachments/' . $s_movement_code . '/' . (isset($s_sub_path) ? $s_sub_path . '/' : '') . $s_filename;
                    //Verificamos si ya existe el archivo
                    if (!file_exists($s_new_filepath)) {
                        $a_normalized[] = [
                            'tmp_filepath' => $s_tmp_filepath,
                            'filename' => $s_filename,
                            'filesize' => $i_filesize,
                            'new_filepath' => $s_new_filepath,
                            'new_url' => $s_new_url,
                            'sub_type' => $s_sub_path,
                        ];
                    } else {
                        $a_errors[] = 'El archivo ' . $s_filename . ' ya existe.';
                    }
                } else {
                    $a_errors[] = 'El archivo ' . $s_filename . ' no subió correctamente.';
                }
            }

            foreach ($a_normalized as $a_item) {
                //Upload the file into the new path
                if (move_uploaded_file($a_item['tmp_filepath'], $a_item['new_filepath'])) {

                    $s_key = USTime::getDateCode();
                    $a_preview[] = $a_item['new_url'];
                    $s_type = SIANAttachment::getAttachmentType($a_item['new_filepath']);
                    //
                    $a_config = [
                        'type' => $s_type,
                        'key' => $s_key,
                        'caption' => $a_item['filename'],
                        'content' => '',
                        'size' => $a_item['filesize'],
                        'downloadUrl' => $this->createUrl('downloadAttachments') . '?folder=' . $s_movement_code . '&key=' . $s_key . '&property_count=' . $s_property_count,
                        'url' => $this->createUrl('deleteAttachments') . '?folder=' . $s_movement_code . '&key=' . $s_key . '&property_count=' . $s_property_count . '&sub_type=' . $s_sub_path,
                    ];
                    //
                    switch ($s_type) {
                        case 'text':
                        case 'html':
                            $a_config['content'] = file_get_contents($a_item['new_filepath']);
                            break;
                        case 'video':
                            $a_config['downloadUrl'] = $a_item['new_url'];
                            $a_config['filename'] = $a_item['filename'];
                            $a_config['filetype'] = "video/mp4";
                            break;
                        default:
                            break;
                    }
                    //
                    $a_configs[] = $a_config;
                    //Guardamos los adjuntos
                    $a_resources[] = [
                        'type' => Resource::TYPE_ATTACHMENT,
                        'url' => $a_item['new_filepath'],
                        'title' => $s_key,
                        'description' => $a_item['filename'],
                        'width' => null,
                        'height' => null
                    ];
                } else {
                    $a_errors[] = 'Ocurrió un error al procesar el archivo ' . $s_filename;
                }
            }

            //Si hay errores
            if (count($a_errors) > 0) {
                goto Finish;
            } else {
                //Guardamos los adjuntos
                $this->_saveAttachments($o_model, $a_resources, $s_property_count, $s_sub_path);
            }
        } catch (CDbException $ex) {
            $a_errors[] = $ex->getMessage();
        } catch (Exception $ex) {
            $a_errors[] = $ex->getMessage();
        }

        Finish:
        $a_out = [
            'initialPreview' => $a_preview,
            'initialPreviewConfig' => $a_configs,
            'initialPreviewAsData' => true
        ];
        if (count($a_errors) > 0) {
            $a_out['error'] = '<p>' . implode('</p><p>', $a_errors) . '</p>';
        }
        echo CJSON::encode($a_out);
    }

    public function actionDownloadAttachments() {

        $s_folder = isset($_GET['folder']) ? $_GET['folder'] : null;
        $s_key = isset($_GET['key']) ? $_GET['key'] : null;

        $a_resource = SIANAttachment::getResource($s_folder, $s_key);

        if (file_exists($a_resource['filepath'])) {
            header('Content-Description: File Transfer');
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . basename($a_resource['filename']) . '"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($a_resource['filepath']));
            readfile($a_resource['filepath']);
        }
    }

    public function actionDeleteAttachments() {

        try {
            $s_folder = isset($_GET['folder']) ? $_GET['folder'] : null;
            $s_key = isset($_GET['key']) ? $_GET['key'] : null;
            $s_property_count = isset($_GET['property_count']) ? $_GET['property_count'] : null;
            $s_sub_type = (isset($_GET['sub_type']) && $_GET['sub_type'] != "") ? $_GET['sub_type'] : Resource::SUB_TYPE_DEFAULT;
            //Obtenemos recursos
            $a_resource = SIANAttachment::getResource($s_folder, $s_key);
            //Si es false es porque no se pudo obtener nada
            if ($a_resource) {
                //Obtenemos movement
                $o_model = $this->_getMovement($a_resource['movement_id'], false);
                //Verificamos si está activo
                if ($o_model->movement->status == 1) {
                    //Eliminamos si el archivo existe
                    if (file_exists($a_resource['filepath'])) {
                        unlink($a_resource['filepath']);
                    }

                    //Elimamos de la BD
                    $this->_deleteResource($o_model, $a_resource['key'], $s_property_count, $s_sub_type);

                    echo CJSON::encode(array(
                        'code' => USREST::CODE_SUCCESS,
                        'message' => 'Archivo borrado exitosamente',
                    ));
                } else {
                    echo CJSON::encode([
                        'code' => USREST::CODE_BAD_REQUEST,
                        'error' => $ex->getMessage()
                    ]);
                }
            } else {
                echo CJSON::encode([
                    'code' => USREST::CODE_BAD_REQUEST,
                    'error' => 'No se pudo obtener el adjunto a eliminar.'
                ]);
            }
        } catch (CDbException $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'error' => $ex->getMessage()
            ]);
        } catch (Exception $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'error' => $ex->getMessage()
            ]);
        }
    }

    private function _saveAttachments($p_o_model, &$p_a_resources, $p_s_property_count, $p_s_sub_type) {

        try {
            $p_s_property_count = isset($p_s_property_count) ? $p_s_property_count : 'attachment_count';

            $this->beginTransaction($p_o_model);
            //
            for ($i = 0; $i < count($p_a_resources); $i++) {
                if (isset($p_s_sub_type) && $p_s_sub_type != '') {
                    $p_a_resources[$i]['sub_type'] = $p_s_sub_type;
                } else {
                    $p_a_resources[$i]['sub_type'] = Resource::SUB_TYPE_DEFAULT;
                }
                $p_a_resources[$i]['owner'] = Movement::OWNER;
                $p_a_resources[$i]['owner_id'] = $p_o_model->movement->movement_id;
                $p_a_resources[$i]['resource_number'] = ($p_o_model->movement[$p_s_property_count] + $i + 1);
            }
            //Insertamos recursos
            USDatabase::multipleInsert('Resource', $p_a_resources);
            //Actualizamos contador
            $i_count = $p_o_model->movement[$p_s_property_count] + count($p_a_resources);
            //Definimos arrays de valores y cambios
            $a_columns = [
                $p_s_property_count => $i_count
            ];
            $a_changes = [
                $p_s_property_count => [
                    'previous' => $p_o_model->movement[$p_s_property_count],
                    'current' => ($p_o_model->movement[$p_s_property_count] + count($p_a_resources))
                ]
            ];
            if (isset($p_s_sub_type)) {

                $s_field = $p_o_model->movement->getCustomCategoryField();

                if (isset($s_field)) {
                    if ($p_s_property_count == $s_field . "_count") {

                        $s_new_value = $p_o_model->movement->getCustomCategoryAutomaticChangeValue($i_count);

                        $a_columns['custom_category'] = $s_new_value;
                        $a_changes['custom_category'] = [
                            'previous' => $p_o_model->movement->custom_category,
                            'current' => $s_new_value,
                        ];
                    }
                }
            }
            Movement::model()->updateByPk(['movement_id' => $p_o_model->movement->movement_id], $a_columns);
            //Actualizamos movimiento
            $p_o_model->simple(Changelog::OTHER, Changelog::DEFAULT_REASON_CODE, '', ['movement' => $a_changes]);

            $this->commitTransaction($p_o_model);
        } catch (CDbException $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error interno. Por favor comuníquese con sistemas.');
        } catch (Exception $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error interno. Por favor comuníquese con sistemas.');
        }
    }

    private function _sortAttachments($p_o_model, $p_a_keys) {

        try {
            $this->beginTransaction($p_o_model);
            //Seteamos ordenes temporales
            $s_sql = "UPDATE resource SET resource_number = resource_number + :count WHERE `owner` = :owner AND owner_id = :owner_id AND type = :type";
            Yii::app()->db->createCommand($s_sql)->execute([
                ':count' => count($p_a_keys),
                ':owner' => Movement::OWNER,
                ':owner_id' => $p_o_model->movement->movement_id,
                ':type' => Resource::TYPE_ATTACHMENT,
            ]);
            //Actualizamos los ordenes
            foreach ($p_a_keys as $i => $s_key) {
                Resource::model()->updateAll([
                    'resource_number' => ($i + 1)
                        ], [
                    'condition' => '`owner` = :owner AND owner_id = :owner_id AND type = :type AND title = :title',
                    'params' => [
                        ':owner' => Movement::OWNER,
                        ':owner_id' => $p_o_model->movement->movement_id,
                        ':type' => Resource::TYPE_ATTACHMENT,
                        ':title' => $s_key
                    ]
                ]);
            }

            //Guardamos changelog
            $p_o_model->simple(Changelog::OTHER, Changelog::DEFAULT_REASON_CODE, '', [
                'movement' => [
                    'attachment_keys' => [
                        'previous' => Strings::NOT_FOUND,
                        'current' => $p_a_keys
                    ]
                ]
            ]);
            $this->commitTransaction($p_o_model);
        } catch (CDbException $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error interno. Por favor comuníquese con sistemas.');
        } catch (Exception $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error interno. Por favor comuníquese con sistemas.');
        }
    }

    private function _deleteResource($p_o_model, $p_s_key, $p_s_property_count, $p_s_sub_type) {
        try {
            $this->beginTransaction($p_o_model);
            //Borramos de la bd
            Resource::model()->deleteAllByAttributes([
                'owner' => Movement::OWNER,
                'owner_id' => $p_o_model->movement->movement_id,
                'type' => Resource::TYPE_ATTACHMENT,
                'sub_type' => $p_s_sub_type,
                'title' => $p_s_key
            ]);
            //Actualizamos contador
            $i_count = SIANAttachment::compactResources($p_o_model->movement->movement_id, $p_s_sub_type);
            //Definimos arrays de valores y cambios
            $a_columns = [
                $p_s_property_count => $i_count
            ];
            $a_changes = [
                $p_s_property_count => [
                    'previous' => $p_o_model->movement[$p_s_property_count],
                    'current' => $i_count
                ]
            ];

            $s_field = $p_o_model->movement->getCustomCategoryField();

            if (isset($s_field)) {
                if ($p_s_property_count == $s_field . "_count") {

                    $s_new_value = $p_o_model->movement->getCustomCategoryAutomaticChangeValue($i_count);

                    $a_columns['custom_category'] = $s_new_value;
                    $a_changes['custom_category'] = [
                        'previous' => $p_o_model->movement->custom_category,
                        'current' => $s_new_value,
                    ];
                }
            }

            //Actualizamos movimiento
            Movement::model()->updateByPk(['movement_id' => $p_o_model->movement->movement_id,], $a_columns);
            //Guardamos changelog
            $p_o_model->simple(Changelog::OTHER, Changelog::DEFAULT_REASON_CODE, '', ['movement' => $a_changes]);

            $this->commitTransaction($p_o_model);
        } catch (CDbException $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error.');
        } catch (Exception $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error.');
        }
    }

    public function actionSendMail($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $selected_option = null, $send_type = null) {

        $modal_html = "";
        $content_html = "";
        $b_success = false;
        $a_flash = [];
        $a_flashes = [];
        $b_ajax = Yii::app()->request->isAjaxRequest;

        $o_model = Movement::model()->with([
                    //Empleado
                    'person' => array(
                        'alias' => 'P',
                        'joinType' => 'join',
                        'select' => 'P.person_name, P.person_id, P.identification_type'
                    ),
                    //Cliente
                    'auxPerson' => array(
                        'alias' => 'XP',
                        'joinType' => 'join',
                        'select' => 'XP.person_name, XP.person_id, XP.identification_type, P.identification_number'
                    )
                ])->findByPk([
            'movement_id' => $id
                ], [
            'select' => [
                'movement_id',
                'document_code',
                'document_serie',
                'document_correlative',
                'emission_date',
                'aux_person_id',
                'person_id',
                'route'
            ]
        ]);

        try {
            //Iniciamos la transacción
            $this->beginTransaction($o_model);
            $a_flash = $this->sendMailNotification($o_model, $selected_option, $send_type);
            //Confirmamos la transacción
            $this->commitTransaction($o_model);
        } catch (CDbException $ex) {
            //Anulamos la transacción
            USLog::save($ex);
            $this->rollbackTransaction($o_model);
            throw $ex;
        } catch (Exception $ex) {
            //Anulamos la transacción
            USLog::save($ex);
            $this->rollbackTransaction($o_model);
            throw $ex;
        }

        if ($b_ajax) {

            echo CJSON::encode(array(
                'success' => $b_success,
                'modal_id' => $modal_id,
                'modal_html' => $modal_html,
                'content_html' => $content_html,
                'type' => $type,
                'element_id' => $element_id,
                'message' => $a_flash['message'],
                'message_type' => $a_flash['message_type'],
            ));
        } else {
            $this->pushFlash($a_flashes, $a_flash);
            $this->showFlashes($a_flashes);
            $this->redirect(array('view', 'id' => $id));
        }
    }

    protected function sendMailNotification($p_o_model, $p_s_selected_option, $p_s_send_type) {

        $o_row = Yii::app()->db->createCommand()
                        ->select([
                            "COALESCE(DS.format, D.format) as format"
                        ])
                        ->from('document D')
                        ->leftJoin('document_serie DS', 'DS.document_code = D.document_code AND DS.document_serie = :document_serie', [
                            ':document_serie' => $p_o_model->document_serie,
                        ])
                        ->where('D.document_code = :document_code', [
                            ':document_code' => $p_o_model->document_code,
                        ])->queryRow();

        if (isset($o_row)) {

            $s_format = $o_row['format'];

            if (isset($s_format)) {
                $a_emails = [];
                $a_data = [];
                switch ($p_s_selected_option) {
                    case Movement::EMAIL_FOR_BUSSINES_PARTNERS:

                        $p_s_send_type = !isset($p_s_send_type) ? Movement::SEND_TYPE_MOVEMENT_NORMAL : $p_s_send_type;
                        $a_data = Yii::app()->db->createCommand()
                                        ->select([
                                            "PE.email_address"
                                        ])
                                        ->from('person P')
                                        ->join('email PE', 'PE.owner_id = P.person_id AND PE.owner = "' . Person::OWNER . '"')
                                        ->where('P.person_id = :aux_person_id', [
                                            ':aux_person_id' => $p_o_model->aux_person_id
                                        ])->queryAll();
                        break;

                    case Movement::EMAIL_FOR_USERS:

                        $p_s_send_type = !isset($p_s_send_type) ? Movement::SEND_TYPE_MOVEMENT_RESEND : $p_s_send_type;
                        $a_data = Yii::app()->db->createCommand()
                                        ->select([
                                            "SE.email_address"
                                        ])
                                        ->from('scenario S')
                                        ->join('email SE', 'SE.owner_id = S.scenario_id AND SE.owner = "' . Scenario::OWNER . '"')
                                        ->where('S.route = :route', [
                                            ':route' => $p_o_model->route
                                        ])->queryAll();
                        break;
                }

                if (count($a_data) > 0) {
                    foreach ($a_data as $row) {
                        $a_emails[] = $row['email_address'];
                    }
                }

                //Verificamos entorno   
                $b_is_development = Yii::app()->params['environment'] == YII_ENVIRONMENT_DEVELOPMENT;
                //Verificamos si es posible mandar correos
                if (count($a_emails) > 0 || $b_is_development) {

                    //Enviamos email
                    if ($this->_sendMail($p_o_model, $a_emails, $p_s_selected_option, $p_s_send_type, $s_format)) {
                        $s_message = "<p>Mensaje enviado correctamente a:</p>";
                        $s_message .= "<p>" . implode("</p><p>", $a_emails) . "</p>";
                        $s_message_type = 'success';
                    } else {
                        $s_message = "<p>No se pudo enviar el mensaje a los correos:</p>";
                        $s_message .= "<p>" . implode("</p><p>", $a_emails) . "</p>";
                        $s_message_type = 'error';
                    }
                } else {
                    //En caso no haber correos
                    $s_message = "<p>No se pudo enviar el correo porque no hay ninguna dirección asociada.</p>";
                    $s_message_type = 'warning';
                }
            } else {
                //En caso no haber formato
                $s_message = "<p>No existe formato de impresión asociado al documento.</p>";
                $s_message_type = 'warning';
            }
        } else {
            //En caso no haber formato
            $s_message = "<p>No existe formato de impresión asociado al documento.</p>";
            $s_message_type = 'warning';
        }

        return [
            'message' => $s_message,
            'message_type' => $s_message_type
        ];
    }

    protected function sendMailScenarioNotification($p_a_data) {

        $a_emails = [];
        $a_data = Yii::app()->db->createCommand()
                        ->select([
                            "SSE.email_address"
                        ])
                        ->from('scenario_setting SS')
                        ->join('scenario S', 'S.scenario_id = SS.scenario_id')
                        ->join('email SSE', 'SSE.owner_id = SS.scenario_id AND SSE.owner = "' . ScenarioSetting::OWNER . '"')
                        ->where('S.route = :route', [
                            ':route' => $p_a_data['route']
                        ])->queryAll();

        if (count($a_data) > 0) {
            foreach ($a_data as $row) {
                $a_emails[] = $row['email_address'];
            }
        }
        //Verificamos entorno   
        $b_is_development = Yii::app()->params['environment'] == YII_ENVIRONMENT_DEVELOPMENT;
        //Verificamos si es posible mandar correos
        if (count($a_emails) > 0 || $b_is_development) {

            //Enviamos email
            if ($this->_sendMailScenario($p_a_data, $a_emails)) {
                $s_message = "<p>Mensaje enviado correctamente a:</p>";
                $s_message .= "<p>" . implode("</p><p>", $a_emails) . "</p>";
                $s_message_type = 'success';
            } else {
                $s_message = "<p>No se pudo enviar el mensaje a los correos:</p>";
                $s_message .= "<p>" . implode("</p><p>", $a_emails) . "</p>";
                $s_message_type = 'error';
            }
        } else {
            //En caso no haber correos
            $s_message = "<p>No se pudo enviar el correo porque no hay ninguna dirección asociada.</p>";
            $s_message_type = 'warning';
        }
        return [
            'message' => $s_message,
            'message_type' => $s_message_type
        ];
    }

    /**
     * Envía un email     
     * @param array $p_o_model el modelo del movimiento
     * @param array $p_a_emails Emails a dónde se enviarán
     * @param array $p_s_selected_option Opción seleccionada
     * @param array $p_s_send_type Tipo de mensaje
     * @param array $p_s_format Formato del documento
     * @return bool Indicador que determina si se envió con éxito o no
     * @throws Exception
     */
    private function _sendMail($p_o_model, array $p_a_emails, $p_s_selected_option, $p_s_send_type, $p_s_format) {

        $is_development = Yii::app()->params['environment'] == YII_ENVIRONMENT_DEVELOPMENT;

        // --- Obtenemos array de emails de destino ---
        if ($is_development) {
            $a_emails = Yii::app()->params['support_emails'];
        } else {
            $a_emails = $p_a_emails;
        }

        // --- Obtenemos data para PDF ---
        $type = MovementController::EXPORT_PDF;
        $a_pdf_data = $this->getFormatData($p_o_model->movement_id, $p_s_format, $type);
        //Obtenemos ruta del archivo
        $prefix_path = Yii::app()->params['files_dir'] . "/export/{$type}/";
        $filename = $p_o_model->toString() . '.' . $type;
        $filepath = $prefix_path . $filename;
        $this->convertToPdf($a_pdf_data['view'], $a_pdf_data['data'], $filepath, true);
        //Generamos Rutas
        $s_pdf_filepath = realpath($filepath);
        if ($s_pdf_filepath === false) {
            throw new Exception('No existe el archivo PDF!', USREST::CODE_INTERNAL_SERVER_ERROR);
        }
        $a_attachments = array(
            $s_pdf_filepath,
        );

        // --- Información para el envío de correos ---
        $send_type_name = Movement::getSendTypesSubject()[$p_s_send_type];
        $s_subject = 'Notificación de ' . $send_type_name . ' de ' . $this->singular;
        $s_view = "";

        switch ($p_s_selected_option) {
            case Movement::EMAIL_FOR_BUSSINES_PARTNERS:
                $s_view = '_movement_notification';
                break;
            case Movement::EMAIL_FOR_USERS:
                $s_view = '_movement_notification_users';
                break;
        }

        $a_data = [
            'model' => $p_o_model,
            'document_name' => $this->singular
        ];
        // --- Agregamos a cola ---
        return $this->_pushMail($a_emails, $a_attachments, $s_subject, $s_view, $a_data);
    }

    /**
     * Envía un email     
     * @param array $p_a_data el modelo del movimiento
     * @param array $p_a_emails Emails a dónde se enviarán
     * @param array $p_s_send_type Tipo de mensaje
     * @param array $p_s_format Formato del documento
     * @return bool Indicador que determina si se envió con éxito o no
     * @throws Exception
     */
    private function _sendMailScenario($p_a_data, array $p_a_emails) {

        $is_development = Yii::app()->params['environment'] == YII_ENVIRONMENT_DEVELOPMENT;
        // --- Obtenemos array de emails de destino ---
        if ($is_development) {
            $a_emails = Yii::app()->params['support_emails'];
        } else {
            $a_emails = $p_a_emails;
        }
        $a_attachments = [];

        switch ($p_a_data['route']) {
            case 'commercial/saleBill':
                $s_header = 'Venta ' . ExtraInfo::SHIPPING_TYPE_DELIVERY;
                $s_subject = 'Tienda : ' . $p_a_data['sale_store'] . ' - ' . $s_header . ' ' . $p_a_data['sale_document'];
                $s_view = "_scenario_setting_commercial_sale_bill_notification";
                break;
            case 'treasury/saleCollect':
                $s_header = 'Venta ' . ExtraInfo::SHIPPING_TYPE_DELIVERY;
                $s_subject = 'Tienda : ' . $p_a_data['sale_store'] . ' - ' . $s_header . ' ' . $p_a_data['sale_document'];
                $s_view = "_scenario_setting_treasury_sale_collect_notification";
                break;
            default:
                break;
        }

        $a_data = [
            'data' => $p_a_data
        ];
        // --- Agregamos a cola ---
        return $this->_pushMail($a_emails, $a_attachments, $s_subject, $s_view, $a_data, $s_header);
    }

    /**
     * Agrega el email a la cola
     * @param array $p_a_data Array que contiene al modelo
     * @param array $p_a_emails Array de emails
     * @param array $p_a_attachments Array de adjuntos
     * @return bool Indicador que determina si se envió con éxito o no
     */
    private function _pushMail(array $p_a_emails, array $p_a_attachments = [], $p_s_subject, $p_s_view, $p_a_data, $p_s_header = null) {

        $s_fromEmail = Yii::app()->params['senderEmail'];
        $s_fromName = Yii::app()->controller->getOrganization()->person->person_name;
        $a_attachments = is_array($p_a_attachments) ? $p_a_attachments : array($p_a_attachments);

        $p_s_header = isset($p_s_header) ? $p_s_header : $p_s_subject;

        $s_message = $this->renderPartial('//email/template', [
            'icon' => 'check',
            'color' => $this->getOrganization()->globalVar->color,
            'header' => strtoupper($p_s_header),
            'view' => '//email/' . $p_s_view,
            'footer' => '',
            'stores' => Store::get(Store::MODE_PRINT),
            'data' => $p_a_data
                ], true, true);

        return EmailQueue::push($s_fromEmail, $s_fromName, $p_s_subject, $s_message, $p_a_emails, $a_attachments);
    }

    public function actionChangeAssigned($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null) {

        $o_sp = $this->getModel('SpU0015');
        $o_sp->movement_id = $id;

        $this->title = "{$o_sp->action->toString()}";

        $modal_html = "";
        $content_html = "";
        $success = false;

        if (isset($_POST['SpU0015'])) {

            $o_sp->attributes = $_POST['SpU0015'];

            if ($o_sp->validate()) {
                //Obtenemos movimiento
                $a_movement = Yii::app()->db->createCommand()
                        ->select([
                            "X.type",
                            "X.person_id",
                        ])
                        ->from('movement X')
                        ->where('X.movement_id = :movement_id', [
                            ':movement_id' => $o_sp->movement_id
                        ])
                        ->queryRow();

                try {
                    $this->beginTransaction();
                    //Ejecutamos
                    $o_sp->execute();
                    //Guardamos changelog
                    $s_class = $a_movement['type'] . 'Movement';

                    if ($s_class == 'ProductListMovement') {
                        (new $s_class())->sameToAll([Movement::OWNER . ',' . $o_sp->movement_id], Changelog::UPDATE, '', [
                            'movement' => [
                                'person_id' => [
                                    'previous' => $a_movement['person_id'],
                                    'current' => $o_sp->person_id
                                ]
                            ]
                        ]);
                    } else {
                        (new $s_class())->sameToAll([$o_sp->movement_id], Changelog::UPDATE, '', [
                            'movement' => [
                                'person_id' => [
                                    'previous' => $a_movement['person_id'],
                                    'current' => $o_sp->person_id
                                ]
                            ]
                        ]);
                    }


                    $success = true;
                    //
                    $this->commitTransaction();
                } catch (CDbException $ex) {
                    $this->rollbackTransaction();
                    Yii::app()->user->setFlash('error', $ex->errorInfo[2]);
                }
            }
        } else {
            $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array(
                    'parent_id' => $parent_id,
                    'modal_id' => $modal_id
                )), true, true);
        }

        if (!$success) {
            $content_html = $this->renderPartial('application.modules.systems.views.utility._u0015', array('data' => array(
                    'model' => $o_sp,
                    'modal_id' => $modal_id,
                    'one_document' => true,
                )), true, true);
        }
        echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $o_sp);
    }

    public function getModel($class, $again = 0) {
        $model = new $class;
        $model->action = ActionObj::model()->findByPk(array(
            'module' => $this->module->id,
            'controller' => $this->id,
            'action' => $this->action->id,
            'owner' => User::OWNER
        ));
        $model->again = $again;
        return $model;
    }

    protected function getHasExtraMovement(&$p_o_model) {
        $b_has_extra_movement = false;

        if (isset($p_o_model->movement->extra_movement_id) && $p_o_model->movement->extra_movement_id != "") {
            $b_has_extra_movement = true;
        } else {
            $p_o_model->movement->extra_movement_id = null;
        }

        if ($b_has_extra_movement) {

            $s_parent_route = "";
            switch ($p_o_model->movement->route) {
                case "warehouse/transferenceOrder":
                case "warehouse/domesticUseOrder":
                case "warehouse/civilWorkUseOrder":
                case "logistic/purchaseOrder":
                    $s_parent_route = 'logistic/prePurchaseOrder';
                    break;
                case "treasury/prePay":
                    $s_parent_route = 'logistic/purchaseOrder';
                    break;
            }
            //Agregamos el link
            $p_o_model->addLink(array(
                'movement_parent_id' => $p_o_model->movement->extra_movement_id,
                'parent_route' => $s_parent_route,
            ));
        }
        return $b_has_extra_movement;
    }

    public function getPrePay($p_s_movement_id) {
        $o_prePay = null;
        $o_prePay = CashboxMovement::model()->with(array(
                    'movement' => array(
                        'with' => array(
                            'ability' => array(
                                'alias' => 'AB',
                                'condition' => 'AB.is_applicable = 1'
                            ),
                            'linkParent' => array(
                                'alias' => 'ML',
                                'condition' => 'ML.movement_parent_id = :parentMovementId',
                                'params' => array(':parentMovementId' => $p_s_movement_id),
                            ),
                        ),
                        'alias' => 'M',
                        'condition' => " M.route = 'treasury/prePay' AND M.status = 1",
                    ),
                ))->findAll();

        return $o_prePay;
    }

    public function getPrePayFromPurchaseBill($p_s_purchase_bill_movement_id) {

        $a_prePay = CashboxMovement::model()->findAll(array(
            'alias' => 'CM',
            'join' => '
                STRAIGHT_JOIN movement_link ML ON ML.movement_id = CM.movement_id
                STRAIGHT_JOIN movement M ON M.movement_id = CM.movement_id
                STRAIGHT_JOIN ability AB ON AB.movement_id = CM.movement_id
            ',
            'condition' => "
                ML.movement_parent_id = (
                    SELECT ML2.movement_parent_id 
                    FROM movement_link AS ML2 
                    WHERE ML2.movement_id = :movementId LIMIT 1
                ) 
                AND M.route = 'treasury/prePay' AND M.status = 1 AND AB.is_applicable = 1;
            ",
            'params' => array(':movementId' => $p_s_purchase_bill_movement_id),
        ));

        return $a_prePay;
    }

    public function getView($route, $ability) {
        $s_view = 'view';
        $b_has_access = $this->checkRoute("/administration/confirmation/evaluate");
        $b_confirm_route = $this->getConfirmationRoute($route);
        $b_assigned = $this->getConfirmAssigned($ability->confirmation_mode, $ability->user_group_confirm);

        if (isset($ability->confirm_id) && $b_has_access && $b_confirm_route && $b_assigned) {
            $s_view = 'view_confirm';
        }
        return $s_view;
    }

    public function getConfirmationRoute($route) {
        $confirmations = Yii::app()->user->getState('confirmations');

        $result = false;
        foreach ($confirmations as $item) {
            if ($item['route'] == $route) {
                $result = true;
            }
        }
        return $result;
    }

    public function getConfirmAssigned($confirmation_mode, $user_group_confirm) {
        if ($confirmation_mode == Scenario::CONFIRMATION_MODE_GROUPS) {
            $a_groups_you_manage = array_keys(Yii::app()->user->getState('groups_you_manage'));
            return in_array($user_group_confirm, $a_groups_you_manage);
        } else {
            return true;
        }
    }

}
