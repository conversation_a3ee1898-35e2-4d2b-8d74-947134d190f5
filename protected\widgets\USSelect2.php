<?php

class USSelect2 extends CWidget {

    public $id;
    public $form;
    public $model;
    public $value_attribute;
    public $text_attribute;
    public $dp;
    public $scenario;
    public $minimumInputLength = 3;
    public $modal_id = null;
    public $readonly = false;
    public $required = null;
    public $extra = [];
    public $value_name = null;
    public $text_name = null;
    //PRIVATE
    private $controller;
    private $div_id = null;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->div_id = $this->controller->getServerId();

        //Control
        if (!isset($this->dp)) {
            throw new Exception('Debe especificar un DP para la carga de datos!');
        }

        //Default
        $this->value_name = isset($this->value_name) ? $this->value_name : get_class($this->model) . "[" . $this->value_attribute . "]";
        $this->text_name = isset($this->text_name) ? $this->text_name : get_class($this->model) . "[" . $this->text_attribute . "]";
        $this->required = isset($this->required) ? $this->required : $this->model->isAttributeRequired($this->value_attribute);

        //Registramos assets
        SIANAssets::registerScriptFile('other/select2/js/select2.min.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
        SIANAssets::registerScriptFile('js/us-span.js');
        SIANAssets::registerScriptFile('js/us-select2.js');
        //
        SIANAssets::registerCssFile('other/select2/css/select2.css');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id . '_ajax', "

        //DATA
        $(document).ready(function() {   
            var html = USSpanInit('{$this->id}', '', '{$this->model->{$this->value_attribute}}', '{$this->model->{$this->text_attribute}}', '{$this->value_name}', '{$this->text_name}', '{$this->model->getAttributeLabel($this->value_attribute)}', [], '{$this->model->getError($this->value_attribute)}', " . CJSON::encode($this->required) . ", " . CJSON::encode($this->readonly) . ");
            $('#{$this->div_id}').html(html);
        });
        
        $('body').on('click', '#{$this->id}', function(e) {
            USSelect2Init('{$this->id}', '{$this->dp}', '{$this->scenario}', " . CJSON::encode($this->extra) . ", {$this->minimumInputLength});
        });

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        echo "<div id='{$this->div_id}'></div>";
    }

}
