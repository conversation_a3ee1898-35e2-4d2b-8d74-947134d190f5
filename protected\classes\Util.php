<?php

class Util {

    const MYSQL_FAKE_ERROR = 45001;
    const MYSQL_DELETE_RESTRICT = 23000;
    const MYSQL_BIGINT_MAX = 18446744073709551615;

    public static function resolveUrl($url) {
        $array = explode('/', $url);
        $domain = array_shift($array);

        $parents = [];
        foreach ($array as $dir) {
            switch ($dir) {
                case '.':
// Don't need to do anything here
                    break;
                case '..':
                    array_pop($parents);
                    break;
                default:
                    $parents[] = $dir;
                    break;
            }
        }

        return $domain . '/' . implode('/', $parents);
    }

    public static function arrayToOptions(array $array, $selected_value = null, $empty = true) {
        $html = ($empty) ? CHtml::tag('option', array('value' => ""), CHtml::encode(Strings::SELECT_OPTION), true) : "";

        foreach ($array as $value => $text) {
            $html .= CHtml::tag('option', array('value' => $value, 'selected' => ($value == $selected_value)), CHtml::encode($text), true);
        }

        return $html;
    }

    public static function getAttributeValue($model, $attribute) {
        $value = $model;

        foreach (explode('.', trim($attribute)) as $attr) {
            $value = $value[$attr];
        }

        return $value;
    }

    public static function setAttributeValue(&$model, $attribute, $value) {
        $X = $model;

        foreach (explode('.', trim($attribute)) as $attr) {
            $X = $X[$attr];
        }

        $X = $value;
    }

    public static function getVarName($exception) {
        return substr($exception->getMessage(), strlen("Undefined variable: "));
    }

    public static function getBooleanListData() {
        return array(1 => 'Si', 0 => 'No');
    }

    public static function getBooleanArray() {
        return array(
            array('value' => 0, 'text' => 'Si'),
            array('value' => 1, 'text' => 'No'),
        );
    }

    /**
     * Codifica unn array como objeto incluyendo funciones
     * @param array $array Array a codificar
     * @return mixed Objecto codificado
     */
    public static function jsonEncode(array $array) {

        $value_arr = [];
        $replace_keys = [];
        foreach ($array as $key => &$value) {
            // Look for values starting with 'function('
            if (strpos($value, 'function(') === 0) {
                // Store function string.
                $value_arr[] = $value;
                // Replace function string in $foo with a 'unique' special key.
                $value = '%' . $key . '%';
                // Later on, we'll look for the value, and replace it.
                $replace_keys[] = '"' . $value . '"';
            }
        }

        // Now encode the array to json format
        $json = json_encode($array);


        // Replace the special keys with the original string.
        return str_replace($replace_keys, $value_arr, $json);
    }

    /**
     * Verifica si un array o cadena está vacio
     * @param mixed $mixed
     * @return boolean TRUE si está en blanco o vacio o FALSO en caso contrario
     */
    public static function isBlank($mixed) {
        if (is_array($mixed)) {
            return count($mixed) === 0;
        } else {
            return strlen(trim($mixed)) == 0;
        }
    }

    public static function convertQuantity($p_m_number) {

        if ((int) $p_m_number == $p_m_number) {
            return (int) $p_m_number;
        } else {
            return (double) $p_m_number;
        }
    }

    public static function formatQuantity($p_m_number, $p_b_allow_decimals) {

        if ($p_b_allow_decimals) {
            return number_format($p_m_number, 2);
        } else {
            if ((int) $p_m_number == $p_m_number) {
                return number_format($p_m_number, 0);
            } else {
                return number_format($p_m_number, 2);
            }
        }
    }

    public static function getServerName() {
        //Obtenemos
        if (Yii::app() instanceof CConsoleApplication) {
            return 'CONSOLE';
        } else {
            if (isset($_SERVER["SERVER_NAME"])) {
                return $_SERVER["SERVER_NAME"];
            } else {
                $s_domain = Yii::app()->params['domain'];
                $a_parts = explode('//', $s_domain);
                if (count($a_parts) > 1) {
                    return $a_parts[1];
                } else {
                    throw new Exception('No se pudo obtener el dominio');
                }
            }
        }
    }

    public static function getUsername() {
        //Obtenemos
        if (Yii::app() instanceof CConsoleApplication) {
            return null;
        } else {
            return Yii::app()->user->isGuest ? false : Yii::app()->user->username;
        }
    }

    public static function getRequest() {
        $o_request = Yii::app()->request;
        return [
            'userAgent' => $o_request->getUserAgent(),
            'userHost' => $o_request->getUserHost(),
            'userHostAddress' => $o_request->getUserHostAddress(),
            'contentType' => $o_request->getContentType(),
            'cookies' => $o_request->getCookies(),
            'queryString' => $o_request->getQueryString(),
            'rawBody' => $o_request->getRawBody(),
            'requestType' => $o_request->getRequestType(),
            'requestUri' => Yii::app() instanceof CConsoleApplication ? 'CONSOLE' : $o_request->getRequestUri(),
            'restParams' => $o_request->getRestParams()
        ];
    }

}
