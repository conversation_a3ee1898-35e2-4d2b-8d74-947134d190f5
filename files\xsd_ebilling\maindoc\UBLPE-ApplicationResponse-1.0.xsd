<?xml version="1.0" encoding="UTF-8"?>
<!--
  Document Type:     ApplicationResponse
  Generated On:      Tue Oct 03 2:26:38 P3 2006

-->
<!-- ===== xsd:schema Element With Namespaces Declarations ===== -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:udt="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" xmlns:ccts="urn:un:unece:uncefact:documentation:2" xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" xmlns:qdt="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2" targetNamespace="urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2" elementFormDefault="qualified" attributeFormDefault="unqualified" version="2.0">
	<!-- ===== Imports ===== -->
	<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" schemaLocation="../common/UBL-CommonAggregateComponents-2.0.xsd"/>
	<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" schemaLocation="../common/UBL-CommonBasicComponents-2.0.xsd"/>
	<xsd:import namespace="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" schemaLocation="../common/UnqualifiedDataTypeSchemaModule-2.0.xsd"/>
	<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" schemaLocation="../common/UBL-CommonExtensionComponents-2.0.xsd"/>
	<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2" schemaLocation="../common/UBL-QualifiedDatatypes-2.0.xsd"/>
	<!-- ===== Root Element ===== -->
	<xsd:element name="ApplicationResponse" type="ApplicationResponseType">
		<xsd:annotation>
			<xsd:documentation>This element MUST be conveyed as the root element in any instance document based on this Schema expression</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="ApplicationResponseType">
		<xsd:annotation>
			<xsd:documentation>
				<ccts:Component>
					<ccts:ComponentType>ABIE</ccts:ComponentType>
					<ccts:DictionaryEntryName>Application Response. Details</ccts:DictionaryEntryName>
					<ccts:Definition>A document to indicate the application's response to a transaction. This may be a business response and/or a technical response, sent automatically by an application or initiated by a user.</ccts:Definition>
					<ccts:ObjectClass>Application Response</ccts:ObjectClass>
				</ccts:Component>
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element ref="ext:UBLExtensions" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>A container for all extensions present in the document.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:UBLVersionID" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. UBL Version Identifier. Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>The earliest version of the UBL 2 schema for this document type that defines all of the elements that might be encountered in the current instance.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>UBL Version Identifier</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
							<ccts:Examples>2.0.5</ccts:Examples>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:CustomizationID" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Customization Identifier. Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>Identifies a user-defined customization of UBL for a specific use.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>Customization Identifier</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
							<ccts:Examples>NES</ccts:Examples>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:ProfileID" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Profile Identifier. Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>Identifies a user-defined profile of the customization of UBL being used.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>Profile Identifier</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
							<ccts:Examples>BasicProcurementProcess</ccts:Examples>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:ID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>An identifier for the Application Response assigned by the sender.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>Identifier</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:UUID" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. UUID. Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>A universally unique identifier for an instance of this ABIE.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>UUID</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:IssueDate">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Issue Date. Date</ccts:DictionaryEntryName>
							<ccts:Definition>The date assigned by the sender's application on which the Application Response was created.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>Issue Date</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Date</ccts:RepresentationTerm>
							<ccts:DataType>Date. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:IssueTime" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Issue Time. Time</ccts:DictionaryEntryName>
							<ccts:Definition>The time assigned by the sender's application at which the Application Response was created.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>Issue Time</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Time</ccts:RepresentationTerm>
							<ccts:DataType>Time. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:ResponseDate" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Response Date. Date</ccts:DictionaryEntryName>
							<ccts:Definition>The date at which the information in the response was created.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>Response Date</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Date</ccts:RepresentationTerm>
							<ccts:DataType>Date. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:ResponseTime" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Response Time. Time</ccts:DictionaryEntryName>
							<ccts:Definition>The time at which the information in the response was created.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>Response Time</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Time</ccts:RepresentationTerm>
							<ccts:DataType>Time. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:Note" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Note. Text</ccts:DictionaryEntryName>
							<ccts:Definition>Free-form text applying to the Application Response.  This element may contain notes or any other similar information that is not contained explicitly in another structure.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>Note</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Text</ccts:RepresentationTerm>
							<ccts:DataType>Text. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:VersionID" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Version Identifier. Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>Identifies the current version of this document.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>Version Identifier</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
							<ccts:Examples>"1.1"</ccts:Examples>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cac:Signature" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Signature</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Signature.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>Signature</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Signature</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cac:SenderParty">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Sender_ Party. Party</ccts:DictionaryEntryName>
							<ccts:Definition>An association to the Party sending this document.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTermQualifier>Sender</ccts:PropertyTermQualifier>
							<ccts:PropertyTerm>Party</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Party</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cac:ReceiverParty">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Receiver_ Party. Party</ccts:DictionaryEntryName>
							<ccts:Definition>An association to the Party receiving this document.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTermQualifier>Receiver</ccts:PropertyTermQualifier>
							<ccts:PropertyTerm>Party</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Party</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cac:DocumentResponse" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Application Response. Document Response</ccts:DictionaryEntryName>
							<ccts:Definition>A response to one or more documents.</ccts:Definition>
							<ccts:Cardinality>1..n</ccts:Cardinality>
							<ccts:ObjectClass>Application Response</ccts:ObjectClass>
							<ccts:PropertyTerm>Document Response</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Document Response</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ===== Element Declarations ===== -->
	<!-- ===== Type Definitions ===== -->
	<!-- ===== Basic Business Information Entity Type Definitions ===== -->
</xsd:schema>
<!-- ===== Copyright Notice ===== -->
<!--
  OASIS takes no position regarding the validity or scope of any 
  intellectual property or other rights that might be claimed to pertain 
  to the implementation or use of the technology described in this 
  document or the extent to which any license under such rights 
  might or might not be available; neither does it represent that it has 
  made any effort to identify any such rights. Information on OASIS's 
  procedures with respect to rights in OASIS specifications can be 
  found at the OASIS website. Copies of claims of rights made 
  available for publication and any assurances of licenses to be made 
  available, or the result of an attempt made to obtain a general 
  license or permission for the use of such proprietary rights by 
  implementors or users of this specification, can be obtained from 
  the OASIS Executive Director.

  OASIS invites any interested party to bring to its attention any 
  copyrights, patents or patent applications, or other proprietary 
  rights which may cover technology that may be required to 
  implement this specification. Please address the information to the 
  OASIS Executive Director.
  
  Copyright (C) OASIS Open 2001-2006. All Rights Reserved.

  This document and translations of it may be copied and furnished to 
  others, and derivative works that comment on or otherwise explain 
  it or assist in its implementation may be prepared, copied, 
  published and distributed, in whole or in part, without restriction of 
  any kind, provided that the above copyright notice and this 
  paragraph are included on all such copies and derivative works. 
  However, this document itself may not be modified in any way, 
  such as by removing the copyright notice or references to OASIS, 
  except as needed for the purpose of developing OASIS 
  specifications, in which case the procedures for copyrights defined 
  in the OASIS Intellectual Property Rights document must be 
  followed, or as required to translate it into languages other than 
  English. 

  The limited permissions granted above are perpetual and will not be 
  revoked by OASIS or its successors or assigns. 

  This document and the information contained herein is provided on 
  an "AS IS" basis and OASIS DISCLAIMS ALL WARRANTIES, 
  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY 
  WARRANTY THAT THE USE OF THE INFORMATION HEREIN 
  WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED 
  WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A 
  PARTICULAR PURPOSE.
-->
