<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SigninController
 *
 * <AUTHOR>
 */
class PersonController extends ApiController {

    use ApiPosControllerTrait;

    public $dp_class = "DpBusinessPartner";
    public $dp_scenario = DpBusinessPartner::SCENARIO_API_POS;

    public function accessRules() {
        return array(
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('restList', 'restCreate', 'restUpdate', 'restView', 'restAgreementStatus'),
                'expression' => 'Yii::app()->controller->checkAuth()',
            ),
            array('deny', // El resto de acciones se deniega
                'users' => array('*'),
                'deniedCallback' => array('ApiController', 'deniedCallback')
            ),
        );
    }

    protected function getListAllowedAttributes() {
        return [
            'person_id',
            'identification_type',
            'identification_name',
            'identification_number',
            'person_name',
            'person_type',
            'phone_number',
            'email_address',
            'official_address',
            'reference',
            'dept_code',
            'dept_name',
            'prov_code',
            'prov_name',
            'dist_code',
            'dist_name',
            'status',
        ];
    }

    public function createRequiredAttributes() {
        return [
            'identification_type' => true,
            'identification_number' => true,
            'person_name' => false,
            'birthday' => false,
            'phone_number' => false,
            'email_address' => false,
            'dept_code' => false,
            'prov_code' => false,
            'dist_code' => false,
            'address' => false,
            'reference' => false
        ];
    }

    public function createDefaultAttributes() {
        return [
            'reference' => ''
        ];
    }

    protected function loadCreate($p_a_filtered, $p_i_id2 = null, $p_i_id3 = null) {
        return new Person(Person::SCENARIO_NORMAL);
    }

    protected function beforeRestCreate(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        //Si se especificó person_name
        if (isset($p_a_filtered['person_name'])) {
            //Otras validaciones
            if (!in_array($p_a_filtered['identification_type'], [USIdentificationValidator::RUC, USIdentificationValidator::OTRO, USIdentificationValidator::CARNET]) && count(explode(',', $p_a_filtered['person_name'])) !== 3) {
                USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                    'attributes' => [
                        'person_name' => "La cadena no tiene el formato correcto para este tipo de identificación '{$p_a_filtered['identification_type']}'. El formato debe ser: 'apellido_paterno,apellido_materno,nombres'"
                    ]
                ]);
                Yii::app()->end();
            }

            $p_o_model->setPersonName($p_a_filtered['person_name']);
        }
        return parent::beforeRestCreate($p_a_filtered, $p_o_model, $p_o_model2, $p_o_model3);
    }

    protected function createAssignment(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        //Asignamos
        $p_o_model->identification_type = $p_a_filtered['identification_type'];
        $p_o_model->identification_number = $p_a_filtered['identification_number'];
        $p_o_model->birthday = $p_a_filtered['birthday'];
        $p_o_model->catchment = Person::CATCH_OTRO;
        //Teléfono
        if (isset($p_a_filtered['phone_number'])) {
            $p_o_model->addPhone(array(
                'phone_number' => $p_a_filtered['phone_number'],
            ));
        }

        //Agregamos email
        if (isset($p_a_filtered['email_address'])) {
            $p_o_model->addEmail(array(
                'email_address' => $p_a_filtered['email_address'],
            ));
        }
        //Agregamos dirección
        if (isset($p_a_filtered['dept_code'], $p_a_filtered['prov_code'], $p_a_filtered['dist_code'], $p_a_filtered['address'])) {
            $p_o_model->addAddress(array(
                'dept_code' => $p_a_filtered['dept_code'],
                'prov_code' => $p_a_filtered['prov_code'],
                'dist_code' => $p_a_filtered['dist_code'],
                'address' => $p_a_filtered['address'],
                'reference' => $p_a_filtered['reference'],
            ));
        }
    }

    protected function validateCreate(&$p_o_model) {
        return $p_o_model->validatingExistence()->validate();
    }

    protected function restCreate(&$p_o_model) {
        $p_o_model->savingExternal()->insert();
    }

    protected function createResponse($p_o_model) {
        return [
            'person_id' => (int) $p_o_model->person_id,
            'identification_type' => $p_o_model->identification_type,
            'identification_number' => $p_o_model->identification_number,
            'person_name' => $p_o_model->toString()
        ];
    }

    //UPDATE
    public function updateRequiredAttributes() {
        return [
            'identification_type' => false,
            'identification_number' => false,
            'person_name' => false,
            'birthday' => false,
            'phone_number' => false,
            'email_address' => false,
            'dept_code' => false,
            'prov_code' => false,
            'dist_code' => false,
            'address' => false,
            'reference' => false
        ];
    }

    protected function loadUpdate($p_a_filtered, $p_i_id, $p_i_id2 = null, $p_i_id3 = null) {
        $o_model = Person::model()->with(array(
                    'phones' => [],
                    'emails' => [],
                    'addresses.geoloc' => [],
                    'bankAccounts' => [],
                ))->findByAutoIncrement($p_i_id, [
            'alias' => 'P',
            'condition' => 'P.`status`',
            'order' => 'PA.`order`, PE.`order`, PP.`order`',
        ]);
        $o_model->scenario = Person::SCENARIO_NORMAL;
        $o_model->loadExternal();
        return $o_model;
    }

    protected function beforeRestUpdate(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        //Si se especificó person_name
        if (isset($p_a_filtered['person_name'])) {
            //Otras validaciones
            if (!in_array($p_a_filtered['identification_type'], [USIdentificationValidator::RUC, USIdentificationValidator::OTRO]) && count(explode(',', $p_a_filtered['person_name'])) !== 3) {
                USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                    'attributes' => [
                        'person_name' => "La cadena no tiene el formato correcto para este tipo de identificación '{$p_a_filtered['identification_type']}'. El formato debe ser: 'apellido_paterno,apellido_materno,nombres'"
                    ]
                ]);
                Yii::app()->end();
            }

            $p_o_model->setPersonName($p_a_filtered['person_name']);
        }
        return parent::beforeRestCreate($p_a_filtered, $p_o_model, $p_o_model2, $p_o_model3);
    }

    protected function updateAssignment(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        //Asignamos
        if (isset($p_a_filtered['identification_type'])) {
            $p_o_model->identification_type = $p_a_filtered['identification_type'];
        }
        if (isset($p_a_filtered['identification_number'])) {
            $p_o_model->identification_number = $p_a_filtered['identification_number'];
        }
        if (isset($p_a_filtered['birthday'])) {
            $p_o_model->birthday = $p_a_filtered['birthday'];
        }
        //Teléfono
        if (isset($p_a_filtered['phone_number'])) {
            //La persona puede tener más de un teléfono
            if (count($p_o_model->tempPhones) > 0) {
                //El teléfono con index 0 será reemplazado
                $p_o_model->tempPhones[0]->phone_number = $p_a_filtered['phone_number'];
                //Buscamos si en el resto de teléfonos existe el mismo número
                for ($i = 1; $i < count($p_o_model->tempPhones); $i++) {
                    if ($p_o_model->tempPhones[$i]->phone_number === $p_a_filtered['phone_number']) {
                        //Si se encuentra se quita
                        array_splice($p_o_model->tempPhones, $i, 1);
                        break;
                    }
                }
            } else {
                //Si no tiene teléfonos sólo agregamos
                $p_o_model->addPhone([
                    'phone_number' => $p_a_filtered['phone_number'],
                ]);
            }
        }

        //Agregamos email
        if (isset($p_a_filtered['email_address'])) {
            //La persona puede tener más de un email
            if (count($p_o_model->tempEmails) > 0) {
                //El email con index 0 será reemplazado
                $p_o_model->tempEmails[0]->email_address = $p_a_filtered['email_address'];
                //Buscamos si en el resto de teléfonos existe el mismo email
                for ($i = 1; $i < count($p_o_model->tempEmails); $i++) {
                    if ($p_o_model->tempEmails[$i]->email_address === $p_a_filtered['email_address']) {
                        //Si se encuentra se quita
                        array_splice($p_o_model->tempEmails, $i, 1);
                        break;
                    }
                }
            } else {
                //Si no tiene emails sólo agregamos
                $p_o_model->addEmail([
                    'email_address' => $p_a_filtered['email_address'],
                ]);
            }
        }
        //Agregamos dirección
        if (isset($p_a_filtered['dept_code'], $p_a_filtered['prov_code'], $p_a_filtered['dist_code'], $p_a_filtered['address'])) {
            //La persona puede tener más de una dirección
            if (count($p_o_model->tempAddresses) > 0) {
                //El email con index 0 será reemplazado
                $p_o_model->tempAddresses[0]->dept_code = $p_a_filtered['dept_code'];
                $p_o_model->tempAddresses[0]->prov_code = $p_a_filtered['prov_code'];
                $p_o_model->tempAddresses[0]->dist_code = $p_a_filtered['dist_code'];
                $p_o_model->tempAddresses[0]->address = $p_a_filtered['address'];
                if (isset($p_a_filtered['reference'])) {
                    $p_o_model->tempAddresses[0]->reference = $p_a_filtered['reference'];
                }
                //Buscamos si en el resto de direcciones existe la mismo dirección
                for ($i = 1; $i < count($p_o_model->tempAddresses); $i++) {
                    if ($p_o_model->tempAddresses[$i]->dept_code === $p_a_filtered['dept_code'] &&
                            $p_o_model->tempAddresses[$i]->prov_code === $p_a_filtered['prov_code'] &&
                            $p_o_model->tempAddresses[$i]->dist_code === $p_a_filtered['dist_code'] &&
                            $p_o_model->tempAddresses[$i]->address === $p_a_filtered['address']) {
                        //Si se encuentra se quita
                        array_splice($p_o_model->tempAddresses, $i, 1);
                        break;
                    }
                }
            } else {
                //Si no tiene emails sólo agregamos
                $p_o_model->addAddress([
                    'dept_code' => $p_a_filtered['dept_code'],
                    'prov_code' => $p_a_filtered['prov_code'],
                    'dist_code' => $p_a_filtered['dist_code'],
                    'address' => $p_a_filtered['address'],
                    'reference' => isset($p_a_filtered['reference']) ? $p_a_filtered['reference'] : null,
                ]);
            }
        }
    }

    protected function validateUpdate(&$p_o_model) {
        return $p_o_model->validatingExistence()->validate();
    }

    protected function restUpdate(&$p_o_model) {
        $p_o_model->savingExternal()->update();
    }

    protected function updateResponse($p_o_model) {
        return [
            'identification_type' => $p_o_model->identification_type,
            'identification_number' => $p_o_model->identification_number,
            'person_name' => $p_o_model->toString()
        ];
    }

    protected function loadView(&$p_a_filtered, $p_i_id, $p_i_id2 = null, $p_i_id3 = null) {

        $o_model = Person::model()->with([
                    'email',
                    'phone',
                    'officialAddress.geoloc'
                ])->findByAutoIncrement($p_i_id, [
            'select' => [
                'P.person_id',
                'P.identification_type',
                'P.identification_number',
                'P.person_name',
                'P.birthday'
            ],
            'alias' => 'P',
            'condition' => 'P.`status`'
        ]);

        $this->exists($o_model, 'El cliente no existe o no está habilitado.');

        return [
            'person_id' => (int) $o_model->person_id,
            'identification_type' => $o_model->identification_type,
            'identification_name' => USIdentificationValidator::getLabel($o_model->identification_type),
            'identification_number' => $o_model->identification_number,
            'person_type' => $o_model->person_type,
            'person_name' => $o_model->person_name,
            'birthday' => $o_model->birthday,
            'phone_number' => isset($o_model->phone) ? $o_model->phone->phone_number : null,
            'email_address' => isset($o_model->email) ? $o_model->email->email_address : null,
            'official_address' => isset($o_model->officialAddress) ? $o_model->officialAddress->address : null,
            'reference' => isset($o_model->officialAddress) ? $o_model->officialAddress->reference : null,
            'dept_code' => isset($o_model->officialAddress) ? $o_model->officialAddress->dept_code : null,
            'dept_name' => isset($o_model->officialAddress) ? $o_model->officialAddress->geoloc->dept_name : null,
            'prov_code' => isset($o_model->officialAddress) ? $o_model->officialAddress->prov_code : null,
            'prov_name' => isset($o_model->officialAddress) ? $o_model->officialAddress->geoloc->prov_name : null,
            'dist_code' => isset($o_model->officialAddress) ? $o_model->officialAddress->dist_code : null,
            'dist_name' => isset($o_model->officialAddress) ? $o_model->officialAddress->geoloc->dist_name : null,
        ];
    }

    public function actionRestList($id2 = null, $id3 = null) {

        try {
            $a_required = $this->listRequiredAttributes();

            $a_default = $this->listDefaultAttributes();

            $a_filtered = $this->getAndFilterAttributes($a_required, $a_default, ['id2', 'id3']);

            $b_has_status_filter = false;
            if (isset($a_filtered['filters']['equal'])) {
                if (array_key_exists('status', $a_filtered['filters']['equal'])) {
                    $b_has_status_filter = true;
                }
            }
            if (!$b_has_status_filter) {
                $a_filtered['filters']['equal']['status'] = 1;
            }
            //Inicializamos
            $o_model = (new $this->dp_class($this->dp_scenario));
            //Verificamos filtros
            $a_filters = $a_filtered['filters'];
            $this->verifyFilters($a_filters, $id2, $id3);
            //Verificamos page and limit
            $this->_verifyCount($a_filtered['count']);
            $this->_verifyPage($a_filtered['page']);
            $this->_verifyLimit($a_filtered['limit']);
            //Verificamos ordenamiento
            $a_order = $this->_getOrder($o_model, $a_filtered);
            //Verificamos atributos
            $this->beforeRestList($o_model, $a_filtered, $id2, $id3);
            //Procesamos filtros
            $this->_processFilters($o_model, $a_filters);
            //Obtenemos atributos permitidos
            $a_allowed = $this->getListAllowedAttributes();
            $a_response = $o_model->setOrder($a_order)->findPage($a_filtered['page'], $a_filtered['limit'], $a_filtered['count'] == 1, true, $a_allowed);
            //Procesamos lo que pasa después de la búsqueda
            $this->afterRestList($a_response, $a_filtered, $id2, $id3);
            //Enviamos respuesta
            USREST::sendResponse(USREST::CODE_SUCCESS, '', $a_response);
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }


    public function actionRestAgreementStatus($id) {
        try {
            $agreement_data = Agreement::getAgreementByIdentificationNumber($id);

            $final_data = [];

            SIANLog::print($agreement_data);

            if (isset($agreement_data)) {
                $final_data = $agreement_data;
            }
            
            USREST::sendResponse(USREST::CODE_SUCCESS, '', $final_data);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }

}
