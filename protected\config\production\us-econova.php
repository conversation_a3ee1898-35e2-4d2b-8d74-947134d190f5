<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'econova.siansystem.com/admin';
$domain = 'https://econova.siansystem.com';
$domain2 = 'http://econova.siansystem.com';
$report_domain = 'reconova.siansystem.com';
$org = 'econova';
//SIAN 2
$domain_react_app = 'https://econova2.siansystem.com';
$api_sian = 'https://api.siansystem.com/';
//database enterprise
$database_server = '161.132.48.88';
$database_name = 'econova';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = 'econova.p12';
$e_billing_certificate_pass = '2023ECOno';//75sbdn8hq6n74
$e_billing_ri = '';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20539744659BILLINGE',
        'password' => '75Sbdohp'
    ]
];

$smtp_username = '<EMAIL>';
$smtp_password = '75neazxql1nuva';
$environment_reports = YII_ENVIRONMENT_PRODUCTION;
$environment = YII_ENVIRONMENT_PRODUCTION;
