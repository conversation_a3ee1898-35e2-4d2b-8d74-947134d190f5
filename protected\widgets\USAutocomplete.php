<?php

class USAutocomplete extends CWidget {

    public $id;
    public $parent_id = null;
    public $name = null;
    public $id_name = null;
    public $aux_name = null;
    public $text_name = null;
    public $label = null;
    public $model;
    public $attribute;
    public $sidx = null;
    public $sord = null;
    public $value = null;
    public $aux_id = null;
    public $id_id = null;
    public $value_id = null;
    public $text_id = null;
    public $id_class = null;
    public $value_class = null;
    public $search_id = null;
    public $reset_id = null;
    public $focus_id = null;
    public $focus = true;
    public $error = false;
    public $required = false;
    public $view = array(
        'model' => true,
        'scenario' => null,
        'attributes' => [],
        'params' => '{}',
    );
    public $autoChoose = true;
    public $readonly = false;
    public $hideText = false;
    public $width = '600px';
    public $placeholder = 'Ingrese término de búsqueda';
    public $hint = null;
    public $useOnkeydown = false;
    public $col = 5;
    //EVENTS
    public $onsearch = '';
    public $onreset = '';
    public $onselect = null;
    public $afterreset = '';
    //MAINTENANCE
    public $maintenance = null;
    //PRIVATE
    private $viewObject;
    private $controller;
    //PRIVATE
    private $aux_attribute = null;
    private $id_attribute = null;
    private $value_attribute = null;
    private $text_attribute = null;
    //SELECT ATTRIBUTES
    private $selectAttributes = [];
    private $searchAttributes = [];

    /**
     * Initializes the widget.
     */
    public function init() {

        //CONTROLADOR DE ORIGEN
        $this->controller = Yii::app()->controller;
        //ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //ID'S DE LOS BOTONES DEL BUSCADOR
        $this->search_id = isset($this->search_id) ? $this->search_id : $this->controller->getServerId();
        $this->reset_id = isset($this->reset_id) ? $this->reset_id : $this->controller->getServerId();
        // Detectar escritura en teclado
        $this->useOnkeydown = isset($this->useOnkeydown) ? $this->useOnkeydown : false;
        // Elegir automáticamente el valor del resultado de la busqueda
        $this->autoChoose = isset($this->autoChoose) ? $this->autoChoose : true;

        if (isset($this->maintenance)) {

            if (!isset($this->maintenance['buttons'])) {
                $this->maintenance['buttons'] = array(
                    'create' => [],
                    'update' => [],
                    'delete' => [],
                    'preview' => []
                );
            }

            //PARA CADA BUTTON
            foreach ($this->maintenance['buttons'] as $action => $parameters) {
                //ID
                if (!isset($parameters['id'])) {
                    $this->maintenance['buttons'][$action]['id'] = $this->controller->getServerId();
                }
                //DATA
                if (!isset($parameters['data'])) {
                    $this->maintenance['buttons'][$action]['data'] = [];
                }

                //ACCESS
                if (!isset($parameters['access'])) {
                    $this->maintenance['buttons'][$action]['access'] = $this->controller->checkRoute("/{$this->maintenance['module']}/{$this->maintenance['controller']}/{$action}");
                }
            }
        }

        //Escenario
        if (!isset($this->view['scenario'])) {
            $this->view['scenario'] = null;
        }

        //Params
        if (!isset($this->view['params'])) {
            $this->view['params'] = '{}';
        }

        //SELECT ATTRIBUTES
        $this->selectAttributes = USArray::array_column($this->view['attributes'], 'name');
        //SEARCH ATTRIBUTES
        foreach ($this->view['attributes'] as $attribute) {
            if (!isset($attribute['search']) || $attribute['search'] == true) {
                $this->searchAttributes[] = $attribute['name'];
            }
        }
        //LABEL DEL CAMPO VALUE
        $this->label = $this->_getLabel();
        //VALOR DEL CAMPO VALUE
        $this->value = isset($this->model) ? $this->model->{$this->attribute} : $this->value;
        //NOMBRE DEL CAMPO VALUE
        $this->name = isset($this->model) && isset($this->attribute) ? get_class($this->model) . "[{$this->attribute}]" : $this->name;
        //ERROR DEL CAMPO VALUE
        $this->error = isset($this->model) ? $this->model->hasErrors($this->attribute) : $this->error;
        //ID'S DE LOS CAMPOS DEL BUSCADOR
        $this->aux_id = isset($this->aux_id) ? $this->aux_id : $this->controller->getServerId();
        $this->id_id = isset($this->id_id) ? $this->id_id : $this->controller->getServerId();
        $this->value_id = isset($this->value_id) ? $this->value_id : $this->controller->getServerId();
        $this->text_id = isset($this->text_id) ? $this->text_id : $this->controller->getServerId();
        // SI EL CAMPO ES REQUERIDO            
        $this->required = isset($this->required) ? $this->required : $this->model->isAttributeRequired($this->attribute);
        //OBTENEMOS EL OBJETO DE LA VISTA
        $this->viewObject = $this->getView();

        //CONTAMOS LOS ATTRIBUTOS
        $count = count($this->view['attributes']);
        $colModel = [];
        $in = [];
        $not_in = [];
        $js = "";

        for ($i = 0; $i < $count; $i++) {
            //SI EL CAMPO OCULTO NO ESTA PRESENTE ENTONCES NO SE OCULTARA
            if (!isset($this->view['attributes'][$i]['hidden'])) {
                $this->view['attributes'][$i]['hidden'] = 0;
            }
            //SI WIDTH NO ESTA PRESENTE SE ASUME 0
            if (!isset($this->view['attributes'][$i]['width'])) {
                $this->view['attributes'][$i]['width'] = 0;
            }
            //SI SORTABLE NO ESTA PRESENTE SE ASUME true
            if (!isset($this->view['attributes'][$i]['sortable'])) {
                $this->view['attributes'][$i]['sortable'] = true;
            }
            //AGREGAMOS UNA NUEVA COLUMNA
            array_push($colModel, "{'columnName': '{$this->view['attributes'][$i]['name']}', 'width':'{$this->view['attributes'][$i]['width']}', 'hidden': {$this->view['attributes'][$i]['hidden']}, 'label': '{$this->viewObject->getAttributeLabel($this->view['attributes'][$i]['name'])}', 'sortable': " . CJSON::encode($this->view['attributes'][$i]['sortable']) . "}");
            //SI EL VALOR IN EXISTE ENTONCES AGREGAMOS LA CONDICION
            if (isset($this->view['attributes'][$i]['in'])) {
                array_push($in, "'" . $this->view['attributes'][$i]['name'] . "':" . $this->view['attributes'][$i]['in']);
            }
            //SI EL VALOR NOT_IN EXISTE ENTONCES AGREGAMOS LA CONDICION
            if (isset($this->view['attributes'][$i]['not_in'])) {
                array_push($not_in, "'" . $this->view['attributes'][$i]['name'] . "':" . $this->view['attributes'][$i]['not_in']);
            }
            //VERIFICAMOS LOS TIPOS ASOCIADOS A CADA ATTRIBUTO DE LA VISTA
            if (isset($this->view['attributes'][$i]['types'])) {

                foreach ($this->view['attributes'][$i]['types'] as $type) {
                    //SI ES EL ATRIBUTO ID TENDRA UN TRATO ESPECIAL
                    if ($type == 'id' && isset($this->maintenance)) {
                        //PREVIEW
                        if (array_key_exists('preview', $this->maintenance['buttons']) && $this->maintenance['buttons']['preview']['access']) {
                            $js .= "USLinkStatus('{$this->maintenance['buttons']['preview']['id']}', true, {id: ui.item.{$this->view['attributes'][$i]['name']}});\n";
                        }
                        //UPDATE
                        if (array_key_exists('update', $this->maintenance['buttons']) && $this->maintenance['buttons']['update']['access']) {
                            $js .= "USLinkStatus('{$this->maintenance['buttons']['update']['id']}', true, {id: ui.item.{$this->view['attributes'][$i]['name']}});\n";
                        }
                        //DELETE
                        if (array_key_exists('delete', $this->maintenance['buttons']) && $this->maintenance['buttons']['delete']['access']) {
                            $js .= "USLinkStatus('{$this->maintenance['buttons']['delete']['id']}', true, {id: ui.item.{$this->view['attributes'][$i]['name']}});\n";
                        }
                    }
                    //ASOCIAMOS EL VALOR DEL ATTRIBUTO A SU RESPECTIVO CAMPO DE TEXTO
                    $js .= "$('#{$this->{$type . "_id"}}').val(ui.item.{$this->view['attributes'][$i]['name']});\n";
                    //GUARDAMOS EL NOMBRE DEL ATTRIBUTO
                    $this->{$type . "_attribute"} = $this->view['attributes'][$i]['name'];
                }
            }

            $js .= "var {$this->view['attributes'][$i]['name']} = ui.item.{$this->view['attributes'][$i]['name']};\n";
            //SI EXISTE EL VALOR UPDATE ENTONCES EJECUTAREMOS CIERTA SENTENCIA JS/JAVASCRIPT
            if (isset($this->view['attributes'][$i]['update'])) {
                $js .= "{$this->view['attributes'][$i]['update']};\n";
            }
        }
        if (isset($this->onselect)) {
            $js .= "{$this->onselect}\n";
        }

        //SI FOCUS_ID EXISTE ENTONCES AL SELECCIONAR UN VALOR ALGUN CONTROL GANARA ENFOQUE
        if ($this->focus) {
            if (isset($this->focus_id)) {
                $js .= "$('#{$this->focus_id}').focus().select();\n";
            }//SI NO EXISTE EL FOCO LO RECIBIRA EL SIGUIENTE ELEMENTO DESPUES DE TEXT
            else {
                $js .= "var inputs = $('#{$this->aux_id}').closest('form').find(':focusable:not([readonly])').not('a');
            inputs.eq(inputs.index($('#{$this->aux_id}')) + 1).focus().select();\n";
            }
        }

        //Registramos JS
        SIANAssets::registerScriptFile("other/jquery.contextMenu/jquery.contextMenu.js");
        SIANAssets::registerScriptFile("other/jquery.contextMenu/jquery.ui.position.js");
        SIANAssets::registerScriptFile("other/autocomplete/plugin/jquery.i18n.properties-1.0.9");
        SIANAssets::registerScriptFile("other/autocomplete/plugin/jquery.ui.combogrid-1.6.3.js");
        SIANAssets::registerScriptFile("js/us-autocomplete.js");
        //CSS
        SIANAssets::registerCssFile("other/jquery.contextMenu/jquery.contextMenu.css");
        SIANAssets::registerCssFile("other/autocomplete/css/smoothness/jquery.ui.combogrid.css");
        SIANAssets::registerCssFile("other/autocomplete/css/smoothness/jquery-ui-1.10.1.custom.css");

//        if (!$this->readonly) {
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        $(document).ready(function() { 
            $('#{$this->aux_id}').combogrid({
               url: '{$this->controller->createUrl("/widget/autocomplete")}',
               autoChoose:" . ($this->autoChoose ? "true" : "false") . ",
               width: '{$this->width}',    
               delayChoose:0,
               debug:false,
               replaceNull: true,
               selectAttributes: ['" . implode("','", $this->selectAttributes) . "'],
               searchAttributes: ['" . implode("','", $this->searchAttributes) . "'],
               params: {$this->view['params']},
               in: {" . implode(',', $in) . "},    
               not_in: {" . implode(',', $not_in) . "},    
               colModel: [" . implode(',', $colModel) . "],
               sidx: '{$this->sidx}',
               sord: '{$this->sord}',
               modelClass: '{$this->view['model']}',
               scenario: '" . (isset($this->view['scenario']) ? $this->view['scenario'] : '') . "',
               searchButton: true,
               resetButton: true,
               select: function( event, ui ) {
                   {$js}
                   return false;
               },
               focus: function( event, ui ) {
                   return false;
               },
           });

            let timer;
            
            //DETECTAR EVENTO DE TECLADO DIFERENTE AL ENTER (CAMPO AUX)
            " . ($this->useOnkeydown ? "
                $('body').on('keydown', '#{$this->aux_id}', function(e){
                    clearTimeout(timer)
                    timer = setTimeout(function() {
                        const value = e.target.value;
                        if(value !== null && value.toString().trim() !== '' ) {
                            if (e.which !== 13 && e.which != 38 && e.which != 40) {
                                $('#{$this->search_id}').click();
                            }
                        }
                    },500)
                });
            " : "") . "
        });

        //AL HACER CLICK EN BUSCAR MARCAMOS QUE SE ABRIO EL BUSCADOR
        $('body').on('click', '#{$this->search_id}', function(e){  
            if(!$('#{$this->search_id}').hasAttr('disabled') === true){
                e.preventDefault();
                {$this->onsearch};
                $('#{$this->search_id}_real').click();
                $('#{$this->aux_id}').notify('Buscando...', {
                    className: 'info',
                    showDuration: 50,
                    hideDuration: 50,
                    autoHideDelay: 500,
                })
            }
        });
        //AL HACER CLICK EN RESET BORRAMOS EL DATO SELECCIONADO
        $('body').on('click', '#{$this->reset_id}',function(e){
            if(!$('#{$this->reset_id}').hasAttr('disabled') === true){
                $('#{$this->text_id}').val(null);
                $('#{$this->id_id}').val(null);
                $('#{$this->value_id}').val(null);
                " . (isset($this->maintenance) && array_key_exists('preview', $this->maintenance['buttons']) ? "USLinkStatus('{$this->maintenance['buttons']['preview']['id']}', false, {});" : "") . "
                " . (isset($this->maintenance) && array_key_exists('update', $this->maintenance['buttons']) ? "USLinkStatus('{$this->maintenance['buttons']['update']['id']}', false, {});" : "") . "
                " . (isset($this->maintenance) && array_key_exists('delete', $this->maintenance['buttons']) ? "USLinkStatus('{$this->maintenance['buttons']['delete']['id']}', false, {});" : "") . "
                $('#{$this->aux_id}').focus();    
                {$this->onreset};
                $('#{$this->reset_id}_real').click();
                {$this->afterreset};
            }
        });

        //AL PRESIONAR ENTER SOBRE EL CAMPO AUX SIMULAREMOS UN CLICK EN EL BOTON BUSCAR
        $('body').on('keypress', '#{$this->aux_id}', function(e){
            if (e.which === 13) {
                e.stopPropagation();
                e.preventDefault();
                $('#{$this->search_id}').click();
            }
        });


            
        //DESPUES DE CREAR/EDITAR UN NUEVO MODELO DESDE MAINTENANCE EJECUTAREMOS UN SCRIPT
        $('#{$this->aux_id}').data('afterSave', function(data){
            
            $.ajax({
                type: 'post',
                url: '{$this->controller->createUrl("/widget/objectView")}',
                data: {
                    viewClass: '{$this->view['model']}',
                    scenario: '{$this->view['scenario']}',
                    aux_attribute: '{$this->aux_attribute}',
                    id_attribute: '{$this->id_attribute}',
                    value_attribute: '{$this->value_attribute}',
                    text_attribute: '{$this->text_attribute}',
                    id: data.primaryKey
                },
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },                  
                success: function(odata) {
                
                    if(isset(odata))
                    {
                        //ASIGNAMOS LOS VALORES A LOS CAMPOS DEL WIDGET
                        $('#{$this->aux_id}').val(odata.{$this->aux_attribute});
                        $('#{$this->search_id}').click();
                    }
                    else
                    {
                        bootbox.alert(us_message('No se pudo cargar el objeto asociado a \'{$this->_getLabel()}\'', 'error'));
                    }

                    window.active_ajax--;
                },
                error: function(request, status, error) {
                    bootbox.alert(us_message(request.responseText, 'error'));
                    window.active_ajax--;
                },
                dataType: 'json'
            });                
        });

        //DESPUES DE ELIMINAR UN NUEVO MODELO DESDE MAINTENANCE EJECUTAREMOS UN SCRIPT
        $('#{$this->aux_id}').data('afterDelete', function(data){
            $('#{$this->reset_id}').click();    
        });

        unfocusable();

        ", CClientScript::POS_END);
        //}
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id={$this->id} class='us-autocomplete' data-reset_id='{$this->reset_id}' data-search_id='{$this->search_id}' data-aux_id='{$this->aux_id}' >";
        $col_dif = 12 - $this->col;
        $sum = $this->hideText ? $col_dif : 0;

        echo "<div class='form-group " . ($this->error ? 'has-error' : '') . "'>";
        if (isset($this->label)) {
            $b_required = ((isset($this->model) && $this->model->isAttributeRequired($this->attribute)) || $this->required );
            echo CHtml::label($this->label . ($b_required ? " <span class='required'>*</span>" : ""), $this->aux_id, array(
                'class' => 'control-label ' . ($b_required ? 'required' : '')
            ));
        }
        echo "<div class='row'>";
        echo "<div class='col-lg-" . ($this->col + $sum) . " col-md-" . ($this->col + $sum) . " col-sm-" . ($this->col + $sum) . " col-xs-12'>";
            $this->renderAux();
        echo "</div>";

        if ($this->hideText) {
            echo CHtml::hiddenField($this->text_name, $this->getValue('text'), array(
                'id' => $this->text_id,
                'class' => 'us-autocomplete-text',
                'readonly' => true,
            ));
        } else {
            echo "<div class='col-lg-{$col_dif} col-md-{$col_dif} col-sm-{$col_dif} col-xs-12'>";
            echo "<div class='" . (isset($this->maintenance) ? 'input-group' : '') . "'>";
            echo CHtml::textField($this->text_name, $this->getValue('text'), array(
                'id' => $this->text_id,
                'class' => 'form-control us-autocomplete-text',
                'placeholder' => '<<< Ingrese un valor',
                'readonly' => true,
            ));
            if (isset($this->maintenance)) {
                echo "<span class='input-group-addon'>";
                echo $this->renderMaintenance();
                echo "</span>";
            }
            echo "</div>";
            echo "</div>";
        }

        echo "</div>"; //ROW
        if (isset($this->hint)) {
            echo "<span class='help-block'>{$this->hint}</span>";
        }
        echo "</div>"; //GROUP
        echo "</div>"; //DIV
    }

    private function renderAux() {
        echo "<div class='input-group'>";
        echo CHtml::textField($this->aux_name, $this->getValue('aux'), array(
            'id' => $this->aux_id,
            'class' => 'us-autocomplete-aux form-control',
            'placeholder' => $this->placeholder,
            'autocomplete' => 'off',
            'readonly' => $this->readonly,
        ));
        echo "<span class='input-group-addon'>";
        $this->widget('application.widgets.USLink', array(
            'id' => $this->search_id,
            'icon' => 'fa fa-lg fa-search',
            'title' => 'Buscar',
            'visible' => !$this->readonly,
            'class' => 'initial',
        ));
        echo '&nbsp';
        $this->widget('application.widgets.USLink', array(
            'id' => $this->reset_id,
            'icon' => 'fa fa-lg fa-eraser',
            'title' => 'Limpiar',
            'visible' => !$this->readonly,
            'class' => 'initial',
        ));
        echo CHtml::link(null, null, array(
            'id' => $this->search_id . '_real',
            'class' => "hide {$this->aux_id} cg-searchButton",
            'disabled' => $this->readonly,
        ));
        echo CHtml::link(null, null, array(
            'id' => $this->reset_id . '_real',
            'class' => "hide {$this->aux_id} cg-resetButton",
            'disabled' => $this->readonly,
        ));
        if ($this->hideText && isset($this->maintenance)) {
            echo '&nbsp';
            echo $this->renderMaintenance();
        }
        echo "</span>";
        echo "</div>";
        if (isset($this->model)) {
            echo CHtml::error($this->model, $this->attribute, array(
                'class' => 'help-block ' . ($this->error ? 'error' : '')
            ));
        }
        echo CHtml::hiddenField($this->id_name, $this->getValue('id'), array(
            'id' => $this->id_id,
            'class' => 'us-autocomplete-id ' . $this->id_class,
        ));
        echo CHtml::hiddenField($this->name, $this->getValue('value'), array(
            'id' => $this->value_id,
            'class' => 'us-autocomplete-value ' . $this->value_class,
        ));
    }

    private function renderMaintenance() {

        $links = [];
        $value = $this->getValue('value');

        if (array_key_exists('preview', $this->maintenance['buttons'])) {

            $links[] = $this->controller->widget('application.widgets.USLink', array(
                'id' => $this->maintenance['buttons']['preview']['id'],
                'icon' => 'fa fa-lg fa-eye',
                'title' => 'Vista previa',
                'class' => 'form' . (isset($value) && strlen($value) > 0 ? ' initial' : ''),
                'route' => "/{$this->maintenance['module']}/{$this->maintenance['controller']}/preview",
                'data' => array_merge(array(
                    'id' => $this->viewObject->{$this->id_attribute},
                    'modal_id' => $this->controller->getServerId(),
                    'parent_id' => $this->parent_id
                        ), $this->maintenance['buttons']['preview']['data']),
                'visible' => isset($this->model->{$this->attribute}) ? $this->maintenance['buttons']['preview']['access'] : false
                    ), true);
        }

        if (array_key_exists('create', $this->maintenance['buttons'])) {

            $links[] = $this->controller->widget('application.widgets.USLink', array(
                'id' => $this->maintenance['buttons']['create']['id'],
                'icon' => 'fa fa-lg fa-plus',
                'title' => 'Crear',
                'class' => 'form initial',
                'route' => "/{$this->maintenance['module']}/{$this->maintenance['controller']}/create",
                'data' => array_merge(array(
                    'modal_id' => $this->controller->getServerId(),
                    'parent_id' => $this->parent_id,
                    'type' => 'afterSave',
                    'element_id' => $this->aux_id
                        ), $this->maintenance['buttons']['create']['data']),
                'visible' => $this->readonly ? false : $this->maintenance['buttons']['create']['access'],
                    ), true);
        }

        if (array_key_exists('update', $this->maintenance['buttons'])) {

            $links[] = $this->controller->widget('application.widgets.USLink', array(
                'id' => $this->maintenance['buttons']['update']['id'],
                'icon' => 'fa fa-lg fa-pencil',
                'title' => 'Editar',
                'class' => 'form' . (isset($value) && strlen($value) > 0 ? ' initial' : ''),
                'route' => "/{$this->maintenance['module']}/{$this->maintenance['controller']}/update",
                'data' => array_merge(array(
                    'id' => $this->viewObject->{$this->id_attribute},
                    'modal_id' => $this->controller->getServerId(),
                    'parent_id' => $this->parent_id,
                    'type' => 'afterSave',
                    'element_id' => $this->aux_id,
                        ), $this->maintenance['buttons']['update']['data']),
                'visible' => isset($this->model->{$this->attribute}) ? $this->maintenance['buttons']['update']['access'] : false
                    ), true);
        }

        if (array_key_exists('delete', $this->maintenance['buttons'])) {

            $links[] = $this->controller->widget('application.widgets.USLink', array(
                'id' => $this->maintenance['buttons']['delete']['id'],
                'icon' => 'fa fa-lg fa-times',
                'title' => 'Eliminar',
                'class' => 'confirm' . (isset($value) && strlen($value) > 0 ? ' initial' : ''),
                'route' => "/{$this->maintenance['module']}/{$this->maintenance['controller']}/delete",
                'data' => array_merge(array(
                    'id' => $this->viewObject->{$this->id_attribute},
                    'modal_id' => $this->controller->getServerId(),
                    'parent_id' => $this->parent_id,
                    'type' => 'afterDelete',
                    'element_id' => $this->aux_id
                        ), $this->maintenance['buttons']['delete']['data']),
                'visible' => $this->readonly ? false : (isset($this->model->{$this->attribute}) ? $this->maintenance['buttons']['delete']['access'] : false)
                    ), true);
        }

        return implode('&nbsp', $links);
    }

    //CREAMOS UNA INSTANCIA DE LA VISTA O CARGAMOS UNA EXISTENTE
    private function getView() {

        if (!isset($this->view['scenario'])) {
            $this->view['scenario'] = null;
        }
        //Inicializamos
        $view = new $this->view['model']($this->view['scenario']);
        $view->unsetAttributes();

        foreach ($this->view['attributes'] as $item) {
            if (isset($item['types']) && in_array('value', $item['types'])) {

                if (!USString::isBlank($this->value)) {

                    if ($view instanceof DpBase) {
                        $_view = $view->addEqualCondition($item['name'], $this->value)->find();
                    } else {
                        $_view = $view->findByAttributes(array(
                            $item['name'] => $this->value
                                ), array(
                            'select' => $this->selectAttributes
                        ));
                    }
                }
            }

            if (isset($_view)) {
                return $_view;
            }
        }

        return $view;
    }

    //DE LA INSTANCIA OBTENEMOS LOS VALORES PARA CARGAR LOS CAMPOS DEL BUSCADOR
    private function getValue($type) {
        foreach ($this->view['attributes'] as $item) {
            if (isset($item['types']) && in_array($type, $item['types']))
                return $this->viewObject->{$item['name']};
        }
    }

    private function _getLabel() {
        return isset($this->model) && is_null($this->label) ? $this->model->getAttributeLabel($this->attribute) : $this->label;
    }

}
