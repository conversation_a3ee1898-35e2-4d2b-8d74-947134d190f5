<?php

interface IDocumentRepository {

    /**
     * @param int $scenarioId
     * @param int|null $internal
     * @param int|null $serialized
     * @param string|null $sunatCode
     * @param string|null $documentCode
     * @param string $person_type
     * @param int|null $default
     * @return array|null
     */
    public function getFirstDocument(
            $scenarioId,
            $internal = null,
            $serialized = null,
            $sunatCode = null,
            $documentCode = null,
            $person_type = Document::PERSON_TYPE_ANY,
            $default = null
    );

    /**
     * @param int $scenarioId
     * @param int|null $internal
     * @param int|null $serialized
     * @param string|null $sunatCode
     * @param string|null $documentCode
     * @param string $person_type
     * @param int|null $default
     * @return array
     */
    public function getAllDocuments(
            $scenarioId,
            $internal = null,
            $serialized = null,
            $sunatCode = null,
            $documentCode = null,
            $person_type = Document::PERSON_TYPE_ANY,
            $default = null
    );
}
