(function(a){a.jgrid=a.jgrid||{};a.extend(a.jgrid,{defaults:{recordtext:"Pregled {0} - {1} od {2}",emptyrecords:"Ne postoji nijedan zapis",loadtext:"Učitivanje...",pgtext:"Strana {0} od {1}"},search:{caption:"Traženje...",Find:"Traži",Reset:"Resetuj",odata:["jednako","nije jednako","manje","manje ili jednako","veće","veće ili jednako","počinje sa","ne počinje sa","je u","nije u","zavr<PERSON>va sa","ne završava sa","sadrži","ne sadrži"],groupOps:[{op:"AND",text:"sva"},{op:"OR",text:"bilo koje"}],matchText:" primje<PERSON>",rulesText:" pravila"},edit:{addCaption:"<PERSON><PERSON><PERSON> zapis",editCaption:"<PERSON>zmjeni zapis",bSubmit:"Pošal<PERSON>",bCancel:"Odustani",bClose:"Zatvori",saveData:"Podatak je izmjenjen! Sačuvaj izmjene?",bYes:"Da",bNo:"Ne",bExit:"Odustani",msg:{required:"Polje je obavezno",number:"Unesite ispravan broj",minValue:"vrijednost mora biti veća od ili jednaka sa ",maxValue:"vrijednost mora biti manja ili jednaka sa",email:"nije ispravna email adresa, nije valjda da ne umiješ ukucati mail!?",integer:"Ne zajebaji se unesi cjelobrojnu vrijednost ",date:"Unesite ispravan datum",url:"nije ispravan URL. Potreban je prefiks ('http://' or 'https://')",nodefined:" nije definisan!",novalue:" zahtjevana je povratna vrijednost!",customarray:"Prilagođena funkcija treba da vrati niz!",customfcheck:"Prilagođena funkcija treba da bude prisutana u slučaju prilagođene provjere!"}},view:{caption:"Pogledaj zapis",bClose:"Zatvori"},del:{caption:"Izbrisi",msg:"Izbrisi izabran(e) zapise(e)?",bSubmit:"Izbriši",bCancel:"Odbaci"},nav:{edittext:"",edittitle:"Izmjeni izabrani red",addtext:"",addtitle:"Dodaj novi red",deltext:"",deltitle:"Izbriši izabran red",searchtext:"",searchtitle:"Nađi zapise",refreshtext:"",refreshtitle:"Ponovo učitaj podatke",alertcap:"Upozorenje",alerttext:"Izaberite red",viewtext:"",viewtitle:"Pogledaj izabrani red"},col:{caption:"Izaberi kolone",bSubmit:"OK",bCancel:"Odbaci"},errors:{errcap:"Greška",nourl:"Nije postavljen URL",norecords:"Nema zapisa za obradu",model:"Dužina modela colNames <> colModel!"},formatter:{integer:{thousandsSeparator:" ",defaultValue:"0"},number:{decimalSeparator:".",thousandsSeparator:" ",decimalPlaces:2,defaultValue:"0.00"},currency:{decimalSeparator:".",thousandsSeparator:" ",decimalPlaces:2,prefix:"",suffix:"",defaultValue:"0.00"},date:{dayNames:["Ned","Pon","Uto","Sre","Čet","Pet","Sub","Nedelja","Ponedeljak","Utorak","Srijeda","Četvrtak","Petak","Subota"],monthNames:["Jan","Feb","Mar","Apr","Maj","Jun","Jul","Avg","Sep","Okt","Nov","Dec","Januar","Februar","Mart","April","Maj","Jun","Jul","Avgust","Septembar","Oktobar","Novembar","Decembar"],AmPm:["am","pm","AM","PM"],S:function(b){return b<11||b>13?["st","nd","rd","th"][Math.min((b-1)%10,3)]:"th"},srcformat:"Y-m-d",newformat:"d/m/Y",masks:{ISO8601Long:"Y-m-d H:i:s",ISO8601Short:"Y-m-d",ShortDate:"n/j/Y",LongDate:"l, F d, Y",FullDateTime:"l, F d, Y g:i:s A",MonthDay:"F d",ShortTime:"g:i A",LongTime:"g:i:s A",SortableDateTime:"Y-m-d\\TH:i:s",UniversalSortableDateTime:"Y-m-d H:i:sO",YearMonth:"F, Y"},reformatAfterEdit:false},baseLinkUrl:"",showAction:"",target:"",checkbox:{disabled:true},idName:"id"}})})(jQuery);