<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of ApiController
 *
 * <AUTHOR>
 * http://www.yiiframework.com/wiki/175/how-to-create-a-rest-api/
 * https://www.restapitutorial.com/httpstatuscodes.html
 */
class WebhookController extends USBaseController {

    protected function checkWebhookAuth() {
        //Obtenemos headers
        $a_headers = USREST::getInsensitiveHeaders();
        //Verificamos que exista Authorization
        if (!isset($a_headers['app-authorization'])) {
            USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'No se ha especificado el token de aplicación');
            Yii::app()->end();
        }
        $s_jwt = $a_headers['app-authorization'];
        //Verificamos token
        $o_token_data = SIANJWT::decodeToken($s_jwt);
        if ($o_token_data == false) {
            SIANJWT::deleteToken($s_jwt);
            USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'EL TOKEN no es válido!');
            Yii::app()->end();
        }

        //Obtenemos data del token
        $this->apiUserToken = $o_token_data;

        //Verificamos que sea el tipo de token correcto
        if ($this->apiUserToken->token_type !== self::TOKEN_TYPE_PROXY) {
            USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'EL TOKEN de autorización de la aplicación no es correcto, posiblemente envió el de usuario!');
            Yii::app()->end();
        }

        //Verificamos que el usuario API corresponda con el API que se está usando
        switch ($this->module->id) {
            case 'api':
                if ($this->apiUserToken->type !== ApiUser::TYPE_WEB) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La autenticación no corresponde a API WEB. Autentíquese en el API WEB!');
                    Yii::app()->end();
                }
                break;
            case 'apiLocker':
                if ($this->apiUserToken->type !== ApiUser::TYPE_LOCKER) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La autenticación no corresponde a API LOCKER. Autentíquese en el API LOCKER.');
                    Yii::app()->end();
                }
                break;
            case 'apiPos':
                if ($this->apiUserToken->type !== ApiUser::TYPE_POS) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La autenticación no corresponde a API POS. Autentíquese en el API POS.');
                    Yii::app()->end();
                }
                break;
            case 'sharedApi':
                if (!in_array($this->apiUserToken->type, [ApiUser::TYPE_WEB, ApiUser::TYPE_POS])) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La autenticación no corresponde a un API válida. Autentíquese en un API válida.');
                    Yii::app()->end();
                }
                break;
            default:
                break;
        }

        $i_expiration = SIANTime::substract($this->apiUserToken->expiration, SIANTime::now());

        if ($i_expiration < 0) {
            SIANJWT::deleteToken($s_jwt);
            USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'EL TOKEN ha expirado!');
            Yii::app()->end();
        }

        //Verificamos si tiene acceso para la ruta
        $s_route = $this->route;
        $b_access = ApiUser::checkRoute($this->apiUserToken->api_user_id, $s_route);

        if (!$b_access) {
            USREST::sendResponse(USREST::CODE_FORBIDDEN, 'Las credenciales de aplicación no permiten realizar esta acción!');
            Yii::app()->end();
        }

        return true;
    }

}
