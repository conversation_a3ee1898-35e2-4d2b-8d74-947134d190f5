<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANUpload
 *
 * <AUTHOR>
 */
class SIANDownload {

    const ZIP_EXTENSION = ".zip";

    /**
     * Obtiene la carpeta de cargas para la empresa
     * @param string $p_s_cms Tipo de contenido
     * @return string Path de cargas
     */
    public static function getDownloadDir() {
        $s_download_dir = Yii::app()->params['download_dir'];

        if (!is_dir($s_download_dir)) {
            mkdir($s_download_dir, 0755);
        }

        return $s_download_dir . DIRECTORY_SEPARATOR;
    }

    /**
     * Obtiene la dirección URL de subidas
     * @param string $p_s_cms Tipo de contenido
     * @return type
     */
    public static function getDownloadUrl() {
        return Yii::app()->params['download_url'];
    }

}
