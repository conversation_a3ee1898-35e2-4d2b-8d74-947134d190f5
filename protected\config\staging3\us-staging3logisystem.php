<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'staging3logisystem.siansystem.com/admin';
$domain = 'https://staging3logisystem.siansystem.com';
$report_domain = 'rstaging3.siansystem.com';
$org = 'logisystem';
$us = 'us_staging3';
$database_server = '69.10.37.246';
$database_name = 'logisystem_staging3';
$database_username = 'sian_test';
$database_password = '75nppt6vr57lx4';
$mongo_enabled = true;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_EFACT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'logisystem.p12';
$e_billing_certificate_pass = 'h8KjwvRKEW3us8X2';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20481818991BILLING7',
        'password' => 'Billing7'
    ],
    YII_OSE_EFACT => [
        'username' => '20481818991',
        'password' => 'G00FmvemUx'
    ]
];
$e_billing_rest_credentials = [
    'client_id' => '8abdcc8a-7dcc-4438-a6c4-b234c74a2e2a',
    'client_secret' => 'kmaAgaj/J7ktMdMdAWWGtw=='
];

$e_billing_send_automatically_to_client = false;
//
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_STAGING;
$environment = YII_ENVIRONMENT_STAGING;
$admin_emails = array(
    'Logisystem SIAN' => '<EMAIL>',
);

