<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'sian/admin';
$domain = 'http://sian';
$report_domain = '192.168.1.61';
$hbs_url = 'http://hbs/';
$org = 'hardtech';
$database_server = '127.0.0.1';
$database_port = '4040';
$database_name = 'hardtech_reserved_stock';
$database_username = 'root';
$database_password = 'Sist3m@s';
$mongo_server = '127.0.0.1';
$mongo_password = 'Sist3m@s';
$report_server = '192.168.1.61';
//
$e_billing_ose = YII_OSE_EFACT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'hardtech.p12';
$e_billing_certificate_pass = 'LLWJBvmYSQszfcMf';
$e_billing_ri = '0620050000126';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20481066094BILLING7',
        'password' => 'EXITO123'
    ],
    YII_OSE_EFACT => [
        'username' => '20481066094',
        'password' => 'CzUKk08RZ3'
    ]
];
//
$gii_password = '123qaz';
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';

