<?php

class SIANProductListGrid extends CWidget {

    public $id;
    public $form;
    public $productList;
    public $models;
    public $viewScenario;
    public $readonly = 0;
    public $show_amount = false;
    public $show_observations = false;
    public $combinations = false;
    public $only_allowed_divisions = false;
    public $allow_service = false;
    public $use_related = 0;
    public $allow_filter_type = false;
    public $allow_filter_presentation = false;
    public $title = 'Productos';
    public $ifixed = ProductListMovement::IFIXED;
    public $tfixed = ProductListMovement::TFIXED;
    public $istep = ProductListMovement::ISTEP;
    public $context = 'default';
    public $hidden = false;
    public $back_link = '';
    public $share_ids = false;
    public $onready = '';
    public $onChangeIds = '';
    public $onChangeCCDependence = ''; //Para hacer un puente    
    public $observation_input_id;
    public $weight_input_id; // input Peso Bruto
    public $number_packages_input_id; // input N° de bultos
    public $allow_new_products = false;
    public $delivery_date_by_item = false;
    public $description_by_item = false;
    public $items_property = 'tempItems';
    public $item_name = 'Item';
    public $from_template = false;
    public $show_stock = true;
    public $show_load_products = true;
    public $show_allow_duplicate = true;
    public $filter_by_warehouse = false;
    public $aux_widget_id;
    public $massive_transformation_type = false;
    public $disable_name = false;
    public $amount_mode = false;
    public $amount_id;
    public $transform_type_items;
    //PRIVATE
    private $enable_aux_widget = 'false';
    private $controller;
    private $presentationMode;
    private $presentationUrl;
    private $autocomplete_id;
    private $item_aux_id;
    private $item_value_id;
    private $item_text_id;
    private $item_item_type_id;
    private $item_product_type_id;
    private $item_pres_quantity_id;
    private $item_equivalence_id;
    private $item_allow_decimals_id;
    private $item_unit_stock_id;
    private $item_mixed_stock_label_id;
    private $item_min_amount_pen_id;
    private $item_min_amount_usd_id;
    private $item_amount_id;
    private $warehouse_input_id;
    private $add_button_id;
    private $allow_duplicate_checkbox_id;
    private $summary_id;
    private $preview_access;
    private $stockUrl;
    private $explainStockUrl;
    private $item_filter_type_id;
    private $item_filter_presentation_id;
    private $value_filter_type_id;
    private $value_filter_presentation_id;
    private $input_search_attribute_id;
    private $input_transformation_type_id;
    private $input_file_id;
    private $url_api_proccess_file;
    private $loader_id;
    private $min_days;
    public $multiplier_id;
    private $route;

    public function init() {

        $this->controller = Yii::app()->controller;
        if ($this->show_amount) {
            $this->presentationMode = SpGetProductPresentations::MODE_COMBOBOX_WITH_PRICES;
        } else {
            $this->presentationMode = SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES;
        }
        $this->presentationUrl = $this->controller->createUrl("/movement/getPresentations");
        //GRID ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->observation_input_id = isset($this->observation_input_id) ? $this->observation_input_id : Yii::app()->controller->getServerId();
        // Variables para `peso` y `numero de bultos`
        $this->weight_input_id = isset($this->weight_input_id) ? $this->weight_input_id : Yii::app()->controller->getServerId();
        $this->number_packages_input_id = isset($this->number_packages_input_id) ? $this->number_packages_input_id : Yii::app()->controller->getServerId();

        $this->enable_aux_widget = isset($this->aux_widget_id) ? 'true' : 'false';
        $this->url_api_proccess_file = Yii::app()->params['url_api_sian'] . 'parse-excel';

        //PRIVATE
        $this->autocomplete_id = $this->controller->getServerId();
        $this->item_aux_id = $this->controller->getServerId();
        $this->item_value_id = $this->controller->getServerId();
        $this->item_text_id = $this->controller->getServerId();
        $this->item_item_type_id = $this->controller->getServerId();
        $this->item_product_type_id = $this->controller->getServerId();
        $this->item_pres_quantity_id = $this->controller->getServerId();
        $this->item_equivalence_id = $this->controller->getServerId();
        $this->item_allow_decimals_id = $this->controller->getServerId();
        $this->item_unit_stock_id = $this->controller->getServerId();
        $this->item_mixed_stock_label_id = $this->controller->getServerId();
        $this->item_min_amount_pen_id = $this->controller->getServerId();
        $this->item_min_amount_usd_id = $this->controller->getServerId();
        $this->item_amount_id = $this->controller->getServerId();
        $this->warehouse_input_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();
        $this->allow_duplicate_checkbox_id = Yii::app()->controller->getServerId();
        $this->summary_id = $this->controller->getServerId();

        $this->item_filter_type_id = $this->controller->getServerId();
        $this->item_filter_presentation_id = $this->controller->getServerId();

        $this->value_filter_type_id = $this->controller->getServerId();
        $this->value_filter_presentation_id = $this->controller->getServerId();
        $this->input_file_id = $this->id . $this->controller->getServerId();

        $this->loader_id = $this->id . $this->controller->getServerId();
        $this->input_search_attribute_id = $this->id . $this->controller->getServerId();
        $this->input_transformation_type_id = $this->controller->getServerId();
        $this->amount_id = isset($this->amount_id) ? $this->amount_id : $this->controller->getServerId();

        $this->min_days = $this->controller->getOrganization()->globalVar->min_days_to_deliver_requirement;
        $this->route = Yii::app()->getController()->getRoute();

        SIANAssets::registerScriptFile('other/moment/moment.js');

        $product_ids = [];
        foreach ($this->models as $model) {
            foreach ($model->{$this->items_property} as $o_item) {
                if (isset($o_item->itemObj->product_id)) {
                    $product_ids[] = $o_item->itemObj->product_id;
                }
            }
        }

        //Si hay productos...
        if (count($product_ids) > 0) {
            $presentationItems = SpGetProductPresentations::getAssociative($this->presentationMode, $product_ids, $this->controller->getOrganization()->globalVar->display_currency);
        }

        //PRODUCT ITEMS
        $instances = [];
        foreach ($this->models as $model) {
            $items = [];
            foreach ($model->{$this->items_property} as $o_item) {

                if ($o_item->itemObj->hasErrors('product_name')) {
                    $o_item->addError('product_name', $o_item->getError('product_name'));
                }

                $items[] = [
                    'product_id' => $o_item->itemObj->product_id,
                    'product_name' => isset($o_item->itemObj->product_name) ? $o_item->itemObj->product_name : '',
                    'description' => isset($o_item->itemObj->description) ? $o_item->itemObj->description : '',
                    'item_type_id' => $o_item->itemObj->item_type_id,
                    'product_type' => $o_item->itemObj->product_type,
                    'pres_quantity' => $o_item->itemObj->pres_quantity,
                    'presentationItems' => !in_array($o_item->itemObj->product_type, [Product::TYPE_NEW, Product::TYPE_GROUP]) ? $presentationItems[$o_item->itemObj->product_id] : [],
                    'equivalence' => $o_item->itemObj->equivalence,
                    'transform_type' => isset($o_item->transform_type) ? $o_item->transform_type : null,
                    'allow_decimals' => $o_item->itemObj->allow_decimals,
                    'min_amount_pen' => isset($o_item->min_amount_pen) ? round($o_item->min_amount_pen, $this->ifixed) : 0,
                    'min_amount_usd' => isset($o_item->min_amount_pen) ? round($o_item->min_amount_usd, $this->ifixed) : 0,
                    'unit_stock' => isset($o_item->itemObj->unit_stock) ? $o_item->itemObj->unit_stock : 0,
                    'mixed_stock' => isset($o_item->itemObj->mixed_stock) ? $o_item->itemObj->mixed_stock : '',
                    'amount' => isset($o_item->{"amount_{$this->productList->movement->currency}"}) ? $o_item->{"amount_{$this->productList->movement->currency}"} : 0,
                    'combination_id' => isset($o_item->itemObj->combination_id) ? $o_item->itemObj->combination_id : '',
                    'combination_name' => isset($o_item->itemObj->combination_name) ? $o_item->itemObj->combination_name : '',
                    //Para enlace entre productos
                    'parent_item_id' => isset($o_item->itemObj->parent_item_id) ? $o_item->itemObj->parent_item_id : '',
                    'delivery_date' => isset($o_item->itemObj->delivery_date) ? $o_item->itemObj->delivery_date : null,
                    //
                    'errors' => $o_item->getAllErrors()
                ];
            }

            $instances[] = [
                'instance_id' => $model->instance_id . $this->item_name,
                'items' => $items,
                'errors' => $model->getErrors()
            ];
        }

        //URL
        $this->stockUrl = $this->controller->createUrl("/movement/loadCostsAndStocks");
        $this->explainStockUrl = $this->controller->createUrl("/logistic/merchandise/explainStock");
        //ACCESS
        $this->preview_access = $this->controller->checkRoute('/logistic/product/preview');
        //Registramos assets
        if ($this->combinations) {
            SIANAssets::registerScriptFile('other/select2/js/select2.min.js');
            SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
            SIANAssets::registerCssFile('other/select2/css/select2.css');
            SIANAssets::registerScriptFile('js/us-select2.js');
            SIANAssets::registerScriptFile('js/us-cost-level.js');
        }
        SIANAssets::registerScriptFile('js/us-span.js');
        SIANAssets::registerScriptFile('js/sian-product-list-grid.js');

        $isBuyValue = method_exists($this->productList, 'isBuy') ? $this->productList->isBuy() : false;

        $add_row = $this->route == 'treasury/cashOutOrder/create' && !Yii::app()->request->isPostRequest;
        $add_row = $add_row ? 'true' : 'false';

        $filter_presentation = $this->allow_filter_presentation === true ? 'true' : 'false';
        $filter_type = $this->allow_filter_type === false ? 'false' : 'true';

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        var grid = $('#{$this->id}');
        grid.data('view-access', " . json_encode($this->preview_access) . "); //ACCESS
        grid.data('view-url', '{$this->controller->createUrl('/logistic/product/preview')}'); //URL
        grid.data('load-products-url', '{$this->controller->createUrl('/widget/getDpModel')}'); //URL
        grid.data('count', 0); //COUNT
        grid.data('show_amount', " . CJSON::encode($this->show_amount) . ");
        grid.data('use_related', " . CJSON::encode($this->use_related) . ");
        grid.data('currency', '{$this->productList->movement->currency}');
        grid.data('exchange_rate', '{$this->productList->movement->exchange_rate}');
        grid.data('share_ids', " . CJSON::encode($this->share_ids) . ");
        grid.data('readonly', " . CJSON::encode($this->readonly) . ");
        grid.data('default_merchandise_measure', '{$this->controller->getDefaultMerchandiseMeasure()->abbreviation}');
        grid.data('allow_new_products', " . CJSON::encode($this->allow_new_products) . ");
        grid.data('allow_service', " . CJSON::encode($this->allow_service) . ");
        grid.data('summary_id', '{$this->summary_id}');
        grid.data('ifixed', {$this->ifixed});
        grid.data('tfixed', {$this->tfixed});
        grid.data('istep', {$this->istep});
        grid.data('independent_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INDEPENDENT . "');
        grid.data('inherit_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INHERIT . "');
        grid.data('validate_stock', " . CJSON::encode($this->productList->movement->validate_stock == 1) . ");
        grid.data('stockUrl', '{$this->stockUrl}');
        grid.data('load-products-url', '{$this->controller->createUrl('/widget/getDpModel')}'); //URL
        grid.data('explainStockUrl', '{$this->explainStockUrl}');
        grid.data('stock_mode', " . CJSON::encode($this->productList->movement->scenario->stock_mode) . ");                
        grid.data('exclude_id', " . CJSON::encode($this->productList->movement->kardex_unlock_exclude_id) . ");
        grid.data('allow_duplicate', " . ($this->productList->allow_duplicate == 1 ? 1 : 0) . ");  
        grid.data('combinations', " . CJSON::encode($this->combinations) . ");
        grid.data('weight_input_id', '{$this->weight_input_id}');
        grid.data('number_packages_input_id', '{$this->number_packages_input_id}');
        grid.data('delivery_date_by_item', " . ($this->delivery_date_by_item ? 1 : 0) . ");  
        grid.data('description_by_item', " . ($this->description_by_item ? 1 : 0) . "); 
        grid.data('is_buy', " . CJSON::encode($isBuyValue) . ");
        grid.data('items_property', '{$this->items_property}'); 
        grid.data('item_name','{$this->item_name}');
        grid.data('from_template','{$this->from_template}');
        grid.data('aux_widget_id','{$this->aux_widget_id}');
        grid.data('min_days','{$this->min_days}');
        grid.data('disable_name','{$this->disable_name}');
        grid.data('amount_mode','{$this->amount_mode}');
        grid.data('amount_id','{$this->amount_id}');
        grid.data('transform_type_items'," . CJSON::encode($this->transform_type_items) . ");

        $(document).ready(function() {

           //PRODUCTOS
            var instances = " . CJSON::encode($instances) . ";
            var active_table_id = null;
            var business_unit_combination_id = grid.data('business_unit_combination_id');
            var business_unit_levels = grid.data('business_unit_levels');
            var overwrite_ids = [];
            var addedTables = {};
            if (business_unit_combination_id != null && business_unit_combination_id != undefined){
                overwrite_ids = [business_unit_combination_id];
            }

            var aux_widget_id =  grid.data('aux_widget_id');
            

            $.each(instances, function(index, instance) {
                
                var table_id = SIANProductListGridAddTable('{$this->id}', instance['instance_id'], \"{$this->back_link}\", instance['errors'],aux_widget_id);

                if(!isset(active_table_id))
                {
                    active_table_id = table_id;
                }
                
                var array = instance['items'];
                
                if (array.length > 0)
                {
                    for (var i = 0;i < array.length;i++)
                    {
                        let item_date = array[i]['delivery_date'] == null? null : moment(array[i]['delivery_date'], 'DD/MM/YYYY'); 
                        let description = array[i]['description'] == null? '' : array[i]['description']; 
                        SIANProductListGridAddItem('{$this->id}', table_id, array[i]['product_id'], array[i]['product_name'], description, array[i]['item_type_id'], array[i]['product_type'], array[i]['unit_stock'], array[i]['mixed_stock'], array[i]['pres_quantity'], array[i]['presentationItems'], array[i]['equivalence'], array[i]['transform_type'], array[i]['allow_decimals'], array[i]['min_amount_pen'], array[i]['min_amount_usd'],array[i]['amount'], array[i]['parent_item_id'], array[i]['combination_id'], array[i]['combination_name'],  item_date, array[i]['errors'], business_unit_levels, overwrite_ids,aux_widget_id);
                    }
                }  
                
            });

            if(isset(active_table_id))
            {
                SIANProductListGridShowTable('{$this->id}', active_table_id, 0);
                if({$add_row} && window.sianProductListIds.length < 1){
                    SIANProductListGridAddNewProduct('{$this->id}', active_table_id, [],'', 'Monto')
                }
            }

            $('#{$this->item_filter_type_id}').change(function() {
                var selectedValue = $(this).val();

                if (selectedValue === '1') {
                    $('#{$this->value_filter_type_id}').val(1);
                } else if (selectedValue === '0') {
                    $('#$this->value_filter_type_id').val(0);
                }
            });
            

            $('#{$this->multiplier_id}').on('input', function() {                
                var multiplierValue = $(this).val();
                $.each(instances, function(index, instance) {
                    var instance_id = instance.instance_id;
                    var table_id = instance_id +'_table';
                    
                    $('#' + table_id).find('tr.sian-product-list-grid-item').each(function(index) {
                        var item = $(this);
                        var currentInstanceId = item.closest('table').data('instance_id');
                        
                        if (currentInstanceId === instance_id) {
                            var originalPresQuantityInput = item.find('input.sian-product-list-grid-item-original-pres-quantity');
                            var originalPresQuantity = parseFloat(originalPresQuantityInput.val());
                            var newPresQuantity = originalPresQuantity * parseFloat(multiplierValue);
                            
                            var presQuantityInput = item.find('input.sian-product-list-grid-item-pres-quantity');
                            presQuantityInput.val(newPresQuantity);
                        }
                    });
                    SIANProductListGridUpdateAmounts('{$this->id}', table_id);
                });
            });


            $('#{$this->input_transformation_type_id}').change(function() {                
                var transformType = $(this).val();
                $.each(instances, function(index, instance) {
                    var instance_id = instance.instance_id;
                    var table_id = instance_id +'_table';
                    
                    $('#' + table_id).find('tr.sian-product-list-grid-item').each(function(index) {
                        var item = $(this);
                        var currentInstanceId = item.closest('table').data('instance_id');
                        if (currentInstanceId === instance_id) {
                            var transformTypeItem = item.find('select.sian-product-list-grid-item-transform-type')[0];
                            transformTypeItem.value = transformType;
                        }
                    });
                    
                });
            });

            $('#{$this->amount_id}').data('updateAmount', function(data){
                SIANProductListGridUpdateAllAmounts('{$this->id}');   
            });

            $('#{$this->input_file_id}').change(function() {
                var file = this.files[0];

                if (file) {
                    var fileName = file.name;
                    var fileExtension = fileName.split('.').pop().toLowerCase();
                    if (fileExtension === 'xls' || fileExtension === 'xlsx') {
                        var formData = new FormData();
                        formData.append('file', file);
                    
                        $('#{$this->loader_id}').css({
                            'width': '4rem',
                            'display': 'block'
                        });
                
                        var proccessData = null;
                        var successProccessData = false;

                        var labelOnTemplate = {
                            barcode : 'Código de Barras',
                            product_id : 'ID Producto',
                            pk : 'ID Producto'
                        };

                        var searchAttribute =  $('#{$this->input_search_attribute_id}').val();

                        var templateAttribute = labelOnTemplate[searchAttribute];
                        
                        
                        $.ajax({
                            url: '{$this->url_api_proccess_file}',
                            type: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function(response) {
                                window.active_ajax--;
                                successProccessData = response.success;
                                var result = deleteDuplicateRegisters(response.data[0],templateAttribute);
                                var proccessData = result[0];
                                var errorsDuplicates = '';
                                
                                
                                result[1].forEach(function(err) {
                                    errorsDuplicates = errorsDuplicates + 'El item ' + err.value + ' tiene ' + err.counter + ' duplicados. \\n';
                                });

                                if(errorsDuplicates !== ''){
                                    alert(errorsDuplicates, '\\n Se considera al primer item encontrado');
                                }
                                

                                if (successProccessData && proccessData.length > 0) {
                                    var keywords = [];
                                    for (var i = 0; i < proccessData.length; i++) {
                                        if(searchAttribute == 'pk'){
                                            var equivalencia = proccessData[i]['Equivalencia'] ? proccessData[i]['Equivalencia'] : 1;
                                            equivalencia =  parseFloat(equivalencia).toFixed(2);
                                            keywords.push(proccessData[i]['ID Producto'] + '-' + equivalencia);
                                        }else {
                                            keywords.push(proccessData[i][templateAttribute])
                                        }
                                    }


                                
                                    var value_presentation = $('#{$this->item_filter_presentation_id}').val();
                                    var allowFilter = {$filter_presentation};

                                    var filter_presentation = null;
    
                                    if(allowFilter && value_presentation && value_presentation !== ''){
                                        filter_presentation =  value_presentation
                                    }


                                    var filter_related = null;

                                    filter_related = $('#{$this->value_filter_type_id}').val() === '1' ? 1 : 0;
                                    
                                
                                    $.ajax({
                                        type: 'post',
                                        url: '{$this->controller->createUrl('/widget/getDpModel')}',
                                        data: {
                                            viewClass: 'DpAllProductPresentations',
                                            scenario: 'stock',
                                            attribute: searchAttribute,
                                            attribute_in: keywords,
                                            presentationMode: {$this->presentationMode},
                                            use_related : {$this->use_related},
                                            filter_presentation : filter_presentation,
                                            filter_related : filter_related

                                        },
                                        beforeSend: function(xhr) {
                                            window.active_ajax++;
                                            $('div.ui-tooltip').remove();
                                        },
                                        success: function(odata) {
                                            window.active_ajax--;
                
                                            $('#{$this->loader_id}').css({
                                                'width': '4rem',
                                                'display': 'none'
                                            });
                
                                            if (odata.length > 0) {
                                                var itemsCharged = 0;
                                                for (var i = 0; i < odata.length; i++) {
                                                    var value = odata[i][searchAttribute];
                                                    for (var j = 0; j < proccessData.length; j++) {
                                                        var product = proccessData[j];
                                                        if(product[templateAttribute] === value){
                                                            proccessData[j]['product_id'] = odata[i]['product_id'];
                                                        }
                                                    }
                                                
                                                }

                                                var productMap = {};

                                                for (var i = 0; i < proccessData.length; i++) {
                                                    var product = proccessData[i];
                                                    productMap[product[templateAttribute]] = product;
                                                }
                                        
                                                for (var i = 0; i < odata.length; i++) {
                                                    var attribute = searchAttribute === 'pk' ? 'product_id' : searchAttribute;
                                                    var valueSearch = odata[i][attribute];
                                                    var product = productMap[valueSearch];        
                                                    var id_search = product['product_id'] ?? valueSearch;

                                                    if (product) {
                                                        if (window.sianProductListIds.findIndex(id => id == id_search) === -1) {
                                                            var press_quantity = product['Cantidad'] ? product['Cantidad'] : '1' ;
                                                            press_quantity = odata[i]['allow_decimals'] === '1' ? parseFloat(press_quantity) : Math.round(press_quantity).toFixed();

                                                            var amount = product['Precio Unitario'] ? product['Precio Unitario'] : 0 ;
                                                            amount = parseFloat(amount);

                                                            SIANProductListGridAddItem('{$this->id}', active_table_id, odata[i]['product_id'], odata[i]['product_name'], '', odata[i]['item_type_id'], odata[i]['product_type'], odata[i]['unit_stock'], odata[i]['mixed_stock'],press_quantity, odata[i]['presentationItems'], odata[i]['equivalence'], odata[i]['transform_type'], odata[i]['allow_decimals'], odata[i]['min_amount_pen'], odata[i]['min_amount_usd'], amount, odata[i]['parent_item_id'], odata[i]['combination_id'], odata[i]['combination_name'], null, [], business_unit_levels, overwrite_ids, aux_widget_id);
                                                            itemsCharged++;
                                                        }

                                                        if({$this->enable_aux_widget}){
                                                            SIANProductListGridUpdateIds(['{$this->id}','{$this->aux_widget_id}']);
                                                        }else {
                                                            SIANProductListGridUpdateIds('{$this->id}');
                                                        }
                                                        
                                                    }
                                                }
                                                var divObj = $('#{$this->id}');
                                                var table_id = divObj.data('table_id');
                                                SIANProductListGridUpdateAmounts('{$this->id}', table_id);
                                                var adicionalInfo = searchAttribute === 'barcode' ? '\\nSe consideraron las equivalencias asociadas a la presentación del código de barras.' : '';
                                                alert('Se cargaron ' + itemsCharged + ' de ' + response.data[0].length + '.' + adicionalInfo );
                                            }else {
                                                alert('No se encontró ningún producto.');
                                            }
                                        },
                                        error: function(request, status, error) {
                                            bootbox.alert(us_message(request.responseText, 'error'));
                                            window.active_ajax--;
                                        },
                                        dataType: 'json'
                                    });
                                } else {
                                    alert('El archivo no tiene datos.');
                                }
                            },
                            beforeSend: function(xhr) {
                                window.active_ajax++;
                                $('div.ui-tooltip').remove();
                            },
                            error: function(xhr, status, error) {
                                alert('No se pudo procesar el archivo');
                                $('#{$this->loader_id}').css({
                                    'width': '4rem',
                                    'display': 'none'
                                });
                                window.active_ajax--;
                            }
                        });

                        $('#{$this->input_file_id}').val('');

                    } else {
                        // El archivo no tiene una extensión válida, muestra un mensaje de error o realiza alguna otra acción
                        alert('Por favor selecciona un archivo con extensión .xls o .xlsx');
                    }

                } else {
                    alert('No se seleccionó ningún archivo.');
                }
            });
            

            //UPDATE
            if({$this->enable_aux_widget}){
                SIANProductListGridUpdateIds(['{$this->id}','{$this->aux_widget_id}']);
            }else {
                SIANProductListGridUpdateIds('{$this->id}');
            }
            SIANProductListGridUpdateAllAmounts('{$this->id}');
            unfocusable();
            
            {$this->onready}
        });

        function deleteDuplicateRegisters(data,attribute){
            let map = new Map();
            let errors = new Map();

            data.forEach((item) => {
                if(!map.has(item[attribute])){
                    map.set(item[attribute],item);
                }else {
                    if(!errors.has(item[attribute])){
                        errors.set(item[attribute], {value : item[attribute], counter : 2 });
                    }else {
                        let value = errors.get(item[attribute]);
                        errors.set(item[attribute],{...value, counter: value.counter + 1 });
                        
                    }
                }
            })
            return [Array.from(map.values()),Array.from(errors.values())];
        }
        
        
        //SI CAMBIA OPERACION
        $('#{$this->id}').data('changeOperation', function(changeData){
            var gridObj =  $('#{$this->id}');
            //GUARDAMOS INFO DE OPERACIÓN
            gridObj.data('operationChangeData', changeData);

            SIANProductListGridUpdateAllAmounts('{$this->id}');
        });
        
        $('#{$this->id}').data('changeEmission', function(changeData){
            var divObj = $('#{$this->id}');
            //Seteamos
            divObj.data('emission_date', changeData.emission_date);  
            //Cambiamos a todos los links
            divObj.find('a.sian-product-list-grid-item-mixed-stock').data('emission_date', changeData.emission_date); 
            $('#{$this->item_mixed_stock_label_id}').data('emission_date', changeData.emission_date);            
            //
            //Si usa stock se cargará al cambiar la fecha de emisión
            " . ($this->productList->movement->validate_stock == 1 ? "divObj.data('notifyStockMode')();" : "") . "
            USAutocompleteReset('{$this->autocomplete_id}');
            
        });


        
        " . ($this->show_amount ? "
        //SI CAMBIA LA MONEDA
        $('#{$this->id}').data('changeCurrency', function(currency){

            var divObj = $('#{$this->id}');
            var exchange_rate = divObj.data('exchange_rate');

            divObj.find('table.table').each(function (index) {

                var tableObj = $(this);

                //Recorremos ítems
                tableObj.find('tr.sian-product-list-grid-item').each(function(index) {

                    var rowObj = $(this);

                    var min_amount_pen = rowObj.find('input.sian-product-list-grid-item-min-amount-pen').floatVal({$this->ifixed});
                    var min_amount_usd = rowObj.find('input.sian-product-list-grid-item-min-amount-usd').floatVal({$this->ifixed});

                    //Actualizamos montos
                    switch(currency)
                    {
                        case '" . Currency::PEN . "':
                            rowObj.find('input.sian-product-list-grid-item-amount').toPen(exchange_rate, {$this->ifixed}, {min: min_amount_pen});
                        break;
                        case '" . Currency::USD . "':
                            rowObj.find('input.sian-product-list-grid-item-amount').toUsd(exchange_rate, {$this->ifixed}, {min: min_amount_usd});
                    }
                });
            });

            divObj.data('currency', currency);
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));

            //Limpiamos
            USAutocompleteReset('{$this->autocomplete_id}');
            //Actualizamos
            SIANProductListGridUpdateAllAmounts('{$this->id}');
        });
        
        $('#{$this->id}').data('changeExchange', function(exchange_rate){

            var divObj = $('#{$this->id}');
            var currency = divObj.data('currency');

            divObj.find('table.table').each(function (index) {

                var tableObj = $(this);
                
                tableObj.find('tr.sian-product-list-grid-item').each(function(index) {
                    
                    var rowObj = $(this);

                    var min_amount_pen = rowObj.find('input.sian-product-list-grid-item-min-amount-pen').floatVal({$this->ifixed});
                    var min_amount_usd = rowObj.find('input.sian-product-list-grid-item-min-amount-usd').floatVal({$this->ifixed});

                    //Cambiamos los balances
                    switch('{$this->controller->getOrganization()->globalVar->display_currency}')
                    {
                        case '" . Currency::PEN . "':
                            min_amount_usd = USMath.divide(min_amount_pen, exchange_rate);
                        break;
                        case '" . Currency::USD . "':
                            min_amount_pen = USMath.multiply(min_amount_usd, exchange_rate);
                         break;
                        default:
                            bootbox.alert(us_message('Moneda Inválida', 'error'));
                        break;
                    }

                    //Cambiamos mínimos
                    switch(currency)
                    {
                        case '" . Currency::PEN . "':
                            rowObj.find('input.sian-product-list-grid-item-amount').floatAttr('min', {$this->ifixed}, min_amount_pen);
                        break;
                        case '" . Currency::USD . "':
                            rowObj.find('input.sian-product-list-grid-item-amount').floatAttr('min', {$this->ifixed}, min_amount_usd);
                         break;
                        default:
                            bootbox.alert(us_message('Moneda Inválida', 'error'));
                        break;
                    }

                    //Seteamos balances
                    rowObj.find('input.sian-product-list-grid-item-min-amount-pen').floatVal({$this->ifixed}, min_amount_pen);
                    rowObj.find('input.sian-product-list-grid-item-min-amount-usd').floatVal({$this->ifixed}, min_amount_usd);
                });

            });

            //Actualizamos TC            
            divObj.data('exchange_rate', exchange_rate);
            //Limpiamos
            USAutocompleteReset('{$this->autocomplete_id}');
            //Actualizamos montos
            SIANProductListGridUpdateAllAmounts('{$this->id}');
        });
        " : "
            $('#{$this->id}').data('changeExchange', function(exchange_rate){
            });
        ") . "
            
        $('#{$this->id}').data('changeWarehouse', function(changeData){
            var grid = $('#{$this->id}');
            //Seteamos
            $('#{$this->warehouse_input_id}').val(changeData.warehouse_id);
            grid.data('warehouse_name', changeData.warehouse_name);
            //Cambiamos a todos los links
            grid.find('a.sian-product-list-grid-item-mixed-stock').data('warehouse_id', changeData.warehouse_id); 
            $('#{$this->item_mixed_stock_label_id}').data('warehouse_id', changeData.warehouse_id);            
            //Obtenemos
            var emission_date = grid.data('emission_date');                
            //
            if(changeData.warehouse_id !== 'undefined' && changeData.warehouse_id.length > 0 && emission_date)
            {
                SIANProductListGridUpdateAllAmounts('{$this->id}');
            }
            " . ($this->productList->movement->validate_stock == 1 ? "grid.data('notifyStockMode')();" : "") . "
            //Limpiamos
            USAutocompleteReset('{$this->autocomplete_id}');
        });
        
        $('#{$this->id}').data('changeBusinessUnit', function(changeData){
            var divObj = $('#{$this->id}');
            divObj.data('business_unit_combination_id', changeData.combination_id);
            divObj.data('business_unit_combination_name', changeData.combination_name);
            divObj.data('business_unit_combination_name', changeData.combination_name);
            divObj.data('business_unit_levels', changeData.levels);
            SIANProductListGridUpdateOverwriteIds('{$this->id}');
        });
        
        " . ($this->combinations ? "
        $('body').on('click', 'span.sian-product-list-grid-item-combination-id-span', function(e) {
            var spanObj = $(this);
            var span_id = spanObj.attr('id');
            USCostLevelInit(span_id, 1, 2, 400);
        });
        " : "") . "   

        " . ($this->productList->movement->validate_stock == 1 ? "
        //Notificación stock
        $('#{$this->id}').data('notifyStockMode', function(){
            var grid = $('#{$this->id}');
            var table_id = grid.data('table_id');
            //Obtenemos
            var emission_date = grid.data('emission_date');
            var warehouse_id = $('#{$this->warehouse_input_id}').val();
            var warehouse_name = grid.data('warehouse_name');
            var stock_message = 'Se mostrará el stock {$this->productList->movement->getStockModeLabel()} en el almacén por defecto. Seleccione un almacén para mostrar el stock en ese almacén.';
            if(!isBlank(warehouse_id) && !isBlank(warehouse_name) && !isBlank(emission_date))
            {
                stock_message = 'Se mostrará el stock {$this->productList->movement->getStockModeLabel()} en \'' + warehouse_name + '\'';            
            }
            
            if(!isBlank(emission_date))
            {
                SIANProductListGridLoadStocks('{$this->id}', table_id);         
            }
            
            $('#{$this->autocomplete_id}').notify(stock_message, 
                    {   className: 'info',
                        showDuration: 50,
                        hideDuration: 50,
                        autoHideDelay: 5000
                    }
                );  
        });
        " : "") . "   

        $('#{$this->id}').data('changeCCDependence', function(operationChangeData){
            {$this->onChangeCCDependence};
        });
        
        $('body').on('change', '#{$this->item_equivalence_id}',function(e) {
            var divObj = $('#{$this->id}');
            var form_currency = divObj.data('currency');
            var exchange_rate = divObj.floatData('exchange_rate', 3);                
            var ifixed = divObj.data('ifixed');
            //
            SIANProductListGridSetAmounts(form_currency, exchange_rate, $('#{$this->item_equivalence_id}'), $('#{$this->item_amount_id}'), $('#{$this->item_min_amount_pen_id}'), $('#{$this->item_min_amount_usd_id}'), ifixed);
                
            var product_id = USAutocompleteField('{$this->autocomplete_id}', 'value');
            //Si se cambia
            " . ($this->productList->movement->validate_stock == 1 ? "
            SIANProductListGridLoadStock('{$this->id}', product_id, $('#{$this->item_equivalence_id}'), $('#{$this->item_unit_stock_id}'), null, $('#{$this->item_mixed_stock_label_id}'));
            " : "") . "
       });
       
       $('body').on('change', '#{$this->id} select.sian-product-list-grid-item-equivalence', function(e) {

            var rowObj = $(this).closest('tr');
            var tableObj = $(this).closest('table.table');
            var divObj = $('#{$this->id}');;            
            var form_currency = divObj.data('currency');
            var exchange_rate = divObj.floatData('exchange_rate', 3);  
            var ifixed = divObj.data('ifixed');
            //
            var equivalenceObj = rowObj.find('select.sian-product-list-grid-item-equivalence');
            var amountObj = rowObj.find('input.sian-product-list-grid-item-amount');
            var minAmountPenObj = rowObj.find('input.sian-product-list-grid-item-min-amount-pen');
            var minAmountPenUsd = rowObj.find('input.sian-product-list-grid-item-min-amount-usd');
            SIANProductListGridSetAmounts(form_currency, exchange_rate, equivalenceObj, amountObj, minAmountPenObj, minAmountPenUsd, ifixed);
            //Actualizar montos
            SIANProductListGridUpdateAmounts('{$this->id}', tableObj.attr('id'));

            " . ($this->productList->movement->validate_stock == 1 ? "
            SIANProductListGridGetAndLoadStock('{$this->id}', rowObj.attr('id'));
            " : "") . "

        });

        $('#{$this->id}').data('changeIds', function(ids, pks){
            var divObj = $('#{$this->id}');
            //Seteamos
            //{$this->onChangeIds}
        });

        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var divObj = $('#{$this->id}');
            var table_id = divObj.data('table_id');
            var currency = divObj.data('currency'); 
            var aux_id = $('#{$this->item_aux_id}').val();
            var product_id = $('#{$this->item_value_id}').val();
            var product_name = $('#{$this->item_text_id}').val();
            var item_type_id = $('#{$this->item_item_type_id}').val();
            var product_type = $('#{$this->item_product_type_id}').val();
            var pres_quantity = $('#{$this->item_pres_quantity_id}').double2();
            var presentationItems = $('#{$this->item_equivalence_id}').data('presentationItems');
            var equivalence = $('#{$this->item_equivalence_id}').double2();
            var allow_decimals = $('#{$this->item_allow_decimals_id}').integer();
            var business_unit_combination_id = divObj.data('business_unit_combination_id');
            var business_unit_combination_name = divObj.data('business_unit_combination_name');
            var business_unit_levels = divObj.data('business_unit_levels');
            var overwrite_ids = [];
            if (business_unit_combination_id != null && business_unit_combination_id != undefined){
                overwrite_ids = [business_unit_combination_id];
            }
            var aux_widget_id =  grid.data('aux_widget_id');

            " . ($this->productList->movement->validate_stock == 1 ? "
                if (product_type === PRODUCT_TYPE_MERCHANDISE)
                {
                    var unit_stock = $('#{$this->item_unit_stock_id}').val();
                    var mixed_stock = $('#{$this->item_mixed_stock_label_id}').text();                        
                }
                else
                {
                    var unit_stock = '0'
                    var mixed_stock = '-';   
                }
            " : "
                var unit_stock = 0;
                var mixed_stock = '';                
            ") . "                
            var amount = $('#{$this->item_amount_id}').floatVal({$this->ifixed});
            var exchange_rate = divObj.data('exchange_rate');
            var min_amount_pen = $('#{$this->item_min_amount_pen_id}').floatVal({$this->ifixed});
            var min_amount_usd = $('#{$this->item_min_amount_usd_id}').floatVal({$this->ifixed});
            var min_error_message = false;
                
            " . ($this->show_amount ? "
            //FUNCIONALIDAD CON MONTOS           
            var min_error_message = false;
            
            switch(currency)
            {
                case '" . Currency::PEN . "':
                    if (amount < min_amount_pen)
                    {
                        min_error_message = 'El monto puede ser menor que ' + min_amount_pen;
                    }
                break;
                case '" . Currency::USD . "':
                    if (amount < min_amount_usd)
                    {
                        min_error_message = 'El monto no puede ser menor que ' + min_amount_usd;

                    }
                break;
                default:
                    bootbox.alert(us_message('Moneda Inválida', 'error'));
                break;
            }                           
         
            if (min_error_message)
            {
                bootbox.alert(us_message(min_error_message, 'warning'));
                $('#{$this->item_amount_id}').focus();
                return;
            } " : "") . "

            if (product_id.length === 0)
            {
                $('#{$this->item_aux_id}').focus();
                return;
            } 
            
            if(isNaN(equivalence)){
                $('#{$this->item_equivalence_id}').notify('Debe elegir una presentación!', 
                    {   className: 'warn',
                        showDuration: 50,
                        hideDuration: 50,
                        autoHideDelay: 5000
                    }
                );
                $('#{$this->item_equivalence_id}').focus().select();
                return;
            }
            
            var date = moment();
            var minDate = date.add({$this->min_days}, 'day');
            SIANProductListGridAddItem('{$this->id}', table_id, product_id, product_name, '', item_type_id, product_type, unit_stock, mixed_stock, pres_quantity, presentationItems, equivalence,'', allow_decimals, min_amount_pen, min_amount_usd, amount, '', '', '', minDate, [], business_unit_levels, overwrite_ids,aux_widget_id);
            USAutocompleteReset('{$this->autocomplete_id}');
                
            //FOCUS
            $('#{$this->item_aux_id}').focus();
            if({$this->enable_aux_widget}){
                SIANProductListGridUpdateIds(['{$this->id}','{$this->aux_widget_id}']);
            }else {
                SIANProductListGridUpdateIds('{$this->id}');
            }

            SIANProductListGridUpdateAmounts('{$this->id}', table_id);
            unfocusable();
        });", CClientScript::POS_END);

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        function {$this->id}Clear()
        {
            $('#{$this->item_item_type_id}').val('');
            $('#{$this->item_product_type_id}').val('');
            $('#{$this->item_pres_quantity_id}').val(1);
            $('#{$this->item_pres_quantity_id}').removeAttr('disabled');
            $('#{$this->item_equivalence_id}').html('<option value>" . Strings::SELECT_OPTION . "</option>');                
            $('#{$this->item_allow_decimals_id}').val(0);
            $('#{$this->item_unit_stock_id}').val(null);
            $('#{$this->item_mixed_stock_label_id}').text(0);    
            USLinkStatus('{$this->item_mixed_stock_label_id}', false, {id: null, equivalence: null});     
            $('#{$this->item_amount_id}').val(0.00);            
            $('#{$this->item_amount_id}').attr('min', 0.00);            
            $('#{$this->item_min_amount_pen_id}').val(0.00);
            $('#{$this->item_min_amount_usd_id}').val(0.00);
        }
        
        function {$this->id}GetPresentations(product_id, equivalence)
        {
            if(product_id != undefined)
            {
                $.ajax({
                    type: 'post',
                    url: '{$this->presentationUrl}',
                    data: {
                        mode: {$this->presentationMode},
                        product_ids: [product_id],
                        currency: '" . $this->controller->getOrganization()->globalVar->display_currency . "'
                    },
                    beforeSend: function (xhr) {
                        window.active_ajax++;
                        //Ocultamos los tooltip
                        $('div.ui-tooltip').remove();
                    },                    
                    success: function(data) {
                        $.each(data, function(index, item) {
                            SIANProductListGridFillPresentations('{$this->item_equivalence_id}', item, equivalence);
                        });       
                        
                        " . ($this->show_amount ? "
                        var divObj = $('#{$this->id}');
                        var form_currency = divObj.data('currency');
                        var exchange_rate = divObj.floatData('exchange_rate', 3);
                        //SIANProductListGridSetAmounts(form_currency, exchange_rate, $('#{$this->item_equivalence_id}'), $('#{$this->item_amount_id}'), $('#{$this->item_min_amount_pen_id}'), $('#{$this->item_min_amount_usd_id}'), {$this->ifixed});
                        " : "") . "
                            
                        window.active_ajax--;
                    },
                    error: function(request, status, error) { // if error occured
                        window.active_ajax--;
                        bootbox.alert(us_message(request.responseText, 'error'));
                    },
                    dataType: 'json'
                });
            }
        }

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        $filter_presentation = $this->allow_filter_presentation === true ? 'true' : 'false';

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->title,
            'headerIcon' => 'list',
            'context' => $this->context,
            'htmlOptions' => array(
                'id' => $this->id,
                'style' => $this->hidden ? 'display: none;' : '',
            )
        ));

        if (!$this->readonly) {

            $a_attributes = [];
            $a_attributes[] = array('name' => 'product_id', 'width' => 10, 'types' => array('id', 'value', 'aux'), 'not_in' => "window.sianProductListIds");
            $a_attributes[] = array('name' => 'equivalence', 'hidden' => true, 'search' => false);
            $a_attributes[] = array('name' => 'barcode', 'width' => 20);
            $a_attributes[] = array('name' => 'product_name', 'width' => (35 - ($this->productList->movement->validate_stock ? 10 : 0) - ($this->show_amount ? 10 : 0)), 'types' => array('text'));
            $a_attributes[] = array('name' => 'item_type_id', 'hidden' => true, 'update' => "$('#{$this->item_item_type_id}').val(item_type_id)");
            $a_attributes[] = array('name' => 'product_type', 'hidden' => true, 'update' => "$('#{$this->item_product_type_id}').val(product_type)");
            if ($this->productList->movement->route == 'warehouse/transforOrderRelated') {
                $a_attributes[] = array('name' => 'recipe_type', 'hidden' => true, 'in' => "['" . Recipe::TYPE_SUPPLY . "']");
            }
            //Usa stock?
//            if ($this->productList->movement->validate_stock == 1) {
            $a_attributes[] = array('name' => 'unit_stock', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->item_unit_stock_id}').val(unit_stock);");
            if ($this->item_name == 'Item' && $this->show_stock) {
                $a_attributes[] = array('name' => 'stock', 'width' => 10, 'search' => false);
            }
            $a_attributes[] = array('name' => 'mixed_stock', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->item_mixed_stock_label_id}').text(mixed_stock); USLinkStatus('{$this->item_mixed_stock_label_id}', true, {id: product_id, equivalence: equivalence})");
            //            }

            if ($this->show_amount) {
                $a_attributes[] = array('name' => 'iaprice', 'width' => 10, 'search' => false);
            }

            $a_attributes[] = array('name' => 'allow_decimals', 'hidden' => true, 'update' => "
                                $('#{$this->item_allow_decimals_id}').val(allow_decimals);
                                $('#{$this->item_pres_quantity_id}').allowDecimals(allow_decimals == 1 ? 2 : 0);
                            ", 'search' => false);
            $a_attributes[] = array('name' => 'mark_name', 'width' => 15, 'search' => false);
            $a_attributes[] = array('name' => 'measure_name', 'width' => 10, 'search' => false);

            echo "<div class = 'row'>";
            if ($this->allow_filter_type) {
                echo "<div class = col-lg-2 col-md-2 col-sm-2 col-xs-12 >";
                echo CHtml::hiddenField($this->value_filter_type_id, null, array('id' => $this->value_filter_type_id));
                echo SIANForm::dropDownListNonActive('Tipo de Producto', 'product_type', '', Product::getRelatedTypes(), array("id" => $this->item_filter_type_id, 'class' => 'form-control'));
                echo "</div>";
            }

            if ($this->allow_filter_presentation) {
                echo "<div class = col-lg-2 col-md-2 col-sm-2 col-xs-12 >";
                echo CHtml::hiddenField($this->value_filter_presentation_id, Multitable::findMultiIdForMesure(Presentation::PRESENTATION_VALUE_CAJA), array('id' => $this->value_filter_presentation_id));
                echo SIANForm::dropDownListNonActive('Tipo de Producto', 'product_presentation', Multitable::findMultiIdForMesure(Presentation::PRESENTATION_VALUE_CAJA), Multitable::getProductTypes(), array("id" => $this->item_filter_presentation_id, 'class' => 'form-control'));
                echo "</div>";
            }
            $scenario = $this->item_name == 'Item' ? DpAllMerchandisePresentations::SCENARIO_WITH_STOCK : DpAllMerchandisePresentations::SCENARIO_WITHOUT_STOCK;
            if ($this->productList->movement->route == 'warehouse/transforOrderRelated') {
                $scenario = DpAllMerchandisePresentations::SCENARIO_WITHOUT_STOCK;
            }
            echo "<div class = '" . ($this->productList->movement->validate_stock == 1 || $this->allow_filter_type || $this->allow_filter_presentation ? "col-lg-5 col-md-5 col-sm-6 col-xs-12" : "col-lg-6 col-md-6 col-sm-6 col-xs-12") . "'>";
            echo $this->widget('application.widgets.USAutocomplete', array(
                'id' => $this->autocomplete_id,
                'label' => 'Productos',
                'name' => null,
                'aux_id' => $this->item_aux_id,
                'value_id' => $this->item_value_id,
                'text_id' => $this->item_text_id,
                'hint' => ($this->productList->movement->validate_stock == 1 ? "El stock que se muestra es el stock <b>{$this->productList->movement->getStockModeLabel()}</b>." : null),
                'view' => array(
                    'model' => $this->allow_service ? 'DpAllProductPresentations' : 'DpAllMerchandisePresentations',
                    'scenario' => $scenario,
                    'attributes' => $a_attributes,
                    //Stock a la fecha
                    'params' => "{
                            filter_related : function () {
                                return $('#{$this->value_filter_type_id}').val() === '1' ? 1 : 0;
                            },

                            filter_presentation : function () {
                                var value_presentation = $('#{$this->item_filter_presentation_id}').val();
                                var allowFilter = {$filter_presentation};

                                if(allowFilter && value_presentation && value_presentation !== ''){
                                    return value_presentation
                                }
                                
                                return null;
                            },
                            
                            "
                    .
                    ($this->filter_by_warehouse == 1 && $this->productList->movement->validate_stock != 1 ?
                            "
                            warehouse_id: function () {
                                return $('#{$this->warehouse_input_id}').val();
                            
                            }," : "") .
                    " 
                            use_related: " . ($this->use_related == 1 ? 1 : 0) . ", 
                            only_allowed_divisions: " . ($this->only_allowed_divisions == 1 ? 1 : 0) . " 
                            " . ($this->productList->movement->validate_stock == 1 ? ",
                            date: function () { 
                                return $('#{$this->id}').data('emission_date'); 
                            },
                            warehouse_id: function () {
                                return $('#{$this->warehouse_input_id}').val();
                            },
                            exclude_id: function(){
                                return " . CJSON::encode($this->productList->movement_id) . "//Para edición
                            },
                            stock_mode: {$this->productList->movement->stock_mode},
                            " : "") . "
                        }",
                ),
                'maintenance' => array(
                    'module' => 'logistic',
                    'controller' => 'product',
                    'buttons' => array(
                        'preview' => array(
                            'access' => $this->preview_access,
                        ),
                        'create' => [],
                        'update' => [],
                    ),
                ),
                'onselect' => "
                    {$this->id}GetPresentations(product_id, equivalence);
                ",
                'onreset' => "{$this->id}Clear();
                    "
                    ), true);

            echo ($this->use_related == 1 ? 'Únicamente se Mostrarán productos que tienen productos relacionados*' : '');
            echo "</div>";

            //Si se usa stock se renderiza el campo
            if ($this->productList->movement->validate_stock == 1 && $this->item_name == 'Item') {
                echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
                echo CHtml::hiddenField(null, 0, array(
                    'id' => $this->item_unit_stock_id,
                    'disabled' => true,
                ));
                echo CHtml::label('Stock', $this->item_mixed_stock_label_id);
                echo "<p>" . $this->widget('application.widgets.USLink', array(
                    'id' => $this->item_mixed_stock_label_id,
                    'route' => '/logistic/merchandise/explainStock',
                    'label' => '0',
                    'title' => '¿Por qué veo este stock?',
                    'class' => 'form',
                    'data' => array(
                        'stock_mode' => $this->productList->movement->stock_mode,
                        'emission_date' => $this->productList->movement->emission_date,
                        'warehouse_id' => $this->productList->warehouse_id,
                        'exclude_id' => $this->productList->movement->kardex_unlock_exclude_id,
                    ),
                    'visible' => false
                        ), true) . "</p>";
                echo "</div>";
            }

            echo $this->show_amount || $this->allow_filter_type || $this->allow_filter_presentation ? "<div class = 'col-lg-1 col-md-1 col-sm-6 col-xs-12'>" : "<div class = 'col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
            echo CHtml::hiddenField(null, '', array(
                'id' => $this->item_item_type_id,
            ));
            echo CHtml::hiddenField(null, '', array(
                'id' => $this->item_product_type_id,
            ));

            echo SIANForm::numberFieldNonActive('Cantidad', null, 1, array(
                'id' => $this->item_pres_quantity_id,
                'class' => 'us-double0 enterTab',
                'min' => 1
            ));
            echo "</div>";

            echo $this->show_amount ? "<div class = 'col-lg-1 col-md-1 col-sm-6 col-xs-12'>" : "<div class = 'col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
            echo SIANForm::dropDownListNonActive('Pres.', null, null, [], array(
                'id' => $this->item_equivalence_id,
                'empty' => Strings::SELECT_OPTION,
            ));

            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->item_allow_decimals_id,
            ));
            echo "</div>";

            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->item_min_amount_pen_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->item_min_amount_usd_id,
            ));
            if ($this->show_amount) {
                echo "<div class='col-lg-2 col-md-2 col-sm-1 col-xs-12'>";
                echo SIANForm::numberFieldNonActive("Precio <span class='currency-symbol'></span>", null, 0, array(
                    'id' => $this->item_amount_id,
                    'class' => "us-double4 enterTab",
                    'step' => '0.0001',
                    'min' => 0.00,
                    'style' => 'text-align:right',
                ));
                echo "</div>";
            } else {
                echo CHtml::hiddenField(null, '', array(
                    'id' => $this->item_amount_id,
                    'value' => 0,
                ));
            }
            echo "<div class = 'col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
            echo "<center>";
            echo CHtml::label('Agregar', $this->add_button_id, []);
            echo "<br/>";
            $this->widget('application.widgets.USButton', array(
                'id' => $this->add_button_id,
                'block' => true,
                'context' => 'primary',
                'icon' => 'fa fa-lg fa-plus white',
                'size' => 'default',
                'title' => 'Añadir'
            ));
            echo "</center>";
            echo "</div>";
            echo "</div>";

            echo "<hr>";
        }

        echo "<div class='sian-product-list-grid-tables'></div>";
        if (!$this->amount_mode) {
            echo "<br>";
        }
        echo $this->form->hiddenField($this->productList, 'warehouse_id', [
            'id' => $this->warehouse_input_id,
            'class' => 'sian-product-list-grid-warehouse-id'
        ]);

        if (!$this->readonly && $this->show_load_products) {
            echo "<style>";
            echo "@keyframes spin { to { transform: rotate(360deg); } }";
            echo ".spin { transform-origin: center; animation: spin 2s linear infinite}";
            echo "</style>";
            echo "<div class = 'col-lg-2 col-md-5 col-sm-12 col-xs-12' style ='display: flex; gap:1rem;'>";
            echo SIANForm::dropDownListNonActive('Criterio de Búsqueda', '', 'barcode', Product::getUploadSearchAttribute(), array(
                "id" => $this->input_search_attribute_id,
                'class' => 'form-control',
            ));
            echo "<div style ='display: flex; flex-direction: column;'>";
            echo CHtml::label('Subir', '', []);
            echo "<label class='btn btn-success'>";
            echo "<input type='file' id='{$this->input_file_id}'  accept='.xls, .xlsx' style='display : none' />";
            echo "<i class='fa fa-file-excel-o' aria-hidden='true'></i>";
            echo "</label>";
            echo "</div>";
            echo "<svg viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg' style='width: 4rem; display:none;' id='{$this->loader_id}'>";
            echo "<circle class='spin' cx='200' cy='200' fill='none' r='104' stroke-width='16' stroke='#E387FF' stroke-dasharray='417.5 700' stroke-linecap='round' />";
            echo "</svg>";
            echo "</div>";
        }

        echo "<div class = 'row'>";
        $col = 8;
        if ($this->delivery_date_by_item) {
            echo "<div class = 'col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
            $this->widget('application.widgets.USDatePicker', array(
                'form' => $this->form,
                'model' => $this->productList,
                'attribute' => 'delivery_date',
                'value' => SIANTime::addDays(SIANTime::formatDate(), $this->min_days, true),
                'options' => array(
                    'format',
                    'minDate' => SIANTime::addDays(SIANTime::formatDate(), $this->min_days, true),
                    'append' => '<span class="fa fa-lg fa-calendar black"></span>',
                    'hint' => 'Actualización masiva',
                    'onChangeDateTime' => "function(dp, input){
                        $('#{$this->id}').find('input.sian-product-list-grid-item-delivery-date').val($(input).val());
                    }"
                ),
                'htmlOptions' => array(
                    'style' => 'text-align:left',
                    'class' => 'us-date',
                    'append' => '<span class="fa fa-lg fa-calendar black"></span>',
                    'hint' => 'Actualización masiva',
                )
            ));
            echo "</div>";
            $col = $col - 2;
        }

        if ($this->from_template && $this->item_name === 'Item') {
            echo "<div class = 'col-lg-1 col-md-1 col-sm-6 col-xs-12'>";
            echo SIANForm::numberIntegerFieldNonActive('Multiplicador', 'multiplier', '1', array("id" => $this->multiplier_id, 'class' => 'form-control'));
            echo "</div>";
            $col = $col - 1;
        }

        if ($this->massive_transformation_type) {
            echo "<div class = 'col-lg-2 col-md-5 col-sm-12 col-xs-12' style ='display: flex; gap: 1rem;'>";
            echo SIANForm::dropDownListNonActive('Tipo de Transformación', '', '', $this->transform_type_items, array(
                "id" => $this->input_transformation_type_id,
                'class' => 'form-control',
                'hint' => 'Actualización masiva',)
            );
            echo "</div>";
            $col = $col - 2;
        }

        if ($this->item_name === 'Item' && !$this->readonly && $this->show_allow_duplicate) {
            echo "<div class = 'col-lg-1 col-md-5 col-sm-12 col-xs-12'>";
            echo $this->widget('application.widgets.USCheckBox', array(
                'id' => $this->allow_duplicate_checkbox_id,
                'model' => $this->productList,
                'attribute' => 'allow_duplicate',
                //'hint' => 'Para que un producto esté más de una vez en la lista',
                'htmlOptions' => array(
                    'disabled' => false,
                    'onchange' => "      
                        var allow_duplicate = $(this).prop('checked') ? 1 : 0;
                        $('#{$this->id}').data('allow_duplicate', allow_duplicate);
                        //SIANCommercialGridGetIds('{$this->id}');
                    ",
                )
                    ), true);
            echo "</div>";
            $col = $col - 1;
        }

        if ($this->show_amount) {
            echo "<div id='{$this->summary_id}' class='col-lg-{$col} col-md-8 col-sm-3 col-xs-12'>";
            echo "<div class='well pull-right extended-summary ' style='padding: 0px !important;'>";
            echo "<table>";
            echo "<tr><td><b>Total:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-product-list-grid-total'></span></td></tr>";
            echo "</table>";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";

        echo "<br><div class = 'row'>";
        if ($this->show_observations) {
            echo "<div class='col-lg-10 col-md-10 col-sm-10 col-xs-12'>";
            $this->_renderObservations();
            echo "</div>";
        }
        echo "</div>";
        $this->endWidget();
    }

    private function _renderObservations() {
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->textAreaRow($this->productList->movement, 'observation', array(
            'id' => $this->observation_input_id,
            'rows' => 1,
            'maxlength' => 500,
            'required' => $this->productList->movement->isObservationRequired()
        ));
        echo "</div>";
        echo "</div>";
    }

}
