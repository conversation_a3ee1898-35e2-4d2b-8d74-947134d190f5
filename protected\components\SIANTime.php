<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANTime
 *
 * <AUTHOR>
 */
class SIANTime {

    public $datetime_php_format = null;
    public $date_php_format = null;
    public $time_php_format = null;
    public $datetime_sql_format = null;
    public $date_sql_format = null;
    public $date_js_format = null;

    const MONTH_LABELS = [
        1 => 'Enero',
        2 =>  'Febrero',
        3 => 'Marzo',
        4 => 'Abril',
        5 => 'Mayo',
        6 => 'Junio',
        7 => 'Julio',
        8 => 'Agosto',
        9 => 'Septiembre',
        10 => 'Octubre', 
        11 => 'Noviembre',
        12 => 'Diciembre',
    ];

    public function init() {
        $this->datetime_php_format = Yii::app()->params['datetime_php_format'];
        $this->date_php_format = Yii::app()->params['date_php_format'];
        $this->time_php_format = Yii::app()->params['time_php_format'];
        $this->datetime_sql_format = Yii::app()->params['datetime_sql_format'];
        $this->date_sql_format = Yii::app()->params['date_sql_format'];
        $this->date_js_format = Yii::app()->params['date_js_format'];
    }

    /**
     * Formatea una fecha y hora
     * @param string $p_s_datetime Fechay hora
     * @param boolean $p_b_reverse TRUE si está en formateada o FALSE si no lo está
     * @param boolean $p_b_continue Si es TRUE continuará en caso haya un error y arrojará FALSE
     * @return string Fecha y hora formateada
     */
    public static function formatDateTime($p_s_datetime = null, $p_b_reverse = false, $p_b_continue = false) {

        if (!isset($p_s_datetime)) {
            $p_s_datetime = self::now();
        }

        if ($p_b_reverse) {
            return USTime::format($p_s_datetime, Yii::app()->params['datetime_php_format'], Yii::app()->params['datetime_sql_format'], $p_b_continue);
        } else {
            return USTime::format($p_s_datetime, Yii::app()->params['datetime_sql_format'], Yii::app()->params['datetime_php_format'], $p_b_continue);
        }
    }

    /**
     * Intenta formatear una fecha y hora
     * @param string $p_s_datetime Fecha y hora
     * @param boolean $p_b_reverse TRUE si está en formateada o FALSE si no lo está
     * @param string $p_b_wildcard Valor que se mostrará si hubo errores en la conversión
     * @return string Fecha y hora formateada
     */
    public static function tryFormatDateTime($p_s_datetime, $p_b_reverse = false, $p_b_wildcard = null) {

        $s_result = false;
        if ($p_b_reverse) {
            $s_result = USTime::format($p_s_datetime, Yii::app()->params['datetime_php_format'], Yii::app()->params['datetime_sql_format'], true);
        } else {
            $s_result = USTime::format($p_s_datetime, Yii::app()->params['datetime_sql_format'], Yii::app()->params['datetime_php_format'], true);
        }

        if ($s_result === false) {
            return isset($p_b_wildcard) ? $p_b_wildcard : $p_s_datetime;
        } else {
            return $s_result;
        }
    }

    /**
     * Formatea una fecha
     * @param string $p_s_date Fecha
     * @param boolean $p_b_reverse TRUE si está en formateada o FALSE si no lo está
     * @param boolean $p_b_continue Si es TRUE continuará en caso haya un error y arrojará FALSE
     * @return string Fecha formateada
     */
    public static function formatDate($p_s_date = null, $p_b_reverse = false, $p_b_continue = false) {

        if (!isset($p_s_date)) {
            $p_s_date = self::today();
        }

        if ($p_b_reverse) {
            return USTime::format($p_s_date, Yii::app()->params['date_php_format'], Yii::app()->params['date_sql_format'], $p_b_continue);
        } else {
            return USTime::format($p_s_date, Yii::app()->params['date_sql_format'], Yii::app()->params['date_php_format'], $p_b_continue);
        }
    }

    /**
     * Intenta formatear una fecha
     * @param string $p_s_date Fecha
     * @param boolean $p_b_reverse TRUE si está en formateada o FALSE si no lo está
     * @param string $p_b_wildcard Valor que se mostrará si hubo errores en la conversión
     * @return string Fecha formateada
     */
    public static function tryFormatDate($p_s_date, $p_b_reverse = false, $p_b_wildcard = null) {

        $s_result = false;
        if ($p_b_reverse) {
            $s_result = USTime::format($p_s_date, Yii::app()->params['date_php_format'], Yii::app()->params['date_sql_format'], true);
        } else {
            $s_result = USTime::format($p_s_date, Yii::app()->params['date_sql_format'], Yii::app()->params['date_php_format'], true);
        }

        if ($s_result === false) {
            return isset($p_b_wildcard) ? $p_b_wildcard : $p_s_date;
        } else {
            return $s_result;
        }
    }

    public static function today() {
        return USTime::now(Yii::app()->params['date_sql_format']);
    }

    public static function now() {
        return USTime::now(Yii::app()->params['datetime_sql_format']);
    }

    public static function todayPHP() {
        return USTime::now(Yii::app()->params['date_php_format']);
    }

    public static function todayWithCustomformat($p_s_format) {
        return USTime::now($p_s_format);
    }

    public static function nowPHP() {
        return USTime::now(Yii::app()->params['date_php_format']);
    }

    public static function addSeconds($p_s_datetime, $p_i_seconds, $p_b_reverse = false) {
        if ($p_b_reverse) {
            $s_format = Yii::app()->params['datetime_php_format'];
        } else {
            $s_format = Yii::app()->params['datetime_sql_format'];
        }

        return USTime::add($p_s_datetime, $s_format, 'seconds', $p_i_seconds);
    }

    public static function addMinutes($p_s_datetime, $p_i_minutes, $p_b_reverse = false) {
        if ($p_b_reverse) {
            $s_format = Yii::app()->params['datetime_php_format'];
        } else {
            $s_format = Yii::app()->params['datetime_sql_format'];
        }

        return USTime::add($p_s_datetime, $s_format, 'minutes', $p_i_minutes);
    }

    public static function addDays($p_s_date, $p_i_days, $p_b_reverse = false) {
        if ($p_b_reverse) {
            $s_format = Yii::app()->params['date_php_format'];
        } else {
            $s_format = Yii::app()->params['date_sql_format'];
        }

        return USTime::add($p_s_date, $s_format, 'days', $p_i_days);
    }

    public static function addMonths($p_s_date, $p_i_months, $p_b_reverse = false) {
        if ($p_b_reverse) {
            $s_format = Yii::app()->params['date_php_format'];
        } else {
            $s_format = Yii::app()->params['date_sql_format'];
        }

        return USTime::add($p_s_date, $s_format, 'months', $p_i_months);
    }

    public static function isDateTimeBetween($p_s_datetime, $p_s_begin_datetime, $p_s_end_datetime, $p_b_reverse = false) {

        if ($p_b_reverse) {
            $s_format = Yii::app()->params['datetime_php_format'];
        } else {
            $s_format = Yii::app()->params['datetime_sql_format'];
        }

        return USTime::isDateBetween($p_s_datetime, $p_s_begin_datetime, $p_s_end_datetime, $s_format);
    }

    public static function isDateBetween($p_s_date, $p_s_begin_date, $p_s_end_date, $p_b_reverse = false) {

        if ($p_b_reverse) {
            $s_format = Yii::app()->params['date_php_format'];
        } else {
            $s_format = Yii::app()->params['date_sql_format'];
        }

        return USTime::isDateBetween($p_s_date, $p_s_begin_date, $p_s_end_date, $s_format);
    }

    public static function displayDate($date) {
        try {
            return self::formatDate($date);
        } catch (Exception $ex) {
            return $date;
        }
    }

    public static function getDate($p_i_year, $p_s_month, $p_s_day, $p_b_reverse = false) {
        $s_month = str_pad($p_s_month, 2, '0', STR_PAD_LEFT);
        $s_day = str_pad($p_s_day, 2, '0', STR_PAD_LEFT);

        if ($p_b_reverse) {
            return "{$s_day}/{$s_month}/{$p_i_year}";
        } else {
            return "{$p_i_year}-{$s_month}-{$s_day}";
        }
    }

    public static function getYear($p_s_date = null, $reverse = false) {

        if (!isset($p_s_date)) {
            $p_s_date = self::today();
        }

        if ($reverse) {
            $s_format = Yii::app()->params['date_php_format'];
        } else {
            $s_format = Yii::app()->params['date_sql_format'];
        }

        return USTime::get($p_s_date, $s_format, 'Y');
    }

    public static function getMonth($p_s_date = null, $reverse = false) {

        if (!isset($p_s_date)) {
            $p_s_date = self::today();
        }

        if ($reverse) {
            $s_format = Yii::app()->params['date_php_format'];
        } else {
            $s_format = Yii::app()->params['date_sql_format'];
        }

        return USTime::get($p_s_date, $s_format, 'm');
    }

    public static function getDay($p_s_date = null, $reverse = false) {

        if (!isset($p_s_date)) {
            $p_s_date = self::today();
        }

        if ($reverse) {
            $s_format = Yii::app()->params['date_php_format'];
        } else {
            $s_format = Yii::app()->params['date_sql_format'];
        }

        return USTime::get($p_s_date, $s_format, 'd');
    }

    /**
     * Devuelve el formato de fecha a usar
     * @param integer $p_i_search Tipo
     * @param string $p_s_date Fecha
     * @param string $p_s_begin_date Fecha de inicio
     * @param string $p_s_end_date Fecha de fin
     * @param integer $p_i_month Mes
     * @param integer $p_i_y Año
     * @return string
     */
    public static function getDateSearch($p_i_search, $p_s_date, $p_s_begin_date, $p_s_end_date, $p_i_month, $p_i_y) {
        $date = '';
        if ($p_i_search == '0') {
            $date = $p_s_date;
        } elseif ($p_i_search == '1') {
            $date = $p_s_begin_date . " - " . $p_s_end_date;
        } elseif ($p_i_search == '2') {
            $date = $p_i_month . ' - ' . $p_i_y;
        }
        return $date;
    }

    public static function getPeriodSQLCase($field) {
        return "CASE {$field} 
                WHEN 0 THEN 'Apertura' 
                WHEN 1 THEN 'Enero' 
                WHEN 2 THEN 'Febrero' 
                WHEN 3 THEN 'Marzo' 
                WHEN 4 THEN 'Abril' 
                WHEN 5 THEN 'Mayo' 
                WHEN 6 THEN 'Junio' 
                WHEN 7 THEN 'Julio' 
                WHEN 8 THEN 'Agosto' 
                WHEN 9 THEN 'Septiembre' 
                WHEN 10 THEN 'Octubre' 
                WHEN 11 THEN 'Noviembre' 
                WHEN 12 THEN 'Diciembre' 
                WHEN 13 THEN 'Clausura' 
            END";
    }

    /**
     * Obtiene la diferencia entre dos fecha/hora
     * @param string $datetime1 Fecha/hora minuendo
     * @param string $datetime2 fecha/hora sustraendo
     * @param bool $reverse Si es están en formato de muestra o no
     * @return integer Timespan entre las dos fecha/hora
     */
    public static function substract($datetime1, $datetime2, $reverse = false) {

        if ($reverse) {
            $s_format1 = Yii::app()->params['datetime_php_format'];
            $s_format2 = Yii::app()->params['datetime_php_format'];
        } else {
            $s_format1 = Yii::app()->params['datetime_sql_format'];
            $s_format2 = Yii::app()->params['datetime_sql_format'];
        }

        return USTime::substract($datetime1, $datetime2, $s_format1, $s_format2);
    }

    /**
     * Convierte un datetime a date y lo resta con un date
     * @param string $p_s_datetime Datetime que se convertira en time
     * @param string $p_s_date Date a restar
     * @param bool $p_b_reverse Estado que indica si está en formato ionvertido
     * @return integer Diferencia entre las dos fechas
     */
    public static function substractDateToDateTime($p_s_datetime, $p_s_date, $p_b_reverse = false) {

        if ($p_b_reverse) {
            $s_format1 = Yii::app()->params['datetime_php_format'];
            $s_format2 = Yii::app()->params['date_php_format'];
        } else {
            $s_format1 = Yii::app()->params['datetime_sql_format'];
            $s_format2 = Yii::app()->params['date_sql_format'];
        }

        return USTime::substract($p_s_datetime, $p_s_date, $s_format1, $s_format2);
    }

    public static function substractDates($p_s_date1, $p_s_date2, $p_b_reverse = false) {


        if ($p_b_reverse) {
            $s_format1 = Yii::app()->params['date_php_format'];
            $s_format2 = Yii::app()->params['date_php_format'];
        } else {
            $s_format1 = Yii::app()->params['date_sql_format'];
            $s_format2 = Yii::app()->params['date_sql_format'];
        }

        return USTime::substract($p_s_date1, $p_s_date2, $s_format1, $s_format2);
    }

    public static function getYearsUntilToday($p_s_date, $p_b_reverse = false) {

        if ($p_b_reverse) {
            $s_format = Yii::app()->params['date_php_format'];
        } else {
            $s_format = Yii::app()->params['date_sql_format'];
        }

        return USTime::getYearsUntilNow($p_s_date, $s_format);
    }

    public static function getDaysBetween($p_s_datetime1, $p_s_datetime2, $p_b_reverse = false) {

        if ($p_b_reverse) {
            $s_format1 = Yii::app()->params['datetime_php_format'];
            $s_format2 = Yii::app()->params['datetime_php_format'];
        } else {
            $s_format1 = Yii::app()->params['datetime_sql_format'];
            $s_format2 = Yii::app()->params['datetime_sql_format'];
        }

        return USTime::getDaysBetween($p_s_datetime1, $p_s_datetime2, $s_format1, $s_format2);
    }

    public static function getLastDateOfMonth($p_i_year, $p_i_month, $p_b_reverse = false) {

        if ($p_b_reverse) {
            $s_format = Yii::app()->params['date_php_format'];
        } else {
            $s_format = Yii::app()->params['date_sql_format'];
        }

        return USTime::getLastDateOfMonth($p_i_year, $p_i_month, $s_format);
    }

    /**
     * Retorna una etiqueta de los minutos que han pasado desde una fecha hasta este momento
     * @param string $p_s_datetime Fecha
     * @param bool $p_b_reverse Si esta en formato visual
     * @return string Etiqueta de minutos desde una fecha hasta este momento    
     */
    public static function getAgoLabel($p_s_datetime, $p_b_reverse = false, $p_b_ucfirst = true) {

        if ($p_b_reverse) {
            $s_format = Yii::app()->params['datetime_php_format'];
        } else {
            $s_format = Yii::app()->params['datetime_sql_format'];
        }

        return USTime::getAgoLabel($p_s_datetime, $s_format, $p_b_ucfirst);
    }

    public static function isFirstNDays($p_s_date) {
        // Cambiamos el formato de entrada a 'd/m/Y'
        $p_s_date = DateTime::createFromFormat('d/m/Y', $p_s_date);
        $firstDay = new DateTime($p_s_date->format('Y-m-01'));
        $nDays = 0;

        while ($firstDay <= $p_s_date) {
            $dayOfWeek = $firstDay->format('N'); // 1 (Lunes) a 7 (Domingo)
            if ($dayOfWeek < 6) { // Si es lunes a viernes
                $nDays++;
            }
            // Si alcanzamos 5 días hábiles, salimos del bucle
            if ($nDays == 5) {
                break;
            }
            $firstDay->modify('+1 day'); // Avanza al siguiente día
        }
        // Si la fecha está dentro de los primeros 5 días hábiles
        return $nDays >= 1 && $firstDay > $p_s_date;
    }

    public static function getFifthBusinessDay($year, $month) {
        $businessDays = 0;
        $fifthDay = null;

        // Iteramos sobre los días del mes
        for ($day = 1; $day <= 31; $day++) {
            // Verificamos si la fecha es válida (para evitar fechas como 31 de febrero)
            if (!checkdate($month, $day, $year)) {
                break;
            }
            // Obtenemos el número del día de la semana: 1 (lunes) a 7 (domingo)
            $dayOfWeek = date('N', strtotime("$year-$month-$day"));

            // Si es un día hábil (lunes a viernes)
            if ($dayOfWeek >= 1 && $dayOfWeek <= 5) {
                $businessDays++;
            }
            // Si alcanzamos el quinto día hábil
            if ($businessDays === 5) {
                $fifthDay = $day;
                break;
            }
        }
        $date = str_pad($fifthDay, 2, "0", STR_PAD_LEFT) . '/' . str_pad($month, 2, "0", STR_PAD_LEFT) . '/' . $year;
        return $date;
    }

    public static function prepareDateForUSTime($date) {
        $datetime = DateTime::createFromFormat('Y-m-d', $date);
        if ($datetime) {
            return $datetime->format('d/m/Y');
        }
        throw new Exception('Fecha inválida para conversión: ' . $date);
    }

}
