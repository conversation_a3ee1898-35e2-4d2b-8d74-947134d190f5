<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'stagingsipe.siansystem.com/admin';
$domain = 'https://stagingsipe.siansystem.com';
$report_domain = 'rstaging.siansystem.com';
$org = 'sipe';
$us = 'us_staging';
$database_server = '************';
$database_name = 'sipe_staging';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$database_sian = 'sian_staging';
$mongo_enabled = true;
$mongo_server = '************';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = '';
$e_billing_certificate_pass = '';//PIN
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '',//usuario
        'password' => ''
    ]
];
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_STAGING;
$environment = YII_ENVIRONMENT_STAGING;
