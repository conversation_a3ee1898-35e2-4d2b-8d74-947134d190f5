<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of USEmail
 *
 * <AUTHOR>
 */
class SIANMail {

    public static function connect() {
        $smtp_host = Yii::app()->params['smtp_host'];
        $smtp_port = Yii::app()->params['smtp_port'];
        $smtp_security = Yii::app()->params['smtp_security'];
        $smtp_username = Yii::app()->params['smtp_username'];
        $smtp_password = Yii::app()->params['smtp_password'];

        return USMail::init($smtp_host, $smtp_port, $smtp_security, $smtp_username, $smtp_password);
    }

    public static function connectSupport() {
        $smtp_host = Yii::app()->params['smtp_host'];
        $smtp_port = Yii::app()->params['smtp_port'];
        $smtp_security = Yii::app()->params['smtp_security'];
        $smtp_support_username = Yii::app()->params['smtp_support_username'];
        $smtp_support_password = Yii::app()->params['smtp_support_password'];

        return USMail::init($smtp_host, $smtp_port, $smtp_security, $smtp_support_username, $smtp_support_password);
    }

    /**
     * Envía un email
     * @param mixed $fromEmail Email de destino
     * @param mixed $fromName Nombre de destino
     * @param mixed $subject Asunto o título
     * @param mixed $message Mensaje o cuerpo
     * @param array $toEmails Array de emails de destino en formato 'Nombre' => 'Correo

      '
     * @param array $attachments Array de adjuntos
     * @return boolean | mixed TRUE si se envió o el error en formato string
     */
    public static function sendMail($p_s_type, $fromEmail, $fromName, $subject, $message, array $toEmails, array $attachments = []) {

        //Si no es producción sobreescribimos los destinatarios
        if (Yii::app()->params['environment'] != YII_ENVIRONMENT_PRODUCTION) {
            $toEmails = Yii::app()->params['support_emails'];
        }

        switch ($p_s_type) {
            case EmailQueue::TYPE_NORMAL:
                $o_mail = self::connect();
                break;
            case EmailQueue::TYPE_SUPPORT:
                $o_mail = self::connectSupport();
                break;
        }
        $m_result = USMail::sendMail($o_mail, $fromEmail, $fromName, $subject, $message, $toEmails, $attachments);

        if ($m_result === true) {
            return true;
        } else {
            USLog::save([
                'code' => 500,
                'file' => __FILE__,
                'line' => __LINE__,
                'message' => $m_result,
            ]);
            return $m_result;
        }
    }

}
