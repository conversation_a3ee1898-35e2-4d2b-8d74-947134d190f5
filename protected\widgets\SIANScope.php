<?php

class SIANScope extends CWidget {

    //CONST
    public $id;
    public $form;
    public $model;
    public $has_parent = false;
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}'>";
        if ($this->controller->action->id === 'create' && !$this->has_parent) {
            $a_scopes = Scenario::getScopeItems();
            echo $this->_iterate($this->model->scope, $this->model->scenario->scopes, $a_scopes);
        }
        echo "</div>";
    }

    private function _iterate($s_exclude, array $a_supported, array $a_scope_items) {

        $a_params = $_GET;
        //Agregamos acción
        array_unshift($a_params, 'create');
        //
        $a_links = [];
        foreach ($a_supported as $s_scope) {
            if ($s_scope !== $s_exclude) {
                $a_params['scope'] = $s_scope;
                $a_links[] = CHtml::link('Cambiar a ' . $a_scope_items[$s_scope], $a_params);
            }
        }

        return implode(' / ', $a_links);
    }

}
