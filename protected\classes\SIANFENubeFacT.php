<?php

/**
 * <AUTHOR>
 */
class SIANFENubeFacT {

    // *Tipos de operacion (NubeFact):*
    public static $GENERATE_RECEIPT = "generar_comprobante"; // OPERACIÓN 1: GENERAR FACTURAS, B<PERSON>ETAS Y NOTAS CON JSON
    public static $CONSULT_RECEIPT = "consultar_comprobante"; // OPERACIÓN 2: CONSULTAR FACTURAS, BOLETAS Y NOTAS CON JSON
    public static $GENERATE_OVERRIVE = "generar_anulacion"; // OPERACIÓN 3: ANULAR FACTURAS, BOLETAS Y NOTAS CON JSON
    public static $CONSULT_ANNULMENT = "consultar_anulacion"; // OPERACIÓN 4: CONSULTAR ANULACIÓN DE FACTURAS, BOLETAS Y NOTAS CON JSON
    public static $GENERATE_GUIDE = "generar_guia"; // GENERAR GUIA
    public static $CONSULT_GUIDE = "consultar_guia"; // CONSULTAR GUIA

    /**
     * Trae la data (array) el json de Emitir Factura
     * @param obj $p_o_commercial_movement: Respuesta de NubeFact
     * @param obj $p_o_document: Respuesta de NubeFact
     * @return array $data: data (array) el json de Emitir Factura 
     */
    public static function getInvoice($p_o_commercial_movement, $p_o_document) {
        $a_header = self::getTicketHeader($p_o_commercial_movement, $p_o_document);
        $a_detail = self::getTicketDetail($a_header, $p_o_commercial_movement->tempItems);

        $o_electronic_bill = new ElectronicBill();
        $o_electronic_bill->setSeries($a_header['document_serie']); // setSerie
        $o_electronic_bill->setCorrelativeNumber($a_header['document_correlative']); // setNumero
        $o_electronic_bill->setSunatTransaction($a_header['detraction'] == 1 ? $o_electronic_bill::$OPERACION_SUJETA_A_DETRACCION : $o_electronic_bill::$VENTA_INTERNA);
        $o_electronic_bill->setCustomer($a_header['identification_type'], $a_header['identification_number'], $a_header['aux_person_name'], $a_header['aux_person_address']);
        $o_electronic_bill->addCustomerEmail($a_header['aux_person_email']);
        if (isset($a_header['aux_person_email'])) {
            $o_electronic_bill->setSendAutomaticallyToClient(Yii::app()->params['e_billing_send_automatically_to_client']);
        }
        $o_electronic_bill->setDateOfIssue($a_header['emission_date']);
        $o_electronic_bill->setDueDate($a_header['expiration_date']);
        $o_electronic_bill->setCurrency($a_header['currency']);
        $o_electronic_bill->setExchangeRate($a_header['exchange_rate']);
        $o_electronic_bill->setIgvPercentage($a_header['igv_percent']); // setPorcentajeIGV
        $o_electronic_bill->setGlobalDiscount(0);
        $o_electronic_bill->setTotalDiscount(0);
        $o_electronic_bill->setTotalAdvance(0);
        $o_electronic_bill->setTotalTaxed($a_header['affected']); // setTotalGravada
        $o_electronic_bill->setTotalInaffected($a_header['inaffected']);
        $o_electronic_bill->setTotalExonerated($a_header['nobill']);
        $o_electronic_bill->setTotalIGV($a_header['igv']);
        $o_electronic_bill->setTotalFree($a_header['free']);
        $o_electronic_bill->setTotalIsc($a_header['isc']); // getTotalIsc
        $o_electronic_bill->setTotal($a_header['total']); // setTotal
        $o_electronic_bill->setTotalPerception($a_header['perception']);
        if ($a_header['perception'] > 0) {
            $o_electronic_bill->setPerceptionType($o_electronic_bill::$PERCEPCION_VENTA_INTERNA);
        }
        // $o_electronic_bill->setPerceptionTaxBase($a_header['affected']); // no sabemos
        $o_electronic_bill->setTotalIncludedPerception($a_header['real']);
        $o_electronic_bill->setDetraction($a_header['detraction'] == 1);
        $o_electronic_bill->setObservations($a_header['observation']);
        $o_electronic_bill->setSendAutomaticallyToSunat(true);
        $o_electronic_bill->setPaymentTerms($a_header['condition']);
        $o_electronic_bill->setPaymentMethod($a_header['payment_method']);

        if ($a_header['condition'] === Condition::CREDIT) {
            $o_electronic_bill->setPaymentMethod("venta_al_credito");
            $o_electronic_bill->setPaymentTerms($a_header['condition'] . " " . $a_header['credit_days'] . " días");
            $o_electronic_bill->addFee(1, $a_header['expiration_date'], $a_header['total']);
        }

        $o_electronic_bill->setPDFformat("A4");

        if (count($a_detail) > 0) {
            foreach ($a_detail as $detail) {
                $o_electronic_bill->addItem($detail['sunat_code'], $detail['product_id'], $detail['product_name'], $detail['pres_quantity'], $detail['vprice'], $detail['sprice'], $detail['net'], ElectronicBill::$IGV_GRAVADO, $detail['igv'], $detail['total']);
            }
        }

        return $o_electronic_bill->getArray();
    }

    /**
     * Trae la data (array) el json de Emitir Boleta
     * @param obj $p_o_commercial_movement: Respuesta de NubeFact
     * @param obj $p_o_document: Respuesta de NubeFact
     * @return array $data: data (array) el json de Emitir Factura 
     */
    public static function getTicket($p_o_commercial_movement, $p_o_document) {
        $a_header = self::getTicketHeader($p_o_commercial_movement, $p_o_document);
        $a_detail = self::getTicketDetail($a_header, $p_o_commercial_movement->tempItems);

        $o_ticket = new ElectronicTicket();
        $o_ticket->setSeries($a_header['document_serie']); // setSerie
        $o_ticket->setCorrelativeNumber($a_header['document_correlative']); // setNumero
        $o_ticket->setSunatTransaction($a_header['detraction'] == 1 ? $o_ticket::$OPERACION_SUJETA_A_DETRACCION : $o_ticket::$VENTA_INTERNA);
        $o_ticket->setCustomer($a_header['identification_type'], $a_header['identification_number'], $a_header['aux_person_name'], $a_header['aux_person_address']);
        $o_ticket->addCustomerEmail($a_header['aux_person_email']);
        $o_ticket->setDateOfIssue($a_header['emission_date']);
        $o_ticket->setDueDate($a_header['expiration_date']);
        $o_ticket->setCurrency($a_header['currency']);
        $o_ticket->setExchangeRate($a_header['exchange_rate']);
        $o_ticket->setIgvPercentage($a_header['igv_percent']); // setPorcentajeIGV
        $o_ticket->setGlobalDiscount(0);
        $o_ticket->setTotalDiscount(0);
        $o_ticket->setTotalAdvance(0);
        $o_ticket->setTotalTaxed($a_header['affected']);
        $o_ticket->setTotalInaffected($a_header['inaffected']);
        $o_ticket->setTotalExonerated($a_header['nobill']);
        $o_ticket->setTotalIGV($a_header['igv']);
        $o_ticket->setTotalFree($a_header['free']);
        $o_ticket->setTotalIsc($a_header['isc']); // getTotalIsc
        $o_ticket->setTotal($a_header['total']); // setTotal
        $o_ticket->setTotalPerception($a_header['perception']);
        if ($a_header['perception'] > 0) {
            $o_ticket->setPerceptionType($o_ticket::$PERCEPCION_VENTA_INTERNA);
        }
        // $ticket->setPerceptionTaxBase($a_header['affected']); // no sabemos
        $o_ticket->setTotalIncludedPerception($a_header['real']);
        $o_ticket->setDetraction($a_header['detraction'] == 1);
        $o_ticket->setObservations($a_header['observation']);
        $o_ticket->setSendAutomaticallyToSunat(false); // enviar_automaticamente_a_la_sunat
        $o_ticket->setPaymentTerms($a_header['condition']);
        $o_ticket->setPaymentMethod($a_header['payment_method']);

        if ($a_header['condition'] === Condition::CREDIT) {
            $o_ticket->setPaymentMethod("venta_al_credito");
            $o_ticket->setPaymentTerms($a_header['condition'] . " " . $a_header['credit_days'] . " días");
            $o_ticket->addFee(1, $a_header['expiration_date'], $a_header['total']);
        }

        if (count($a_detail) > 0) {
            foreach ($a_detail as $detail) {
                $o_ticket->addItem($detail['sunat_code'], $detail['product_id'], $detail['product_name'], $detail['pres_quantity'], $detail['vprice'], $detail['sprice'], $detail['net'], ElectronicTicket::$IGV_GRAVADO, $detail['igv'], $detail['total']);
            }
        }

        return $o_ticket->getArray();
    }

    public static function getCreditNote($p_o_commercial_movement, $p_o_document, $p_o_movement_parent) {
        $a_header = self::getTicketHeader($p_o_commercial_movement, $p_o_document, $p_o_movement_parent);
        $a_detail = self::getTicketDetail($a_header, $p_o_commercial_movement->tempItems);

        $o_credit_note = new CreditNote();
        $o_credit_note->setSeries($a_header['document_serie']); // setSerie
        $o_credit_note->setCorrelativeNumber($a_header['document_correlative']); // setNumero
        $o_credit_note->setSunatTransaction($a_header['detraction'] == 1 ? $o_credit_note::$OPERACION_SUJETA_A_DETRACCION : $o_credit_note::$VENTA_INTERNA);
        $o_credit_note->setCustomer($a_header['identification_type'], $a_header['identification_number'], $a_header['aux_person_name'], $a_header['aux_person_address']);
        $o_credit_note->addCustomerEmail($a_header['aux_person_email']);
        $o_credit_note->setDateOfIssue($a_header['emission_date']);
        $o_credit_note->setDueDate($a_header['expiration_date']);
        $o_credit_note->setCurrency($a_header['currency']);
        $o_credit_note->setExchangeRate($a_header['exchange_rate']);
        $o_credit_note->setIgvPercentage($a_header['igv_percent']); // setPorcentajeIGV
        $o_credit_note->setGlobalDiscount(0);
        $o_credit_note->setTotalDiscount(0);
        $o_credit_note->setTotalAdvance(0);
        $o_credit_note->setTotalTaxed($a_header['affected']); // setTotalGravada
        $o_credit_note->setTotalInaffected($a_header['inaffected']);
        $o_credit_note->setTotalExonerated($a_header['nobill']);
        $o_credit_note->setTotalIGV($a_header['igv']);
        $o_credit_note->setTotalFree($a_header['free']);
        $o_credit_note->setTotalIsc($a_header['isc']);
        $o_credit_note->setTotal($a_header['total']);
        $o_credit_note->setTotalPerception($a_header['perception']);
        if ($a_header['perception'] > 0) {
            $o_credit_note->setPerceptionType($o_credit_note::$PERCEPCION_VENTA_INTERNA);
        }
        // $o_credit_note->setPerceptionTaxBase($a_header['affected']); // no sabemos
        $o_credit_note->setTotalIncludedPerception($a_header['real']);
        $o_credit_note->setDetraction($a_header['detraction'] == 1);
        $o_credit_note->setObservations($a_header['observation']);

        if ($a_header['parent_sunat_code'] === Document::FACTURA) {
            $o_credit_note->setDocumentWhichModifiesType($o_credit_note::$FACTURA);
            $o_credit_note->setPDFformat("A4");
        } else {
            $o_credit_note->setDocumentWhichModifiesType($o_credit_note::$BOLETA);
        }

        $o_credit_note->setDocumentToModifySeries($a_header['parent_document_serie']);
        $o_credit_note->setDocumentWhichModifiesNumber($a_header['parent_document_correlative']);
        $o_credit_note->setTypeOfCreditNote($a_header['reason_code']); // Código de motivo

        $o_credit_note->setSendAutomaticallyToSunat(true);

        $o_credit_note->setPaymentTerms($a_header['condition']);
        $o_credit_note->setPaymentMethod($a_header['payment_method']);

        if ($a_header['condition'] === Condition::CREDIT) {
            $o_credit_note->setPaymentMethod("venta_al_credito");
            $o_credit_note->setPaymentTerms($a_header['condition'] . " " . $a_header['credit_days'] . " días");
            $o_credit_note->addFee(1, $a_header['expiration_date'], $a_header['total']);
        }

        if (count($a_detail) > 0) {
            foreach ($a_detail as $detail) {
                $o_credit_note->addItem($detail['sunat_code'], $detail['product_id'], $detail['product_name'], $detail['pres_quantity'], $detail['vprice'], $detail['sprice'], $detail['net'], ElectronicBill::$IGV_GRAVADO, $detail['igv'], $detail['total']);
            }
        }

        return $o_credit_note->getArray();
    }

    public static function getDebitNote($p_o_commercial_movement, $p_o_document, $p_o_movement_parent) {
        $a_header = self::getTicketHeader($p_o_commercial_movement, $p_o_document, $p_o_movement_parent);
        $a_detail = self::getTicketDetail($a_header, $p_o_commercial_movement->tempItems);

        $o_debit_note = new DebitNote();
        $o_debit_note->setSeries($a_header['document_serie']); // setSerie
        $o_debit_note->setCorrelativeNumber($a_header['document_correlative']); // setNumero
        $o_debit_note->setSunatTransaction($a_header['detraction'] == 1 ? $o_debit_note::$OPERACION_SUJETA_A_DETRACCION : $o_debit_note::$VENTA_INTERNA);
        $o_debit_note->setCustomer($a_header['identification_type'], $a_header['identification_number'], $a_header['aux_person_name'], $a_header['aux_person_address']);
        $o_debit_note->addCustomerEmail($a_header['aux_person_email']);
        $o_debit_note->setDateOfIssue($a_header['emission_date']);
        $o_debit_note->setDueDate($a_header['expiration_date']);
        $o_debit_note->setCurrency($a_header['currency']);
        $o_debit_note->setExchangeRate($a_header['exchange_rate']);
        $o_debit_note->setIgvPercentage($a_header['igv_percent']); // setPorcentajeIGV
        $o_debit_note->setGlobalDiscount(0);
        $o_debit_note->setTotalDiscount(0);
        $o_debit_note->setTotalAdvance(0);
        $o_debit_note->setTotalTaxed($a_header['affected']); // setTotalGravada
        $o_debit_note->setTotalInaffected($a_header['inaffected']);
        $o_debit_note->setTotalExonerated($a_header['nobill']);
        $o_debit_note->setTotalIGV($a_header['igv']);
        $o_debit_note->setTotalFree($a_header['free']);
        $o_debit_note->setTotalIsc($a_header['isc']); // getTotalIsc
        $o_debit_note->setTotal($a_header['total']);
        $o_debit_note->setTotalPerception($a_header['perception']);
        if ($a_header['perception'] > 0) {
            $o_debit_note->setPerceptionType($o_debit_note::$PERCEPCION_VENTA_INTERNA);
        }
        // $o_debit_note->setPerceptionTaxBase($a_header['affected']); // no sabemos
        $o_debit_note->setTotalIncludedPerception($a_header['real']);
        $o_debit_note->setDetraction($a_header['detraction'] == 1);
        $o_debit_note->setObservations($a_header['observation']);

        if ($a_header['parent_sunat_code'] === Document::FACTURA) {
            $o_debit_note->setDocumentWhichModifiesType($o_debit_note::$FACTURA);
            $o_debit_note->setPDFformat("A4");
        } else {
            $o_debit_note->setDocumentWhichModifiesType($o_debit_note::$BOLETA);
        }

        $o_debit_note->setDocumentToModifySeries($a_header['parent_document_serie']);
        $o_debit_note->setDocumentWhichModifiesNumber($a_header['parent_document_correlative']);
        $o_debit_note->setDebitNoteType($a_header['reason_code']); // Código de motivo

        $o_debit_note->setSendAutomaticallyToSunat(true);
        $o_debit_note->setPaymentTerms($a_header['condition']);
        $o_debit_note->setPaymentMethod($a_header['payment_method']);

        if ($a_header['condition'] === Condition::CREDIT) {
            $o_debit_note->setPaymentMethod("venta_al_credito");
            $o_debit_note->setPaymentTerms($a_header['condition'] . " " . $a_header['credit_days'] . " días");
            $o_debit_note->addFee(1, $a_header['expiration_date'], $a_header['total']);
        }

        if (count($a_detail) > 0) {
            foreach ($a_detail as $detail) {
                $o_debit_note->addItem($detail['sunat_code'], $detail['product_id'], $detail['product_name'], $detail['pres_quantity'], $detail['vprice'], $detail['sprice'], $detail['net'], DebitNote::$IGV_GRAVADO, $detail['igv'], $detail['total']);
            }
        }

        return $o_debit_note->getArray();
    }

    public static function getAnnulment($p_o_movement, $p_o_document, $p_s_reason_annulment) {
        $voucher_type = null;

        switch ($p_o_document->sunat_code) {
            case Document::BOLETA:
                $voucher_type = Annulment::$BOLETA;
                break;
            case Document::FACTURA:
                $voucher_type = Annulment::$FACTURA;
                break;
            case Document::CREDIT_NOTE:
            case Document::SPECIAL_CREDIT_NOTE:
            case Document::NOT_DOMICILED_CREDIT_NOTE:
                $voucher_type = Annulment::$FACTURA;
                break;
            case Document::DEBIT_NOTE:
            case Document::SPECIAL_DEBIT_NOTE:
            case Document::NOT_DOMICILED_DEBIT_NOTE:
                $voucher_type = Annulment::$FACTURA;
                break;
            default:
                throw new Exception("No se puede anular este tipo de documento: '{$p_o_document->sunat_code}'");
        }

        $annulment = new Annulment();
        $annulment->setVoucherType($voucher_type);
        $annulment->setSeries($p_o_movement->document_serie);
        $annulment->setCorrelativeNumber(substr($p_o_movement->document_correlative, -8));
        $annulment->setReason($p_s_reason_annulment);
        // $annulment->setUniqueCode("");
        return $annulment->getArray();
    }

    /**
     * # GENERAR GUIA REMITENTE
     */
    public static function getSenderGuide($p_o_warehouse_movement, $p_o_document) {
        $o_movement = $p_o_warehouse_movement->movement;

        $a_sender_guide = [];
        $a_sender_guide["operacion"] = "generar_guia";
        $a_sender_guide["tipo_de_comprobante"] = 7;
        $a_sender_guide["serie"] = $o_movement->document_serie; // "TTT1"
        $a_sender_guide["numero"] = substr($o_movement->document_correlative, -8); // "1";
        // Traer al Cliente
        $o_aux_person = Person::model()->findByPk($o_movement->aux_person_id);
        $a_sender_guide['cliente_tipo_de_documento'] = $o_aux_person->identification_type; // 6;
        $a_sender_guide['cliente_numero_de_documento'] = $o_aux_person->identification_number;
        $a_sender_guide["cliente_denominacion"] = $o_aux_person->person_name; // "NUBEFACT SA";
        // Traer direccion del cliente
        $o_address = Address::model()->findByAttributes(['owner' => Person::OWNER, 'owner_id' => $o_aux_person->person_id, 'order' => 1]);
        $a_sender_guide["cliente_direccion"] = isset($o_address->address) ? $o_address->address : '-'; // "MIRAFLORES LIMA";
        // Traer email del cliente
        $o_email = Email::model()->findByAttributes([
            'owner' => Person::OWNER,
            'owner_id' => $o_aux_person->person_id,
            'order' => 1,
        ]);

        if (count($o_email) > 0) {
            $a_sender_guide["cliente_email"] = is_null($o_email) ? '' : $o_email->email_address; // "<EMAIL>";
            $a_sender_guide["cliente_email_1"] = "";
            $a_sender_guide["cliente_email_2"] = "";
            if (!empty($a_sender_guide["cliente_email"])) {
                $a_sender_guide["enviar_automaticamente_al_cliente"] = Yii::app()->params['e_billing_send_automatically_to_client'];
            }
        }

        $a_sender_guide["fecha_de_emision"] = self::getFormattedDate($o_movement->emission_date); // "16-12-2022";
        $a_sender_guide["observaciones"] = isset($o_movement->observation) ? $o_movement->observation : ""; // "observaciones";

        $o_reason_transfer = Multitable::model()->findByPk(array('multi_id' => $p_o_warehouse_movement->reason_transfer_id));

        $a_sender_guide["motivo_de_traslado"] = $o_reason_transfer->value; // "01"
        $a_sender_guide["peso_bruto_total"] = $p_o_warehouse_movement->total_weight;  // "1";
        $a_sender_guide["peso_bruto_unidad_de_medida"] = "KGM"; // KGM = “Kilogramos”
        $a_sender_guide["numero_de_bultos"] = $p_o_warehouse_movement->number_packages;
        ;
        // tipo_de_transporte (Sólo para GRE Remitente): "01" = "TRANSPORTE PÚBLICO", "02" = "TRANSPORTE PRIVADO"
        $a_sender_guide["tipo_de_transporte"] = $p_o_warehouse_movement->transport_data == 1 ? "02" : "01"; // "02";
        $a_sender_guide["fecha_de_inicio_de_traslado"] = self::getFormattedDate($p_o_warehouse_movement->transfer_start_date); // "21-12-2022";
        // compañía de transporte
        $o_transport_company = Person::model()->findByPk(array('person_id' => $p_o_warehouse_movement->transport_company_id));
        $a_sender_guide["transportista_documento_tipo"] = $o_transport_company->identification_type; // "6";
        $a_sender_guide["transportista_documento_numero"] = $o_transport_company->identification_number; // "20600695771"; 
        $a_sender_guide["transportista_denominacion"] = $o_transport_company->person_name; //"NUBEFACT SA";
        // $a_sender_guide["transportista_placa_numero"] = "ABC444"; // 
        // unidades de transporte
        $a_transport_units = $p_o_warehouse_movement->tempTransportUnits;
        foreach ($a_transport_units as $transport_unit) {
            if ($transport_unit['default'] == 1) {
                $o_transport_unit = TransportUnit::model()->findByPk(array('transport_unit_id' => $transport_unit['owner_id']));
                $a_sender_guide["transportista_placa_numero"] = $o_transport_unit->plate; // "ABC444";
            }
        }

        if ($p_o_warehouse_movement->transport_data == 1) { // 1 = PRIVADO, 0 = PUBLICO
            // 
            $a_shippers = $p_o_warehouse_movement->tempShippers;

            // Conductores
            foreach ($a_shippers as $shipper) {
                if ($shipper['default'] == 1) {
                    $o_shipper = Shipper::model()->with(array('person' => array('alias' => 'P')))->findByPk(array('shipper_id' => $shipper['owner_id']));
                    // conductor
                    $a_sender_guide["conductor_documento_tipo"] = $o_shipper->person->identification_type; // "1";
                    $a_sender_guide["conductor_documento_numero"] = $o_shipper->person->identification_number; // "XXXXXXXX";
                    $a_sender_guide["conductor_nombre"] = $o_shipper->person->firstname; //"JORGE";
                    $a_sender_guide["conductor_apellidos"] = $o_shipper->person->paternal . " " . $o_shipper->person->maternal; // "LOPEZ";
                    $a_sender_guide["conductor_numero_licencia"] = $o_shipper->license_number; // "QXXXXXXXX";
                }
            }
        }
        //
        $o_originAddress = $o_movement->tempOriginAddresses[0];
        $o_deliveryAddress = $o_movement->tempAddresses[0];

        $o_originAddressGeoloc = Geoloc::model()->with([
                    'ubigeo'
                ])->findByAttributes([
            'dept_code' => $o_originAddress->dept_code,
            'prov_code' => $o_originAddress->prov_code,
            'dist_code' => $o_originAddress->dist_code
        ]);
        $o_deliveryAddressGeoloc = Geoloc::model()->with([
                    'ubigeo'
                ])->findByAttributes([
            'dept_code' => $o_deliveryAddress->dept_code,
            'prov_code' => $o_deliveryAddress->prov_code,
            'dist_code' => $o_deliveryAddress->dist_code
        ]);
        $a_sender_guide["punto_de_partida_ubigeo"] = $o_originAddressGeoloc->ubigeo->dept_code . $o_originAddressGeoloc->ubigeo->prov_code . $o_originAddressGeoloc->ubigeo->dist_code; // "151021";
        $a_sender_guide["punto_de_partida_direccion"] = $o_originAddress->address; //"DIRECCION PARTIDA";
        // punto_de_llegada_codigo_establecimiento_sunat: Para los motivos de traslado con código 04, 18 Ejemplo: “0000”
        // $a_sender_guide["punto_de_partida_codigo_establecimiento_sunat"] = "0000";        
        $a_sender_guide["punto_de_llegada_ubigeo"] = $o_deliveryAddressGeoloc->ubigeo->dept_code . $o_deliveryAddressGeoloc->ubigeo->prov_code . $o_deliveryAddressGeoloc->ubigeo->dist_code; //"211101";
        $a_sender_guide["punto_de_llegada_direccion"] = $o_deliveryAddress->address; // "DIRECCION LLEGADA";
        // punto_de_llegada_codigo_establecimiento_sunat: Para los motivos de traslado con código 04, 18 Ejemplo: “0000”
        // $a_sender_guide["punto_de_llegada_codigo_establecimiento_sunat"] = "0000";
        //
        $a_sender_guide["formato_de_pdf"] = "A4";

        if (count($p_o_warehouse_movement->tempItems) > 0) {
            $item_header = [];
            foreach ($p_o_warehouse_movement->tempItems as $i => $o_item) {
                $o_presentation = Presentation::model()->with('multitable2')->findByAttributes([
                    'product_id' => $o_item->itemObj->product_id,
                    'equivalence' => $o_item->itemObj->equivalence
                ]);

                // $item_header[$i]['unidad_de_medida'] = $o_item['sunat_code']; // "NIU"
                if (isset($o_presentation->multitable2)) {
                    $item_header[$i]['unidad_de_medida'] = $o_presentation->multitable2->value;
                } else {
                    $item_header[$i]['unidad_de_medida'] = Presentation::DEFAULT_SERVICE_CODE;
                }

                $item_header[$i]['codigo'] = $o_item->itemObj->product_id; // "001"
                $item_header[$i]['descripcion'] = $o_item->itemObj->product_name; // "DETALLE DEL PRODUCTO 1"
                $item_header[$i]['cantidad'] = $o_item->itemObj->pres_quantity; // "1"
            }
            $a_sender_guide['items'] = $item_header;
        }

        return $a_sender_guide;
    }

    /**
     * # GENERAR GUIA DE REMISION DE TRANSPORTISTA
     */
    public static function getCarrierRemissionGuide($p_o_product_list_movement, $p_o_document) {
        $o_movement = $p_o_product_list_movement->movement;

        $o_CarrierRemissionGuide = new CarrierRemissionGuide();
        $o_CarrierRemissionGuide->setSeries($o_movement->document_serie); // "TTT1"
        $o_CarrierRemissionGuide->setCorrelativeNumber(substr($o_movement->document_correlative, -8));
        // Traer al Cliente
        $o_aux_person = Person::model()->findByPk($o_movement->aux_person_id);
        // Traer direccion del cliente
        $o_address = Address::model()->findByAttributes(['owner' => Person::OWNER, 'owner_id' => $o_aux_person->person_id, 'order' => 1]);
        // Seteamos al Cliente
        $o_CarrierRemissionGuide->setCustomer($o_aux_person->identification_type, $o_aux_person->identification_number, $o_aux_person->person_name, isset($o_address->address) ? $o_address->address : '-');

        // Traer email del cliente
        $o_email = Email::model()->findByAttributes([
            'owner' => Person::OWNER,
            'owner_id' => $o_aux_person->person_id,
            'order' => 1,
        ]);

        if (count($o_email) > 0) {
            $o_CarrierRemissionGuide->addCustomerEmail(is_null($o_email) ? '' : $o_email->email_address);
            if (isset($o_email->email_address)) {
                $o_CarrierRemissionGuide->setSendAutomaticallyToClient(Yii::app()->params['e_billing_send_automatically_to_client']);
            }
        }
        //
        $o_CarrierRemissionGuide->setDateOfIssue($o_movement->emission_date);
        $o_CarrierRemissionGuide->setObservations(isset($o_movement->observation) ? $o_movement->observation : "");
        //
        $o_CarrierRemissionGuide->setTotalGrossWeight($p_o_product_list_movement->total_weight); // peso_bruto_total
        $o_CarrierRemissionGuide->setGrossWeightUnitOfMeasure("KGM"); // "peso_bruto_unidad_de_medida": "KGM". KGM = “Kilogramos”
        $o_CarrierRemissionGuide->setTransferStartDate($p_o_product_list_movement->transfer_start_date); // "fecha_de_inicio_de_traslado": "21-12-2022",
        // unidades de transporte
        $a_transport_units = $p_o_product_list_movement->tempTransportUnits;
        foreach ($a_transport_units as $transport_unit) {
            if ($transport_unit['default'] == 1) {
                $o_transport_unit = TransportUnit::model()->findByPk(array('transport_unit_id' => $transport_unit['owner_id']));
                $o_CarrierRemissionGuide->setCarrierPlateNumber($o_transport_unit->plate); // "transportista_placa_numero": "ABC444"
            }
        }
        // Conductores
        $a_shippers = $p_o_product_list_movement->tempShippers;
        foreach ($a_shippers as $shipper) {
            if ($shipper['default'] == 1) {
                $o_shipper = Shipper::model()->with(array('person' => array('alias' => 'P')))->findByPk(array('shipper_id' => $shipper['owner_id']));
                // conductor
                $o_CarrierRemissionGuide->setDriverDocumentType($o_shipper->person->identification_type); // "1";
                $o_CarrierRemissionGuide->setDriverDocumentNumber($o_shipper->person->identification_number); // "XXXXXXXX";
                $o_CarrierRemissionGuide->setDriverName($o_shipper->person->firstname); //"JORGE";
                $o_CarrierRemissionGuide->setDriverSurname(trim($o_shipper->person->paternal . " " . $o_shipper->person->maternal)); // "LOPEZ";
                $o_CarrierRemissionGuide->setDriverLicenseNumber($o_shipper->license_number); // "QXXXXXXXX";
            }
        }

        // DESTINATARIO
        $o_recipient = Person::model()->findByPk($p_o_product_list_movement->recipient_id);
        $o_CarrierRemissionGuide->setRecipientDocumentType($o_recipient->identification_type); // "7"
        $o_CarrierRemissionGuide->setRecipientDocumentNumber($o_recipient->identification_number); // "87654321"
        $o_CarrierRemissionGuide->setRecipientDenomination($o_recipient->person_name); // "DESTINATARIO"
        //
        $a_origin_addresses = $o_movement->tempOriginAddresses[0];
        $o_CarrierRemissionGuide->setStartingPointUbigeo($a_origin_addresses->dept_code . $a_origin_addresses->prov_code . $a_origin_addresses->dist_code); // "punto_de_partida_ubigeo": "151021"
        $o_CarrierRemissionGuide->setStartingPointDirection($a_origin_addresses->address); // "punto_de_partida_direccion": "DIRECCION PARTIDA"
        $o_CarrierRemissionGuide->setStartingPointCodeEstablishmentSunat(""); // "punto_de_partida_codigo_establecimiento_sunat": "0000"
        //
        $a_addresses = $o_movement->tempAddresses[0];
        $o_CarrierRemissionGuide->setArrivalPointUbigeo($a_addresses->dept_code . $a_addresses->prov_code . $a_addresses->dist_code); // "punto_de_llegada_ubigeo": "211101"
        $o_CarrierRemissionGuide->setArrivalPointDirection($a_addresses->address); // "punto_de_llegada_direccion": "DIRECCION LLEGADA"
        $o_CarrierRemissionGuide->setArrivalPointCodeEstablishmentSunat(""); // "punto_de_llegada_codigo_establecimiento_sunat": "0000"
        $o_CarrierRemissionGuide->setSendAutomaticallyToClient(false); // "enviar_automaticamente_al_cliente": "false"
        $o_CarrierRemissionGuide->setPDFformat("A4"); // "formato_de_pdf": ""

        if (count($p_o_product_list_movement->tempItems) > 0) {
            foreach ($p_o_product_list_movement->tempItems as $i => $o_item) {
                $o_presentation = Presentation::model()->with('multitable2')->findByAttributes([
                    'product_id' => $o_item->itemObj->product_id,
                    'equivalence' => $o_item->itemObj->equivalence
                ]);

                $unit_of_measurement = isset($o_presentation->multitable2) ? $o_presentation->multitable2->value : Presentation::DEFAULT_SERVICE_CODE;
                $code = $o_item->itemObj->product_id; // "001"
                $description = $o_item->itemObj->product_name; // "DETALLE DEL PRODUCTO 1"
                $quantity = $o_item->itemObj->pres_quantity; // "1"
                $o_CarrierRemissionGuide->addItemGR($unit_of_measurement, $code, $description, $quantity);
            }
        }

        return $o_CarrierRemissionGuide->getArray();
    }

    /**
     * Traer el array para Consulta de documentos
     * @param string $p_s_operation: `consultar_comprobante` o `consultar_anulacion`
     * @param string $s_sunat_code: parametro para obtener `tipo_de_comprobante`
     * @param string $p_s_serie: `serie`, ejemplo: "BBB1"
     * @param string $p_s_correlative: `numero`, ejemplo: "1"
     * @return array $a_data_document: data para el json
     */
    public static function getArrayToCheckDocuments($p_s_operation, $s_sunat_code, $p_s_serie, $p_s_correlative) {
        if (($p_s_operation === self::$CONSULT_RECEIPT || $p_s_operation === self::$CONSULT_ANNULMENT || $p_s_operation === self::$CONSULT_GUIDE) === false) {
            throw new Exception("La opción `operacion` no es correcta: '{$p_s_operation}'");
        }

        $a_data_document = [];
        $a_data_document['operacion'] = $p_s_operation;

        switch ($s_sunat_code) {
            case Document::BOLETA:
                $a_data_document['tipo_de_comprobante'] = DocumentContract::$BOLETA;
                break;
            case Document::FACTURA:
                $a_data_document['tipo_de_comprobante'] = DocumentContract::$FACTURA;
                break;
            case Document::CREDIT_NOTE:
            case Document::SPECIAL_CREDIT_NOTE:
            case Document::NOT_DOMICILED_CREDIT_NOTE:
                $a_data_document['tipo_de_comprobante'] = DocumentContract::$NOTA_CREDITO;
                break;
            case Document::DEBIT_NOTE:
            case Document::SPECIAL_DEBIT_NOTE:
            case Document::NOT_DOMICILED_DEBIT_NOTE:
                $a_data_document['tipo_de_comprobante'] = DocumentContract::$NOTA_DEBITO;
                break;
            case Document::REMISSION_GUIDE:
                $a_data_document['tipo_de_comprobante'] = RemissionGuide::$SENDER_REMISSION_GUIDE;
                break;
            default:
                throw new Exception("No se puede consultar este tipo de documento: '{$s_sunat_code}'");
        }

        $a_data_document['serie'] = $p_s_serie;
        $a_data_document['numero'] = substr($p_s_correlative, -8);
        return $a_data_document;
    }

    /**
     * Consultar el estado de COMPROBANTE DE PAGO ELECTRONICO (CPE)
     * @param string $p_s_operation: `consultar_comprobante` o `consultar_anulacion`
     * @param string $s_sunat_code: parametro para obtener `tipo_de_comprobante`
     * @param string $p_s_serie: `serie`, ejemplo: "BBB1"
     * @param string $p_s_correlative: `numero`, ejemplo: "1"
     * @return array $o_response: respuesta decodificada de la API
     */
    public static function checkCPE($p_s_ruta, $p_s_token, $p_s_operation, $s_sunat_code, $p_s_serie, $p_s_correlative) {
        $a_data_document = self::getArrayToCheckDocuments($p_s_operation, $s_sunat_code, $p_s_serie, $p_s_correlative);
        $json_response = self::sendDocument($p_s_ruta, $p_s_token, $a_data_document);
        $a_response = json_decode($json_response, true);
        return $a_response;
    }

    /**
     * Envio de datos a las diferentes END POINTS de la API de Nubefact
     * @param string $p_s_ruta: URL para el consumo de la API
     * @param string $p_s_token: TOKEN de acceso a la API
     * @param array $p_a_data_document: los datos que se requieren segun los END POINTS
     * @return string (json) $response: respuesta, en formato json, de la API
     */
    public static function sendDocument($p_s_ruta, $p_s_token, $p_a_data_document) {
        $nubefact = new Nubefact($p_s_ruta, $p_s_token);
        $json_response = $nubefact->sendDocument($p_a_data_document);
        return $json_response;
    }

    /**
     * downloadElectronicInvoicingFiles: Descargar archivos de la facturación electrónica.
     * Ésta función se encarga de descargar archivos de enlaces provenientes de APIs de facturación electrónica, 
     * como por ejemplo: Nubefact.
     * @param array $a_flashes Array que guarda mensajes para la vista
     * @param int $movement_id Id del movimiento
     */
    public static function downloadElectronicInvoicingFiles($a_flashes, $o_movement) {
        // Validar si la OSE es `NUBEFACT`
        if (Yii::app()->params['e_billing_ose'] !== YII_OSE_NUBEFACT) {
            return;
        }

        $o_sent_movement = SentMovement::model()->findByAttributes([
            'movement_id' => $o_movement->movement_id,
            'last' => 1
        ]);

        if (count($o_sent_movement) > 0) {
            $xml_url = isset($o_sent_movement['xml_link']) ? $o_sent_movement['xml_link'] : "";
            $cdr_url = isset($o_sent_movement['cdr_link']) ? $o_sent_movement['cdr_link'] : "";
            $pdf_url = isset($o_sent_movement['pdf_link']) ? $o_sent_movement['pdf_link'] : "";
            $basename = isset($o_sent_movement['basename']) ? $o_sent_movement['basename'] : "";

            $new_basename = "";
            $is_basename_changed = false;
            $xml_path = ""; // xml
            $pdf_path = empty($pdf_url) ? "" : SIANFE::getPdfDir(); // pdf
            $cdr_path = ""; // cdr

            switch ($o_sent_movement['type']) {
                case SentMovement::TYPE_INVOICE:
                    $xml_path = empty($xml_url) ? "" : SIANFE::getInvoiceDir();
                    $cdr_path = empty($cdr_url) ? "" : SIANFE::getRInvoiceDir();
                    break;
                case SentMovement::TYPE_SUMMARY:
                    $xml_path = empty($xml_url) ? "" : SIANFE::getSummaryDir();
                    $cdr_path = empty($cdr_url) ? "" : SIANFE::getRSummaryDir();
                    break;
                case SentMovement::TYPE_SUMMARY_LOW:
                    $xml_path = empty($xml_url) ? "" : SIANFE::getSummaryLowDir();
                    $pdf_path = empty($pdf_url) ? "" : SIANFE::getSummaryLowDir();
                    $cdr_path = empty($cdr_url) ? "" : SIANFE::getRSummaryLowDir();
                    break;
                case SentMovement::TYPE_REMISSION_GUIDE:
                    $xml_path = empty($xml_url) ? "" : SIANFE::getRemissionGuideDir();
                    $pdf_path = empty($pdf_url) ? "" : SIANFE::getRemissionGuideDir();
                    $cdr_path = empty($cdr_url) ? "" : SIANFE::getRRemissionGuideDir();
                    break;
                default:
                    $a_flash = [
                        'message' => "<p>El archivo de tipo `" . $o_sent_movement['type'] . "` no existe.</p>",
                        'message_type' => 'warning'
                    ];
                    $a_flashes[$a_flash['message_type']][] = $a_flash['message'];
                    break;
            }

            if (!empty($xml_url)) {
                $storage_path = $xml_path . $basename . SIANFE::XML_EXTENSION;
                $response = SIANFile::download($xml_url, $storage_path);
                if ($response === false) {
                    $a_flash = [
                        'message' => "<p>No se pudo descargar el archivo xml.</p>",
                        'message_type' => 'warning'
                    ];
                    $a_flashes[$a_flash['message_type']][] = $a_flash['message'];
                }

                // Validamos si es un compobrante de `COMUNICACIÓN DE BAJA`, para cambiar el nombre
                if ($o_sent_movement['type'] === SentMovement::TYPE_SUMMARY_LOW) {
                    // Cargando el archivo XML
                    $xml = simplexml_load_file($storage_path);
                    // Capturando el valor de <cbc:ID>
                    $id_value = (string) $xml->xpath('//cbc:ID')[0];
                    $new_basename = Yii::app()->controller->getOrganization()->person->identification_number . "-" . $id_value;
                    if (rename($storage_path, $xml_path . $new_basename . SIANFE::XML_EXTENSION) === false) {
                        $a_flash = [
                            'message' => "<p>Ha habido un error al intentar renombrar el archivo XML.</p>",
                            'message_type' => 'warning'
                        ];
                        $a_flashes[$a_flash['message_type']][] = $a_flash['message'];
                    } else {
                        $is_basename_changed = true;
                    }
                }
            }

            if (!empty($cdr_url)) {
                // Validamos si es un compobrante de `COMUNICACIÓN DE BAJA`, para cambiar el nombre
                $cdr_basename = $o_sent_movement['type'] === SentMovement::TYPE_SUMMARY_LOW ? $new_basename : $basename;
                $cdr_storage_path = $cdr_path . SIANFE::RESPONSE_PREFFIX . $cdr_basename . SIANFE::XML_EXTENSION;
                $response = SIANFile::download($cdr_url, $cdr_storage_path);
                if (!$response) {
                    $s_message = "<p>No se pudo descargar el archivo cdr.</p>";
                    $a_flash = [
                        'message' => $s_message,
                        'message_type' => 'warning'
                    ];
                    $a_flashes[$a_flash['message_type']][] = $a_flash['message'];
                }
            }

            if (!empty($pdf_url)) {
                // Validamos si es un compobrante de `COMUNICACIÓN DE BAJA`, para cambiar el nombre
                $pdf_basename = $o_sent_movement['type'] === SentMovement::TYPE_SUMMARY_LOW ? $new_basename : $basename;
                $pdf_storage_path = $pdf_path . $pdf_basename . SIANFE::PDF_EXTENSION;
                $response = SIANFile::download($pdf_url, $pdf_storage_path);
                if (!$response) {
                    $s_message = "<p>No se pudo descargar el archivo pdf.</p>";
                    $a_flash = [
                        'message' => $s_message,
                        'message_type' => 'warning'
                    ];
                    $a_flashes[$a_flash['message_type']][] = $a_flash['message'];
                }
            }

            if (!empty($new_basename) && $is_basename_changed) {
                //Actualizamos el `basename` de `SentMovement`
                $o_sent_movement->basename = $new_basename;
                $o_sent_movement->update(['basename']);
            }
        }
    }

    public static function getTicketHeader($p_o_commercial_movement, $p_o_document, $p_o_movement_parent = null) {
        $a_header = [];
        $a_header['sunat_code'] = $p_o_document->sunat_code;
        $a_header['document_serie'] = $p_o_commercial_movement->movement->document_serie;
        $a_header['document_correlative'] = substr($p_o_commercial_movement->movement->document_correlative, -8);
        $a_header['detraction'] = $p_o_commercial_movement->detraction;
        // Traer al Cliente
        $o_aux_person = Person::model()->findByPk($p_o_commercial_movement->movement->aux_person_id);
        $a_header['identification_type'] = $o_aux_person->identification_type;
        $a_header['identification_number'] = $o_aux_person->identification_number;
        $a_header['aux_person_name'] = $o_aux_person->person_name; // "REPLACE(XP.person_name, ',', ' ') AS aux_person_name",
        // Traer direccion del cliente
        $o_address = Address::model()->findByAttributes([
            'owner' => Person::OWNER,
            'owner_id' => $o_aux_person->person_id,
            'order' => 1,
        ]);
        $a_header['aux_person_address'] = isset($o_address->address) ? $o_address->address : '-';
        // Traer email del cliente
        $o_email = Email::model()->findByAttributes([
            'owner' => Person::OWNER,
            'owner_id' => $o_aux_person->person_id,
            'order' => 1,
        ]);

        $a_header['aux_person_email'] = is_null($o_email) ? '' : $o_email->email_address;
        //
        $a_header['emission_date'] = $p_o_commercial_movement->movement->emission_date;
        $a_header['expiration_date'] = $p_o_commercial_movement->movement->expiration_date;
        $a_header['currency'] = $p_o_commercial_movement->movement->currency;
        $a_header['exchange_rate'] = $p_o_commercial_movement->movement->exchange_rate;
        $a_header['igv_percent'] = $p_o_commercial_movement->igv_percent * 100;
        $a_header['affected'] = round($p_o_commercial_movement->movement->currency == Currency::PEN ? $p_o_commercial_movement->affected_pen : $p_o_commercial_movement->affected_usd, 2);
        $a_header['inaffected'] = round($p_o_commercial_movement->movement->currency == Currency::PEN ? $p_o_commercial_movement->inaffected_pen : $p_o_commercial_movement->inaffected_usd, 2);
        $a_header['nobill'] = round($p_o_commercial_movement->movement->currency == Currency::PEN ? $p_o_commercial_movement->nobill_pen : $p_o_commercial_movement->nobill_usd, 2);
        $a_header['igv'] = round($p_o_commercial_movement->movement->currency == Currency::PEN ? $p_o_commercial_movement->igv_pen : $p_o_commercial_movement->igv_usd, 2);
        $a_header['free'] = round($p_o_commercial_movement->movement->currency == Currency::PEN ? $p_o_commercial_movement->free_pen : $p_o_commercial_movement->free_usd, 2);
        $a_header['isc'] = round($p_o_commercial_movement->movement->currency == Currency::PEN ? $p_o_commercial_movement->isc_pen : $p_o_commercial_movement->isc_usd, 2);
        $a_header['total'] = round($p_o_commercial_movement->movement->currency == Currency::PEN ? $p_o_commercial_movement->total_pen : $p_o_commercial_movement->total_usd, 2);
        $a_header['perception'] = round($p_o_commercial_movement->movement->currency == Currency::PEN ? $p_o_commercial_movement->perception_pen : $p_o_commercial_movement->perception_usd, 2);
        $a_header['real'] = round($p_o_commercial_movement->movement->currency == Currency::PEN ? $p_o_commercial_movement->real_pen : $p_o_commercial_movement->real_usd, 2);
        $a_header['observation'] = $p_o_commercial_movement->movement->observation;

        $a_header['condition'] = $p_o_commercial_movement->condition;
        $a_header['payment_method'] = $p_o_commercial_movement->payment_method;
        if ($p_o_commercial_movement->condition === Condition::CREDIT) {
            $a_header['credit_days'] = $p_o_commercial_movement->credit_days;
        }

        // Verifico si exite un movimiento padre
        if (count($p_o_movement_parent) > 0) {
            $o_document_parent = Document::model()->findByAttributes([
                'document_code' => $p_o_movement_parent->document_code
            ]);

            $a_header['parent_sunat_code'] = $o_document_parent->sunat_code;
            $a_header['parent_document_serie'] = $p_o_movement_parent->document_serie;
            $a_header['parent_document_correlative'] = substr($p_o_movement_parent->document_correlative, -8);
            $a_header['reason_code'] = $p_o_commercial_movement->changelog->reason_code;
        }

        $a_header['items'] = [];

        return $a_header;
    }

    public static function getTicketDetail($p_a_header, $a_items = []) {
        if (count($a_items) > 0) {
            $item_header = [];
            foreach ($a_items as $i => $o_item) {
                $item_header[$i]['currency'] = $p_a_header['currency'];
                $item_header[$i]['igv_percent'] = Yii::app()->controller->getOrganization()->globalVar->igv * 100;

                $item_header[$i]['equivalence'] = $o_item->itemObj->equivalence; // obtener
                $o_presentation = Presentation::model()->with('multitable2')->findByAttributes([
                    'product_id' => $o_item->itemObj->product_id,
                    'equivalence' => $o_item->itemObj->equivalence
                ]);

                if (isset($o_presentation->multitable2)) {
                    $item_header[$i]['sunat_code'] = $o_presentation->multitable2->value;
                } else {
                    $item_header[$i]['sunat_code'] = Presentation::DEFAULT_SERVICE_CODE;
                }

                $item_header[$i]['item_number'] = $o_item->itemObj->item_number;
                $item_header[$i]['product_id'] = $o_item->itemObj->product_id;
                $item_header[$i]['product_name'] = $o_item->itemObj->product_name;
                $item_header[$i]['product_type'] = $o_item->itemObj->product_type;
                $item_header[$i]['pres_quantity'] = $o_item->itemObj->pres_quantity;
                $item_header[$i]['unit_quantity'] = $o_item->itemObj->unit_quantity;
                $item_header[$i]['unit_balance'] = $o_item->itemObj->unit_balance;
                $item_header[$i]['total_usd'] = $o_item->itemObj->total_usd;
                $item_header[$i]['total_pen'] = $o_item->itemObj->total_pen;
                $item_header[$i]['owner'] = $o_item->itemObj->owner;
                $item_header[$i]['movement_id'] = $o_item->itemObj->movement_id;
                $item_header[$i]['item_type_id'] = $o_item->itemObj->item_type_id;
                $item_header[$i]['item_id'] = $o_item->itemObj->item_id;
                $item_header[$i]['balance_usd'] = $o_item->itemObj->balance_usd;
                $item_header[$i]['balance_pen'] = $o_item->itemObj->balance_pen;
                $item_header[$i]['allow_decimals'] = $o_item->itemObj->allow_decimals;
                //
                $item_header[$i]['round_mode'] = $o_item['round_mode'];
                $item_header[$i]['perception_affected'] = $o_item['perception_affected'];
                $item_header[$i]['movement_id'] = $o_item['movement_id'];
                $item_header[$i]['item_id'] = $o_item['item_id'];
                $item_header[$i]['is_locked'] = $o_item['is_locked'];
                $item_header[$i]['include_igv'] = $o_item['include_igv'];
                $item_header[$i]['igv_affection'] = $o_item['igv_affection'];
                $item_header[$i]['free_transfer'] = $o_item['free_transfer'];
                $item_header[$i]['force_igv'] = $o_item['force_igv'];
                $item_header[$i]['expiration'] = $o_item['expiration'];
                $item_header[$i]['as_bill'] = $o_item['as_bill'];

                if ($p_a_header['currency'] === Currency::PEN) {
                    $item_header[$i]['vprice'] = $o_item['vprice_pen'];
                    $item_header[$i]['total'] = $o_item['total_pen'];
                    $item_header[$i]['sprice'] = $o_item['sprice_pen'];
                    $item_header[$i]['rprice'] = $o_item['rprice_pen'];
                    $item_header[$i]['real'] = $o_item['real_pen'];
                    $item_header[$i]['price'] = $o_item['price_pen'];
                    $item_header[$i]['perception'] = $o_item['perception_pen'];
                    $item_header[$i]['ntotal'] = $o_item['ntotal_pen'];
                    $item_header[$i]['nreal'] = $o_item['nreal_pen'];
                    $item_header[$i]['nobill'] = $o_item['nobill_pen'];
                    $item_header[$i]['nnet'] = $o_item['nnet_pen'];
                    $item_header[$i]['nigv'] = $o_item['nigv_pen'];
                    $item_header[$i]['net'] = $o_item['net_pen'];
                    $item_header[$i]['inaffected'] = $o_item['inaffected_pen'];
                    $item_header[$i]['igv'] = $o_item['igv_pen'];
                    $item_header[$i]['ftotal'] = $o_item['ftotal_pen'];
                    $item_header[$i]['free'] = $o_item['free_pen'];
                    $item_header[$i]['freal_pen'] = $o_item['freal_pen'];
                    $item_header[$i]['fnet_pen'] = $o_item['fnet_pen'];
                    $item_header[$i]['figv_pen'] = $o_item['figv_pen'];
                    $item_header[$i]['export'] = $o_item['export_pen'];
                    $item_header[$i]['discount_pen'] = $o_item['discount_pen'];
                    $item_header[$i]['crude_pen'] = $o_item['crude_pen'];
                    $item_header[$i]['cprice_pen'] = $o_item['cprice_pen'];
                    $item_header[$i]['cdiscount_pen'] = $o_item['cdiscount_pen'];
                    $item_header[$i]['affected'] = $o_item['affected_pen'];
                    $item_header[$i]['affected3_pen'] = $o_item['affected3_pen'];
                    $item_header[$i]['affected2_pen'] = $o_item['affected2_pen'];
                    $item_header[$i]['affected1_pen'] = $o_item['affected1_pen'];
                } else {
                    $item_header[$i]['vprice'] = $o_item['vprice_usd'];
                    $item_header[$i]['total'] = $o_item['total_usd'];
                    $item_header[$i]['sprice'] = $o_item['sprice_usd'];
                    $item_header[$i]['rprice'] = $o_item['rprice_usd'];
                    $item_header[$i]['real'] = $o_item['real_usd'];
                    $item_header[$i]['price_usd'] = $o_item['price_usd'];
                    $item_header[$i]['perception'] = $o_item['perception_usd'];
                    $item_header[$i]['ntotal'] = $o_item['ntotal_usd'];
                    $item_header[$i]['nreal_usd'] = $o_item['nreal_usd'];
                    $item_header[$i]['nobill'] = $o_item['nobill_usd'];
                    $item_header[$i]['nnet'] = $o_item['nnet_usd'];
                    $item_header[$i]['nigv_usd'] = $o_item['nigv_usd'];
                    $item_header[$i]['net'] = $o_item['net_usd'];
                    $item_header[$i]['inaffected'] = $o_item['inaffected_usd'];
                    $item_header[$i]['igv'] = $o_item['igv_usd'];
                    $item_header[$i]['ftotal'] = $o_item['ftotal_usd'];
                    $item_header[$i]['free'] = $o_item['free_usd'];
                    $item_header[$i]['freal_usd'] = $o_item['freal_usd'];
                    $item_header[$i]['fnet_usd'] = $o_item['fnet_usd'];
                    $item_header[$i]['figv_usd'] = $o_item['figv_usd'];
                    $item_header[$i]['export'] = $o_item['export_usd'];
                    $item_header[$i]['discount_usd'] = $o_item['discount_usd'];
                    $item_header[$i]['crude_usd'] = $o_item['crude_usd'];
                    $item_header[$i]['cprice_usd'] = $o_item['cprice_usd'];
                    $item_header[$i]['cdiscount_usd'] = $o_item['cdiscount_usd'];
                    $item_header[$i]['affected'] = $o_item['affected_usd'];
                    $item_header[$i]['affected3_usd'] = $o_item['affected3_usd'];
                    $item_header[$i]['affected2_usd'] = $o_item['affected2_usd'];
                    $item_header[$i]['affected1_usd'] = $o_item['affected1_usd'];
                }
            }
            return $item_header;
        } else {
            return [
                [
                    'item_number' => 1,
                    'product_id' => 0,
                    'product_name' => $p_a_header['operation_name'],
                    'product_type' => Product::TYPE_GROUP,
                    'pres_quantity' => 1,
                    'currency' => $p_a_header['currency'],
                    'vprice' => $p_a_header['net'],
                    'sprice' => $p_a_header['total'],
                    'rprice' => $p_a_header['nnet'],
                    'affected' => $p_a_header['affected'],
                    'inaffected' => $p_a_header['inaffected'],
                    'nobill' => $p_a_header['nobill'],
                    'export' => $p_a_header['export'],
                    'free' => $p_a_header['free'],
                    'net' => $p_a_header['net'],
                    'igv' => $p_a_header['igv'],
                    'total' => $p_a_header['total'],
                    'perception' => $p_a_header['perception'],
                    'real' => $p_a_header['real'],
                    'sunat_code' => Presentation::DEFAULT_SERVICE_CODE,
                    'igv_percent' => Yii::app()->controller->getOrganization()->globalVar->igv * 100,
                    'igv_affection' => $p_a_header['inaffected'] > 0 ? CommercialMovementProduct::IGV_AFFECTION_AFFECTED : CommercialMovementProduct::IGV_AFFECTION_INAFFECTED
                ]
            ];
        }
    }

    /**
     * Trae el estado de SentMovement
     * @param array $p_a_response_api: Respuesta de NubeFact
     * @return string $status: columna `status` de la tabla `sent_movement`
     */
    public static function getStatusSentMovement($p_a_response_api) {
        if (isset($p_a_response_api['anulado']) && $p_a_response_api['anulado'] === true) {
            return SentMovement::STATUS_ANNULLED;
        } else {
            if ($p_a_response_api['aceptada_por_sunat']) {
                return SentMovement::STATUS_ACCEPTED;
            } else {
                $status = is_null($p_a_response_api['sunat_responsecode']) ? SentMovement::STATUS_PENDING : SentMovement::STATUS_REJECTED;
                return $status;
            }
        }
    }

    public static function getBasenameSentMovement($p_o_movement, $p_o_document) {
        // return: RUC-codigo doc - serie- correlativo
        $document = $p_o_movement->document_serie . "-" . $p_o_movement->document_correlative;
        $basename = Yii::app()->controller->getOrganization()->person->identification_number . "-{$p_o_document->sunat_code}-{$document}";
        return $basename;
    }

    public static function getOrderSentMovement($p_i_movement_id) {
        $order = 1;
        $o_sent_movement = SentMovement::model()->findByAttributes([
            'movement_id' => $p_i_movement_id,
            'last' => 1,
        ]);

        if (count($o_sent_movement) > 0) {
            $order = $o_sent_movement->order + 1;
        }

        return $order;
    }

    public static function getCodeSentMovement($p_o_movement, $p_s_sent_movement_status, $p_s_sent_movement_type) {
        $movement_status = $p_o_movement->status;
        $movement_sunat_sent = $p_o_movement->sunat_sent;
        $sent_movement_code = '';

        switch ($p_s_sent_movement_status) {
            case SentMovement::STATUS_NOT_SENDED: // const STATUS_NOT_SENDED = -1;  
                break;
            case SentMovement::STATUS_REJECTED: // STATUS_REJECTED = 0;
                if ($p_s_sent_movement_type === SentMovement::TYPE_INVOICE ||
                        $p_s_sent_movement_type === SentMovement::TYPE_SUMMARY ||
                        $p_s_sent_movement_type === SentMovement::TYPE_REMISSION_GUIDE) {
                    if ($movement_status == 1 && $movement_sunat_sent == 1) {
                        $sent_movement_code = SentMovement::CODE_7;
                    }
                }
                if ($p_s_sent_movement_type === SentMovement::TYPE_INVOICE) {
                    if ($movement_status == 0 && $movement_sunat_sent == 1) {
                        $sent_movement_code = SentMovement::CODE_0;
                    }
                }

                if ($p_s_sent_movement_type === SentMovement::TYPE_SUMMARY_LOW) {
                    if ($movement_status == 0 && $movement_sunat_sent == 1) {
                        $sent_movement_code = SentMovement::CODE_2;
                    }
                }
                break;
            case SentMovement::STATUS_ACCEPTED: // STATUS_ACCEPTED = 1;
                if ($p_s_sent_movement_type === SentMovement::TYPE_INVOICE ||
                        $p_s_sent_movement_type === SentMovement::TYPE_SUMMARY ||
                        $p_s_sent_movement_type === SentMovement::TYPE_REMISSION_GUIDE) {
                    if ($movement_status == 1 && $movement_sunat_sent == 1) {
                        $sent_movement_code = SentMovement::CODE_8;
                    }
                }
                if ($p_s_sent_movement_type === SentMovement::TYPE_SUMMARY_LOW) {
                    if ($movement_status == 0 && $movement_sunat_sent == 1) {
                        $sent_movement_code = SentMovement::CODE_3;
                    }
                }
                break;
            case SentMovement::STATUS_CONNECTION_ERROR: // STATUS_CONNECTION_ERROR = 2;
                if ($p_s_sent_movement_type === SentMovement::TYPE_INVOICE ||
                        $p_s_sent_movement_type === SentMovement::TYPE_SUMMARY ||
                        $p_s_sent_movement_type === SentMovement::TYPE_REMISSION_GUIDE) {
                    if ($movement_status == 1 && $movement_sunat_sent == 0) {
                        $sent_movement_code = SentMovement::CODE_9;
                    }
                }
                if ($p_s_sent_movement_type === SentMovement::TYPE_SUMMARY_LOW) {
                    if ($movement_status == 0 && $movement_sunat_sent == 0) {
                        $sent_movement_code = SentMovement::CODE_4;
                    }
                }
                break;
            case SentMovement::STATUS_PENDING: // STATUS_PENDING = 3;
                if ($p_s_sent_movement_type === SentMovement::TYPE_SUMMARY || $p_s_sent_movement_type === SentMovement::TYPE_REMISSION_GUIDE) {
                    if ($movement_status == 1 && $movement_sunat_sent == 0) {
                        $sent_movement_code = SentMovement::CODE_10;
                    }
                }
                if ($p_s_sent_movement_type === SentMovement::TYPE_SUMMARY_LOW) {
                    if ($movement_status == 0 && $movement_sunat_sent == 0) {
                        $sent_movement_code = SentMovement::CODE_5;
                    }
                }
                break;
            case SentMovement::STATUS_ANNULLED: // const STATUS_ANNULLED = 4;
                break;
            default:
                throw new Exception("Error: estado no existe {$p_s_sent_movement_status}");
                break;
        }

        return $sent_movement_code;
    }

    /**
     * ATRIBUTO: tipo_de_comprobante
     * VALOR: Tipo de COMPROBANTE que desea generar: 1 = FACTURA, 2 = BOLETA, 3 = NOTA DE CRÉDITO, 4 = NOTA DE DÉBITO
     * TIPO DE DATO: Integer
     * REQUISITO: Obligatorio
     * LONGITUD: 1 exacto
     */
    public static function getVoucherType($p_s_sunat_code) {
        $s_sunat_code = isset($p_s_sunat_code) ? $p_s_sunat_code : "";
        if ($s_sunat_code === "") {
            throw new Exception("El Tipo de Comprobante es obligatorio");
        }

        switch ($s_sunat_code) {
            case Document::FACTURA:
                return 1;
                break;
            case Document::BOLETA:
                return 2;
                break;
            case Document::CREDIT_NOTE:
            case Document::SPECIAL_CREDIT_NOTE:
            case Document::NOT_DOMICILED_CREDIT_NOTE:
                $tipo_de_comprobante = 3;
                break;
            case Document::DEBIT_NOTE:
            case Document::SPECIAL_DEBIT_NOTE:
            case Document::NOT_DOMICILED_DEBIT_NOTE:
                return 4;
                break;
            default:
                throw new Exception("No se puede declarar Tipo de documento con el código: '{$s_sunat_code}'");
                return null;
        }
    }

    public static function getFormattedDate($p_s_date, $p_s_name_date = "fecha") {
        if (!preg_match('/[0-9]{2}\-[0-9]{2}\-[0-9]{4}/i', $p_s_date)) {
            $s_date_formated = strtr($p_s_date, '/', '-');
            $newDate = date("d-m-Y", strtotime($s_date_formated));

            if (!preg_match('/[0-9]{2}\-[0-9]{2}\-[0-9]{4}/i', $newDate)) {
                throw new \Exception("El formato de {$p_s_name_date} debe de ser DD-MM-AAAA.");
            }
            return $newDate;
        }
    }

}
