<?php

/**
 * Controller is the customized base controller class.
 * All controller classes for this application should extend from this base class.
 */
class SIANController extends USController {

    public $modal_id = "";
    public $module_title;
    public $navbar = "";
    public $modelClass;
    public $viewClass;
    public $viewScenario = 'search';
    public $defaultCss = null;
    public $defaultJS = null;
    public $default_values_changed = false;
    public $currentNonAjaxUrl = null;
    public $items_menu = [];
    //
    public $layout = '//layouts/main';
    public $login_route = 'site/login';
    public $toolbar = "";
    private $geoloc = null;
    //Access
    private $urlActions;
    private $checkedActions = [];
    private $urlActionsLoaded = false;
    //Objects
    private $business_unit_ids = null;
    private $store_ids = null;
    private $xwarehouse_ids;
    private $xcashboxes_ids;
    private $division_ids = null;
    private $checkedWarehouses = [];
    private $checkedCashboxes = [];
    private $loadedWarehouses = false;
    private $loadedCashboxes = false;
    //Objects confirmation
    private $confirmations;
    private $checkedConfirmations = [];
    private $confirmationsLoaded = false;

    //    private $checkLog = "";
    //Formatos de impresion 
    const F_ORDEN_VENTA = 'ORDEN_VENTA';
    const F_COTIZACION_VENTA = 'COTIZACION_VENTA';
    const F_BOLETA = 'BOLETA';
    const F_BOLETA_02 = 'BOLETA_02';
    const F_BOLETA_10 = 'BOLETA_10';
    const F_FACTURA = 'FACTURA';
    const F_FELECTRONICA = 'FELECTRONICA';
    const F_FACTURA_08 = 'FACTURA_08';
    const F_FACTURA_03 = 'FACTURA_03';
    const F_FACTURA_01 = 'FACTURA_01';
    const F_FACTURA_09 = 'FACTURA_09';
    const F_FACTURA_10 = 'FACTURA_10';
    const F_NOTA_CREDITO_01 = 'NOTA_CREDITO_01';
    const F_NOTA_DEBITO = 'NOTA_DEBITO';
    const F_GUIA_REMISION = 'GUIA_REMISION';
    const F_GUIA_REMISION_TRANSPORTISTA = 'GUIA_REMISION_TRANS';
    const F_GUIA_REMISION_01 = 'GUIA_REMISION_01';
    const F_ORDEN_COMPRA = 'ORDEN_COMPRA';
    const F_ORDEN_SERVICIO = 'ORDEN_SERVICIO';
    const F_GASTOS_MOVILIDAD = 'GASTOS_MOVILIDAD';
    const F_INGRESO_ALMACEN = 'INGRESO_ALMACEN';
    const F_SALIDA_ALMACEN = 'SALIDA_ALMACEN';
    const F_ORDEN_CONSUMO = 'ORDEN_CONSUMO';
    const F_ORDEN_CONSUMO_OBRA = 'ORDEN_CONSUMO_OBRA';
    const F_ORDEN_CONSIGNACION = 'ORDEN_CONSIGNACION';
    const F_PEDIDO_COMPRA = 'PEDIDO_COMPRA';
    const F_ORDEN_TRANSFORMACION = 'ORDEN_TRANSFORMACION';
    const F_DESPACHO = 'DESPACHO';
    const F_DESPACHO_PROYECTO = 'DESPACHO_PROYECTO';
    const F_INGRESO_CAJA = 'INGRESO_CAJA';
    const F_SALIDA_CAJA = 'SALIDA_CAJA';
    const F_INGRESO_MASIVO_CAJA = 'INGRESO_MASIVO_CAJA';
    const F_SALIDA_MASIVA_CAJA = 'SALIDA_MASIVA_CAJA';
    const F_INGRESO_COMPRA = 'INGRESO_COMPRA';
    const F_RECIBO_INGRESO = 'RECIBO_INGRESO';
    const F_VOUCHER_CONTABLE = 'VOUCHER_CONTABLE';
    const F_PAYROLL = 'PAYROLL';
    const F_ORDEN_DESPACHO = 'ORDEN_DESPACHO';
    const F_ORDEN_SALIDA_DINERO = 'ORDEN_SALIDA_DINERO';
    const F_COMPROBANTE_RETENCION = 'COMPROBANTE_RETENCION';

    public function runActionWithFilters($action, $filters) {
        if (Yii::app()->user->hasState('checkedActions')) {
            $this->checkedActions = Yii::app()->user->getState('checkedActions');
        }
        if (Yii::app()->user->hasState('checkedWarehouses')) {
            $this->checkedWarehouses = Yii::app()->user->getState('checkedWarehouses');
        }
        if (Yii::app()->user->hasState('checkedCashboxes')) {
            $this->checkedCashboxes = Yii::app()->user->getState('checkedCashboxes');
        }
        if (Yii::app()->user->hasState('checkedConfirmations')) {
            $this->checkedConfirmations = Yii::app()->user->getState('checkedConfirmations');
        }

        //Si la petición no es ajax
        if (!Yii::app()->request->isAjaxRequest) {
            $this->currentNonAjaxUrl = Yii::app()->request->url;
        }

        return parent::runActionWithFilters($action, $filters);
    }

    public function accessRules() {
        return array(
            array('allow',
                'controllers' => array('site'),
                'actions' => array('error'),
            ),
        );
    }

    protected function beforeAction($action) {

        if (isset($this->module)) {
            if (!in_array($this->action->id, ['loadWildcardData'])) {
                $a_module_menus = SpGetMenu::getFormat($this->module->id);
                $this->items_menu = $a_module_menus['menus'];
                $this->module_title = $a_module_menus['module']; // 'Principal';
            }
        } else {
            $this->module_title = 'Principal';
        }
        if (Yii::app()->user->isGuest && $this->route != $this->login_route) {
            $this->redirect(array($this->login_route));
        }

        return parent::beforeAction($action);
    }

    protected function afterAction($action) {
        if ($this->urlActionsLoaded) {
            Yii::app()->user->setState('checkedActions', $this->checkedActions);
        }
        if ($this->loadedWarehouses) {
            Yii::app()->user->setState('checkedWarehouses', $this->checkedWarehouses);
        }
        if ($this->loadedCashboxes) {
            Yii::app()->user->setState('checkedCashboxes', $this->checkedCashboxes);
        }
        if ($this->confirmationsLoaded) {
            Yii::app()->user->setState('checkedConfirmations', $this->checkedConfirmations);
        }
//        SIANFile::writeString($this->checkLog, $this->route);
        return parent::afterAction($action);
    }

    protected function beforeRender($view) {
        $this->pageTitle = $this->title . " | " . Yii::app()->name;
        return parent::beforeRender($view);
    }

    protected function performAjaxValidation(array $models, $form_id = null) {
        if (isset($_POST ['ajax']) && $_POST ['ajax'] === $form_id) {
            echo CActiveForm::validate($models);
            Yii::app()->end();
        }
    }

    /**
     * Declares class-based actions.
     */
    public function actions() {
        return array(
// captcha action renders the CAPTCHA image displayed on the contact page
            'captcha' => array(
                'class' => 'CCaptchaAction',
                'backColor' => 0xFFFFFF
            ),
            // page action renders "static" pages stored under 'protected/views/site/pages'
// They can be accessed via: index.php?r=site/page&view=FileName
            'page' => array(
                'class' => 'CViewAction'
            )
        );
    }

    /**
     * This is the action to handle external exceptions.
     */
    public function actionError() {
//        echo json_encode(Yii::app()->errorHandler->error);
        $this->layout = '//layouts/index';
        if ($error = Yii::app()->errorHandler->error) {
            //Excepciones que no deben generar log
            if (isset($error['type']) && $error['type'] === 'CHttpException') {

                $s_detail = 'Posiblemente la ruta está mal escrita o usted no tiene permiso para realizar esta acción.';

                if (Yii::app()->request->isAjaxRequest) {
                    echo "<h4>{$error['message']}</h4><p>{$s_detail}</p>";
                } else {
                    //Limpiamos todos los scripts
                    Yii::app()->clientScript->reset();
                    //
                    $this->render('chttp_log', [
                        'error' => $error,
                        'detail' => $s_detail,
                    ]);
                }
            } else {
                $s_log_code = USLog::save($error);

                if (Yii::app()->request->isAjaxRequest) {
                    echo "<h4>" . Strings::ERROR_MESSAGE . "</h4><p>" . Strings::ERROR_LOG . $s_log_code . "</p>" . $this->renderPartial('/site/_log', ['log' => $s_log_code], true);
                } else {
                    //Limpiamos todos los scripts
                    Yii::app()->clientScript->reset();
                    //
                    $this->render('log', [
                        'error' => $error,
                        'log' => $s_log_code,
                    ]);
                }
            }
        }
    }

    public function actionSendLog() {

        $s_log = $_POST['log'];
        if (empty($s_log)) {
            throw new Exception('Archivo de log vacío!');
        }

        $s_name = $_POST['name'];
        if (empty($s_name)) {
            throw new Exception('Nombre vacío!');
        }

        $s_email = $_POST['email'];
        if (empty($s_email)) {
            throw new Exception('Email vacío!');
        }

        $a_log = null;
        if (Yii::app()->params['mongo_enabled']) {
            $a_log = Yii::app()->mongo->findOne('errorlog', [
                'skin' => Yii::app()->params['skin'],
                'server_name' => Util::getServerName(),
                'log_code' => $s_log,
            ]);
        }


        if (isset($a_log)) {
            //No se requiere un formato visualmente agradable
            $s_message = "<pre>" . json_encode($a_log, JSON_PRETTY_PRINT) . "</pre>";

            if (EmailQueue::push($s_email, $s_name, $s_log, $s_message, [Yii::app()->params["supportEmail"]], [], EmailQueue::TYPE_SUPPORT)) {
                echo CJSON::encode(array(
                    'type' => 'message',
                    'element_id' => 'success',
                    'content_html' => 'Correo enviado a ' . Yii::app()->params["supportEmail"],
                ));
            } else {
                echo CJSON::encode(array(
                    'type' => 'message',
                    'element_id' => 'error',
                    'content_html' => 'No se pudo enviar el correo a ' . Yii::app()->params["supportEmail"],
                ));
            }
        } else {
            throw new Exception('No se encuentra el archivo de log: ' . $s_log . '. Probablemente ya pasó el día.');
        }
    }

    public function actionMultifrontend($value) {
        $this->multiboolean($_POST ['selection'], 'frontend', $value);
    }

    public function actionMultioutstanding($value) {
        $this->multiboolean($_POST ['selection'], 'outstanding', $value);
    }

    public function actionMultistatus($value) {
        $this->multiboolean($_POST['selection'], 'status', $value);
    }

    public function multiboolean($p_a_pks, $p_s_field, $p_i_value) {

        $model = new $this->modelClass;

        try {
            //Iniciamos la transacción
            $this->beginTransaction($model);
            ///Ejecutamos   
            $model->updateBoolean($p_a_pks, $p_s_field, $p_i_value);
            //Chequeamos si es SAFE
            if (is_subclass_of($model, 'SIANSafeModel')) {

                $a_attributes = $model->getRecursiveArray($p_s_field, [
                    'previous' => Strings::NOT_FOUND,
                    'current' => $p_i_value
                ]);

                $model->sameToAll($p_a_pks, Changelog::UPDATE, null, $a_attributes);
            }
            //Confirmamos la transacción
            $this->commitTransaction($model);

            echo CJSON::encode(array(
                'success' => 1,
            ));
        } catch (CDbException $ex) {
            $this->rollbackTransaction($model);

            if ($ex->getCode() !== Util::MYSQL_FAKE_ERROR) {
                USLog::save($ex);
            }

            echo CJSON::encode(array(
                'success' => 1,
                'message' => $ex->errorInfo[2],
                'message_type' => 'error'
            ));
        } catch (Exception $ex) {
            $this->rollbackTransaction($model);

            USLog::save($ex);

            echo CJSON::encode(array(
                'success' => 1,
                'message' => $ex->getMessage(),
                'message_type' => 'error'
            ));
        }
    }

    public function actionMultidelete() {

        //IDS
        $a_pks = $_POST ['selection'];
        //Si hay para eliminar
        if (count($a_pks) > 0) {
            //MODEL
            $o_model = new $this->modelClass;

            $s_pk = $o_model->getStringPk();

            $criteria = new CDbCriteria;
            $criteria->compare($s_pk, $a_pks);

            try {
                $this->beginTransaction($o_model);

                //Chequeamos si es SAFE (se llama antes para poder obtener los ID's)
                if (is_subclass_of($o_model, 'SIANSafeModel')) {
                    //Al borrar no es necesario especificar detalle de cambios
                    $o_model->sameToAll($a_pks, Changelog::DELETE);
                }
                //Llamamos a evento before
                $o_model->beforeMultidelete($a_pks);
                //Eliminamos
                $o_model->deleteAll($criteria);
                //Llamamos a evento after
                $o_model->afterMultidelete($a_pks);

                $this->commitTransaction($o_model);

                echo CJSON::encode([
                    'code' => USREST::CODE_SUCCESS,
                    'message_type' => 'success',
                    'message' => Strings::SUCCESS_OPERATION
                ]);
            } catch (Exception $ex) {
                $this->rollbackTransaction($o_model);

                switch ($ex->getCode()) {
                    case 23000 :
                        echo CJSON::encode([
                            'code' => USREST::CODE_FORBIDDEN,
                            'message_type' => 'warning',
                            'message' => "No se borró ningún registro porque al menos uno de ellos está siendo usado"
                        ]);
                        break;
                    default :
                        throw $ex;
                }
            }
        }
    }

    public function getGeoloc() {
        if (!isset($this->geoloc)) {

            $geoloc = SIANCache::get(SIANCache::GEOLOC);

            if ($geoloc === false) {
                $geoloc = Geoloc::getItems();
                SIANCache::add(SIANCache::GEOLOC, $geoloc);
            }

            $this->geoloc = $geoloc;
        }

        return $this->geoloc;
    }

    public function checkModule($module) {
        //Chequeamos el modulo
        if (isset($this->checkedActions[$module])) {
            return $this->checkedActions[$module];
        } else {
            return $this->loadModuleActions($module);
        }
    }

    public function checkRoute($route) {
        //Creamos la URL
        return $this->checkUrl($this->createUrl($route));
    }

    public function checkUrl($url) {
        //Si ya está en los ítems chequeados
        if (isset($this->checkedActions[$url])) {
            return $this->checkedActions[$url];
        } else {
            return $this->loadUrlActions($url);
        }
    }

    public function checkRoutes(array $routes) {
        $access = [];
        foreach ($routes as $route) {
            //Creamos la URL
            $access[$route] = $this->checkRoute($route);
        }
        return $access;
    }

    public function checkActionRoutes(array $actionRoutes) {
        $allRoutes = [];
        foreach ($actionRoutes as $action => $routes) {
            foreach ($routes as $route) {
                $allRoutes[] = "/{$route}/{$action}";
            }
        }

        return $this->checkRoutes(USArray::array_unique($allRoutes));
    }

    /**
     * Checkea si el usuario tiene permiso para las siguientes rutas
     * @param array $routes Rutas
     * @return string|bool URL en caso de existo o FALSE en caso de error
     */
    public function getUrls(array $routes) {
        $access = [];
        foreach ($routes as $route) {
            //Creamos la URL
            $access[$route] = $this->checkRoute($route) ? $this->createUrl($route) : false;
        }
        return $access;
    }

    public function checkCurrent() {

        //Verificamos el permiso
        $checked = $this->checkRoute("/{$this->route}");

        if ($checked && isset($this->module)) {
            //Actualizamos hit
            $sql = "UPDATE `action` SET hits = hits + 1 WHERE `module` = :module AND controller = :controller AND `action` = :action";
            Yii::app()->db->createCommand($sql)->execute([
                ':module' => $this->module->id,
                ':controller' => $this->id,
                ':action' => $this->action->id
            ]);
        }

        return $checked;
    }

    public function checkBusinessUnit($p_i_business_unit_id) {
        $a_business_unit_ids = $this->getBusinessUnitIds();
        return in_array((int) $p_i_business_unit_id, $a_business_unit_ids, true);
    }

    public function checkStore($p_i_store_id) {
        $a_store_ids = $this->getStoreIds();
        return in_array((int) $p_i_store_id, $a_store_ids, true);
    }

    public function checkWarehouse($p_i_warehouse_id) {
        //Si ya está en los ítems chequeados
        if (isset($this->checkedWarehouses[$p_i_warehouse_id])) {
            return $this->checkedWarehouses[$p_i_warehouse_id];
        } else {
            return $this->_loadWarehouses($p_i_warehouse_id);
        }
    }

    public function checkCashbox($p_i_cashbox_id) {
        //Si ya está en los ítems chequeados
        if (isset($this->checkedCashboxes[$p_i_cashbox_id])) {
            return $this->checkedCashboxes[$p_i_cashbox_id];
        } else {
            return $this->_loadCashboxes($p_i_cashbox_id);
        }
    }

    public function checkConfirmation($route, $option_type) {
        //Si ya está en los ítems chequeados
        if (isset($this->checkedConfirmations[$route][$option_type])) {
            return $this->checkedConfirmations[$route][$option_type];
        } else {
            return $this->loadConfirmations($route, $option_type);
        }
    }

    public function getConfirmationRoutes() {

        $confirmations = Yii::app()->user->getState('confirmations');

        $routes_type = [];
        foreach ($confirmations as $item) {
            $routes_type[] = $item['route'] . Confirmation::SEPARATOR . $item['option_type'];
        }
        return $routes_type;
    }

    public function getConfirmationScenarioIds($p_s_option_type) {

        $confirmations = Yii::app()->user->getState('confirmations');

        $a_scenario_ids = [];
        foreach ($confirmations as $item) {
            if ($item['option_type'] == $p_s_option_type) {
                $a_scenario_ids[] = $item['scenario_id'];
            }
        }
        return $a_scenario_ids;
    }

    public function checkConfirmations($route, array $option_type) {
        $access = [];
        foreach ($option_type as $option) {
            //Creamos la URL
            $access[$option] = $this->checkConfirmation($route, $option);
        }
        return $access;
    }

    private function _loadWarehouses($p_i_warehouse_id) {
        //Si no habia sido cargado lo cargamos
        if (!$this->loadedWarehouses) {
            if (Yii::app()->user->hasState('warehouse_ids')) {
                $this->xwarehouse_ids = Yii::app()->user->getState('warehouse_ids');
            } else {
                $this->redirectToLogin();
            }
            $this->loadedWarehouses = true;
        }
        //Lo chequeamos y guardamos el check
//        $this->checkLog .= $url . "\r\n";

        $check = in_array((int) $p_i_warehouse_id, $this->xwarehouse_ids, true);
        $this->checkedWarehouses[$p_i_warehouse_id] = $check;
        return $check;
    }

    private function _loadCashboxes($p_i_cashbox_id) {
        //Si no habia sido cargado lo cargamos
        if (!$this->loadedCashboxes) {
            if (Yii::app()->user->hasState('cashbox_ids')) {
                $this->xcashboxes_ids = Yii::app()->user->getState('cashbox_ids');
            } else {
                $this->redirectToLogin();
            }
            $this->loadedCashboxes = true;
        }
        //Lo chequeamos y guardamos el check
//        $this->checkLog .= $url . "\r\n";

        $check = in_array((int) $p_i_cashbox_id, $this->xcashboxes_ids, true);
        $this->checkedCashboxes[$p_i_cashbox_id] = $check;
        return $check;
    }

    private function loadConfirmations($route, $option_type) {
        //Si no habia sido cargado lo cargamos
        if (!$this->confirmationsLoaded) {
            if (Yii::app()->user->hasState('confirmations')) {
                $this->confirmations = Yii::app()->user->getState('confirmations');
            } else {
                $this->redirectToLogin();
            }
            $this->confirmationsLoaded = true;
        }
        //Lo chequeamos y guardamos el check
        //$this->checkLog .= $url . "\r\n";

        $check = $this->checkByConfirmation($this->confirmations, $route, $option_type);
        $this->checkedConfirmations[$route][$option_type] = $check;
        return $check;
    }

    private function loadUrlActions($url) {
        //Si no habia sido cargado lo cargamos
        if (!$this->urlActionsLoaded) {
            if (Yii::app()->user->hasState('urlActions')) {
                $this->urlActions = Yii::app()->user->getState('urlActions');
            } else {
                $this->redirectToLogin();
            }
            $this->urlActionsLoaded = true;
        }
        //Lo chequeamos y guardamos el check
//        $this->checkLog .= $url . "\r\n";

        $check = $this->checkByUrl($this->urlActions, $url);
        $this->checkedActions[$url] = $check;
        return $check;
    }

    private function loadModuleActions($module) {
        //Si no habia sido cargado lo cargamos
        if (!$this->urlActionsLoaded) {
            if (Yii::app()->user->hasState('urlActions')) {
                $this->urlActions = Yii::app()->user->getState('urlActions');
            } else {
                $this->redirectToLogin();
            }
            $this->urlActionsLoaded = true;
        }
        //Lo chequeamos y guardamos el check
//        $this->checkLog .= $module . "\r\n";

        $check = $this->checkByLike($this->urlActions, '#^.*/' . $module . '/.*/index.*$#');
        $this->checkedActions[$module] = $check;
        return $check;
    }

    private function checkByUrl(array $urlActions, $url) {

        $found = false;

        foreach ($urlActions as $urlAction) {
            if ($url == $urlAction['url']) {
                return true;
            } else {
                $found = $this->checkByUrl($urlAction['children'], $url);
                if ($found) {
                    return $found;
                }
            }
        }

        return $found;
    }

    private function checkByLike(array $urlActions, $regex) {

        $found = false;

        foreach ($urlActions as $urlAction) {

            if (preg_match($regex, $urlAction['url'])) {
                return true;
            } else {
                $found = $this->checkByLike($urlAction['children'], $regex);
                if ($found) {
                    return $found;
                }
            }
        }

        return $found;
    }

    private function checkByConfirmation(array $confirmations, $route, $option_type) {

        $found = false;

        foreach ($confirmations as $confirmation) {
            if ($route == $confirmation['route'] && $option_type == $confirmation['option_type']) {
                return true;
            }
        }
        return $found;
    }

    public function actionUpdate($id, $modal_id = null, $parent_id = null, $type = null, $element_id = null) {

        $model = $this->loadUpdate($id);

        $this->title = "Editar {$this->singular} '{$model->toString()}'";

        $modal_html = "";
        $content_html = "";
        $success = false;

        if (isset($_POST[$this->modelClass])) {

            $model->attributes = $_POST[$this->modelClass];
            if ($model->validate()) {
                $success = $this->secureSave($model);
            }
        } else {
            $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('parent_id' => $parent_id, 'modal_id' => $modal_id)), true, true);
        }

        if (!$success) {
            $content_html = $this->renderPartial("_form", array(
                'data' => $this->updateData($model, $modal_id)
                    ), true, true);
        } else {
            $content_html = $this->afterSuccess($element_id, $type, $model);
        }

        echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $model);
    }

    public function actionIndex() {
        $this->title = $this->plural;

        $model = new $this->viewClass($this->viewScenario);
        $model->unsetAttributes();

        foreach ($_GET as $key => $value) {
            if (property_exists($model, $key)) {
                $model->{$key} = $value;
            }
        }

        $this->filterIndex($model);

        $this->_getIndexUserCache($model);

        if (isset($_GET[$this->viewClass])) {
            $model->attributes = $_GET[$this->viewClass];
        }

        $this->_setIndexUserCache();

        $this->render('index', array(
            'data' => $this->indexData($model)
        ));
    }

    protected function filterIndex(&$p_o_model) {
        
    }

    public function actionCreate($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null) {

        $model = $this->loadCreate();

        $this->title = "Crear {$this->singular}";

        $modal_html = "";
        $content_html = "";
        $success = false;

        if (Yii::app()->request->isPostRequest) {

            $model->attributes = $_POST[$this->modelClass];

            if ($model->validate()) {
                $success = $this->secureSave($model);
            }
        } else {
            $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('parent_id' => $parent_id, 'modal_id' => $modal_id)), true, true);
        }


        if (!$success) {
            $content_html = $this->renderPartial($this->createView(), array(
                'data' => $this->createData($model, $modal_id)
                    ), true, true);
        } else {
            $content_html = $this->afterSuccess($element_id, $type, $model);
        }

        echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $model);
    }

    public function actionPreview($id, $modal_id = null, $parent_id = null) {

        $model = $this->loadPreview($id);

        $this->title = "Visualizando {$this->singular} '{$model->toString()}'";

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array(
                'parent_id' => $parent_id,
                'modal_id' => $modal_id
            )
                ), true, true);
        $content_html = $this->renderPartial("_preview", array(
            'data' => array(
                'model' => $model,
                'modal_id' => $modal_id
            )
                ), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html
        ));
    }

    public function actionDelete($id, $type = null, $element_id = null) {

        $success = 0;

        try {
            //Requerido para poder grabar un changelog
            $o_model = $this->loadDelete($id);

            if (is_null($o_model)) {
                throw new Exception("El elemento ya ha sido eliminado!");
            }

            try {
                $this->beginTransaction($o_model);
                $o_model->delete();
                $this->commitTransaction($o_model);
            } catch (CDbException $ex) {
                $this->rollbackTransaction($o_model);
                throw $ex;
            } catch (Exception $ex) {
                $this->rollbackTransaction($o_model);
                throw $ex;
            }
            $success = 1;
        } catch (CDbException $ex) {
            $type = 'message';
            $element_id = 'error';
            if ($ex->getCode() == Util::MYSQL_DELETE_RESTRICT) {
                $content_html = "No se puede eliminar este elemento porque está siendo usado.";
            } else {
                $content_html = $ex->errorInfo[2];
            }
        } catch (Exception $ex) {
            $type = 'message';
            $element_id = 'error';
            $content_html = $ex->getMessage();
        }

        if (Yii::app()->request->isAjaxRequest) {

            if ($success) {
                $content_html = $this->afterSuccess($element_id, $type);
            }

            echo CJSON::encode(array(
                'type' => $type,
                'element_id' => $element_id,
                'content_html' => $content_html
            ));
        } else {
            if ($success) {
                Yii::app()->user->setFlash('success', Strings::SUCCESS_OPERATION);
                $this->redirect(array('index'));
            } else {
                Yii::app()->user->setFlash('error', $content_html);
                $this->redirect(array('view', 'id' => $id));
            }
        }
    }

    public function indexData($model) {
        return array(
            'grid_id' => $this->id . "_grid",
            'model' => $model,
        );
    }

    public function createData($model, $modal_id) {
        return array(
            'modal_id' => $modal_id,
            'model' => $model,
        );
    }

    public function updateData($model, $modal_id) {
        return array(
            'modal_id' => $modal_id,
            'model' => $model,
        );
    }

    public function createView() {
        return "_form";
    }

    public function loadCreate() {
        return new $this->modelClass;
    }

    /**
     * Obtiene los formatos de impresión disponibles
     * @return array Array asociativo de formatos de impresión
     */
    protected function getFormats() {
        $s_path = Yii::getPathOfAlias('application.views.print');
        $a_items = [];
        $this->_getFormats($s_path, $a_items);
        sort($a_items, SORT_STRING);
        return $this->_normalize($a_items);
    }

    /**
     * Lee recursivamente los archivos de una carpeta
     * @param string $s_path Path de la carpeta
     * @param array $a_files Archivos dentro de la carpeta
     */
    private function _getFormats($s_path, &$a_files) {

        $a_items = scandir($s_path);

        foreach ($a_items as $s_item) {
            //Si la carpeta no es un retroceso
            if ($s_item !== '.' && $s_item !== '..') {

                $s_filepath = $s_path . DIRECTORY_SEPARATOR . $s_item;
                //Si es un directorio
                if (is_dir($s_filepath)) {
                    //Si es o bien common o bien, la carpeta de la organización
                    if ($s_item == 'common' || $s_item == $this->getOrganization()->organization_code) {
                        $this->_getFormats($s_filepath, $a_files);
                    }
                } else {
                    $_filename = pathinfo($s_item, PATHINFO_FILENAME);
                    $a_files[] = $_filename;
                }
            }
        }
    }

    /**
     * Convierte un array en asociativo
     * @param array $a_items Array de datos
     * @return array Array asociativo
     */
    private function _normalize($a_items) {
        $aa_items = [];

        foreach ($a_items as $s_item) {
            $aa_items[$s_item] = $s_item;
        }

        return $aa_items;
    }

    protected function _getIndexUserCache(&$model) {

        //Si el usuario tiene activada la opción remember_filters
        if (Yii::app()->user->getState('remember_filters') == 1) {
            if (!Yii::app()->request->isAjaxRequest) {
                //SORT AND PAGE
                $a_sort_and_page = SIANUserCache::get($this->getUnderscoredRoute() . '_SORT_AND_PAGE');

                if ($a_sort_and_page) {

                    foreach (['ajax', 'page', 'sort'] as $value) {
                        if (!isset($_GET[$value]) && isset($a_sort_and_page[$value])) {
                            $_GET[$value] = $a_sort_and_page[$value];
                        }
                    }
                }
            }

            //Vemos si hay una búsqueda anterior
            $a_filters = SIANUserCache::get($this->getUnderscoredRoute() . '_FILTERS');

            if ($a_filters) {

                foreach ($model->attributes as $attribute => $value) {
                    if (isset($a_filters[$attribute]) && $a_filters[$attribute] != $value) {
                        $this->default_values_changed = true;
                        break;
                    }
                }

                $model->attributes = $a_filters;
            }
        }
    }

    protected function _setIndexUserCache() {

        //Si el usuario tiene activada la opción remember_filters
        if (Yii::app()->user->getState('remember_filters') == 1) {
            //AJAX, SORT AND PAGE
            if (isset($_GET['ajax']) || isset($_GET['sort']) || isset($_GET['page'])) {

                $a_data = [];
                foreach (['ajax', 'page', 'sort'] as $s_value) {
                    $a_data[$s_value] = isset($_GET[$s_value]) ? $_GET[$s_value] : null;
                }

                SIANUserCache::add($this->getUnderscoredRoute() . '_SORT_AND_PAGE', $a_data);
            }

            //FILTERS
            if (isset($_GET[$this->viewClass])) {
                SIANUserCache::add($this->getUnderscoredRoute() . '_FILTERS', $_GET[$this->viewClass]);
            }
        }
    }

    protected function _setDefaultFilters(&$model, $default) {

        //Seteamos valores por defecto
        $model->attributes = $default;

        //Seteamos filtros por defecto
        SIANUserCache::add($this->getUnderscoredRoute() . '_DEFAULT', $default);
    }

    private function redirectToLogin() {
        $s_code = USTime::getDateCode();
        SIANCache::add($s_code, $this->currentNonAjaxUrl);

        $s_url = $this->createUrl('/site/login') . '?redirect=' . $s_code;
        $this->redirect($s_url);
    }

    public function getBusinessUnitIds() {
        if (!isset($this->business_unit_ids)) {

            if (!Yii::app()->user->hasState('business_unit_ids')) {
                $this->redirectToLogin();
            }

            $this->business_unit_ids = Yii::app()->user->getState('business_unit_ids');
        }

        return $this->business_unit_ids;
    }

    public function getStoreIds() {
        if (!isset($this->store_ids)) {

            if (!Yii::app()->user->hasState('store_ids')) {
                $this->redirectToLogin();
            }

            $this->store_ids = Yii::app()->user->getState('store_ids');
        }

        return $this->store_ids;
    }

    public function getDivisionIds() {
        if (!isset($this->division_ids)) {
            //Obtenemos unidades de negocio del usuario
            $a_business_unit_ids = $this->getBusinessUnitIds();
            //Obtenemos asociacion de unidades de negocio y divisiones
            $a_business_unit_divisions = $this->getBusinessUnitDivisions();
            //Armamos
            $a_divisions_ids = [];
            foreach ($a_business_unit_ids as $i_business_unit_id) {
                if (isset($a_business_unit_divisions[$i_business_unit_id])) {
                    $a_divisions_ids = array_merge($a_divisions_ids, $a_business_unit_divisions[$i_business_unit_id]);
                }
            }
            //Seteamos
            $this->division_ids = array_unique($a_divisions_ids);
        }

        return $this->division_ids;
    }

    public function pushFlash(&$p_a_flashes, $p_a_flash) {
        $p_a_flashes[$p_a_flash['message_type']][] = $p_a_flash['message'];
    }

    public function showFlashes($a_flashes) {
        foreach ($a_flashes as $s_type => $a_messages) {
            Yii::app()->user->setFlash($s_type, implode("<br><br>", $a_messages));
        }
    }

    public function actionUploadAttachments() {
        header('Content-Type: application/json');
        $a_preview = [];
        $a_configs = [];
        $a_resources = [];
        $a_errors = [];
        try {
            $s_input = $this->modelClass; // the input name for the fileinput plugin
            if (empty($_FILES[$s_input])) {
                return [];
            }
            //Variables
            $s_key = USTime::getDateCode();
            //Files
            $a_files = $_FILES[$s_input];
            $a_allowed_extensions = ['jpg', 'jpeg', 'png', 'pdf', 'xls', 'xlsx', 'doc', 'docx'];
            // Validar cada archivo individualmente
            for ($i = 0; $i < count($a_files['name']); $i++) {
                $s_file_name = $a_files['name'][$i];
                // Obtener la extensión del archivo
                $s_file_extension = strtolower(pathinfo($s_file_name, PATHINFO_EXTENSION));
                // Validar la extensión
                if (!in_array($s_file_extension, $a_allowed_extensions)) {
                    $a_errors[] = $s_file_name . ' archivo inválido. Sólo se permiten archivos con extensiones JPG, JPEG, PNG, DOC, DOCX, XLS, XLSX y PDF.';
                    goto Finish;
                }
            }
            $i_id = isset($_POST['id']) ? $_POST['id'] : null;
            $s_owner = isset($_POST['owner']) ? $_POST['owner'] : null;
            $s_type = isset($_POST['type']) ? $_POST['type'] : null;
            $s_sub_path = isset($_POST['sub_path']) ? $_POST['sub_path'] : null;
            $s_has_limit = isset($_POST['has_limit']) ? $_POST['has_limit'] : null;
            $s_limit = isset($_POST['limit']) ? $_POST['limit'] : null;
            $s_property_count = isset($_POST['property_count']) ? $_POST['property_count'] : null;
            //Generamos path
            $s_path = Yii::app()->params['files_dir'] . DIRECTORY_SEPARATOR . 'attachments' . DIRECTORY_SEPARATOR . $s_owner . DIRECTORY_SEPARATOR;
            //Obtenemos modelo
            $model = CActiveRecord::model($this->modelClass);
            $o_model = $model->findByPk([
                $model->primaryKey() => $i_id
            ]);
            //Verificamos si el movimiento está activo
            if ($o_model->status == 0) {
                $a_errors[] = "No se puede adjuntar archivos a un {$s_owner} inactivo.";
                goto Finish;
            }
            if ($s_has_limit == '1') {
                $i_limit = isset($s_limit) ? intval($s_limit) : $this->getOrganization()->globalVar->max_attachments;
                //Verificamos la cantidad de adjuntos
                if ($o_model->{$s_property_count} >= $i_limit) {
                    $a_errors[] = 'El máximo de archivos permitidos es ' . $i_limit;
                    goto Finish;
                }
            }
            //Creamos carpeta si no existe
            if (!file_exists($s_path . $i_id)) {
                mkdir($s_path . $i_id, 0755);
            }
            if (isset($s_sub_path)) {
                if (!file_exists($s_path . $i_id . DIRECTORY_SEPARATOR . $s_sub_path)) {
                    mkdir($s_path . $i_id . DIRECTORY_SEPARATOR . $s_sub_path, 0755);
                }
            }
            //Verificamos si el archivo existe
            $a_normalized = [];
            for ($i = 0; $i < count($a_files['name']); $i++) {
                //Obtenemos
                $s_tmp_filepath = $a_files['tmp_name'][$i];
                $s_filename = $a_files['name'][$i];
                $i_filesize = $a_files['size'][$i];
                //Make sure we have a file path
                if ($s_tmp_filepath != "") {
                    //Seteamos nueva ruta
                    $s_new_filepath = $s_path . $i_id . DIRECTORY_SEPARATOR . (isset($s_sub_path) ? $s_sub_path . DIRECTORY_SEPARATOR : '') . $s_filename;
                    $s_new_url = Yii::app()->params['files_url'] . '/attachments/' . $s_owner . '/' . $i_id . '/' . (isset($s_sub_path) ? $s_sub_path . '/' : '') . $s_filename;
                    //Verificamos si ya existe el archivo
                    if (!file_exists($s_new_filepath)) {
                        $a_normalized[] = [
                            'tmp_filepath' => $s_tmp_filepath,
                            'filename' => $s_filename,
                            'filesize' => $i_filesize,
                            'new_filepath' => $s_new_filepath,
                            'new_url' => $s_new_url,
                            'sub_type' => $s_sub_path,
                        ];
                    } else {
                        $a_errors[] = 'El archivo ' . $s_filename . ' ya existe.';
                    }
                } else {
                    $a_errors[] = 'El archivo ' . $s_filename . ' no subió correctamente.';
                }
            }
            foreach ($a_normalized as $a_item) {
                //Upload the file into the new path
                if (move_uploaded_file($a_item['tmp_filepath'], $a_item['new_filepath'])) {
                    $s_key = USTime::getDateCode();
                    $a_preview[] = $a_item['new_url'];
                    $sub_type = $a_item['sub_type'];
                    $s_type = SIANAttachment::getAttachmentType($a_item['new_filepath']);
                    //
                    $a_config = [
                        'type' => $s_type,
                        'key' => $s_key,
                        'caption' => $a_item['filename'],
                        'content' => '',
                        'size' => $a_item['filesize'],
                        'downloadUrl' => $this->createUrl('downloadAttachments') . '?folder=' . $i_id . '&key=' . $s_key,
                        'url' => $this->createUrl('deleteAttachments') . '?folder=' . $i_id . '&key=' . $s_key,
                    ];
                    //
                    switch ($s_type) {
                        case 'text':
                        case 'html':
                            $a_config['content'] = file_get_contents($a_item['new_filepath']);
                            break;
                        case 'video':
                            $a_config['downloadUrl'] = $a_item['new_url'];
                            $a_config['filename'] = $a_item['filename'];
                            $a_config['filetype'] = "video/mp4";
                            break;
                        default:
                            break;
                    }
                    //
                    $a_configs[] = $a_config;
                    //Guardamos los adjuntos
                    $a_resources[] = [
                        'type' => Resource::TYPE_ATTACHMENT,
                        'url' => $a_item['new_filepath'],
                        'title' => $s_key,
                        'sub_type' => $sub_type,
                        'description' => $a_item['filename'],
                        'width' => null,
                        'height' => null
                    ];
                } else {
                    $a_errors[] = 'Ocurrió un error al procesar el archivo ' . $s_filename;
                }
            }
            //Si hay errores
            if (count($a_errors) > 0) {
                goto Finish;
            } else {
                //Guardamos los adjuntos
                $this->_saveAttachments($o_model, $a_resources, $s_property_count);
            }
        } catch (CDbException $ex) {
            $a_errors[] = $ex->getMessage();
        } catch (Exception $ex) {
            $a_errors[] = $ex->getMessage();
        }
        Finish:
        $a_out = [
            'initialPreview' => $a_preview,
            'initialPreviewConfig' => $a_configs,
            'initialPreviewAsData' => true
        ];
        if (count($a_errors) > 0) {
            $a_out['error'] = '<p>' . implode('</p><p>', $a_errors) . '</p>';
        }
        $s_json = CJSON::encode($a_out);
        echo $s_json;
    }

    private function _saveAttachments($p_o_model, &$p_a_resources, $p_s_property_count) {
        try {
            $this->beginTransaction($p_o_model);
            //
            for ($i = 0; $i < count($p_a_resources); $i++) {
                $p_a_resources[$i]['owner'] = $p_o_model->getOwner();
                $p_a_resources[$i]['owner_id'] = $p_o_model[$p_o_model->primaryKey()];
                $p_a_resources[$i]['resource_number'] = ($p_o_model->{$p_s_property_count} + $i + 1);
            }
            //Insertamos recursos
            USDatabase::multipleInsert('Resource', $p_a_resources);
            //Actualizamos contador
            $p_o_model->updateByPk([
                'id' => $p_o_model[$p_o_model->primaryKey()]
                    ], [
                $p_s_property_count => ($p_o_model->{$p_s_property_count} + count($p_a_resources))
            ]);
            //Guardamos changelog
            $p_o_model->simple(Changelog::OTHER, Changelog::DEFAULT_REASON_CODE, '', [
                $p_s_property_count => [
                    'previous' => $p_o_model->{$p_s_property_count},
                    'current' => ($p_o_model->{$p_s_property_count} + count($p_a_resources))
                ]
            ]);
            $this->commitTransaction($p_o_model);
        } catch (CDbException $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error interno. Por favor comuníquese con sistemas.');
        } catch (Exception $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error interno. Por favor comuníquese con sistemas.');
        }
    }

    public function actionSortAttachments() {
        try {
            $i_id = isset($_POST['id']) ? $_POST['id'] : null;
            $a_keys = isset($_POST['keys']) ? $_POST['keys'] : null;
            $s_property_count = isset($_POST['property_count']) ? $_POST['property_count'] : null;
            $s_type = isset($_POST['type']) ? $_POST['type'] : null;
            //
            if (isset($i_id, $a_keys)) {
                //Obtenemos movement
                $o_model = $this->_getModel($i_id);
                //Comparamos si coincide la cantidad de adjuntos
                if ($o_model->{$s_property_count} == count($a_keys)) {
                    //Actualizamos en BD, sólo si hay más de un adjunto
                    if ($o_model->{$s_property_count} > 1) {
                        $this->_sortAttachments($o_model, $a_keys, $s_type);
                    }
                    //
                    echo CJSON::encode(array(
                        'code' => USREST::CODE_SUCCESS,
                        'message' => 'Success',
                    ));
                } else {
                    echo CJSON::encode(array(
                        'code' => USREST::CODE_BAD_REQUEST,
                        'message' => 'La lista de adjuntos no coincide con la lista de claves enviada.',
                    ));
                }
            } else {
                echo CJSON::encode(array(
                    'code' => USREST::CODE_BAD_REQUEST,
                    'message' => 'Debe especificar el ID del movimiento y los códigos de los adjuntos',
                ));
            }
        } catch (CDbException $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => $ex->getMessage()
            ]);
        } catch (Exception $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => $ex->getMessage()
            ]);
        }
    }

    /**
     * Obtiene el modelo para anular o eliminar
     * @param integer $p_i_id ID Movimiento
     * @param boolean $p_b_all Indicador que determina si se incluirá todas las relaciones
     * @return object Movement
     */
    private function _getModel($p_i_id) {
        $a_pk = [
            'id' => $p_i_id
        ];
        return CActiveRecord::model($this->modelClass)->findByPk($a_pk);
    }

    private function _sortAttachments($p_o_model, $p_a_keys, $p_s_type) {
        try {
            $this->beginTransaction($p_o_model);
            //Seteamos ordenes temporales
            $s_sql = "UPDATE resource SET resource_number = resource_number + :count WHERE `owner` = :owner AND owner_id = :owner_id AND type = :type";
            Yii::app()->db->createCommand($s_sql)->execute([
                ':count' => count($p_a_keys),
                ':owner' => $p_o_model->getOwner(),
                ':owner_id' => $p_o_model[$p_o_model->primaryKey()],
                ':type' => $p_s_type,
            ]);
            //Actualizamos los ordenes
            foreach ($p_a_keys as $i => $s_key) {
                Resource::model()->updateAll([
                    'resource_number' => ($i + 1)
                        ], [
                    'condition' => '`owner` = :owner AND owner_id = :owner_id AND type = :type AND title = :title',
                    'params' => [
                        ':owner' => $p_o_model->getOwner(),
                        ':owner_id' => $p_o_model[$p_o_model->primaryKey()],
                        ':type' => $p_s_type,
                        ':title' => $s_key
                    ]
                ]);
            }
            //Guardamos changelog
            $p_o_model->simple(Changelog::OTHER, Changelog::DEFAULT_REASON_CODE, '', [
                'attachment_keys' => [
                    'previous' => Strings::NOT_FOUND,
                    'current' => $p_a_keys
                ]
            ]);
            $this->commitTransaction($p_o_model);
        } catch (CDbException $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error interno. Por favor comuníquese con sistemas.');
        } catch (Exception $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error interno. Por favor comuníquese con sistemas.');
        }
    }

    public function actionViewAttachments() {
        try {
            $s_id = isset($_POST['id']) ? $_POST['id'] : null;
            $s_owner = isset($_POST['owner']) ? $_POST['owner'] : null;
            $s_sub_path = isset($_POST['sub_path']) ? $_POST['sub_path'] : null;
            $s_property_count = isset($_POST['property_count']) ? $_POST['property_count'] : null;
            $o_model = CActiveRecord::model($this->modelClass);
            $s_table = $o_model->tableName();
            $s_column_id = $o_model->primaryKey();
            $s_where = "R.`owner` = :owner AND R.type = :type";
            $a_params = [
                ':owner' => $o_model->getOwner(),
                ':type' => Resource::TYPE_ATTACHMENT
            ];
            if (isset($s_sub_path)) {
                $s_where = $s_where . " AND R.sub_type = :sub_type";
                $a_params["sub_type"] = $s_sub_path;
            }
            if (isset($s_id)) {
                $a_resources = Yii::app()->db->createCommand()->select([
                            "R.title AS key",
                            "R.description AS filename",
                            "R.url AS filepath",
                            "R.width",
                            "R.height",
                            "O.{$s_column_id} AS id",
                        ])
                        ->from('resource R')
                        ->join("{$s_table} O", "O.{$s_column_id} = R.owner_id AND O.{$s_column_id} = :id ", [
                            ':id' => $s_id
                        ])
                        ->where($s_where, $a_params)
                        ->queryAll();
                $a_attachments = [];
                //URL es en realidad el filepath
                foreach ($a_resources as $a_resource) {
                    //Verificamos si el archivo existe físicamente
                    if (file_exists($a_resource['filepath'])) {
                        $s_type_file = SIANAttachment::getAttachmentType($a_resource['filepath']);
                        $s_content = '';
                        //Si es un archivo de text
                        if (in_array($s_type_file, ['text', 'html'])) {
                            $s_content = file_get_contents($a_resource['filepath']);
                        }
                        $a_attachments[] = [
                            'type' => $s_type_file,
                            'key' => $a_resource['key'],
                            'preview_url' => Yii::app()->params['files_url'] . '/attachments/' . $s_owner . '/' . $s_id . '/' . (isset($s_sub_path) ? $s_sub_path . '/' : '') . $a_resource['filename'],
                            'caption' => $a_resource['filename'],
                            'content' => $s_content,
                            'size' => filesize($a_resource['filepath']),
                            'width' => isset($a_resource['width']) ? $a_resource['width'] . 'px' : null,
                            'height' => isset($a_resource['height']) ? $a_resource['height'] . 'px' : null,
                            'downloadUrl' => $this->createUrl('downloadAttachments') . '?folder=' . $s_id . '&key=' . $a_resource['key'],
                            'url' => $this->createUrl('deleteAttachments') . '?folder=' . $s_id . '&key=' . $a_resource['key'] . '&property_count=' . $s_property_count,
                        ];
                    } else {
                        $a_attachments[] = [
                            'type' => SIANAttachment::TYPE_IMAGE,
                            'key' => $a_resource['key'],
                            'preview_url' => Yii::app()->params['admin_url'] . "/images/attachment/404.jpg",
                            'caption' => 'Archivo no encontrado',
                            'content' => '',
                            'size' => 0,
                            'width' => null,
                            'height' => null,
                            'downloadUrl' => false,
                            'url' => $this->createUrl('deleteAttachments') . '?folder=' . $s_id . '&key=' . $a_resource['key'] . '&property_count=' . $s_property_count,
                        ];
                    }
                }
                echo CJSON::encode(array(
                    'code' => USREST::CODE_SUCCESS,
                    'message' => 'Success',
                    'attachments' => $a_attachments
                ));
            } else {
                echo CJSON::encode(array(
                    'code' => USREST::CODE_BAD_REQUEST,
                    'message' => 'No se ha especificado el id.',
                ));
            }
        } catch (CDbException $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => $ex->getMessage()
            ]);
        } catch (Exception $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'message' => $ex->getMessage()
            ]);
        }
    }

    public function actionDeleteAttachments() {
        try {
            $s_folder = isset($_GET['folder']) ? $_GET['folder'] : null;
            $s_key = isset($_GET['key']) ? $_GET['key'] : null;
            $s_property_count = isset($_GET['property_count']) ? $_GET['property_count'] : null;
            //Obtenemos recursos
            $a_resource = SIANAttachment::getModelResource($s_folder, $s_key, $this->modelClass);
            //Si es false es porque no se pudo obtener nada
            if ($a_resource) {
                //Obtenemos movement
                $o_model = $this->_getModel($a_resource['id']);
                //Verificamos si está activo
                if ($o_model->status == 1) {
                    //Eliminamos si el archivo existe
                    if (file_exists($a_resource['filepath'])) {
                        unlink($a_resource['filepath']);
                    }
                    //Elimamos de la BD
                    $this->_deleteResource($o_model, $a_resource['key'], $s_property_count);
                    echo CJSON::encode(array(
                        'code' => USREST::CODE_SUCCESS,
                        'message' => 'Archivo borrado exitosamente',
                    ));
                } else {
                    echo CJSON::encode([
                        'code' => USREST::CODE_BAD_REQUEST,
                        'error' => $ex->getMessage()
                    ]);
                }
            } else {
                echo CJSON::encode([
                    'code' => USREST::CODE_BAD_REQUEST,
                    'error' => 'No se pudo obtener el adjunto a eliminar.'
                ]);
            }
        } catch (CDbException $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'error' => $ex->getMessage()
            ]);
        } catch (Exception $ex) {
            echo CJSON::encode([
                'code' => USREST::CODE_INTERNAL_SERVER_ERROR,
                'error' => $ex->getMessage()
            ]);
        }
    }

    private function _deleteResource($p_o_model, $p_s_key, $p_s_property_count) {
        try {
            $this->beginTransaction($p_o_model);
            //Borramos de la bd
            Resource::model()->deleteAllByAttributes([
                'owner' => $p_o_model->getOwner(),
                'owner_id' => $p_o_model[$p_o_model->primaryKey()],
                'type' => Resource::TYPE_ATTACHMENT,
                'title' => $p_s_key
            ]);
            //Compactamos
            $i_count = SIANAttachment::compactResources($p_o_model[$p_o_model->primaryKey()], $this->modelClass);
            //Actualizamos contador            
            CActiveRecord::model($this->modelClass)->updateByPk([
                $p_o_model->primaryKey() => $p_o_model[$p_o_model->primaryKey()],
                    ], [
                $p_s_property_count => $i_count
            ]);
            //Changelog
            $p_o_model->simple(Changelog::OTHER, Changelog::DEFAULT_REASON_CODE, '', [
                $p_s_property_count => [
                    'previous' => $p_o_model->{$p_s_property_count},
                    'current' => $i_count
                ]
            ]);
            $this->commitTransaction($p_o_model);
        } catch (CDbException $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error.');
        } catch (Exception $ex) {
            $this->rollbackTransaction($p_o_model);
            USLog::save($ex);
            throw new Exception('Ha ocurrido un error.');
        }
    }

    public function actionDownloadAttachments() {
        $s_folder = isset($_GET['folder']) ? $_GET['folder'] : null;
        $s_key = isset($_GET['key']) ? $_GET['key'] : null;
        $a_resource = SIANAttachment::getModelResource($s_folder, $s_key, $this->modelClass);
        if (file_exists($a_resource['filepath'])) {
            header('Content-Description: File Transfer');
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . basename($a_resource['filename']) . '"');
            header('Expires: 0');
            header('Cache-Control: must-revalidate');
            header('Pragma: public');
            header('Content-Length: ' . filesize($a_resource['filepath']));
            readfile($a_resource['filepath']);
        }
    }

}
