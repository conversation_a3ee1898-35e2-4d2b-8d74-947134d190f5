<?php

class SIANSimpleSortable extends CWidget {

    public $id;
    public $items;
    public $attribute_id;
    public $attribute_name;
    public $attribute_order;
    public $tag = null;
    public $modal_id = null;
    public $preview_route;
    //PRIVATE
    private $controller;
    private $preview_access = false;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->tag = isset($this->tag) ? $this->tag : 'objeto';
        //
        $this->preview_access = $this->controller->checkRoute('/marketing/division/preview');
        //
        SIANAssets::registerScriptFile('js/sian-simple-sortable.js');
        SIANAssets::registerCssFile('css/sian-simple-sortable.css');

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        $(document).ready(function() { 
            //LENAMOS LA TABLA
            SIANSimpleSortableInit('{$this->id}');
        });
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}' class='row'>";
        if (count($this->items) > 0) {
            foreach ($this->items as $o_item) {
                echo "<div class='us-sortable-item col-lg-4 col-md-4 col-sm-6 col-xs-12'>";
                echo "<div class='form-group'>";
                echo "<div class='form-control'>";
                echo $this->widget('application.widgets.USLink', array(
                    'route' => '/marketing/division/preview',
                    'label' => $o_item->{$this->attribute_name},
                    'title' => "Ver {$this->tag}",
                    'class' => 'form',
                    'data' => array(
                        'id' => $o_item->{$this->attribute_id},
                        'parent_id' => $this->modal_id
                    ),
                    'visible' => $this->preview_access,
                        ), true);
                echo "</div>";
                echo CHtml::hiddenField("Item[{$o_item->{$this->attribute_id}}]", $o_item->{$this->attribute_order});
                echo "</div>";
                echo "</div>";
            }
        } else {
            echo "<center>";
            echo Strings::NO_DATA;
            echo "</center>";
        }
        echo "</div>";
        echo "<hr>";
        echo "<p><em>Para ver un(a) {$this->tag} haga click en su nombre. ";
        echo "<b>Puede cambiar el orden arrastrándo los ítems.<b></em></p>";
    }

}
