<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of USLog
 *
 * <AUTHOR>
 */
class USLog {

    /**
     * Obtiene los datos de una exception
     * @param mixed $p_o_exception Exception o sus derivados, tales como SoapFault, CException, etc
     * @return array Array de datos
     */
    private static function getData($p_o_exception) {

        if (isset($p_o_exception)) {
            return [
                'code' => $p_o_exception->getCode(),
                'file' => $p_o_exception->getFile(),
                'line' => $p_o_exception->getLine(),
                'message' => $p_o_exception->getMessage(),
                'trace' => $p_o_exception->getTraceAsString(),
                'previous' => self::getData($p_o_exception->getPrevious()),
            ];
        } else {
            return null;
        }
    }

    /**
     * Obtiene los datos de una exception
     * @param mixed $p_a_data Exception o sus derivados, tales como SoapFault, CException, etc
     * @return array Array de datos
     */
    private static function processData($p_a_data) {

        $log_code = USTime::getDateCode();
        //
        $a_process = [
            'skin' => Yii::app()->params['skin'],
            'server_name' => Util::getServerName(),
            'username' => Util::getUsername(),
            'log_code' => $log_code,
            'date' => USTime::decodeDateCode($log_code, 'Y-m-d'),
            'time' => USTime::decodeDateCode($log_code, 'H:i:s'),
            'datetime' => USTime::decodeDateCode($log_code, 'Y-m-d H:i:s'),
        ];

        //Campos propios de la excepción
        foreach ($p_a_data as $s_key => $m_value) {

            if ($s_key == "trace") {
                $a_process[$s_key] = explode(PHP_EOL, $m_value);
                continue;
            }

            if ($s_key == "traces") {
                continue;
            }

            if (is_object($m_value)) {
                continue;
            }

            if (is_array($m_value)) {
                continue;
            }

            if (is_string($m_value)) {
                $a_process[$s_key] = $m_value;
                continue;
            }

            $a_process[$s_key] = $m_value;
        }

        //Campos propios del request
        $a_process['request'] = Util::getRequest();

        return $a_process;
    }

    /**
     * Guarda un log
     * @param mixed $p_m_data Excepción o array
     * @return string Filepath del log
     */
    public static function save($p_m_data) {

        //Si es objeto convertimos a array
        if (is_object($p_m_data)) {
            $p_m_data = self::getData($p_m_data);
        }
        //Procesamos data
        $a_data = self::processData($p_m_data);
        //Guardamos en BD
        if (Yii::app()->params['mongo_enabled']) {
            Yii::app()->mongo->insertOne('errorlog', $a_data);
        }
        //Retornamos log_code
        return $a_data['log_code'];
    }

}
