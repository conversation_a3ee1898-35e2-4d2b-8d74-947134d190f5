<?php

class USKeywordField extends CWidget {

    public $id;
    public $form;
    public $model;
    public $attribute;
    public $tags;

    public function init() {
        $this->id = isset($this->id) ? $this->id : Yii::app()->controller->getServerId();
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo "<div class='form-group " . ($this->model->getError($this->attribute) ? 'has-error' : '') . "'>";
        echo $this->form->labelEx($this->model, $this->attribute);
        echo Yii::app()->controller->widget('booster.widgets.TbSelect2', array(
            'asDropDownList' => false,
            'model' => $this->model,
            'attribute' => $this->attribute,
            'options' => array(
                'tags' => $this->tags,
                'placeholder' => $this->model->getAttributeLabel($this->attribute),
                'width' => '100%',
                'tokenSeparators' => array(','),
            ),
            'htmlOptions' => array(
                'id' => $this->id,
            )), true);
        echo SIANForm::errorBlock($this->model, $this->attribute);
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }

}
