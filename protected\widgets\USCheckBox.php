<?php

class USCheckBox extends CWidget {

    public $id;
    public $model = null;
    public $attribute = null;
    public $htmlOptions = [];
    public $hidden = false;
    public $label;
    public $name;
    public $value;
    public $hint = '';
    public $onchange = null;
    //Private
    private $controller;
    private $hidden_id;

    /**
     * Initializes the widget.
     */
    public function init() {
        $this->controller = Yii::app()->controller;
        //
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->hidden_id = $this->controller->getServerId();

        //Si no es nulo es porque se específico un modelo y atributo
        if (isset($this->model, $this->attribute)) {
            $this->label = $this->model->getAttributeLabel($this->attribute);
            $this->name = get_class($this->model) . '[' . $this->attribute . ']';
            $this->value = $this->model->{$this->attribute};
        }

//        if (isset($this->htmlOptions['readonly']) && $this->htmlOptions['readonly']) {
//            $this->htmlOptions['onclick'] = 'this.checked=!this.checked;';
//        }

        if ($this->hidden) {
            $this->htmlOptions['class'] = 'hide';
        }


        Yii::app()->clientScript->registerScript(Yii::app()->controller->getServerId(), "
       
        $('body').on('change', '#{$this->id}', function(e){
            {$this->onchange};
        });

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        echo "<div class='form-group'>";
        if (!USString::isBlank($this->label)) {
            echo CHtml::label($this->label, null, array(
                'class' => ($this->hidden ? 'hide' : ''),
            ));
            echo "<br/>";
        }
        echo CHtml::hiddenField($this->name, 0, array(
            'id' => $this->hidden_id,
            'disabled' => isset($this->htmlOptions['disabled']) ? $this->htmlOptions['disabled'] : false,
        ));

        $this->htmlOptions['id'] = $this->id;
        $this->htmlOptions['class'] = (isset($this->htmlOptions['class']) ? $this->htmlOptions['class'] : '');
        $this->htmlOptions['data-hidden_id'] = $this->hidden_id;

        echo CHtml::checkBox($this->name, $this->value, $this->htmlOptions);
        //Si hay hint
        if (isset($this->hint)) {
            echo "<span class='help-block'>{$this->hint}</span>";
        }
        echo "</div>";
    }

}
