<?php

class SIANScheduledEntries extends CWidget {

    public $id;
    public $model;
    public $dataProvider;
    public $parent_routes = [];
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //
        $this->dataProvider->pagination = false;
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<h4>Lista de pagos programados:</h4>";

        //ACCESS
        $access1 = $this->controller->checkRoute('/accounting/operation/preview');
        $access2 = $this->controller->checkActionRoutes(['view' => $this->parent_routes]);
        $access3 = $this->controller->checkRoute('/administration/person/preview');
        $access4 = $this->controller->checkActionRoutes([
            'requestPay' => $this->parent_routes,
            'create' => $this->controller->getChildPayableRoutes($this->parent_routes)
        ]);
        $confirmationRoutes = $this->controller->getConfirmationRoutes();
        //GRID
        $columns = [];
        $counter = 0;

        array_push($columns, array(
            'header' => 'Item',
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function($row) use(&$counter) {
                return ++$counter;
            },
        ));

        array_push($columns, array(
            'name' => 'operation_code',
            'type' => 'raw',
            'value' => function($row) use ($access1) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                            'route' => "/accounting/operation/preview",
                            'label' => $row->operation_name,
                            'title' => 'Ver operación',
                            'class' => 'form',
                            'data' => array(
                                'id' => $row->operation_code
                            ),
                            'visible' => $access1,
                                ), true);
            },
        ));

        array_push($columns, array(
            'name' => 'document',
            'type' => 'raw',
            'value' => function($row) use ($access2) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                            'route' => "/{$row->route}/view",
                            'label' => $row->document,
                            'title' => 'Ver documento',
                            'params' => array(
                                'id' => $row->movement_id
                            ),
                            'target' => $row->movement_id,
                            'visible' => $access2["/{$row->route}/view"],
                                ), true);
            },
        ));

        array_push($columns, array(
            'name' => 'detail',
            'type' => 'raw',
        ));

        array_push($columns, array(
            'name' => 'aux_person_id',
            'type' => 'raw',
            'value' => function($row) use ($access3) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                            'route' => "/administration/person/preview",
                            'label' => $row->aux_person_name,
                            'title' => 'Ver socio de negocio',
                            'class' => 'form',
                            'data' => array(
                                'id' => $row->aux_person_id
                            ),
                            'visible' => $access3,
                                ), true);
            },
        ));

        array_push($columns, array(
            'name' => 'emission_date',
            'type' => 'raw',
        ));

        array_push($columns, array(
            'name' => 'pay_date',
            'type' => 'raw',
        ));

        array_push($columns, array(
            'header' => 'Monto ' . Currency::getSymbol($this->model->movement->currency),
            'headerHtmlOptions' => array('style' => 'text-align:right'),
            'htmlOptions' => array('style' => 'text-align:right'),
            'type' => 'raw',
            'value' => function($row) {
                return $row->amount;
            },
        ));

        array_push($columns, array(
            'name' => 'finished',
            'type' => 'raw',
            'htmlOptions' => array('style' => 'text-align:center'),
            'value' => function($row) {
                return Yii::app()->controller->widget('application.widgets.USBooleanLabel', array(
                            'value' => $row->finished,
                                ), true);
            },
        ));

        array_push($columns, array(
            'name' => 'valid',
            'type' => 'raw',
            'htmlOptions' => array('style' => 'text-align:center'),
            'value' => function($row) {
                return Yii::app()->controller->widget('application.widgets.USBooleanLabel', array(
                            'value' => $row->valid,
                                ), true);
            },
        ));

        array_push($columns, array(
            'header' => 'Opciones',
            'headerHtmlOptions' => array('style' => 'width:6%;text-align:center;'),
            'type' => 'raw',
            'htmlOptions' => array('style' => 'text-align:center'),
            'value' => function($row) use ($access2, $access3, $access4, $confirmationRoutes) {

                if ($row->route == 'accounting/loan') {
                    if (USString::explodeContains(',', $row->account_code, "452")) {
                        $payableRoute = '/accounting/leasing/create';
                    } else {
                        $payableRoute = '/financial/feePay/create';
                    }
                } else {
                    $payableRoute = "/{$this->controller->getChildPayableRoute($row->route, Scenario::DIRECTION_OUT)}/create";
                }

                $payable = $row->pay_directly == 1 || $row->pay_confirmed == 1 || in_array($row->route . Confirmation::SEPARATOR . Confirmation::TYPE_PAY, $confirmationRoutes);

                return Yii::app()->controller->widget('application.widgets.USGridButtons', array(
                            'id' => $row->pk,
                            'grid_id' => $this->controller->getServerId(),
                            'buttons' => array(
                                array('icon' => 'fa fa-dollar fa-lg', 'color' => 'red', 'class' => 'ajax-check', 'title' => 'Solicitar pago', 'route' => "/{$row->route}/requestPay", 'params' => ['id' => $row->movement_id], 'visible' => $row->is_payable && $access4["/{$row->route}/requestPay"] && $row->finished == 0, 'when' => !$payable),
                                array('icon' => 'fa fa-dollar fa-lg', 'color' => 'black', 'title' => 'Pagar', 'route' => $payableRoute, 'visible' => $row->is_payable && $access4[$payableRoute] && $row->finished == 0, 'when' => $payable),
                            )), true);
            },
        ));

        $gridParams = array(
            'id' => $this->id,
            'type' => 'hover condensed',
            'dataProvider' => $this->dataProvider,
            'enableSorting' => true,
            'selectableRows' => 0,
            'columns' => $columns,
            'template' => '{items}',
            'nullDisplay' => Strings::NONE,
        );
        $this->widget('application.widgets.USGridView', $gridParams);

        echo "<br>";
        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-6 us-detail'>";
        echo "<table style='table-layout: fixed'>";
        echo "<tr><td><strong>Observaciones</strong></td></tr>";
        echo "<tr><td>" . USString::ifBlank($this->model->movement->observation, Strings::NONE) . "</td></tr>";
        echo "</table>";
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-4'></div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-2 us-detail'>";
        echo "<table>";
        echo "<tr><td colspan='2'><h4>Montos ({$this->model->movement->getCurrencySymbol()})</h4></td></tr>";
        echo "<tr><td><strong>TOTAL:</strong></td><td class='pull-right'>{$this->model->displayValue('amount')}</td></tr>";
        echo "</table>";
        echo "</div>";
        echo "</div>";
        echo "<p>(*) Un documento programado está 'Procesado' si ya se realizó el pago total o parcial del mismo. Si se anula o elimina el pago, el documento seguirá en estado 'Procesado', pero se podrá volver a programar.</p>";
        echo "<p>(**) Un documento programado deja de ser válido, cuando la cuenta por pagar programada fue editada.</p>";
    }

}
