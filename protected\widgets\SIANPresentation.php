<?php

class SIANPresentation extends CWidget {

    public $id;
    public $form;
    public $model;
    public $unit_measure;
    public $ifixed = Product::IFIXED;
    public $tfixed = Product::TFIXED;
    public $measure_id = null;
    public $measure_items = [];
    public $custom_measure_items = [];
    public $lock_prices = false;
    public $lock_default = false;
    public $lock_add_and_remove = false;
    public $min_margin = 0;
    public $avg_margin = 0;
    public $web_margin = 0;
    public $cost = 0;
    public $prices = [];
    public $has_panel = true;
    //PRIVATE
    private $controller;
    private $add_button_id;
    private $istep;
    private $tstep;
    private $currency;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->currency = isset($this->model->price_currency) ? $this->model->price_currency : $this->controller->getOrganization()->globalVar->display_currency;
        //PRIVATE
        $this->add_button_id = $this->controller->getServerId();
        //VAR
        $this->istep = USMath::toStep($this->ifixed);
        $this->tstep = USMath::toStep($this->tfixed);
        //ITEMS
        $presentationItems = [];

        foreach ($this->model->tempPresentations as $presentation) {
            $attributes = [];

            $attributes['equivalence'] = $presentation->equivalence;
            $attributes['min_stock'] = $presentation->min_stock;
            $attributes['measure_id'] = $presentation->measure_id;
            $attributes['custom_measure_id'] = $presentation->custom_measure_id;
            $attributes['abbreviation'] = $presentation->abbreviation;
            $attributes['measure_name'] = $presentation->measure_name;
            $attributes['barcode'] = $presentation->barcode;
            $attributes['mprice'] = 0;
            $attributes['imprice'] = 0;
            $attributes['aprice'] = 0;
            $attributes['iaprice'] = 0;
            $attributes['wprice'] = 0;
            $attributes['iwprice'] = 0;
            if (Yii::app()->controller->getOrganization()->globalVar->display_currency == GlobalVar::PRICE_MODE_BY_ORGANIZATION) {

                if (isset($this->prices[$presentation->equivalence])) {
                    $a_price = $this->prices[$presentation->equivalence];
                    $attributes['mprice'] = $a_price["mprice_{$this->currency}"];
                    $attributes['imprice'] = $a_price["imprice_{$this->currency}"];
                    $attributes['aprice'] = $a_price["aprice_{$this->currency}"];
                    $attributes['iaprice'] = $a_price["iaprice_{$this->currency}"];
                    $attributes['wprice'] = $a_price["wprice_{$this->currency}"];
                    $attributes['iwprice'] = $a_price["iwprice_{$this->currency}"];
                }
            }
            $attributes['weight'] = $presentation->weight;
            $attributes['length'] = $presentation->length;
            $attributes['width'] = $presentation->width;
            $attributes['height'] = $presentation->height;
            $attributes['xdefault'] = $presentation->default;
            $attributes['lock_equivalence'] = $presentation->lock_equivalence;
            $attributes['lock_name'] = $presentation->lock_name;
            $attributes['lock_combo'] = $presentation->lock_combo;

            $attributes['errors'] = [
                'equivalence' => $presentation->getError('equivalence'),
                'min_stock' => $presentation->getError('min_stock'),
                'measure_id' => $presentation->getError('measure_id'),
                'measure_name' => $presentation->getError('measure_name'),
                'barcode' => $presentation->getError('barcode'),
                'mprice' => $presentation->getError("mprice_{$this->currency}"),
                'aprice' => $presentation->getError("aprice_{$this->currency}"),
                'wprice' => $presentation->getError("wprice_{$this->currency}"),
                'imprice' => $presentation->getError("imprice_{$this->currency}"),
                'iaprice' => $presentation->getError("iaprice_{$this->currency}"),
                'iwprice' => $presentation->getError("iwprice_{$this->currency}"),
                'weight' => $presentation->getError('weight'),
                'length' => $presentation->getError('length'),
                'width' => $presentation->getError('width'),
                'height' => $presentation->getError('height'),
            ];

            $presentationItems[] = $attributes;
        }

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-presentation.js');

        //Registramos scripts
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        $(document).ready(function() {
        
            //COUNT
            $('#{$this->id}').data('count', 0);
            //TIPE
            $('#{$this->id}').data('product_type', '{$this->model->product_type}');
            //IGV
            $('#{$this->id}').data('igv', {$this->controller->getOrganization()->globalVar->igv});
            $('#{$this->id}').data('measure_items', " . CJSON::encode($this->measure_items) . ");
            $('#{$this->id}').data('custom_measure_items', " . CJSON::encode($this->custom_measure_items) . ");
            $('#{$this->id}').data('allow_decimals', {$this->model->allow_decimals});
            $('#{$this->id}').data('ifixed', {$this->ifixed});
            $('#{$this->id}').data('tfixed', {$this->tfixed});
            $('#{$this->id}').data('istep', {$this->istep});
            $('#{$this->id}').data('tstep', {$this->tstep});
            $('#{$this->id}').data('lock_prices', {$this->lock_prices});
            $('#{$this->id}').data('lock_default', {$this->lock_default});
            $('#{$this->id}').data('lock_add_and_remove', {$this->lock_add_and_remove});
            $('#{$this->id}').data('advanced', {$this->model->advanced});
            $('#{$this->id}').data('price_mode', {$this->model->price_mode});
            $('#{$this->id}').data('measure_id', '{$this->measure_id}');
            //MARGENES DE UTILIDAD
            $('#{$this->id}').data('min_margin', {$this->min_margin});
            $('#{$this->id}').data('avg_margin', {$this->avg_margin});
            $('#{$this->id}').data('web_margin', {$this->web_margin});
            $('#{$this->id}').data('cost', {$this->cost});
            $('#{$this->id}').data('currency', '{$this->currency}');
            $('#{$this->id}').data('global_price_mode', " . Yii::app()->controller->getOrganization()->globalVar->price_mode . ");
            //MOVIMIENTOS
            var array = " . CJSON::encode($presentationItems) . ";
            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {
                    SIANPresentationAddItem('{$this->id}', array[i]['equivalence'], array[i]['barcode'], array[i]['min_stock'], array[i]['measure_id'], array[i]['custom_measure_id'], array[i]['abbreviation'], array[i]['measure_name'], '{$this->currency}', array[i]['mprice'], array[i]['imprice'], array[i]['aprice'], array[i]['iaprice'], array[i]['wprice'], array[i]['iwprice'], array[i]['weight'], array[i]['length'], array[i]['width'], array[i]['height'], array[i]['xdefault'], array[i]['lock_equivalence'], array[i]['lock_name'], array[i]['lock_combo'], array[i]['errors']);
                }
            }
            else
            {
                $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
            }
           
            SIANPresentationUpdate('{$this->id}');

            unfocusable();
            $('#{$this->id}').find('thead span.currency-symbol').text(USCurrencyGetSymbol('{$this->currency}'));           
        });
        
        $('body').on('click', '#{$this->id} input.sian-presentation-item-default', function() {
            $('#{$this->id} input[type=radio]:checked').not(this).prop('checked', false);
        });
        
        $('body').on('click', '#{$this->add_button_id}', function(e) {
            var currency = $('#{$this->id}').data('currency');
            SIANPresentationAddItem('{$this->id}', 1, '', 100, {$this->measure_id}, '', '', '', currency, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, {}); 
            SIANPresentationUpdate('{$this->id}');
            unfocusable();
        });  
            
        $('#{$this->id}').data('allowDecimals', function(allow_decimals){
            var table = $('#{$this->id}');
            table.data('allow_decimals', allow_decimals);
            //Actualizamos
            SIANPresentationUpdate('{$this->id}');
        });
        
        $('#{$this->id}').data('changeCurrency', function(currency){
            
            var table = $('#{$this->id}');            
            var header = table.find('thead');
            var exchange_rate = " . Yii::app()->controller->getLastExchange() . ";
            var tfixed = {$this->tfixed};
            
            $(header).find('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            
            $.each(table.find('tr.sian-presentation-item'), function( index, row ) {
            
                var rowObj = $(row);                
                rowObj.find('input.sian-presentation-item-currency').val(currency);
                
                if(currency == '" . Currency::USD . "'){  
                    
                    rowObj.find('input.sian-presentation-item-mprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-item-imprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-item-aprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-item-iaprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-item-wprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-item-iwprice').toUsd(exchange_rate, tfixed);
                    
                }else{

                    rowObj.find('input.sian-presentation-item-mprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-item-imprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-item-aprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-item-iaprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-item-wprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-item-iwprice').toPen(exchange_rate, tfixed);
                }
            });           
        });
        
        $('#{$this->id}').data('setCurrency', function(currency){
            
            var table = $('#{$this->id}');     
            $.each(table.find('tr.sian-presentation-item'), function (index, row) {
                var rowObj = $(row);
                rowObj.find('input.sian-presentation-item-currency').val(currency);
            });
        });
                    
       
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->has_panel) {
            $this->beginWidget('booster.widgets.TbPanel', array('title' => $this->model->getAttributeLabel('presentations'),
                'headerIcon' => 'list',
                'htmlOptions' => array(
                    'class' => ($this->model->hasErrors('tempPresentations') ? 'us-error' : ''),
                )
            ));
        }

        echo "<div class='table-responsive'>";
        echo "<table id='{$this->id}' class='table table-condensed table-hover'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th width='12%' class='sian-presentation-basic'>{$this->model->getAttributeLabel('presentations.measure_id')} *</th>";
        echo "<th width='9%' class='sian-presentation-basic'>{$this->model->getAttributeLabel('presentations.equivalence')}</th>";
        echo "<th width='12%' class='sian-presentation-basic'>{$this->model->getAttributeLabel('presentations.custom_measure_id')}</th>";
        echo "<th width='14%' class='sian-presentation-basic'>{$this->model->getAttributeLabel('presentations.barcode')}</th>";
        echo "<th width='9%' class='sian-presentation-advanced' " . ($this->model->advanced ? '' : 'style=\'display: none;\'') . ">{$this->model->getAttributeLabel('presentations.min_stock')}</th>";
//        if (Yii::app()->controller->getOrganization()->globalVar->price_mode == GlobalVar::PRICE_MODE_BY_ORGANIZATION) {
//            echo "<th width='10%' class='sian-presentation-basic sian-presentation-without-igv' " . ($this->model->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">P.Min. <span class = 'currency-symbol'></span></th>";
//            echo "<th width='10%' class='sian-presentation-basic sian-presentation-with-igv' " . ($this->model->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">P.Min. <span class = 'currency-symbol'></span> (Inc. IGV)</th>";
//            echo "<th width='10%' class='sian-presentation-basic sian-presentation-without-igv' " . ($this->model->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">P. Pro. <span class = 'currency-symbol'></span></th>";
//            echo "<th width='10%' class='sian-presentation-basic sian-presentation-with-igv' " . ($this->model->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">P. Pro. <span class = 'currency-symbol'></span> (Inc. IGV)</th>";
//            echo "<th width='10%' class='sian-presentation-basic sian-presentation-without-igv' " . ($this->model->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">P. Web. <span class = 'currency-symbol'></span></th>";
//            echo "<th width='10%' class='sian-presentation-basic sian-presentation-with-igv' " . ($this->model->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">P. Web. <span class = 'currency-symbol'></span> (Inc. IGV)</th>";
//        }
        echo "<th width='8%' class='sian-presentation-advanced' " . ($this->model->advanced ? '' : 'style=\'display: none;\'') . ">{$this->model->getAttributeLabel('presentations.weight')}</th>";
        echo "<th width='8%' class='sian-presentation-advanced' " . ($this->model->advanced ? '' : 'style=\'display: none;\'') . ">{$this->model->getAttributeLabel('presentations.length')}</th>";
        echo "<th width='8%' class='sian-presentation-advanced' " . ($this->model->advanced ? '' : 'style=\'display: none;\'') . ">{$this->model->getAttributeLabel('presentations.width')}</th>";
        echo "<th width='8%' class='sian-presentation-advanced' " . ($this->model->advanced ? '' : 'style=\'display: none;\'') . ">{$this->model->getAttributeLabel('presentations.height')}</th>";
        echo "<th width='2%'>{$this->model->getAttributeLabel('presentations.default')}</th>";
        echo "<th width='2%'></th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody></tbody>";
        if ($this->model->product_type == Product::TYPE_MERCHANDISE && !$this->lock_add_and_remove) {
            echo "<tfoot>";
            echo "<tr><td colspan = 99>(*) Para otras presentaciones que no esten en la lista elija unidad y personalice la etiqueta.</td></tr>";
            echo "<tr>";
            echo "<td colspan=99>";
            $this->widget('application.widgets.USLink', array(
                'id' => $this->add_button_id,
                'label' => 'Agregar',
                'icon' => 'fa fa-lg fa-plus white',
                'title' => 'Agregar',
            ));
            echo "</td>";
            echo "</tr>";
            echo "</tfoot>";
        }
        echo "</table>";
        echo "</div>";

        if ($this->has_panel) {
            $this->endWidget();
        }
    }

}
