<?php

class USCostLevel extends CWidget {

    public $id;
    public $form;
    public $model;
    public $value_attribute;
    public $text_attribute;
    public $all = true;
    public $extra_class = '';
    public $minimumInputLength = 3;
    public $modal_id = null;
    public $readonly = false;
    public $required = null;
    public $width = 400;
    public $extra = [];
    public $value_name = null;
    public $text_name = null;
    public $value = null;
    public $text = null;
    public $label = '';
    //PRIVATE
    private $controller;
    private $div_id = null;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->div_id = $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('other/select2/js/select2.min.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
        SIANAssets::registerScriptFile('js/us-span.js');
        SIANAssets::registerScriptFile('js/us-select2.js');
        SIANAssets::registerScriptFile('js/us-cost-level.js');
        //
        SIANAssets::registerCssFile('other/select2/css/select2.css');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id . '_ajax', "

        //DATA
//        $(document).ready(function() {   
            var html = USSpanInit('{$this->id}', '{$this->extra_class}', '{$this->_getValue()}', '{$this->_getText()}', '{$this->_getValueName()}', '{$this->_getTextName()}', '{$this->_getLabel()}', [], '{$this->_getError()}', " . CJSON::encode($this->_getRequired()) . ", " . CJSON::encode($this->readonly) . ");
            $('#{$this->div_id}').html(html);
                 
            //Agregamos extra data
            USSpanSetData('{$this->id}', " . CJSON::encode($this->extra) . ");
//        });
        
        $('body').on('click', '#{$this->id}', function(e) {
            USCostLevelInit('{$this->id}', " . ($this->all == true ? 1 : 0) . ", {$this->minimumInputLength}, {$this->width});
        });

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        echo "<div id='{$this->div_id}'></div>";
    }

    private function _getValueName() {
        if (isset($this->model) && isset($this->value_attribute)) {
            return get_class($this->model) . "[" . $this->value_attribute . "]";
        } else {
            return $this->value_name;
        }
    }

    private function _getTextName() {
        if (isset($this->model) && isset($this->text_attribute)) {
            return get_class($this->model) . "[" . $this->text_attribute . "]";
        } else {
            return $this->text_name;
        }
    }

    private function _getValue() {
        if (isset($this->model) && isset($this->value_attribute)) {
            return $this->model->{$this->value_attribute};
        } else {
            return $this->value;
        }
    }

    private function _getText() {
        if (isset($this->model) && isset($this->text_attribute)) {
            return $this->model->{$this->text_attribute};
        } else {
            return $this->text;
        }
    }

    private function _getLabel() {
        if (isset($this->model) && isset($this->value_attribute)) {
            return $this->model->getAttributeLabel($this->value_attribute);
        } else {
            return $this->label;
        }
    }

    private function _getRequired() {
        if (isset($this->model) && isset($this->value_attribute)) {
            return $this->model->isAttributeRequired($this->value_attribute);
        } else {
            return false;
        }
    }

    private function _getError() {
        if (isset($this->model) && isset($this->value_attribute)) {
            return $this->model->getError($this->value_attribute);
        } else {
            return '';
        }
    }

}
