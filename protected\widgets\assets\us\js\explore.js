$('body').on('click', '.explore', function () {

    var element = $(this);

    $.ajax({
        type: 'get',
        url: element.attr('url'),
        data: element.data(),
        beforeSend: function (xhr) {
            window.active_ajax++;
            //Ocultamos los tooltip
            $('div.ui-tooltip').remove();
        },
        success: function (data) {

            if ($('#' + data.modal_id).length == 0) {
                $('.modal_dynamic').append(data.modal_html);
                $('#' + data.modal_id + ' .form_div').html(data.content_html);

                window.KCFinder = {
                    callBack: function (url) {
                        $('#' + data.element_id).val(url);
                        $('#' + data.modal_id).modal('hide');
                        $('#' + data.element_id).focus();
                    }
                };
            }

            $('#' + data.modal_id).modal('show');
            window.active_ajax--;
        },
        error: function (request, status, error) {
            bootbox.alert(us_message(request.responseText, 'error'));
            window.active_ajax--;
        },
        dataType: 'json'
    });

    return false;
});