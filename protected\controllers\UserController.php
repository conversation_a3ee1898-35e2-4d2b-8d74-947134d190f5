<?php

class UserController extends SIANController {

    /**
     * @var string the default layout for the views. Defaults to '//layouts/main', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
    public $plural = "Usuarios";
    public $singular = "usuario";
    public $modelClass = 'User';
    public $viewClass = 'DpHumanUserIndex';
    public $modelPk = 'username';

    /**
     * Specifies the access control rules.
     * This method is used by the 'accessControl' filter.
     * @return array access control rules
     */
    public function accessRules() {
        return array(
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('password', 'myprofile', 'profile', 'sign'),
                'users' => array('@'),
            ),
            array('deny', // deny all users
                'users' => array('*'),
            ),
        );
    }

    public function actionPassword($type = null, $modal_id = null, $parent_id = null, $element_id = null) {

        $model = $this->loadPassword(Yii::app()->user->username);

        $this->title = "Cambiar clave de {$this->singular} '{$model->toString()}'";

        $modal_html = "";
        $content_html = "";
        $success = false;

        if (isset($_POST['User'])) {

            $model->attributes = $_POST['User'];

            if ($model->validate()) {
                $success = $this->secureSave($model);
            }
        } else {
            $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('modal_id' => $modal_id, 'parent_id' => $parent_id)), true, true);
        }

        if (!$success) {
            $content_html = $this->renderPartial('_password', array(
                'data' => array(
                    'modal_id' => $modal_id,
                    'model' => $model,
                )
                    ), true, true);
        } else {
            $element_id = 'success';
            $content_html = 'Se ha cambiado su clave con éxito';
        }

        echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $model);
    }

    public function actionMyProfile($type = null, $modal_id = null, $parent_id = null, $element_id = null) {

        $model = $this->loadMyProfile(Yii::app()->user->username);

        $this->title = "Mi perfil";

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array('modal_id' =>
                $modal_id,
                'parent_id' => $parent_id)
                ), true, true);

        //OBTENEMOS UN ARRAY DE TODO LOS ATRIBUTOS
        $groupDataProvider = (new DpUserGroup())->getDataProvider(false);

        $content_html = $this->renderPartial('_myprofile', array(
            'data' => array(
                'modal_id' => $modal_id,
                'groupDataProvider' => $groupDataProvider,
                'model' => $model,
            )
                ), true, true);
        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html
        ));
    }

    public function actionProfile($type = null, $modal_id = null, $parent_id = null, $element_id = null) {

        $model = $this->loadProfile(Yii::app()->user->username);

        $this->title = "Editar perfil de {$this->singular} '{$model->toString()}'";

        $modal_html = "";
        $content_html = "";
        $success = false;

        if (isset($_POST['User'])) {
            $model->attributes = $_POST['User'];

            if ($model->validate()) {
                $success = $this->secureSave($model);

                if ($model->hasChanged('email_address')) {
                    Yii::app()->user->setState('email_address', $model->email_address);
                }
                if ($model->hasChanged('phone_number')) {
                    Yii::app()->user->setState('phone_number', $model->phone_number);
                }
                if ($model->hasChanged('remember_filters')) {
                    Yii::app()->user->setState('remember_filters', $model->remember_filters);
                }
                if ($model->hasChanged('show_prices')) {
                    Yii::app()->user->setState('show_prices', $model->show_prices);
                }
            }
        } else {
            $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('modal_id' => $modal_id, 'parent_id' => $parent_id)), true, true);
        }

        if (!$success) {
            $content_html = $this->renderPartial('_profile', array(
                'data' => array(
                    'modal_id' => $modal_id,
                    'model' => $model,
                )
                    ), true, true);
        } else {
            $element_id = 'success';
            $content_html = 'Se han guardado los cambios de su perfil con éxito';
        }

        echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $model);
    }

    public function actionSign($type = null, $modal_id = null, $parent_id = null, $element_id = null) {

        $o_model = $this->loadSign(Yii::app()->user->username);

        $this->title = "Generar firma para {$this->singular} '{$o_model->toString()}'";

        //Obtenemos tiendas
        $a_stores = Store::get(Store::MODE_API);
        //Obtenemos redes
        $a_socials = Resource::model()->findAll([
            'condition' => 'owner = :owner AND owner_id = :owner_id AND type = :type',
            'params' => [
                ':owner' => Organization::OWNER,
                ':owner_id' => $this->getOrganization()->organization_id,
                ':type' => Resource::TYPE_SOCIAL
            ]
        ]);
        //Obtenemos recursos publicitarios
        $a_ads = Resource::model()->findAll([
            'condition' => 'owner = :owner AND owner_id = :owner_id AND type = :type',
            'params' => [
                ':owner' => Organization::OWNER,
                ':owner_id' => $this->getOrganization()->organization_id,
                ':type' => Resource::TYPE_AD
            ]
        ]);
        //Obtenemos logos
        $a_logos = SIANImage::getSignLogos();

        $success = true;

        $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('modal_id' => $modal_id, 'parent_id' => $parent_id)), true, true);

        $content_html = $this->renderPartial('_sign', array(
            'data' => array(
                'modal_id' => $modal_id,
                'model' => $o_model,
                'stores' => $a_stores,
                'socials' => $a_socials,
                'social_icons' => Resource::getSocialIcons(),
                'ads' => $a_ads,
                'ad_icons' => Resource::getAdIcons(),
                'ad_labels' => Resource::getAdLabels(),
                'logos' => $a_logos,
            )
                ), true, true);

        header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
        header("Cache-Control: post-check=0, pre-check=0", false);
        header("Pragma: no-cache");

        echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $o_model);
    }

    public function createData($model, $modal_id) {
        return array(
            'modal_id' => $modal_id,
            'model' => $model,
        );
    }

    public function loadPassword($p_s_username) {
        $model = User::model()->findByAttributes([
            'username' => $p_s_username
                ], [
            'select' => [
                'user_id', 'username', 'password'
            ]
        ]);
        $model->password = '';
        $model->scenario = User::SCENARIO_USER_PASSWORD;
        return $model;
    }

    //myprofile
    public function loadMyprofile($p_s_username) {

        return User::model()->with([
                    'person' => [
                        'select' => [
                            'P.person_id',
                            'P.identification_type',
                            'P.identification_number',
                            'P.person_name',
                        ],
                        'alias' => 'P'
                    ]
                ])->findByAttributes([
                    'username' => $p_s_username
                        ], [
                    'select' => [
                        'username', 'email_address', 'phone_number', 'show_prices'
                    ]
        ]);
    }

    public function loadProfile($p_s_username) {
        $model = User::model()->findByAttributes([
            'username' => $p_s_username
                ], [
            'select' => [
                'user_id', 'username', 'email_address', 'phone_number', 'remember_filters', 'show_prices'
            ]
        ]);
        $model->scenario = User::SCENARIO_USER_PROFILE;
        return $model;
    }

    public function loadSign($p_s_username) {

        return User::model()->with([
                    'person' => [
                        'select' => [
                            'P.identification_type',
                            'P.person_name',
                        ],
                        'alias' => 'P'
                    ]
                ])->findByAttributes([
                    'username' => $p_s_username
                        ], [
                    'select' => [
                        'username', 'email_address', 'phone_number', 'show_prices'
                    ]
        ]);
    }

    public function secureSave($model) {
        $success = false;

        try {
            //Iniciamos la transacción
            $this->beginTransaction($model);
            //Actualizamos el modelo   
            switch ($model->scenario) {
                case User::SCENARIO_USER_PASSWORD:
                    $success = $model->update(['password']);
                    break;
                case User::SCENARIO_USER_PROFILE:
                    Yii::app()->user->setState('remember_filters', $model->remember_filters);
                    $success = $model->update(['username', 'email_address', 'phone_number', 'remember_filters', 'show_prices']);
                    break;
            }
            //Confirmamos la transacción
            $this->commitTransaction($model);
        } catch (Exception $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($model);
            throw $ex;
        }

        return $success;
    }

}
