<?php

class SIANMovementPaymentView extends CWidget {

    public $id;
    public $dataProvider;
    public $direction = Scenario::DIRECTION_IN;
    //PRIVATE
    private $controller;
    private $total = 0.00;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //
        $this->dataProvider->pagination = false;

        foreach ($this->dataProvider->rawData as $item) {
            $this->total += isset($item->amount) ? $item->amount : 0;
        }
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<h5>Listado de " . ($this->direction == Scenario::DIRECTION_IN ? 'Cobros' : 'Pagos') . ":</h5>";

        $columns = [];
        $counter = 0;

        array_push($columns, array(
            'header' => '#',
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) use (&$counter) {
                return ++$counter;
            },
        ));

        array_push($columns, array(
            'header' => 'Fecha',
            'name' => 'payment_date',
            'type' => 'raw',
            'value' => function ($row) {
                return $row->payment_date;
            },
        ));

        array_push($columns, array(
            'header' => 'Tipo',
            'name' => 'type',
            'type' => 'raw',
            'value' => function ($row) {
                return $row->type;
            }
        ));

        array_push($columns, array(
            'header' => 'Caja/Cuenta',
            'headerHtmlOptions' => array('style' => 'width:30%'),
            'name' => 'cashbox_id',
            'type' => 'raw',
            'value' => function ($row) {
                return $row->cashbox->cashbox_name;
            }
        ));

        array_push($columns, array(
            'header' => '# Referencia',
            'headerHtmlOptions' => array('style' => 'text-align:center'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'name' => 'reference_number',
            'type' => 'raw',
            'value' => function ($row) {
                return $row->reference_number;
            }
        ));

        array_push($columns, array(
            'header' => '# Tarjeta',
            'headerHtmlOptions' => array('style' => 'text-align:center'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'name' => 'credit_card',
            'type' => 'raw',
            'value' => function ($row) {
                return $row->credit_card;
            },
            'footer' => '<b>Total:</b>',
            'footerHtmlOptions' => array('style' => 'text-align:right'),
        ));

        array_push($columns, array(
            'header' => 'Monto',
            'name' => 'amount',
            'headerHtmlOptions' => array('style' => 'text-align:right'),
            'htmlOptions' => array('style' => 'text-align:right'),
            'type' => 'raw',
            'value' => function ($row) {
                return $row->amount;
            },
            'footer' => number_format($this->total, 2),
            'footerHtmlOptions' => array('style' => 'text-align:right'),
        ));

        $gridParams = array(
            'id' => $this->id,
            'type' => 'hover condensed',
            'dataProvider' => $this->dataProvider,
            'enableSorting' => true,
            'selectableRows' => 0,
            'columns' => $columns,
            'template' => '{items}',
            'nullDisplay' => Strings::NONE,
        );
        $this->widget('application.widgets.USGridView', $gridParams);
    }

}
