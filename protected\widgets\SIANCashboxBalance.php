<?php

class SIANCashboxBalance extends CWidget {

    public $id;
    public $model;
    public $name = 'MultipleCashboxBalance';
    public $property = 'tempCashboxBalance';
    public $title = 'Verificación de Saldos';
    public $aclaration = null;
    public $disabled = false;
    public $readonly = false;
    public $removable = true;
    public $visible = true;
    public $zero_warning = '';
    //PRIVATE
    private $clientScript;
    private $controller;

    public function init() {

        $this->clientScript = Yii::app()->clientScript;
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //Recorremos los ítems
        $a_cashbox_Items = [];

        foreach ($this->model->{$this->property} as $o_cashbox_item) {
            $a_attributes = [];
            $a_attributes['cashbox_balance_id'] = $o_cashbox_item->cashbox_balance_id;
            $a_attributes['cashbox_id'] = $o_cashbox_item->cashbox_id;
            $a_attributes['cashbox_name'] = $o_cashbox_item->cashbox_name;
            $a_attributes['currency_name'] = $o_cashbox_item->currency_name;
            $a_attributes['currency'] = $o_cashbox_item->currency;
            $a_attributes['balance'] = $o_cashbox_item->balance;
            $a_attributes['errors'] = $o_cashbox_item->getAllErrors();
            $a_cashbox_Items[] = $a_attributes;
        }
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-cashbox-balance.js');

        //SCRIPTS
        $this->clientScript->registerScript($this->id, "

        var array = " . CJSON::encode($a_cashbox_Items) . " ;          

        //COUNT y LIMIT
        var divObj = $('#{$this->id}');
        divObj.data('count', 0);
        divObj.data('name', '{$this->name}');
        divObj.data('visible', " . CJSON::encode($this->visible) . ");
        divObj.data('readonly', " . CJSON::encode($this->readonly) . ");
        divObj.data('disabled', " . CJSON::encode($this->disabled) . ");
        divObj.data('removable', " . CJSON::encode($this->removable) . ");
        divObj.data('zero_warning', '{$this->zero_warning}');
            
        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANCashboxBalanceAddItem('{$this->id}', array[i]['cashbox_balance_id'], array[i]['cashbox_id'], array[i]['cashbox_name'], array[i]['currency'], array[i]['currency_name'], array[i]['balance'], array[i]['errors']);
            }
        }
        else
        {
            $('#{$this->id}').find('div.sian-cashbox_items').html('<p>" . Strings::NO_DATA . "</p>');
        }
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "{$this->title}" . (isset($this->aclaration) ? ' *' : ''),
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => $this->model->hasErrors($this->property) ? 'us-error' : '',
                'style' => $this->visible ? 'display:block;' : 'display:none;',
            )
        ));
        echo "<div class='sian-cashbox-balance-items'></div>";

        if (isset($this->aclaration)) {
            echo "<p><em>(*) {$this->aclaration}</em></p>";
        }
        $this->endWidget();
    }

}
