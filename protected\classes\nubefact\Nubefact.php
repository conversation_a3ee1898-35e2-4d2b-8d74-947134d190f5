<?php

class Nubefact
{
    private $path;
    private $token;
    public $operation;

    /**
     * *Tipos de operacion:*
     * OPERACIÓN 1: GENERAR FACTURAS, BOLETAS Y NOTAS CON JSON
     * OPERACIÓN 2: CONSULTAR FACTURAS, BOLETAS Y NOTAS CON JSON
     * OPERACIÓN 3: ANULAR FACTURAS, BOLETAS Y NOTAS CON JSON
     * OPERACIÓN 4: CONSULTAR ANULACIÓN DE FACTURAS, BOLETAS Y NOTAS CON JSON
     */

    // CODIGOS DE ERROR
    const ERROR_CODE_10 = 10; // No se pudo autenticar, token incorrecto o eliminado
    const ERROR_CODE_11 = 11; // La ruta o URL que estás usando no es correcta o no existe. Ingresa a tu cuenta en www.nubefact.com en la opción Api-Integración para verificar este dato
    const ERROR_CODE_12 = 12; // Solicitud incorrecta, la cabecera (Header) no contiene un Content-Type correcto
    const ERROR_CODE_20 = 20; // El archivo enviado no cumple con el formato establecido
    const ERROR_CODE_21 = 21; // No se pudo completar la operación, se acompaña el problema con un mensaje
    const ERROR_CODE_22 = 22; // Documento enviado fuera del plazo permitido
    const ERROR_CODE_23 = 23; // Este documento ya existe en NubeFacT
    const ERROR_CODE_24 = 24; // El documento indicado no existe o no fue enviado a NubeFacT
    const ERROR_CODE_40 = 40; // Error interno desconocido
    const ERROR_CODE_50 = 50; // Su cuenta ha sido suspendida
    const ERROR_CODE_51 = 51; // Su cuenta ha sido suspendida por falta de pago

    // MENSAJES DE ERROR

    // CÓDIGOS DE ESTADO HTTP
    // 200 //Operación exitosa
    // 400 //Solicitud incorrecta
    // 401 //No autorizado
    // 500 //error de servidor interno

    public function __construct($s_path, $s_token)
    {
        $this->path = $s_path;
        $this->token = $s_token;
    }

    public function sendDocument($a_data_document)
    {
        $data_json = json_encode($a_data_document);
        $response = [];

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->path);
            curl_setopt(
                $ch,
                CURLOPT_HTTPHEADER,
                array(
                    'Authorization: Token token="' . $this->token . '"',
                    'Content-Type: application/json',
                )
            );
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data_json);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response  = curl_exec($ch);
            curl_close($ch);
        } catch (\Throwable $th) {
            throw $th;
        }

        return $response;
    }
    
}
