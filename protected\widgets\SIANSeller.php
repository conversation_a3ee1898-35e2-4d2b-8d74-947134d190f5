<?php

class SIANSeller extends CWidget {

    //CONST
    public $id;
    public $form;
    public $model;
    public $items = [];
    public $readonly = false;
    public $disabled = false;
    public $required = false;
    public $onChange = '';
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        $(document).ready(function () {
            " . ($this->disabled || $this->readonly ? "USAutocompleteReadOnly('{$this->id}', true);" : "") . "
        });
        //Evento load porque se ejecuta cuando ya todo cargó
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->id,
            'model' => $this->model,
            'attribute' => 'seller_id',
            'useOnkeydown' => Yii::app()->controller->getOrganization()->globalVar->keyboard_search == 1 ? true : false,
            'autoChoose' => Yii::app()->controller->getOrganization()->globalVar->keyboard_search == 1 ? false : true,
            'view' => array(
                'model' => 'DpSeller',
                'attributes' => array(
                    array('name' => 'seller_id', 'width' => 0, 'types' => array('id', 'value'), 'hidden' => true, 'update' => "
                        var changeData = {
                                seller_code: seller_code,
                                identification_type: identification_type,
                                identification_name: identification_name,
                                identification_number: identification_number,
                                person_name: person_name
                            };
                            {$this->onChange}"),
                    array('name' => 'identification_type', 'width' => 0, 'hidden' => true),
                    array('name' => 'identification_name', 'width' => 0, 'hidden' => true),
                    array('name' => 'identification_number', 'width' => 0, 'hidden' => true),
                    array('name' => 'seller_code', 'width' => 30, 'types' => array('aux')),
                    array('name' => 'person_name', 'width' => 70, 'types' => array('text')),
                )
            ),
            'readonly' => $this->readonly,
            'maintenance' => array(
                'module' => 'commercial',
                'controller' => 'seller',
            )
        ));
    }

}
