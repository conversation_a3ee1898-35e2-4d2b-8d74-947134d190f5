<?php

class SIANWarehouseGrid extends CWidget {

    public $id;
    public $form;
    public $model;
    public $readonly = false;
    public $force_series = false;
    public $advanced = false;
    public $ifixed = WarehouseMovement::IFIXED;
    public $tfixed = WarehouseMovement::TFIXED;
    public $step = WarehouseMovement::STEP;
    public $max_value = 99999999;
    public $allow_duplicate_checkbox_id;
    public $onChangeCCDependence = ''; //Para hacer un puente
    public $weight_input_id; // input Peso Bruto
    public $number_packages_input_id; // input N° de bultos
    //PRIVATE
    private $controller;
    private $presentationMode;
    private $presentationUrl;
    private $stockUrl;
    private $explainStockUrl;
    private $autocomplete_id;
    private $item_aux_id;
    private $item_value_id;
    private $item_text_id;
    private $item_type_id;
    private $item_use_batch_id;
    private $item_pres_quantity_id;
    private $item_equivalence_id;
    private $item_allow_decimals_id;
    private $item_unit_stock_id;
    private $item_mixed_stock_label_id;
    private $item_bcost_id;
    private $item_max_bcost_pen_id;
    private $item_max_bcost_usd_id;
    private $item_series_id;
    private $add_button_id;
    private $preview_access;
    private $local_storage_name;
    private $global_use_batch;

    public function init() {

        //CONTROL
        if (!isset($this->model->movement->stock_mode)) {
            throw new Exception('Se necesita un modo de stock');
        }

        $this->controller = Yii::app()->controller;
        $this->presentationMode = SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES;
        $this->presentationUrl = $this->controller->createUrl("/movement/getPresentations");
        $this->stockUrl = $this->controller->createUrl("/movement/loadCostsAndStocks");
        $this->explainStockUrl = $this->controller->createUrl("/logistic/merchandise/explainStock");
        //GRID ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        // Variables para `peso` y `numero de bultos`
        $this->weight_input_id = isset($this->weight_input_id) ? $this->weight_input_id : Yii::app()->controller->getServerId();
        $this->number_packages_input_id = isset($this->number_packages_input_id) ? $this->number_packages_input_id : Yii::app()->controller->getServerId();
        $this->allow_duplicate_checkbox_id = $this->controller->getServerId();

        //PRIVATE
        $this->autocomplete_id = $this->controller->getServerId();
        $this->item_aux_id = $this->controller->getServerId();
        $this->item_value_id = $this->controller->getServerId();
        $this->item_text_id = $this->controller->getServerId();
        $this->item_type_id = $this->controller->getServerId();
        $this->item_use_batch_id = $this->controller->getServerId();
        $this->item_pres_quantity_id = $this->controller->getServerId();
        $this->item_equivalence_id = $this->controller->getServerId();
        $this->item_allow_decimals_id = $this->controller->getServerId();
        $this->item_bcost_id = $this->controller->getServerId();
        $this->item_max_bcost_pen_id = $this->controller->getServerId();
        $this->item_max_bcost_usd_id = $this->controller->getServerId();
        $this->item_unit_stock_id = $this->controller->getServerId();
        $this->item_mixed_stock_label_id = $this->controller->getServerId();
        $this->item_series_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();
        $this->local_storage_name = 'datos_' . $this->id;
        $this->global_use_batch = Yii::app()->controller->getOrganization()->globalVar->use_batch == GlobalVar::BATCH_USE_BATCH_NO ? 0 : 1;

        $product_ids = [];
        foreach ($this->model->tempItems as $item) {
            $product_ids[] = $item->itemObj->product_id;
        }

        $productItems = [];
        //Si hay productos...
        if (count($product_ids) > 0) {

            $presentationItems = SpGetProductPresentations::getAssociative($this->presentationMode, $product_ids, $this->controller->getOrganization()->globalVar->display_currency);

            //PRODUCT ITEMS
            foreach ($this->model->tempItems as $item) {


                //Actualizamos los máximos según el tipo de cambio del formulario
                if ($this->model->movement->currency == Currency::PEN) {
                    $item->max_bcost_usd = $item->max_bcost_pen / $this->model->movement->exchange_rate;
                } else {
                    $item->max_bcost_pen = $item->max_bcost_usd * $this->model->movement->exchange_rate;
                }

                $attributes = [];
                $attributes['product_id'] = $item->itemObj->product_id;
                $attributes['item_type_id'] = $item->itemObj->item_type_id;
                $attributes['product_name'] = $item->itemObj->product_name;
                $attributes['pres_quantity'] = $item->itemObj->pres_quantity;
                $attributes['unit_stock'] = $item->itemObj->unit_stock;
                $attributes['mixed_stock'] = $item->itemObj->mixed_stock;
                $attributes['bcost'] = round($item->{"bcost_{$this->model->movement->currency}"}, $this->ifixed);
                $attributes['max_bcost_pen'] = round($item->max_bcost_pen, $this->ifixed);
                $attributes['max_bcost_usd'] = round($item->max_bcost_usd, $this->ifixed);
                $attributes['bnet'] = round($item->{"bnet_{$this->model->movement->currency}"}, $this->tfixed);
                $attributes['series'] = $item->arraySeries;
                $attributes['serialized'] = $item->serialized;
                $attributes['use_batch'] = $item->use_batch;
                $attributes['equivalence'] = $item->itemObj->equivalence;
                $attributes['allow_decimals'] = $item->itemObj->allow_decimals;
                $attributes['presentationItems'] = $presentationItems[$item->itemObj->product_id];

                $attributes['parent_item_id'] = isset($item->itemObj->parent_item_id) ? $item->itemObj->parent_item_id : '';
                //Para enlace entre productos
                $attributes['parent_route'] = isset($item->itemObj->parent_route) ? $item->itemObj->parent_route : '';
                $attributes['quantity_item_id'] = isset($item->itemObj->quantity_item_id) ? $item->itemObj->quantity_item_id : '';
                $attributes['total_item_id'] = isset($item->itemObj->total_item_id) ? $item->itemObj->total_item_id : '';
                $attributes['batch_details'] = [];

                if ($this->global_use_batch == 1) {
                    foreach ($item->tempBatchDetails as $batchDetail) {
                        $attributes2 = [];
                        $attributes2['batch_id'] = $batchDetail->batch_id;
                        $attributes2['batch_code'] = $batchDetail->batch_code;
                        $attributes2['direction'] = $batchDetail->direction;
                        $attributes2['unit_stock'] = $batchDetail->unit_stock;
                        $attributes2['pres_stock'] = $batchDetail->pres_stock;
                        $attributes2['pres_stock_label'] = $batchDetail->pres_stock_label;
                        $attributes2['pres_quantity'] = $batchDetail->pres_quantity;
                        $attributes2['expiration_date'] = $batchDetail->expiration_date;
                        $attributes['batch_details'][] = $attributes2;
                    }
                }
                $attributes['errors'] = $item->getAllErrors();

                $productItems[] = $attributes;
            }
        }

        //ACCESS
        $this->preview_access = $this->controller->checkRoute('/logistic/product/preview');
        //ADVANCED: si es un scenario de entrada obligatoriamente tiene que ser avanzado
        $this->advanced = $this->model->movement->scenario->direction == Scenario::DIRECTION_IN ? true : $this->advanced;
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-warehouse-grid.js');
        SIANAssets::registerScriptFile('js/sian-warehouse-general-grid.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        window.hasPresentation = 0;
        var grid = $('#{$this->id}');
        grid.data('view-access', " . json_encode($this->preview_access) . ");//ACCESS
        grid.data('view-access-batch', '{$this->global_use_batch}');//ACCESS BATCH
        grid.data('view-url', '{$this->controller->createUrl('/logistic/product/preview')}');//URL        
        grid.data('view-url-batch', '{$this->controller->createUrl('/logistic/batch/batchMovement')}');//URL BATCH
        grid.data('get-url-batch-stock', '{$this->controller->createUrl("/logistic/batch/getBatchStock")}');
        grid.data('auto-batch-output', '" . Yii::app()->controller->getOrganization()->globalVar->auto_batch_output . "');
        grid.data('order-batch', '" . Yii::app()->controller->getOrganization()->globalVar->order_batch_output . "');
        grid.data('currency', '{$this->model->movement->currency}');
        grid.data('exchange_rate', '{$this->model->movement->exchange_rate}');
        grid.data('direction', '{$this->model->movement->direction}');
        grid.data('route', '{$this->model->movement->route}');
        grid.data('max_value', {$this->max_value});
        grid.data('ifixed', {$this->ifixed});
        grid.data('stockUrl', '{$this->stockUrl}');
        grid.data('explainStockUrl', '{$this->explainStockUrl}');
        grid.data('stock_mode', {$this->model->movement->stock_mode});
        grid.data('exclude_id', " . CJSON::encode($this->model->movement->kardex_unlock_exclude_id) . ");
        grid.data('allow_duplicate', $('#{$this->allow_duplicate_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('independent_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INDEPENDENT . "');
        grid.data('inherit_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INHERIT . "');
        grid.data('local_storage_name', '{$this->local_storage_name}');
        grid.data('count', 0);//COUNT
        grid.data('ready', 0);
        grid.data('emission_date', '" . SIANTime::formatDate(SIANTime::today()) . "');
        grid.data('weight_input_id', '{$this->weight_input_id}');
        grid.data('number_packages_input_id', '{$this->number_packages_input_id}');
        if(isset({$this->model->warehouse_id})){
            grid.data('warehouse_id', {$this->model->warehouse_id});
        }
        localStorage.clear();         
        
        //PRODUCTOS
        var array = " . CJSON::encode($productItems) . ";
        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                var row_id = SIANWarehouseGridAddItem('{$this->id}', array[i]['product_id'], array[i]['item_type_id'], array[i]['product_name'], array[i]['unit_stock'], array[i]['mixed_stock'], array[i]['pres_quantity'], array[i]['bcost'], array[i]['max_bcost_pen'], array[i]['max_bcost_usd'], array[i]['serialized'], array[i]['series'], array[i]['equivalence'], array[i]['allow_decimals'], array[i]['presentationItems'], array[i]['parent_route'], array[i]['quantity_item_id'], array[i]['total_item_id'], array[i]['use_batch'], array[i]['errors']);
                "
                . ($this->global_use_batch == 1 ? "   
                SIANWarehouseGeneralGridFormatJSONLocalStorage('{$this->id}', row_id, array[i]['batch_details']);                    
                if(array[i]['use_batch'] == 1 && array[i]['batch_details'].length == 0){
                    SIANWarehouseGeneralGridBatch('{$this->id}', row_id);                    
                }
                " : "")
                . "
            }
        }
        else
        {
            $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
        }

        //UPDATE
        SIANWarehouseGridUpdate('{$this->id}');
        unfocusable();        

        //CURRENCY SYMBOL
        $('span.currency-symbol').text('" . Currency::getSymbol($this->model->movement->currency) . "');
        
        //SI CAMBIA LA MONEDA
        $('#{$this->id}').data('changeCurrency', function(currency){

            var table = $('#{$this->id}');
            var ifixed = table.data('ifixed');
            var tfixed = table.data('tfixed');
            var exchange_rate = table.data('exchange_rate');

            table.find('tr.sian-warehouse-grid-item').each(function(index) {

                var item = $(this);

                var max_bcost_pen = item.find('input.sian-warehouse-grid-item-max-bcost-pen').floatVal({$this->ifixed});
                var max_bcost_usd = item.find('input.sian-warehouse-grid-item-max-bcost-usd').floatVal({$this->ifixed});

                //Actualizamos montos
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-warehouse-grid-item-bcost').toPen(exchange_rate, ifixed, {max: max_bcost_pen});
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-warehouse-grid-item-bcost').toUsd(exchange_rate, ifixed, {max: max_bcost_usd});
                    break;
                }
            });

            table.data('currency', currency);
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            //Limpiamos
            {$this->id}Clear();
            //Actualizar montos
            SIANWarehouseGridUpdateAmounts('{$this->id}');
        });    
        
        //SI CAMBIA OPERACION
        $('#{$this->id}').data('changeOperation', function(changeData){
            var table = $('table#{$this->id}');
            //GUARDAMOS INFO DE OPERACIÓN
            table.data('operationChangeData', changeData);

            SIANWarehouseGridUpdateAmounts('{$this->id}');
        });

        $('#{$this->id}').data('changeEmission', function(changeData){
            var table = $('table#{$this->id}');
            //Seteamos
            table.data('emission_date', changeData.emission_date);
            //A cada items
            table.find('a.sian-warehouse-grid-item-mixed-stock').data('emission_date', changeData.emission_date); 
            $('#{$this->item_mixed_stock_label_id}').data('emission_date', changeData.emission_date);            
            
            //Reseteamos el autocompletador para evitar que hayan quedado datos en el mismo
            USAutocompleteReset('{$this->autocomplete_id}');

            var warehouse_id = $('#{$this->id}').data('warehouse_id');
            if(warehouse_id !== undefined && warehouse_id.length > 0)
            {
                SIANWarehouseGridLoadCostsAndStocks(warehouse_id, changeData.emission_date, '{$this->id}');
                " . ($this->global_use_batch == 1 && $this->model->movement->direction == Scenario::DIRECTION_OUT ? "  
                
                    grid.find('tr.sian-warehouse-grid-item').each(function (index) {
                    
                    var rowObj = $(this);
                    var use_batch = rowObj.find('input.sian-warehouse-grid-item-use-batch').val();
                    if(use_batch == 1){
                        var isReady = table.data('ready');
                        console.log('listo: ' + isReady);
                        if(isReady == 1){
                            SIANWarehouseGeneralGridBatch('{$this->id}', rowObj.attr('id'));
                        }                        
                    }
                });
                
                " : "") . " 
            }
            
            $('#{$this->id}').data('notifyStockMode')();
        });
        
        $('#{$this->id}').data('changeExchange', function(exchange_rate){

            var table = $('table#{$this->id}');
            //Seteamos
            table.data('exchange_rate', exchange_rate);                 
            //Obtenemos
            var currency = table.data('currency');
            var ifixed = table.data('ifixed');
            var tfixed = table.data('tfixed');

            table.find('tr.sian-warehouse-grid-item').each(function(index) {
            
                var item = $(this);

                var max_bcost_pen = item.find('input.sian-warehouse-grid-item-max-bcost-pen').floatVal({$this->ifixed});
                var max_bcost_usd = item.find('input.sian-warehouse-grid-item-max-bcost-usd').floatVal({$this->ifixed});

                //Cambiamos los balances
                switch('{$this->controller->getOrganization()->globalVar->display_currency}')
                {
                    case '" . Currency::PEN . "':
                        max_bcost_usd = max_bcost_pen / exchange_rate;
                    break;
                    case '" . Currency::USD . "':
                        max_bcost_pen = max_bcost_usd * exchange_rate;
                     break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
                
                //Cambiamos mínimos
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-warehouse-grid-item-bcost').floatAttr('max', {$this->ifixed}, max_bcost_pen);
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-warehouse-grid-item-bcost').floatAttr('max', {$this->ifixed}, max_bcost_usd);
                     break;
                    default:
                    break;
                }

                //Seteamos balances
                item.find('input.sian-warehouse-grid-item-max-bcost-pen').floatVal({$this->ifixed}, max_bcost_pen);
                item.find('input.sian-warehouse-grid-item-max-bcost-usd').floatVal({$this->ifixed}, max_bcost_usd);
            });
            
            //Limpiamos
            {$this->id}Clear();
            //Actualizamos montos
            SIANWarehouseGridUpdateAmounts('{$this->id}');
        });
        
        $('body').on('change', '#{$this->item_equivalence_id}', function(e) {
            var product_id = USAutocompleteField('{$this->autocomplete_id}', 'value');
            SIANWarehouseGridLoadCostAndStock('{$this->id}', product_id, $('#{$this->item_equivalence_id}'), $('#{$this->item_unit_stock_id}'), null, $('#{$this->item_mixed_stock_label_id}'), $('#{$this->item_bcost_id}'), $('#{$this->item_max_bcost_pen_id}'), $('#{$this->item_max_bcost_usd_id}'));            
        });
       
        $('body').on('change', '#{$this->id} select.sian-warehouse-grid-item-equivalence', function(e) {

            var row = $(this).closest('tr');
            
            SIANWarehouseGridGetAndLoadCostAndStock('{$this->id}', row.attr('id'));
            
            //Actualizar montos
            SIANWarehouseGridUpdateAmounts('{$this->id}');"
                . ($this->global_use_batch == 1 ?
                        "var use_batch = $(row).find('input.sian-warehouse-grid-item-use-batch').val();
            
            if(use_batch == 1){                   
                var isReady = $('#{$this->id}').data('ready');
                if(isReady == 1){
                    SIANWarehouseGeneralGridBatch('{$this->id}', row.attr('id'));
                }
            }" : "")
                . "
       });
        
        $('body').on('click', '#{$this->add_button_id}', function(e) {
            
            if(window.hasPresentation==1){
            
                var currency = $('#{$this->id}').data('currency');

                var product_id = $('#{$this->item_value_id}').val();
                var item_type_id = $('#{$this->item_type_id}').val()
                var product_name = $('#{$this->item_text_id}').val();
                var pres_quantity = ($('#{$this->item_pres_quantity_id}').val().length > 0)? parseFloat($('#{$this->item_pres_quantity_id}').val()) : 1;
                var equivalence = $('#{$this->item_equivalence_id}').double2();
                var allow_decimals = $('#{$this->item_allow_decimals_id}').integer();
                var unit_stock = $('#{$this->item_unit_stock_id}').val();
                var mixed_stock = $('#{$this->item_mixed_stock_label_id}').text();
                var presentationItems = $('#{$this->item_equivalence_id}').data('presentationItems');
                var bcost = ($('#{$this->item_bcost_id}').val().length > 0) ? parseFloat($('#{$this->item_bcost_id}').val()) : 0;
                var max_bcost_pen = $('#{$this->item_max_bcost_pen_id}').floatVal({$this->ifixed});
                var max_bcost_usd = $('#{$this->item_max_bcost_usd_id}').floatVal({$this->ifixed});
                var serialized = $('#{$this->item_series_id}').hasAttr('disabled') ? 0 : 1;               
                var series = $('#{$this->item_series_id}').val().split(\"\\n\");
                var direction = $('#{$this->id}').data('direction');   
                var use_batch = $('#{$this->item_use_batch_id}').val();
                var url_batch_stock = $('#{$this->id}').data('get-url-batch-stock'); 
                var warehouse_id = $('#{$this->id}').data('warehouse_id');
                var order_batch = $('#{$this->id}').data('order-batch');
                    
                if (product_id.length === 0)
                {
                    $('#{$this->item_aux_id}').focus();
                    return;
                }
                
                if(isNaN(equivalence)){
                    $('#{$this->item_equivalence_id}').notify('Debe elegir una presentación!', 
                        {   className: 'warn',
                            showDuration: 50,
                            hideDuration: 50,
                            autoHideDelay: 5000
                        }
                    );
                    $('#{$this->item_equivalence_id}').focus().select();
                    return;
                }

                if (direction == '" . Scenario::DIRECTION_OUT . "' && (pres_quantity * equivalence) > unit_stock)
                {
                    bootbox.alert(us_message('La cantidad elegida es mayor que la que hay en stock!', 'warning'));
                    $('#{$this->item_pres_quantity_id}').focus();
                    return;
                }

                //PRECIO SUPERIOR AL MAXIMO
                var max_error_message = false;

                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        if (bcost > max_bcost_pen)
                        {
                            max_error_message = 'El costo no puede ser mayor que ' + max_bcost_pen;
                        }
                    break;
                    case '" . Currency::USD . "':
                        if (bcost > max_bcost_usd)
                        {
                            max_error_message = 'El costo no puede ser mayor que ' + max_bcost_usd;

                        }
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }

                if (max_error_message)
                {
                    bootbox.alert(us_message(max_error_message, 'warning'));
                    $('#{$this->item_bcost_id}').focus();
                    return;
                }

                var row_id = SIANWarehouseGridAddItem('{$this->id}', product_id, item_type_id, product_name, unit_stock, mixed_stock, pres_quantity, bcost, max_bcost_pen, max_bcost_usd, serialized, series, equivalence, allow_decimals, presentationItems, '', '', '', use_batch, []);
                "
                . ($this->global_use_batch == 1 ? "                
                if(use_batch == 1){
                    SIANWarehouseGeneralGridBatch('{$this->id}', row_id);                    
                }
                " : "") . "
                {$this->id}Clear();

                //FOCUS
                $('#{$this->item_aux_id}').focus();
                SIANWarehouseGridUpdate('{$this->id}');
                unfocusable();
            }
        });
        
        //Para hacer reordenable las filas
//        $(function() {
//            $('#{$this->id}').find('tbody').sortable({
//                stop: function( event, ui ) {
//                    SIANWarehouseGridUpdateAmounts('{$this->id}');
//                }
//            });
//        });

        function {$this->id}Clear()
        {
            USAutocompleteReset('{$this->autocomplete_id}');
            $('#{$this->item_equivalence_id}').html('<option value>" . Strings::SELECT_OPTION . "</option>'); 
            $('#{$this->item_allow_decimals_id}').val(0);
            $('#{$this->item_bcost_id}').val(0);
            $('#{$this->item_max_bcost_pen_id}').val(" . GlobalVar::MAX . ");
            $('#{$this->item_max_bcost_usd_id}').val(" . GlobalVar::MAX . ");
            $('#{$this->item_unit_stock_id}').val(null);
            $('#{$this->item_mixed_stock_label_id}').text(0);
            USLinkStatus('{$this->item_mixed_stock_label_id}', false, {id: null, equivalence: null});     
            $('#{$this->item_series_id}').val('');
            $('#{$this->item_series_id}').prop('disabled', true);
            $('#{$this->item_aux_id}').focus().select();
            
        }
    
        function {$this->id}GetPresentations(product_id, equivalence)
        {
            if(product_id != undefined)
            {

                $.ajax({
                    type: 'post',
                    url: '{$this->presentationUrl}',
                    data: {
                        mode: {$this->presentationMode},
                        product_ids: [product_id],
                        currency: '" . $this->controller->getOrganization()->globalVar->display_currency . "'
                    },
                    beforeSend: function (xhr) {
                        window.active_ajax++;
                        //Ocultamos los tooltip
                        $('div.ui-tooltip').remove();
                    },                      
                    success: function(data) {
                    
                        $.each(data, function(index, item) {
                            SIANWarehouseGridFillPresentations('{$this->item_equivalence_id}', item, equivalence);
                            SIANWarehouseGridSetCostGeneral('{$this->model->movement->direction}', $('#{$this->item_equivalence_id}'), $('#{$this->item_bcost_id}'), $('#{$this->item_max_bcost_pen_id}'), $('#{$this->item_max_bcost_usd_id}'), $('#{$this->id}').floatData('exchange_rate', 3), {$this->ifixed});
                        });     
                        
                        window.hasPresentation = 1;
                        window.active_ajax--;
                    },
                    error: function(request, status, error) { // if error occured
                        bootbox.alert(us_message(request.responseText, 'error'));
                        window.active_ajax--;
                    },
                    dataType: 'json'
                });
            }
        }
        
        //En teoría, al iniciar el formulario el campo de almacén sufre un cambio que hace que se recalculen los stocks y costos
        //Por lo tanto no es necesario llamar la función en 'ready'
        $('#{$this->id}').data('changeWarehouse', function(changeData){
            //Reseteamos el autocompletador para evitar que hayan quedado datos en el mismo
            USAutocompleteReset('{$this->autocomplete_id}');
            var grid = $('#{$this->id}');
            //Seteamos
            grid.data('warehouse_id', changeData.warehouse_id);
            grid.data('warehouse_name', changeData.warehouse_name);
            grid.find('a.sian-warehouse-grid-item-mixed-stock').data('warehouse_id', changeData.warehouse_id);             
            $('#{$this->item_mixed_stock_label_id}').data('warehouse_id', changeData.warehouse_id);            
            //Obtenemos
            var emission_date = grid.data('emission_date');
            if(changeData.warehouse_id !== 'undefined' && changeData.warehouse_id.length > 0 && emission_date)
            {
                SIANWarehouseGridLoadCostsAndStocks(changeData.warehouse_id, emission_date, '{$this->id}');
                " . ($this->global_use_batch == 1 && $this->model->movement->direction == Scenario::DIRECTION_OUT ? "  
                
                    grid.find('tr.sian-warehouse-grid-item').each(function (index) {
                    
                    var rowObj = $(this);
                    var use_batch = rowObj.find('input.sian-warehouse-grid-item-use-batch').val();
                    if(use_batch == 1){                        
                        var isReady = $('#{$this->id}').data('ready');
                        if(isReady == 1){
                            SIANWarehouseGeneralGridBatch('{$this->id}', rowObj.attr('id'));
                        }
                    }
                });
                
                " : "") . "                
            }
            
            $('#{$this->id}').data('notifyStockMode')();
        });
        
        $('#{$this->id}').data('notifyStockMode', function(){
            var grid = $('#{$this->id}');
            //Obtenemos
            var emission_date = grid.data('emission_date');
            var warehouse_id = grid.data('warehouse_id');
            var warehouse_name = grid.data('warehouse_name');
            var stock_message = 'Se mostrará el stock {$this->model->movement->getStockModeLabel()} en el almacén por defecto. Seleccione un almacén para mostrar el stock en ese almacén.';
            if(!isBlank(warehouse_id) && !isBlank(warehouse_name) && !isBlank(emission_date))
            {
                stock_message = 'Se mostrará el stock {$this->model->movement->getStockModeLabel()} en \'' + warehouse_name + '\'';            
            }
            
            $('#{$this->autocomplete_id}').notify(stock_message, 
                    {   className: 'info',
                        showDuration: 50,
                        hideDuration: 50,
                        autoHideDelay: 5000
                    }
                );  
        });        
        
        $('#{$this->id}').data('changeCCDependence', function(operationChangeData){
            {$this->onChangeCCDependence};
        });
        
        $(document).ready(function() {               
            console.log('inicio');
        });
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Mercaderías (stock y costo a la fecha de emisión elegida)',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempItems') ? 'us-error' : ''
            )
        ));

        if (!$this->readonly) {

            $a_attributes = [];
            $a_attributes[] = array('name' => 'product_id', 'width' => 10, 'types' => array('id', 'value', 'aux'), 'not_in' => "window.sianWarehouseGridDynamicIds");
            $a_attributes[] = array('name' => 'item_type_id', 'search' => false, 'hidden' => true, 'update' => "$('#{$this->item_type_id}').val(item_type_id);");
            $a_attributes[] = array('name' => 'barcode', 'width' => 20);
            $a_attributes[] = array('name' => 'product_name', 'width' => 30, 'types' => array('text'));
            $a_attributes[] = array('name' => 'model', 'width' => 10);
            $a_attributes[] = array('name' => 'part_number', 'hidden' => true);
            $a_attributes[] = array('name' => 'serialized', 'search' => false, 'hidden' => true, 'update' => "$('#{$this->item_series_id}').prop('disabled', serialized != 1);");
            $a_attributes[] = array('name' => 'equivalence', 'search' => false, 'hidden' => true);
            $a_attributes[] = array('name' => 'use_batch', 'search' => false, 'hidden' => true);
            $a_attributes[] = array('name' => 'allow_decimals', 'hidden' => true, 'update' => "
                                $('#{$this->item_allow_decimals_id}').val(allow_decimals);
                                $('#{$this->item_pres_quantity_id}').allowDecimals(allow_decimals == 1 ? 2 : 0);
                            ", 'search' => false);
            $a_attributes[] = array('name' => 'unit_stock', 'search' => false, 'hidden' => true, 'update' => "$('#{$this->item_unit_stock_id}').val(unit_stock);");
            $a_attributes[] = array('name' => 'stock', 'width' => 5, 'search' => false);
            $a_attributes[] = array('name' => 'measure_name', 'width' => 10, 'search' => false);
            $a_attributes[] = array('name' => 'mixed_stock', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->item_mixed_stock_label_id}').text(mixed_stock); USLinkStatus('{$this->item_mixed_stock_label_id}', true, {id: product_id, equivalence: equivalence})");
            $a_attributes[] = array('name' => 'currency_name', 'search' => false, 'width' => 5);
            $a_attributes[] = array('name' => 'unit_cost', 'search' => false, 'hidden' => true, 'update' => "$('#{$this->item_bcost_id}').data('unit_cost', unit_cost);");
            $a_attributes[] = array('name' => 'cost', 'search' => false, 'width' => 10);

            echo "<div class='row'>";
            $col = $this->advanced ? 5 : 6;
            echo "<div class='col-lg-{$col} col-md-{$col} col-sm-{$col} col-xs-12'>";
            echo $this->widget('application.widgets.USAutocomplete', array(
                'id' => $this->autocomplete_id,
                'label' => 'Mercadería',
                'name' => 'newItem[product_id]',
                'aux_id' => $this->item_aux_id,
                'value_id' => $this->item_value_id,
                'text_id' => $this->item_text_id,
                'view' => array(
                    'model' => 'DpAllMerchandisePresentations',
                    'scenario' => DpAllMerchandisePresentations::SCENARIO_WITH_STOCK,
                    'attributes' => $a_attributes,
                    'params' => "{
                        disable_filter_type: 1,
                        date: function () { 
                            return $('#{$this->id}').data('emission_date'); 
                        },
                        warehouse_id: function () { 
                            return $('#{$this->id}').data('warehouse_id'); 
                        },
                        stock_mode: {$this->model->movement->stock_mode}
                    }",
                ),
                'maintenance' => array(
                    'module' => 'logistic',
                    'controller' => 'merchandise',
                    'buttons' => array(
                        'preview' => array(
                            'access' => $this->preview_access,
                        ),
                        'create' => [],
                        'update' => [],
                        'delete' => [],
                    ),
                ),
                'onreset' => "
                    $('#{$this->item_type_id}').val('');
                    $('#{$this->item_pres_quantity_id}').val(1);
                    $('#{$this->item_pres_quantity_id}').allowDecimals(0);    
                    $('#{$this->item_equivalence_id}').val(null);
                    $('#{$this->item_bcost_id}').val(0);
                    $('#{$this->item_max_bcost_pen_id}').val(" . GlobalVar::MAX . ");
                    $('#{$this->item_max_bcost_usd_id}').val(" . GlobalVar::MAX . ");
                    $('#{$this->item_unit_stock_id}').val(0);
                    $('#{$this->item_mixed_stock_label_id}').text(0);
                    $('#{$this->item_series_id}').val('');
                    $('#{$this->item_series_id}').prop('disabled', true);
                    $('#{$this->item_aux_id}').focus().select();
                    $('#{$this->item_use_batch_id}').val('');
                    window.hasPresentation = 0;
                        ",
                'onsearch' => "
                        var warehouse_id = $('#{$this->id}').data('warehouse_id');
                        var emission_date = $('#{$this->id}').data('emission_date');
                            
                        if(typeof emission_date == 'undefined' || emission_date.trim().length === 0)
                        {
                            bootbox.alert(us_message('Aun no carga el campo de fecha de emisión, espere un segundo!', 'warning'));
                            return false;
                        }

                        if(typeof warehouse_id == 'undefined' || warehouse_id.trim().length === 0)
                        {
                            bootbox.alert(us_message('Seleccione un almacén primero!', 'warning'));
                            return false;
                        }
                        ",
                'onselect' => "
                    window.hasPresentation = 0; 
                    $('#{$this->item_use_batch_id}').val(use_batch);
                    {$this->id}GetPresentations(product_id, equivalence);
                ",
                    ), true);

            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->item_unit_stock_id,
                'disabled' => true,
            ));
            echo CHtml::label('Stock', $this->item_mixed_stock_label_id);
            echo "<p>" . $this->widget('application.widgets.USLink', array(
                'id' => $this->item_mixed_stock_label_id,
                'route' => '/logistic/merchandise/explainStock',
                'label' => '0',
                'title' => '¿Por qué veo este stock?',
                'class' => 'form',
                'data' => array(
                    'stock_mode' => $this->model->movement->stock_mode,
                    'emission_date' => $this->model->movement->emission_date,
                    'warehouse_id' => $this->model->warehouse_id,
                    'exclude_id' => $this->model->movement->kardex_unlock_exclude_id,
                ),
                'visible' => false
                    ), true) . "</p>";
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
            echo SIANForm::numberFieldNonActive('Cantidad', null, 1, array(
                'id' => $this->item_pres_quantity_id,
                'class' => 'enterTab',
                'style' => 'text-align:right'
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-6'>";
            echo SIANForm::dropDownListNonActive('Pres.', null, null, [], array(
                'id' => $this->item_equivalence_id,
                'empty' => Strings::SELECT_OPTION,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->item_allow_decimals_id,
            ));
            echo "</div>";

            echo CHtml::hiddenField(null, GlobalVar::MAX, array(
                'id' => $this->item_max_bcost_pen_id,
            ));
            echo CHtml::hiddenField(null, GlobalVar::MAX, array(
                'id' => $this->item_max_bcost_usd_id,
            ));
            echo CHtml::hiddenField(null, '', array(
                'id' => $this->item_type_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->item_use_batch_id,
            ));
            if ($this->advanced) {
                echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
                echo SIANForm::numberFieldNonActive("Costo <span class='currency-symbol'></span>", null, 0, array(
                    'id' => $this->item_bcost_id,
                    'class' => "us-double{$this->ifixed} enterTab",
                    'step' => $this->step,
                    'min' => 0,
                    'style' => 'text-align:right',
                    'disabled' => $this->model->movement->scenario->direction == Scenario::DIRECTION_OUT,
                ));
                echo "</div>";
            } else {
                echo CHtml::hiddenField(null, null, array(
                    'id' => $this->item_bcost_id,
                ));
            }

            $col = 2;
            //Si NO es abanzado le aumentamos 1
            if (!$this->advanced) {
                $col++;
            }
            echo "<div class='col-lg-{$col} col-md-{$col} col-sm-{$col} col-xs-12'>";
            echo SIANForm::textAreaNonActive('Series', null, null, array('id' => $this->item_series_id,
                'disabled' => true
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-4 col-xs-12 text-center'>";
            echo CHtml::label('Agregar', $this->add_button_id, []);
            echo "<br/>";
            $this->widget('application.widgets.USButton', array(
                'id' => $this->add_button_id,
                'context' => 'primary',
                'icon' => 'fa fa-lg fa-plus white',
                'size' => 'default',
                'title' => 'Añadir',
                'block' => true,
            ));
            echo "</div>";
            echo "</div>";
            echo "<hr>";
        }

        echo CHtml::tag('table', array(
            'id' => $this->id,
            'class' => 'table table-condensed table-hover',
            'data-ifixed' => $this->ifixed,
            'data-tfixed' => $this->tfixed,
            'data-step' => $this->step,
            'data-currency' => $this->model->movement->currency,
            'data-direction' => $this->model->movement->scenario->direction,
            'data-readonly' => $this->readonly,
            'data-force_series' => $this->force_series,
            'data-advanced' => $this->advanced,
                ), $this->renderTable(), true);

        echo "<div class='row'>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->allow_duplicate_checkbox_id,
            'model' => $this->model,
            'attribute' => 'allow_duplicate',
            'htmlOptions' => array(
                'readonly' => $this->readonly,
                'onchange' => "      
                    var allow_duplicate = $(this).prop('checked') ? 1 : 0;
                    $('#{$this->id}').data('allow_duplicate', allow_duplicate);
                    SIANWarehouseGridGetProductIds('{$this->id}');
                ",
            )
                ), true);
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo "</div>";
        echo "</div>";
        //
        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12' >";
        echo "<p><b>(*)</b> El stock mostrado es el stock <b>{$this->model->movement->getStockModeLabel()}</b> a la fecha de emisión elegida</p>";
        echo "<p><b>(**)</b> Todos los montos están sin IGV</p>";
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

    private function renderTable() {

        $width = 25;
        //Si NO es avanzado aumentamos 20;
        if (!$this->advanced) {
            $width += 20;
        }

        $colspan = 5;
        //Si es salida aumentamos 1;
        if ($this->model->movement->scenario->direction == Scenario::DIRECTION_OUT) {
            $colspan++;
        }

        //Si es advanced aumentamos 2;
        if ($this->advanced) {
            $colspan += 2;
        }

        $html = '';
        $html .= "<thead>";
        $html .= "<tr>";
        $html .= "<th width='4%'>#</th>";
        $html .= "<th width='7%'>ID</th>";
        $html .= "<th width='{$width}%'>Nombre producto</th>";
        $html .= "<th width='7%' style='text-align:right;'>Stock*</th>";
        $html .= "<th width='7%' style='text-align:right;'>Cantidad</th>";
        $html .= "<th width='10%' style='text-align:right;'>Pres.</th>";
        $html .= "<th width='9%' style='text-align:right; display:" . ($this->advanced ? 'table-cell' : 'none') . "'>Costo <span class='currency-symbol'></span> **</th>";
        $html .= "<th width='9%' style='text-align:right; display:" . ($this->advanced ? 'table-cell' : 'none') . "'>Neto <span class='currency-symbol'></span></th>";
        $html .= "<th width='15%'>Series</th>";
        $html .= "<th width='5%'>Opc.</th>";
        $html .= "</tr>";
        $html .= "</thead>";
        $html .= "<tbody></tbody>";
        $html .= "<tfoot>";
        $html .= "<tr>";
        $html .= "<td colspan='{$colspan}' style='text-align:right; display:" . ($this->advanced ? 'table-cell' : 'none') . ";'><strong>TOTALES: </strong></td>";
        $html .= "<td style='text-align:right; display:" . ($this->advanced ? 'table-cell' : 'none') . ";'><strong><span class='sian-warehouse-grid-total-bnet'></span></strong></td>";
        $html .= "<td style='text-align:right; display:" . ($this->advanced ? 'table-cell' : 'none') . ";'></td>";
        $html .= "</tr>";
        $html .= "</tfoot>";

        return $html;
    }

}
