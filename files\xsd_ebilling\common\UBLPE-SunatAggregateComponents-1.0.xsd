<?xml version="1.0" encoding="UTF-8"?>
<!--
  Autor:			       SUNAT
  Document Type:     Definiciones SUNAT
  Generated On:      01 Mayo 2012
-->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:sunat:names:specification:ubl:peru:schema:xsd:SunatAggregateComponents-1" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:udt="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" xmlns:ccts="urn:un:unece:uncefact:documentation:2" xmlns:qdt="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2" targetNamespace="urn:sunat:names:specification:ubl:peru:schema:xsd:SunatAggregateComponents-1" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0">
	<!-- ===== Imports ===== -->
	<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" schemaLocation="UBL-CommonAggregateComponents-2.0.xsd"/>
	<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" schemaLocation="UBL-CommonBasicComponents-2.0.xsd"/>
	<xsd:import namespace="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" schemaLocation="UnqualifiedDataTypeSchemaModule-2.0.xsd"/>
	<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2" schemaLocation="UBL-QualifiedDatatypes-2.0.xsd"/>
	<!-- ===== Element Declarations ===== -->
	<xsd:element name="SummaryDocumentsLine" type="SummaryDocumentsLineType"/>
	<xsd:element name="VoidedDocumentsLine" type="VoidedDocumentsLineType"/>
	<xsd:element name="DocumentSerialID" type="udt:IdentifierType"/>
	<xsd:element name="DocumentNumberID" type="udt:IdentifierType"/>
	<xsd:element name="VoidReasonDescription" type="udt:TextType"/>
	<xsd:element name="StartDocumentNumberID" type="udt:IdentifierType"/>
	<xsd:element name="EndDocumentNumberID" type="udt:IdentifierType"/>
	<xsd:element name="PeriodID" type="udt:IdentifierType"/>
	<xsd:element name="BillingPayment" type="cac:PaymentType"/>
	<xsd:element name="AdditionalInformation" type="AdditionalInformationType"/>
	<xsd:element name="AdditionalMonetaryTotal" type="AdditionalMonetaryTotalType"/>
	<xsd:element name="AdditionalProperty" type="AdditionalPropertyType"/>
	<xsd:element name="ReferenceAmount" type="cbc:AmountType"/>
	<xsd:element name="TotalAmount" type="cbc:AmountType"/>
	<!-- ===== Aggregate Business Information Entity Type Definitions ===== -->
	<xsd:complexType name="SummaryDocumentsLineType">
		<xsd:annotation>
			<xsd:documentation>
				<ccts:Component>
					<ccts:ComponentType>ABIE</ccts:ComponentType>
					<ccts:DictionaryEntryName>Consolidated Invoice Line Details</ccts:DictionaryEntryName>
					<ccts:Definition>Information about a Consolidated Invoice Line.</ccts:Definition>
					<ccts:ObjectClass>Consolidated  Invoice Line</ccts:ObjectClass>
				</ccts:Component>
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element ref="cbc:LineID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated  Invoice Line. Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>Identifies the Consolidated  Invoice Line.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Identifier</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:DocumentTypeCode">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Type Code. Code</ccts:DictionaryEntryName>
							<ccts:Definition>Code specifying the type of the Invoice.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Consolidated Invoice Line Type Code</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
							<ccts:DataType>Code. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="DocumentSerialID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Billing Payment</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Billing Payment.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Billing Payment</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Consolidated Invoice Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="StartDocumentNumberID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Billing Payment</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Billing Payment.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Billing Payment</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Consolidated Invoice Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="EndDocumentNumberID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line End Invoice Number</ccts:DictionaryEntryName>
							<ccts:Definition>An association to End Invoice Number.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>End Invoice Number</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Consolidated Invoice Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="TotalAmount">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Type Code. Code</ccts:DictionaryEntryName>
							<ccts:Definition>Code specifying the type of the Invoice.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Consolidated Invoice Line Type Code</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
							<ccts:DataType>Code. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="BillingPayment" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Billing Payment</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Billing Payment.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Billing Payment</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Consolidated Invoice Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cac:AllowanceCharge" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line. Allowance Charge</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Allowance Charge.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Allowance Charge</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Allowance Charge</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cac:TaxTotal" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line. Tax Total</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Tax Total.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Tax Total</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Tax Total</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="VoidedDocumentsLineType">
		<xsd:annotation>
			<xsd:documentation>
				<ccts:Component>
					<ccts:ComponentType>ABIE</ccts:ComponentType>
					<ccts:DictionaryEntryName>Consolidated Invoice Line Details</ccts:DictionaryEntryName>
					<ccts:Definition>Information about a Consolidated Invoice Line.</ccts:Definition>
					<ccts:ObjectClass>Consolidated  Invoice Line</ccts:ObjectClass>
				</ccts:Component>
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element ref="cbc:LineID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated  Invoice Line. Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>Identifies the Consolidated  Invoice Line.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Identifier</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:DocumentTypeCode">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Type Code. Code</ccts:DictionaryEntryName>
							<ccts:Definition>Code specifying the type of the Invoice.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Consolidated Invoice Line Type Code</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
							<ccts:DataType>Code. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="DocumentSerialID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Billing Payment</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Billing Payment.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Billing Payment</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Consolidated Invoice Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="DocumentNumberID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Billing Payment</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Billing Payment.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Billing Payment</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Consolidated Invoice Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="VoidReasonDescription">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Voided Document Line Void Reason Description</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Void Reason.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Voided Document Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Voided Document</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Voided Document Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="AdditionalInformationType">
		<xsd:annotation>
			<xsd:documentation>
				<ccts:Component>
					<ccts:ComponentType>ABIE</ccts:ComponentType>
					<ccts:DictionaryEntryName>Consolidated Invoice Line Details</ccts:DictionaryEntryName>
					<ccts:Definition>Information about a Consolidated Invoice Line.</ccts:Definition>
					<ccts:ObjectClass>Consolidated  Invoice Line</ccts:ObjectClass>
				</ccts:Component>
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element ref="AdditionalMonetaryTotal" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated  Invoice Line. Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>Identifies the Consolidated  Invoice Line.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Identifier</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="AdditionalProperty" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Type Code. Code</ccts:DictionaryEntryName>
							<ccts:Definition>Code specifying the type of the Invoice.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Consolidated Invoice Line Type Code</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
							<ccts:DataType>Code. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="AdditionalMonetaryTotalType">
		<xsd:annotation>
			<xsd:documentation>
				<ccts:Component>
					<ccts:ComponentType>ABIE</ccts:ComponentType>
					<ccts:DictionaryEntryName>Consolidated Invoice Line Details</ccts:DictionaryEntryName>
					<ccts:Definition>Information about a Consolidated Invoice Line.</ccts:Definition>
					<ccts:ObjectClass>Consolidated  Invoice Line</ccts:ObjectClass>
				</ccts:Component>
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element ref="cbc:ID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Billing Payment</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Billing Payment.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Billing Payment</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Consolidated Invoice Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:Name" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line. Allowance Charge</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Allowance Charge.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Allowance Charge</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Allowance Charge</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="ReferenceAmount" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Billing Payment</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Billing Payment.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Billing Payment</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Consolidated Invoice Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:PayableAmount">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Billing Payment</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Billing Payment.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Billing Payment</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Consolidated Invoice Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:Percent" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line. Allowance Charge</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Allowance Charge.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Allowance Charge</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Allowance Charge</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="TotalAmount" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line. Tax Total</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Tax Total.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Tax Total</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Tax Total</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="AdditionalPropertyType">
		<xsd:annotation>
			<xsd:documentation>
				<ccts:Component>
					<ccts:ComponentType>ABIE</ccts:ComponentType>
					<ccts:DictionaryEntryName>Consolidated Invoice Line Details</ccts:DictionaryEntryName>
					<ccts:Definition>Information about a Consolidated Invoice Line.</ccts:Definition>
					<ccts:ObjectClass>Consolidated  Invoice Line</ccts:ObjectClass>
				</ccts:Component>
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element ref="cbc:ID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line Billing Payment</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Billing Payment.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Billing Payment</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Consolidated Invoice Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:Name" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line. Allowance Charge</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Allowance Charge.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Allowance Charge</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Allowance Charge</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:Value">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Line. Tax Total</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Tax Total.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice Line</ccts:ObjectClass>
							<ccts:PropertyTerm>Tax Total</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Tax Total</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
</xsd:schema>
