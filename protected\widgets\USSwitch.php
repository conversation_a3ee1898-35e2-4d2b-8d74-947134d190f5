<?php

class USSwitch extends CWidget {

    public $id;
    public $model;
    public $attribute;
    public $size = null;
    public $onText = 'SI';
    public $offText = 'NO';
    public $switchChange = '';
    public $label = null;
    public $required = null;
    public $readonly = false;
    public $disabled = false;
    public $hint = '';
    public $htmlOptions = [];

    public function init() {
        //ID
        $this->id = isset($this->id) ? $this->id : Yii::app()->controller->getServerId();
        //Si no está seteado el required
        if (!isset($this->required)) {
            $this->required = isset($this->model) ? $this->model->isAttributeRequired($this->attribute) : false;
        }
        //ETIQUETA
        if ($this->label !== false) {
            $this->label = (isset($this->label) ? $this->label : $this->model->getAttributeLabel($this->attribute)) . ($this->required ? " <span class='required'>*</span>" : "");
        }
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->model->hasErrors($this->attribute)) {
            echo "<div class='form-group has-error'>";
        } else {
            echo "<div class='form-group'>";
        }
        //Generamos label
        if ($this->label !== false) {
            echo CHtml::label($this->label, null, array(
                'class' => 'control-label ' . ($this->required ? 'required' : '') . ' ' . ($this->model->hasErrors($this->attribute) ? 'error' : ''),
            ));
            echo "</br>";
        }
        //Generamos switch
        echo $this->widget('booster.widgets.TbSwitch', array(
            'model' => $this->model,
            'attribute' => $this->attribute,
            'options' => array(
                'onText' => $this->onText,
                'offText' => $this->offText,
                'readonly' => $this->readonly,
                'disabled' => $this->disabled,
                'size' => $this->size,
            ),
            'events' => array(
                'switchChange' => "js:function(event, state) {
                        {$this->switchChange}
                    }"
            ),
            'htmlOptions' => array_merge(array(
                'id' => $this->id,
                    ), $this->htmlOptions)
                ), true);
        //Si hay errores
        if ($this->model->hasErrors($this->attribute)) {
            echo "<span class='help-block error'>{$this->model->getError($this->attribute)}</span>";
        }
        //Si hay hint
        if (isset($this->hint)) {
            echo "<span class='help-block'>{$this->hint}</span>";
        }
        echo "</div>";

        //Si hay errores
        if ($this->model->hasErrors($this->attribute)) {
            Yii::app()->clientScript->registerScript($this->id, "
            $('#{$this->id}').closest('div.bootstrap-switch').addClass('us-error');
        ", CClientScript::POS_READY);
        }
    }

}
