<?php

class SIANMask<PERSON>erson extends CWidget {

    public $id;
    public $form;
    public $model;
    public $attribute;
    //PRIVATE
    private $controller;

    /**
     * Initializes the widget.
     */
    public function init() {
        //CONTROL
        if (!$this->model->scope === Scenario::SCOPE_IMPORT) {
            throw new Exception('Solo se debe usar este widget cuando es importación');
        }
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $a_maskAttributes = array(
            array('name' => 'identification_name', 'width' => 5, 'search' => false),
            array('name' => 'identification_number', 'width' => 15, 'types' => array('aux')),
            array('name' => 'person_id', 'width' => 0, 'types' => array('id', 'value'), 'hidden' => true),
            array('name' => 'person_name', 'width' => 40, 'types' => array('text')),
            array('name' => 'official_address', 'width' => 40, 'search' => false),
            array('name' => 'reference', 'hidden' => true, 'search' => false),
            array('name' => 'dept_code', 'hidden' => true, 'search' => false),
            array('name' => 'prov_code', 'hidden' => true, 'search' => false),
            array('name' => 'dist_code', 'hidden' => true, 'search' => false),
            array('name' => 'lat', 'hidden' => true, 'search' => false),
            array('name' => 'lng', 'hidden' => true, 'search' => false),
            array('name' => 'type', 'hidden' => true, 'search' => false),
            array('name' => 'expired', 'hidden' => true, 'search' => false),
            array('name' => 'retention', 'hidden' => true, 'search' => false),
        );

        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->id,
            'model' => $this->model,
            'attribute' => $this->attribute,
            'view' => array(
                'model' => 'DpBusinessPartner',
                'scenario' => $this->model->scenario->direction == Scenario::DIRECTION_OUT ? 'client' : 'provider',
                'attributes' => $a_maskAttributes
            ),
            'readonly' => true,
        ));
    }

}
