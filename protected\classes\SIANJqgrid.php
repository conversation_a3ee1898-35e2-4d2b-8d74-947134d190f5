<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of jqgridhelper
 *
 * <AUTHOR> Wilfredo Akamine
 */
class SIANJqgrid {

    /**
     *
     * @param array(array(),...) $aColumns Arreglo en formato array(array('COLUMNA_1','ALIAS_1'),array('COLUMNA_2','ALIAS_2'))
     * @return String COLUMNAS EN FORMATO SQL =  COLUMNA_1 AS ALIAS_1, COLUMNA_2 AS ALIAS_2, ..., COLUMNA_N AS ALIAS_N
     */
    public static function arrayColsToSQL($aColumns) {
        $SQL_cols = "";
        foreach ($aColumns as $sKey => $sValue) {
            $SQL_cols .=$sValue . " AS " . $sKey;
            $SQL_cols .=', ';
        }
        return substr($SQL_cols, 0, strlen($SQL_cols) - 2);
    }

    /**
     *
     * @param String $cols Columnas y sus alias en formato SQL
     * @param String $tables_join Relaciones entre las tablas en formato SQL
     * @param String $where_conditions Condiciones en formato SQL
     * @param String $where_search Condiciones adicionales desde el jqGrid
     * @param String $sidx Columna de ordenamiento
     * @param String $sord Dirección del ordenamiento ASC o DESC
     * @param Srting $limit Valor para el LIMIT
     * @param String $start Valor para el OFFSET
     * @return array Resultado de la consulta
     */
    public static function generateSQL($cols, $tables_join, $where_conditions, $where_search, $sidx, $sord, $limit, $start, $grouping='', $having='', $s_connection = 'default') {        
//        print_r('SELECT ' . $cols . ' FROM ' . $tables_join . ' WHERE ' . $where_conditions . $where_search  . $grouping .  ' ORDER BY ' . $sidx . ' ' . $sord . ' LIMIT ' . $limit . ' OFFSET ' . $start);
//        die();
        $s_limit = ' LIMIT '.$limit.' OFFSET '.$start;
        if($limit < 0 || $limit==''){
            $s_limit = '';
        }
        $query = 'SELECT ' . $cols 
                . ' FROM ' . $tables_join 
                . ' WHERE ' . $where_conditions . $where_search . ' ' . $grouping . ' ' . $having . ' ' ;
        $sidx = trim($sidx);
        if ($sidx !=NULL AND $sidx!=''){
               $query .= ' ORDER BY ' . $sidx . ' ' . $sord;
        }
        $query .= $s_limit;
//        print_r($query); die;
        $connection = Yii::app()->db;
        $connection->active=true;
        $command = $connection->createCommand($query);
        return  $command->queryAll();

    }

    /**
     *
     * @param String $cols Columnas y sus alias en formato SQL
     * @param String $tables_join Relaciones entre las tablas en formato SQL
     * @param String $where_conditions Condiciones en formato SQL
     * @param String $where_search Condiciones adicionales desde el jqGrid
     * @param String $grouping Sentencia GROUP BY personalizada
     * @param String $count_detail Sentencia dentro del COUNT($count_detail) personalizada
     * @return int Número de resultados de la consulta con las condiciones dadas.
     */
    public static function getCount($cols, $tables_join, $where_conditions, $where_search='', $grouping='', $count_detail='*', $having=NULL, $s_connection = 'default') {
        //print_r('SELECT COUNT('.$count_detail.') AS total FROM ' . $tables_join . ' WHERE ' . $where_conditions . ' ' . $where_search . ' ' . $grouping);
        $sqlSubQueryPre = '';
        $sqlSelect = 'SELECT COUNT(' . $count_detail . ') AS total';
        $sqlSubQueryPost = '';
        if ($having != NULL) {
            $sqlSubQueryPre = 'SELECT COUNT(*) AS total FROM ( ';
            $sqlSelect = ' SELECT ' . $cols . ' ';
            $sqlSubQueryPost = ' ) tblTesting ';
        }
        $connection = Yii::app()->db;
        $connection->active = true;
        $command = $connection->createCommand($sqlSubQueryPre 
                . $sqlSelect 
                . ' FROM ' . $tables_join 
                . ' WHERE ' . $where_conditions . ' ' . $where_search . ' ' . $grouping . ' ' . $having . ' ' . $sqlSubQueryPost);
        $count = $command->queryAll();
        
        if (count($count) != 0){
            if(count($count)==1)
                $count = $count[0]['total'];  
            else
                $count=count($count);
        } else
            $count = 0;
        
        return $count;
    }

    /**
     *
     * @param int $page Número de página del paginador jqGrid
     * @param int $count Número de resultados de la consulta con las condiciones dadas.
     * @param int $limit Número de resultados por página para el jqGrid
     * @param int $total_pages Número de páginas para el paginador, este párametro se pasa por referencia
     * @return int $start El siguiente número de fila que inicia en la siguiente página del paginador jqGrid
     */
    public static function calculateStart($page, $count, $limit, &$total_pages) {
        if ($count > 0 && $limit > 0) {
            $total_pages = ceil($count / $limit);
        } else {
            $total_pages = 0;
        }
        if ($page > $total_pages)
            $page = $total_pages;
        $start = $limit * $page - $limit;

        if ($start < 0)
            $start = 0;
        return $start;
    }

    /**
     *
     * @param String $search_String
     * @return string Condiciones para la búsqueda en nu jqGrid
     */
    public static function constructWhere($search_String, $column_array = NULL) {
        $qwery = "";
        //['eq','ne','lt','le','gt','ge','bw','bn','in','ni','ew','en','cn','nc']
        $qopers = array(
            'eq' => " = ",
            'ne' => " <> ",
            'lt' => " < ",
            'le' => " <= ",
            'gt' => " > ",
            'ge' => " >= ",
            'bw' => " LIKE ",
            'bn' => " NOT LIKE ",
            'in' => " IN ",
            'ni' => " NOT IN ",
            'ew' => " LIKE ",
            'en' => " NOT LIKE ",
            'cn' => " LIKE ",
            'nc' => " NOT LIKE ");
        if ($search_String) {
            $jsona = json_decode($search_String, true);
            if (is_array($jsona)) {
                $gopr = $jsona['groupOp'];
                $rules = $jsona['rules'];
                $i = 0;
                foreach ($rules as $key => $val) {
                    if ($column_array != NULL) {
                        $field = $column_array[$val['field']];
                    } else {
                        $field = $val['field'];
                    }
                    $op = $val['op'];
                    $v = $val['data'];
                    if ($v && $op) {
                        $i++;
                        // ToSql in this case is absolutley needed
                        $v = self::ToSql($field, $op, $v);
                        if ($i == 1)
                            $qwery = " AND ";
                        else
                            $qwery .= " " . $gopr . " ";
                        switch ($op) {
                            // in need other thing
                            case 'in' :
                            case 'ni' :
                                $qwery .= $field . $qopers[$op] . " (" . $v . ")";
                                break;
                            default:
                                $qwery .= $field . $qopers[$op] . $v;
                        }
                    }
                }
            }
        } 
        return $qwery;
    }

    /**
     *
     * @param String $field columna de la consulta
     * @param String $oper operador comparativo
     * @param any_type $val
     * @return String retorna en formato SQL las condiciones para un campo y operador recibido
     */
    private static function ToSql($field, $oper, $val) {
        // we need here more advanced checking using the type of the field - i.e. integer, string, float
        switch ($field) {
            /* AGREGAR LAS COLUMNAS DE TIPO NUMÉRICO
              case 'product.idProduct':
              return intval($val);
              break; */
            case'product.price':
                return floatval($val);
                break;
            default :
                //mysql_real_escape_string is better
                if ($oper == 'bw' || $oper == 'bn')
                    return "'%" . addslashes($val) . "%'";
                else if ($oper == 'ew' || $oper == 'en')
                    return "'%" . addslashes($val) . "'";
                else if ($oper == 'cn' || $oper == 'nc')
                    return "'%" . addslashes($val) . "%'";
                else
                    return "'" . addslashes($val) . "'";
        }
    }

    public static function Strip($value) {
        if (get_magic_quotes_gpc() != 0) {
            if (is_array($value))
                if (self::array_is_associative($value)) {
                    foreach ($value as $k => $v)
                        $tmp_val[$k] = stripslashes($v);
                    $value = $tmp_val;
                }
                else
                    for ($j = 0; $j < sizeof($value); $j++)
                        $value[$j] = stripslashes($value[$j]);
            else
                $value = stripslashes($value);
        }
        return $value;
    }

    private static function array_is_associative($array) {
        if (is_array($array) && !empty($array)) {
            for ($iterator = count($array) - 1; $iterator; $iterator--) {
                if (!array_key_exists($iterator, $array)) {
                    return true;
                }
            }
            return!array_key_exists(0, $array);
        }
        return false;
    }

    public static function JSON4jqGrid($page, $total_pages, $count, $result_array, $array_cols, $include_id=false,$array_user_data=NULL) {
        $json = "";
        $json .= "{";
        $json .= "\"page\":\"$page\",";
        $json .= "\"total\":\"$total_pages\",";
        $json .= "\"records\":\"$count\",";
        $json .= "\"rows\": [";
        $rc = false;
        $col_alias_name = array_values($array_cols);
        $lenght = count($col_alias_name);

        $sId = array_keys($array_cols);
        $sId = $sId[0];

        if (!$include_id) {
            $array_cols = array_slice($array_cols, 1);
        }

        foreach ($result_array as $result_row) {
            if ($rc)
                $json .= ",";
            $json .= "{";
            $json .= "\"id\":\"" . $result_row[$sId] . "\",";
            $json .= "\"cell\":[";

            foreach ($array_cols as $sKey => $sValue) {             
                $json .= self::generateJSONdata4jqGrid($result_row, $sKey);
            }

            $json = substr($json, 0, strlen($json) - 1);
            $json .= "]}";
            $rc = true;
        }
        $json .= "]";
        if (is_array($array_user_data))
            $json .= ",\"userdata\": " . json_encode($array_user_data);

        $json .= "}";

        return $json;
    }

    private static function generateJSONdata4jqGrid($result_row, $alias_name) {
        return "\"" . htmlspecialchars(self::nl2br2($result_row[$alias_name])) . "\",";
    }

    public static function nl2br2($string) {
        $string = str_replace(array("\r\n", "\r", "\n"), "", $string);
        return $string;
    }

}