<?php

$arrayItemsNavbar = array(
    // array('icon' => 'th-list', 'label' => 'Datos generales', 'url' => '#', 'items' => array(
    array('label' => 'Datos generales', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'list', 'label' => 'Series', 'route' => 'serie/index'),
                array('icon' => 'user', 'label' => 'Tipo de Vendedores', 'route' => 'sellerType/index', 'items' => array(
                        array('icon' => 'plus', 'label' => 'Crear', 'route' => 'sellerType/create'),
                    )),
                array('icon' => 'user', 'label' => 'Vendedores', 'route' => 'seller/index'),
                array('icon' => 'user', 'label' => 'Turnos', 'route' => 'shift/index'),
                array('icon' => 'list', 'label' => 'Metas Comerciales', 'route' => 'commercialGoal/index'),
                array('icon' => 'gift', 'label' => 'Promociones/ofertas', 'route' => 'promotion/index'),
                array('icon' => 'thumbs-up', 'label' => 'Convenios', 'route' => 'agreement/index'),
            ),
            array(
                array('icon' => 'book', 'label' => 'Plantillas de Cotización', 'route' => 'quoteTemplate/index', 'items' => array(
                        array('icon' => 'plus', 'label' => 'Crear', 'route' => 'quoteTemplate/create'),
                    )),
            ),
            array(
                array('icon' => 'cog', 'label' => 'Config. Escenarios', 'route' => 'scenarioSetting/index'),
            ))
    ),
    // array('icon' => 'th-list', 'label' => 'CRM', 'url' => '#', 'items' => array(
    array('label' => 'CRM', 'url' => '#', 'items' => array(
            array('icon' => 'list', 'label' => 'Categoría de Personas', 'route' => 'categoryPerson/index', 'items' => array(
                    array('icon' => 'plus', 'label' => 'Crear', 'route' => 'categoryPerson/create'),
                )),
            array('icon' => 'list', 'label' => 'Modo de Contacto', 'route' => 'contactMode/index', 'items' => array(
                    array('icon' => 'plus', 'label' => 'Crear', 'route' => 'contactMode/create'),
                )),
            array(
                array('icon' => 'user', 'label' => 'Contactos', 'route' => 'contact/index', 'items' => array(
                        array('icon' => 'plus', 'label' => 'Crear', 'route' => 'contact/create'),
                    )),
            ),
            array(
                array('icon' => 'tag', 'label' => 'Gestión de Casos', 'route' => 'commercialCase/index', 'items' => array(
                        array('icon' => 'plus', 'label' => 'Crear', 'route' => 'commercialCase/create'),
                    )),
            ),
        )),
    // array('icon' => 'shopping-cart', 'label' => 'Movimientos', 'url' => '#', 'items' => array(
    array('label' => 'Movimientos', 'url' => '#', 'items' => array(
            array('icon' => 'tag', 'label' => 'Orden de Consignación', 'route' => 'consignmentOrder/index', 'items' => array(
                    array('icon' => 'plus', 'label' => 'Crear', 'route' => 'consignmentOrder/create'),
                )),
            array('icon' => 'tag', 'label' => 'Cotizaciones', 'route' => 'saleQuote/index', 'items' => array(
                    array('icon' => 'plus', 'label' => 'Crear', 'route' => 'saleQuote/create'),
                )),
            array(
                array('icon' => 'tag', 'label' => 'Ordenes de venta', 'route' => 'saleOrder/index', 'items' => array(
                        array('icon' => 'plus', 'label' => 'Crear', 'route' => 'saleOrder/create'),
                    )),
                array('icon' => 'tags', 'label' => 'Comprobantes', 'route' => 'saleBill/index'),
            ),
//            array(
//                array('icon' => 'refresh', 'label' => 'Canje de comprobantes', 'route' => 'saleBillExchange/index'),
//            ),
            array(
                array('icon' => 'file', 'label' => 'Notas de Crédito para Clientes (Dif.Precio)', 'route' => 'creditNote1/index'),
                array('icon' => 'file', 'label' => 'Notas de Crédito para Clientes (Devolución)', 'route' => 'creditNote2/index'),
                array('icon' => 'file', 'label' => 'Notas de Crédito para Clientes (No despacho)', 'route' => 'creditNote3/index'),
                array('icon' => 'file', 'label' => 'Notas de Débito para Clientes', 'route' => 'debitNote/index'),
            ),
        )),
    // array('icon' => 'file', 'label' => 'Reportes', 'url' => '#', 'items' => array(array(
    array('label' => 'Reportes', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'usd', 'label' => 'Ventas Mensuales (Gráfico)', 'route' => 'report/monthlySalesGraph'),
                array('icon' => 'list', 'label' => 'Costo de Venta', 'route' => 'report/saleCost'),
                array('icon' => 'list', 'label' => 'Ventas', 'route' => 'report/sales'),
                array('icon' => 'list', 'label' => 'Ventas Diarias', 'route' => 'report/salesDiaryProjected'),
                array('icon' => 'list', 'label' => 'Ventas por Sublínea', 'route' => 'report/salesPerSubline'),
                array('icon' => 'list', 'label' => 'Comparativo - Ventas por Sublínea', 'route' => 'report/comparativeSalesPerSubline'),
                array('icon' => 'list', 'label' => 'Ubigeo', 'route' => 'report/ubigeo'),
                array('icon' => 'list', 'label' => 'Ubigeo por Condición', 'route' => 'report/ubigeoByCondition'),
                array('icon' => 'list', 'label' => 'Productividad', 'route' => 'report/production'),
                array('icon' => 'list', 'label' => 'Cobranzas', 'route' => 'report/collecting'),
                array('icon' => 'list', 'label' => 'Consignaciones pendientes', 'route' => 'report/pendingConsignments'),
                array('icon' => 'eye-open', 'label' => 'Control de Operaciones ', 'route' => 'report/operationsControl'),
            ),
            array(
                array('icon' => 'list', 'label' => 'Lista de Clientes', 'route' => 'report/clientsList'),
                array('icon' => 'list', 'label' => 'Clientes por Ubigeo', 'route' => 'report/clientsByUbigeo'),
                array('icon' => 'list', 'label' => 'Clientes Nuevos', 'route' => 'report/newClients'),
                array('icon' => 'list', 'label' => 'Clientes por Vendedor', 'route' => 'report/clientsPerSeller'),
                array('icon' => 'list', 'label' => 'Clasificación de Clientes', 'route' => 'report/clientsClassification'),
                array('icon' => 'list', 'label' => 'Frecuencia de Clientes', 'route' => 'report/clientsFrequency'),
            ),
            array(
                array('icon' => 'list', 'label' => 'Ventas por Productos', 'route' => 'report/salesByProducts'),
                array('icon' => 'list', 'label' => 'Productos por debajo del mínimo', 'route' => 'report/productsBelowStock')
            ),
            array(
                array('icon' => 'list', 'label' => 'CRM - Casos de ventas', 'route' => 'report/crmCommercialCases'))
        )
    ),
);

$this->widget('application.widgets.SIANNavbar', array(
    'brand' => Yii::app()->id,
    'class' => 'navbar-commercial',
    'items' => $this->items_menu, //$arrayItemsNavbar,
));
