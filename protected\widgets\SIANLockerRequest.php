<?php

class SIANLockerRequest extends CWidget {

    //CONST
    public $id;
    public $form;
    public $model;
    public $max_accesses = 1;
    //PRIVATE
    private $locker_input_id;
    private $controller;

    public function init() {

        if (!isset($this->model->person_client_id)) {
            throw new Exception('Debe especificar el ID de persona para los accesos de clientes');
        }
        if (!isset($this->model->person_deliverer_id)) {
            throw new Exception('Debe especificar el ID de persona para los accesos de repartidores');
        }
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //
        $this->locker_input_id = $this->controller->getServerId();
        //
        SIANAssets::registerScriptFile('other/jquery.autocomplete/js/jquery.autocomplete.js');
        SIANAssets::registerScriptFile('js/sian-locker-request.js');
        SIANAssets::registerCssFile('other/jquery.autocomplete/css/jquery.autocomplete.css');
        //Procesamos
        $a_prev_quantities = [];
        foreach ($this->model->tempBoxRequests as $o_boxRequest) {
            foreach ($o_boxRequest->tempArticles as $o_article) {
                $a_prev_quantities[$o_article->article_code][] = [
                    'quantity' => $o_article->quantity,
                    'box_number' => $o_boxRequest->box_number
                ];
            }
        }
        //
        $a_prev_accesses = [];
        foreach ($this->model->tempAccesses as $o_access) {

            if (!in_array($o_access->type, [LockerAccess::TYPE_CLIENT, LockerAccess::TYPE_DELIVERER], true)) {
                throw new Exception("Tipo de acceso no válido: {$o_access->type}");
            }

            $a_prev_accesses[$o_access->type][] = [
                'firstname' => isset($o_access->firstname) ? $o_access->firstname : '',
                'lastname' => isset($o_access->lastname) ? $o_access->lastname : '',
                'dni' => isset($o_access->dni) ? $o_access->dni : '',
                'email_address' => isset($o_access->email_address) ? $o_access->email_address : '',
            ];
        }

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        //Evento load porque se ejecuta cuando ya todo cargó
        $(document).ready(function() {
            var divObj = $('#{$this->id}');
            divObj.data('locker_input_id', '{$this->locker_input_id}');
            divObj.data('locker_box_url', '{$this->controller->createUrl('/widget/getLockerBoxes')}');
            divObj.data('prev_quantities', " . CJSON::encode($a_prev_quantities) . ");
            divObj.data('prev_accesses', " . CJSON::encode($a_prev_accesses) . ");
            divObj.data('person_client_id', {$this->model->person_client_id});
            divObj.data('person_deliverer_id', {$this->model->person_deliverer_id});
            divObj.data('contact_url', '{$this->controller->createUrl('/widget/getContactForLockerRequest')}');
            divObj.data('max_accesses', {$this->max_accesses});
        });
        
        $('body').on('change', '#{$this->id}_article_modal .form-control, #{$this->id}_access_modal .form-control', function(e) {
            var elementObj = $(this);
            elementObj.cleanError();
        });
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        echo "<div id='{$this->id}'>";
        echo $this->form->hiddenField($this->model, 'locker_id', [
            'id' => $this->locker_input_id
        ]);
        echo $this->form->hiddenField($this->model, 'person_client_id', [
        ]);
        echo $this->form->hiddenField($this->model, 'person_deliverer_id', [
        ]);
        //ARTICLES
        echo "<div class='sian-locker-request-articles'>";
        foreach ($this->model->tempBoxRequests as $i => $o_boxRequest) {
            echo CHtml::hiddenField("LockerRequest[boxRequests][{$i}][box_number]", $o_boxRequest->box_number);
            foreach ($o_boxRequest->tempArticles as $j => $o_article) {
                echo CHtml::hiddenField("LockerRequest[boxRequests][{$i}][articles][{$j}][article_code]", $o_article->article_code);
                echo CHtml::hiddenField("LockerRequest[boxRequests][{$i}][articles][{$j}][article_description]", $o_article->article_description);
                echo CHtml::hiddenField("LockerRequest[boxRequests][{$i}][articles][{$j}][quantity]", $o_article->quantity);
                echo CHtml::hiddenField("LockerRequest[boxRequests][{$i}][articles][{$j}][allow_decimals]", $o_article->allow_decimals);
                echo CHtml::hiddenField("LockerRequest[boxRequests][{$i}][articles][{$j}][article_img]", $o_article->article_img);
            }
        }
        echo "</div>";
        //ACCESOS
        echo "<div class='sian-locker-request-accesses'>";
        foreach ($this->model->tempAccesses as $i => $o_access) {
            echo CHtml::hiddenField("LockerRequest[accesses][{$i}][firstname]", $o_access->firstname);
            echo CHtml::hiddenField("LockerRequest[accesses][{$i}][lastname]", $o_access->lastname);
            echo CHtml::hiddenField("LockerRequest[accesses][{$i}][dni]", $o_access->dni);
            echo CHtml::hiddenField("LockerRequest[accesses][{$i}][type]", $o_access->type);
            echo CHtml::hiddenField("LockerRequest[accesses][{$i}][email_address]", $o_access->email_address);
        }
        echo "</div>";
        //
        echo "</div>";
    }

}
