<?php

class SIANMovementLogView extends CWidget {

    public $id;
    public $title = 'Registro de eventos';
    public $panel = false;
    public $logs;
    //PRIVATE
    private $clientScript;
    private $controller;

    public function init() {

        $this->clientScript = Yii::app()->clientScript;
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //SCRIPTS
        $this->clientScript->registerScript($this->id, "           
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        
        $logs_by_type = [];
        foreach ($this->logs as $log) {
            $logs_by_type[$log['title']][] = [
                'movement_log_type_id' => $log['movement_log_type_id'],
                'register_date' => $log['register_date'],
                'description' => $log['description'],
                'person_name' => $log['person_name']];
        }

        if ($this->panel === true) {
            $this->beginWidget('booster.widgets.TbPanel', array(
                'title' => $this->title,
                'headerIcon' => 'list',
                'htmlOptions' => array(
                    'id' => $this->id,
                )
            ));
        } else {
            echo "<h4>{$this->title}</h4>";
        }

        $a_tabs[] = [
            'id' => $this->id . '_pane_' . MovementLog::ORDER_BY_DATE,
            'label' => "Mostrar por " . MovementLog::ORDER_BY_DATE,
            'active' => true,
            'content' => $this->controller->renderPartial("application.views.widget._logs", array('data' => $this->logs, 'mode' => MovementLog::ORDER_BY_DATE), true),
            'paneOptions' => [
            ],
            'itemOptions' => [
                'id' => $this->id . '_item_' . MovementLog::ORDER_BY_DATE,
            ],
            'linkOptions' => [
                'id' => $this->id . '_link_' . MovementLog::ORDER_BY_DATE,
            ]
        ];

        $a_tabs[] = [
            'id' => $this->id . '_pane_' . MovementLog::ORDER_BY_TYPE,
            'label' => "Mostrar por " . MovementLog::ORDER_BY_TYPE,
            'content' => $this->controller->renderPartial("application.views.widget._logs", array('data' => $logs_by_type, 'mode' => MovementLog::ORDER_BY_TYPE), true),
            'paneOptions' => [
            ],
            'itemOptions' => [
                'id' => $this->id . '_item_' . MovementLog::ORDER_BY_TYPE,
            ],
            'linkOptions' => [
                'id' => $this->id . '_link_' . MovementLog::ORDER_BY_TYPE,
            ]
        ];

        $this->widget('booster.widgets.TbTabs', array(
            'type' => 'tabs', // 'tabs' or 'pills'
            'placement' => 'left',
            'tabs' => $a_tabs
        ));

        if ($this->panel === true) {
            $this->endWidget();
        }
    }

}
