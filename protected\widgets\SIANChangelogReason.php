<?php

class SIANChangelogReason extends CWidget {

    public $id;
    public $form;
    public $model;
    public $items = [];
    public $reason_code_input_id;
    public $reason_input_id;
    public $onChange = "";
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->reason_code_input_id = isset($this->reason_code_input_id) ? $this->reason_code_input_id : $this->controller->getServerId();
        $this->reason_input_id = isset($this->reason_input_id) ? $this->reason_input_id : $this->controller->getServerId();
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Motivo de Emisión',
            'headerIcon' => 'th-list'
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-6'>";
        echo $this->form->dropDownListRow($this->model, 'reason_code', $this->items, array(
            'id' => $this->reason_code_input_id,
            'empty' => Strings::SELECT_OPTION,
            'onchange' => "
                var text = $('#{$this->reason_code_input_id} option:selected').text();
                $('#{$this->reason_input_id}').val(removeTildes(text).toUpperCase());
            ". $this->onChange
        ));
        echo "</div>";
        echo "<div class='col-lg-8 col-md-8 col-sm-8 col-xs-6'>";
        echo $this->form->textFieldRow($this->model, 'reason', array(
            'id' => $this->reason_input_id,
            'readonly' => true,
            'rows' => 5,
        ));
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

}
