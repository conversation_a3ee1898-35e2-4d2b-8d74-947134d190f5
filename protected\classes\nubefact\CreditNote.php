<?php
// class Nota<PERSON><PERSON><PERSON> extends DocumentoEmision
class CreditNote extends DocumentIssue
{
    public static $NC_ANULACION_OPERACION = 1; // 1 = ANULACIÓN DE LA OPERACIÓN
    public static $NC_ANULACION_ERROR_RUC = 2; // 2 = ANULACIÓN POR ERROR EN EL RUC
    public static $NC_CORRECCION_ERROR_DESCRIPCION = 3; // 3 = CORRECCIÓN POR ERROR EN LA DESCRIPCIÓN
    public static $NC_DESCUENTO_GLOBAL = 4; // 4 = DESCUENTO GLOBAL
    public static $NC_DESCUENTO_ITEM = 5; // 5 = DESCUENTO POR ÍTEM
    public static $NC_DEVOLUCION_TOTAL = 6; // 6 = DEVOLUCIÓN TOTAL
    public static $NC_DEVOLUCION_ITEM = 7; // 7 = DEVOLUCIÓN POR ÍTEM
    public static $NC_BONIFICACION = 8; // 8 = BONIFICACIÓN
    public static $NC_DISMINUCION_VALOR = 9; // 9 = DISMINUCIÓN EN EL VALOR
    public static $NC_OTROS_CONCEPTOS = 10; // 10= OTROS CONCEPTOS
    public static $NC_AJUSTES_AFECTOS_IVAP = 11; // 11= AJUSTES AFECTOS AL IVAP
    public static $NC_AJUSTES_OPERACIONES_EXPORTACIÓN = 12; // 12 = AJUSTES DE OPERACIONES DE EXPORTACIÓN
    public static $NC_AJUSTES_MONTOS_FECHAS_PAGO = 13; // 13 = AJUSTES - MONTOS Y/O FECHAS DE PAGO


    public function __construct()
    {
        parent::__construct();

        $this->data['tipo_de_comprobante'] = self::$NOTA_CREDITO;
    }

    /**
     * ATRIBUTO: documento_que_se_modifica_tipo
     * VALOR: 1 = FACTURAS ELECTRÓNICAS, 2 = BOLETAS DE VENTA ELECTRÓNICAS
     * TIPO DE DATO: Integer
     * REQUISITO: Condicional
     * LONGITUD: 1 exacto
     */
    public function setDocumentWhichModifiesType($type)
    {
        $this->setRaw('documento_que_se_modifica_tipo', $type);
    }

    /**
     * ATRIBUTO: documento_que_se_modifica_serie
     * VALOR: SERIE de la FACTURA o BOLETA que se modifica (previamente comunicado)
     * TIPO DE DATO: String
     * REQUISITO: Condicional
     * LONGITUD: 4 exactos
     */
    public function setDocumentToModifySeries($serie)
    {
        $this->setRaw('documento_que_se_modifica_serie', $serie);
    }

    /**
     * ATRIBUTO: documento_que_se_modifica_numero
     * VALOR: NÚMERO de la FACTURA o BOLETA que se modifica (previamente comunicado)
     * TIPO DE DATO: Integer
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 8
     */
    public function setDocumentWhichModifiesNumber($number)
    {
        $this->setRaw('documento_que_se_modifica_numero', $number);
    }

    /**
     * ATRIBUTO: tipo_de_nota_de_credito
     * VALOR: 1 = ANULACIÓN DE LA OPERACIÓN, 2 = ANULACIÓN POR ERROR EN EL RUC, 3 = CORRECCIÓN POR ERROR EN LA DESCRIPCIÓN, 
     *   4 = DESCUENTO GLOBAL, 5 = DESCUENTO POR ÍTEM, 6 = DEVOLUCIÓN TOTAL, 7 = DEVOLUCIÓN POR ÍTEM, 8 = BONIFICACIÓN, 
     *   9 = DISMINUCIÓN EN EL VALOR, 10= OTROS CONCEPTOS, 11= AJUSTES AFECTOS AL IVAP, 12 = AJUSTES DE OPERACIONES DE EXPORTACIÓN, 
     *   13 = AJUSTES - MONTOS Y/O FECHAS DE PAGO
     * TIPO DE DATO: Integer
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 2
     */
    public function setTypeOfCreditNote($type)
    {
        $this->setRaw('tipo_de_nota_de_credito', $type);
    }
}