<?php

class SIANSearchGrid extends CWidget {

    public $id;
    public $property_pks = "";
    public $deactivateList = "";
    public $resetList = "";
    public $onCheck = "";
    public $onUncheck = "";
    //PRIVATE
    private $controller;
    private $autocomplete_id;
    private $search_pk_id;
    private $search_pres_quantity_id;
    private $search_uncheck_button_id;
    private $search_radio_button_list_id;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->autocomplete_id = $this->controller->getServerId();
        $this->search_pres_quantity_id = $this->controller->getServerId();
        $this->search_pk_id = $this->controller->getServerId();
        $this->search_radio_button_list_id = $this->controller->getServerId();
        $this->search_uncheck_button_id = $this->controller->getServerId();

        Yii::app()->clientScript->registerScript($this->id, "
        $(document).ready(function() {
        
            $('body').on('keydown', '#{$this->search_pres_quantity_id}', function(e) {
                if(e.which === 13) {
                    var pk = $('#{$this->search_pk_id}').val();
                    if(pk == ''){
                        alert('Seleccione un producto!');
                        USAutocompleteFocus('{$this->autocomplete_id}');
                    }
                    else
                    {                    
                        var pres_quantity = $('#{$this->search_pres_quantity_id}').val();
                        if(pres_quantity != '' && pres_quantity > 0){
                            if ($('#{$this->search_radio_button_list_id}_1:checked').is(':checked')) {
                                  {$this->onCheck} 
                            } else if ($('#{$this->search_radio_button_list_id}_2:checked').is(':checked')) {
                                {$this->onUncheck}      
                            }                                  
                            USAutocompleteReset('{$this->autocomplete_id}');
                            $('#{$this->search_pk_id}').val('');  
                            $('#{$this->search_pres_quantity_id}').val(0);
                        }else
                        {
                            alert('Ingrese una cantidad mayor a 0.');
                        }
                    }
                }
            });
            
            $('body').on('click', '#{$this->search_uncheck_button_id}', function(e) {
                var pk = $('#{$this->search_pk_id}').val();
                if(pk == ''){
                    alert('Seleccione un producto!');
                    USAutocompleteFocus('{$this->autocomplete_id}');
                }
                else
                {  
                    {$this->onUncheck}
                    USAutocompleteReset('{$this->autocomplete_id}');
                    $('#{$this->search_pk_id}').val('');
                    $(this).prev().focus();
                }
            });
            
            $('body').on('change', '#{$this->search_radio_button_list_id}_0', function(e) {
                USAutocompleteReadOnly('{$this->autocomplete_id}', true)
                $('#{$this->search_pres_quantity_id}').attr('readonly', true);
                $('#{$this->search_uncheck_button_id}').attr('disabled', true);
                $('#{$this->search_pres_quantity_id}').val(0);
                {$this->resetList}
            });
            
            $('body').on('change', '#{$this->search_radio_button_list_id}_1', function(e) {
                USAutocompleteReadOnly('{$this->autocomplete_id}', false);
                USAutocompleteFocus('{$this->autocomplete_id}');
                $('#{$this->search_pres_quantity_id}').removeAttr('readonly');
                
                if(confirm('¿Desea limpiar la selección actual?'))
                {
                    {$this->deactivateList}
                }
                $('.sian-search-grid-uncheck').css('display', 'none');
                $('.sian-search-grid-check').css('display', 'block');
            });
            
            $('body').on('change', '#{$this->search_radio_button_list_id}_2', function(e) {
                USAutocompleteReadOnly('{$this->autocomplete_id}', false);
                USAutocompleteFocus('{$this->autocomplete_id}');
                $('#{$this->search_pres_quantity_id}').val(0);
                $('#{$this->search_uncheck_button_id}').removeAttr('disabled');
                $('.sian-search-grid-uncheck').css('display', 'block');
                $('.sian-search-grid-check').css('display', 'none');
            });
        });
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $a_attributes = [];
        $a_attributes[] = array('name' => 'product_id', 'width' => 7, 'types' => array('id', 'value', 'aux'));
        $a_attributes[] = array('name' => 'barcode_unit', 'width' => 20);
        $a_attributes[] = array('name' => 'product_name', 'width' => 50, 'types' => array('text'));
        $a_attributes[] = array('name' => 'pk', 'in' => $this->property_pks, 'update' => "$('#{$this->search_pk_id}').val(pk);");

        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-6'>";
        echo SIANForm::radioButtonListNonActive('Selección', $this->search_radio_button_list_id, true,
                [1 => 'Resetear', 2 => 'Marcar', 0 => 'Quitar'],
                [
                    'style' => "margin-right:4px",
                    'separator' => ' ',
                    'container' => 'div',
                    'containerHtmlOptions' => [
                        'style' => "display:flex"
                    ],
                    'labelOptions' => [
                        'style' => "margin-top:7px;margin-right:5px;"
                    ],
                ]
        );
        echo "</div>";
        echo "<div class='col-lg-5 col-md-5 col-sm-5 col-xs-6'>";
        echo $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->autocomplete_id,
            'label' => 'Producto',
            'name' => null,
            'useOnkeydown' => Yii::app()->controller->getOrganization()->globalVar->keyboard_search == 1 ? true : false,
            'autoChoose' => Yii::app()->controller->getOrganization()->globalVar->keyboard_search == 1 ? false : true,
            'view' => array(
                'model' => 'DpAllProductPresentations',
                'scenario' => DpAllProductPresentations::SCENARIO_WITHOUT_STOCK,
                'attributes' => $a_attributes,
            ),
            'readonly' => true,
            "onselect" => "",
            'onreset' => ""
                ), true);
        echo CHtml::hiddenField('pk', '', [
            'id' => $this->search_pk_id
        ]);
        echo "</div>";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-6 sian-search-grid-check'>";
        echo SIANForm::numberFieldNonActive('Cantidad', null, 0, array(
            'id' => $this->search_pres_quantity_id,
            'data-last' => 1,
            'class' => 'enterTab',
            'style' => 'text-align:right',
            'readonly' => true
        ));
        echo "</div>";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-6 sian-search-grid-uncheck' style='display:none'>";
        echo CHtml::label('', $this->search_uncheck_button_id, []);
        echo "<br/>";
        $this->widget('application.widgets.USButtons', array(
            'buttons' => array(
                array(
                    'id' => $this->search_uncheck_button_id,
                    'context' => 'primary',
                    'icon' => 'fa fa-lg fa-minus white',
                    'size' => 'default',
                    'title' => 'Añadir'
                )
            )
        ));
        echo "</div>";
        echo "</div>";
    }

}
