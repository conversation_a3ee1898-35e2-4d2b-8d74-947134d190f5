function SIANBusinessUnitFill(select_id, items, business_unit_id)
{
    var selectObj = $('#' + select_id);

    var found = false;

    $.each(items, function (index, item) {
        var optionObj = $('<option>').val(item.business_unit_id)
                .text(item.business_unit_name)
                .data('combination_id', item.combination_id)
                .data('combination_name', item.combination_name)
                .data('levels', item.levels);

        selectObj.append(optionObj);

        if (business_unit_id == item.business_unit_id)
        {
            found = true;
        }
    });

    if (found)
    {
        selectObj.val(business_unit_id);
    }

    //Seteamos last
    selectObj.change();
    //Si no funciona dejarlo asi:
    //selectObj.trigger("change");
}

function SIANBusinessUnitWarnMessage(input_id)
{
    var inputObj = $('#' + input_id);
    //Valores
    var business_unit_id = inputObj.val();
    var default_business_unit_id = inputObj.data('default_business_unit_id');
    var parent_business_unit_id = inputObj.data('parent_business_unit_id');
    //
    var xmessage = false;
    //Verificamos si el movimiento NO tiene tienda de padre
    if (isBlank(parent_business_unit_id))
    {
        //Verificamos si es igual a la tienda por defecto
        if (business_unit_id != default_business_unit_id)
        {
            xmessage = "La unidad de negocio seleccionada es diferente a la unidad de negocio que tiene asignada como predeterminada.";
        }
    } else
    {
        //Verificamos si es diferente a la tienda del padre
        if (business_unit_id != parent_business_unit_id)
        {
            xmessage = "La unidad de negocio seleccionada es diferente a la unidad de negocio que esperaba movimiento padre.";
        }
    }

    return xmessage;

}