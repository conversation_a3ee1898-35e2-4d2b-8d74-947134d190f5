.combogrid{
    /*max-width: 800px;
    min-width: 500px;*/
    font-size: 0.8em !important;
}
.input-bg{
    background-image: url("cg-images/magnifier.png");
    background-position: 100% 4px;
    background-repeat: no-repeat;
    padding-right: 16px;
}
#cg-divHeader {
    height: 17px;
    /*padding: 4px;*/
    -moz-border-radius: 3px 3px 0 0;
    -webkit-border-radius: 3px 3px 0 0;
    margin: -3px -3px 0 -1px;
}
.cg-colHeader {
    float:left;
    /*font: 13px verdana,arial,sans-serif;*/
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 11px;
    font-weight: bold;
}
.cg-colHeader-label {
    padding: 0;
    margin:0;
    cursor: pointer;
}
.cg-colHeader-label:hover {
    text-decoration: underline;
}
.cg-colHeader span {
    height: 12px;
    width: 12px;
    position: absolute;
}
.cg-colHeader.asc{
    background-image: url("images/ui-icons_888888_256x240.png");
    background-position: -64px -16px;
}
.cg-colHeader.desc{
    background-image: url("images/ui-icons_888888_256x240.png");
    background-position: 0 -16px;
}
.cg-colItem{
    height:14px;
    padding: 5px;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
}
.cgcombo-state-hover {
    background-color: #eeeeee;
    border: 1px solid #cccccc;
}
.cg-comboButton{
    background-color: #eeeeee;
    width: 100%;
    height: 25px;
    margin: -3px 0 -3px -1px;
    padding: 2px 2px 2px 0;
    width: 100%;
    -moz-border-radius: 0 0 3px 3px;
    -webkit-border-radius: 0 0 3px 3px;
    outline: 0 none;
    vertical-align: baseline;
}
.cg-navTable{
    float: left;
    table-layout:auto;
    width: 100%;
    font-size: 0.7em;
    font-weight: normal;
    border: 0;
    margin: 0;
}
.cg-navTable td{
    padding: 1px;
    border:0;
    text-shadow: none;
}
.cg-navTable tr{
    color: inherit;
    background: none;
    border:0;
}
.cg-comboItem{
    height: 24px;
    margin: 0;
    width:100%;
    padding-bottom: 2px !important;
    border: 1px solid #ffffff;
}
.cg-comboItem-even{
    height: 24px;
    padding-bottom: 2px !important;
    background-color: #edf6f8;
}
.cg-comboItem-odd{
    height: 24px;
    padding-bottom: 2px !important;
}
.cg-DivItem {
    float:left;
    /*font-size: 0.8em;*/
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 11px;
    overflow: hidden;
    height: 15px;
}
.cg-pg-button {
    padding: 1px;
    width: 18px;
}
.cg-pg-table {
    border: 0;
    padding: 0;
    margin:0;
}
.cg-pg-button:hover {
    border: 1px solid #999999;
    background: #dadada url(images/ui-bg_glass_75_dadada_1x400.png) 50% 50% repeat-x;
    font-weight: normal;
    color: #212121;
    padding: 0;
}
.cg-pg-button.cg-state-disabled:hover {
    border: 1px #ececec !important;
    padding: 1px;
    background:none;
}
#cg-navInfo{
    min-width: 150px;
    text-align: center;
}

.cg-autocomplete {
    position: absolute; 
    cursor: default; 
}	

/* workarounds */
* html .cg-autocomplete { width:1px;} /* without this, the menu expands to 100% in IE6 */

.cg-menu {
    padding-right: 2px;
    margin: 0;
}
.cg-menu .cg-menu {
    margin-top: -3px;
}
.cg-menu .cg-menu-item {
    margin: 0;
    padding: 0;
    clear: left;
    overflow: hidden;
    border: 1px solid transparent;
}

.cg-menu .cg-menu-item a {
    text-decoration:none;
    display:block;
    padding:.2em .4em;
    line-height:1.5;
    zoom:1;
}
.cg-menu .cg-menu-item a.cg-state-hover,
.cg-menu .cg-menu-item a.cg-state-active {
    font-weight: normal;
    margin: -1px;
}
.cg-resetButton {
    cursor: pointer;
    display: inline-block;
    margin: 1px;
    padding: 3px;
    vertical-align: bottom;
}
.cg-searchButton {
    cursor: pointer;
    display: inline-block;
    margin: 1px;
    padding: 3px;
    vertical-align: bottom;
}
.cg-loading{
    background-image: url("cg-images/loading.gif");
    background-position: 100% 2px;
    background-repeat: no-repeat;
}
.ok-icon{
    background-image: url("cg-images/accept.png");
    margin-bottom: 6px;
    vertical-align: bottom;
    height: 16px;
    width: 16px;
    display: inline-block;
    margin-left: 3px;
}
.notok-icon{
    background-image: url("cg-images/exclamation.png");
    margin-bottom: 6px;
    vertical-align: bottom;
    height: 16px;
    width: 16px;
    display: inline-block;
    margin-left: 3px;
}

