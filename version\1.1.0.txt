Fecha: 28/05/2014

General
- Se redujo el número de peticiones a la base de datos hasta en un 90% lo cual se verá reflejado en una carga más rápida de las páginas.
- Se optimizaron los buscadores. Ahora cuentan con vista previa por defecto.
- Se corrigió la creación doble de pedidos u órdenes de compra que ocurría en algunos casos al hacer varios clics en el botón “Guardar”.
- Se quitó el botón para desactivar elementos desde las grillas principales, con el fin de evitar que por accidente se realice dicha operación.
- Se enlazaron los documentos, cajas y empleados a las tiendas con el fin de poder medir la productividad de una tienda y facilitar las transacciones dentro de la misma . 
Administración
- Se optimizó el árbol de áreas. 
- Se mejoró el formulario para crear personas.
Almacén
- Se movió el ingreso por saldo a inicial a este módulo y ahora se realiza en un solo paso.
- Se mejoró el formulario de ingreso por transferencia.
- Se mejoró el formulario de ingreso por guía de remisión.
- Se unió la salida por consumo interno con las salidas por transferencias ya que por el momento consumo interno es un almacén.
- Se agregó una lista desplegable antes de crear una salida por transferencia.
- Se habilitó la emisión de varias guías de remisión de salida por comprobante de venta.
- El almacenero puede ver el stock y movimientos de todos los almacenes. Pero sólo puede dar movimiento a los productos de los almacenes que tiene asignados.
Logística
- Se habilitó la verificación del nombre (alias) para divisiones, líneas, sublíneas, marcas, productos, servicios y unidades de medida, lo cual evitará que se creen elementos con nombres. Por ejemplo: “Case Avatec 2014” con “Case avatec 2014”.
- Se habilitó la columna “Imagen” en la grilla de productos que indica si una mercadería tiene imagen o no.
- Se mejoró los formularios para crear atributos para los productos.
- Se habilito la creación de proveedores a partir de personas naturales.
- Se habilitó dos propiedades a cada almacén: “Habilitado para ingresos”, “Habilitado para salidas”. Por ejemplo, si un almacén tiene la primera propiedad pero no la segunda, entonces no se podrá despachar desde el mismo. Es el caso de los almacenes de consumo interno, consignaciones y ventas en bolívar .
- Se quitó el ingreso de saldos iniciales desde este módulo, ahora se realiza en un solo paso desde almacén.
- Ya no hay restricciones para visualizar almacenes desde este módulo.
Comercial
- Se optimizó la vista general de stock y precios que aparece en la primera página
- Se unificó los pedidos, ahora se puede hacer un pedido que combine mercaderías y servicios.
- Se habilito la edición de pedidos, siempre y cuando no hayan sido facturados.
Contabilidad
- Se mejoraron los formularios en general.
Finanzas
- Se habilitó la validación de los formularios de los pagos por compras y ventas.
Tesorería
- Se habilitó las propiedades “Permite entradas”, “Permite salidas” a las cajas/bancos, para MAS ADELANTE evitar que ciertas cajas, que sólo cobran, puedan dar dinero como si fueran una caja chica, entre otros casos.
- Ahora un empleado solo podrá ver las facturas/boletas y cajas/bancos enlazadas a las tiendas a la hora de facturar, salvo que tenga el rol admin o superadmin.
Soporte técnico
- Las órdenes de servicio se movieron al módulo comercial, pues éste último ahora permite hacer pedidos de servicio.
- Este módulo ha sido desactivado por el momento.
Otros
- Se optimizó el módulo multimedia para la carga de imágenes.
Consideraciones:
Es posible que con los cambios realizados algunos reportes no carguen o no se visualicen correctamente, pero estos serán solo errores de muestra, por favor informar si se detecta un error en los mismos.