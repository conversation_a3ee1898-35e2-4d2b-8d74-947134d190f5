<?php

include('us-config.php');

Yii::setPathOfAlias('booster', "{$dir}/{$admin}/protected/extensions/booster");
Yii::setPathOfAlias('admin', "{$dir}/{$admin}/protected");
Yii::setPathOfAlias('us', "{$dir}/{$admin}/protected/us");

ob_start('My_OB');

function My_OB($str, $flags) {
    //remove UTF-8 BOM
    $str = preg_replace("/\xef\xbb\xbf/", "", $str);

    return $str;
}

$app = 'SIAN';

return array(
    'id' => $app,
    'name' => 'Sistema Integral para la Administración de Negocios',
    'theme' => 'sian', // requires you to copy the theme under your themes directory
    'basePath' => "{$dir}/{$admin}/protected",
    'defaultController' => 'site',
    // preloading 'log' component
    'preload' => array('log', 'booster', 'us'),
    // autoloading model and component classes
    'import' => array(
        'application.apis.*',
        'application.classes.*',
        'application.classes.nubefact.*',
        'application.components.*',
        'application.components.repositories.*',
        'application.components.repositories.interfaces.*',
        'application.components.services.*',
        'application.controllers.*',
        'application.dist.*',
        'application.extended.*',
        'application.fake.*',
        'application.traits.*',
        'application.models.*',
        'application.models.forms.*',
        'application.models.procedures.*',
        'application.models.procedures.ability.*',
        'application.models.procedures.control.*',
        'application.models.dataProviders.*',
        'application.models.dataProviders.auto.*',
        'application.models.dataProviders.grid.*',
        'application.models.dataProviders.print.*',
        'application.models.dataProviders.reports.*',
        'application.models.dataProviders.subgrid.*',
        'application.models.dataProviders.ws.*',
        'application.models.extended.*',
        'application.models.procedures.forms.*',
        'application.models.procedures.menu.*',
        'application.models.procedures.sh.*',
        'application.models.procedures.tasks.*',
        'application.models.procedures.validators.*',
        'application.models.procedures.reports.*',
        'application.models.procedures.utility.*',
        'application.models.procedures.ws.*',
        'application.models.sian.*',
        'application.models.system.*',
        'application.models.views.*',
        'application.models.views.grid.*',
        'application.models.views.system.*',
        'application.models.views.system.ability.*',
        'application.validators.*',
        'application.widgets.*',
        'us.helpers.*',
    ),
    'modules' => array(
        'api',
        'apiSian',
        'apiPos',
        'apiKiosk',
        'asset',
        'accounting',
        'administration',
        'cms',
        'commercial',
        'financial',
        'human',
        'apiLocker',
        'logistic',
        'marketing',
        'project',
        'sharedApi',
        'support',
        'systems',
        'treasury',
        'warehouse',
        'webhook',
        // uncomment the following to enable the Gii tool
        'gii' => array(
            'class' => 'system.gii.GiiModule',
            'password' => $gii_password,
            // If removed, Gii defaults to localhost only. Edit carefully to taste.
            'ipFilters' => array('127.0.0.1', '::1'),
            'generatorPaths' => array(
                'booster.gii',
            ),
        ),
    ),
    // application components
    'components' => array(
        'user' => array(
            'class' => 'CWebUser',
            'absoluteAuthTimeout' => $absolute_auth_timeout,
            'authTimeout' => $auth_timeout,
        ),
        'session' => [
            'class' => 'CCacheHttpSession',
            'timeout' => $absolute_auth_timeout,
        ],
        'booster' => array(
            'class' => 'booster.components.Booster',
            'bootstrapCss' => false,
            'responsiveCss' => true,
            'fontAwesomeCss' => true,
        ),
        // uncomment the following to enable URLs in path-format
        'urlManager' => array(
            'urlFormat' => 'path',
            'showScriptName' => false,
            'urlSuffix' => '.html',
            'rules' => array(
                //  '' => 'site/index',
                //  '<action:\w+>' => 'site/<action>',
                //REST
                /* 'post/<id:\d+>/<title:.*?>' => 'post/view',
                  'posts/<tag:.*?>' => 'post/index', */
                // webCampaing
                array('api/webCampaing/restActivated', 'pattern' => 'api/webCampaing', 'verb' => 'GET'),
                //Claim: no se usa ID sino CODE, que es alfanumérico
                array('api/claim/restView', 'pattern' => 'api/claim/<id:\w+>', 'verb' => 'GET'),
                array('api/claim/restUpdate', 'pattern' => 'api/claim/<id:\w+>', 'verb' => 'PUT'),
                array('api/claim/restResolve', 'pattern' => 'api/claim/resolve/<id:\w+>', 'verb' => 'PUT'),
                //Para distribuidores
                array('api/eCommerce/restGetDeptItems', 'pattern' => 'api/eCommerce', 'verb' => 'GET'),
                array('api/eCommerce/restGetProvItems', 'pattern' => 'api/eCommerce/<dept_code:\w+>', 'verb' => 'GET'),
                array('api/eCommerce/restGetDistItems', 'pattern' => 'api/eCommerce/<dept_code:\w+>/<prov_code:\w+>', 'verb' => 'GET'),
                //General
                array('api/general/restInfo', 'pattern' => 'api/general/info', 'verb' => 'GET'),
                array('api/general/restSitemap', 'pattern' => 'api/general/sitemap', 'verb' => 'GET'),
                //Para geoloc
                array('api/geoloc/restGetDeptItems', 'pattern' => 'api/geoloc', 'verb' => 'GET'),
                array('api/geoloc/restGetProvItems', 'pattern' => 'api/geoloc/<dept_code:\w+>', 'verb' => 'GET'),
                array('api/geoloc/restGetDistItems', 'pattern' => 'api/geoloc/<dept_code:\w+>/<prov_code:\w+>', 'verb' => 'GET'),
                //Combo
                array('api/combo/restSheet', 'pattern' => 'api/combo/sheet/<id:\d+>', 'verb' => 'GET'),
                //Merchandise
                array('api/merchandise/restCompare', 'pattern' => 'api/merchandise/compare', 'verb' => 'POST'),
                array('api/merchandise/restFilters', 'pattern' => 'api/merchandise/filters', 'verb' => 'POST'),
                array('api/merchandise/restTree', 'pattern' => 'api/merchandise/tree', 'verb' => 'GET'),
                array('api/merchandise/restSheet', 'pattern' => 'api/merchandise/sheet/<id:\d+>', 'verb' => 'GET'),
                array('api/merchandise/restGroupedList', 'pattern' => 'api/merchandise/groupedList', 'verb' => 'GET'),
                //Multitable
                array('api/multitable/restGetItems', 'pattern' => 'api/multitable', 'verb' => 'GET'),
                //NEWSLETTER
                array('api/newsletter/restSubscribeAll', 'pattern' => 'api/newsletter', 'verb' => 'POST'),
                array('api/newsletter/restSubscribe', 'pattern' => 'api/newsletter/<id:\d+>', 'verb' => 'POST'),
                //VOTE
                array('api/product/restRating', 'pattern' => 'api/product/rating/<id:\d+>', 'verb' => 'GET'),
                array('api/product/restVote', 'pattern' => 'api/product/vote/<id:\d+>', 'verb' => 'PUT'),
                //SALE BILL
                array('api/saleBill/restConfirm', 'pattern' => 'api/saleBill/confirm', 'verb' => 'POST'),
                array('api/saleBill/restView', 'pattern' => 'api/saleBill/<id:\w+>', 'verb' => 'GET'),
                //SALE ORDER                
                array('api/saleOrder/restView', 'pattern' => 'api/saleOrder/<id:\w+>', 'verb' => 'GET'),
                array('api/saleOrder/restDelete', 'pattern' => 'api/saleOrder/<id:\w+>', 'verb' => 'DELETE'),
                array('api/saleOrder/restGenerateTokenPay', 'pattern' => 'api/saleOrder/generateToken', 'verb' => 'POST'),
                //PUBLIC SALE ORDER
                array('api/publicSaleOrder/restView', 'pattern' => 'api/publicSaleOrder/<id:\w+>', 'verb' => 'GET'),
                array('api/publicSaleOrder/restPay', 'pattern' => 'api/publicSaleOrder/pay', 'verb' => 'POST'),
                array('api/publicSaleOrder/restGenerateTokenPay', 'pattern' => 'api/publicSaleOrder/generateToken', 'verb' => 'POST'),
                //SEARCH BILL
                array('api/searchBill/restCdr', 'pattern' => 'api/searchBill/cdr/<id:\w+>', 'verb' => 'GET'),
                array('api/searchBill/restPdf', 'pattern' => 'api/searchBill/pdf/<id:\w+>', 'verb' => 'GET'),
                array('api/searchBill/restSearch', 'pattern' => 'api/searchBill/search', 'verb' => 'POST'),
                array('api/searchBill/restSearchByCode', 'pattern' => 'api/searchBill/search/<id:\w+>', 'verb' => 'GET'),
                array('api/searchBill/restView', 'pattern' => 'api/searchBill/<id:\w+>', 'verb' => 'GET'),
                array('api/searchBill/restXml', 'pattern' => 'api/searchBill/xml/<id:\w+>', 'verb' => 'GET'),
                //LOGIN
                array('api/signin/restLogin', 'pattern' => 'api/signin', 'verb' => 'POST'),
                //SHOPPING_CART
                array('api/shoppingCart/restAddFrom', 'pattern' => 'api/shoppingCart/addFrom/<id:\d+>', 'verb' => 'PUT'),
                array('api/shoppingCart/restAssign', 'pattern' => 'api/shoppingCart/assign/<id:\d+>', 'verb' => 'POST'),
                array('api/shoppingCart/restCheckValidity', 'pattern' => 'api/shoppingCart/checkValidity/<id:\d+>', 'verb' => 'GET'),
                array('api/shoppingCart/restClean', 'pattern' => 'api/shoppingCart/clean/<id:\d+>', 'verb' => 'PUT'),
                array('api/shoppingCart/restExport', 'pattern' => 'api/shoppingCart/export/<id:\d+>', 'verb' => 'GET'),
                array('api/shoppingCart/restValidateStock', 'pattern' => 'api/shoppingCart/validateStock/<id:\d+>', 'verb' => 'GET'),
                array('api/shoppingCart/restValidateAmount', 'pattern' => 'api/shoppingCart/validateAmount/<id:\d+>', 'verb' => 'GET'),
                array('api/shoppingCart/restCostShipping', 'pattern' => 'api/shoppingCart/costShipping/<id:\d+>', 'verb' => 'POST'),
                //UTL
                array('api/util/restVerifyToken', 'pattern' => 'api/util/verifyToken', 'verb' => 'POST'),
                //WEB USER
                array('api/webUser/restVerify', 'pattern' => 'api/webUser/verify', 'verb' => 'POST'),
                array('api/webUser/restChangePassword', 'pattern' => 'api/webUser/changePassword/<id:\d+>', 'verb' => 'PUT'),
                array('api/webUser/restCurriculum', 'pattern' => 'api/webUser/curriculum/<id:\d+>', 'verb' => 'PUT'),
                array('api/webUser/restLogin', 'pattern' => 'api/webUser/login', 'verb' => 'POST'),
                array('api/webUser/restRecover', 'pattern' => 'api/webUser/recover', 'verb' => 'POST'),
                array('api/webUser/restReset', 'pattern' => 'api/webUser/reset', 'verb' => 'POST'),
                array('api/webUser/restGuest', 'pattern' => 'api/webUser/guest', 'verb' => 'POST'),
                //
                array('api/<controller>/restList', 'pattern' => 'api/<controller:\w+>', 'verb' => 'GET'),
                array('api/<controller>/restView', 'pattern' => 'api/<controller:\w+>/<id:\d+>', 'verb' => 'GET'),
                array('api/<controller>/restSecret', 'pattern' => 'api/<controller:\w+>/secret/<secret:.*>', 'verb' => 'GET'),
                array('api/<controller>/restUpdate', 'pattern' => 'api/<controller:\w+>/<id:\d+>', 'verb' => 'PUT'),
                array('api/<controller>/restDelete', 'pattern' => 'api/<controller:\w+>/<id:\d+>', 'verb' => 'DELETE'),
                array('api/<controller>/restCreate', 'pattern' => 'api/<controller:\w+>', 'verb' => 'POST'),
                //API LOCKER
                //Login
                array('apiLocker/signin/restLogin', 'pattern' => 'apiLocker/signin', 'verb' => 'POST'),
                //
                array('apiLocker/general/restStatus', 'pattern' => 'apiLocker/general/status', 'verb' => 'POST'),
                //Access
                array('apiLocker/access/restLogin', 'pattern' => 'apiLocker/access/login', 'verb' => 'POST'),
                array('apiLocker/access/restList', 'pattern' => 'apiLocker/request/<id2:\d+>/access', 'verb' => 'GET'),
                array('apiLocker/access/restView', 'pattern' => 'apiLocker/request/<id2:\d+>/access/<id:\d+>', 'verb' => 'GET'),
                array('apiLocker/access/restUpdate', 'pattern' => 'apiLocker/request/<id2:\d+>/access/<id:\d+>', 'verb' => 'PUT'),
                array('apiLocker/access/restDelete', 'pattern' => 'apiLocker/request/<id2:\d+>/access/<id:\d+>', 'verb' => 'DELETE'),
                array('apiLocker/access/restCreate', 'pattern' => 'apiLocker/request/<id2:\d+>/access', 'verb' => 'POST'),
                array('apiLocker/access/restVote', 'pattern' => 'apiLocker/request/<id2:\d+>/vote', 'verb' => 'POST'),
                //Article
                array('apiLocker/article/restList', 'pattern' => 'apiLocker/request/<id2:\d+>/article', 'verb' => 'GET'),
                array('apiLocker/article/restView', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id3:\d+>/article/<id:\w+>', 'verb' => 'GET'),
                array('apiLocker/article/restUpdate', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id3:\d+>/article/<id:\w+>', 'verb' => 'PUT'),
                array('apiLocker/article/restDelete', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id3:\d+>/article/<id:\w+>', 'verb' => 'DELETE'),
                array('apiLocker/article/restCreate', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id3:\d+>/article', 'verb' => 'POST'),
                //Box Request
                array('apiLocker/boxRequest/restList', 'pattern' => 'apiLocker/request/<id2:\d+>/box', 'verb' => 'GET'),
                array('apiLocker/boxRequest/restView', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id:\d+>', 'verb' => 'GET'),
                array('apiLocker/boxRequest/restUpdate', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id:\d+>', 'verb' => 'PUT'),
                array('apiLocker/boxRequest/restDelete', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id:\d+>', 'verb' => 'DELETE'),
                array('apiLocker/boxRequest/restCreate', 'pattern' => 'apiLocker/request/<id2:\d+>/box', 'verb' => 'POST'),
                array('apiLocker/boxRequest/restNotFilled', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id:\d+>/notFilled', 'verb' => 'PUT'),
                array('apiLocker/boxRequest/restToPickUp', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id:\d+>/toPickUp', 'verb' => 'PUT'),
                array('apiLocker/boxRequest/restToReturn', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id:\d+>/toReturn', 'verb' => 'PUT'),
                array('apiLocker/boxRequest/restReturned', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id:\d+>/returned', 'verb' => 'PUT'),
                array('apiLocker/boxRequest/restFinished', 'pattern' => 'apiLocker/request/<id2:\d+>/box/<id:\d+>/finished', 'verb' => 'PUT'),
                //Request
                array('apiLocker/request/restToFill', 'pattern' => 'apiLocker/request/<id:\d+>/toFill', 'verb' => 'PUT'),
                array('apiLocker/request/restNotFilled', 'pattern' => 'apiLocker/request/<id:\d+>/notFilled', 'verb' => 'PUT'),
                array('apiLocker/request/restToPickUp', 'pattern' => 'apiLocker/request/<id:\d+>/toPickUp', 'verb' => 'PUT'),
                array('apiLocker/request/restToReturn', 'pattern' => 'apiLocker/request/<id:\d+>/toReturn', 'verb' => 'PUT'),
                array('apiLocker/request/restReturned', 'pattern' => 'apiLocker/request/<id:\d+>/returned', 'verb' => 'PUT'),
                array('apiLocker/request/restFinished', 'pattern' => 'apiLocker/request/<id:\d+>/finished', 'verb' => 'PUT'),
                //
                //SHIFT
                array('apiPos/shift/restOpening', 'pattern' => 'apiPos/shift/opening', 'verb' => 'GET'),
                array('apiPos/shift/restActive', 'pattern' => 'apiPos/shift/active', 'verb' => 'GET'),
                array('apiPos/shift/restClosing', 'pattern' => 'apiPos/shift/closing', 'verb' => 'GET'),
                //
                array('apiLocker/<controller>/restList', 'pattern' => 'apiLocker/<controller:\w+>', 'verb' => 'GET'),
                array('apiLocker/<controller>/restView', 'pattern' => 'apiLocker/<controller:\w+>/<id:\d+>', 'verb' => 'GET'),
                array('apiLocker/<controller>/restUpdate', 'pattern' => 'apiLocker/<controller:\w+>/<id:\d+>', 'verb' => 'PUT'),
                array('apiLocker/<controller>/restDelete', 'pattern' => 'apiLocker/<controller:\w+>/<id:\d+>', 'verb' => 'DELETE'),
                array('apiLocker/<controller>/restCreate', 'pattern' => 'apiLocker/<controller:\w+>', 'verb' => 'POST'),
                //API POS
                array('apiPos/<controller>/restSyncCount', 'pattern' => 'apiPos/<controller:\w+>/syncCount', 'verb' => 'GET'),
                array('apiPos/<controller>/restSync', 'pattern' => 'apiPos/<controller:\w+>/sync', 'verb' => 'GET'),
                //NC
                array('apiPos/creditNote/restEscPos', 'pattern' => 'apiPos/creditNote/escPos/<id:\w+>', 'verb' => 'GET'),
                array('apiPos/creditNote/restReason', 'pattern' => 'apiPos/creditNote/reason', 'verb' => 'GET'),
                array('apiPos/creditNote/restRequestCreate', 'pattern' => 'apiPos/creditNote/request', 'verb' => 'POST'),
                array('apiPos/creditNote/restRequestState', 'pattern' => 'apiPos/creditNote/request', 'verb' => 'GET'),
                //commercialDocument
                array('apiPos/commercialDocument/restEscPos', 'pattern' => 'apiPos/commercialDocument/escPos', 'verb' => 'GET'),
                //PRODUCTS
                array('apiPos/product/restGifts', 'pattern' => 'apiPos/product/gifts', 'verb' => 'POST'),
                array('apiPos/product/restComboItems', 'pattern' => 'apiPos/product/comboItems', 'verb' => 'GET'),
                //REPORTS
                array('apiPos/report/restCashClose', 'pattern' => 'apiPos/report/cashClose', 'verb' => 'GET'),
                array('apiPos/report/restCashCloseEscPos', 'pattern' => 'apiPos/report/escPos', 'verb' => 'GET'),
                //RAW PRINTER
                array('apiPos/rawPrinter/restDefault', 'pattern' => 'apiPos/rawPrinter/default/<id:\d+>', 'verb' => 'PUT'),
                //SALE
                array('apiPos/sale/restCalculate', 'pattern' => 'apiPos/sale/calculate', 'verb' => 'POST'),
                array('apiPos/sale/restCalculateItem', 'pattern' => 'apiPos/sale/calculateItem', 'verb' => 'POST'),
                array('apiPos/sale/restEscPos', 'pattern' => 'apiPos/sale/escPos/<id:\w+>', 'verb' => 'GET'),
                array('apiPos/sale/restPrintDispatchTicket', 'pattern' => 'apiPos/sale/printDispatchTicket/<id:\w+>', 'verb' => 'GET'),
                array('apiPos/sale/restFrom', 'pattern' => 'apiPos/sale/from', 'verb' => 'POST'),
                array('apiPos/sale/restSeries', 'pattern' => 'apiPos/sale/series', 'verb' => 'POST'),
                array('apiPos/sale/restView', 'pattern' => 'apiPos/sale/<id:\w+>', 'verb' => 'GET'),
                //LOGIN
                array('apiSian/signin/restLogin', 'pattern' => 'apiSian/signin/login', 'verb' => 'POST'),
                //CLUB MIA
                array('apiSian/signin/restLoginClubMia', 'pattern' => 'apiSian/signin/clubMia/login', 'verb' => 'POST'),
                array('apiSian/signin/restRegisterClubMia', 'pattern' => 'apiSian/signin/clubMia/register', 'verb' => 'POST'),
                array('apiSian/pbi/restSales', 'pattern' => 'apiSian/pbi/sales', 'verb' => 'GET'),
                array('apiSian/pbi/restSalesByMonth', 'pattern' => 'apiSian/pbi/salesByMonth', 'verb' => 'GET'),
                array('apiSian/pbi/restProgressGoals', 'pattern' => 'apiSian/pbi/progressGoals', 'verb' => 'GET'),
                array('apiSian/user/restLogin', 'pattern' => 'apiSian/user/login', 'verb' => 'POST'),
                array('apiSian/fixedAsset/restCreate', 'pattern' => 'apiSian/fixedAsset/depreciate', 'verb' => 'POST'),
                array('apiSian/pay/restFiles', 'pattern' => 'apiSian/pay/files', 'verb' => 'POST'),
                array('apiSian/pay/restGetResources', 'pattern' => 'apiSian/resources', 'verb' => 'POST'),
                array('apiSian/<controller>/restCreate', 'pattern' => 'apiSian/<controller:\w+>', 'verb' => 'POST'),
                //LOGIN
                array('apiPos/signin/restLogin', 'pattern' => 'apiPos/signin', 'verb' => 'POST'),
                //POS USER
                array('apiPos/user/restChangePassword', 'pattern' => 'apiPos/user/changePassword/<id:\d+>', 'verb' => 'PUT'),
                array('apiPos/user/restLogin', 'pattern' => 'apiPos/user/login', 'verb' => 'POST'),
                //POS PERSON
                array('apiPos/person/restAgreementStatus', 'pattern' => 'apiPos/person/agreementStatus/<id:\d+>', 'verb' => 'GET'),
                //SALE ORDER ORDER
                array('apiPos/saleOrder/restAlertPending', 'pattern' => 'apiPos/saleOrder/alert', 'verb' => 'GET'),
                array('apiPos/saleOrder/restListPending', 'pattern' => 'apiPos/saleOrder/pending', 'verb' => 'GET'),
                array('apiPos/saleOrder/restNull', 'pattern' => 'apiPos/saleOrder/null/<id:\w+>', 'verb' => 'POST'),
                array('apiPos/saleOrder/restView', 'pattern' => 'apiPos/saleOrder/<id:\w+>', 'verb' => 'GET'),
                //array('apiPos/<controller>/sync', 'pattern' => 'apiPos/<controller:\w+>/sync/<id:.*>', 'verb' => 'GET'),
                array('apiPos/<controller>/restList', 'pattern' => 'apiPos/<controller:\w+>', 'verb' => 'GET'),
                array('apiPos/<controller>/restView', 'pattern' => 'apiPos/<controller:\w+>/<id:\d+>', 'verb' => 'GET'),
                array('apiPos/<controller>/restSecret', 'pattern' => 'apiPos/<controller:\w+>/secret/<secret:.*>', 'verb' => 'GET'),
                array('apiPos/<controller>/restUpdate', 'pattern' => 'apiPos/<controller:\w+>/<id:\d+>', 'verb' => 'PUT'),
                array('apiPos/<controller>/restDelete', 'pattern' => 'apiPos/<controller:\w+>/<id:\d+>', 'verb' => 'DELETE'),
                array('apiPos/<controller>/restCreate', 'pattern' => 'apiPos/<controller:\w+>', 'verb' => 'POST'),
                //TRANSFERENCE OUT
                array('apiPos/transferenceOut/restCashClose', 'pattern' => 'apiPos/transferenceOut/cashClose', 'verb' => 'POST'),
                //SHIFT
                array('apiPos/shift/restOpening', 'pattern' => 'apiPos/shift/opening', 'verb' => 'GET'),
                array('apiPos/shift/restActive', 'pattern' => 'apiPos/shift/active', 'verb' => 'GET'),
                array('apiPos/shift/restOpening', 'pattern' => 'apiPos/shift/closing', 'verb' => 'GET'),
                //
                array('apiPos/apiUserCashboxVariant/restPaymentMethod', 'pattern' => 'apiPos/apiUserCashboxVariant/paymentMethod', 'verb' => 'GET'),
                //Api Kiosko
                //LOGIN
                array('apiKiosk/signin/restLogin', 'pattern' => 'apiKiosk/signin', 'verb' => 'POST'),
                array('apiKiosk/user/restLogin', 'pattern' => 'apiKiosk/user/login', 'verb' => 'POST'),
                array('apiKiosk/<controller>/restList', 'pattern' => 'apiKiosk/<controller:\w+>', 'verb' => 'GET'),
                array('apiKiosk/<controller>/restView', 'pattern' => 'apiKiosk/<controller:\w+>/<id:\d+>', 'verb' => 'GET'),
                array('apiKiosk/<controller>/restUpdate', 'pattern' => 'apiKiosk/<controller:\w+>/<id:\d+>', 'verb' => 'PUT'),
                array('apiKiosk/<controller>/restCreate', 'pattern' => 'apiKiosk/<controller:\w+>', 'verb' => 'POST'),
                //SMS (TEST)
                array('sharedApi/sms/restSend', 'pattern' => 'sharedApi/sms/send', 'verb' => 'POST'),
                //SHARED API
                array('sharedApi/<controller>/restList', 'pattern' => 'sharedApi/<controller:\w+>', 'verb' => 'GET'),
                array('sharedApi/<controller>/restView', 'pattern' => 'sharedApi/<controller:\w+>/<id:\d+>', 'verb' => 'GET'),
                array('sharedApi/<controller>/restUpdate', 'pattern' => 'sharedApi/<controller:\w+>/<id:\d+>', 'verb' => 'PUT'),
                array('sharedApi/<controller>/restDelete', 'pattern' => 'sharedApi/<controller:\w+>/<id:\d+>', 'verb' => 'DELETE'),
                array('sharedApi/<controller>/restCreate', 'pattern' => 'sharedApi/<controller:\w+>', 'verb' => 'POST'),
                //
                array('sharedApi/general/restOptions', 'pattern' => 'api/<controller:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'api/<controller:\w+>/<action:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'api/<controller:\w+>/<action:\w+>/<id:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'api/<controller:\w+>/<action:\w+>/<id:\d+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'apiLocker/<controller:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'apiLocker/<controller:\w+>/<action:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'apiLocker/<controller:\w+>/<action:\w+>/<id:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'apiLocker/<controller:\w+>/<action:\w+>/<id:\d+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'apiPos/<controller:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'apiPos/<controller:\w+>/<action:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'apiPos/<controller:\w+>/<action:\w+>/<id:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'apiPos/<controller:\w+>/<action:\w+>/<id:\d+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'sharedApi/<controller:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'sharedApi/<controller:\w+>/<action:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'sharedApi/<controller:\w+>/<action:\w+>/<id:\w+>', 'verb' => 'OPTIONS'),
                array('sharedApi/general/restOptions', 'pattern' => 'sharedApi/<controller:\w+>/<action:\w+>/<id:\d+>', 'verb' => 'OPTIONS'),
                //WEBHOOK
                array('webhook/lockerRequest/default', 'pattern' => 'webhook/lockerRequest/default', 'verb' => 'POST'),
                //
                '<module:\w+>/<controller:\w+>/<id:\d+>' => '/<module>/<controller>/view',
                '<module:\w+>/<controller:\w+>/<action:\w+>/<id:.*>' => '/<module>/<controller>/<action>',
                '<module:\w+>/<controller:\w+>/<action:\w+>' => '/<module>/<controller>/<action>',
                '<module:\w+>' => '/<module>',
            ),
        ),
        // uncomment the following to use a MySQL database
        'db' => array(
            'connectionString' => "mysql:host={$database_server};port={$database_port};dbname={$database_name}",
            'emulatePrepare' => true,
            'username' => $database_username,
            'password' => $database_password,
            'charset' => $database_charset,
            'enableProfiling' => true,
            'enableParamLogging' => true,
            'schemaCachingDuration' => 86400,
            'persistent' => true,
            'initSQLs' => [],
            'charset' => 'utf8',
        ),
        'dbSian' => array(
            'class' => 'CDbConnection',
            'connectionString' => "mysql:host={$database_server};port={$database_port};dbname={$database_sian}",
            'emulatePrepare' => true,
            'username' => $database_username,
            'password' => $database_password,
            'charset' => $database_charset,
            'enableProfiling' => true,
            'enableParamLogging' => true,
            'schemaCachingDuration' => 86400,
            'persistent' => true,
            'initSQLs' => [],
            'charset' => 'utf8',
        ),
        'mongo' => array(
            'class' => 'application.components.SIANMongo',
        ),
        'errorHandler' => $environment == YII_ENVIRONMENT_PRODUCTION ? ['errorAction' => 'site/error'] : [],
        'cache' => array(
            'class' => 'CRedisCache',
            'hostname' => '127.0.0.1',
            'port' => 6379,
            'database' => 0,
            'password' => 'hbs', //The REDIS password
            'options' => STREAM_CLIENT_CONNECT,
        ),
        'log' => array(
            'class' => 'CLogRouter',
            'routes' => array(
                array(
                    'class' => 'CFileLogRoute',
                    'levels' => 'error, warning',
                ),
            // uncomment the following to show log messages on web pages
            //    array(
            //        'class' => 'CWebLogRoute',
            //    ),
            ),
        )
    ),
    // application-level parameters that can be accessed
    // using Yii::app()->params['paramName']
    'params' => array(
        'version' => '*********',
        'versionname' => 'Recuerda! Lo importante es que hay salud.',
        'adminEmail' => '<EMAIL>',
        'supportEmail' => '<EMAIL>',
        'senderEmail' => '<EMAIL>',
        //FORMATS
        'datetime_php_format' => $datetime_php_format,
        'date_php_format' => $date_php_format,
        'time_php_format' => $time_php_format,
        'datetime_sql_format' => $datetime_sql_format,
        'date_sql_format' => $date_sql_format,
        'date_js_format' => $date_js_format,
        //RUTAS
        'domain' => $domain,
        'rdomain' => $rdomain,
        'report_domain' => $report_domain,
        'domain_react_app' => $domain_react_app,
        'report_protocol' => $report_protocol,
        'report_port' => $report_port,
        'admin' => $admin,
        'environment' => $environment,
        //E-Billing
        'e_billing_ose' => $e_billing_ose,
        'e_billing_env' => $e_billing_env,
        'e_billing_certificate_pass' => $e_billing_certificate_pass,
        'e_billing_certificate' => $e_billing_certificate,
        'e_billing_ri' => $e_billing_ri,
        'e_billing_credentials' => $e_billing_credentials,
        'e_billing_rest_credentials' => $e_billing_rest_credentials,
        'e_billing_send_automatically_to_client' => $e_billing_send_automatically_to_client,
        //
        'org' => "{$org}",
        'admin_url' => "{$domain}/{$admin}",
        'admin_dir' => $dir . DIRECTORY_SEPARATOR . $admin,
        'us_dir' => $dir . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . $us,
        'dist_dir' => "{$domain}/{$admin}" . DIRECTORY_SEPARATOR . 'protected/dist',
        'modules_dir' => "{$domain}/{$admin}" . DIRECTORY_SEPARATOR . 'protected/modules',
        'files_url' => "{$domain}/{$org}/files",
        'files_dir' => $dir . DIRECTORY_SEPARATOR . $org . DIRECTORY_SEPARATOR . 'files',
        'images_url' => "{$domain}/{$org}/images",
        'images_dir' => $dir . DIRECTORY_SEPARATOR . $org . DIRECTORY_SEPARATOR . 'images',
        'org_url' => "{$domain}/{$org}",
        'org_url2' => "{$domain2}/{$org}",
        'skin' => "{$org}",
        'org_dir' => $dir . DIRECTORY_SEPARATOR . $org,
        'media_url' => "{$domain}/{$media}",
        'media_dir' => $dir . DIRECTORY_SEPARATOR . $media,
        'soap_url' => "{$domain}/{$admin}",
        'upload_url' => "{$domain}/{$org}/files/uploads",
        'upload_dir' => $dir . DIRECTORY_SEPARATOR . $org . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'uploads',
        'download_url' => "{$domain}/{$org}/files/downloads",
        'download_dir' => $dir . DIRECTORY_SEPARATOR . $org . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'downloads',
        'hbs_url' => "{$hbs_url}",
        'hbs_username' => "{$hbs_username}",
        'hbs_password' => "{$hbs_password}",
        //DATABASE
        'database_name' => $database_name,
        'database_server' => $database_server,
        'database_username' => $database_username,
        'database_password' => $database_password,
        //MONGO
        'mongo_enabled' => $mongo_enabled,
        'mongo_server' => $mongo_server,
        'mongo_port' => $mongo_port,
        'mongo_db' => $mongo_db,
        'mongo_username' => $mongo_username,
        'mongo_password' => $mongo_password,
        //ATD
        'atd_enabled' => $atd_enabled,
        //SMTP
        'smtp_host' => $smtp_host,
        'smtp_port' => $smtp_port,
        'smtp_security' => $smtp_security,
        'smtp_username' => $smtp_username,
        'smtp_password' => $smtp_password,
        'smtp_support_username' => $smtp_support_username,
        'smtp_support_password' => $smtp_support_password,
        //JWT
        'jwt_secret' => $jwt_secret,
        'jwt_lifetime' => $jwt_lifetime,
        //PAYU
        'payu_api_key' => $payu_api_key,
        'payu_api_login' => $payu_api_login,
        'payu_merchant_id' => $payu_merchant_id,
        'payu_account_id' => $payu_account_id,
        'payu_test' => $payu_test,
        //IZIPAY
        'izipay_username' => $izipay_username,
        'izipay_password' => $izipay_password,
        'izipay_public_key' => $izipay_public_key,
        'izipay_sha256_key' => $izipay_sha256_key,
        //CALLMEBOT
        'callmebot_api_key' => $callmebot_api_key,
        //BITLY ACCESS TOKEN
        'bitly_access_token' => $bitly_access_token,
        //SERVICIOS
        'ruc_source' => $ruc_source,
        'dni_source' => $dni_source,
        'migo_token' => $migo_token,
        //SIAN-API 
        'api-sian' => $api_sian,
        //REPORT
        'report_server' => $report_server,
        //MEDIA
        'media_config' => $media_config,
        //GOOGLE APIS
        'ga_server_key' => $ga_server_key,
        'ga_browser_key' => $ga_browser_key,
        'ga_disabled' => $ga_disabled,
        //LIMITS
        'max_users' => $max_users,
        //REPORTS
        'environment_reports' => $environment_reports,
        'support_emails' => $support_emails,
        'admin_emails' => $admin_emails,
        //API
        'url_api_sian' => $url_api_sian,
    ),
    'language' => 'es'
);
