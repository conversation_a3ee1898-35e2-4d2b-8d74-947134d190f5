<?php

/**
 * @preserve jQuery DateTimePicker plugin v2.4.1
 * @homepage http://xdsoft.net/jqplugins/datetimepicker/
 * (c) 2014, Chupurnov Valeriy.
 */
class USDatePicker extends USDateTimePicker {

    /**
     * Initializes the widget.
     */
    public function init() {
        $this->options['format'] = isset($this->options['format']) ? $this->options['format'] : Yii::app()->params['date_php_format'];
        $this->options['formatDate'] = isset($this->options['formatDate']) ? $this->options['formatDate'] : Yii::app()->params['date_php_format'];
        $this->options['timepicker'] = false;

        parent::init();
    }

    /**
     * Runs the widget.
     */
    public function run() {
        parent::run();
    }

}
