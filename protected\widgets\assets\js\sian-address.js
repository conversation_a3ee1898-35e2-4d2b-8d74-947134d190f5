function SIANAddressVisible(div_id, visible)
{
    var divObj = $('#' + div_id);
    //Obtnemos
    if (visible)
    {
        divObj.show();
    } else
    {
        divObj.hide();
    }
}

function SIANAddressAddItem(div_id, dept_code, prov_code, dist_code, address, reference, lat, lng, type, errors)
{
    //DIV
    var divObj = $('#' + div_id);
    var count = parseInt(divObj.data('count'));
    var removable = divObj.data('removable');
    var readonly = divObj.data('readonly');
    var disabled = divObj.data('disabled');
    var limit = parseInt(divObj.data('limit'));
    var name = divObj.data('name');

    if (disabled)
    {
        return;
    }

    if (count < limit)
    {
        //ID
        var id = getLocalId();
        var reference_input_id = getLocalId();
        var reference_button_id = getLocalId();
        var popover_textarea_id = getLocalId();
        var popover_submit_id = getLocalId();
        var popover_cancel_id = getLocalId();

        var dept_list_id = getLocalId();
        var prov_list_id = getLocalId();
        var dist_list_id = getLocalId();
        var address_id = getLocalId();
        var lat_id = getLocalId();
        var lng_id = getLocalId();
        var type_id = getLocalId();


        var html = '<div id=\'' + id + '\' class=\'row sian-address-item\'>';
        html += '<div class=\'col-lg-2 col-md-4 col-sm-4 col-xs-12\'>';
        html += '<div class=\'form-group ' + (errors['dept_code'] ? 'has-error' : '') + '\'>';
        html += '<select id=\'' + dept_list_id + '\' class=\'form-control sian-address-item-dept-code\' maxlength=\'2\' name=\'' + name + '[' + id + '][dept_code]\' onChange=\'SIANAddressGetProvOptions(\"' + dept_list_id + '\", \"' + prov_list_id + '\", \"' + dist_list_id + '\", \"' + lat_id + '\", \"' + lng_id + '\")\' ' + (readonly ? 'readonly' : '') + ' ' + (disabled ? 'disabled' : '') + '>' + getOptionsHtml(SIANAddressGetDeptItems(), dept_code) + '</select>';
        if (errors['dept_code'])
        {
            html += '<span class=\'help-block error\'>' + errors['dept_code'] + '</span>';
        }
        html += '</div>';
        html += '</div>';

        html += '<div class=\'col-lg-2 col-md-4 col-sm-4 col-xs-12\'>';
        html += '<div class=\'form-group ' + (errors['prov_code'] ? 'has-error' : '') + '\'>';
        html += '<select id=\'' + prov_list_id + '\' class=\'form-control sian-address-item-prov-code\' maxlength=\'2\' name=\'' + name + '[' + id + '][prov_code]\' onChange=\'SIANAddressGetDistOptions(\"' + dept_list_id + '\", \"' + prov_list_id + '\", \"' + dist_list_id + '\", \"' + lat_id + '\", \"' + lng_id + '\")\' ' + (readonly ? 'readonly' : '') + ' ' + (disabled ? 'disabled' : '') + '>' + getOptionsHtml(SIANAddressGetProvItems(dept_code), prov_code) + '</select>';
        if (errors['prov_code'])
        {
            html += '<span class=\'help-block error\'>' + errors['prov_code'] + '</span>';
        }
        html += '</div>';
        html += '</div>';

        html += '<div class=\'col-lg-2 col-md-4 col-sm-4 col-xs-12\'>';
        html += '<div class=\'form-group ' + (errors['dist_code'] ? 'has-error' : '') + '\'>';
        html += '<select id=\'' + dist_list_id + '\' class=\'form-control sian-address-item-dist-code\' maxlength=\'2\' name=\'' + name + '[' + id + '][dist_code]\' onChange=\'SIANAddressClearLocation(\"' + lat_id + '\", \"' + lng_id + '\")\' ' + (readonly ? 'readonly' : '') + ' ' + (disabled ? 'disabled' : '') + '>' + getOptionsHtml(SIANAddressGetDistItems(dept_code, prov_code), dist_code) + '</select>';
        if (errors['dist_code'])
        {
            html += '<span class=\'help-block error\'>' + errors['dist_code'] + '</span>';
        }
        html += '</div>';
        html += '</div>';

        html += '<div class=\'col-lg-6 col-md-12 col-sm-12 col-xs-12\'>';
        html += '<div class=\'form-group ' + (errors['address'] ? 'has-error' : '') + '\'>';
        html += '<div class=\'input-group\'>';
        html += '<input id=\'' + address_id + '\' class=\'form-control sian-address-item-address us-cleantext\' maxlength=\'100\' name=\'' + name + '[' + id + '][address]\' type=\'text\' placeholder=\'Dirección\' value=\'' + (address === null ? '' : address) + '\' ' + (readonly ? 'readonly' : '') + ' ' + (disabled ? 'disabled' : '') + '>';
        html += '<input type=\'hidden\' id=\'' + reference_input_id + '\' class=\'sian-address-item-reference\' maxlength=\'300\' name=\'' + name + '[' + id + '][reference]\' value=\'' + (isset(reference) ? reference : '') + '\' ' + (readonly ? 'readonly' : '') + ' ' + (disabled ? 'disabled' : '') + '>';
        html += '<span class=\'input-group-addon\'>';
        if (readonly == false)
        {
            //Se deshabilitan los mapas
            //html += '<a onclick=\'SIANAddressOpenMap(\"' + div_id + '\", \"' + dept_list_id + '\", \"' + prov_list_id + '\", \"' + dist_list_id + '\", \"' + address_id + '\", \"' + lat_id + '\", \"' + lng_id + '\", \"' + type_id + '\")\' title=\'Abrir mapa\'><span class=\'fa fa-map-marker fa-lg black\'></span></a>';
            html += ' <a id=\'' + reference_button_id + '\' title=\'Especificar referencia\'><span class=\'fa fa-question-circle fa-lg black\'></span></a>';

            if (removable)
            {
                html += ' <a onclick=\'SIANAddressRemoveItem(\"' + div_id + '\",\"' + id + '\", false)\' title=\'Eliminar dirección\'><span class=\'fa fa-times fa-lg black\'></span></a>';
            }
        }
        html += '</span>';
        html += '</div>';
        if (errors['address'] > 0)
        {
            html += '<span class=\'help-block error\'>' + errors['address'] + '</span>';
        }
        html += '<input id=\'' + lat_id + '\' class=\'sian-address-item-lat\' name=\'' + name + '[' + id + '][lat]\' type=\'hidden\' value=\'' + (lat === null ? '' : lat) + '\' ' + (disabled ? 'disabled' : '') + '>';
        html += '<input id=\'' + lng_id + '\' class=\'sian-address-item-lng\' name=\'' + name + '[' + id + '][lng]\' type=\'hidden\' value=\'' + (lng === null ? '' : lng) + '\' ' + (disabled ? 'disabled' : '') + '>';
        html += '<input id=\'' + type_id + '\' class=\'sian-address-item-type\' name=\'' + name + '[' + id + '][type]\' type=\'hidden\' value=\'' + (type === null ? '' : type) + '\' ' + (disabled ? 'disabled' : '') + '>';
        html += '</div>';
        html += '</div>';

        html += '</div>';

        //COUNT DE ITEMS
        if (count === 0)
        {
            divObj.find('div.sian-address-items').html(html);
        } else
        {
            divObj.find('div.sian-address-items').append(html);
        }
        divObj.data('count', count + 1);

        var popover = '';
        popover += '<div class=\'row\'>';
        popover += '<div class=\'col-lg-12 col-md-12 col-sm-12 col-xs-12\'>';
        popover += '<textarea id=\'' + popover_textarea_id + '\' maxlength=50 rows=3></textarea>';
        popover += '</div>';
        popover += '</div>';
        popover += '<div class=\'row\'>';
        popover += '<div class=\'col-lg-6 col-md-6 col-sm-6 col-xs-6\'>';
        popover += '<button id=\'' + popover_submit_id + '\' type=\'button\' class=\'btn btn-primary btn-block\'><span class=\'fa fa-lg fa-check white\'></span></button>';
        popover += '</div>';
        popover += '<div class=\'col-lg-6 col-md-6 col-sm-6 col-xs-6\'>';
        popover += '<button id=\'' + popover_cancel_id + '\' type=\'button\' class=\'btn btn-default btn-block\'><span class=\'fa fa-lg fa-times black\'></span></button>';
        popover += '</div>';
        popover += '</div>';
        //JQUERY
        $('#' + reference_button_id).popover({
            placement: 'top',
            title: 'Especificar referencia',
            html: true,
            content: popover
        }).on('shown.bs.popover', function () {

            var currentDiv = $('#' + div_id);
            var current_disabled = currentDiv.data('disabled');

            if (current_disabled)
            {
                $('#' + reference_button_id).popover('hide')
            } else
            {
                var reference = $('#' + reference_input_id).val();
                $('#' + popover_textarea_id).val(reference).focus();
            }

        }).on('click', function () {
            $('#' + popover_submit_id).click(function () {
                var reference = $('#' + popover_textarea_id).val();
                $('#' + reference_input_id).val(reference);
                $('#' + reference_button_id).click();
            });
            $('#' + popover_cancel_id).click(function () {
                $('#' + reference_button_id).click();
            });
        });
    } else
    {
        bootbox.alert(us_message('El máximo permitido es ' + limit + ' direccion(es)!', 'warning'));
    }
}

function SIANAddressRemoveItem(div_id, id, confirmation)
{
    if (confirmation ? confirm('¿Está seguro de eliminar este ítem?') : true)
    {
        var divObj = $('#' + div_id);
        var count = parseInt(divObj.data('count'));
        var zero_warning = divObj.data('zero_warning');

        $('#' + id).remove();

        if (count === 1)
        {
            divObj.find('div.sian-address-items').html(STRINGS_NO_DATA);
            //Si zero_warning está definido
            if (zero_warning.length > 0)
            {
                $.notify(zero_warning, 'error');
            }
        }

        //COUNT DE ITEMS
        divObj.data('count', count - 1);
    }
}

function SIANAddressReplace(div_id, dept_code, prov_code, dist_code, address, reference, lat, lng, type)
{
    var divObj = $('#' + div_id);
    divObj.data('count', 0);
    divObj.find('div.sian-address-items').empty();

    SIANAddressAddItem(div_id, dept_code, prov_code, dist_code, address, reference, lat, lng, type, []);
}

function SIANAddressGetDeptOptions(dept_id, prov_id, dist_id, lat_id, lng_id)
{
    var dept_items = SIANAddressGetDeptItems();

    $('#' + dept_id).html(getOptionsHtml(dept_items));
    $('#' + prov_id).html(getOptionsHtml());
    $('#' + dist_id).html(getOptionsHtml());
    SIANAddressClearLocation(lat_id, lng_id);

}

function SIANAddressGetProvOptions(dept_id, prov_id, dist_id, lat_id, lng_id)
{
    var dept_code = $('#' + dept_id).val();
    var prov_items = SIANAddressGetProvItems(dept_code);

    $('#' + prov_id).html(getOptionsHtml(prov_items));
    $('#' + dist_id).html(getOptionsHtml());
    SIANAddressClearLocation(lat_id, lng_id);

}

function SIANAddressGetDistOptions(dept_id, prov_id, dist_id, lat_id, lng_id)
{
    var dept_code = $('#' + dept_id).val();
    var prov_code = $('#' + prov_id).val();

    var dist_items = SIANAddressGetDistItems(dept_code, prov_code);

    $('#' + dist_id).html(getOptionsHtml(dist_items));
    SIANAddressClearLocation(lat_id, lng_id);
}

function SIANAddressClearLocation(lat_id, lng_id)
{
    $('#' + lat_id).val(null);
    $('#' + lng_id).val(null);
}

function SIANAddressGetDeptItems()
{
    var geoloc = window.geoloc;

    var data = {};

    $.each(geoloc, function (dept_code, dept) {
        data[dept_code] = dept.name;
    });

    return data;
}

function SIANAddressGetProvItems(dept_code)
{
    var geoloc = window.geoloc;

    var data = {};

    if (dept_code !== null && dept_code !== '')
    {
        $.each(geoloc[dept_code].items, function (prov_code, prov) {
            data[prov_code] = prov.name;
        });
    }
    return data;
}

function SIANAddressGetDistItems(dept_code, prov_code)
{
    var geoloc = window.geoloc;

    var data = {};

    if (dept_code !== null && dept_code !== '' && prov_code !== null && prov_code !== '')
    {
        $.each(geoloc[dept_code].items[prov_code].items, function (dist_code, dist) {
            data[dist_code] = dist.name;
        });
    }

    return data;
}

//SE DESHABILITAN LOS MAPAS
//function SIANAddressOpenMap(div_id, dept_id, prov_id, dist_id, address_id, lat_id, lng_id, type_id)
//{
//
//    //VAR TRUJILLO
//    var default_location = new google.maps.LatLng(-8.112000, -79.028799);
//    //INPUTS
//    var div = $('#' + div_id);
//    var dept = $('#' + dept_id);
//    var prov = $('#' + prov_id);
//    var dist = $('#' + dist_id);
//    var address = $('#' + address_id);
//    var lat = $('#' + lat_id);
//    var lng = $('#' + lng_id);
//
//    var modal = div.closest('div.us-modal');
//
//    var pac_id = getLocalId();
//    var map_id = getLocalId();
//
//
//    if (dist.val().length === 0)
//    {
//        bootbox.alert(us_message('Debe elegir un distrito!', 'warning'));
//        dist.focus();
//        return;
//    }
//
//    var html = '';
//    html += '<input id=\'' + pac_id + '\' class=\'pac-input\' style=\'width:300px;\' type=\'text\' placeholder=\'Ingrese una búsqueda\'/>';
//    html += '<div id=\'' + map_id + '\' class=\'us-map-container\'></div>';
//
//    var dialog = bootbox.dialog({
//        message: html,
//        title: 'Elegir ubicación',
//        className: 'us-modal',
//        buttons: {
//            success: {
//                label: 'Guardar!',
//                className: 'btn-primary',
//                callback: function () {
//                    lat.val(dialog.data('lat'));
//                    lng.val(dialog.data('lng'));
//                    //Le damos los valores temporales
//                    SIANAddressShowModal(modal);
//                    --window.has_beforeunload;
//                }
//            },
//            default: {
//                label: 'Cancelar!',
//                className: 'btn-default',
//                callback: function () {
//                    SIANAddressShowModal(modal);
//                    --window.has_beforeunload;
//                }
//            }
//        }
//    });
//
//    if (typeof modal !== 'undefined')
//    {
//        //El padre será el modal
//        dialog.attr('parent_id', modal.attr('id'));
//        //Le damos el ID del input para hacer focus
//        modal.data('focus', address_id);
//    }
//
//    //Latitud y longitud temporal
//    dialog.data('lat', '');
//    dialog.data('lng', '');
//
//    //MAPS
//    var markers = [];
//    var map = new google.maps.Map(document.getElementById(map_id), {
//        zoom: 15,
//        mapTypeId: 'roadmap'
//    });
//
//    //Foco al buscador
//    if (lat.val().length > 0 && lng.val().length > 0)
//    {
//        default_location = new google.maps.LatLng(parseFloat(lat.val()), parseFloat(lng.val()));
//        SIANAddressAddMarker(default_location);
//    } else
//    {
//        $('#' + pac_id).val(dist.find('option:selected').text() + ' ' + prov.find('option:selected').text() + ' ' + dept.find('option:selected').text());
//        $('#' + pac_id).focus();
//    }
//
//    //Centramos mapa
//    map.setCenter(default_location);
//
//    // Create the search box and link it to the UI element.
//    var input = (document.getElementById(pac_id));
//    map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);
//
//    var searchBox = new google.maps.places.SearchBox((input));
//
//    // Listen for the event fired when the user selects an item from the
//    // pick list. Retrieve the matching places for that item.
//    google.maps.event.addListener(searchBox, 'places_changed', function () {
//        var places = searchBox.getPlaces();
//
//        if (places.length == 0) {
//            return;
//        }
//        for (var i = 0, marker; marker = markers[i]; i++) {
//            marker.setMap(null);
//        }
//
//        // For each place, get the icon, place name, and location.
//        markers = [];
//        for (var i = 0, place; place = places[i]; i++) {
//            // Create a marker for each place.
//            var marker = SIANAddressAddMarker(place.geometry.location, place.name);
//
//            markers.push(marker);
//            default_location = place.geometry.location;
//            //Mandamos la localización a los campos
//            SIANAddressSetLocation(default_location);
//
//        }
//
//        map.setCenter(default_location);
//    });
//
//    // Bias the SearchBox results towards places that are within the bounds of the
//    // current map's viewport.
//    google.maps.event.addListener(map, 'bounds_changed', function () {
//        var bounds = map.getBounds();
//        searchBox.setBounds(bounds);
//    });
//
//    //Agregamos marcador al mapa
//    function SIANAddressAddMarker(location, title)
//    {
//        if (typeof title === 'undefined')
//        {
//            title = '';
//        }
//
//        var marker = new google.maps.Marker({
//            map: map,
//            title: title,
//            draggable: true,
//            position: location
//        });
//
//        //Cuando termino de arrastrar el marcador,, obtendré la posición
//        google.maps.event.addListener(marker, 'dragend', function (e) {
//            SIANAddressSetLocation(this.getPosition());
//        });
//
//        return marker;
//    }
//
//    function SIANAddressSetLocation(location)
//    {
//        dialog.data('lat', location.lat());
//        dialog.data('lng', location.lng());
//    }
//
//}

function SIANAddressShowModal(modal)
{
    if (typeof modal !== 'undefined')
    {
        modal.modal('show');
    }
}

function SIANAddressDisabled(div_id, disabled)
{
    //DIV
    var divObj = $('#' + div_id);
    divObj.data('disabled', disabled);

    divObj.find('div.sian-address-items div.sian-address-item').each(function ()
    {
        var rowObj = $(this);

        rowObj.find('select.sian-address-item-dept-code').prop('disabled', disabled);
        rowObj.find('select.sian-address-item-prov-code').prop('disabled', disabled);
        rowObj.find('select.sian-address-item-dist-code').prop('disabled', disabled);
        rowObj.find('input.sian-address-item-address').prop('disabled', disabled);
        rowObj.find('input.sian-address-item-reference').prop('disabled', disabled);
        rowObj.find('input.sian-address-item-lat').prop('disabled', disabled);
        rowObj.find('input.sian-address-item-lng').prop('disabled', disabled);
        rowObj.find('input.sian-address-item-type').prop('disabled', disabled);
    });
}


// FUNCIONES A CREAR EN sian-address, a parte de SIANAddressEnabled
function SIANAddressClean(div_id)
{
    var divObj = $('#' + div_id);

    divObj.find('div.sian-address-items div.sian-address-item').remove();

    //COUNT DE ITEMS
    divObj.data('count', 0);
}

function SIANAddressCount(div_id)
{
    var divObj = $('#' + div_id);
    //COUNT DE ITEMS
    return divObj.data('count');
}