<?php

class <PERSON><PERSON><PERSON><PERSON> extends CWidget {

    public $form;
    public $model;
    //ITEMS
    public $identification_type_items;
    public $phone_type_items;
    public $email_type_items;
    public $category_items;
    public $convention_items;
    public $bank_account_bank_items;
    public $bank_account_type_items;
    public $bank_account_currency_items;
    public $user_items;
    public $category_person_items;
    public $active_agreements;
    //IDS
    public $verification_id;
    public $paternal_id;
    public $not_domiciled_id;
    public $convention_type_id;
    public $country_id;
    public $maternal_id;
    public $firstname_id;
    public $person_name_id;
    public $identification_type_id;
    public $identification_number_id;
    public $birthday_id;
    public $phone_div_id;
    public $email_div_id;
    public $address_div_id;
    public $get_info_id;
    public $advanced_id;
    public $bankaccount_div_id;
    public $is_transport_company_id;
    public $modal_id = null;
    public $identification_type_readonly = false;
    //PRIVATE
    private $controller;
    //CONTROL
    public $lockPrincipal = false;
    public $sunat_route = "/administration/person/getRUCData";
    public $reniec_route = "/administration/person/getDNIData";

    public function init() {

        $this->controller = Yii::app()->controller;

        //IDS
        $this->identification_type_id = isset($this->identification_type_id) ? $this->identification_type_id : $this->controller->getServerId();
        $this->identification_number_id = isset($this->identification_number_id) ? $this->identification_number_id : $this->controller->getServerId();
        $this->person_name_id = isset($this->person_name_id) ? $this->person_name_id : $this->controller->getServerId();
        $this->verification_id = isset($this->verification_id) ? $this->verification_id : $this->controller->getServerId();
        $this->not_domiciled_id = isset($this->not_domiciled_id) ? $this->not_domiciled_id : $this->controller->getServerId();
        $this->convention_type_id = isset($this->convention_type_id) ? $this->convention_type_id : $this->controller->getServerId();
        $this->country_id = isset($this->country_id) ? $this->country_id : $this->controller->getServerId();
        $this->paternal_id = isset($this->paternal_id) ? $this->paternal_id : $this->controller->getServerId();
        $this->maternal_id = isset($this->maternal_id) ? $this->maternal_id : $this->controller->getServerId();
        $this->firstname_id = isset($this->firstname_id) ? $this->firstname_id : $this->controller->getServerId();
        $this->birthday_id = isset($this->birthday_id) ? $this->birthday_id : $this->controller->getServerId();
        $this->phone_div_id = isset($this->phone_div_id) ? $this->phone_div_id : $this->controller->getServerId();
        $this->email_div_id = isset($this->email_div_id) ? $this->email_div_id : $this->controller->getServerId();
        $this->address_div_id = isset($this->address_div_id) ? $this->address_div_id : $this->controller->getServerId();
        $this->bankaccount_div_id = isset($this->bankaccount_div_id) ? $this->bankaccount_div_id : $this->controller->getServerId();
        $this->get_info_id = isset($this->get_info_id) ? $this->get_info_id : $this->controller->getServerId();

        $this->advanced_id = isset($this->advanced_id) ? $this->advanced_id : $this->controller->getServerId();
        $this->is_transport_company_id = isset($this->is_transport_company_id) ? $this->is_transport_company_id : $this->controller->getServerId();
        $this->convention_items = isset($this->convention_items) ? $this->convention_items : [];
        $this->identification_type_readonly = isset($this->identification_type_readonly) ? $this->identification_type_readonly : false;

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        " . ($this->model->requireJuridicalValidation() ? "$('.natural').hide(0); $('#{$this->is_transport_company_id}').show('fast');" : "$('.juridical').hide(0); $('#{$this->is_transport_company_id}').hide('fast');") . "
        ");

        if (!$this->lockPrincipal) {

            Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

            $('body').on('click', '#{$this->get_info_id}', function(e) {
               
                if(!$(this).hasAttr('disabled'))
                {
                    var identification_type = $('#{$this->identification_type_id}').val();
                    var identification_number = $('#{$this->identification_number_id}').val();
                        
                    if(identification_type === '" . USIdentificationValidator::RUC . "' && identification_number.length > 0)
                    {
                        //Desactivamos el botón para evitar reiteradas solicitudes.
                        waitingDialog.show(STRINGS_WAITING_MESSAGE);
                        USLinkStatus('{$this->get_info_id}', false, {});

                        $.ajax({
                            type: 'GET',
                            url: '{$this->controller->createUrl($this->sunat_route)}',
                            data: {
                                ruc: identification_number
                            },
                            beforeSend: function (xhr) {
                                window.active_ajax++;
                                //Ocultamos los tooltip
                                $('div.ui-tooltip').remove();
                            },                               
                            success: function(data) {
                                if(data.code == " . USREST::CODE_SUCCESS . ")
                                {
                                    $('#{$this->verification_id}').val('" . Person::VERIFICATION_VERIFIED . "');
                                    $('#{$this->identification_type_id}').attr('readonly', true);
                                    $('#{$this->identification_number_id}').val(data.data.identification_number).attr('readonly', true);
                                    $('#{$this->person_name_id}').val(data.data.person_name).attr('readonly', true);
                                    if(isset(data.data.birthday))
                                    {
                                        $('#{$this->birthday_id}').val(data.data.birthday).attr('readonly', true);
                                    }
                                    if(data.data.dept_code !=null &&  data.data.prov_code!= null && data.data.dist_code!= null && data.data.address != null){
                                        SIANAddressReplace('{$this->address_div_id}', data.data.dept_code, data.data.prov_code, data.data.dist_code, data.data.address, '', null, null, null);
                                    }                                    
                                }
                                else
                                {
                                    bootbox.alert(us_message(data.message, 'warning'));
                                }
                                
                                //Activamos el botón
                                waitingDialog.hide();
                                USLinkStatus('{$this->get_info_id}', true, {});

                                window.active_ajax--;
                            },
                            error: function(request, status, error) { // if error occured
                                bootbox.alert(us_message(request.responseText, 'error'));
                                waitingDialog.hide();
                                USLinkStatus('{$this->get_info_id}', true, {});

                                window.active_ajax--;
                            },
                            dataType: 'json'
                        });
                    }

                    if(identification_type === '" . USIdentificationValidator::DNI . "' && identification_number.length > 0)
                    {
                        //Desactivamos el botón para evitar reiteradas solicitudes.
                        waitingDialog.show(STRINGS_WAITING_MESSAGE); 
                        USLinkStatus('{$this->get_info_id}', false, {});
                            
                        $.ajax({
                            type: 'GET',
                            url: '{$this->controller->createUrl($this->reniec_route)}',
                            data: {
                                dni: identification_number
                            },
                            beforeSend: function (xhr) {
                                window.active_ajax++;
                                //Ocultamos los tooltip
                                $('div.ui-tooltip').remove();
                            },                               
                            success: function(data) {

                                if(data.code == " . USREST::CODE_SUCCESS . ")
                                {
                                    $('#{$this->verification_id}').val('" . Person::VERIFICATION_VERIFIED . "');
                                    $('#{$this->identification_type_id}').attr('readonly', true);
                                    $('#{$this->identification_number_id}').val(data.data.identification_number).attr('readonly', true);
                                    $('#{$this->paternal_id}').val(data.data.paternal).attr('readonly', true);
                                    $('#{$this->maternal_id}').val(data.data.maternal).attr('readonly', true);
                                    $('#{$this->firstname_id}').val(data.data.firstname).attr('readonly', true);
                                }
                                else
                                {
                                    bootbox.alert(us_message(data.message, 'warning'));
                                }
                                
                                //Activamos el botón
                                waitingDialog.hide();
                                USLinkStatus('{$this->get_info_id}', true, {});

                                window.active_ajax--;
                            },
                            error: function(request, status, error) { // if error occured
                                bootbox.alert(us_message(request.responseText, 'error'));
                                waitingDialog.hide();
                                USLinkStatus('{$this->get_info_id}', true, {});

                                window.active_ajax--;
                            },
                            dataType: 'json'
                        });
                    }
                }
            });

            $('#{$this->identification_type_id}').change(function (){

                var type = 'natural';
                var length;
                var link_status = false;

                // Es empresa de transporte?
                $('#{$this->is_transport_company_id}').hide('fast');
                
                switch($(this).val())
                {
                    case '" . USIdentificationValidator::DNI . "':
                        length = " . USIdentificationValidator::DNI_LONGITUD . ";
                        link_status = true;
                        break;
                    case '" . USIdentificationValidator::CARNET . "':
                        length = " . USIdentificationValidator::CARNET_LONGITUD . ";
                        type = 'juridical';
                        break; 
                    case '" . USIdentificationValidator::RUC . "':
                        length = " . USIdentificationValidator::RUC_LONGITUD . ";
                        type = 'juridical';
                        link_status = true;
                        $('#{$this->is_transport_company_id}').show('fast');
                        break;     
                    case '" . USIdentificationValidator::PASAPORTE . "':
                        length = " . USIdentificationValidator::PASAPORTE_LONGITUD . ";
                        break;
                    case '" . USIdentificationValidator::CEDULA . "':
                        length = " . USIdentificationValidator::CEDULA_LONGITUD . ";
                        break;
                    case '" . USIdentificationValidator::OTRO . "':
                        type = 'any';
                        length = " . USIdentificationValidator::OTRO_LONGITUD . ";
                        break;
                }

                $('#{$this->identification_number_id}').attr('maxlength',length);

                if(type == 'juridical' || type == 'any')
                {
                    $('.juridical').show('fast');
                    $('.natural').hide('fast');
                }
                else
                {
                    $('.natural').show('fast');
                    $('.juridical').hide('fast');
                }
                
                USLinkStatus('{$this->get_info_id}', link_status, {});

            });
           
            ");
        }

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        $('#{$this->advanced_id}').change(function (){

            var advanced = $(this).val();

            if(advanced == 1)
            {
                $('.sian-advanced').show(100);
                $('.sian-basic').each(function(index) {
                    var element = $(this);
                    var basic_span = element.data('basic_span');
                    var advanced_span = element.data('advanced_span');

                    element.removeClass(basic_span);
                    element.addClass(advanced_span);
                });
            }
            else
            {
                $('.sian-advanced').hide(100);
                 $('.sian-basic').each(function(index) {
                    var element = $(this);
                    var basic_span = element.data('basic_span');
                    var advanced_span = element.data('advanced_span');

                    element.removeClass(advanced_span);
                    element.addClass(basic_span);
                });
            }
            
            $('#{$this->phone_div_id}').data('advanced', advanced);
            $('#{$this->email_div_id}').data('advanced', advanced);    
        });
        
        $('#{$this->advanced_id}').change();

        ");
    }

    private function renderIdentification() {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Identificación',
            'headerIcon' => 'user'
        ));

        echo $this->form->dropDownListRow($this->model, 'identification_type', $this->identification_type_items, array(
            'id' => $this->identification_type_id,
            'readonly' => !$this->model->isNewRecord || $this->model->verification === Person::VERIFICATION_VERIFIED || $this->identification_type_readonly,
        ));

        echo $this->form->textFieldRow($this->model, 'identification_number', array(
            'id' => $this->identification_number_id,
            'maxlength' => USIdentificationValidator::getLength($this->model->identification_type),
            'placeholder' => $this->model->getAttributeLabel('identification_number'),
            'readonly' => !$this->model->isNewRecord || $this->model->verification === Person::VERIFICATION_VERIFIED,
            'append' => $this->widget('application.widgets.USLink', array(
                'id' => $this->get_info_id,
                'title' => 'Obtener datos de SUNAT/RENIEC',
                'icon' => 'fa fa-lg fa-user',
                'visible' => in_array($this->model->identification_type, [USIdentificationValidator::DNI, USIdentificationValidator::RUC]) && $this->model->verification !== Person::VERIFICATION_VERIFIED,
                    ), true)
        ));

        $this->widget('application.widgets.USDatePicker', array(
            'id' => $this->birthday_id,
            'form' => $this->form,
            'model' => $this->model,
            'attribute' => 'birthday',
            'options' => array(
                'maxDate' => SIANTime::formatDate(),
            ),
            'htmlOptions' => array(
                'readonly' => $this->model->identification_type == USIdentificationValidator::RUC && $this->model->verification === Person::VERIFICATION_VERIFIED
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USSwitch', array(
            'id' => $this->not_domiciled_id,
            'model' => $this->model,
            'attribute' => 'not_domiciled',
            'switchChange' => "
                USAutocompleteRequired('{$this->country_id}', this.checked);
                "
        ));

        echo "</div>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'convention', $this->convention_items, array(
            'id' => $this->convention_type_id,
            'hint' => 'Para evitar la doble tributación',
        ));
        echo "</div>";
        echo "</div>";

        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->country_id,
            'model' => $this->model,
            'attribute' => 'country',
            'hideText' => true,
            'view' => array(
                'model' => 'DpMultitable',
                'scenario' => Multitable::CATALOG_35,
                'attributes' => array(
                    array('name' => 'multi_id', 'hidden' => true, 'types' => array('id', 'value')),
                    array('name' => 'value', 'width' => 20),
                    array('name' => 'description', 'width' => 80, 'types' => array('aux', 'text')),
                )
            ),
        ));

        echo "<div class='sian-advanced'>";
        $this->widget('application.widgets.USSwitch', array('model' => $this->model, 'attribute' => 'status'));
        echo "</div>";

        $this->endWidget();
    }

    private function renderName() {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Apellidos y nombres / Razón Social',
            'headerIcon' => 'list'
        ));

        echo "<div class='natural'>";
        echo $this->form->textFieldRow($this->model, 'paternal', array(
            'id' => $this->paternal_id,
            'maxlength' => 30,
            'readonly' => $this->lockPrincipal || $this->model->verification === Person::VERIFICATION_VERIFIED,
        ));
        echo $this->form->textFieldRow($this->model, 'maternal', array(
            'id' => $this->maternal_id,
            'maxlength' => 30,
            'readonly' => $this->lockPrincipal || $this->model->verification === Person::VERIFICATION_VERIFIED,
        ));
        echo $this->form->textFieldRow($this->model, 'firstname', array(
            'id' => $this->firstname_id,
            'maxlength' => 40,
            'readonly' => $this->lockPrincipal || $this->model->verification === Person::VERIFICATION_VERIFIED,
        ));

        echo "<div class='sian-advanced'>";
        echo $this->form->textAreaRow($this->model, 'description', array(
            'rows' => 3,
            'readonly' => $this->lockPrincipal,
        ));
        echo "</div>";
        echo "</div>";

        echo "<div class='juridical'>";
        echo $this->form->textFieldRow($this->model, 'person_name', array(
            'id' => $this->person_name_id,
            'maxlength' => 100,
            'readonly' => $this->lockPrincipal || $this->model->verification === Person::VERIFICATION_VERIFIED,
        ));
        echo "</div>";
        $this->endWidget();
    }

    private function renderOthers() {

        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        $this->widget('SIANPhone', array(
            'id' => $this->phone_div_id,
            'model' => $this->model,
            'types' => $this->phone_type_items,
            'advanced' => $this->model->advanced,
            'class_panel' => 'sian-advanced'
        ));
        echo "</div>";

        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        $this->widget('SIANEmail', array(
            'id' => $this->email_div_id,
            'model' => $this->model,
            'attribute' => 'tempEmails',
            'types' => $this->email_type_items,
            'advanced' => $this->model->advanced,
            'class_panel' => 'sian-advanced'
        ));
        echo "</div>";
        echo "</div>";

        //
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->widget('SIANBankAccount', array(
            'id' => $this->bankaccount_div_id,
            'model' => $this->model,
            'attribute' => 'tempBankAccounts',
            'bank_items' => $this->bank_account_bank_items,
            'type_items' => $this->bank_account_type_items,
            'currency_items' => $this->bank_account_currency_items,
            'advanced' => $this->model->advanced,
            'class_panel' => 'sian-advanced'
        ));
        echo "</div>";
        echo "</div>";
    }

    private function renderGovermentSpecificactions () {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Otros',
            'headerIcon' => 'certificate',
            'htmlOptions' => array(
                'class' => 'sian-advanced'
            )
        ));

        echo "<div class='row' style='display: flex; flex-wrap: wrap'>";

        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-6'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'is_gov',
            'hint' => 'Indica si es una entidad del estado.'
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-6'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'is_associated',
            'hint' => 'Para recibir consumos internos y movilidades',
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-6'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'is_dealer',
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-6'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'retention',
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-6'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'perception',
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-6'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'good_contributor',
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-6' id='{$this->is_transport_company_id}'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'is_transport_company',
            'hint' => 'Indica si es una empresa de transporte.',
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-4 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'catchment', Person::getCatchmentItems(), array(
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-4 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'category_id', $this->category_person_items, array(
            'empty' => Strings::SELECT_OPTION,
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-4 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'user_catchment_id', $this->user_items, array(
            'empty' => Strings::SELECT_OPTION,
        ));
        echo "</div>";

        if(!empty($this->active_agreements)){
            echo "<div class='col-lg-3 col-md-4 col-sm-6 col-xs-12'>";
            echo $this->form->dropDownListRow($this->model, 'agreement_id', $this->active_agreements, array(
                'empty' => Strings::SELECT_OPTION,
            ));
            echo "</div>";
        }
        
        echo "</div>";

        $this->endWidget();
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo $this->form->hiddenField($this->model, 'verification', [
            'id' => $this->verification_id,
        ]);
        echo $this->form->hiddenField($this->model, 'advanced', [
            'id' => $this->advanced_id,
        ]);

        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-12'>";
        $this->renderIdentification();
        echo "</div>";
        echo "<div class = 'col-lg-8 col-md-8 col-sm-6 col-xs-12'>";
        $this->renderName();
        $this->renderGovermentSpecificactions();
        echo "</div>";
        echo "</div>";
        //
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->widget('SIANAddress', array(
            'id' => $this->address_div_id,
            'model' => $this->model,
            'aclaration' => 'La primera dirección será considerada como domicilio fiscal',
            'limit' => 30,
        ));
        echo "</div>";
        echo "</div>";

        $this->renderOthers();
    }

}
