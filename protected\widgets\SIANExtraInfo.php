<?php

class SIANExtraInfo extends CWidget {

    public $id;
    public $form;
    public $model;
    public $warehouse_label = null;
    public $aux_warehouse_label = null;
    public $cashbox_label = null;
    public $warehouse_filters = [];
    public $cashbox_items = [];
    public $cashbox_filters = [];
    public $readonly = false;
    public $single_readonly = [];
    public $empty_warehouse_label = Strings::SELECT_OPTION;
    //IDS
    public $warehouse_input_id;
    public $aux_warehouse_input_id;
    public $cashbox_input_id;
    public $delivery_date_input_id = null;
    public $combination_span_id = null;
    public $kardex_clock_input_id = null;
    public $validate_combination_input_id = null;
    public $title = "Información adicional";
    public $shipping_type_input_id = null;
    public $manual_address_input_id = null;
    public $upgrade_document_code_input_id = null;
    public $dispatch_document_code_input_id = null;
    public $transport_company_id = null;
    public $require_address_input_id = null;
    public $liquidation_date_input_id = null;
    public $cashbox_amount_id = null;
    public $require_company_input_id = null;
    public $require_contact_input_id = null;
    public $require_locker_input_id = null;
    //Externo
    public $person_type_input_id = null;
    //CONTROL
    public $onChangeWarehouse = '';
    public $onChangeUpgradeDocument = '';
    public $onChangeShippingType = '';
    public $onChangeKardexRLock = '';
    public $onChangeKardexCLock = '';
    public $orientation = 'V';
    //PRIVATE
    private $controller;
    private $shipping_type_items;
    private $upgrade_document_items;
    private $dispatch_document_items;

    const ORIENTATION_VERTICAL = 'V';
    const ORIENTATION_HORIZONTAL = 'H';

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->warehouse_input_id = isset($this->warehouse_input_id) ? $this->warehouse_input_id : $this->controller->getServerId();
        $this->aux_warehouse_input_id = isset($this->aux_warehouse_input_id) ? $this->aux_warehouse_input_id : $this->controller->getServerId();
        $this->cashbox_input_id = isset($this->cashbox_input_id) ? $this->cashbox_input_id : $this->controller->getServerId();
        $this->delivery_date_input_id = isset($this->delivery_date_input_id) ? $this->delivery_date_input_id : $this->controller->getServerId();
        $this->combination_span_id = isset($this->combination_span_id) ? $this->combination_span_id : $this->controller->getServerId();
        $this->validate_combination_input_id = isset($this->validate_combination_input_id) ? $this->validate_combination_input_id : $this->controller->getServerId();
        $this->kardex_clock_input_id = isset($this->kardex_clock_input_id) ? $this->kardex_clock_input_id : $this->controller->getServerId();
        $this->shipping_type_input_id = isset($this->shipping_type_input_id) ? $this->shipping_type_input_id : $this->controller->getServerId();
        $this->manual_address_input_id = isset($this->manual_address_input_id) ? $this->manual_address_input_id : $this->controller->getServerId();
        $this->upgrade_document_code_input_id = isset($this->upgrade_document_code_input_id) ? $this->upgrade_document_code_input_id : $this->controller->getServerId();
        $this->dispatch_document_code_input_id = isset($this->dispatch_document_code_input_id) ? $this->dispatch_document_code_input_id : $this->controller->getServerId();
        $this->transport_company_id = isset($this->transport_company_id) ? $this->transport_company_id : $this->controller->getServerId();
        $this->require_address_input_id = isset($this->require_address_input_id) ? $this->require_address_input_id : $this->controller->getServerId();
        $this->liquidation_date_input_id = isset($this->liquidation_date_input_id) ? $this->liquidation_date_input_id : $this->controller->getServerId();
        $this->cashbox_amount_id = isset($this->cashbox_amount_id) ? $this->cashbox_amount_id : $this->controller->getServerId();
        $this->require_company_input_id = isset($this->require_company_input_id) ? $this->require_company_input_id : $this->controller->getServerId();
        $this->require_contact_input_id = isset($this->require_contact_input_id) ? $this->require_contact_input_id : $this->controller->getServerId();
        $this->require_locker_input_id = isset($this->require_locker_input_id) ? $this->require_locker_input_id : $this->controller->getServerId();
        $this->shipping_type_items = (new DpShippingType())->findAll();
        $this->upgrade_document_items = ExtraInfo::getUpgradeDocumentsItems($this->model->route, Person::TYPE_ANY);
        $this->dispatch_document_items = ExtraInfo::getDispatchDocumentsItems($this->model->route, Person::TYPE_ANY);
        //CAJAS
        $cashboxItems = [];

        foreach ($this->cashbox_items as $cashbox) {
            $add = true;
            foreach ($this->cashbox_filters as $field => $value) {
                $add = $add && ($cashbox->{$field} == $value);
            }

            if ($add) {
                $cashboxItems[$cashbox->cashbox_id] = $cashbox->cashbox_name;
            }
        }

        $this->cashbox_items = $cashboxItems;

        //Registramos assets
        SIANAssets::registerScriptFile('js/us-span.js');
        SIANAssets::registerScriptFile('js/sian-extra-info.js');

        Yii::app()->clientScript->registerScript($this->id, "
            
        //EVENTOS
        $(document).ready(function()
        {
            var div = $('#{$this->id}');
            SIANExtraInfoFillShippingTypes('{$this->shipping_type_input_id}', " . json_encode($this->shipping_type_items) . ", '" . (isset($this->model->extraInfo) ? $this->model->extraInfo->shipping_type : null) . "');
            " . ($this->model->scenario->include_warehouse ? "
            div.data('warehouse_filters', " . json_encode($this->warehouse_filters) . ");
            div.data('warehouseUrl', '{$this->controller->createUrl("/movement/getWarehouses")}');
            div.data('kardex_clock_input_id', '{$this->kardex_clock_input_id}');
            div.data('upgrade_document_items', " . json_encode($this->upgrade_document_items) . " );
            SIANExtraInfoLoadWarehouses('{$this->id}', '{$this->model->extraInfo->warehouse_id}', '{$this->warehouse_input_id}', '{$this->empty_warehouse_label}');
            SIANExtraInfoLoadWarehouses('{$this->id}', '{$this->model->extraInfo->aux_warehouse_id}', '{$this->aux_warehouse_input_id}', '{$this->empty_warehouse_label}');                        
            " : "") . (isset($this->model->extraInfo) ? "SIANExtraInfoLoadUpgradeDocuments();" : "") . "
        });
        
        $('body').on('change', '#{$this->warehouse_input_id}', function(e) {
            
            //consoleLog('Se inicia cambio de almacén (ExtraInfo)');

            var warehouseObj = $('#{$this->warehouse_input_id}');
            var optionObj = warehouseObj.find('option:selected');
            
            var warehouse_id = optionObj.val();
            var warehouse_name = !isBlank(warehouse_id) ? optionObj.text() : '';
            var last = warehouseObj.data('last');
            
            var changeData = {
                warehouse_id : isset(warehouse_id) ? warehouse_id: '',
                warehouse_name: warehouse_name,
                realChange: (last != warehouse_id)
            };
            {$this->onChangeWarehouse};
            //consoleLog('Cambio de almacén a: ' + warehouse_id);
            warehouseObj.data('last', warehouse_id);
        }); 
        
        $('body').on('change', '#{$this->upgrade_document_code_input_id}', function(e) {
            
            var selectObj = $(this);
            var optionObj = selectObj.find('option:selected');
            var internal = optionObj.data('internal');
            var exclude_document = (internal == 1 ? DOCUMENT_REMISSION_GUIDE : '');
            var dispatch_document_items = " . json_encode($this->dispatch_document_items) . ";            
            " . (isset($this->model->extraInfo) ? "SIANExtraInfoFillDispatchDocuments('{$this->dispatch_document_code_input_id}', dispatch_document_items, '{$this->model->extraInfo->dispatch_document_code}', exclude_document); " : "" ) .
                "{$this->onChangeUpgradeDocument}
        }); 
            
        //FUNCIONES        
        $('#{$this->id}').data('changeOperation', function(operationChangeData){
            " . ($this->model->scenario->include_combination ? "

            SIANExtraInfoFillCombinations('{$this->id}', operationChangeData.combination_ids);
            
            var last_change_status  = $('#{$this->combination_span_id}').data('last_change_status');
            var change_status = (isset(operationChangeData.combination_ids) && operationChangeData.combination_ids.length > 0 && operationChangeData.cc_dependence);
            
            USSpanSetReadonly('{$this->combination_span_id}', !change_status);
            USSpanSetRequired('{$this->combination_span_id}', change_status);
            if(!change_status)
            {
                USSpanClear('{$this->combination_span_id}');
            }

            $('#{$this->validate_combination_input_id}').val(change_status ? 1: 0);
                
            //Si hubo un cambio real
            if(change_status && last_change_status !== change_status)
            {
                $.notify('Se activó el campo \'Centro de Costo\', por favor revise la sección \'{$this->title}\' en la parte inferior del formulario.', {
                    className: 'info',
                    showDuration: 50,
                    hideDuration: 50,
                    autoHideDelay: 5000,
                }); 
            }
            //Guardamos data de la operación
            $('#{$this->combination_span_id}').data('last_change_status', change_status);    

            " : "") . "
        });
        
        $('#{$this->id}').data('changeBusinessUnit', function(changeData){
            var divObj = $('#{$this->id}');
            divObj.data('business_unit_combination_id', changeData.combination_id);
            SIANExtraInfoUpdateOverwriteIds('{$this->id}');
        });
        
        $('#{$this->id}').data('changeBusinessPartner', function(changeData){
            SIANExtraInfoLoadUpgradeDocuments();            
        });
        
        function SIANExtraInfoLoadUpgradeDocuments() {            
            var person_type = $('#{$this->person_type_input_id}').val();
            var upgrade_document_items = " . json_encode($this->upgrade_document_items) . ";            
            " . (isset($this->model->extraInfo) ? "SIANExtraInfoFillUpgradeDocuments('{$this->upgrade_document_code_input_id}', upgrade_document_items, person_type, '{$this->model->extraInfo->upgrade_document_code}');" : "") . "
        }

        $('#{$this->id}').data('changeEmission', function(changeData){
            " . ($this->model->scenario->include_delivery_date ? "$('#{$this->delivery_date_input_id}').data('changeEmission')(changeData);
            " : "") . "
        });

                ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id = {$this->id}>";

        if ($this->model->scenario->include_panel) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class = 'row'>";
                echo "<div class = 'col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
            }

            $this->beginWidget('booster.widgets.TbPanel', array(
                'title' => $this->title,
                'headerIcon' => 'asterisk',
                'htmlOptions' => array(
                    'class' => $this->model->extraInfo->hasErrors() ? 'us-error' : ''
                )
            ));
        }

        if ($this->model->scenario->include_movement_name) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class = 'col-lg-3 col-md-3 col-sm-6 col-xs-6'>";
            }
            echo $this->form->textFieldRow($this->model->extraInfo, 'movement_name', array(
                'readonly' => isset($this->single_readonly['movement_name']) ? $this->single_readonly['movement_name'] : $this->readonly,
                'required' => $this->model->scenario->require_movement_name,
            ));
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        //Si tiene almacén
        if ($this->model->scenario->include_warehouse) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class = 'col-lg-3 col-md-3 col-sm-6 col-xs-6'>";
            }
            echo $this->form->dropDownListRow($this->model->extraInfo, 'warehouse_id', [], array(
                'id' => $this->warehouse_input_id,
                'class' => 'sian-extra-info-warehouse-id',
                'label' => "{$this->model->extraInfo->getAttributeLabel('warehouse_id')} " . (isset($this->warehouse_label) ? "({$this->warehouse_label})" : ''),
                'empty' => Strings::SELECT_OPTION,
                'readonly' => isset($this->single_readonly['warehouse_id']) ? $this->single_readonly['warehouse_id'] : $this->readonly,
                'required' => $this->model->scenario->require_warehouse,
            ));
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        //Si tiene almacén
        if ($this->model->scenario->include_aux_warehouse) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class = 'col-lg-3 col-md-3 col-sm-6 col-xs-6'>";
            }
            echo $this->form->dropDownListRow($this->model->extraInfo, 'aux_warehouse_id', [], array(
                'id' => $this->aux_warehouse_input_id,
                'class' => 'sian-extra-info-aux-warehouse-id',
                'label' => "{$this->model->extraInfo->getAttributeLabel('aux_warehouse_id')} " . (isset($this->aux_warehouse_label) ? "({$this->aux_warehouse_label})" : ''),
                'empty' => Strings::SELECT_OPTION,
                'readonly' => isset($this->single_readonly['aux_warehouse_id']) ? $this->single_readonly['aux_warehouse_id'] : $this->readonly,
                'required' => $this->model->scenario->require_aux_warehouse,
            ));
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        if ($this->model->scenario->include_cashbox) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-6' id='{$this->cashbox_input_id}_div' >";
            }
            echo $this->form->dropDownListRow($this->model->extraInfo, 'cashbox_id', $this->cashbox_items, array(
                'id' => $this->cashbox_input_id,
                'label' => "{$this->model->extraInfo->getAttributeLabel('cashbox_id')} " . (isset($this->cashbox_label) ? "({$this->cashbox_label})" : ''),
                'empty' => Strings::SELECT_OPTION,
                'readonly' => isset($this->single_readonly['cashbox_id']) ? $this->single_readonly['cashbox_id'] : $this->readonly,
                'required' => $this->model->scenario->require_cashbox,
            ));

            echo $this->form->hiddenField($this->model->extraInfo, 'cashbox_amount', array('id' => $this->cashbox_amount_id));

            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        if ($this->model->scenario->include_delivery_date) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-6' id='{$this->delivery_date_input_id}_div'>";
            }
            $this->widget('application.widgets.USDatePickerFrom', array(
                'id' => $this->delivery_date_input_id,
                'form' => $this->form,
                'model' => $this->model->extraInfo,
                'attribute' => 'delivery_date',
                'options' => [
                    'validateOnBlur' => true,
                ],
                'htmlOptions' => array(
                    'required' => $this->model->scenario->require_delivery_date,
                    'readonly' => isset($this->single_readonly['delivery_date']) ? $this->single_readonly['delivery_date'] : $this->readonly,
                ),
                'fromModel' => $this->model, 'fromAttribute' => 'emission_date',
            ));
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }
        if ($this->model->scenario->include_liquidation_date) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-6' id='{$this->liquidation_date_input_id}_div'>";
            }
            $this->widget('application.widgets.USDatePickerFrom', array(
                'id' => $this->liquidation_date_input_id,
                'form' => $this->form,
                'model' => $this->model->extraInfo,
                'attribute' => 'liquidation_date',
                'options' => [
                    'validateOnBlur' => true,
                ],
                'htmlOptions' => array(
                    'required' => $this->model->scenario->require_liquidation_date,
                    'readonly' => isset($this->single_readonly['liquidation_date']) ? $this->single_readonly['liquidation_date'] : $this->readonly,
                ),
                'fromModel' => $this->model->extraInfo, 'fromAttribute' => 'delivery_date',
            ));

            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        if ($this->model->scenario->include_purchase_order) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class = 'col-lg-3 col-md-3 col-sm-6 col-xs-6'>";
            }
            echo $this->form->textFieldRow($this->model->extraInfo, 'purchase_order', array(
                'maxlength' => 20,
                'placeholder' => $this->model->extraInfo->getAttributeLabel('purchase_order'),
                'readonly' => isset($this->single_readonly['purchase_order']) ? $this->single_readonly['purchase_order'] : $this->readonly,
                'required' => $this->model->scenario->require_purchase_order,
            ));
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        if ($this->model->scenario->include_combination) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class = 'col-lg-3 col-md-3 col-sm-6 col-xs-6'>";
            }
            echo $this->form->hiddenField($this->model->extraInfo, 'validate_combination', array(
                'id' => $this->validate_combination_input_id,
                'readonly' => true,
            ));
            $this->widget('application.widgets.USCostLevel', array(
                'id' => $this->combination_span_id,
                'form' => $this->form,
                'model' => $this->model->extraInfo,
                'value_attribute' => 'combination_id',
                'text_attribute' => 'combination_name',
                'extra_class' => 'sian-extra-info-combination',
                'minimumInputLength' => 2,
                'all' => false,
                'readonly' => $this->readonly,
                'required' => $this->model->scenario->require_combination,
            ));
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        if ($this->model->scenario->include_upgrade_document_code == 1 || $this->model->scenario->include_dispatch_document_code == 1) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class = 'col-lg-3 col-md-3 col-sm-6 col-xs-6'>";
            }
            echo "<div class = 'row'>";
            if ($this->model->scenario->include_upgrade_document_code == 1) {
                echo "<div class = 'col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
                echo $this->form->dropDownListRow($this->model->extraInfo, 'upgrade_document_code', [], array(
                    'id' => $this->upgrade_document_code_input_id,
                    'empty' => Strings::SELECT_OPTION,
                    'readonly' => isset($this->single_readonly['upgrade_document_code']) ? $this->single_readonly['upgrade_document_code'] : $this->readonly,
                    'required' => $this->model->scenario->require_upgrade_document_code == 1,
                ));
                echo "</div>";
            }
            if ($this->model->scenario->include_dispatch_document_code == 1) {
                echo "<div class = 'col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
                echo $this->form->dropDownListRow($this->model->extraInfo, 'dispatch_document_code', [], array(
                    'id' => $this->dispatch_document_code_input_id,
                    'empty' => Strings::SELECT_OPTION,
                    'readonly' => isset($this->single_readonly['dispatch_document_code']) ? $this->single_readonly['dispatch_document_code'] : $this->readonly,
                    'required' => $this->model->scenario->require_dispatch_document_code == 1,
                ));
                echo "</div>";
            }
            echo "</div>";
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        if ($this->model->scenario->include_shipping_type == 1) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class = 'col-lg-3 col-md-3 col-sm-6 col-xs-6'>";
            }
            echo "<div class = 'row'>";
            echo "<div class = 'col-lg-8 col-md-8 col-sm-12 col-xs-12'>";
            echo $this->form->dropDownListRow($this->model->extraInfo, 'shipping_type', [], array(
                'id' => $this->shipping_type_input_id,
                'readonly' => isset($this->single_readonly['shipping_type']) ? $this->single_readonly['shipping_type'] : $this->readonly,
                'required' => $this->model->scenario->require_shipping_type == 1,
                'onchange' => "
                //consoleLog('Se inicia cambio de indicador shipping_type (ExtraInfo)');

                var manualAddressObj = $('#{$this->manual_address_input_id}');
                var shippingTypeObj = $('#{$this->shipping_type_input_id} option:selected');
                var shipping_type = shippingTypeObj.val();
                var enable_manual_address = shippingTypeObj.data('enable_manual_address');
                var require_address = shippingTypeObj.data('require_address');
                var require_company = shippingTypeObj.data('require_company');
                var require_contact = shippingTypeObj.data('require_contact');
                var require_locker = shippingTypeObj.data('require_locker');
                var manual_address = manualAddressObj.prop('checked');
                //Desactivamos check de manual
                manualAddressObj.disabled(enable_manual_address == 0);
                var changeShippingTypeData = {
                shipping_type: shipping_type,
                enable_manual_address: enable_manual_address,
                manual_address: manual_address,
                require_address: require_address,
                require_company: require_company,
                require_contact: require_contact,
                require_locker: require_locker
                };
                $('#{$this->require_address_input_id}').val(require_address);
                $('#{$this->require_company_input_id}').val(require_company);
                $('#{$this->require_contact_input_id}').val(require_contact);
                $('#{$this->require_locker_input_id}').val(require_locker);
                {$this->onChangeShippingType};
                //consoleLog('Seteo el indicador de shipping_type: ' + shipping_type);
                "
            ));
            echo $this->form->hiddenField($this->model->extraInfo, 'require_address', array(
                'id' => $this->require_address_input_id,
                'readonly' => true,
            ));
            echo $this->form->hiddenField($this->model->extraInfo, 'require_company', array(
                'id' => $this->require_company_input_id,
                'readonly' => true,
            ));
            echo $this->form->hiddenField($this->model->extraInfo, 'require_contact', array(
                'id' => $this->require_contact_input_id,
                'readonly' => true,
            ));
            echo $this->form->hiddenField($this->model->extraInfo, 'require_locker', array(
                'id' => $this->require_locker_input_id,
                'readonly' => true,
            ));
            echo "</div>";
            echo "<div class = 'col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
            echo $this->widget('application.widgets.USCheckBox', array(
                'id' => $this->manual_address_input_id,
                'model' => $this->model->extraInfo,
                'attribute' => 'manual_address',
                'htmlOptions' => [
                    'readonly' => isset($this->single_readonly['shipping_type']) ? $this->single_readonly['shipping_type'] : $this->readonly,
                    'disabled' => $this->model->extraInfo->shippingType->enable_manual_address == 0,
                    'onchange' => "
                //consoleLog('Se inicia cambio de indicador manual_address (ExtraInfo)');

                var shippingTypeObj = $('#{$this->shipping_type_input_id} option:selected');
                var shipping_type = shippingTypeObj.val();
                var enable_manual_address = shippingTypeObj.data('enable_manual_address');
                var require_address = shippingTypeObj.data('require_address');
                var require_company = shippingTypeObj.data('require_company');
                var require_contact = shippingTypeObj.data('require_contact');
                var require_locker = shippingTypeObj.data('require_locker');
                var manual_address = this.checked;
                //                    
                var changeShippingTypeData = {
                shipping_type: shipping_type,
                enable_manual_address: enable_manual_address,
                manual_address: manual_address,
                require_address: require_address,
                require_company: require_company,
                require_contact: require_contact,
                require_locker: require_locker
                };
                {$this->onChangeShippingType};
                //consoleLog('Seteo el indicador de manual_address: ' + manual_address);
                "
                ]
                    ), true);
            echo "</div>";
            echo "</div>";
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        if ($this->model->scenario->include_transport_company_id == 1) {

            $this->widget('application.widgets.USAutocomplete', array(
                'id' => $this->transport_company_id,
                'model' => $this->model->extraInfo,
                'attribute' => 'transport_company_id',
                'useOnkeydown' => Yii::app()->controller->getOrganization()->globalVar->keyboard_search == 1 ? true : false,
                'autoChoose' => Yii::app()->controller->getOrganization()->globalVar->keyboard_search == 1 ? false : true,
                'view' => array(
                    'model' => 'DpBusinessPartner',
                    'scenario' => DpBusinessPartner::SCENARIO_TRANSPORT_COMPANY,
                    'attributes' => array(
                        array('name' => 'identification_number', 'width' => 20, 'types' => array('aux')),
                        array('name' => 'identification_name', 'width' => 20),
                        array('name' => 'person_name', 'width' => 60, 'types' => array('text')),
                        array('name' => 'person_id', 'width' => 0, 'types' => array('id', 'value'), 'hidden' => true),
                    )
                ),
                'maintenance' => array(
                    'module' => 'administration',
                    'controller' => 'person',
                    'buttons' => [
                        'create' => ['data' => ['is_transport_company' => 1]],
                        'update' => [],
                        'delete' => [],
                        'preview' => []
                    ]
                ),
                'onreset' => "",
                'readonly' => isset($this->single_readonly['transport_company_id']) ? $this->single_readonly['transport_company_id'] : $this->readonly,
                'required' => $this->model->scenario->require_transport_company_id == 1,
            ));
        }

        if ($this->model->scenario->include_kardex_rlock == 1) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class = 'col-lg-3 col-md-3 col-sm-6 col-xs-6'>";
            }
            $this->widget('application.widgets.USSwitch', array(
                'model' => $this->model->extraInfo,
                'attribute' => 'kardex_rlock',
                'readonly' => isset($this->single_readonly['kardex_rlock']) ? $this->single_readonly['kardex_rlock'] : $this->readonly,
                'required' => $this->model->scenario->require_kardex_rlock == 1,
                'switchChange' => "
                var checked = this.checked;
                {$this->onChangeKardexRLock};
                ",
            ));
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        if ($this->model->scenario->include_kardex_clock == 1) {
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class = 'col-lg-3 col-md-3 col-sm-6 col-xs-6'>";
            }
            $this->widget('application.widgets.USSwitch', array(
                'id' => $this->kardex_clock_input_id,
                'model' => $this->model->extraInfo,
                'attribute' => 'kardex_clock',
                'readonly' => $this->readonly,
                'readonly' => isset($this->single_readonly['kardex_clock']) ? $this->single_readonly['kardex_clock'] : $this->readonly,
                'required' => $this->model->scenario->require_kardex_clock == 1,
                'switchChange' => "
                var checked = this.checked;
                {$this->onChangeKardexCLock};
                ",
            ));
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        if ($this->model->scenario->include_contact) {

            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "<div class = 'col-lg-3 col-md-3 col-sm-6 col-xs-6'>";
            } else {
                echo "<hr>";
            }
            echo "<div class = 'row'>";
            echo "<div class = 'col-lg-3 col-md-3 col-sm-6 col-xs-6'>";
            echo $this->form->textFieldRow($this->model->extraInfo, 'contact_identification_number', array(
                'maxlength' => 15,
                'placeholder' => $this->model->extraInfo->getAttributeLabel('contact_identification_number'),
                'readonly' => isset($this->single_readonly['contact_identification_number']) ? $this->single_readonly['contact_identification_number'] : $this->readonly,
            ));
            echo "</div>";
            echo "<div class = 'col-lg-9 col-md-9 col-sm-6 col-xs-6'>";
            echo $this->form->textFieldRow($this->model->extraInfo, 'contact_name', array(
                'maxlength' => 100,
                'placeholder' => $this->model->extraInfo->getAttributeLabel('contact_name'),
                'readonly' => isset($this->single_readonly['contact_name']) ? $this->single_readonly['contact_name'] : $this->readonly,
            ));
            echo "</div>";
            echo "</div>";
            echo $this->form->textFieldRow($this->model->extraInfo, 'contact_phone', array(
                'maxlength' => 100,
                'placeholder' => $this->model->extraInfo->getAttributeLabel('contact_phone'),
                'readonly' => isset($this->single_readonly['contact_phone']) ? $this->single_readonly['contact_phone'] : $this->readonly,
            ));
            echo $this->form->textFieldRow($this->model->extraInfo, 'contact_email', array(
                'maxlength' => 100,
                'placeholder' => $this->model->extraInfo->getAttributeLabel('contact_email'),
                'readonly' => isset($this->single_readonly['contact_email']) ? $this->single_readonly['contact_email'] : $this->readonly,
            ));
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
            }
        }

        if ($this->model->scenario->include_panel) {
            $this->endWidget();
            if ($this->orientation == self::ORIENTATION_HORIZONTAL) {
                echo "</div>";
                echo "</div>";
            }
        }
        echo "</div>   

                                                                                                                     ";
    }

}
