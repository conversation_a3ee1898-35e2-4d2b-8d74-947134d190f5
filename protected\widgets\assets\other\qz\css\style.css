.btn-default {
    color: #333;
    background-color: #FFF;
    border-color: #CCC;
}

.btn-primary {
    color: #FFFFFF;
    background-color: #44AB53;
    border-color: #3D994A;
}

.btn-success {
    color: #FFF;
    background-color: #2A6831;
    border-color: #235628;
}

.btn-warning {
    color: #FFF;
    background-color: #9F7435;
    border-color: #8C662F;
}

.btn-danger {
    color: #FFF;
    background-color: #9F3535;
    border-color: #8C2F2F;
}

.btn-info {
    color: #FFF;
    background-color: #246D6D;
    border-color: #1E5A5A;
}

.btn-default:hover {
    background-color: #E6E6E6;
    border-color: #B3B3B3;
}

.btn-primary:hover {
    background-color: #358741;
    border-color: #2E7438;
}

.btn-success:hover {
    background-color: #1B4420;
    border-color: #143217;
}

.btn-warning:hover {
    background-color: #795828;
    border-color: #664A22;
}

.btn-danger:hover {
    background-color: #792828;
    border-color: #662222;
}

.btn-info:hover {
    background-color: #174747;
    border-color: #113333;
}

.btn-default:active {
    background-color: #E6E6E6;
    border-color: #B3B3B3;
}

.btn-primary:active {
    background-color: #358741;
    border-color: #2E7438;
}

.btn-success:active {
    background-color: #1B4420;
    border-color: #143217;
}

.btn-warning:active {
    background-color: #795828;
    border-color: #664A22;
}

.btn-danger:active {
    background-color: #792828;
    border-color: #662222;
}

.btn-info:active {
    background-color: #174747;
    border-color: #113333;
}

.btn-default.disabled {
    background-color: #FFFFFF;
    border-color: #CFCFCF;
}

.btn-primary.disabled {
    background-color: #45AF55;
    border-color: #3E9C4C;
}

.btn-success.disabled {
    background-color: #2B6C33;
    border-color: #24592A;
}

.btn-warning.disabled {
    background-color: #A37736;
    border-color: #906930;
}

.btn-danger.disabled {
    background-color: #A33636;
    border-color: #903030;
}

.btn-info.disabled {
    background-color: #257171;
    border-color: #1F5E5E;
}

a {
    color: #44AB53;
}

a:hover,
a:focus {
    color: #2E7438;
}

.text-primary {
    color: #44AB53;
}

.text-danger {
    color: #9F3535;
}

.form-control .has-error {
    border-color: #EED3D7;
}

.form-control .has-success {
    border-color: #D6E9C6;
}

.navbar-default {
    background-color: #44AB53;
}

.navbar-default .navbar-nav > li > a,
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-header .navbar-brand {
    color: #FFF;
}

@media (max-width: 768px) {
    .navbar-default .navbar-nav .open .dropdown-menu > li > a {
        color: #FFF;
    }
}

.navbar-default .navbar-nav > li > a:hover {
    background-color: #3D994A;
}

.navbar-default .navbar-toggle > .icon-bar {
    background-color: #FFF;
}

.navbar-default .navbar-toggle:focus > .icon-bar {
    background-color: #555;
    color: #2E7438;
}

.navbar-default .navbar-toggle:hover > .icon-bar {
    background-color: #FFF;
}

.navbar-default .navbar-toggle:hover {
    background-color: #3D994A;
}

.panel.panel-primary {
    border-color: #3D994A;
}

.panel.panel-primary .panel-heading {
    border-color: #3D994A;
    background-color: #3D994A;
}

.alert.alert-warning a {
    color: #68522C;
}

.alert.alert-danger a {
    color: #662222;
}

html {
    position: relative;
    min-height: 100%;
}

body {
    padding-bottom: 80px;
}

table {
    font-size: inherit;
}

.versiontable > tbody > tr > th {
    text-align: right;
}

.versiontable > thead > tr > th {
    vertical-align: top;
}

.homesection + .homesection {
    border-top: solid 1px #EEE;
}

.homesection {
    text-align: center;
    margin: 40px 0;
    padding: 40px 0;
}

img.jumbo {
    margin-top: -40px;
    margin-left: -60px;
}

h1.jumbo {
    margin-top: 0;
}

footer {
    text-align: center;
    height: 80px;
    position: absolute;
    bottom: 0;
    width: 100%;
}

@media (min-width: 768px) {
    h1.jumbo,
    h3.jumbo {
        text-align: right;
    }
}

@media (max-width: 767px) {
    h1.jumbo,
    h3.jumbo {
        text-align: center;
    }
}

@media (min-width: 1200px) {
    h1.jumbo {
        margin-top: 40px;
    }
}

@media (min-width: 768px) {
    h3.jumbo {
        margin-top: -6px;
    }
}

.vertical-align {
    display: flex;
    align-items: center;
}

.hide_ {
    display: none;
}

.dropdown-submenu {
    position: relative;
}

.dropdown-submenu > .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -6px;
    margin-left: 10px;
    -webkit-border-radius: 0 6px 6px 6px;
    -moz-border-radius: 0 6px 6px 6px;
    border-radius: 0 6px 6px 6px;
}

@media (min-width: 768px) {
    .dropdown-submenu:hover > .dropdown-menu {
        display: block;
    }

    .dropdown-submenu > .dropdown-menu {
        margin-left: -1px;
    }
}

.dropdown-submenu > a:after {
    display: block;
    content: " ";
    float: right;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-left-color: #CCCCCC;
    margin-top: 5px;
    margin-right: -10px;
}

.dropdown-submenu:hover > a:after {
    border-left-color: #FFFFFF;
}

.dropdown-submenu.pull-left {
    float: none;
}

.dropdown-submenu.pull-left > .dropdown-menu {
    left: -100%;
    margin-left: 10px;
    -webkit-border-radius: 6px 0 6px 6px;
    -moz-border-radius: 6px 0 6px 6px;
    border-radius: 6px 0 6px 6px;
}

.tip {
    border-bottom: 1px dashed;
}

code {
    color: #31708F;
    background-color: #D9EDF7;
}
