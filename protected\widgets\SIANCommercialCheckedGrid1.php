<?php

class SIANCommercialCheckedGrid1 extends SIANCommercialCheckedGrid {

    public $net_input_id;
    public $total_input_id;
    public $max_iterations = 10;

    public function init() {

        $this->grid_number = 1;
        parent::init();

        //GRID ID
        $this->net_input_id = Yii::app()->controller->getServerId();
        $this->total_input_id = Yii::app()->controller->getServerId();
        //CHECKED ITEMS
        foreach ($this->model->tempItems as $item) {

            if (USString::isBlank($item->itemObj->parent_item_id)) {
                throw new Exception('No está seteado el campo parent_item_id para uno o más ítems que deben ir en la grilla.');
            }

            $this->checked_items[$item->itemObj->parent_item_id] = $item;
        }
        //MODEL
        $price_field = $this->model->movement->currency == Currency::PEN ? 'price_pen' : 'price_usd';
        //ITEMS
        $product_ids = [];
        foreach ($this->items as $spItem) {
            if (!USString::isBlank($spItem->product_id)) {
                $product_ids[] = $spItem->product_id;
            }
        }
        $this->presentationItems = SpGetProductPresentations::getAssociative($this->presentationMode, $product_ids, $this->controller->getOrganization()->globalVar->display_currency);

        foreach ($this->items as $spItem) {

            //BALANCES
            switch ($spItem->currency) {
                case Currency::PEN:
                    $spItem->max_price_pen = $spItem->max_price;
                    $spItem->max_price_usd = round($spItem->max_price / $this->model->movement->exchange_rate, $this->ifixed);
                    break;
                case Currency::USD:
                    $spItem->max_price_pen = round($spItem->max_price * $this->model->movement->exchange_rate, $this->ifixed);
                    $spItem->max_price_usd = $spItem->max_price;
                    break;
                default:
                    throw new Exception('Moneda inválida!', USREST::CODE_INTERNAL_SERVER_ERROR);
            }

            if (isset($this->checked_items[$spItem->parent_item_id])) {
                $spItem->checked = true;
                //Monto
                $spItem->price = number_format($this->checked_items[$spItem->parent_item_id]->{$price_field}, $this->ifixed, '.', '');
                if ($this->checked_items[$spItem->parent_item_id]->hasErrors($price_field)) {
                    $spItem->addError('price', $this->checked_items[$spItem->parent_item_id]->getError($price_field));
                }
            } else {
                $spItem->checked = false;
                //Monto
                switch ($this->model->movement->currency) {
                    case Currency::PEN:
                        $spItem->price = $spItem->max_price_pen;
                        break;
                    case Currency::USD:
                        $spItem->price = $spItem->max_price_usd;
                        break;
                    default:
                        throw new Exception('Moneda inválida!', USREST::CODE_INTERNAL_SERVER_ERROR);
                }
            }
        }

        //DATA PROVIDER
        $this->dataProvider = new USArrayDataProvider($this->items, array(
            'keyField' => array('product_id'), // PRIMARY KEY
            'sort' => false,
        ));
        $this->dataProvider->pagination = false;
        //ACCESS
        $this->preview_access = $this->controller->checkRoute('/logistic/product/preview');

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-commercial-checked-grid-1.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        var grid = $('#{$this->id}');
        grid.data('include_igv', $('#{$this->include_igv_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('as_bill', $('#{$this->as_bill_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('force_igv', $('#{$this->force_igv_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('allow_duplicate', $('#{$this->allow_duplicate_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('is_commercial_route', " . CJSON::encode($this->model->movement->scenario->isCommercialRoute()) . ");
        grid.data('scope', '{$this->model->movement->scope}');
        grid.data('is_buy', " . CJSON::encode($this->model->isBuy()) . ");
        grid.data('ifixed', {$this->ifixed});
        grid.data('tfixed', {$this->tfixed});
        grid.data('istep', {$this->istep});
        grid.data('tstep', {$this->tstep});
        grid.data('max_iterations', {$this->max_iterations});
        grid.data('igv_global', " . $this->controller->getOrganization()->globalVar->igv . ");
        grid.data('perception_global', " . $this->controller->getOrganization()->globalVar->perception . ");
        grid.data('retention4', {$this->controller->getOrganization()->globalVar->retention4});
        grid.data('currency', '{$this->model->movement->currency}');
        grid.data('round_mode', {$this->model->round_mode});
        grid.data('direction', '{$this->model->movement->scenario->direction}');
        grid.data('warehouse_input_id', '{$this->warehouse_input_id}');
        grid.data('independent_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INDEPENDENT . "');
        grid.data('inherit_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INHERIT . "');
        grid.data('summary_id', '{$this->summary_id}');
        grid.data('modifiers', " . CJSON::encode(Document::getModifiers()) . ");

        $(document).ready(function() {                  
            
            var gridTotals = SIANCommercialCheckedGrid1Calculate('{$this->id}', 0);
            SIANCommercialCheckedGrid1CC('{$this->id}', gridTotals);    

            //CURRENCY SYMBOL
            $('span.currency-symbol').text('" . Currency::getSymbol($this->model->movement->currency) . "');

            var divObj = $('#{$this->id}');
        });
        
        //SI CAMBIA LA MONEDA
        $('#{$this->id}').data('changeCurrency', function(currency){

            //GET
            var grid = $('#{$this->id}');
            var exchange_rate = grid.data('exchange_rate');
            var ifixed = grid.data('ifixed');
            var tfixed = grid.data('tfixed');

            grid.find('tr.sian-commercial-checked-grid-1-item').each(function(index) {

                var item = $(this);

                var max_price_pen = item.find('input.sian-commercial-checked-grid-1-item-max-price-pen').floatVal({$this->ifixed});
                var max_price_usd = item.find('input.sian-commercial-checked-grid-1-item-max-price-usd').floatVal({$this->ifixed});

                //Actualizamos montos
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-commercial-checked-grid-1-item-price').toPen(exchange_rate, ifixed, {max: max_price_pen});
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-commercial-checked-grid-1-item-price').toUsd(exchange_rate, ifixed, {max: max_price_usd});
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida', 'error'));
                    break;
                }
            });

            //SET
            grid.data('currency', currency);
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            var gridTotals = SIANCommercialCheckedGrid1Calculate('{$this->id}', 0);
            SIANCommercialCheckedGrid1CC('{$this->id}', gridTotals);    
        });
        
        $('#{$this->id}').data('changeOperation', function(changeData){
            
            var divObj = $('#{$this->id}');

            //GUARDAMOS INFO DE OPERACIÓN
            divObj.data('operationChangeData', changeData);
            
            var gridTotals = SIANCommercialCheckedGrid1Calculate('{$this->id}');            
            SIANCommercialCheckedGrid1CC('{$this->id}', gridTotals);    
        });
        
        $('#{$this->id}').data('changeWarehouse', function(changeData){
            var grid = $('#{$this->id}');
            //Seteamos
            $('#{$this->warehouse_input_id}').val(changeData.warehouse_id);
            //
            if(changeData.warehouse_id !== 'undefined' && changeData.warehouse_id.length > 0)
            {
                var gridTotals = SIANCommercialCheckedGrid1Calculate('{$this->id}');            
                SIANCommercialCheckedGrid1CC('{$this->id}', gridTotals);  
            }            
        });
        
        $('#{$this->id}').data('changeCCDependence', function(operationChangeData){
            {$this->onChangeCCDependence};
        });

        $('#{$this->id}').data('changeDocument', function(changeData){
            
            var divObj = $('#{$this->id}');
            var scope = divObj.data('scope');
            var is_commercial_route = divObj.data('is_commercial_route');
            var modifiers = divObj.data('modifiers');
            
            " . ($this->model->isBuy() ? "
                
            //Desactivamos y seteamos data de sunat
            $('#{$this->retention_checkbox_id}').data('sunat_code', changeData.sunat_code);
            //Comparamos
            if(scope == '" . Scenario::SCOPE_NOT_DOMICILED . "' || changeData.sunat_code == '" . Document::HONORARIOS . "')
            {   
                //Quitamos readonly
                $('#{$this->retention_checkbox_id}')
                    .attr('readonly', false)
                    .change(); 
                   
                $.notify('Se activó la casilla \'Retención\'.', {
                    className: 'info',
                    showDuration: 30,
                    hideDuration: 50,
                    autoHideDelay: 1000,
                }); 
            } 
            else 
            {
                $('#{$this->retention_checkbox_id}')
                    .attr('readonly', true)
                    .prop('checked', false)
                    .change();
            }
            " : "") . "
            
            var as_bill_readonly = is_commercial_route || jQuery.inArray(changeData.sunat_code, ['" . Document::TICKET . "']) == -1;

            $('#{$this->as_bill_checkbox_id}').attr('readonly', as_bill_readonly);

            if(as_bill_readonly){

                if(!jQuery.inArray(changeData.sunat_code, modifiers))
                {
                    $('#{$this->as_bill_checkbox_id}').prop('checked', is_commercial_route || jQuery.inArray(changeData.sunat_code, ['" . Document::BOLETA . "', '" . Document::BOLETO . "', '" . Document::HONORARIOS . "']) == -1).change();            
                }

            } else {

                $.notify('Se activó la casilla \'Crédito fiscal\'.', {
                    className: 'info',
                    showDuration: 30,
                    hideDuration: 50,
                    autoHideDelay: 1000,
                }); 
            }
        }); 

        $('#{$this->id}').data('changeExchange', function(exchange_rate){

            var grid = $('#{$this->id}');
                
            //Seteamos TC
            grid.data('exchange_rate', exchange_rate);
                
            //Obtenemos
            var currency = grid.data('currency');
            var ifixed = grid.data('ifixed');
            var tfixed = grid.data('tfixed');

            grid.find('tr.sian-commercial-checked-grid-1-item').each(function(index) {
                var item = $(this);
                var item_currency = item.find('input.sian-commercial-checked-grid-1-item-currency').val();
                var max_price_pen = item.find('input.sian-commercial-checked-grid-1-item-max-price-pen').floatVal({$this->ifixed});
                var max_price_usd = item.find('input.sian-commercial-checked-grid-1-item-max-price-usd').floatVal({$this->ifixed});

                //Cambiamos los balances
                switch(item_currency)
                {
                    case '" . Currency::PEN . "':
                        max_price_usd = USMath.divide(max_price_pen, exchange_rate);
                    break;
                    case '" . Currency::USD . "':
                        max_price_pen = USMath.multiply(max_price_usd, exchange_rate);
                     break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida', 'error'));
                    break;
                }

                //CAMBIAMOS EL MAX
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-commercial-checked-grid-1-item-price').floatAttr('max', {$this->ifixed}, max_price_pen);
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-commercial-checked-grid-1-item-price').floatAttr('max', {$this->ifixed}, max_price_usd);
                     break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida', 'error'));
                    break;
                }
                    
                //Seteamos balances
                item.find('input.sian-commercial-checked-grid-1-item-max-price-pen').floatVal({$this->ifixed}, max_price_pen);
                item.find('input.sian-commercial-checked-grid-1-item-max-price-usd').floatVal({$this->ifixed}, max_price_usd);
            });

            //Actualizamos montos
            var gridTotals = SIANCommercialCheckedGrid1Calculate('{$this->id}', 0);
            SIANCommercialCheckedGrid1CC('{$this->id}', gridTotals);    
        });

        $('body').on('change', '#{$this->retention_checkbox_id}', function(e) {
            
            var divObj = $('#{$this->id}');
            var retention4 = divObj.data('retention4');
            
            var elementObj = $(this);            
            var sunat_code = elementObj.data('sunat_code');
            var retention = elementObj.prop('checked') ? 1 : 0;

            if(retention != elementObj.data('retention'))
            {                    
                $('#{$this->retention_percent_field_id}').attr('readonly', retention == 0 || sunat_code === '" . Document::HONORARIOS . "');
                //
                if(retention == 0)
                {
                    $('#{$this->retention_percent_field_id}').val(0);
                    $('#{$this->retention_percent_field_id}').closest('div.row').addClass('hide');
                }
                else
                {
                    $('#{$this->retention_percent_field_id}').closest('div.row').removeClass('hide');
                        
                    //Si es recibo por honorarios se pone 8%
                    if(sunat_code == '" . Document::HONORARIOS . "') {
                        $('#{$this->retention_percent_field_id}').val(retention4 * 100);
                    }
                }
                //Calculamos retención
                SIANCommercialCheckedGrid1CalculateRetention('{$this->id}');
                
                $(this).data('last', retention);
            }
        });

        $('body').on('focus', '#{$this->net_input_id}', function(e) {
            var element = $(this);
            element.floatData('pnet', {$this->tfixed}, element.floatVal({$this->tfixed}));
        });
        
        $('body').on('focus', '#{$this->total_input_id}', function(e) {
            var element = $(this);
            element.floatData('ptotal', {$this->tfixed}, element.floatVal({$this->tfixed}));
        });

        $('body').on('change', '#{$this->net_input_id}', function(e) {
            var element = $(this);
            var field = 'net';
            var pvalue = element.floatData('p' + field, {$this->tfixed});
            var current = element.floatVal({$this->tfixed});

            if(current === 0)
            {
                bootbox.alert(us_message('No puede poner un monto cero (0)', 'warning'));

                element.floatVal({$this->ifixed}, pvalue);
                e.preventDefault();
                return false;
            }

            var multiplicator = USMath.divide(current, pvalue === 0 ? 1 : pvalue);
            var obj = SIANCommercialCheckedGrid1Adjust('{$this->id}', current, multiplicator, field, [], 0);
            SIANCommercialCheckedGrid1CC('{$this->id}', obj);
            element.floatData('p' + field, {$this->tfixed}, obj[field]);
        });
        
        $('body').on('change', '#{$this->total_input_id}', function(e) {
            var element = $(this);
            var field = 'total';
            var pvalue = element.floatData('p' + field, {$this->tfixed});
            var current = element.floatVal({$this->tfixed});
                
            if(current === 0)
            {
                bootbox.alert(us_message('No puede poner un monto cero (0)', 'warning'));
                element.floatVal({$this->ifixed}, pvalue);
                e.preventDefault();
                return false;
            }
            
            var multiplicator = USMath.divide(current, pvalue === 0 ? 1 : pvalue);
            var obj = SIANCommercialCheckedGrid1Adjust('{$this->id}', current, multiplicator, field, [], 0);
            SIANCommercialCheckedGrid1CC('{$this->id}', obj);
            element.floatData('p' + field, {$this->tfixed}, obj[field]);

        });

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Mercaderías',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => 'sian-commercial-checked-grid-1 ' . ($this->model->hasErrors('tempItems') ? 'us-error' : '')
            )
        ));

        $this->_renderItems();

        echo "<div class='row'>";

        echo "<div class='col-lg-8 col-md-8 col-sm-8 col-xs-12'>";
        $this->_renderCheckboxes();
        $this->_renderObservations();
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        if ($this->model->isBuy()) {
            $this->_renderRetention();
        }
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        $this->_renderSummary();
        echo "</div>";

        echo "</div>";

        echo $this->form->hiddenField($this->model, 'warehouse_id', [
            'id' => $this->warehouse_input_id,
            'class' => 'sian-commercial-checked-grid-1-warehouse-id'
        ]);

        $this->endWidget();
    }

    private function _renderItems() {

        $b_preview_access = $this->preview_access;
        $o_form = $this->form;
        $s_form_currency = $this->model->movement->currency;
        $a_igv_affections = $this->igv_affections;
        $a_igv_list = $this->igv_list;

        $counter = 0;
        $columns = [];

        array_push($columns, array(
            'header' => CHtml::checkBox("All", $this->getGlobalCheckBoxState(), array(
                'class' => 'sian-credit-note1-check',
                'onchange' => 'SIANCommercialCheckedGrid1GridCheck($(this));',
            )),
            'headerHtmlOptions' => array('style' => 'width:2%; text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) {
                return CHtml::checkBox("Checked[{$row->parent_item_id}]", $row->checked, array(
                    'class' => 'sian-credit-note1-item-check',
                    'onchange' => 'SIANCommercialCheckedGrid1RowCheck($(this));',
                ));
            },
        ));

        array_push($columns, array(
            'header' => '#',
            'headerHtmlOptions' => array('style' => 'width:3%;'),
            'type' => 'raw',
            'value' => function ($row) use (&$counter) {
                return "<span class='sian-commercial-checked-grid-1-item-index'>" . ( ++$counter) . "</span>";
            },
        ));

        array_push($columns, array(
            'header' => 'ID',
            'headerHtmlOptions' => array('style' => 'width:5%;'),
            'type' => 'raw',
            'value' => function ($row) {
                return $this->renderProductCell($row);
            },
        ));

        array_push($columns, array(
            'header' => 'Tipo',
            'headerHtmlOptions' => array('style' => 'width:4%;'),
            'type' => 'raw',
            'value' => function ($row) use ($o_form) {
                return $o_form->textFieldRow($row, 'product_type', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][product_type]",
                    'class' => 'sian-commercial-checked-grid-1-item-product-type',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:center'
                ));
            },
        ));

        array_push($columns, array(
            'header' => 'Producto',
            'headerHtmlOptions' => array('style' => 'width:26%;'),
            'type' => 'raw',
            'value' => function ($row) use ($o_form) {
                return $o_form->textFieldRow($row, 'product_name', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][product_name]",
                    'class' => 'sian-commercial-checked-grid-1-item-product-name sian-force-tooltip',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'title' => $row->product_name
                ));
            },
        ));

        array_push($columns, array(
            'header' => 'Afec. IGV',
            'headerHtmlOptions' => array('style' => 'width:8%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($a_igv_affections) {
                $s_name = "Item[{$row->parent_item_id}][igv_affection]";
                $s_class = "sian-commercial-checked-grid-1-item-igv-affection";
                $b_readonly = true;
                return $this->renderIGVAffectionSelect($row, $a_igv_affections, $s_name, $s_class, $b_readonly, !$row->checked);
            },
        ));

        array_push($columns, array(
            'header' => '%IGV',
            'headerHtmlOptions' => array('style' => 'width:5%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($a_igv_list) {
                $s_name = "Item[{$row->parent_item_id}][igv_percentage]";
                $s_class = "sian-commercial-checked-grid-1-item-igv-percentage";
                $b_igv_readonly = 1;
                $s_onchange = "";
                return $this->renderIGVListSelect($row, $a_igv_list, $s_name, $s_class, $b_igv_readonly, !$row->checked, $s_onchange);
            },
        ));

        array_push($columns, array(
            'header' => 'Perc?',
            'headerHtmlOptions' => array('style' => 'width:3%;'),
            'type' => 'raw',
            'value' => function ($row) {
                return $this->controller->widget('application.widgets.USCheckBox', array(
                    'name' => "Item[{$row->parent_item_id}][perception_affected]",
                    'value' => $row->perception_affected,
                    'htmlOptions' => array(
                        'class' => 'sian-commercial-checked-grid-1-item-perception-affected',
                        'readonly' => true,
                        'disabled' => !$row->checked
                    )), true);
            },
        ));

        array_push($columns, array(
            'header' => 'Cantidad',
            'headerHtmlOptions' => array('style' => 'width:7%;'),
            'type' => 'raw',
            'value' => function ($row) use ($o_form) {
                return $o_form->textFieldRow($row, 'pres_quantity', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][pres_quantity]",
                    'class' => "sian-commercial-checked-grid-1-item-pres-quantity " . ($row->allow_decimals == 1 ? "us-double2" : "us-double0"),
                    'readonly' => true, //En este tipo de nota de crédito no puede editarse la cantidad
                    'disabled' => !$row->checked,
                    'style' => 'text-align:center',
                ));
            },
        ));

        array_push($columns, array(
            'header' => 'Pres.',
            'headerHtmlOptions' => array('style' => 'width:7%'),
            'type' => 'raw',
            'value' => function ($row) {
                $s_name = "Item[{$row->parent_item_id}][equivalence]";
                $s_class = "sian-commercial-checked-grid-1-item-equivalence";
                return $this->renderPresentationCell($row, $s_name, $s_class, true, !$row->checked);
            },
        ));

        array_push($columns, array(
            'header' => 'Hidden',
            'headerHtmlOptions' => array('style' => 'display:none'),
            'htmlOptions' => array('style' => 'display:none'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) {

                $html = CHtml::hiddenField("Item[{$row->parent_item_id}][item_type_id]", $row->item_type_id, array('class' => "sian-commercial-checked-grid-1-item-item-type-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][allow_decimals]", $row->allow_decimals, array('class' => "sian-commercial-checked-grid-1-item-allow-decimals",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][skip_cost_validation]", $row->skip_cost_validation, array('class' => "sian-commercial-checked-grid-1-item-skip-cost-validation",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][currency]", $row->currency, array(
                            'class' => "sian-commercial-checked-grid-1-item-currency",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_price_pen]", $row->max_price_pen, array(
                            'class' => "sian-commercial-checked-grid-1-item-max-price-pen",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_price_usd]", $row->max_price_usd, array(
                            'class' => "sian-commercial-checked-grid-1-item-max-price-usd",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][parent_item_id]", $row->parent_item_id, array(
                            'class' => "sian-commercial-checked-grid-1-item-parent-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][total_item_id]", $row->total_item_id, array(
                            'class' => "sian-commercial-checked-grid-1-item-total-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][item_type_id]", $row->item_type_id, array('class' => "sian-commercial-checked-grid-1-item-item-type-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                return $html;
            },
        ));

        array_push($columns, array(
            'header' => "Precio <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:8%;'),
            'type' => 'raw',
            'value' => function ($row) use ($o_form, $s_form_currency) {

                return $o_form->numberFieldRow($row, 'price', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][price]",
                    'class' => "sian-commercial-checked-grid-1-item-price us-double{$this->ifixed}",
                    'readonly' => ($row->igv_affection == CommercialMovementProduct::IGV_AFFECTION_FREE || $row->igv_affection == CommercialMovementProduct::IGV_AFFECTION_GIFT),
                    'disabled' => !$row->checked,
                    'min' => 0,
                    'max' => $s_form_currency ? $row->max_price_pen : $row->max_price_usd,
                    'step' => $this->istep,
                    'style' => 'text-align:right',
                    'onfocus' => 'this.oldvalue = this.value;',
                    'onchange' => "
                                if(this.value.length > 0){
                                    var gridTotals = SIANCommercialCheckedGrid1Calculate('{$this->id}', 0);
                                    SIANCommercialCheckedGrid1CC('{$this->id}', gridTotals);    
                                    this.oldvalue = this.value;
                                }
                                else{
                                    this.value = this.oldvalue;
                                }
                            "
                ));
            },
            'footer' => '<b>TOTALES:</b>',
        ));

        array_push($columns, array(
            'header' => "Subtotal <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:8%;'),
            'type' => 'raw',
            'value' => function ($row) {

                return SIANForm::textFieldNonActive(false, "Item[{$row->parent_item_id}][net]", 0, array(
                    'class' => "sian-commercial-checked-grid-1-item-net",
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right',
                    'readonly' => true,
                ));
            },
            'footer' => SIANForm::numberFieldNonActive(false, null, 0, array(
                'id' => $this->net_input_id,
                'class' => "sian-commercial-checked-grid-1-net us-double{$this->tfixed}",
                'min' => 0,
                'step' => $this->tstep,
                'readonly' => false,
                'style' => 'text-align:right',
                'hint' => 'Escriba el subtotal directamente aquí',
            ))
        ));

        array_push($columns, array(
            'header' => "IGV <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:8%;'),
            'type' => 'raw',
            'value' => function ($row) {

                return SIANForm::textFieldNonActive(false, "Item[{$row->parent_item_id}][igv]", 0, array(
                    'class' => "sian-commercial-checked-grid-1-item-igv",
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right',
                    'readonly' => true,
                ));
            },
            'footer' => SIANForm::textFieldNonActive(false, null, 0, array(
                'class' => 'sian-commercial-checked-grid-1-igv',
                'readonly' => true,
                'style' => 'text-align:right'
            ))
        ));

        array_push($columns, array(
            'header' => "Total <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => 'width:8%;'),
            'type' => 'raw',
            'value' => function ($row) {

                return SIANForm::textFieldNonActive(false, "Item[{$row->parent_item_id}][total]", 0, array(
                    'class' => "sian-commercial-checked-grid-1-item-total",
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right',
                    'readonly' => true,
                ));
            },
            'footer' => SIANForm::numberFieldNonActive(false, null, 0, array(
                'id' => $this->total_input_id,
                'class' => "sian-commercial-checked-grid-1-total us-double{$this->tfixed}",
                'min' => 0,
                'step' => $this->tstep,
                'readonly' => false,
                'style' => 'text-align:right',
                'hint' => 'Escriba el total directamente aquí',
            ))
        ));

        array_push($columns, array(
            'header' => 'Opc.',
            'headerHtmlOptions' => array('style' => 'width:3%; text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($b_preview_access) {
                return $this->_renderRowButtons($row, $b_preview_access);
            },
        ));

        $gridParams = array(
            'id' => $this->controller->getServerId(),
            'type' => 'condensed',
            'dataProvider' => $this->dataProvider,
            'enableSorting' => false,
            'selectableRows' => 0,
            'columns' => $columns,
            'template' => '{items}',
            'rowCssClassExpression' => '"sian-commercial-checked-grid-1-item " . ($data->checked == 1 ? "success" : "danger")',
        );

        $this->widget('application.widgets.USGridView', $gridParams);
    }

    private function _renderCheckboxes() {

        echo "<div class='row'>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->include_igv_checkbox_id,
            'model' => $this->model,
            'attribute' => 'include_igv',
            'htmlOptions' => array(
                'readonly' => true, //Este estado debería ser e mismo que la factura
            )
                ), true);
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->as_bill_checkbox_id,
            'model' => $this->model,
            'attribute' => 'as_bill',
            'htmlOptions' => array(
                'onchange' => "
                    if(window.documents_loaded)
                    {
                        //consoleLog('Se entró al evento onchange de as_bill pues ya terminaron de cargar los documentos');

                        var current = $(this).prop('checked') ? 1 : 0;
                        $('#{$this->id}').data('as_bill', current);
                        var gridTotals = SIANCommercialCheckedGrid1Calculate('{$this->id}', 0);
                        SIANCommercialCheckedGrid1CC('{$this->id}', gridTotals);    
                    }
                    else
                    {
                        //consoleLog('Se intentó cambiar as_bill pero aún no terminaban de cargar los documentos');
                    }
                ",
            )
                ), true);
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->force_igv_checkbox_id,
            'model' => $this->model,
            'attribute' => 'force_igv',
            'hint' => 'Para indicar si se aumenta el IGV al monto inafecto/exonerado',
            'htmlOptions' => array(
                'readonly' => true
            )
                ), true);
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->allow_duplicate_checkbox_id,
            'model' => $this->model,
            'attribute' => 'allow_duplicate',
            'htmlOptions' => array(
                'readonly' => true
            )
                ), true);
        echo "</div>";

        if ($this->model->isBuy()) {
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo $this->widget('application.widgets.USCheckBox', array(
                'id' => $this->retention_checkbox_id,
                'model' => $this->model,
                'attribute' => 'retention',
                'htmlOptions' => array(
                    'readonly' => false,
                ),
                    ), true);
            echo "</div>";
        }

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->form->dropDownListRow($this->model, 'round_mode', [
            PHP_ROUND_HALF_UP => 'Hacia arriba',
            PHP_ROUND_HALF_DOWN => 'Hacia abajo',
                ], [
            'id' => $this->round_mode_select_id,
            'readonly' => true,
        ]);
        echo "</div>";

        echo "</div>";
    }

    private function _renderObservations() {

        echo "<div class='row'>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->textAreaRow($this->model->movement, 'observation', array(
            'id' => $this->observation_input_id,
            'rows' => 3,
            'maxlength' => 500,
            'required' => $this->model->movement->isObservationRequired()
        ));
        echo "</div>";

        echo "</div>";
    }

    private function _renderRetention() {

        echo "<div class='row " . ($this->model->retention == 0 ? 'hide' : '') . "'>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "retention_percent", array(
            'id' => $this->retention_percent_field_id,
            'label' => "% retención",
            'class' => "us-integer sian-commercial-checked-grid-1-retention-percent",
            'step' => 0,
            'readonly' => $this->model->retention == 0,
            'onchange' => "SIANCommercialCheckedGrid1CalculateRetention('{$this->id}');"
        ));
        echo "</div>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "ret_{$this->model->movement->currency}", array(
            'id' => $this->ret_field_id,
            'label' => "Retenido <span class='currency-symbol'></span>",
            'class' => "us-double{$this->tfixed} sian-commercial-checked-grid-1-ret",
            'step' => $this->tstep,
            'readonly' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "no_ret_{$this->model->movement->currency}", array(
            'id' => $this->no_ret_field_id,
            'label' => "No Ret. <span class='currency-symbol'></span>",
            'class' => "us-double{$this->tfixed} sian-commercial-checked-grid-1-no-ret",
            'step' => $this->tstep,
            'readonly' => true,
        ));
        echo "</div>";

        echo "</div>";
    }

    private function _renderSummary() {
        echo "<div id='{$this->summary_id}'>";
        echo "<div class='well pull-right extended-summary' style='padding: 0px !important;'>";
        echo "<table class='table-condensed'>";
        echo "<tr><td><b>Gravado:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-commercial-checked-grid-1-affected1'></span></td></tr>";
        echo "<tr><td><b>Inafecto:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-commercial-checked-grid-1-inaffected'></span></td></tr>";
        echo "<tr><td><b>Exonerado:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-commercial-checked-grid-1-nobill'></span></td></tr>";
        echo "<tr><td><b>Exportación:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-commercial-checked-grid-1-export'></span></td></tr>";
        echo "<tr><td><b>Gratuito:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-commercial-checked-grid-1-free'></span></td></tr>";
        echo "<tr><td><b>Subtotal:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-commercial-checked-grid-1-net'></span></td></tr>";
        echo "<tr><td><b>IGV:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-commercial-checked-grid-1-igv'></span></td></tr>";
        echo "<tr><td><b>Total venta:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-commercial-checked-grid-1-total'></span></td></tr>";
        echo "<tr><td><b>Percepción:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-commercial-checked-grid-1-perception'></span></td></tr>";
        echo "<tr><td><b>Total a pagar:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-commercial-checked-grid-1-real'></span></td></tr>";
        echo "</table>";
        echo "</div>";
        echo "</div>";
    }

    private function _renderRowButtons($row, $preview_access) {
        if ($row->product_type === Product::TYPE_GROUP) {
            return Yii::app()->controller->widget('application.widgets.USLink', array(
                        'icon' => 'fa fa-eye fa-lg',
                        'route' => '/movement/getCommercialItems',
                        'label' => '',
                        'title' => 'Ver ítems',
                        'class' => 'form',
                        'data' => array(
                            'id' => $row->parent_item_id
                        ),
                        'visible' => true,
                            ), true);
        } else {
            return Yii::app()->controller->widget('application.widgets.USLink', array(
                        'icon' => 'fa fa-eye fa-lg',
                        'route' => '/logistic/product/preview',
                        'label' => '',
                        'title' => 'Visualizar',
                        'class' => 'form',
                        'data' => array(
                            'id' => $row->product_id
                        ),
                        'visible' => $preview_access,
                            ), true);
        }
    }

}
