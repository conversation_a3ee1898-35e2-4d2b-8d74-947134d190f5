
<?php

// class GuiaRemision extends DocumentoEmision
class RemissionGuide extends DocumentIssue
{
    // Tipo de COMPROBANTE que desea generar:
    public static $SENDER_REMISSION_GUIDE = 7; // 7 = GUÍA REMISIÓN REMITENTE
    public static $CARRIER_REMISSION_GUIDE = 8; // 8 = GUÍA REMISIÓN TRANSPORTISTA
    //
    const CARRIER_REMISSION_GUIDE = 8; // 8 = GUÍA REMISIÓN TRANSPORTISTA

    public function __construct()
    {
        // operación (String, Obligatorio): Este valor siempre deberá ser "generar_guia" exactamente. LONGITUD: 11 exactos
        $this->data['operacion'] = SIANFENubeFacT::$GENERATE_GUIDE;
    }

    /**
     * ATRIBUTO: tipo_de_comprobante
     * VALOR: Tipo de COMPROBANTE que desea generar:
     * 7 = GUÍA REMISIÓN REMITENTE
     * 8 = GUÍA REMISIÓN TRANSPORTISTA
     * TIPO DE DATO: Integer
     * REQUISITO: Obligatorio
     * LONGITUD: 1 exacto
     */
    public function setVoucherType($p_i_voucher_type)
    {
        $i_voucher_type = isset($p_i_voucher_type) ? $p_i_voucher_type : null;
        if (is_null($i_voucher_type)) {
            throw new Exception("El Tipo de Comprobante es obligatorio");
        }
        $this->data['tipo_de_comprobante'] = $i_voucher_type;
    }

    /**
     * ATRIBUTO: serie (String, Obligatorio)
     * VALOR: Para GRE Remitente: debe empezar con "T" (Ejm: TTT1)
     * Para GRE Transportista: debe empezar con "V" (Ejm: VVV1)
     * LONGITUD: 4 exactos
     */
    public function setSeries($p_s_serie)
    {
        parent::setSeries($p_s_serie);
    }

    /**
     * PARAMS: cliente_tipo_de_documento (Integer), cliente_numero_de_documento (String), cliente_denominacion (String), cliente_direccion (String). Todos son de REQUISITO `Obligatorio`
     * VALOR: igual que en $this->setCustomerDocumentType(), parent::setCustomerDocumentNumber(), parent::setCustomerName(), parent::setCustomerAddress()
     * Para GRE Remitente: este campo se refiere al Destinatario.
     * Para GRE Transportista: este campo se refiere al Remitente.
     * LONGITUD: (1 exacto, 1 hasta 15, 1 hasta 100,  1 hasta 100)
     */
    public function setCustomer($p_identification_type, $p_identification_number, $p_customer_name, $p_customer_address)
    {
        $this->setCustomerDocumentType($p_identification_type);
        parent::setCustomerDocumentNumber($p_identification_number);
        parent::setCustomerName($p_customer_name);
        parent::setCustomerAddress($p_customer_address);
    }

    /**
     * ATRIBUTO: cliente_tipo_de_documento (Integer, Obligatorio)
     * VALOR: 6 = RUC - REGISTRO ÚNICO DE CONTRIBUYENTE, 1 = DNI - DOC. NACIONAL DE IDENTIDAD, 4 = CARNET DE EXTRANJERÍA
     * 7 = PASAPORTE, A = CÉDULA DIPLOMÁTICA DE IDENTIDAD, 0 = NO DOMICILIADO, SIN RUC (EXPORTACIÓN)
     * Para GRE Remitente: este campo se refiere al Destinatario.
     * Para GRE Transportista: este campo se refiere al Remitente.
     * LONGITUD: 1 exacto
     */
    public function setCustomerDocumentType($p_s_identification_type)
    {
        $s_identification_type = isset($p_s_identification_type) ? $p_s_identification_type : "";
        if ($s_identification_type === "") {
            throw new Exception("El tipo de documento cliente es obligatorio");
        }
        $this->data['cliente_tipo_de_documento'] = $s_identification_type;
    }

    /**
     * ATRIBUTO: fecha_de_emision (Date, Obligatorio)
     * VALOR: igual que en parent::setDateOfIssue()
     * *Importante: Como máximo puede emitir con 1 día anterior a la fecha actual. 
     * LONGITUD: 10 exactos
     */
    public function setDateOfIssue($p_s_emission_date)
    {
        parent::setDateOfIssue($p_s_emission_date);
    }

    /**
     * ATRIBUTO: motivo_de_traslado (String, Obligatorio)
     * VALOR: 
     *     "01" = "VENTA"
     *     "14" = "VENTA SUJETA A CONFIRMACION DEL COMPRADOR"
     *     "02" = "COMPRA"
     *     "04" = "TRASLADO ENTRE ESTABLECIMIENTOS DE LA MISMA EMPRESA"
     *     "18" = "TRASLADO EMISOR ITINERANTE CP"
     *     "08" = "IMPORTACION"
     *     "09" = "EXPORTACION"
     *     "13" = "OTROS"
     * LONGITUD: 2 exactos
     */
    public function setReasonForTransfer($p_s_reason_for_transfer)
    {
        $this->setRaw('motivo_de_traslado', $p_s_reason_for_transfer);
    }

    /**
     * ATRIBUTO: motivo_de_traslado_otros_descripcion (String, Opcional)
     * VALOR: Solo se aplica para Motivo de traslado: “13” - OTROS
     * Descripción adicional alfanumérico con espaciados simples.
     * LONGITUD: Hasta 70 caracteres.
     */
    public function setReasonForTransferOtherDescription($p_s_reason_for_transfer_other_description)
    {
        $this->data['motivo_de_traslado_otros_descripcion'] = $p_s_reason_for_transfer_other_description;
    }

    /**
     * ATRIBUTO: documento_relacionado_codigo (String, Condicional)
     * VALOR: Solo para motivo de traslado importación ó exportación)
        50 = "Declaración Aduanera de Mercancías"
        52 = "Declaración Simplificada (DS)"
        CÓDIGO DAM o DS
        * Enviar primero la SERIE del código DAM o DS de 4 dígitos numéricos.
        * Posteriormente enviar el código DAM o DS.

        IMPORTANTE:
        Si el motivo de traslado es IMPORTACIÓN debe registrar el siguiente formato en el formulario del código DAM o DS.
        a) Si el tipo de documento relacionado es 50 - Declaración Aduanera de Mercancías el formato sería:
        xxxx(serie)/xxx-xxxx-10-xxxxxx(DAM)
        Ejemplo: 0001/123-1234-10-123456
        b) Si el tipo de documento relacionado es 52 - Declaración Simplificada (DS) el formato sería:
        xxxx(serie)/xxx-xxxx-18-xxxxxx(DS)
        Ejemplo: 0001/123-1234-18-123456
        _____________________________________________
        Si el motivo de traslado es EXPORTACIÓN debe registrar el siguiente formato en el formulario del código DAM o DS.

        a) Si el tipo de documento relacionado es 50 - Declaración Aduanera de Mercancías el formato sería:
        xxxx(serie)/xxx-xxxx-40-xxxxxx(DAM)
        Ejemplo: 0001/123-1234-40-123456
        b) Si el tipo de documento relacionado es 52 - Declaración Simplificada (DS) el formato sería:
        xxxx(serie)/xxx-xxxx-48-xxxxxx(DS)
        Ejemplo: 0001/123-1234-48-123456
     * LONGITUD: 2 exactos
     */
    public function setDocumentRelatedCode($p_s_document_related_code)
    {
        $this->data['documento_relacionado_codigo'] = $p_s_document_related_code;
    }

    /**
     * ATRIBUTO: peso_bruto_total (Decimal, Obligatorio)
     * VALOR: En KILOGRAMOS, ejemplo: 4.00
        Importante: debe ser mayor a “0”.
     * LONGITUD: 1 hasta 12 enteros, hasta con 10 decimales
     */
    public function setTotalGrossWeight($p_f_total_gross_weight)
    {
        $f_total_gross_weight = isset($p_f_total_gross_weight) ? $p_f_total_gross_weight : null;
        if (!is_null($f_total_gross_weight) && !empty($f_total_gross_weight)) {
            if ($f_total_gross_weight > 0) {
                $this->data['peso_bruto_total'] = $p_f_total_gross_weight;
            } else {
                throw new Exception("El Peso bruto debe ser mayor a “0”.");
            }
        } else {
            throw new Exception("El Peso bruto es obligatorio");
        }
    }

    /**
     * ATRIBUTO: peso_bruto_unidad_de_medida (String, Obligatorio)
     * VALOR: Código de la unidad de medida, solo se aceptan:
        KGM = “Kilogramos” ó TNE = “Toneladas”
     * LONGITUD: 3 dígitos
     */
    public function setGrossWeightUnitOfMeasure($p_s_gross_weight_unit_of_measure)
    {   
        $s_gross_weight_unit_of_measure = isset($p_s_gross_weight_unit_of_measure) ? $p_s_gross_weight_unit_of_measure : "";

        if (!empty($s_gross_weight_unit_of_measure)) {
            if ($s_gross_weight_unit_of_measure === "KGM" || $s_gross_weight_unit_of_measure === "TNE") {
                $this->data['peso_bruto_unidad_de_medida'] = $s_gross_weight_unit_of_measure;
            } else {
                throw new Exception("Para el Código de la unidad de medida, solo se aceptan: KGM = “Kilogramos” ó TNE = “Toneladas”");
            }
        } else {
            throw new Exception("La Unidad de Medida para Peso bruto es obligatorio");
        }
    }

    /**
     * ATRIBUTO: numero_de_bultos (Decimal, Obligatorio)
     * VALOR: Sólo para GRE Remitente
        Cantidad de bultos.
        Importante: debe ser numérico (entero)
     * LONGITUD: 1 hasta 6 enteros
     */
    public function setNumberOfPackages($p_f_number_of_packages)
    {
        $this->data['numero_de_bultos'] = $p_f_number_of_packages;
    }

    /**
     * ATRIBUTO: tipo_de_transporte (String, Obligatorio)
     * VALOR: Sólo para GRE Remitente
        "01" = "TRANSPORTE PÚBLICO"
        "02" = "TRANSPORTE PRIVADO"
     * LONGITUD: 2 exactos
     */
    public function setTypeTransport($p_s_type_transport)
    {
        $this->data['tipo_de_transporte'] = $p_s_type_transport;
    }

    /**
     * ATRIBUTO: fecha_de_inicio_de_traslado (Date, Obligatorio)
     * VALOR: Para GRE Remitente y Transportista
        Fecha de inicio del traslado. Formato DD-MM-AAAA
        Ejemplo: 10-05-2017
     * LONGITUD: 10 exactos
     */
    public function setTransferStartDate($p_d_transfer_start_date)
    {
        $this->data['fecha_de_inicio_de_traslado'] = $p_d_transfer_start_date;
    }

    /**
     * ATRIBUTO: transportista_documento_tipo (Integer, Condicional)
     * VALOR: Sólo para GRE Remitente
        6 = RUC - REGISTRO ÚNICO DE CONTRIBUYENTE
        Sólo cuando 'tipo_de_transporte' es "01"
     * LONGITUD: 1 exacto
     */
    public function setCarrierDocumentType($p_i_carrier_document_type)
    {
        $this->data['transportista_documento_tipo'] = $p_i_carrier_document_type;
    }

    /**
     * ATRIBUTO: transportista_documento_numero (String, Condicional)
     * VALOR: Sólo para GRE Remitente
        Ejemplo: RUC del CLIENTE
        Sólo cuando 'tipo_de_transporte' es "01"
     * LONGITUD: 11 exacto
     */
    public function setCarrierDocumentNumber($p_s_carrier_document_number)
    {
        $this->data['transportista_documento_numero'] = $p_s_carrier_document_number;
    }

    /**
     * ATRIBUTO: transportista_denominacion (String, Condicional)
     * VALOR: Sólo para GRE Remitente
        Razón o nombre completo del TRANSPORTISTA.
        Sólo cuando 'tipo_de_transporte' es "01"
     * LONGITUD: 1 hasta 100
     */
    public function setCarrierName($p_s_carrier_name)
    {
        $this->data['transportista_denominacion'] = $p_s_carrier_name;
    }

    /**
     * ATRIBUTO: transportista_placa_numero (String, Obligatorio)
     * VALOR: Para GRE Remitente y Transportista
        Ejemplo: ABC321 (no deben ser ceros ni debe tener guiones)
     * LONGITUD: 6 hasta 8
     */
    public function setCarrierPlateNumber($p_s_carrier_plate_number)
    {
        $this->data['transportista_placa_numero'] = $p_s_carrier_plate_number;
    }

    /**
     * ATRIBUTO: tuc_vehiculo_principal (String, Opcional)
     * VALOR: Para GRE Transportista
        Tarjeta Única de Circulación Electrónica o Certificado de Habilitación vehicular
        Ejemplo: ABC321981R (Solo se permite mayúsculas y números alfanumérico sin guiones o cualquier otro caracter)
     * LONGITUD: 10 hasta 15 dígitos.
     */
    public function setTucMainVehicle($p_s_tuc_main_vehicle)
    {
        $this->data['tuc_vehiculo_principal'] = $p_s_tuc_main_vehicle;
    }

    /**
     * ATRIBUTO: conductor_documento_tipo (Integer, Condicional)
     * VALOR: 1 = DNI - DOC. NACIONAL DE IDENTIDAD
        4 = CARNET DE EXTRANJERÍA
        7 = PASAPORTE
        A = CÉDULA DIPLOMÁTICA DE IDENTIDAD
        0 = NO DOMICILIADO, SIN RUC (EXPORTACIÓN)
        Para GRE Remitente: sólo cuando 'tipo_de_transporte' es "02".
        Para GRE Transportista: dato obligatorio.
     * LONGITUD: 1 exacto
     */
    public function setDriverDocumentType($p_i_driver_document_type)
    {
        $this->data['conductor_documento_tipo'] = $p_i_driver_document_type;
    }

    /**
     * ATRIBUTO: conductor_documento_numero (String, Condicional)
     * VALOR: Ejemplo: Número de DNI, Etc.
        Para GRE Remitente: sólo cuando 'tipo_de_transporte' es "02".
        Para GRE Transportista: dato obligatorio.
     * LONGITUD: 1 hasta 15
     */
    public function setDriverDocumentNumber($p_s_driver_document_number)
    {
        $this->data['conductor_documento_numero'] = $p_s_driver_document_number;
    }

    /**
     * ATRIBUTO: conductor_denominacion (String, Condicional)
     * VALOR: Razón o nombre completo del CONDUCTOR.
        Para GRE Remitente: sólo cuando 'tipo_de_transporte' es "02".
        Para GRE Transportista: dato obligatorio.
     * LONGITUD: 1 hasta 100
     */
    public function setDriverDenomination($p_s_driver_denomination)
    {
        $this->data['conductor_denominacion'] = $p_s_driver_denomination;
    }

    /**
     * ATRIBUTO: conductor_nombre (String, Condicional)
     * VALOR: Es obligatorio Si el tipo de transporte es PRIVADO
        Para GRE Remitente: sólo cuando 'tipo_de_transporte' es "02".
        Para GRE Transportista: dato obligatorio.
     * LONGITUD: Hasta 250
     */
    public function setDriverName($p_s_driver_name)
    {
        $this->data['conductor_nombre'] = $p_s_driver_name;
    }

    /**
     * ATRIBUTO: conductor_apellidos (String, Condicional)
     * VALOR: Es obligatorio Si el tipo de transporte es PRIVADO
        Para GRE Remitente: sólo cuando 'tipo_de_transporte' es "02".
        Para GRE Transportista: dato obligatorio.
     * LONGITUD: Hasta 250
     */
    public function setDriverSurname($p_s_driver_surname)
    {
        $this->data['conductor_apellidos'] = $p_s_driver_surname;
    }

    /**
     * ATRIBUTO: conductor_numero_licencia (String, Condicional)
     * VALOR: Es obligatorio Si el tipo de transporte es PRIVADO
        Para GRE Remitente: sólo cuando 'tipo_de_transporte' es "02".
        Para GRE Transportista: dato obligatorio.
     * LONGITUD: 9 hasta 10
     */
    public function setDriverLicenseNumber($p_s_driver_license_number)
    {
        $this->data['conductor_numero_licencia'] = $p_s_driver_license_number;
    }

    /**
     * ATRIBUTO: punto_de_partida_ubigeo (String, Obligatorio)
     * VALOR: Usar la siguiente tabla según corresponda: https://drive.google.com/open?id=1-aHRVG5c5-IUkTC_jOJ4ktna6MCR86rK8Bc7AwW2whA
     * LONGITUD: Hasta 6
     */
    public function setStartingPointUbigeo($p_s_ubigeo_starting_point)
    {
        $this->data['punto_de_partida_ubigeo'] = $p_s_ubigeo_starting_point;
    }

    /**
     * ATRIBUTO: punto_de_partida_direccion (String, Obligatorio)
     * VALOR: Dirección exacta
     * LONGITUD: Hasta 150
     */
    public function setStartingPointDirection($p_s_starting_point_direction)
    {
        $this->data['punto_de_partida_direccion'] = $p_s_starting_point_direction;
    }

    /**
     * ATRIBUTO: punto_de_partida_codigo_establecimiento_sunat (String, Condicional)
     * VALOR: Para los motivos de traslado con código 04, 18 Ejemplo: “0000”
     * LONGITUD: 4 exactos
     */
    public function setStartingPointCodeEstablishmentSunat($p_s_starting_point_code_establishment_sunat)
    {
        $this->data['punto_de_partida_codigo_establecimiento_sunat'] = $p_s_starting_point_code_establishment_sunat;
    }

    /**
     * ATRIBUTO: punto_de_llegada_ubigeo (String, Obligatorio)
     * VALOR: Usar la siguiente tabla según corresponda: https://drive.google.com/open?id=1-aHRVG5c5-IUkTC_jOJ4ktna6MCR86rK8Bc7AwW2whA
     * LONGITUD: Hasta 6
     */
    public function setArrivalPointUbigeo($p_s_arrival_point_ubigeo)
    {
        $this->data['punto_de_llegada_ubigeo'] = $p_s_arrival_point_ubigeo;
    }

    /**
     * ATRIBUTO: punto_de_llegada_direccion (String, Obligatorio)
     * VALOR: Dirección exacta
     * LONGITUD: Hasta 150
     */
    public function setArrivalPointDirection($p_s_arrival_point_direction)
    {
        $this->data['punto_de_llegada_direccion'] = $p_s_arrival_point_direction;
    }

    /**
     * ATRIBUTO: punto_de_llegada_codigo_establecimiento_sunat (String, Condicional)
     * VALOR: Para los motivos de traslado con código 04, 18 Ejemplo: “0000”
     * LONGITUD: 4 exactos
     */
    public function setArrivalPointCodeEstablishmentSunat($p_s_arrival_point_code_establishment_sunat)
    {
        $this->data['punto_de_llegada_codigo_establecimiento_sunat'] = $p_s_arrival_point_code_establishment_sunat;
    }

    /**
     * ATRIBUTO: enviar_automaticamente_al_cliente (Boolean, Condicional)
     * VALOR: igual que en parent::setSendAutomaticallyToClient(). Se envía sólo si la GRE fue aceptada por la Sunat.
     * LONGITUD: Hasta 5
     */
    public function setSendAutomaticallyToClient($p_b_send_automatically_to_client)
    {
        parent::setSendAutomaticallyToClient($p_b_send_automatically_to_client);
    }

    /**
     * ADD ITEMS O LÍNEAS DEL DOCUMENTO A GRE (REMITENTE Y TRANSOPORTISTA)
     * ATRIBUTOS: unidad_de_medida, codigo, descripcion, cantidad, codigo_dam
     * VALOR: 
     * LONGITUD: 11 exacto
     */
    public function addItemGR($p_unit_of_measurement, $p_code, $p_description, $p_quantity, $p_dam_code = "") {
        $item = [
            'unidad_de_medida'  => $p_unit_of_measurement,
            'codigo'            => $p_code,
            'descripcion'       => $p_description,
            'cantidad'          => $p_quantity,
            'codigo_dam'    => $p_dam_code
        ];

        if (!isset($this->data['items'])) {
            $this->data['items'] = [];
        }
        $this->data['items'][] = $item;
    }

    /**
     * ATRIBUTO: unidad_de_medida (String, Obligatorio)
     * VALOR: 
        NIU = PRODUCTO
        ZZ = SERVICIO
        Si necesitas más unidades de medida, debes crearlas primeramente en tu cuenta de NUBEFACT para que estén disponibles.

        En caso de que el motivo sea Importación o Exportación debe usar las unidades de medida del Catálogo 65 de la SUNAT:  Código de unidades de medida (para uso solo para la GRE en DAM o DS)

        https://cpe.sunat.gob.pe/node/116
     * LONGITUD: 2 hasta 5
     */
    public function setUnitOfMeasurement($p_s_unit_of_measurement)
    {
        $this->data['unidad_de_medida'] = $p_s_unit_of_measurement;
    }

    /**
     * ATRIBUTO: codigo (String, Opcional)
     * VALOR: Código interno del producto o servicio, asignado por ti. Ejemplo: C001
     * LONGITUD: 1 hasta 250
     */
    public function setItemCode($p_f_number_of_packages)
    {
        $this->data['codigo'] = $p_f_number_of_packages;
    }

    /**
     * ATRIBUTO: descripcion (Text, Obligatorio)
     * VALOR: Descripción del producto o servicio. Ejemplo: SERVICIO DE REPARACIÓN DE PC, ETC.
     * LONGITUD: 1 hasta 250
     */
    public function setItemDescription($p_s_description)
    {
        $this->data['descripcion'] = $p_s_description;
    }

    /**
     * ATRIBUTO: cantidad (Numeric, Obligatorio)
     * VALOR: Ejemplo: 1.215
     * LONGITUD: 1 hasta
        12 enteros, hasta con 
        10 decimales
     */
    public function setItemQuantity($p_f_quantity)
    {
        $this->data['cantidad'] = $p_f_quantity;
    }

    /**
     * ATRIBUTO: codigo_dam (String, Condicional)
     * VALOR: Solo para motivo de traslado exportación o importación:
        Ejemplo (DAM): xxxx(serie)/xxx-xxxx-10-xxxxxx(DAM)
        0001/123-1234-10-123456
        b) Ejemplo (DS): xxxx(serie)/xxx-xxxx-18-xxxxxx(DS)
        0001/123-1234-18-123456

     * LONGITUD: 23 caracteres
     */
    public function setItemDamCode($p_s_dam_code)
    {
        $this->data['codigo_dam'] = $p_s_dam_code;
    }

}