<?php

class SIANImport extends CWidget {

    public $id;
    public $form;
    public $model;
    public $viewScenario;
    public $readonly = false;
    public $ifixed = 3;
    public $tfixed = 2;
    public $istep = 0.001;
    public $only_allowed_divisions = false;
    public $advanced = true;
    public $onChangeCCDependence = ''; //Para hacer un puente
    //PRIVATE
    private $controller;
    private $presentationMode;
    private $presentationUrl;
    private $autocomplete_id;
    private $item_aux_id;
    private $item_value_id;
    private $item_text_id;
    private $item_item_type_id;
    private $item_product_type_id;
    private $item_pres_quantity_id;
    private $item_equivalence_id;
    private $item_allow_decimals_id;
    private $item_unit_stock_id;
    private $item_mixed_stock_label_id;
    private $item_tfob_id;
    private $item_kilos_id;
    private $item_freight_id;
    private $item_insurance_id;
    private $add_button_id;
    private $summary_id;
    private $observation_input_id;
    private $ad_valorem_input_id;
    private $round_mode_select_id;
    private $warehouse_input_id;
    private $preview_access;
    private $commercialStockUrl;
    private $explainCommercialStockUrl;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->presentationMode = SpGetProductPresentations::MODE_COMBOBOX_WITH_PRICES;
        $this->presentationUrl = $this->controller->createUrl("/movement/getPresentations");

        //GRID ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //PRIVATE
        $this->autocomplete_id = $this->controller->getServerId();
        $this->item_aux_id = $this->controller->getServerId();
        $this->item_value_id = $this->controller->getServerId();
        $this->item_text_id = $this->controller->getServerId();
        $this->item_item_type_id = $this->controller->getServerId();
        $this->item_product_type_id = $this->controller->getServerId();
        $this->item_pres_quantity_id = $this->controller->getServerId();
        $this->item_equivalence_id = $this->controller->getServerId();
        $this->item_allow_decimals_id = $this->controller->getServerId();
        $this->item_unit_stock_id = $this->controller->getServerId();
        $this->item_mixed_stock_label_id = $this->controller->getServerId();
        $this->item_tfob_id = $this->controller->getServerId();
        $this->item_kilos_id = $this->controller->getServerId();
        $this->item_freight_id = $this->controller->getServerId();
        $this->item_insurance_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();
        $this->observation_input_id = $this->controller->getServerId();
        $this->ad_valorem_input_id = $this->controller->getServerId();
        $this->round_mode_select_id = $this->controller->getServerId();
        $this->warehouse_input_id = $this->controller->getServerId();
        //URL
        $this->commercialStockUrl = $this->controller->createUrl("/movement/loadCommercialStocks");
        $this->explainCommercialStockUrl = $this->controller->createUrl("/logistic/merchandise/explainCommercialStock");
        //DIVS
        $this->summary_id = $this->controller->getServerId();

        $product_ids = [];
        foreach ($this->model->tempItems as $item) {
            $product_ids[] = $item->itemObj->product_id;
        }

        //Si hay productos...
        if (count($product_ids) > 0) {

            $presentationItems = SpGetProductPresentations::getAssociative($this->presentationMode, $product_ids, $this->controller->getOrganization()->globalVar->display_currency);

            //PRODUCT ITEMS
            $productItems = [];
            foreach ($this->model->tempItems as $item) {

                $attributes = [];

                $attributes['product_id'] = $item->itemObj->product_id;
                $attributes['item_type_id'] = $item->itemObj->item_type_id;
                $attributes['product_type'] = $item->itemObj->product_type;
                $attributes['product_name'] = $item->itemObj->product_name;
                $attributes['unit_stock'] = isset($item->itemObj->unit_stock) ? $item->itemObj->unit_stock : 0;
                $attributes['mixed_stock'] = isset($item->itemObj->mixed_stock) ? $item->itemObj->mixed_stock : '';
                $attributes['pres_quantity'] = $item->itemObj->pres_quantity;
                $attributes['presentationItems'] = $presentationItems[$item->itemObj->product_id];
                $attributes['equivalence'] = $item->itemObj->equivalence;
                $attributes['allow_decimals'] = $item->itemObj->allow_decimals;
                $attributes['tfob'] = round($item->duaProduct->{"tfob_{$this->model->movement->currency}"}, $this->ifixed);
                $attributes['kilos'] = round($item->duaProduct->kilos, 3);
                $attributes['freight'] = round($item->duaProduct->{"freight_{$this->model->movement->currency}"}, $this->ifixed);
                $attributes['insurance'] = round($item->duaProduct->{"insurance_{$this->model->movement->currency}"}, $this->ifixed);

                //Para enlace entre productos
                $attributes['quantity_item_id'] = isset($item->itemObj->quantity_item_id) ? $item->itemObj->quantity_item_id : '';
                $attributes['total_item_id'] = isset($item->itemObj->total_item_id) ? $item->itemObj->total_item_id : '';

                $attributes['errors'] = $item->getAllErrors();

                $productItems[] = $attributes;
            }
        } else {
            $productItems = [];
        }
        //ACCESS
        $this->preview_access = $this->controller->checkRoute('/logistic/product/preview');
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-import.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        
        var grid = $('#{$this->id}');
        grid.data('view-access', " . json_encode($this->preview_access) . ");//ACCESS
        grid.data('view-url', '{$this->controller->createUrl('/logistic/product/preview')}');//URL
        grid.data('currency', '{$this->model->movement->currency}');
        grid.data('ifixed', {$this->ifixed});
        grid.data('tfixed', {$this->tfixed});
        grid.data('istep', {$this->istep});
        grid.data('count', 0);//COUNT
        grid.data('round_mode', {$this->model->round_mode});
        grid.data('warehouse_input_id', '{$this->warehouse_input_id}');
        grid.data('independent_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INDEPENDENT . "');
        grid.data('inherit_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INHERIT . "');
        grid.data('validate_stock', " . CJSON::encode($this->model->movement->validate_stock == 1) . ");
        grid.data('commercialStockUrl', '{$this->commercialStockUrl}');
        grid.data('explainCommercialStockUrl', '{$this->explainCommercialStockUrl}');
        grid.data('stock_mode', " . CJSON::encode($this->model->movement->scenario->stock_mode) . ");            
        grid.data('exclude_id', " . CJSON::encode($this->model->movement->kardex_unlock_exclude_id) . ");
            
        $(document).ready(function() {                  

            //PRODUCTOS
            var array = " . CJSON::encode($productItems) . ";

            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {
                    SIANImportAddItem('{$this->id}', array[i]['product_id'], array[i]['item_type_id'], array[i]['product_type'], array[i]['product_name'], array[i]['unit_stock'], array[i]['mixed_stock'], array[i]['pres_quantity'], array[i]['presentationItems'], array[i]['equivalence'], array[i]['allow_decimals'], array[i]['tfob'], array[i]['kilos'], array[i]['freight'], array[i]['insurance'], array[i]['quantity_item_id'], array[i]['total_item_id'], array[i]['errors']);
                }
            }
            else
            {
                $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
            }

            //UPDATE
            SIANImportUpdate('{$this->id}');
            unfocusable();        

            //CURRENCY SYMBOL
            $('span.currency-symbol').text('" . Currency::getSymbol($this->model->movement->currency) . "');
        
            //Para hacer reordenable las filas
            $(function() {
                $('#{$this->id}').find('tbody').sortable({
                    stop: function( event, ui ) {
                        SIANImportUpdateAmounts('{$this->id}');
                    }
                });
            });

        });

        //SI CAMBIA LA MONEDA
        $('#{$this->id}').data('changeCurrency', function(currency){

            var table = $('#{$this->id}');
            var exchange_rate = table.data('exchange_rate');

            table.find('tr.sian-import-item').each(function(index) {
                var item = $(this);

                //Actualizamos montos
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                    break;
                    case '" . Currency::USD . "':
                    break;
                }
            });

            table.data('currency', currency);
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            //Limpiamos
            USAutocompleteReset('{$this->autocomplete_id}');
            //Actualizar montos
            SIANImportUpdateAmounts('{$this->id}');
        });    
       
        //SI CAMBIA OPERACION
        $('#{$this->id}').data('changeOperation', function(changeData){
            var gridObj =  $('#{$this->id}');
            //GUARDAMOS INFO DE OPERACIÓN
            gridObj.data('operationChangeData', changeData);

            SIANImportUpdateAmounts('{$this->id}');
        });

        $('#{$this->id}').data('changeCCDependence', function(operationChangeData){
            {$this->onChangeCCDependence};
        });

        $('#{$this->id}').data('changeDocument', function(changeData){
        });
        
        $('#{$this->id}').data('changeEmission', function(changeData){
            var table = $('table#{$this->id}');
            //Seteamos
            table.data('emission_date', changeData.emission_date); 
            //Cambiamos a todos los links
            table.find('a.sian-import-item-mixed-stock').data('emission_date', changeData.emission_date);    
            $('#{$this->item_mixed_stock_label_id}').data('emission_date', changeData.emission_date);
            //Si usa stock se cargará al cambiar la fecha de emisión
            " . ($this->model->movement->validate_stock == 1 ? "SIANImportLoadCommercialStocks('{$this->id}');" : "") . "
            USAutocompleteReset('{$this->autocomplete_id}');
        });
        
        $('#{$this->id}').data('changeExchange', function(exchange_rate){

            var table = $('table#{$this->id}');
            //Seteamos
            table.data('exchange_rate', exchange_rate); 
            //Obtenemos
            var currency = table.data('currency');

            table.find('tr.sian-import-item').each(function(index) {
                var item = $(this);


                //Cambiamos los balances
                switch('{$this->controller->getOrganization()->globalVar->display_currency}')
                {
                    case '" . Currency::PEN . "':
                    break;
                    case '" . Currency::USD . "':
                     break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
                
                //Cambiamos mínimos
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                    break;
                    case '" . Currency::USD . "':
                     break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }

                //Seteamos balances
            });

            //Actualizamos TC
            table.data('exchange_rate', exchange_rate);
            //Limpiamos
            USAutocompleteReset('{$this->autocomplete_id}');
            //Actualizamos montos
            SIANImportUpdateAmounts('{$this->id}');
        });
       
        $('#{$this->id}').data('changeWarehouse', function(changeData){
            var grid = $('#{$this->id}');
            //Seteamos
            var emission_date = grid.data('emission_date');
            $('#{$this->warehouse_input_id}').val(changeData.warehouse_id);
            //
            if(changeData.warehouse_id !== 'undefined' && changeData.warehouse_id.length > 0 && emission_date)
            {
                SIANImportUpdateAmounts('{$this->id}');
            }      
        });
        
        $('body').on('change', '#{$this->item_equivalence_id}', function(e) {
            var product_id = USAutocompleteField('{$this->autocomplete_id}', 'value');
            //Si se cambia
            " . ($this->model->movement->validate_stock == 1 ? "
            SIANImportLoadCommercialStock('{$this->id}', product_id, $('#{$this->item_equivalence_id}'), $('#{$this->item_unit_stock_id}'), null, $('#{$this->item_mixed_stock_label_id}'));
            " : "") . "
        });
        
        $('body').on('change', '#{$this->id} select.sian-import-item-equivalence', function(e) {
            var rowObj = $(this).closest('tr');
            " . ($this->model->movement->validate_stock == 1 ? "
            SIANImportGetAndLoadCommercialStock('{$this->id}', rowObj.attr('id'));
            " : "") . "
        });
        
        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var currency = $('#{$this->id}').data('currency');

            var aux_id = $('#{$this->item_aux_id}').val();
            var product_id = $('#{$this->item_value_id}').val();
            var product_name = $('#{$this->item_text_id}').val();
            var item_type_id = $('#{$this->item_item_type_id}').val();
            var product_type = $('#{$this->item_product_type_id}').val();
            var pres_quantity = $('#{$this->item_pres_quantity_id}').double2();
            var presentationItems = $('#{$this->item_equivalence_id}').data('presentationItems');
            var equivalence = $('#{$this->item_equivalence_id}').double2();
            var allow_decimals = $('#{$this->item_allow_decimals_id}').integer();
            " . ($this->model->movement->validate_stock == 1 ? "
                if (product_type === PRODUCT_TYPE_MERCHANDISE)
                {
                    var unit_stock = $('#{$this->item_unit_stock_id}').val();
                    var mixed_stock = $('#{$this->item_mixed_stock_label_id}').text();                        
                }
                else
                {
                    var unit_stock = '0'
                    var mixed_stock = '-';   
                }
            " : "
                var unit_stock = 0;
                var mixed_stock = '';                
            ") . "                
            var tfob = $('#{$this->item_tfob_id}').floatVal({$this->ifixed});
            var kilos = $('#{$this->item_kilos_id}').floatVal(3);
            var freight = $('#{$this->item_freight_id}').floatVal({$this->ifixed});
            var insurance = $('#{$this->item_insurance_id}').floatVal({$this->ifixed});
                
            if (product_id.length === 0)
            {
                $('#{$this->item_aux_id}').focus();
                return;
            }

            SIANImportAddItem('{$this->id}', product_id, item_type_id, product_type, product_name, unit_stock, mixed_stock, pres_quantity, presentationItems, equivalence, allow_decimals, tfob, kilos, freight, insurance, '', '', []);
            USAutocompleteReset('{$this->autocomplete_id}');
                
            //FOCUS
            $('#{$this->item_aux_id}').focus();
            SIANImportUpdate('{$this->id}');
            unfocusable();
        });
        
        function {$this->id}Clear()
        {
            $('#{$this->item_item_type_id}').val('');    
            $('#{$this->item_product_type_id}').val('');    
            $('#{$this->item_pres_quantity_id}').val(1);    
            $('#{$this->item_pres_quantity_id}').removeAttr('disabled');
            $('#{$this->item_equivalence_id}').html('<option value>" . Strings::SELECT_OPTION . "</option>'); 
            $('#{$this->item_allow_decimals_id}').val(0);
            $('#{$this->item_unit_stock_id}').val(null);
            $('#{$this->item_mixed_stock_label_id}').text(0);  
            USLinkStatus('{$this->item_mixed_stock_label_id}', false, {id: null});
            $('#{$this->item_tfob_id}').val(0);
            $('#{$this->item_kilos_id}').val(0);
            $('#{$this->item_freight_id}').val(0);
            $('#{$this->item_insurance_id}').val(0);
            $('#{$this->item_aux_id}').focus().select();
        }
        
        function {$this->id}GetPresentations(product_id, equivalence)
        {
            if(product_id != undefined)
            {
                $.ajax({
                    type: 'post',
                    url: '{$this->presentationUrl}',
                    data: {
                        mode: {$this->presentationMode},
                        product_ids: [product_id],
                        currency: '" . $this->controller->getOrganization()->globalVar->display_currency . "'
                    },
                    beforeSend: function (xhr) {
                        window.active_ajax++;
                        //Ocultamos los tooltip
                        $('div.ui-tooltip').remove();
                    },                          
                    success: function(data) {
                    
                        $.each(data, function(index, item) {
                            SIANImportFillPresentations('{$this->item_equivalence_id}', item, equivalence);
                        });       
                        window.active_ajax--;
                    },
                    error: function(request, status, error) { // if error occured
                        window.active_ajax--;
                        bootbox.alert(us_message(request.responseText, 'error'));
                    },
                    dataType: 'json'
                });
            }
        }
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Mercaderías y servicios',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempItems') ? 'us-error' : ''
            )
        ));

        if (!$this->readonly) {

            $a_attributes = [
                array('name' => 'product_id', 'width' => 10, 'types' => array('id', 'value', 'aux'), 'not_in' => "window.productIds"),
                array('name' => 'item_type_id', 'hidden' => true, 'update' => "$('#{$this->item_item_type_id}').val(item_type_id);"),
                array('name' => 'product_type', 'width' => 5, 'in' => "['" . Product::TYPE_MERCHANDISE . "']", 'update' => "$('#{$this->item_product_type_id}').val(product_type);"),
                array('name' => 'barcode', 'width' => 20),
                array('name' => 'product_name', 'width' => ($this->model->movement->validate_stock == 1 ? 30 : 35), 'types' => array('text')),
                array('name' => 'model', 'hidden' => true),
                array('name' => 'part_number', 'hidden' => true),
                array('name' => 'equivalence', 'hidden' => true),
                array('name' => 'allow_decimals', 'hidden' => true, 'update' => "
                                $('#{$this->item_allow_decimals_id}').val(allow_decimals);
                                $('#{$this->item_pres_quantity_id}').allowDecimals(allow_decimals == 1 ? 2 : 0);
                            ", 'search' => false)
            ];
            //Usa stock
            if ($this->model->movement->validate_stock == 1) {
                $a_attributes[] = array('name' => 'unit_stock', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->item_unit_stock_id}').val(unit_stock);");
                $a_attributes[] = array('name' => 'stock', 'width' => 5, 'search' => false);
                $a_attributes[] = array('name' => 'mixed_stock', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->item_mixed_stock_label_id}').text(mixed_stock); USLinkStatus('{$this->item_mixed_stock_label_id}', true, {id: product_id, equivalence: equivalence})");
            }

            $a_attributes[] = array('name' => 'measure_name', 'width' => 15);
            $a_attributes[] = array('name' => 'currency', 'hidden' => true, 'sortable' => false);
            $a_attributes[] = array('name' => 'currency_name', 'width' => 5, 'sortable' => false);
            $a_attributes[] = array('name' => 'iaprice', 'width' => 10);

            echo "<div class='row'>";
            echo "<div class='" . ($this->model->movement->validate_stock == 1 ? "col-lg-4 col-md-4 col-sm-12 col-xs-12" : "col-lg-5 col-md-5 col-sm-12 col-xs-12") . "'>";
            echo $this->widget('application.widgets.USAutocomplete', array(
                'id' => $this->autocomplete_id,
                'label' => 'Mercadería / servicio',
                'name' => null,
                'aux_id' => $this->item_aux_id,
                'value_id' => $this->item_value_id,
                'text_id' => $this->item_text_id,
                'hint' => ($this->model->movement->validate_stock == 1 ? "El stock que se muestra es el stock <b>{$this->model->movement->getStockModeLabel()}</b>." : null),
                'view' => array(
                    'model' => 'DpAllProductPresentations',
                    'scenario' => $this->model->movement->validate_stock == 1 ? DpAllProductPresentations::SCENARIO_WITH_STOCK : DpAllProductPresentations::SCENARIO_WITHOUT_STOCK,
                    'attributes' => $a_attributes,
                    'params' => "{
                        only_allowed_divisions: " . ($this->only_allowed_divisions == 1 ? 1 : 0) . "
                        " . ($this->model->movement->validate_stock == 1 ? ",
                        date: function () { 
                            return $('#{$this->id}').data('emission_date'); 
                        },
                        exclude_id: function(){
                            return " . CJSON::encode($this->model->movement_id) . "//Para edición
                        },
                        stock_mode: {$this->model->movement->stock_mode}" : "")
                    . "}",
                ),
                'maintenance' => array(
                    'module' => 'logistic',
                    'controller' => 'product',
                    'buttons' => array(
                        'preview' => array(
                            'access' => $this->preview_access,
                        ),
                        'create' => [],
                        'update' => [],
                    ),
                ),
                "onselect" => "{$this->id}GetPresentations(product_id, equivalence);",
                'onreset' => "{$this->id}Clear();"
                    ), true);
            echo "</div>";

            //Si se usa stock se renderiza el campo
            if ($this->model->movement->validate_stock == 1) {
                echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
                echo CHtml::hiddenField(null, 0, array(
                    'id' => $this->item_unit_stock_id,
                    'disabled' => true,
                ));
                echo CHtml::label('Stock', $this->item_mixed_stock_label_id);
                echo "<p>" . $this->widget('application.widgets.USLink', array(
                    'id' => $this->item_mixed_stock_label_id,
                    'route' => '/logistic/merchandise/explainCommercialStock',
                    'label' => '0',
                    'title' => '¿Por qué veo este stock?',
                    'class' => 'form',
                    'data' => array(
                        'stock_mode' => $this->model->movement->stock_mode,
                        'emission_date' => $this->model->movement->emission_date,
                        'exclude_id' => $this->model->movement->kardex_unlock_exclude_id,
                    ),
                    'visible' => false
                        ), true) . "</p>";
                echo "</div>";
            }

            echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
            echo "<div class='row'>";
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo SIANForm::numberFieldNonActive('Cantidad', null, 1, array(
                'id' => $this->item_pres_quantity_id,
                'class' => 'us-double0 enterTab',
                'min' => 1
            ));
            echo "</div>";

            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo SIANForm::dropDownListNonActive('Pres.', null, null, [], array(
                'id' => $this->item_equivalence_id,
                'empty' => Strings::SELECT_OPTION,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->item_allow_decimals_id,
            ));
            echo "</div>";

            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo SIANForm::numberFieldNonActive("FOB Tot <span class='currency-symbol'></span>", null, 0, array(
                'id' => $this->item_tfob_id,
                'class' => "us-double{$this->ifixed} enterTab",
                'min' => 0,
                'step' => $this->istep,
            ));
            echo "</div>";

            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo SIANForm::numberFieldNonActive("Kilos", null, 0, array(
                'id' => $this->item_kilos_id,
                'class' => "us-double{$this->ifixed} enterTab",
                'min' => 0,
                'step' => $this->istep,
            ));
            echo "</div>";

            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo SIANForm::numberFieldNonActive("Flete <span class='currency-symbol'></span>", null, 0, array(
                'id' => $this->item_freight_id,
                'class' => "us-double{$this->ifixed} enterTab",
                'min' => 0,
                'step' => $this->istep,
            ));
            echo "</div>";

            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo SIANForm::numberFieldNonActive("Seguro <span class='currency-symbol'></span>", null, 0, array(
                'id' => $this->item_insurance_id,
                'class' => "us-double{$this->ifixed} enterTab",
                'min' => 0,
                'step' => $this->istep,
            ));
            echo "</div>";
            echo CHtml::hiddenField(null, '', array(
                'id' => $this->item_item_type_id,
            ));
            echo CHtml::hiddenField(null, '', array(
                'id' => $this->item_product_type_id,
            ));
            echo "</div>";
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-6 text-center'>";
            echo CHtml::label('Agregar', $this->add_button_id, []);
            echo "<br/>";
            $this->widget('application.widgets.USButtons', array(
                'buttons' => array(
                    array(
                        'id' => $this->add_button_id,
                        'context' => 'primary',
                        'icon' => 'fa fa-lg fa-plus white',
                        'size' => 'default',
                        'title' => 'Añadir'
                    )
                )
            ));
            echo "</div>";
            echo "</div>";

            echo "<hr>";
        }

        echo CHtml::tag('table', array(
            'id' => $this->id,
            'class' => 'table table-condensed table-hover',
            'data-readonly' => $this->readonly,
            'data-advanced' => $this->advanced ? 1 : 0,
            'data-currency' => $this->model->movement->currency,
            'data-exchange_rate' => $this->model->movement->exchange_rate,
            'data-igv_global' => $this->controller->getOrganization()->globalVar->igv,
            'data-perception_global' => $this->controller->getOrganization()->globalVar->perception,
            'data-direction' => $this->model->movement->scenario->direction,
            'data-summary_id' => $this->summary_id,
                ), $this->renderTable(), true);

        echo "<hr>";
        echo "<div id='{$this->summary_id}'>";
        echo "<div class='row'>";

        $this->widget('SIANLoadProducts', array(
            'id' => $this->controller->getServerId(),
            'presentationMode' => $this->presentationMode,
            'colums' => 2,
            'onLoadProduct' => "               
                var divObj = $('#{$this->id}');
                var main_table_id = divObj.data('main_table_id');
                var mainTableObj = $('#' + main_table_id);
                                
                if (window.productPks.findIndex(id => id == itemProduct['pk']) === -1) {

                    var amount = parseFloat(itemFile['amount'] ?? 1 );
                    var pres_quantity = itemFile['pres_quantity'] == null || itemFile['pres_quantity']  == undefined || itemFile['pres_quantity'] == 0? 1 : itemFile['pres_quantity'];
                    pres_quantity = itemProduct['allow_decimals'] === 1 ? parseFloat(pres_quantity) : Math.round(pres_quantity).toFixed();
                    var tfob = itemFile.tfob?? 0;
                    var kilos = itemFile.kilos?? 0;
                    var freight = itemFile.freight?? 0;
                    var insurance = itemFile.insurance?? 0;
                    
                    SIANImportAddItem('{$this->id}', itemProduct.product_id, itemProduct.item_type_id, itemProduct.product_type, itemProduct.product_name, itemProduct.unit_stock, itemProduct.mixed_stock, pres_quantity, itemProduct.presentationItems, itemProduct.equivalence, itemProduct.allow_decimals, tfob, kilos, freight, insurance, '', '', []);
                    window.productPks.push(itemProduct['pk']);
                    itemsCharged++;
                } 
            ",
            'onLoadedProducts' => "SIANImportUpdateAmounts('{$this->id}');",
        ));

        echo "<div class='col-lg-1 col-md-2 col-sm-2 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, 'ad_valorem', array(
            'id' => $this->ad_valorem_input_id,
            'class' => 'sian-import-ad-valorem',
            'onchange' => "SIANImportUpdateAmounts('{$this->id}');",
            'min' => 0,
            'step' => $this->istep,
        ));
        echo "</div>";
        echo "<div class='col-lg-1 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->form->dropDownListRow($this->model, 'round_mode', [
            PHP_ROUND_HALF_UP => 'Hacia arriba',
            PHP_ROUND_HALF_DOWN => 'Hacia abajo',
                ], [
            'id' => $this->round_mode_select_id,
            'label' => 'M.Redondeo',
            'readonly' => $this->readonly,
            'onchange' => $this->readonly ? "" : "
                    $('#{$this->id}').data('round_mode', this.value);
                    SIANImportUpdateAmounts('{$this->id}');
                "
        ]);
        echo $this->form->hiddenField($this->model, 'warehouse_id', [
            'id' => $this->warehouse_input_id,
        ]);
        echo "</div>";
        echo "<div class='col-lg-5 col-md-5 col-sm-6 col-xs-12'>";
        echo $this->form->textAreaRow($this->model->movement, 'observation', array(
            'id' => $this->observation_input_id,
            'rows' => 3,
            'maxlength' => 500,
            'required' => $this->model->movement->isObservationRequired()
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-3 col-xs-12'>";
        echo "<div class='well pull-right extended-summary ' style='padding: 0px !important;'>";
        echo "<table>";
        echo "<tr><td><b>Subtotal:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-import-total-net'></span></td></tr>";
        echo "<tr><td><b>IGV:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-import-total-igv'></span></td></tr>";
        echo "<tr><td><b>Total venta:</b></td><td style='text-align:right'><span class='currency-symbol'></span> <span class='sian-import-total-total'></span></td></tr>";
        echo "</table>";
        echo "<input type='hidden' class='sian-import-total-kilos' value='{$this->model->kilos}' name='CommercialMovement[kilos]' readonly>";
        echo "<input type='hidden' class='sian-import-total-include-igv' value=0 name='CommercialMovement[include_igv]' readonly>";
        echo "<input type='hidden' class='sian-import-total-force-igv' value=1 name='CommercialMovement[force_igv]' readonly>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

    private function renderTable() {
        $html = '';
        $html .= "<thead>";
        $html .= "<tr>";
        $html .= "<th width='" . ($this->advanced ? 2 : 2) . "%'>#</th>";
        $html .= "<th width='" . ($this->advanced ? 3 : 3) . "%'>ID</th>";
        $html .= "<th width='" . (($this->advanced ? 25 : 27) - ($this->model->movement->validate_stock == 1 ? 5 : 0)) . "%'>Nombre producto</th>";
        if ($this->model->movement->validate_stock) {
            $html .= "<th width='5%'>Stock.</th>";
        }

        $html .= "<th width='" . ($this->advanced ? 5 : 7) . "%'>Cant.</th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 7) . "%'>Pres.</th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 0) . "%' style='text-align:right; display:" . ($this->advanced ? 'table-cell' : 'none') . ";'>FOB Uni. <span class='currency-symbol'></span></th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 7) . "%' style='text-align:right;'>FOB Tot. <span class='currency-symbol'></span></th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 7) . "%' style='text-align:right;'>Kilos</th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 7) . "%' style='text-align:right;'>Flete <span class='currency-symbol'></span></th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 5) . "%' style='text-align:right;'>CFR <span class='currency-symbol'></span></th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 8) . "%' style='text-align:right;'>Seguro <span class='currency-symbol'></span></th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 5) . "%' style='text-align:right;'>Ad Val. <span class='currency-symbol'></span></th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 5) . "%' style='text-align:right;'>CIF <span class='currency-symbol'></span></th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 0) . "%' style='text-align:right; display:" . ($this->advanced ? 'table-cell' : 'none') . ";'>Otros <span class='currency-symbol'></span></th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 5) . "%' style='text-align:right;'>Cos. Tot. <span class='currency-symbol'></span></th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 0) . "%' style='text-align:right; display:" . ($this->advanced ? 'table-cell' : 'none') . ";'>Cost Uni. <span class='currency-symbol'></span></th>";
        $html .= "<th width='" . ($this->advanced ? 5 : 5) . "%'>Opc.</th>";
        $html .= "</tr>";
        $html .= "</thead>";
        $html .= "<tbody></tbody>";
        $html .= "<tfoot>";
        $html .= "<tr>";
        $html .= "<td colspan='" . ($this->advanced ? 6 : 5) . "' style='text-align:right'><strong>TOTALES: </strong></td>";
        $html .= "<td style='text-align:right;'><strong><span class='sian-import-total-tfob'></span></strong></td>";
        $html .= "<td style='text-align:right;'><strong><span class='sian-import-total-kilos'></span></strong></td>";
        $html .= "<td style='text-align:right;'><strong><span class='sian-import-total-freight'></span></strong></td>";
        $html .= "<td style='text-align:right;'><strong><span class='sian-import-total-cfr'></span></strong></td>";
        $html .= "<td style='text-align:right;'><strong><span class='sian-import-total-insurance'></span></strong></td>";
        $html .= "<td style='text-align:right;'><strong><span class='sian-import-total-ad-valorem'></span></strong></td>";
        $html .= "<td style='text-align:right;'><strong><span class='sian-import-total-cif'></span></strong></td>";
        $html .= "<td style='text-align:right; display:" . ($this->advanced ? 'table-cell' : 'none') . ";'><strong><span class='sian-import-total-other'></span></strong></td>";
        $html .= "<td style='text-align:right;'><strong><span class='sian-import-total-tcost'></span></strong></td>";
        $html .= "<td style='text-align:center; display:" . ($this->advanced ? 'table-cell' : 'none') . ";'></td>";
        $html .= "<td style='text-align:center; display:" . ($this->advanced ? 'table-cell' : 'none') . ";'></td>";
        $html .= "<td></td>";
        $html .= "</tr>";
        $html .= "</tfoot>";

        return $html;
    }

}
