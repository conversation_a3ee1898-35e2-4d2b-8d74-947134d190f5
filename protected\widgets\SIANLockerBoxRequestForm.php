<?php

class SIANLockerBoxRequestForm extends CWidget {

    //CONST
    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $locker_boxes = [];
    public $person_client_autocomplete_id;
    public $person_deliverer_autocomplete_id;
    public $max_accesses = 3;
    //PRIVATE
    private $locker_input_id;
    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->person_client_autocomplete_id = isset($this->person_client_autocomplete_id) ? $this->person_client_autocomplete_id : $this->controller->getServerId();
        $this->person_deliverer_autocomplete_id = isset($this->person_deliverer_autocomplete_id) ? $this->person_deliverer_autocomplete_id : $this->controller->getServerId();
        //
        $this->locker_input_id = $this->controller->getServerId();
        //
        SIANAssets::registerScriptFile('js/sian-locker-box-request-form.js');
        SIANAssets::registerScriptFile("js/us-kcfinder.js");
        //
        $a_articles = [];
        foreach ($this->model->tempArticles as $o_article) {

            $a_articles[] = [
                'article_code' => isset($o_article->article_code) ? $o_article->article_code : '',
                'article_description' => isset($o_article->article_description) ? $o_article->article_description : '',
                'allow_decimals' => isset($o_article->allow_decimals) ? $o_article->allow_decimals : 0,
                'quantity' => isset($o_article->quantity) ? $o_article->quantity : 1,
                'article_img' => isset($o_article->article_img) ? $o_article->article_img : '',
                'errors' => $o_article->getAllErrors()
            ];
        }

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        //Evento load porque se ejecuta cuando ya todo cargó
        $(document).ready(function() {
            var divObj = $('#{$this->id}');
            divObj.data('person_client_autocomplete_id', '{$this->person_client_autocomplete_id}');
            divObj.data('person_deliverer_autocomplete_id', '{$this->person_deliverer_autocomplete_id}');
            divObj.data('contact_url', '{$this->controller->createUrl('/widget/getContactForLockerRequest')}');
            divObj.data('max_accesses', {$this->max_accesses});
            divObj.data('media_url', '" . Yii::app()->params ['media_url'] . "');
            divObj.data('parent_id', '{$this->modal_id}');
            divObj.data('lock_allow_decimals', " . ($this->model->lock_allow_decimals == 1 ? 1 : 0) . ");
         
            //Llenamos
            SIANLockerBoxRequestFormInit('{$this->id}', " . CJSON::encode($a_articles) . ");
        });
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}'>";

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Datos principales',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id . '_basic'
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'box_number', $this->locker_boxes, array(
            'empty' => Strings::SELECT_OPTION,
            'required' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'status',
                ), true);
        echo "</div>";

        echo "</div>";

        $this->endWidget();

        echo "<div class='row sian-locker-request-articles'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Especificar artículos',
            'headerIcon' => 'user',
            'htmlOptions' => array(
                'id' => $this->id . '_article_panel'
            )
        ));
        echo $this->_renderArticleTable();
        $this->endWidget();
        echo "</div>";

        echo "</div>";
        //
        echo "</div>";
    }

    private function _renderArticleTable() {
        $s_html = "<table class='article_table items table table-hover table-condensed'>";
        $s_html .= "<thead>";
        $s_html .= "<tr>";
        $s_html .= "<th width='10%'><b>{$this->model->getAttributeLabel('lockerArticles.article_code')}</b></th>";
        $s_html .= "<th width='45%'><b>{$this->model->getAttributeLabel('lockerArticles.article_description')}</b></th>";
        $s_html .= "<th width='5%'><b>{$this->model->getAttributeLabel('lockerArticles.allow_decimals')}</b></th>";
        $s_html .= "<th width='10%'><b>{$this->model->getAttributeLabel('lockerArticles.quantity')}</b></th>";
        $s_html .= "<th width='25%'><b>{$this->model->getAttributeLabel('lockerArticles.article_img')}</b></th>";
        $s_html .= "<th width='5%'></th>";
        $s_html .= "</tr>";
        $s_html .= "</thead>";
        $s_html .= "<tbody>";
        $s_html .= "</tbody>";
        $s_html .= "<tfoot>";
        $s_html .= "<tr>";
        $s_html .= "<td colspan=99>";
        $s_html .= "<a onclick='SIANLockerBoxRequestFormAddArticleItem(\"{$this->id}\", \"\", \"\", 1, 0, \"\", [], true);' title='Agregar nuevo artículo'><span class='fa fa-plus fa-lg black'></span> Agregar nuevo</a>";
        $s_html .= "</td>";
        $s_html .= "</tr>";
        $s_html .= "</tfoot>";
        $s_html .= "</table>";

        return $s_html;
    }

}
