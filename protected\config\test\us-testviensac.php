<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'testviensac.siansystem.com/admin';
$domain = 'https://testviensac.siansystem.com';
$report_domain = 'rtest.siansystem.com';
$org = 'viensac';
$us = 'us_test';
$database_server = '161.132.48.88';
$database_name = 'viensac_test';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_EFACT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'viensac.p12';
$e_billing_certificate_pass = 'sQFu9AWnBzpSza7q';//PIN
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20481242836VIENSAC1',//usuario
        'password' => 'Conta147_'
    ],
    YII_OSE_EFACT => [
        'username' => '20481242836',
        'password' => '6ttM2Sx2po'
    ]
];
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_TESTING;
$environment = YII_ENVIRONMENT_TESTING;
