<?php

$arrayItemsNavbar = array(
    // array('icon' => 'inbox', 'label' => 'Caja', 'url' => '#', 'items' => array(
    array('label' => 'Caja', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'plus-sign', 'label' => 'Cobros de ventas', 'route' => 'saleCollect/index'),
            ),
            array(
                array('icon' => 'plus-sign', 'label' => 'Anticipos de clientes', 'route' => 'preCollect/index'),
                array('icon' => 'plus-sign', 'label' => 'Aplicación de movimientos de clientes', 'route' => 'clientApplication/index'),
            ),
            array(
                array('icon' => 'minus-sign', 'label' => 'Anticipos a proveedores/Entregas a rendir', 'route' => 'prePay/index'),
                array('icon' => 'minus-sign', 'label' => 'Aplicación de movimientos de proveedores', 'route' => 'providerApplication/index'),
            ),
            array(
                array('icon' => 'arrow-right', 'label' => 'Salidas por Transferencia', 'route' => 'transferenceOut/index'),
                array('icon' => 'arrow-left', 'label' => 'Ingresos por Transferencia', 'route' => 'transferenceIn/index'),
            ),
        )),
    // array('icon' => 'file', 'label' => 'Reportes', 'url' => '#', 'items' => array(
    array('label' => 'Reportes', 'url' => '#', 'items' => array(
            array('icon' => 'lock', 'label' => 'Cierre de Caja', 'route' => 'report/cashClose'),
            array('icon' => 'resize-small', 'label' => 'Consolidado de Ventas', 'route' => 'report/salesConsolidated'),
            array('icon' => 'list', 'label' => 'Anticipos a Proveedores/Entregas a Rendir', 'route' => 'report/applicationProviderEmployee'),
            array('icon' => 'list', 'label' => 'Anticipos de Clientes', 'route' => 'report/applicationClient'),
            array('icon' => 'list', 'label' => 'Notas de Crédito Proveedor', 'route' => 'report/creditNoteProvider'),
            array('icon' => 'list', 'label' => 'Notas de Crédito Cliente', 'route' => 'report/creditNoteClient'),
            array('icon' => 'list', 'label' => 'Transferencias de Efectivo', 'route' => 'report/cashTransfers'),
            array('icon' => 'eye-open', 'label' => 'Control de Operaciones', 'route' => 'report/operationsControl'),
        )),
);

$this->widget('application.widgets.SIANNavbar', array(
    'brand' => Yii::app()->id,
    'class' => 'navbar-treasury',
    'items' => $this->items_menu, // $arrayItemsNavbar,
));
