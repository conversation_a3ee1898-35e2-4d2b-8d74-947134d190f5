/**
 * Brazilian translation for bootstrap-datetimepicker
 * <PERSON><PERSON><PERSON> <<EMAIL>>
 */
;(function($){
	$.fn.datetimepicker.dates['pt-BR'] = {
        format: 'dd/mm/yyyy',
		days: ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sábad<PERSON>", "<PERSON>"],
		daysShort: ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sex", "Sáb", "Dom"],
		daysMin: ["<PERSON>", "Se", "<PERSON>", "<PERSON>u", "<PERSON>u", "Se", "Sa", "Do"],
		months: ["Janeiro", "Fever<PERSON>", "Março", "Abri<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jul<PERSON>", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"],
		monthsShort: ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"],
		today: "Ho<PERSON>",
		suffix: [],
		meridiem: []
	};
}(jQuery));
