<?php

//FOLDERS
$framework = 'framework';
$domain = '';
$domain2 = '';
$api_sian = 'http://apitesthardtech.siansystem.com/';
//
$hbs_url = 'http://hbs.siansystem.com/';
$hbs_username = 'jeanpaper';
$hbs_password = '576722';
//
$dir = dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . '..';
$admin = 'admin';
$org = '';
$media = 'us-media';
$us = 'us';
//REACT
//AUTOR
$author = 'Grupo Hardtech S.A.C.';
//DATABASE
$database_server = '';
$database_port = '3306';
$database_username = '';
$database_password = '';
$database_name = '';
$database_charset = 'utf8';
$database_sian = 'sian';
//MONGO
$mongo_enabled = false;
$mongo_server = '';
$mongo_port = '27017';
$mongo_db = '';
$mongo_username = '';
$mongo_password = '';
//ATD
$atd_enabled = true;
//
$ruc_source = YII_RUC_SOURCE_MIGO;
$dni_source = YII_DNI_SOURCE_MIGO;
$migo_token = 'DUGTbjslOBwTVtdz1ZGQlCwi3SqSqaYX0egcTmWhPdlR9sct1RM5pvpQ5ar7';
//REPORT
$rdomain = '';
$report_server = '216.158.235.190';
$report_domain = '';
$report_protocol = 'https';
$report_port = '8443';
//FORMATS
$datetime_php_format = 'd/m/Y h:i:s A';
$date_php_format = 'd/m/Y';
$time_php_format = 'h:i A';
$datetime_sql_format = 'Y-m-d H:i:s';
$date_sql_format = 'Y-m-d';
$date_js_format = 'dd/mm/yy';
//SESSION
$absolute_auth_timeout = 86400; //Tiempo para que se cierre la sesión, independientemente de la actividad. Un día
$auth_timeout = 86400; //Tiempo para que se cierre la sesión si está inactivo. Un día
//E-BILLING SUNAT
//https://www.sunat.gob.pe/ol-ti-itemision-otroscpe-gem/billService //Desde mayo 2018
//URLS: http://orientacion.sunat.gob.pe/index.php/empresas-menu/comprobantes-de-pago-empresas/comprobantes-de-pago-electronicos-empresas/see-desde-los-sistemas-del-contribuyente/guias-manuales-y-servicios-web
//Segun http://orientacion.sunat.gob.pe/index.php/empresas-menu/comprobantes-de-pago-empresas/comprobantes-de-pago-electronicos-empresas/see-desde-los-sistemas-del-contribuyente/988-guias-manuales-y-servicios-web
//Segun http://orientacion.sunat.gob.pe/index.php/empresas-menu/comprobantes-de-pago-empresas/comprobantes-de-pago-electronicos-empresas/see-desde-los-sistemas-del-contribuyente/2-comprobantes-que-se-pueden-emitir-desde-see-sistemas-del-contribuyente/comprobante-de-retencion-electronico-desde-sistemas-del-contribuyente/3649-rutas-para-pruebas-y-envios
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = '';
$e_billing_certificate_pass = '';
$e_billing_ri = '';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '',
        'password' => ''
    ]
];
$e_billing_rest_credentials = [
    YII_OSE_SUNAT => [
        'client_id ' => '',
        'client_secret' => ''
    ]
];
$e_billing_send_automatically_to_client = false;
//GII
$gii_password = 'Sist3m@s';
//SMTP
$smtp_host = 'smtp.gmail.com';
$smtp_port = 465;//465
$smtp_security = 'ssl';//ssl
$smtp_username = '';
$smtp_password = '';
$smtp_support_username = '<EMAIL>';
$smtp_support_password = 'Sist3m@s25HT';
//JWT
$jwt_secret = 'Sist3m@s';
$jwt_lifetime = 86400;
//PAYU
$payu_api_key = '4Vj8eK4rloUd272L48hsrarnUA';
$payu_api_login = 'pRRXKOl8ikMmt9u';
$payu_merchant_id = 508029;
$payu_account_id = 512323;
$payu_test = true;
//IZIPAY
$izipay_username = '********';
$izipay_password = 'testpassword_QSjgcE7TYnG1fe0IvCVQzxPAjzxvRAhUgNsNR7ZBflDBR';
$izipay_public_key = '********:testpublickey_BkSHYHsYhhTPLKII0OP1RsHothA7G57AyvSYPgcz77Xb7';
$izipay_sha256_key = 'OU9lo6SeklTKSHFIyavYRpul2p81JC9xtscBdNEylePFI';
//CALLMEBOT
$callmebot_api_key = '931221';
//BITLY
$bitly_access_token = '****************************************';
//MEDIA
$media_config = array(
    'default' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'division' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'faq' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'gallery' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'html' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'image' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'jobOffer' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'line' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'link' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'mark' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'merchandise' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'news' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'organization' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'combo' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'locker' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
    'webCampaing' => array(
        'thumb_width' => 100,
        'thumb_height' => 100,
        'medium_width' => 300,
        'medium_height' => 300,
        'max_width' => 2000,
        'max_height' => 2000,
    ),
);
//USERS
$max_users = 10000;
//GOOGLE APIS
$ga_server_key = 'AIzaSyB5b1GDQleWgM_I0wJgGIsRN5fb3NU9tIc';
$ga_browser_key = 'AIzaSyBIsFLammkh8NGTrxxDfUWSh-K7Sbi0XME';
$ga_disabled = false;

/* Entorno de Reportes en Producción = 10 */
/* Entorno de Reportes en Staging = 20 */
/* Entorno de Reportes en Testing  = 30 */
/* Entorno de Reportes en Local = 40 */

$environment_reports = YII_ENVIRONMENT_DEVELOPMENT;
$environment = YII_ENVIRONMENT_DEVELOPMENT;
$support_emails = array(
    'helpdesk' => '<EMAIL>',
);
$admin_emails = array(
    'Hardtech SIAN' => '<EMAIL>',
);
// API DE APOYO SIAN
$url_api_sian = 'https://api-test.siansystem.com/api/V1/';