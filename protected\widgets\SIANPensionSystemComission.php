<?php

class SIANPensionSystemComission extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $fixed = 2;
    public $step = 0.01;
    //PRIVATE
    private $controller;
    private $date_ini_input_id;
    private $date_end_input_id;
    private $contribution_input_id;
    private $insurance_tax_input_id;
    private $var_comission_input_id;
    private $mix_comission_input_id;
    private $max_remuneration_input_id;
    private $add_button_id;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->modal_id = isset($this->modal_id) ? $this->modal_id : $this->controller->getServerId();
        //PRIVATE
        $this->date_ini_input_id = $this->controller->getServerId();
        $this->date_end_input_id = $this->controller->getServerId();
        $this->insurance_tax_input_id = $this->controller->getServerId();
        $this->contribution_input_id = $this->controller->getServerId();
        $this->var_comission_input_id = $this->controller->getServerId();
        $this->mix_comission_input_id = $this->controller->getServerId();
        $this->max_remuneration_input_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();

        //ITEMS
        $items = [];

        foreach ($this->model->tempItems as $item) {
            $attributes = [];
            array_push($attributes, "date_ini:'{$item->date_ini}'");
            array_push($attributes, "date_end:'{$item->date_end}'");
            array_push($attributes, "contribution:{$item->contribution}");
            array_push($attributes, "insurance_tax:{$item->insurance_tax}");
            array_push($attributes, "var_comission:{$item->var_comission}");
            array_push($attributes, "mix_comission:{$item->mix_comission}");
            array_push($attributes, "max_remuneration:{$item->max_remuneration}");
            array_push($attributes, "errors:" . json_encode($item->getErrors()));
            array_push($items, '{' . implode(',', $attributes) . "}");
        }

        $items = '[' . implode(',', $items) . ']';

        //Assets
        SIANAssets::registerScriptFile('other/moment/moment.js');
        SIANAssets::registerScriptFile("other/jquery.datetimepicker/js/jquery.datetimepicker.js");
        SIANAssets::registerCssFile("other/jquery.datetimepicker/css/jquery.datetimepicker.css");
        //ITEMS

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        $(document).ready(function() {
            //FORMAT
            var date_format = '" . Yii::app()->params['date_php_format'] . "';
            $('#{$this->id}').data('date_format', date_format);
            //COUNT
            $('#{$this->id}').data('count', 0);
            //MODAL
            $('#{$this->id}').data('modal_id', '{$this->modal_id}');
            //MOVIMIENTOS
            var array = {$items};

            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {
                    SIANPensionSystemComissionAddItem('{$this->id}', moment(array[i]['date_ini'], 'DD/MM/YYYY'), moment(array[i]['date_end'], 'DD/MM/YYYY'), array[i]['contribution'], array[i]['insurance_tax'], array[i]['var_comission'], array[i]['mix_comission'], array[i]['max_remuneration'], array[i]['errors']);
                }
                $('#{$this->contribution_input_id}').val(array[array.length-1]['contribution']);
                $('#{$this->insurance_tax_input_id}').val(array[array.length-1]['insurance_tax']);
                $('#{$this->var_comission_input_id}').val(array[array.length-1]['var_comission']);
                $('#{$this->mix_comission_input_id}').val(array[array.length-1]['mix_comission']);
                $('#{$this->max_remuneration_input_id}').val(array[array.length-1]['max_remuneration']);
            }
            else
            {
                $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
            }
           
            SIANPensionSystemComissionUpdate('{$this->id}');
            unfocusable();
            
            //Para hacer reordenable las filas
            SIANPensionSystemComissionSortable('{$this->id}');
                
            $('#{$this->date_ini_input_id}').datetimepicker({
                lang: 'es',
                format: date_format,
                formatDate: date_format,
                timepicker: false,
                closeOnDateSelect: true,
                scrollMonth: false,
                scrollInput: false
            });
            
            $('#{$this->date_end_input_id}').datetimepicker({
                lang: 'es',
                format: date_format,
                formatDate: date_format,
                timepicker: false,
                closeOnDateSelect: true,
                scrollMonth: false,
                scrollInput: false
            });
     
            $('#{$this->insurance_tax_input_id}').focus();
        });
       
        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var date_ini = $('#{$this->date_ini_input_id}').val();
            var date_end = $('#{$this->date_end_input_id}').val();
            var contribution = $('#{$this->contribution_input_id}').floatVal({$this->fixed});
            var insurance_tax = $('#{$this->insurance_tax_input_id}').floatVal({$this->fixed});
            var var_comission = $('#{$this->var_comission_input_id}').floatVal({$this->fixed});
            var mix_comission = $('#{$this->mix_comission_input_id}').floatVal({$this->fixed});
            var max_remuneration = $('#{$this->max_remuneration_input_id}').floatVal({$this->fixed});

            if(date_ini && date_end)
            {                
                SIANPensionSystemComissionAddItem('{$this->id}', moment(date_ini, 'DD/MM/YYYY'), moment(date_end, 'DD/MM/YYYY'), contribution, insurance_tax, var_comission, mix_comission, max_remuneration, []); 
                
                SIANPensionSystemComissionUpdate('{$this->id}');
                unfocusable();
            }
            
            $('#{$this->contribution_input_id}').floatVal({$this->fixed}, 0);
            $('#{$this->insurance_tax_input_id}').floatVal({$this->fixed}, 0);
            $('#{$this->var_comission_input_id}').floatVal({$this->fixed}, 0);
            $('#{$this->mix_comission_input_id}').floatVal({$this->fixed}, 0);
            $('#{$this->max_remuneration_input_id}').floatVal({$this->fixed}, 0);
        });  
        
        ", CClientScript::POS_END);


        Yii::app()->clientScript->registerScript(get_class($this), "
        
        function SIANPensionSystemComissionSortable(table_id)
        {
            //Para hacer reordenable las filas
            $(function() {
                $('#' + table_id).find('tbody').sortable({
                    stop: function( event, ui ) {
                        SIANPensionSystemComissionUpdate(table_id);
                    }
                });
            });
        }
        
        function SIANPensionSystemComissionUpdate(table_id){
        
            var table = $('#' + table_id);
            
            var q = (table.find('tbody tr.sian-pension-system-comission-item')).length;
            table.find('tbody tr.sian-pension-system-comission-item').each(function(index) {
                var row = $(this);   
                row.find('span.sian-pension-system-comission-item-order').text(q-index);
            });
        }
        
        function SIANPensionSystemComissionAddItem(table_id, date_ini, date_end, contribution, insurance_tax, var_comission, mix_comission, max_remuneration, errors)
        {
            //TABLE
            var table = $('#' + table_id);
            var count = parseInt(table.data('count'));
            var modal_id = table.data('modal_id');
            var date_format = table.data('date_format');

            //ID
            var id = getLocalId();
            var date_ini_id = getLocalId();
            var date_end_id = getLocalId();
            var fixed = table.data('fixed');
            var step = table.data('step');

            //HTML
            var row = '';
            row += '<tr id=\'' + id + '\' class=\'sian-pension-system-comission-item us-sortable-item\'>';
            
            row += '<td class=\'form-group\'>';
            row += '<span class=\'sian-pension-system-comission-item-order\'></span>';
            row += '</td>';
            
            row += '<td class=\'form-group ' + (errors['date_ini'] ? 'has-error' : '') + '\'>';
            row += '<input id=\'' + date_ini_id + '\' type=\'text\' class=\'form-control us-datepicker sian-pension-system-comission-date-ini\' name=\'Item[' + id + '][date_ini]\' value=\'' + date_ini.format('DD/MM/YYYY') + '\'>';
            if (errors['date_ini'])
            {
                row += '<span class=\'help-block error\'>' + errors['date_ini'] + '</span>';
            }
            row += '</td>';
            
            row += '<td class=\'form-group ' + (errors['date_end'] ? 'has-error' : '') + '\'>';
            row += '<input id=\'' + date_end_id + '\' type=\'text\' class=\'form-control us-datepicker sian-pension-system-comission-date-ini\' name=\'Item[' + id + '][date_end]\' value=\'' + date_end.format('DD/MM/YYYY') + '\'>';
            if (errors['date_end'])
            {
                row += '<span class=\'help-block error\'>' + errors['date_end'] + '</span>';
            }
            row += '</td>';
            
            row += '<td class=\'form-group ' + (errors['contribution'] ? 'has-error' : '') + '\'>';
            row += '<input type=\'number\' class=\'form-control sian-pension-system-comission-contribution us-double' + fixed + '\' style=\'text-align:right;\' name=\'Item[' + id + '][contribution]\' value=\'' + contribution.toFixed(fixed) + '\' min=\'0\' step=\'' + step + '\'>';
            if (errors['contribution'])
            {
                row += '<span class=\'help-block error\'>' + errors['contribution'] + '</span>';
            }
            row += '</td>';

            row += '<td class=\'form-group ' + (errors['insurance_tax'] ? 'has-error' : '') + '\'>';
            row += '<input type=\'number\' class=\'form-control sian-pension-system-comission-insurance-tax us-double' + fixed + '\' style=\'text-align:right;\' name=\'Item[' + id + '][insurance_tax]\' value=\'' + insurance_tax.toFixed(fixed) + '\' min=\'0\' step=\'' + step + '\'>';
            if (errors['insurance_tax'])
            {
                row += '<span class=\'help-block error\'>' + errors['insurance_tax'] + '</span>';
            }
            row += '</td>';
            
            row += '<td class=\'form-group ' + (errors['var_comission'] ? 'has-error' : '') + '\'>';
            row += '<input type=\'number\' class=\'form-control sian-pension-system-comission-var-comission us-double' + fixed + '\' style=\'text-align:right;\' name=\'Item[' + id + '][var_comission]\' value=\'' + var_comission.toFixed(fixed) + '\' min=\'0\' step=\'' + step + '\'>';
            if (errors['var_comission'])
            {
                row += '<span class=\'help-block error\'>' + errors['var_comission'] + '</span>';
            }
            row += '</td>';
            
            row += '<td class=\'form-group ' + (errors['mix_comission'] ? 'has-error' : '') + '\'>';
            row += '<input type=\'number\' class=\'form-control sian-pension-system-comission-mix-comission us-double' + fixed + '\' style=\'text-align:right;\' name=\'Item[' + id + '][mix_comission]\' value=\'' + mix_comission.toFixed(fixed) + '\' min=\'0\' step=\'' + step + '\'>';
            if (errors['mix_comission'])
            {
                row += '<span class=\'help-block error\'>' + errors['mix_comission'] + '</span>';
            }
            row += '</td>';
            
            row += '<td class=\'form-group ' + (errors['max_remuneration'] ? 'has-error' : '') + '\'>';
            row += '<input type=\'number\' class=\'form-control sian-pension-system-comission-max-remuneration us-double' + fixed + '\' style=\'text-align:right;\' name=\'Item[' + id + '][max_remuneration]\' value=\'' + max_remuneration.toFixed(fixed) + '\' min=\'0\' step=\'' + step + '\'>';
            if (errors['max_remuneration'])
            {
                row += '<span class=\'help-block error\'>' + errors['max_remuneration'] + '</span>';
            }
            row += '</td>';
            
            row += '<td>';      
            row += '<a title=\'Borrar ítem\' onclick = \'SIANPensionSystemComissionRemoveItem(\"' + table_id + '\", \"' + id + '\", false)\'><span class=\'fa fa-lg fa-times black\'></span></a>';
            row += '</td>';
            
            row += '</tr>';
            
            //COUNT DE ITEMS
            if(count === 0)
            {
                table.find('tbody').html(row);
            }
            else
            {
                table.find('tbody').prepend(row);
            }
            
            table.data('count', count + 1);
            $('#' + date_ini_id).datetimepicker({
                lang: 'es',
                format: date_format,
                formatDate: date_format,
                timepicker: false,
                closeOnDateSelect: true,
                scrollMonth: false,
                scrollInput: false
            });
            $('#' + date_end_id).datetimepicker({
                lang: 'es',
                format: date_format,
                formatDate: date_format,
                timepicker: false,
                closeOnDateSelect: true,
                scrollMonth: false,
                scrollInput: false
            });
        }
        
        function SIANPensionSystemComissionRemoveItem(table_id, id, confirmation)
        {
            if(confirmation ? confirm('¿Está seguro de eliminar este ítem?') : true)
            {
                var table = $('#' + table_id);
                var count = parseInt(table.data('count'));

                $('#' + id).remove();


                if(count === 1)
                {
                    table.find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
                }

                //COUNT DE ITEMS
                table.data('count', count - 1);
                SIANPensionSystemComissionUpdate(table_id);
            }
        }
       
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array('title' => $this->model->getAttributeLabel('tempItems'),
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempItems') ? 'us-error' : ''
            )
        ));
        
        echo "<div class='row'>";
        //ROW 
        
        echo "<div class='col-lg-3 col-md-3 col-sm-12 col-xs-12'>";
        //SUB ROW
        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo SIANForm::textFieldNonActive($this->model->getAttributeLabel('items.date_ini'), null, SIANTime::formatDate(), array(
            'id' => $this->date_ini_input_id,
            'placeholder' => $this->model->getAttributeLabel('items.date_ini'),
            'class' => 'us-datepicker'
        ));
        echo "</div>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo SIANForm::textFieldNonActive($this->model->getAttributeLabel('items.date_end'), null, SIANTime::formatDate(), array(
            'id' => $this->date_end_input_id,
            'placeholder' => $this->model->getAttributeLabel('items.date_end'),
            'class' => 'us-datepicker'
        ));
        echo "</div>";       
        echo "</div>";
        //SUB ROW
        echo "</div>";
        
        
        echo "<div class='col-lg-9 col-md-9 col-sm-12 col-xs-12'>";
        //SUB ROW
        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo SIANForm::numberFieldNonActive($this->model->getAttributeLabel('items.contribution'), null, 0.00, array(
            'id' => $this->contribution_input_id,
            'min' => 0,
            'step' => $this->step,
            'class' => "us-double{$this->fixed}",
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo SIANForm::numberFieldNonActive($this->model->getAttributeLabel('items.insurance_tax'), null, 0.00, array(
            'id' => $this->insurance_tax_input_id,
            'min' => 0,
            'step' => $this->step,
            'class' => "us-double{$this->fixed}",
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo SIANForm::numberFieldNonActive($this->model->getAttributeLabel('items.var_comission'), null, 0.00, array(
            'id' => $this->var_comission_input_id,
            'min' => 0,
            'step' => $this->step,
            'class' => "us-double{$this->fixed}",
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo SIANForm::numberFieldNonActive($this->model->getAttributeLabel('items.mix_comission'), null, 0.00, array(
            'id' => $this->mix_comission_input_id,
            'min' => 0,
            'step' => $this->step,
            'class' => "us-double{$this->fixed}",
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo SIANForm::numberFieldNonActive($this->model->getAttributeLabel('items.max_remuneration'), null, 0.00, array(
            'id' => $this->max_remuneration_input_id,
            'min' => 0,
            'step' => $this->step,
            'class' => "us-double{$this->fixed}",
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo CHtml::label('Agregar', $this->add_button_id, []);
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_button_id,
            'block' => true,
            'context' => 'primary',
            'icon' => 'fa fa-lg fa-plus white',
            'size' => 'default',
            'title' => 'Añadir'
        ));
        echo "</div>";         
        echo "</div>";
        //SUB ROW
        echo "</div>";
        //ROW
        echo "</div>";
        
        echo "<hr>";
        echo "<table id='{$this->id}' class='table table-condensed table-hover' data-fixed={$this->fixed} data-step={$this->step}>";
        echo "<thead>";
        echo "<tr>";
        echo "<th width='2%'>#</th>";
        echo "<th width='10%'>{$this->model->getAttributeLabel('items.date_ini')}</th>";
        echo "<th width='10%'>{$this->model->getAttributeLabel('items.date_end')}</th>";
        echo "<th width='15%'>{$this->model->getAttributeLabel('items.contribution')}</th>";
        echo "<th width='15%'>{$this->model->getAttributeLabel('items.insurance_tax')}</th>";
        echo "<th width='15%'>{$this->model->getAttributeLabel('items.var_comission')}</th>";
        echo "<th width='15%'>{$this->model->getAttributeLabel('items.mix_comission')}</th>";
        echo "<th width='15%'>{$this->model->getAttributeLabel('items.max_remuneration')}</th>";
        echo "<th width='3%'>Opc.</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody></tbody>";
        echo "<tfoot>";
        echo "</tfoot>";
        echo "</table>";
        $this->endWidget();
    }

}
