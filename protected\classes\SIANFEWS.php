<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANFEWS
 *
 * <AUTHOR>
 */
class SIANFEWS {

    const WS_SEND_BILL = "sendBill";
    const WS_SEND_SUMMARY = "sendSummary";
    const WS_GET_STATUS = "getStatus";
    const WS_GET_STATUS_CDR = "getStatusCdr";
    const WS_SEND_GUIDE = "sendGuide";
    const WS_GET_STATUS_CDR_GUIDE = "getStatusCdrGuide";
    //Codigos de respuesta de SUNAT
    const CODE_WS_EXCEPTION = 'X';
    const CODE_WS_0 = '0'; //EXITO
    const CODE_WS_98 = '98'; //EN PROCESAMIENTO
    const CODE_WS_99 = '99'; //PROCESO CON ERRORES
    const CODE_WS_200 = '200'; //NO CONFORME

    /**
     * Añade las credenciales a los parámetros
     * @param array $p_a_parameters Array de parámetros
     */
    private static function addCredentials(&$p_a_parameters, $p_s_ose) {

        $a_credentials = Yii::app()->params['e_billing_credentials'];

        if (!isset($a_credentials[$p_s_ose])) {
            throw new Exception("No se encuentran credenciales para el OSE: '{$p_s_ose}'");
        }

        $p_a_parameters["Username"] = $a_credentials[$p_s_ose]['username'];
        $p_a_parameters["Password"] = $a_credentials[$p_s_ose]['password'];
    }

    /**
     * Obtiene la URL del servicio
     * @param string $p_s_ws Nombre del servicio
     * @param string $p_s_ose OSE
     * @return string URL del servicio
     */
    private static function getUrl($p_s_ws, $p_s_ose) {

        $s_env = Yii::app()->params['e_billing_env'];

        switch ($p_s_ose) {
            case YII_OSE_SUNAT:
                switch ($s_env) {
                    case YII_OSE_PROD:
                        switch ($p_s_ws) {
                            case self::WS_SEND_BILL:
                                return YII_OSE_SUNAT_SENDBILL_PROD;
                            case self::WS_SEND_SUMMARY:
                                return YII_OSE_SUNAT_SENDSUMMARY_PROD;
                            case self::WS_GET_STATUS:
                                return YII_OSE_SUNAT_GETSTATUS_PROD;
                            case self::WS_GET_STATUS_CDR:
                                return YII_OSE_SUNAT_GETSTATUSCDR_PROD;
                            case self::WS_SEND_GUIDE:
                                return YII_OSE_SUNAT_SENDGUIDE_PROD;
                            case self::WS_GET_STATUS_CDR_GUIDE:
                                return YII_OSE_SUNAT_GETSTATUSCDR_GUIDE_PROD;
                            default:
                                throw new Exception('Servicio WEB no soportado!');
                        }
                    case YII_OSE_BETA:
                        switch ($p_s_ws) {
                            case self::WS_SEND_BILL:
                                return YII_OSE_SUNAT_SENDBILL_BETA;
                            case self::WS_SEND_SUMMARY:
                                return YII_OSE_SUNAT_SENDSUMMARY_BETA;
                            case self::WS_GET_STATUS:
                                return YII_OSE_SUNAT_GETSTATUS_BETA;
                            case self::WS_GET_STATUS_CDR:
                                return YII_OSE_SUNAT_GETSTATUSCDR_BETA;
                            case self::WS_SEND_GUIDE:
                                return YII_OSE_SUNAT_SENDGUIDE_PROD;
                            case self::WS_GET_STATUS_CDR_GUIDE:
                                return YII_OSE_SUNAT_GETSTATUSCDR_GUIDE_PROD;
                            default:
                                throw new Exception('Servicio WEB no soportado!');
                        }
                }
            case YII_OSE_EFACT:
                switch ($s_env) {
                    case YII_OSE_PROD:
                        switch ($p_s_ws) {
                            case self::WS_SEND_BILL:
                                return YII_OSE_EFACT_SENDBILL_PROD;
                            case self::WS_SEND_SUMMARY:
                                return YII_OSE_EFACT_SENDSUMMARY_PROD;
                            case self::WS_GET_STATUS:
                                return YII_OSE_EFACT_GETSTATUS_PROD;
                            case self::WS_GET_STATUS_CDR:
                                return YII_OSE_EFACT_GETSTATUSCDR_PROD;
                            case self::WS_SEND_GUIDE:
                                return YII_OSE_SUNAT_SENDGUIDE_PROD;
                            case self::WS_GET_STATUS_CDR_GUIDE:
                                return YII_OSE_SUNAT_GETSTATUSCDR_GUIDE_PROD;
                            default:
                                throw new Exception('Servicio WEB no soportado!');
                        }
                    case YII_OSE_BETA:
                        switch ($p_s_ws) {
                            case self::WS_SEND_BILL:
                                return YII_OSE_EFACT_SENDBILL_BETA;
                            case self::WS_SEND_SUMMARY:
                                return YII_OSE_EFACT_SENDSUMMARY_BETA;
                            case self::WS_GET_STATUS:
                                return YII_OSE_EFACT_GETSTATUS_BETA;
                            case self::WS_GET_STATUS_CDR:
                                return YII_OSE_EFACT_GETSTATUSCDR_BETA;
                            case self::WS_SEND_GUIDE:
                                return YII_OSE_SUNAT_SENDGUIDE_PROD;
                            case self::WS_GET_STATUS_CDR_GUIDE:
                                return YII_OSE_SUNAT_GETSTATUSCDR_GUIDE_PROD;
                            default:
                                throw new Exception('Servicio WEB no soportado!');
                        }
                }
        }
    }

    /**
     * Obtiene la URL del servicio
     * @param string $p_s_ws Nombre del servicio
     * @param string $p_s_ose OSE
     * @return string URL del servicio
     */
    private static function getUrlGuide($p_s_ws, $p_s_ose, $a_parameters) {

        $s_pre_url = SIANFEWS::getUrl($p_s_ws, $p_s_ose);

        switch ($p_s_ws) {
            case self::WS_SEND_GUIDE:
                return $s_pre_url . $a_parameters['organization_identification_number'] . '-' . $a_parameters['document_sunat_code'] . '-' . $a_parameters['document_serie'] . '-' . $a_parameters['document_correlative'];
            case self::WS_GET_STATUS_CDR_GUIDE:
                return $s_pre_url . $a_parameters['reference'];
            default:
                throw new Exception('No se puede generar la url!');
        }
    }

    /**
     * Obtiene los datos de un mensaje de error
     * Ejemplos:
     * - "El comprobante fue informado previamente en una comunicacion de baja - Detalle: xxx.xxx.xxx value='ticket: 201802508068044 error: El comprobante F02T-00002284 fue rechazado con codigo de error: 2108'";
     * @param SoapFault $p_o_fault
     * @return array Data del mensaje
     */
    private static function getErrorData($p_o_fault) {

        switch (Yii::app()->params['e_billing_ose']) {
            case YII_OSE_SUNAT:
                //Capturamos los datos
                $s_message = USString::capture('/(.*) - Detalle/', $p_o_fault->getMessage());
                $s_reference = USString::capture('/ticket: ([\d]+) /', $p_o_fault->getMessage());
                $s_code = USString::capture('/soap-env:Client.([\d]+)/', $p_o_fault->faultcode);

                return [
                    'code' => $s_code !== false ? $s_code : self::CODE_WS_EXCEPTION,
                    'reference' => $s_reference,
                    'message' => $s_message !== false ? $s_message : $p_o_fault->getMessage()
                ];
            case YII_OSE_EFACT:
                //Capturamos los datos
                $s_message = isset($p_o_fault->detail) && isset($p_o_fault->detail->detail) ? $p_o_fault->detail->detail : $p_o_fault->getMessage();
                $s_reference = null;
                $s_code = isset($p_o_fault->faultstring) ? $p_o_fault->faultstring : self::CODE_WS_EXCEPTION;

                return [
                    'code' => $s_code,
                    'reference' => $s_reference,
                    'message' => $s_message
                ];
        }
    }

    /**
     * Ejecuta la llamada en SOAP
     * @param string $p_s_ws Nombre del WS
     * @param string $p_s_xml XML de envío
     * @param string $p_s_ose OSE
     * @param string $p_a_errorData Array de la data de respuesta
     * @return Object Respuesta o FALSE si falla
     */
    private static function callSoap($p_s_ws, $p_s_xml, $p_s_ose, &$p_a_errorData = null) {

        try {
            //Obtenemos URL
            $s_url = self::getUrl($p_s_ws, $p_s_ose);

            $options = array(
                'trace' => true,
                'location' => $s_url,
                'uri' => 'http://service.sunat.gob.pe',
                'soap_version' => SOAP_1_1
            );

            ini_set('max_execution_time', 600);
            ini_set('default_socket_timeout', 600);

            $oSOAP = new feedSoap(null, $options);
            $oSOAP->SoapClientCall($p_s_xml);
            return $oSOAP->__call($p_s_ws, [], []);
        } catch (SoapFault $fault) {
            //Guardamos log
            USLog::save($fault);
            $p_a_errorData = self::getErrorData($fault);
            return false;
        } catch (Exception $ex) {
            //Guardamos log
            USLog::save($ex);
            $p_a_errorData = [
                'code' => self::CODE_WS_EXCEPTION,
                'reference' => '',
                'message' => $ex->getMessage()
            ];
            return false;
        }
    }

    public static function getToken($p_a_data, $p_a_rest_data) {

        $a_data = [
            'grant_type' => 'password',
            'scope' => 'https://api-cpe.sunat.gob.pe'
        ];

        $a_final_data = array_merge($a_data, array_merge($p_a_data, $p_a_rest_data));

        $s_url = str_replace("client_id", $p_a_rest_data['client_id'], "https://api-seguridad.sunat.gob.pe/v1/clientessol/client_id/oauth2/token/");

        $o_sunat_api = new SIANSunatApi($s_url);
        $a_response = $o_sunat_api->call($a_final_data);

        if ($a_response['code'] == USREST::CODE_SUCCESS) {
            if (isset($a_response['data'])) {
                if (isset($a_response['data'] ['access_token'])) {
                    return $a_response['data'] ['access_token'];
                } else {
                    if (isset($a_response['data']['error_description'])) {
                        throw new Exception("No se pudo generar el token. " . $a_response['data']['error_description'] . json_encode($a_final_data) . $s_url);
                    } else {
                        throw new Exception("No se pudo generar el token. " . $a_response['message']);
                    }
                }
            } else {
                throw new Exception("No se pudo generar el token. " . $a_response['message']);
            }
        } else {
            throw new Exception("No se pudo generar el token. " . $a_response['message']);
        }
    }

    /**
     * Setea un valor para todos los items de data
     * @param array $p_a_response Array de responses
     * @param string $p_s_key Key
     * @param mixed $p_m_value Valor
     */
    private static function setForAll(&$p_a_response, $p_s_key, $p_m_value) {
        for ($i = 0; $i < count($p_a_response['data']); $i++) {
            $p_a_response['data'][$i][$p_s_key] = $p_m_value;
        }
    }

    /**
     * Normaliza la data
     * @param array $p_a_movement_ids ID de movimientos
     * @param string $p_s_code Código del WS
     * @param string $p_s_reference Referencis
     * @param string $p_s_ose OSE
     * @param string $p_s_response Respuesta del WS
     * @param string $p_s_basename Nombre del archivo
     * @return array Data
     */
    private static function normalizeData($p_a_movement_ids, $p_s_code, $p_s_reference, $p_s_ose, $p_s_response, $p_s_basename = null) {
        //Generamos data
        $a_data = [];
        foreach ($p_a_movement_ids as $i_movement_id) {
            $a_data[] = [
                'movement_id' => $i_movement_id,
                'reference' => $p_s_reference,
                'ose' => $p_s_ose,
                'response' => $p_s_response,
                'basename' => $p_s_basename,
            ];
        }
        //Obtenemos ruta
        return [
            'code' => $p_s_code,
            'message' => $p_s_response,
            'data' => $a_data,
        ];
    }

    /**
     * Valida las respuestas
     * https://llama.pe/listado-de-errores-manual-del-programador-factura-electronica-sunat
     * @param array $a_response Respuesta del WS
     */
    public static function validateResponse(array &$a_response) {

        $i_status = 0;

        if (!is_numeric($a_response['code']) || $a_response['code'] === false) {
            $i_status = SentMovement::STATUS_CONNECTION_ERROR;
        } else {
            $code = (int) $a_response['code'];
            if (in_array($code, [self::CODE_WS_0])) {
                $i_status = SentMovement::STATUS_ACCEPTED;
            } elseif (in_array($code, [self::CODE_WS_98])) {
                $i_status = SentMovement::STATUS_PENDING;
            } elseif (in_array($code, [self::CODE_WS_99])) {
                $i_status = SentMovement::STATUS_CONNECTION_ERROR;
            } elseif ($code >= 100 && $code <= 1999) {
                $i_status = SentMovement::STATUS_CONNECTION_ERROR;
            } elseif ($code >= 2000 && $code <= 3999) {//Códigos que generan rechazos
                $i_status = SentMovement::STATUS_REJECTED;
            } elseif ($code >= 4000) {//Aceptado pero con observaciones
                $i_status = SentMovement::STATUS_ACCEPTED;
            } else {
                $i_status = SentMovement::STATUS_CONNECTION_ERROR;
            }
        }

        self::setForAll($a_response, 'status', $i_status);
        return $i_status;
    }

    /**
     * Genera el XML que se enviará a SUNAT
     * @param string $p_s_ws Nombre del WS
     * @param array $p_a_parameters Parámetros de envío
     * @return string XML
     */
    private static function getSOAPEnvelopeXML($p_s_ws, $p_a_parameters) {
        if ($p_s_ws == self::WS_SEND_GUIDE) {
            $s_xml = '<?xml version="1.0" encoding="UTF-8"?><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.sunat.gob.pe" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">   
                    <soapenv:Header>
                    <wsse:Security>
                    <wsse:UsernameToken>
                        <wsse:Username>' . $p_a_parameters["Username"] . '</wsse:Username>
                        <wsse:Password>' . $p_a_parameters["Password"] . '</wsse:Password>
                    </wsse:UsernameToken>
                    </wsse:Security>
                    </soapenv:Header>
                    <soapenv:Body>
                    <ser:' . $p_s_ws . '>';
        } else {
            //Si no es getStatusCDR
            if ($p_s_ws != self::WS_GET_STATUS_CDR) {
                $s_xml = '<?xml version="1.0" encoding="UTF-8"?><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ser="http://service.sunat.gob.pe" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">   
                    <soapenv:Header>
                    <wsse:Security>
                    <wsse:UsernameToken>
                        <wsse:Username>' . $p_a_parameters["Username"] . '</wsse:Username>
                        <wsse:Password>' . $p_a_parameters["Password"] . '</wsse:Password>
                    </wsse:UsernameToken>
                    </wsse:Security>
                    </soapenv:Header>
                    <soapenv:Body>
                    <ser:' . $p_s_ws . '>';

                if ($p_s_ws == self::WS_GET_STATUS) {
                    $s_xml = $s_xml . '<ticket>' . $p_a_parameters['ticket'] . '</ticket>';
                } else {
                    $s_xml = $s_xml . '<fileName>' . $p_a_parameters['basename'] . SIANFE::ZIP_EXTENSION . '</fileName><contentFile>' . base64_encode($p_a_parameters['contentFile']) . '</contentFile>';
                }
                $s_xml = $s_xml . '</ser:' . $p_s_ws . '></soapenv:Body></soapenv:Envelope>';
            } else {
                $s_xml = '<?xml version="1.0" encoding="UTF-8"?>
                                <SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/"
                                xmlns:SOAP-ENC="http://schemas.xmlsoap.org/soap/encoding/"
                                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                                xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                                xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
                                <SOAP-ENV:Header xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope">
                                <wsse:Security>
                                <wsse:UsernameToken>
                                <wsse:Username>' . $p_a_parameters["Username"] . '</wsse:Username>
                                <wsse:Password>' . $p_a_parameters["Password"] . '</wsse:Password>
                                </wsse:UsernameToken>
                                </wsse:Security>
                                </SOAP-ENV:Header>
                                <SOAP-ENV:Body>
                                <m:getStatusCdr xmlns:m="http://service.sunat.gob.pe">
                                <rucComprobante>' . $p_a_parameters['organization_identification_number'] . '</rucComprobante>
                                <tipoComprobante>' . $p_a_parameters['document_sunat_code'] . '</tipoComprobante>
                                <serieComprobante>' . $p_a_parameters['document_serie'] . '</serieComprobante>
                                <numeroComprobante>' . $p_a_parameters['document_correlative'] . '</numeroComprobante>
                                </m:getStatusCdr>
                                </SOAP-ENV:Body>
                                </SOAP-ENV:Envelope>';
            }
        }
        return $s_xml;
    }

    /**
     * Envía un resumen
     * @param array $p_a_parameters Parámetros
     * @param string $p_s_ose OSE
     * @return array Resultados
     */
    public static function sendBill(array $p_a_parameters, $p_s_ose) {

        //Agregamos credenciales
        self::addCredentials($p_a_parameters, $p_s_ose);
        //Generamos XML de envío
        $s_soap_envelope_xml = self::getSOAPEnvelopeXML(self::WS_SEND_BILL, $p_a_parameters);
        $s_soap_envelope_xml = $s_soap_envelope_xml;
        //Ejecutamos llamada a SOAP
        $a_error_data = [];
        $o_response = self::callSoap(self::WS_SEND_BILL, $s_soap_envelope_xml, $p_s_ose, $a_error_data);
        //Verificamos
        if ($o_response !== false) {
            //Obtenemos ruta
            $s_path = SIANFE::getRInvoiceDir();
            //Creamos ZIP
            file_put_contents($s_path . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::ZIP_EXTENSION, base64_decode($o_response));
            //Extraemos
            USFile::extractOrReadZip($s_path . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::ZIP_EXTENSION, $s_path);
            //Obtenemos tamaño del archivo ZIP
            $i_filezise = filesize($s_path . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::ZIP_EXTENSION);
            //Comprobamos si hay data en el zip
            if ($i_filezise > 0) {
                //Generamos objeto XML
                $o_xml = new DOMDocument();
                $o_xml->load($s_path . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::XML_EXTENSION);
                //Normalizamos
                $s_code = $o_xml->getElementsByTagName('ResponseCode')->item(0)->nodeValue;
                $s_reference = $o_xml->getElementsByTagName('ReferenceID')->item(0)->nodeValue;
                $s_response = $o_xml->getElementsByTagName('Description')->item(0)->nodeValue;

                return self::normalizeData([$p_a_parameters['movement_id']], $s_code, $s_reference, $p_s_ose, $s_response, $p_a_parameters['basename']);
            } else {
                return self::normalizeData([$p_a_parameters['movement_id']], self::CODE_WS_98, '', $p_s_ose, "Se envió el documento pero no hubo CDR en la respuesta. Por favor comprobar el estado manualmente en la pestaña 'FE Documentos Declarados'.", $p_a_parameters['basename']);
            }
        } else {
            return self::normalizeData([$p_a_parameters['movement_id']], $a_error_data['code'], $a_error_data['reference'], $p_s_ose, $a_error_data['message'], $p_a_parameters['basename']);
        }
    }

    /**
     * Envía un resumen
     * @param array $p_a_parameters Parámetros
     * @param string $p_s_ose OSE
     * @return array Resultados
     */
    public static function sendGuide(array $p_a_parameters, $p_s_ose, $p_a_e_billing_credentials, $p_a_rest_credentials) {

        $s_url = SIANFEWS::getUrlGuide(self::WS_SEND_GUIDE, $p_s_ose, $p_a_parameters);

        $s_content_file = base64_encode($p_a_parameters['content_file']);

        $a_post_data = [
            'archivo' => [
                'nomArchivo' => $p_a_parameters['basename'] . SIANFE::ZIP_EXTENSION,
                'arcGreZip' => $s_content_file,
                'hashZip' => self::getHash($p_a_parameters['path'])
            ]
        ];

        $s_token = self::getToken($p_a_e_billing_credentials[$p_s_ose], $p_a_rest_credentials);
        $o_sunat_api = new SIANSunatApi($s_url, $s_token);
        $a_response = $o_sunat_api->call($a_post_data, 'JSON');
        //Verificamos        
        if ($a_response['code'] != 404) {
            return self::normalizeData([$p_a_parameters['movement_id']], self::CODE_WS_98, $a_response['data']['numTicket'], $p_s_ose, "Documento enviado. Consulte con el ticket n° " . $a_response['data']['numTicket'] . ".", $p_a_parameters['basename']);
        } else {
            return $a_response;
        }
    }

    private static function getHash($s_file_path) {
        // Abre el archivo en modo binario
        $file_handle = fopen($s_file_path, 'rb');
        // Inicializa el contexto del hash
        $hash_context = hash_init('sha256');
        // Lee el archivo en bloques y actualiza el contexto del hash
        while (!feof($file_handle)) {
            $buffer = fread($file_handle, 8192);
            hash_update($hash_context, $buffer);
        }
        // Finaliza el cálculo del hash
        $file_hash = hash_final($hash_context);
        // Cierra el archivo
        fclose($file_handle);
        return $file_hash;
    }

    /**
     * Envía un resumen
     * @param array $p_a_parameters Parámetros
     * @param string $p_s_ose OSE
     * @return array Resultados
     */
    public static function sendSummary(array $p_a_parameters, $p_s_ose) {
        //Agregamos credenciales
        self::addCredentials($p_a_parameters, $p_s_ose);
        //Generamos XML de envío
        $s_incoming_xml = self::getSOAPEnvelopeXML(self::WS_SEND_SUMMARY, $p_a_parameters);
        //Ejecutamos llamada a SOAP
        $a_error_data = [];
        $s_response = self::callSoap(self::WS_SEND_SUMMARY, $s_incoming_xml, $p_s_ose, $a_error_data);
        //Verificamos
        if ($s_response !== false) {
            //Normalizamos
            return self::normalizeData($p_a_parameters['movement_ids'], self::CODE_WS_98, $s_response, $p_s_ose, $s_response, $p_a_parameters['basename']);
        } else {
            return self::normalizeData($p_a_parameters['movement_ids'], $a_error_data['code'], $a_error_data['reference'], $p_s_ose, $a_error_data['message'], $p_a_parameters['basename']);
        }
    }

    /**
     * Consulta el estado de un resumen
     * @param array $p_a_parameters Parámetros
     * @param string $p_s_ose OSE
     * @return array Resultados
     */
    public static function getStatus(array $p_a_parameters, $p_s_ose) {

        //Agregamos credenciales
        self::addCredentials($p_a_parameters, $p_s_ose);
        //Generamos XML de envío
        $s_incoming_xml = self::getSOAPEnvelopeXML(self::WS_GET_STATUS, $p_a_parameters);
        //Ejecutamos llamada a SOAP
        $a_error_data = [];
        $o_response = self::callSoap(self::WS_GET_STATUS, $s_incoming_xml, $p_s_ose, $a_error_data);
        //Verificamos
        if ($o_response !== false) {
            //Verificamos si la respuesta es un objeto, en ocasiones es un string
            if (is_object($o_response)) {
                //Excepciones (no generan archivo)
                if ((int) $o_response->statusCode > self::CODE_WS_99) {
                    return self::normalizeData($p_a_parameters['movement_ids'], $o_response->statusCode, $p_a_parameters['ticket'], $p_s_ose, $o_response->content);
                }

                if (!in_array((int) $o_response->statusCode, [self::CODE_WS_98])) {
                    //Guardamos archivo
                    file_put_contents($p_a_parameters['path'] . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::ZIP_EXTENSION, base64_decode($o_response->content));
                    //Extraemos
                    $a_statIndexes = USFile::extractOrReadZip($p_a_parameters['path'] . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::ZIP_EXTENSION, $p_a_parameters['path'], [
                                USFile::ZIP_FILTER_EXTENSION => SIANFE::XML_EXTENSION
                    ]);

                    if ($a_statIndexes !== false && count($a_statIndexes) > 0) {

                        $o_xml = new DOMDocument();
                        $o_xml->load($p_a_parameters['path'] . $a_statIndexes[0]["name"]);

                        $s_code = $o_xml->getElementsByTagName('ResponseCode')->item(0)->nodeValue;
                        $s_reference = $o_xml->getElementsByTagName('ReferenceID')->item(0)->nodeValue;
                        $s_response = $o_xml->getElementsByTagName('Description')->item(0)->nodeValue;

                        if ($s_code != self::CODE_WS_200) {
                            return self::normalizeData($p_a_parameters['movement_ids'], $s_code, $s_reference, $p_s_ose, 'Ok - ' . $s_response, $p_a_parameters['basename']);
                        } else {
                            USLog::save([
                                'custom_parameters' => CJSON::encode($p_a_parameters),
                                'custom_request' => $s_incoming_xml,
                                'custom_response' => CJSON::encode($o_response)]);
                            return self::normalizeData($p_a_parameters['movement_ids'], $s_code, $s_reference, $p_s_ose, 'Código: ' . $s_code . ' Descripción: ' . $s_response . ' No se pudo procesar su solicitud. (Ocurrio un error en el batch).', $p_a_parameters['basename']);
                        }
                    } else {
                        USLog::save([
                            'custom_parameters' => CJSON::encode($p_a_parameters),
                            'custom_request' => $s_incoming_xml,
                            'custom_response' => CJSON::encode($o_response)]);
                        return self::normalizeData($p_a_parameters['movement_ids'], self::CODE_WS_98, $p_a_parameters['ticket'], $p_s_ose, 'La respuesta contiene un archivo ZIP vacío o corrupto. ' . ' ' . CJSON::encode($o_response));
                    }
                } else {
                    USLog::save([
                        'custom_parameters' => CJSON::encode($p_a_parameters),
                        'custom_request' => $s_incoming_xml,
                        'custom_response' => CJSON::encode($o_response)]);
                    return self::normalizeData($p_a_parameters['movement_ids'], $o_response->statusCode, $p_a_parameters['ticket'], $p_s_ose, $o_response->statusCode . ' - Archivo aún en procesamiento por parte de SUNAT. ' . ' ' . CJSON::encode($o_response));
                }
            } else {
                USLog::save([
                    'custom_parameters' => CJSON::encode($p_a_parameters),
                    'custom_request' => $s_incoming_xml,
                    'custom_response' => CJSON::encode($o_response)]);
                return self::normalizeData($p_a_parameters['movement_ids'], self::CODE_WS_EXCEPTION, $p_a_parameters['ticket'], $p_s_ose, 'No se puede interpretar la respuesta: ' . CJSON::encode($o_response));
            }
        } else {
            return self::normalizeData($p_a_parameters['movement_ids'], $a_error_data['code'], $a_error_data['reference'], $p_s_ose, 'Falso - ' . $a_error_data['message']);
        }
    }

    /**
     * Consulta el estado CDR de un resumen
     * @param array $p_a_parameters Parámetros
     * @param string $p_s_ose OSE
     * @return array Resultados
     */
    public static function getStatusCDR(array $p_a_parameters, $p_s_ose) {

        switch ($p_a_parameters['document_sunat_code']) {

            case Document::REMISSION_GUIDE:
            case Document::TRANSPORT_GUIDE:

                $s_url = SIANFEWS::getUrlGuide(self::WS_GET_STATUS_CDR_GUIDE, $p_s_ose, $p_a_parameters);

                $s_token = self::getToken($p_a_parameters['e_billing_credentials'][$p_s_ose], $p_a_parameters['e_billing_rest_credentials']);
                $o_sunat_api = new SIANSunatApi($s_url, $s_token);

                $a_response = $o_sunat_api->call(null, "", false);
                return self::normalizeCDRGuide($p_a_parameters, $p_s_ose, $a_response);
                break;
            default:
                //Agregamos credenciales
                self::addCredentials($p_a_parameters, $p_s_ose);
                //Generamos XML de envío
                $s_incoming_xml = self::getSOAPEnvelopeXML(self::WS_GET_STATUS_CDR, $p_a_parameters);
                //Ejecutamos llamada a SOAP
                $a_error_data = [];
                $m_response = self::callSoap(self::WS_GET_STATUS_CDR, $s_incoming_xml, $p_s_ose, $a_error_data);
                //Verificamos
                if ($m_response !== false) {
                    switch ($p_s_ose) {
                        case YII_OSE_SUNAT:
                            //Verificamos si hay contenido
                            if (isset($m_response->content)) {
                                return self::normalizeCDR($p_a_parameters, $p_s_ose, $m_response->content);
                            } else {
                                return self::normalizeData([$p_a_parameters['movement_id']], $m_response->statusCode, $p_a_parameters['reference'], $p_s_ose, $m_response->statusMessage, $p_a_parameters['basename']);
                            }
                            break;
                        case YII_OSE_EFACT:
                            return self::normalizeCDR($p_a_parameters, $p_s_ose, $m_response);
                    }
                } else {
                    return self::normalizeData([$p_a_parameters['movement_id']], self::CODE_WS_EXCEPTION, '', $p_s_ose, $a_error_data['message']);
                }
                break;
        }
    }

    private static function normalizeCDR($p_a_parameters, $p_s_ose, $p_s_content) {

        //Creamos el ZIP
        file_put_contents($p_a_parameters['path'] . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::ZIP_EXTENSION, base64_decode($p_s_content));
        //Extraemos
        USFile::extractOrReadZip($p_a_parameters['path'] . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::ZIP_EXTENSION, $p_a_parameters['path']);
        //Procesamos
        $xml = new DOMDocument();
        $xml->load($p_a_parameters['path'] . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::XML_EXTENSION);
        $s_code = $xml->getElementsByTagName('ResponseCode')->item(0)->nodeValue;
        $s_reference = $xml->getElementsByTagName('ReferenceID')->item(0)->nodeValue;
        $s_response = $xml->getElementsByTagName('Description')->item(0)->nodeValue;

        return self::normalizeData([$p_a_parameters['movement_id']], $s_code, $s_reference, $p_s_ose, $s_response, $p_a_parameters['basename']);
    }

    private static function normalizeCDRGuide($p_a_parameters, $p_s_ose, $p_a_response) {

        $s_code = self::CODE_WS_98;
        $s_description = "No se obtuvo respuesta del Api - Sunat.";
        $s_reference = null;

        if ($p_a_response['code'] == self::CODE_WS_200) {

            $a_data = $p_a_response['data'];

            if (isset($a_data['codRespuesta'])) {

                $s_code = $a_data['codRespuesta'];

                switch ($s_code) {
                    case self::CODE_WS_98:
                        $s_description = "Envío en proceso.";
                        $s_reference = $p_a_parameters['reference'];
                        break;

                    case self::CODE_WS_99:
                        $s_description = "Envío con error. ";
                        if (isset($a_data['error'])) {
                            $a_error = $a_data['error'];
                            $s_code = $a_error->numError;
                            $s_description = $s_description . $a_error->desError;
                        }
                        break;
                }
            }
            if (isset($a_data['indCdrGenerado']) && $a_data['indCdrGenerado'] == 1) {

                $s_content_cdr = base64_decode($a_data['arcCdr']);
                //Creamos el ZIP
                file_put_contents($p_a_parameters['path'] . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::ZIP_EXTENSION, $s_content_cdr);
                //Extraemos
                USFile::extractOrReadZip($p_a_parameters['path'] . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::ZIP_EXTENSION, $p_a_parameters['path']);

                //Procesamos
                $xml = new DOMDocument();
                $xml->load($p_a_parameters['path'] . SIANFE::RESPONSE_PREFFIX . $p_a_parameters['basename'] . SIANFE::XML_EXTENSION);
                $s_code = $xml->getElementsByTagName('ResponseCode')->item(0)->nodeValue;
                $s_reference = $xml->getElementsByTagName('ReferenceID')->item(0)->nodeValue;
                $s_description = $xml->getElementsByTagName('Description')->item(0)->nodeValue;
            }
            return self::normalizeData([$p_a_parameters['movement_id']], $s_code, $s_reference, $p_s_ose, $s_description, $p_a_parameters['basename']);
        }
    }

}
