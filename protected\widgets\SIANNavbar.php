<?php

class SIANNavbar extends CWidget {

    public $id;
    public $class;
    public $items = [];
    public $itemsUserMenu = array();
    public $headerNameModule = array();
    public $brand;
    //PRIVATE
    private $controller;
    private $header = null;
    private $currentUrl = null;
    private $module = null;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->module = $this->controller->module->id;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->currentUrl = $this->controller->createUrl("/{$this->controller->route}");

        //Registramos assets
        SIANAssets::registerCssFile('css/sian-navbar.css');
        SIANAssets::registerScriptFile('js/sian-navbar.js');
    }

    public function checkItems($items, &$p_b_has_active = null) {

        $normalized_items = [];

        for ($i = 0; $i < count($items); $i++) {

            if (USArray::isAssoc($items[$i])) {
                $items[$i] = array($items[$i]);
            } else {
                if ($i > 0) {
                    $count = count($normalized_items);
                    if ($count > 0 && $normalized_items[$count - 1] != '---') {
                        $normalized_items[] = '---';
                    }
                }
            }

            foreach ($items[$i] as $item) {

                $visible = false;

                if (is_array($item)) {
                    $normalized_item = [];
                    $normalized_item['icon'] = isset($item['icon']) ? $item['icon'] : null;
                    $normalized_item['label'] = isset($item['label']) ? $item['label'] : null;

                    //Hay una url
                    if (isset($item['url'])) {
                        $normalized_item['url'] = $item['url'];
                        $normalized_item['linkOptions'] = array('target' => '_blank');
                        $visible = true;
                    } else {
                        $normalized_item['route'] = $item['route'];
                        $normalized_item['params'] = isset($item['params']) ? $item['params'] : [];

                        //La url es un array
                        $visible = $this->controller->checkRoute($normalized_item['route']);
                        if ($visible) {
                            $s_url = $this->controller->createUrl($normalized_item['route'], $normalized_item['params']);
                            //Vemos si es la URL actual
                            if ($s_url === $this->currentUrl) {
                                $this->_setNotify($normalized_item);
                                $p_b_has_active = true;
                            } else {
                                $normalized_item['url'] = $s_url;
                            }
                        }
                    }

                    if ($visible && isset($this->header)) {
                        $normalized_items[] = $this->header;
                        $this->header = null;
                    }

                    $normalized_item['items'] = [];
                    if (isset($item['items'])) {
                        $b_has_active = false;
                        $normalized_item['items'] = $this->checkItems($item['items'], $b_has_active);
                        if ($b_has_active) {
                            $normalized_item['active'] = true;
                        }
                    }

                    //Items
                    if ($visible || count($normalized_item['items']) > 0) {
                        $normalized_items[] = $normalized_item;
                    }
                } else {
                    $this->header = array('label' => $item);
                }
            }
        }

        //Eliminamos un --- al final si lo hubiera
        $count = count($normalized_items);

        if ($count > 0 && $normalized_items[$count - 1] == '---') {
            array_pop($normalized_items);
        }

        return $normalized_items;
    }

    /**
     * Runs the widget.
     */
    public function run() {
        //  $itemsUserMenu =  array_push($items, array('icon' => 'user', 'encodeLabel' => false,
        $this->itemsUserMenu = array(
            'encodeLabel' => false,
            'label' => $this->getNameUser(),
            'url' => Strings::LINK_TEXT, 'visible' => !Yii::app()->user->isGuest,
            'logo' => Yii::app()->params['admin_url'] . '/images/icons/icon-person.svg',
            //'logo' => Yii::app()->params['admin_url'].'/images/profiles/profile-default.svg',
            'items' => array(
                array('icon' => 'user', 'label' => 'Mi Perfil', 'url' => Strings::LINK_TEXT, 'linkOptions' =>
                    array('class' => 'form', 'url' => $this->controller->createUrl('/user/myprofile'), 'data-modal_id' => $this->controller->getServerId(), 'data-type' => 'message')
                ),
                array('icon' => 'list', 'label' => 'Editar perfil', 'url' => Strings::LINK_TEXT, 'linkOptions' =>
                    array('class' => 'form', 'url' => $this->controller->createUrl('/user/profile'), 'data-modal_id' => $this->controller->getServerId(), 'data-type' => 'message')
                ),
                array('icon' => 'lock', 'label' => 'Cambiar contraseña', 'url' => Strings::LINK_TEXT, 'linkOptions' =>
                    array('class' => 'form', 'url' => $this->controller->createUrl('/user/password'), 'data-modal_id' => $this->controller->getServerId(), 'data-type' => 'message')
                ),
                array('icon' => 'pencil', 'label' => 'Crear firma', 'url' => Strings::LINK_TEXT, 'linkOptions' =>
                    array('class' => 'form', 'url' => $this->controller->createUrl('/user/sign'), 'data-modal_id' => $this->controller->getServerId(), 'data-type' => 'message')
                ),
                array('icon' => 'log-out', 'label' => 'Salir', 'url' => Strings::LINK_TEXT, 'linkOptions' =>
                    array('submit' => array('/site/logout'), 'confirm' => '¿Seguro que desea salir?')
                ),
            )
        );

        $items = $this->checkItems($this->items);

        $this->headerNameModule = array('label' => $this->controller->module_title, 'url' => array("/{$this->controller->module->id}/default/index"));

        array_unshift($items, $this->headerNameModule);

        array_push($items, $this->itemsUserMenu);

        $logoFolder = Yii::app()->params['org_dir'] . '/images/logos';
        $logoFullName = 'logo.png';
        $logoRoute = Yii::app()->params['org_url'] . '/images/logos';
        $logoFullRoute = $logoRoute . '/' . $logoFullName;
        $logoSVGroute = '';

        if (is_dir($logoFolder)) {
            if ($openDirectory = opendir($logoFolder)) {
                // Recorre todos los elementos del directorio
                while (($file = readdir($openDirectory)) !== false) {
                    $fullPath = $logoFolder . "/" . $file;
                    // Se muestran todos los archivos y carpetas excepto "." y ".."
                    if ($file != "." && $file != "..") {
                        // Si es un directorio se recorre recursivamente
                        if (!is_dir($fullPath)) {
                            $infoFile = pathinfo($file);
                            if ($infoFile['filename'] == 'logo') {
                                $logoFullRoute = $logoRoute . "/" . $file;
                                if ($infoFile['extension'] == 'svg') {
                                    $logoSVGroute = $logoRoute . "/" . $file;
                                }
                            }
                        }
                    }
                }
                closedir($openDirectory);
            }
        }

        if (!empty($logoSVGroute)) {
            $logoFullRoute = $logoSVGroute;
        }

        $this->brand = '<img class="header-logo-org" src="' . $logoFullRoute . '" alt="logo">';

        $this->widget('booster.widgets.TbNavbar', array(
            'brand' => "{$this->brand}",
            'fixed' => 'top',
            'htmlOptions' => ['class' => $this->class . isset($this->module) ? ' navbar-' . $this->module : ""],
            'items' => array(
                array(
                    'class' => 'booster.widgets.TbMenu',
                    'type' => 'navbar',
                    'items' => $items,
                    'firstItemCssClass' => 'sian-navbar-first',
                ),
            ),
            'itemsUserMenu' => $this->itemsUserMenu,
            'headerNameModule' => $this->headerNameModule,
        ));

        return $this->itemsUserMenu;
    }

    private function _setNotify(&$p_a_normalized_item) {
        $p_a_normalized_item['encodeLabel'] = false;
        $p_a_normalized_item['url'] = 'javascript:void(0)';
        $p_a_normalized_item['active'] = true;
        if (!isset($p_a_normalized_item['linkOptions'])) {
            $p_a_normalized_item['linkOptions'] = [];
        }
        if (!isset($p_a_normalized_item['linkOptions']['onclick'])) {
            $p_a_normalized_item['linkOptions']['onclick'] = "";
        }
        $p_a_normalized_item['linkOptions']['onclick'] .= "
                                        $.notify('Está aquí actualmente. Recargue la página si desea', {
                                            className: 'info',
                                            showDuration: 30,
                                            hideDuration: 50,
                                            autoHideDelay: 3000,
                                        }); ";
    }

    public function getNameUser() {
        $fullName = isset(Yii::app()->user->firstname) ? Yii::app()->user->firstname : Yii::app()->user->person_name;
        $fullNameArray = explode(" ", $fullName);
        $firstName = ucwords(strtolower($fullNameArray[0]));
        return $firstName;
    }

}
