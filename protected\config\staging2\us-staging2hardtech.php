<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'staging2hardtech.siansystem.com/admin';
$domain = 'https://staging2hardtech.siansystem.com';
$report_domain = 'rstaging2.siansystem.com';
$org = 'hardtech';
$us = 'us_staging';

//SIAN 2
$domain2 = 'https://staging2supermercadosmia.siansystem.com';
$domain_react_app = 'http://staging2supermercadosmia2.siansystem.com';
$api_sian = 'http://api-staging2.siansystem.com/';

$database_server = '161.132.48.88';
$database_name = 'hardtech_test';
$database_username = 'sian_test';
$database_password = '75nppt6vr57lx4';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
$database_sian = 'sian_staging';
//
$e_billing_ose = YII_OSE_EFACT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'hardtech.p12';
$e_billing_certificate_pass = 'spE2iKheAzgix7y2';
$e_billing_ri = '0620050000126';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20481066094BILLING7',
        'password' => 'EXITO123'
    ],
    YII_OSE_EFACT => [
        'username' => '20481066094',
        'password' => 'CzUKk08RZ3'
    ]
];
//IZIPAY
$izipay_username = '77869192';
$izipay_password = 'testpassword_QSjgcE7TYnG1fe0IvCVQzxPAjzxvRAhUgNsNR7ZBflDBR';
$izipay_public_key = '77869192:testpublickey_BkSHYHsYhhTPLKII0OP1RsHothA7G57AyvSYPgcz77Xb7';
$izipay_sha256_key = 'OU9lo6SeklTKSHFIyavYRpul2p81JC9xtscBdNEylePFI';
//
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_STAGING;
$environment = YII_ENVIRONMENT_TESTING;
