<?php

$a_order_items = [];
if (Yii::app()->controller->getOrganization()->globalVar->allow_dispatch_purchase_order != GlobalVar::ALLOW_DISPATCH_ORDER_NO) {
    $a_order_items[] = array('icon' => 'user', 'label' => 'Orden de Ingreso', 'route' => 'buyInOrder/index');
}
if (Yii::app()->controller->getOrganization()->globalVar->allow_dispatch_sale_order != GlobalVar::ALLOW_DISPATCH_ORDER_NO) {
    $a_order_items[] = array('icon' => 'user', 'label' => 'Orden de Despacho', 'route' => 'saleOutOrder/index');
}
$a_order_items[] = array('icon' => 'user', 'label' => 'Orden de consumo', 'route' => 'domesticUseOrder/index');
if (Yii::app()->controller->getOrganization()->globalVar->use_transference_order == 1) {
    $a_order_items[] = array('icon' => 'user', 'label' => 'Orden de transferencia', 'route' => 'transferenceOrder/index');
}
$a_order_items[] = array('icon' => 'user', 'label' => 'Orden de transformación', 'route' => 'transformationOrder/index');
if (Yii::app()->controller->getOrganization()->globalVar->use_products_related == 1) {
    $a_order_items[] = array('icon' => 'user', 'label' => 'Orden de transformación de relacionados', 'route' => 'transforOrderRelated/index');
}

$arrayItemsNavbar = array(
    array('label' => 'Datos Generales', 'url' => '#', 'items' => array(
            array('icon' => 'list', 'label' => 'Series', 'route' => 'serie/index'),
            array('icon' => 'list', 'label' => 'Plantillas de Transformacion', 'route' => 'transformTemplate/index'),
        )),
    array('label' => 'Órdenes y Guía', 'url' => '#', 'items' => array(
            $a_order_items,
            array(
                array('icon' => 'user', 'label' => 'Orden de consumo en proyecto', 'route' => 'civilWorkUseOrder/index'),
                array('icon' => 'user', 'label' => 'Orden de retorno de consumo', 'route' => 'civilWorkReturnOrder/index'),
            ),
            array(
                array('icon' => 'user', 'label' => 'Guías - otros motivos', 'route' => 'guide/index'),
            ),
        )),
    array('label' => 'Movimientos', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'gift', 'label' => 'Ingresos por compra', 'route' => 'buyIn/index'),
                array('icon' => 'repeat', 'label' => 'Ingresos por devolución', 'route' => 'productReturnIn/index'),
                array('icon' => 'plus-sign', 'label' => 'Otros ingresos', 'route' => 'adjustIn/index'),
            ),
            array(
                array('icon' => 'share-alt', 'label' => 'Despachos', 'route' => 'saleOut/index'),
                array('icon' => 'user', 'label' => 'Salida por consumo interno', 'route' => 'domesticUse/index'),
                array('icon' => 'repeat', 'label' => 'Salidas por devolución', 'route' => 'productReturnOut/index'),
                array('icon' => 'minus-sign', 'label' => 'Otras salidas', 'route' => 'adjustOut/index'),
            ),
            array(
                array('icon' => 'arrow-up', 'label' => 'Salidas por transferencia', 'route' => 'transferenceOut/index'),
                array('icon' => 'arrow-down', 'label' => 'Ingresos por transferencia', 'route' => 'transferenceIn/index'),
            ),
            array(
                array('icon' => 'minus-sign', 'label' => 'Salidas por transformación', 'route' => 'transformationOut/index'),
                array('icon' => 'plus-sign', 'label' => 'Ingresos por transformación', 'route' => 'transformationIn/index'),
            ),
            array(
                array('icon' => 'minus-sign', 'label' => 'Salidas por consumo en proyecto', 'route' => 'civilWorkUse/index'),
                array('icon' => 'plus-sign', 'label' => 'Ingreso por devolución de consumo', 'route' => 'civilWorkReturnIn/index'),
            ),
            array(
                array('icon' => 'tags', 'label' => 'Ingresos pendientes', 'route' => 'toReceive/index'),
                array('icon' => 'shopping-cart', 'label' => 'Salidas pendientes', 'route' => 'toDispatch/index'),
            )
        )),
    array('label' => 'Otros', 'url' => '#', 'items' => array(
            array('icon' => 'th-large', 'label' => 'Solicitudes de locker', 'route' => 'lockerRequest/index'),
        )),
    array('label' => 'Inventarios', 'url' => '#', 'items' => array(
            array('icon' => 'list', 'label' => 'Causas raíz', 'route' => 'inventoryReason/index'),
            array('icon' => 'list', 'label' => 'Inventario', 'route' => 'inventory/index'),
        )),
    array('label' => 'Reportes', 'url' => '#', 'items' => array(
            array('icon' => 'arrow-right', 'label' => 'Despachos', 'route' => 'report/saleOut'),
            array('icon' => 'arrow-left', 'label' => 'Ingresos', 'route' => 'report/warehouseIn'),
            array('icon' => 'list', 'label' => 'Órdenes', 'route' => 'report/orders'),
            array('icon' => 'arrow-right', 'label' => 'Salidas Por Consumo Interno', 'route' => 'report/domesticUse'),
            array('icon' => 'list', 'label' => 'Stock de Productos Por Almacén', 'route' => 'report/balanceByWarehouse'),
            array('icon' => 'list', 'label' => 'Productos Entregados Por Consignación', 'route' => 'report/balanceConsignment'),
            array('icon' => 'search', 'label' => 'Búsqueda por serie', 'route' => 'report/serieSearch'),
            array('icon' => 'search', 'label' => 'Delivery', 'route' => 'report/delivery'),
            array('icon' => 'list', 'label' => 'Consumo en obra por Centro de costos', 'route' => 'report/civilWorkUseProducts'),
            array('icon' => 'eye-open', 'label' => 'Control de Operaciones', 'route' => 'report/operationsControl'),
            array(
                array('icon' => 'list', 'label' => 'Detalle del Inventario', 'route' => 'report/inventoryDetail'),
                array('icon' => 'book', 'label' => 'Causas Raíz del Inventario', 'route' => 'report/rootCause'),
                array('icon' => 'fa fa-money', 'label' => 'Variación del costo de producto', 'route' => 'report/productCostVariance'),
            )
        )),
);

$this->widget('application.widgets.SIANNavbar', array(
    'brand' => Yii::app()->id,
    'class' => 'navbar-warehouse',
    'items' => $this->items_menu, //$arrayItemsNavbar,
));
