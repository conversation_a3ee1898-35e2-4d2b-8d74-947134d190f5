<?php

class SiteController extends SIANController {

    public function accessRules() {
        return array(
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('index', 'updateRestoreId', 'logout', 'sendLog', 'afterLogin'),
                'users' => array('@'),
            ),
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('login', 'error'),
                'users' => array('*'),
            ),
            array('deny', // deny all users
                'users' => array('*'),
            ),
        );
    }

    public function actionIndex() {

        $this->layout = '//layouts/index';

        //Verificamos si hay solicitudes de aprobación pendientes
        $a_pendingRoutes = Confirmation::getPendingRoutes();
        $a_groups_you_manage = array_keys(Yii::app()->user->getState('groups_you_manage'));
        $a_groups_you_manage [] = 0;
        $b_there_are_pending = false;
        foreach ($a_pendingRoutes as $a_pendingRoute) {
            //Chequeamos si tiene acceso para evaluar esta ruta
            if ($this->checkConfirmation($a_pendingRoute['route'], $a_pendingRoute['option_type'])) {
                if (in_array($a_pendingRoute['confirm_by'], $a_groups_you_manage) || Yii::app()->user->getState('level') == User::LEVEL_ROOT) {
                    $b_there_are_pending = true;
                    //Si tiene acceso a al menos salimos del bucle
                    break;
                }
            }
        }

        if ($b_there_are_pending) {
            Yii::app()->user->setFlash('warning', Strings::PENDING_REQUESTS . ' ' . CHtml::link('aquí', $this->createUrl('/administration/confirmation/index')));
        }

        if (Yii::app()->user->level == User::LEVEL_ROOT) {
            $a_email_queue = EmailQueue::model()->findAllByAttributes(['state' => EmailQueue::STATE_PENDIENTE]);
            if (count($a_email_queue) > 0) {
                Yii::app()->user->setFlash('error', "Hay " . count($a_email_queue) . " mensaje(s)de correo pendiente(s) de envío.");
            }
        }
//Por ahora comentado porque se trabaja las múltiples precios
//        //Verificamos si hay productos con precio desactualizado.
//        if ($this->checkRoute('/logistic/merchandise/prices')) {
//            $i = (new DpLogisticDefaultIndexTab1())->count();
//            if ($i > 0) {
//                Yii::app()->user->setFlash('error', Strings::PENDING_MERCHANDISE_PRICES . ' ' . CHtml::link('aquí', $this->createUrl('/logistic/default/index')));
//            }
//        }
        //Verificamos si hay Motivos de Inventarios por validar
        $a_pending_inventory_reasons = Inventory::getPendingInventoryReasons();
        if (count($a_pending_inventory_reasons) > 0) {
            if ($this->checkRoute('/warehouse/inventoryDetail/validate')) {
                Yii::app()->user->setFlash('warning', Strings::PENDING_REQUESTS_REASON_INVENTORY . ' ' . CHtml::link('aquí', $this->createUrl('/warehouse/inventory/index', ['DpWarehouseInventoryIndex' => ['state' => Inventory::STATE_IN_ANALYSIS, 'reason_confirm' => 0]])));
            }
        }

        //Verificamos si hay ajustes de Inventarios por validar
        $a_pending_inventory_reasons = Inventory::getPendingInventoryAdjust();
        if (count($a_pending_inventory_reasons) > 0) {
            if ($this->checkRoute('/warehouse/inventory/adjust')) {
                Yii::app()->user->setFlash('warning', Strings::PENDING_REQUESTS_ADJUST_INVENTORY . ' ' . CHtml::link('aquí', $this->createUrl('/warehouse/inventory/index', ['DpWarehouseInventoryIndex' => ['state' => Inventory::STATE_IN_ADJUST]])));
            }
        }

        $this->render('index', array('data' => array(
                'modules' => (new SpGetMenu())->setParams(array(
                    'xmodule' => '',
                    'xusername' => '',
                    'xdetail' => 0,
                    'xversion' => 1
                ))->findAll(),
            )
        ));
    }

//    public function actionLogin($redirect = null) {
//
//        //Si ya está logueado, direccionamos a index
//        if (!Yii::app()->user->isGuest) {
//            $this->redirect('index');
//        }
//
//        $this->layout = '//layouts/login';
//        $model = new LoginForm;
//
//        if (isset($_POST['ajax']) && $_POST['ajax'] === 'login-form') {
//            echo CActiveForm::validate($model);
//            Yii::app()->end();
//        }
//
//        if (isset($_POST['LoginForm'])) {
//
//            $model->attributes = $_POST['LoginForm'];
//
//            if ($model->validate() && $model->login()) {
//                //Si hay parámetro para URL
//                if (isset($redirect)) {
//                    $s_url = SIANCache::get($redirect);
//                    //Si hay URL reireccionamos, sino puede ser que el código 
//                    //este mal o haya sido alterado
//                    if ($s_url !== false) {
//                        SIANCache::delete($redirect);
//                        $this->redirect($s_url);
//                    }
//                }
//                $this->redirect('index');
//            }
//        }
//        $this->render('login', array('model' => $model));
//    }

    public function actionLogin($redirect = null) {

        //Si ya está logueado, direccionamos a index
        if (!Yii::app()->user->isGuest) {
            $this->redirect('index');
        }

        $this->layout = '//layouts/login';
        $model = new LoginForm;

        if (isset($_POST['ajax']) && $_POST['ajax'] === 'login-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }

        if (isset($_POST['LoginForm'])) {

            $model->attributes = $_POST['LoginForm'];

            if ($model->validate() && $model->login()) {
                $domain_redirect = Yii::app()->params['domain'];
                $admin_url_redirect = Yii::app()->params['admin_url'];
                $domain_react_app = Yii::app()->params['domain_react_app'];
                $react_app_auth = $domain_react_app . "/authentication/sian";
              
                //Si hay parámetro para URL
                if (isset($redirect)) {
                    $s_url = SIANCache::get($redirect);
                    //Si hay URL reireccionamos, sino puede ser que el código 
                    //este mal o haya sido alterado
                    if ($s_url !== false) {
                        SIANCache::delete($redirect);
                        if (isset($domain_react_app)) {
                            $token = $model->tokenForReact;
                            $getParams = "?user=" . $model->username . "&token=" . $token . "&domain_redirect=" . $domain_redirect . "&admin_url_redirect=" . $admin_url_redirect . "&url=" . $s_url;
                            // $this->redirect($s_url);
                            header("location: " . $react_app_auth . $getParams);
                            exit();
                        } else {
                            $this->redirect($s_url);
                        }
                    }
                }
                if (isset($domain_react_app)) {
                    $token = $model->tokenForReact;
                    // $this->redirect('index');
                    header("location: " . $react_app_auth . "?user=" . $model->username . "&token=" . $token . "&domain_redirect=" . $domain_redirect . "&admin_url_redirect=" . $admin_url_redirect);
                    exit();
                } else {
                    $this->redirect('index');
                }
            }
        }
        $this->render('login', array('model' => $model));
    }

    public function actionAfterLogin() {
        $domain_react_app = Yii::app()->params['domain_react_app'];
        if (isset($domain_react_app)) {
            $user_token = isset($_GET['token']) ? $_GET['token'] : '';
            $path = isset($_GET['path']) ? $_GET['path'] : '';

            if (empty($user_token)) {
                $this->redirect('index/error');
            }

            SIANCache::add('user_token', $user_token);

            if (empty($path)) {
                $this->redirect('index');
            }

            $this->redirect($path);
        }
    }

    public function actionUpdateRestoreId() {
        $restore_id = isset($_POST['restore_id']) ? $_POST['restore_id'] : null;
        //Actualizamos en sesión
        Yii::app()->user->setState('restore_id', $restore_id);
        //Actualizamos en BD
        User::model()->updateAll([
            'restore_id' => $restore_id
                ], [
            'condition' => "username = :username",
            'params' => [
                ':username' => Yii::app()->user->getState('username')
            ]
        ]);
    }

    /**
     * Logs out the current user and redirect to homepage.
     */
    public function actionLogout() {
        $domain_react_app = Yii::app()->params['domain_react_app'];
        if (isset($domain_react_app)) {
            $domain_redirect = Yii::app()->params['domain'];
            $react_app_auth = $domain_react_app . "/unauthenticate/sian";
            $user_token = SIANCache::get('user_token');
            SIANCache::delete('user_token');
            $username = Yii::app()->user->getState('username');
            Yii::app()->user->logout();
            header("location: " . $react_app_auth . "?username=" . $username . "&token=" . $user_token . "&domain_redirect=" . $domain_redirect);
            exit();
        } else {
            Yii::app()->user->logout();
            $this->redirect('login');
        }
    }

}
