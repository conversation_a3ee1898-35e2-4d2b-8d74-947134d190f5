<?php

// class GuiaDeRemisiónDeTransportista extends DocumentoEmision
class CarrierRemissionGuide extends RemissionGuide
{
    public function __construct()
    {
        parent::__construct();
        // $this->setVoucherType($this::CARRIER_REMISSION_GUIDE);
        $this->setVoucherType(self::CARRIER_REMISSION_GUIDE);
    }

    /**
     * ATRIBUTO: conductor_documento_tipo (Integer, Obligatorio)
     * VALOR: 1 = DNI - DOC. NACIONAL DE IDENTIDAD
        4 = CARNET DE EXTRANJERÍA
        7 = PASAPORTE
        A = CÉDULA DIPLOMÁTICA DE IDENTIDAD
        0 = NO DOMICILIADO, SIN RUC (EXPORTACIÓN)
     * LONGITUD: 1 exacto
     */
    public function setDriverDocumentType($p_i_driver_document_type)
    {
        $i_driver_document_type = isset($p_i_driver_document_type) ? $p_i_driver_document_type : null;

        if (!is_null($i_driver_document_type)) {
            parent::setDriverDocumentType($i_driver_document_type);
        } else {
            throw new Exception("El Tipo Documento para conductor es obligatorio");
        }
    }

    /**
     * ATRIBUTO: conductor_documento_numero (String, Obligatorio)
     * VALOR: Ejemplo: Número de DNI, Etc.
     * LONGITUD: 1 hasta 15
     */
    public function setDriverDocumentNumber($p_s_driver_document_number)
    {
        $s_driver_document_number = isset($p_s_driver_document_number) ? $p_s_driver_document_number : "";

        if (!empty($s_driver_document_number)) {
            parent::setDriverDocumentNumber($s_driver_document_number);
        } else {
            throw new Exception("El Número de Documento del conductor es obligatorio");
        }
    }

    /**
     * ATRIBUTO: conductor_nombre (String, Obligatorio)
     * VALOR: Es obligatorio Si el tipo de transporte es PRIVADO
     * LONGITUD: Hasta 250
     */
    public function setDriverName($p_s_driver_name)
    {
        $s_driver_name = isset($p_s_driver_name) ? $p_s_driver_name : "";
        if (!empty($s_driver_name)) {
            parent::setDriverName($s_driver_name);
        } else {
            throw new Exception("El Nombre del conductor es obligatorio");
        }
    }

    /**
     * ATRIBUTO: conductor_apellidos (String, Oligatorio)
     * VALOR: Es obligatorio Si el tipo de transporte es PRIVADO
     * LONGITUD: Hasta 250
     */
    public function setDriverSurname($p_s_driver_surname)
    {
        $s_driver_surname = isset($p_s_driver_surname) ? $p_s_driver_surname : "";
        if (!empty($s_driver_surname)) {
            parent::setDriverSurname($s_driver_surname);
        } else {
            throw new Exception("Los Apellidos del conductor son obligatorio");
        }
    }

    /**
     * ATRIBUTO: conductor_numero_licencia (String, Oligatorio)
     * VALOR: Es obligatorio Si el tipo de transporte es PRIVADO
     * LONGITUD: 9 hasta 10
     */
    public function setDriverLicenseNumber($p_s_driver_license_number)
    {
        $s_driver_license_number = isset($p_s_driver_license_number) ? $p_s_driver_license_number : "";
        if (!empty($s_driver_license_number)) {
            parent::setDriverLicenseNumber($s_driver_license_number);
        } else {
            throw new Exception("Los Número de Licencia del conductor es obligatorio");
        }
    }

    /**
     * ATRIBUTO: destinatario_documento_tipo (String, Condicional)
     * VALOR: A quién va dirigido el envío
        Sólo para GRE Transportista
        6 = RUC - REGISTRO ÚNICO DE CONTRIBUYENTE
        1 = DNI - DOC. NACIONAL DE IDENTIDAD
        4 = CARNET DE EXTRANJERÍA
        7 = PASAPORTE
        A = CÉDULA DIPLOMÁTICA DE IDENTIDAD
        0 = NO DOMICILIADO, SIN RUC (EXPORTACIÓN)
     * LONGITUD: 1 exacto
     */
    public function setRecipientDocumentType($p_s_recipient_document_type)
    {
        $this->data['destinatario_documento_tipo'] = $p_s_recipient_document_type;
    }

    /**
     * ATRIBUTO: destinatario_documento_numero (String, Condicional)
     * VALOR: Sólo para GRE Transportista
     * LONGITUD: 1 hasta 15
     */
    public function setRecipientDocumentNumber($p_s_recipient_document_number)
    {
        $this->data['destinatario_documento_numero'] = $p_s_recipient_document_number;
    }

    /**
     * ATRIBUTO: destinatario_denominacion (String, Condicional)
     * VALOR: Sólo para GRE Transportista
     * LONGITUD: 1 hasta 100
     */
    public function setRecipientDenomination($p_s_recipient_denomination)
    {
        $this->data['destinatario_denominacion'] = $p_s_recipient_denomination;
    }

}
