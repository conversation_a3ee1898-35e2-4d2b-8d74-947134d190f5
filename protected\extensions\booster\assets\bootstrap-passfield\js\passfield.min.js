/*! Pass*Field v1.1.7  | (c) 2013 Antelle | https://github.com/antelle/passfield/blob/master/MIT-LICENSE.txt */
!function(a,b,c){"use strict";var d=c.PassField=c.PassField||{};d.Config=d.Config||{},d.Config.locales={en:{lower:!0,msg:{pass:"password",and:"and",showPass:"Show password",hidePass:"Hide password",genPass:"Random password",passTooShort:"password is too short (min. length: {})",noCharType:"password must contain {}",digits:"digits",letters:"letters",letters_up:"letters in UPPER case",symbols:"symbols",inBlackList:"password is in list of top used passwords",passRequired:"password is required",equalTo:"password is equal to login",repeat:"password consists of repeating characters",badChars:"password contains bad characters: “{}”",weakWarn:"weak",invalidPassWarn:"*",weakTitle:"This password is weak",generateMsg:"To generate a strong password, click {} button."}},de:{lower:!1,msg:{pass:"Passwort",and:"und",showPass:"Passwort anzeigen",hidePass:"Passwort verbergen",genPass:"Zufallspasswort",passTooShort:"Passwort ist zu kurz (Mindestlänge: {})",noCharType:"Passwort muss {} enthalten",digits:"Ziffern",letters:"Buchstaben",letters_up:"Buchstaben in GROSSSCHRIFT",symbols:"Symbole",inBlackList:"Passwort steht auf der Liste der beliebtesten Passwörter",passRequired:"Passwort wird benötigt",equalTo:"Passwort ist wie Anmeldung",repeat:"Passwort besteht aus sich wiederholenden Zeichen",badChars:"Passwort enthält ungültige Zeichen: “{}”",weakWarn:"Schwach",invalidPassWarn:"*",weakTitle:"Dieses Passwort ist schwach",generateMsg:"Klicken Sie auf den {}-Button, um ein starkes Passwort zu generieren."}},fr:{lower:!0,msg:{pass:"mot de passe",and:"et",showPass:"Montrer le mot de passe",hidePass:"Cacher le mot de passe",genPass:"Mot de passe aléatoire",passTooShort:"le mot de passe est trop court (min. longueur: {})",noCharType:"le mot de passe doit contenir des {}",digits:"chiffres",letters:"lettres",letters_up:"lettres en MAJUSCULES",symbols:"symboles",inBlackList:"le mot de passe est dans la liste des plus utilisés",passRequired:"le mot de passe est requis",equalTo:"le mot de passe est le même que l'identifiant",repeat:"le mot de passe est une répétition de caractères",badChars:"le mot de passe contient des caractères incorrects: “{}”",weakWarn:"faible",invalidPassWarn:"*",weakTitle:"Ce mot de passe est faible",generateMsg:"Pour créer un mot de passe fort cliquez sur le bouton {}."}},it:{lower:!1,msg:{pass:"password",and:"e",showPass:"Mostra password",hidePass:"Nascondi password",genPass:"Password casuale",passTooShort:"la password è troppo breve (lunghezza min.: {})",noCharType:"la password deve contenere {}",digits:"numeri",letters:"lettere",letters_up:"lettere in MAIUSCOLO",symbols:"simboli",inBlackList:"la password è nella lista delle password più usate",passRequired:"è necessaria una password",equalTo:"la password è uguale al login",repeat:"la password è composta da caratteri che si ripetono",badChars:"la password contiene caratteri non accettati: “{}”",weakWarn:"debole",invalidPassWarn:"*",weakTitle:"Questa password è debole",generateMsg:"Per generare una password forte, clicca sul tasto {}."}},ru:{lower:!0,msg:{pass:"пароль",and:"и",showPass:"Показать пароль",hidePass:"Скрыть пароль",genPass:"Случайный пароль",passTooShort:"пароль слишком короткий (мин. длина: {})",noCharType:"в пароле должны быть {}",digits:"цифры",letters:"буквы",letters_up:"буквы в ВЕРХНЕМ регистре",symbols:"символы",inBlackList:"этот пароль часто используется в Интернете",passRequired:"пароль обязателен",equalTo:"пароль совпадает с логином",repeat:"пароль состоит из повторяющихся символов",badChars:"в пароле есть недопустимые символы: «{}»",weakWarn:"слабый",invalidPassWarn:"*",weakTitle:"Пароль слабый, его легко взломать",generateMsg:"Чтобы сгенерировать пароль, нажмите кнопку {}."}},ua:{lower:!0,msg:{pass:"пароль",and:"i",showPass:"Показати пароль",hidePass:"Сховати пароль",genPass:"Випадковий пароль",passTooShort:"пароль є занадто коротким (мiн. довжина: {})",noCharType:"пароль повинен містити {}",digits:"цифри",letters:"букви",letters_up:"букви у ВЕРХНЬОМУ регістрі",symbols:"cимволи",inBlackList:"пароль входить до списку паролей, що використовуються найчастіше",passRequired:"пароль є обов'язковим",equalTo:"пароль та логін однакові",repeat:"пароль містить повторювані символи",badChars:"пароль містить неприпустимі символи: «{}»",weakWarn:"слабкий",invalidPassWarn:"*",weakTitle:"Цей пароль є слабким",generateMsg:"Щоб ​​створити надійний пароль, натисніть кнопку {}."}},es:{lower:!0,msg:{pass:"contraseña",and:"y",showPass:"Mostrar contraseña",hidePass:"Ocultar contraseña",genPass:"Contraseña aleatoria",passTooShort:"contraseña demasiado corta (longitud mín.: {})",noCharType:"la contraseña debe contener {}",digits:"dígitos",letters:"letras",letters_up:"letras en MAYÚSCULAS",symbols:"símbolos",inBlackList:"la contraseña está en la lista de las contraseñas más usadas",passRequired:"se requiere contraseña",equalTo:"la contraseña es igual al inicio de sesión",repeat:"la contraseña tiene caracteres repetidos",badChars:"la contraseña contiene caracteres no permitidos: “{}”",weakWarn:"débil",invalidPassWarn:"*",weakTitle:"Esta contraseña es débil",generateMsg:"Para generar una contraseña segura, haga clic en el botón de {}."}},el:{lower:!0,msg:{pass:"πρόσβασης",and:"και",showPass:"Προβολή κωδικού πρόσβασης",hidePass:"Απόκρυψη κωδικού πρόσβασης",genPass:"Τυχαίος κωδικός πρόσβασης",passTooShort:"ο κωδικός πρόσβασης είναι πολύ μικρός (ελάχιστο μήκος: {})",noCharType:"ο κωδικός πρόσβασης πρέπει να περιέχει {}",digits:"ψηφία",letters:"λατινικά γράμματα",letters_up:"λατινικά γράμματα με ΚΕΦΑΛΑΙΑ",symbols:"σύμβολα",inBlackList:"ο κωδικός πρόσβασης βρίσκεται σε κατάλογο δημοφιλέστερων κωδικών",passRequired:"απαιτείται κωδικός πρόσβασης",equalTo:"ο κωδικός είναι όμοιος με το όνομα χρήστη",repeat:"ο κωδικός αποτελείται από επαναλαμβανόμενους χαρακτήρες",badChars:"ο κωδικός περιέχει μη επιτρεπτούς χαρακτήρες: “{}”",weakWarn:"αδύναμος",invalidPassWarn:"*",weakTitle:"Αυτός ο κωδικός πρόσβασης είναι αδύναμος",generateMsg:"Για να δημιουργήσετε δυνατό κωδικό πρόσβασης, κάντε κλικ στο κουμπί {}."}},pt:{lower:!0,msg:{pass:"senha",and:"e",showPass:"Mostrar senha",hidePass:"Ocultar senha",genPass:"Senha aleatória",passTooShort:"senha muito curta (tamanho mínimo: {})",noCharType:"Senha deve conter {}",digits:"dígito",letters:"letras",letters_up:"letras maiúsculas",symbols:"símbolos",inBlackList:"senha está na lista das senhas mais usadas",passRequired:"senha é obrigatória",equalTo:"senha igual ao login",repeat:"senha consiste em uma repetição de caracteres",badChars:"senha tem caracteres inválidos: “{}”",weakWarn:"fraca",invalidPassWarn:"*",weakTitle:"Esta senha é fraca",generateMsg:"Para gerar uma senha forte, clique no botão {}."}}}}(window.jQuery,document,window),function(a,b,c,d){"use strict";var e=c.PassField=c.PassField||{};e.CharTypes={DIGIT:"digits",LETTER:"letters",LETTER_UP:"letters_up",SYMBOL:"symbols",UNKNOWN:"unknown"},e.CheckModes={MODERATE:0,STRICT:1},e.Config={defaults:{pattern:"abcdef12",acceptRate:.8,allowEmpty:!0,isMasked:!0,showToggle:!0,showGenerate:!0,showWarn:!0,showTip:!0,tipPopoverStyle:{},strengthCheckTimeout:500,validationCallback:null,blackList:[],locale:"",localeMsg:{},warnMsgClassName:"help-inline",errorWrapClassName:"error",allowAnyChars:!0,checkMode:e.CheckModes.MODERATE,chars:{digits:"1234567890",letters:"abcdefghijklmnopqrstuvwxyzßабвгедёжзийклмнопрстуфхцчшщъыьэюяґєåäâáàãéèêëíìîїóòôõöüúùûýñçøåæþðαβγδεζηθικλμνξοπρσςτυφχψω",letters_up:"ABCDEFGHIJKLMNOPQRSTUVWXYZАБВГЕДЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯҐЄÅÄÂÁÀÃÉÈÊËÍÌÎЇÓÒÔÕÖÜÚÙÛÝÑÇØÅÆÞÐΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΤΥΦΧΨΩ",symbols:"@#$%^&*()-_=+[]{};:<>/?!"},events:{generated:null,switched:null},nonMatchField:null},locales:e.Config?e.Config.locales:{},blackList:["password","123456","12345678","abc123","qwerty","monkey","letmein","dragon","111111","baseball","iloveyou","trustno1","1234567","sunshine","master","123123","welcome","shadow","ashley","football","jesus","michael","ninja","mustang","password1","p@ssw0rd","miss","root","secret"],generationChars:{digits:"1234567890",letters:"abcdefghijklmnopqrstuvwxyz",letters_up:"ABCDEFGHIJKLMNOPQRSTUVWXYZ"},dataAttr:"PassField.Field"},e.Field=function(g,h){function i(){j(),k()&&(l(),n(),B(),o(),D(),M(Bb.isMasked,!1),P(),sb(e.Config.dataAttr,this))}function j(){Bb.showGenerate&&!Bb.showToggle&&(Bb.isMasked=!1),Bb.blackList=(Bb.blackList||[]).concat(e.Config.blackList)}function k(){return"string"==typeof g&&(g=b.getElementById(g)),Ab.mainInput=g,!!Ab.mainInput}function l(){var a="en",b=Bb.locale;!b&&navigator.language&&(b=navigator.language.replace(/\-.*/g,"")),b&&(tb=zb.locales[b]),tb&&(tb=f.extend({},zb.locales[a],tb)),tb||(tb=f.extend({},zb.locales[a])),Bb.localeMsg&&f.extend(tb.msg,Bb.localeMsg)}function m(a){Ab.mainInput.value=a,Ab.clearInput&&(Ab.clearInput.value=a),G()}function n(){ub=Ab.mainInput.id,ub||(ub="i"+Math.round(1e5*Math.random()),Ab.mainInput.id=ub)}function o(){var a=hb(Ab.mainInput);a.top+=kb(Ab.mainInput,"marginTop"),p(),q(),r(),s(),t(),u(a),v(a),w(),setTimeout(x,0)}function p(){Ab.wrapper=Ab.mainInput.parentNode,ob(Ab.wrapper,"wrap"),yb.hasInlineBlock||ob(Ab.wrapper,"wrap-no-ib"),"static"==jb(Ab.wrapper,"position")&&(Ab.wrapper.style.position="relative")}function q(){if(!yb.changeType){Ab.clearInput=bb("input",{type:"text",id:"txt-clear",className:"txt-clear",value:Ab.mainInput.value},{display:"none"});var a=Ab.mainInput.className;a&&ob(Ab.clearInput,a,!0);var b=Ab.mainInput.style.cssText;b&&(Ab.clearInput.style.cssText=b),f.each(["maxLength","size","placeholder"],function(a){var b=Ab.mainInput.getAttribute(a);b&&Ab.clearInput.setAttribute(a,b)}),lb(Ab.mainInput,Ab.clearInput)}ob(Ab.mainInput,"txt-pass")}function r(){Bb.showWarn&&(Ab.warnMsg=bb("div",{id:"warn",className:"warn"},{margin:"0 0 0 3px"}),Bb.warnMsgClassName&&ob(Ab.warnMsg,Bb.warnMsgClassName,!0),lb(Ab.clearInput||Ab.mainInput,Ab.warnMsg))}function s(){Bb.showToggle&&(Ab.maskBtn=bb("div",{id:"btn-mask",className:"btn-mask",title:tb.msg.showPass},{position:"absolute",margin:"0",padding:"0"}),ob(Ab.maskBtn,"btn"),mb(Ab.maskBtn,"abc"),lb(Ab.mainInput,Ab.maskBtn))}function t(){Bb.showGenerate&&(Ab.genBtn=bb("div",{id:"btn-gen",className:"btn-gen",title:tb.msg.genPass},{position:"absolute",margin:"0",padding:"0"}),ob(Ab.genBtn,"btn"),lb(Ab.mainInput,Ab.genBtn),Ab.genBtnInner=bb("div",{id:"btn-gen-i",className:"btn-gen-i",title:tb.msg.genPass}),Ab.genBtn.appendChild(Ab.genBtnInner))}function u(b){if(Bb.showTip)if(Bb.tipPopoverStyle&&a&&"function"==typeof a.fn.popover)a(Ab.mainInput).popover(f.extend({title:null,placement:Bb.tipPopoverStyle.placement||function(b,d){var e=a(d).position().top-a(c).scrollTop(),f=a(c).height()-e;return f>300||f>e?"bottom":"top"},animation:!1},Bb.tipPopoverStyle,{trigger:"manual",html:!0,content:function(){return Kb}}));else{Ab.tip=bb("div",{id:"tip",className:"tip"},{position:"absolute",margin:"0",padding:"0",width:b.width+"px"}),lb(Ab.mainInput,Ab.tip);var d=bb("div",{id:"tip-arr-wrap",className:"tip-arr-wrap"});Ab.tip.appendChild(d),d.appendChild(bb("div",{id:"tip-arr",className:"tip-arr"})),d.appendChild(bb("div",{id:"tip-arr-in",className:"tip-arr-in"})),Ab.tipBody=bb("div",{id:"tip-body",className:"tip-body"}),Ab.tip.appendChild(Ab.tipBody)}}function v(a){if(yb.placeholders)!Ab.mainInput.getAttribute("placeholder")&&Ab.mainInput.getAttribute("data-placeholder")&&Ab.mainInput.setAttribute("placeholder",Ab.mainInput.getAttribute("data-placeholder"));else{var b=Ab.mainInput.getAttribute("placeholder")||Ab.mainInput.getAttribute("data-placeholder");b&&(Ab.placeholder=bb("div",{id:"placeholder",className:"placeholder"},{position:"absolute",margin:"0",padding:"0",height:a.height+"px",lineHeight:a.height+"px"}),mb(Ab.placeholder,b),lb(Ab.mainInput,Ab.placeholder))}}function w(){yb.passSymbol&&(Ab.passLengthChecker=bb("div",{id:"len"},{position:"absolute",height:jb(Ab.mainInput,"height"),top:"-10000px",left:"-10000px",display:"block",color:"transparent",border:"none"}),lb(Ab.mainInput,Ab.passLengthChecker),setTimeout(function(){f.each(["marginLeft","fontFamily","fontSize","fontWeight","fontStyle","fontVariant"],function(a){var b=jb(Ab.mainInput,a);b&&(Ab.passLengthChecker.style[a]=b)})},50))}function x(){z(),A();var a=hb(C()),b=y();Ab.maskBtn&&"none"!=Ab.maskBtn.style.display&&(b+=kb(Ab.maskBtn,"width"),ib(Ab.maskBtn,{top:a.top,left:a.left+a.width-b,height:a.height})),Ab.genBtn&&"none"!=Ab.genBtn.style.display&&(b+=kb(Ab.genBtn,"width"),ib(Ab.genBtn,{top:a.top,left:a.left+a.width-b,height:a.height}),Ab.genBtnInner.style.marginTop=Math.max(0,Math.round((a.height-19)/2))+"px"),Ab.placeholder&&"none"!=Ab.placeholder.style.display&&ib(Ab.placeholder,{top:a.top,left:a.left+7,height:a.height}),Ab.tip&&"none"!=Ab.tip.style.display&&ib(Ab.tip,{left:a.left,top:a.top+a.height,width:a.width})}function y(){var a=kb(C(),"paddingRight");return Math.max(Nb,a)}function z(){Ab.genBtn&&(Ab.genBtn.style.display=Fb||Gb&&!Hb?"block":"none"),Ab.maskBtn&&(Ab.maskBtn.style.display=Fb||Gb&&!Ib?"block":"none")}function A(){if(Bb.showTip)if(Ab.tip)Ab.tip.style.display=Db&&Gb?"block":"none";else if(Db&&Gb){if(!Jb||Kb!=Jb){var b=a(Ab.mainInput).data("popover")||a(Ab.mainInput).data("bs.popover"),c=b.options,d=c.animation;Jb&&(c.animation=!1);var e=C().offsetWidth-2,f=b.$tip;f?f.width(e):b.options.template&&(b.options.template=b.options.template.replace('class="popover"','class="popover" style="width: '+e+'px"')),Ab.clearInput&&(b.$element=a(C())),a(Ab.mainInput).popover("show"),Jb=Kb,c.animation=d}}else Jb&&(Jb=null,a(Ab.mainInput).popover("hide"))}function B(){var a=!0,c=!0,d=b.createElement("input");"placeholder"in d||(a=!1),d.setAttribute("style","position:absolute;left:-10000px;top:-10000px;"),b.body.appendChild(d);try{d.setAttribute("type","password")}catch(e){c=!1}b.body.removeChild(d);var f=b.createElement("div");f.setAttribute("style","display:inline-block"),f.style.paddingLeft=f.style.width="1px",b.body.appendChild(f);var g=2==f.offsetWidth,h="inline-block"===jb(f,"display");b.body.removeChild(f);var i=navigator.userAgent.indexOf("AppleWebKit")>=0||navigator.userAgent.indexOf("Opera")>=0||navigator.userAgent.indexOf("Firefox")>=0&&navigator.platform.indexOf("Mac")>=0?"•":"●";yb={placeholders:a,changeType:c,boxModel:g,hasInlineBlock:h,passSymbol:i}}function C(){return Cb?Ab.mainInput:Ab.clearInput||Ab.mainInput}function D(){if(f.each(Ab.clearInput?[Ab.mainInput,Ab.clearInput]:[Ab.mainInput],function(a){f.attachEvent(a,"onkeyup",G),f.attachEvent(a,"onfocus",J),f.attachEvent(a,"onblur",K),f.attachEvent(a,"onmouseover",F),f.attachEvent(a,"onmouseout",F),Ab.placeholder&&f.attachEvent(a,"onkeydown",I)}),f.attachEvent(c,"onresize",x),Ab.maskBtn&&(f.attachEvent(Ab.maskBtn,"onclick",function(){M()}),f.attachEvent(Ab.maskBtn,"onmouseover",F),f.attachEvent(Ab.maskBtn,"onmouseout",F)),Ab.genBtn&&(f.attachEvent(Ab.genBtn,"onclick",function(){Q()}),f.attachEvent(Ab.genBtn,"onmouseover",F),f.attachEvent(Ab.genBtn,"onmouseout",F)),Ab.placeholder&&f.attachEvent(Ab.placeholder,"onclick",E),Bb.nonMatchField){var a=nb(Bb.nonMatchField);a&&f.attachEvent(a,"onkeyup",L)}}function E(){C().focus()}function F(a){var b="mouseover"===a.type,c=a.relatedTarget?a.relatedTarget:b?a.fromElement:a.toElement;(!c||!c.id||0!=c.id.indexOf(Lb+"btn")&&c!==Ab.mainInput&&c!==Ab.clearInput)&&(Fb=b,x())}function G(a){var b,c=a?a.which||a.keyCode:null,d=c===Pb||c===Ob;b=Ab.clearInput?Cb?Ab.clearInput.value=Ab.mainInput.value:Ab.mainInput.value=Ab.clearInput.value:Ab.mainInput.value,Bb.strengthCheckTimeout>0&&!Db&&!d?(vb&&clearTimeout(vb),vb=setTimeout(R,Bb.strengthCheckTimeout)):R(),Ab.placeholder&&!b&&(Ab.placeholder.style.display="block"),H()}function H(){if(Ab.passLengthChecker){var a=C().value;Cb&&(a=a.replace(/./g,yb.passSymbol)),mb(Ab.passLengthChecker,a);var b=Ab.passLengthChecker.offsetWidth;b+=kb(Ab.mainInput,"paddingLeft");var c=0,d=0,e=gb(C()),f=e.width,g=!1,h=y();if(Ab.maskBtn){c=kb(Ab.maskBtn,"width");var i=f-c-h,j=b>i;Ib!=j&&(g=!0,Ib=j)}if(Ab.genBtn){d=kb(Ab.genBtn,"width");var k=f-c-d-h,l=b>k;Hb!=l&&(g=!0,Hb=l)}g&&x()}}function I(){Ab.placeholder&&(Ab.placeholder.style.display="none")}function J(){wb&&(clearTimeout(wb),wb=null),xb&&(clearTimeout(xb),xb=null),Gb=!0,x()}function K(){wb=setTimeout(function(){wb=null,Gb=!1,x(),Bb.isMasked&&!xb&&(xb=setTimeout(function(){xb=null,M(!0,!1)},1500))},100)}function L(){Db&&R()}function M(a,b){b===d&&(b=!0);var c=a!=Cb;if(a=a===d?!Cb:!!a,yb.changeType){var e=C(),g=N(e);e.setAttribute("type",a?"password":"text"),b&&(O(e,g),e.focus())}else{var h=jb(C(),"display")||"block",i=a?Ab.clearInput:Ab.mainInput,j=a?Ab.mainInput:Ab.clearInput;Cb!=a&&f.each(["paddingRight","width","backgroundImage","backgroundPosition","backgroundRepeat","backgroundAttachment","border"],function(a){var b=i.style[a];b&&(j.style[a]=b)});var k=N(i);j.style.display=h,i.style.display="none",j.value=i.value,b&&(O(j,k),j.focus()),Ab.mainInput.nextSibling!=Ab.clearInput&&lb(Ab.mainInput,Ab.clearInput)}Ab.maskBtn&&(mb(Ab.maskBtn,a?"abc":"&bull;&bull;&bull;"),Ab.maskBtn.title=a?tb.msg.showPass:tb.msg.hidePass),Cb=a,H(),x(),c&&rb("switched",Cb)}function N(a){return"number"==typeof a.selectionStart&&"number"==typeof a.selectionEnd?{start:a.selectionStart,end:a.selectionEnd}:null}function O(a,b){b&&"number"==typeof a.selectionStart&&"number"==typeof a.selectionEnd&&(a.selectionStart=b.start,a.selectionEnd=b.end)}function P(){("function"==typeof Ab.mainInput.hasAttribute&&Ab.mainInput.hasAttribute("autofocus")||Ab.mainInput.getAttribute("autofocus"))&&(Ab.mainInput.focus(),J())}function Q(){var a=Z();Ab.mainInput.value=a,Ab.clearInput&&(Ab.clearInput.value=a),rb("generated",a),M(!1),vb&&(clearTimeout(vb),vb=null),R(),Ab.placeholder&&(Ab.placeholder.style.display="none")}function R(){vb&&(clearTimeout(vb),vb=null);var a=C().value,b=S(a);if(0==a.length)b={strength:Bb.allowEmpty?0:null,messages:[tb.msg.passRequired]};else{!Bb.allowAnyChars&&b.charTypes[e.CharTypes.UNKNOWN]&&(b={strength:null,messages:[tb.msg.badChars.replace("{}",b.charTypes[e.CharTypes.UNKNOWN])]}),delete b.charTypes;var c=!1;f.each(Bb.blackList,function(b){return b==a?(c=!0,!1):!0}),c&&(b={strength:0,messages:[tb.msg.inBlackList]}),a&&a===W()&&(b={strength:0,messages:[tb.msg.equalTo]})}if("function"==typeof Bb.validationCallback){var d,g,h=Bb.validationCallback(Ab.mainInput,b);h&&h.messages&&f.isArray(h.messages)&&(d=h.messages),h&&Object.prototype.hasOwnProperty.call(h,"strength")&&("number"==typeof h.strength||null===h.strength)&&(g=h.strength),d&&d.length?(b.messages=d,b.strength=g):g&&g>b.strength&&(b.strength=g)}return 0==a.length&&Bb.allowEmpty?(V(),Eb={strength:0},!0):null===b.strength||b.strength<Bb.acceptRate?(U(b.strength,b.messages),!1):(V(),Eb={strength:b.strength},!0)}function S(a){var b=$(Bb.pattern,e.CharTypes.SYMBOL),c=$(a,Bb.allowAnyChars?e.CharTypes.SYMBOL:e.CharTypes.UNKNOWN),d=[],g=0;f.each(b,function(a){if(g++,!c[a]){var b=tb.msg[a];if(a==e.CharTypes.SYMBOL){var f=4,h=Bb.chars[a];h.length>f&&(h=h.substring(0,f)),b=b+" ("+h+")"}d.push(b)}});var h=1-d.length/g;if(d.length&&(d=[T(d)]),Bb.checkMode==e.CheckModes.MODERATE){var i=0;f.each(c,function(a){b[a]||i++}),h+=i/g}var j=a.length/Bb.pattern.length-1;if(0>j?(h+=j,d.push(tb.msg.passTooShort.replace("{}",Bb.pattern.length.toString()))):Bb.checkMode==e.CheckModes.MODERATE&&(h+=j/g),a.length>2){for(var k=a.charAt(0),l=!0,m=0;m<a.length;m++)if(a.charAt(m)!=k){l=!1;break}l&&(h=0,d=[tb.msg.repeat])}return 0>h&&(h=0),h>1&&(h=1),{strength:h,messages:d,charTypes:c}}function T(a){for(var b=a[0],c=1;c<a.length;c++)b+=c==a.length-1?" "+tb.msg.and+" ":", ",b+=a[c];return tb.msg.noCharType.replace("{}",b)}function U(a,b){var c="",d="";if(null===a)c=tb.msg.invalidPassWarn,d=b[0].charAt(0).toUpperCase()+b[0].substring(1);else if(c=tb.msg.weakWarn,d="",b)for(var e=0;e<b.length;e++){var f=b[e].charAt(0);0==e?(d+=tb.msg.weakTitle+": ",tb.lower&&(f=f.toLowerCase())):(d+="<br/>",f=f.toUpperCase()),d+=f+b[e].substring(1),d&&"."!=d.charAt(d.length-1)&&(d+=".")}if(d&&"."!=d.charAt(d.length-1)&&(d+="."),Eb={strength:a,message:d},Ab.warnMsg&&(mb(Ab.warnMsg,c),Ab.warnMsg.title=d,Bb.errorWrapClassName&&ob(Ab.wrapper,Bb.errorWrapClassName,!0)),Bb.showTip){var g=d;Ab.genBtn&&(g+="<br/>"+tb.msg.generateMsg.replace("{}",'<div class="'+ab("btn-gen-help")+'"></div>')),Kb=g,Ab.tipBody&&mb(Ab.tipBody,g)}Db=!0,x()}function V(){Ab.warnMsg&&(mb(Ab.warnMsg,""),Ab.warnMsg.title="",Bb.errorWrapClassName&&pb(Ab.wrapper,Bb.errorWrapClassName,!0)),Kb=null,Db=!1,x()}function W(){if(!Bb.nonMatchField)return null;var a=nb(Bb.nonMatchField);return a?a.value:null}function X(){return Eb?Eb.message:null}function Y(){return Eb?Eb.strength:-1}function Z(){var a="",b=$(Bb.pattern,e.CharTypes.SYMBOL),c=[];return f.each(b,function(a,b){for(var d=0;d<b.length;d++)c.push(a)}),c.sort(function(){return.7-Math.random()}),f.each(c,function(b){var c=zb.generationChars[b];c?Bb.chars[b]&&Bb.chars[b].indexOf(c)<0&&(c=Bb.chars[b]):c=Bb.chars[b],a+=f.selectRandom(c)}),a}function $(a,b){for(var c={},d=0;d<a.length;d++){var e=a.charAt(d),g=b;f.each(Bb.chars,function(a,b){return b.indexOf(e)>=0?(g=a,!1):!0}),c[g]=(c[g]||"")+e}return c}function _(a){return Lb+a+"-"+ub}function ab(a){return Lb+a}function bb(a,b,c){return b.id&&(b.id=_(b.id)),b.className&&(b.className=ab(b.className)),f.newEl(a,b,c)}function cb(a){try{return a.getBoundingClientRect()}catch(b){return{top:0,left:0}}}function db(a){var b=a.ownerDocument;if(!b)return{top:0,left:0};var d=cb(a);return{top:d.top+(c.pageYOffset||0)-(b.documentElement.clientTop||0),left:d.left+(c.pageXOffset||0)-(b.documentElement.clientLeft||0)}}function eb(a){var c;try{c=a.offsetParent}catch(d){}for(c||(c=b.documentElement);c&&"html"!=c.nodeName.toLowerCase()&&"static"===jb(c,"position");)c=c.offsetParent;return c||b.documentElement}function fb(a){var b,c={top:0,left:0};if("fixed"===jb(a,"position"))b=cb(a);else{var d=eb(a);b=db(a),"html"!=d.nodeName.toLowerCase()&&(c=db(d)),c.top+=kb(d,"borderTopWidth"),c.left+=kb(d,"borderLeftWidth")}return{top:b.top-c.top-kb(a,"marginTop"),left:b.left-c.left-kb(a,"marginLeft")}}function gb(a){return{width:a.offsetWidth,height:a.offsetHeight}}function hb(a){return f.extend(db(a),gb(a))}function ib(a,b){if(b.height&&!isNaN(b.height)&&(a.style.height=b.height+"px",a.style.lineHeight=b.height+"px"),b.width&&!isNaN(b.width)&&(a.style.width=b.width+"px"),b.top||b.left){if("none"==jb(a,"display"))return a.style.top=b.top+"px",a.style.left=b.left+"px",void 0;var c,d,e;if(e=db(a),d=jb(a,"top")||0,c=jb(a,"left")||0,(d+c+"").indexOf("auto")>-1){var f=fb(a);d=f.top,c=f.left}else d=parseFloat(d)||0,c=parseFloat(c)||0;b.top&&(a.style.top=b.top-e.top+d+"px"),b.left&&(a.style.left=b.left-e.left+c+"px")}}function jb(a,b){var d="function"==typeof c.getComputedStyle?c.getComputedStyle(a,null):a.currentStyle;return d?d[b]:null}function kb(a,b){var c=jb(a,b);if(!c)return 0;var d=parseFloat(c);return isNaN(d)?0:d}function lb(a,b){a.parentNode&&a.parentNode.insertBefore(b,a.nextSibling)}function mb(a,c){try{a.innerHTML=c}catch(d){var e=b.createElement("c");for(e.innerHTML=c;a.firstChild;)a.removeChild(a.firstChild);a.appendChild(e)}}function nb(a){return"string"==typeof a?b.getElementById(a):a.jquery?a[0]:a}function ob(a,b,c){qb(a,b,c)||(a.className=a.className+(a.className?" ":"")+(c===!0?b:ab(b)))}function pb(a,b,c){qb(a,b,c)&&(a.className=(" "+a.className+" ").replace((c===!0?b:ab(b))+" ","").replace(/^\s+|\s+$/g,""))}function qb(a,b,c){return b=" "+(c===!0?b:ab(b))+" ",(" "+a.className+" ").replace(/[\n\t]/g," ").indexOf(b)>-1}function rb(b,c){if(a)try{a(Ab.mainInput).trigger(Mb+b,c)}catch(d){}if(Bb.events&&"function"==typeof Bb.events[b])try{Bb.events[b].call(Ab.mainInput,c)}catch(d){}}function sb(b,c){a&&a(Ab.mainInput).data(b,c)}var tb,ub,vb,wb,xb,yb,zb=e.Config,Ab={},Bb=f.extend({},zb.defaults,h),Cb=!0,Db=!1,Eb=null,Fb=!1,Gb=!1,Hb=!1,Ib=!1,Jb=!1,Kb=null,Lb="a_pf-",Mb="pass:",Nb=5,Ob=46,Pb=8;this.toggleMasking=function(a){M(a)},this.setPass=m,this.validatePass=R,this.getPassValidationMessage=X,this.getPassStrength=Y,i.call(this)};var f={};f.extend=function(){for(var a=arguments,b=1;b<a.length;b++)f.each(a[b],function(b,c){a[0][b]=f.isArray(a[0][b])||f.isArray(c)?a[0][b]?a[0][b].concat(c||[]):c:f.isElement(c)?c:"object"==typeof a[0][b]&&"object"==typeof c&&null!==c?f.extend({},a[0][b],c):"object"==typeof c&&null!==c?f.extend({},c):c});return a[0]},f.newEl=function(a,c,d){var e=b.createElement(a);return c&&f.each(c,function(a,b){b&&(e[a]=b)}),d&&f.each(d,function(a,b){b&&(e.style[a]=b)}),e},f.attachEvent=function(a,b,d){var e=a[b];a[b]=function(a){a||(a=c.event),d(a),"function"==typeof e&&e(a)}},f.each=function(a,b){if(f.isArray(a)){for(var c=0;c<a.length;c++)if(b(a[c])===!1)return}else for(var d in a)if(Object.prototype.hasOwnProperty.call(a,d)&&b(d,a[d])===!1)return},f.isArray=function(a){return"[object Array]"===Object.prototype.toString.call(a)},f.isElement=function(b){if(!b)return!1;try{return b instanceof HTMLElement||a&&b instanceof jQuery}catch(c){return"object"==typeof b&&b.nodeType||b.jquery}},f.selectRandom=function(a){var b=Math.floor(Math.random()*a.length);return f.isArray(a)?a[b]:a.charAt(b)},f.contains=function(a,b){if(!a)return!1;var c=!1;return f.each(a,function(a){return a===b?(c=!0,!1):!0}),c},a&&(a.fn.passField=function(a){return this.each(function(){new e.Field(this,a)})},a.fn.togglePassMasking=function(b){return this.each(function(){var c=a(this).data(e.Config.dataAttr);c&&c.toggleMasking(b)})},a.fn.setPass=function(b){return this.each(function(){var c=a(this).data(e.Config.dataAttr);c&&c.setPass(b)})},a.fn.validatePass=function(){var b=!0;return this.each(function(){var c=a(this).data(e.Config.dataAttr);c&&!c.validatePass()&&(b=!1)}),b},a.fn.getPassValidationMessage=function(){var a=this.first();if(a){var b=a.data(e.Config.dataAttr);if(b)return b.getPassValidationMessage()}return null},a.fn.getPassStrength=function(){var a=this.first();if(a){var b=a.data(e.Config.dataAttr);if(b)return b.getPassStrength()}return null}),a&&a.validator&&jQuery.validator.addMethod("passfield",function(b,c){return a(c).validatePass()},function(b,c){return a(c).getPassValidationMessage()})}(window.jQuery,document,window);