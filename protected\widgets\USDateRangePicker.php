<?php

/**
 * @preserve jQuery DateTimePicker plugin v2.4.1
 * @homepage http://xdsoft.net/jqplugins/datetimepicker/
 * (c) 2014, Chupurnov Valeriy.
 */
class USDateRangePicker extends CWidget {

    public $id = null;
    public $form;
    public $name;
    public $label;
    public $value;
    public $model;
    public $icon = true;
    public $attribute;
    public $options = [];
    public $htmlOptions = [];
    //PRIVATE
    private $controller;

    /**
     * Initializes the widget.
     */
    public function init() {

        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //OPTIONS
        $this->options['language'] = isset($this->options['lang']) ? $this->options['lang'] : Yii::app()->language;
        //Formatos
        if (isset($this->options['format'])) {
            $this->options['format'] = $this->options['format'];
        }
        $this->options['separator'] = ' a ';
        $this->options = Util::jsonEncode($this->options);

        //HTML OPTIONS
        $this->htmlOptions['id'] = $this->id;
        $this->htmlOptions['class'] = (isset($this->htmlOptions['class']) ? $this->htmlOptions['class'] : '') . ' us-datepicker';
        if (isset($this->model, $this->attribute)) {
            $this->htmlOptions['placeholder'] = $this->model->getAttributeLabel($this->attribute);
        } else {
            $this->htmlOptions['placeholder'] = $this->label;
        }
        $this->htmlOptions['singleMonth'] = true;

        SIANAssets::registerCssFile("other/dateRangePicker/css/daterangepicker.css");
        SIANAssets::registerScriptFile("other/dateRangePicker/js/moment.js");
        SIANAssets::registerScriptFile("other/dateRangePicker/js/jquery.daterangepicker.js");

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        jQuery(function(){
            jQuery('#{$this->id}').dateRangePicker({$this->options})
                .bind('datepicker-apply', function()
                {   
                    $('#{$this->id}').trigger({
                        type: 'keydown',
                        keyCode: 13, // Código de la tecla Enter
                        which: 13    // También puedes usar 'which' para la misma tecla
                    });
                });
        });
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->icon) {
            $this->htmlOptions['append'] = '<span class="fa fa-lg fa-calendar black"></span>';
        }
//
        if (isset($this->model, $this->attribute)) {
            if ($this->label) {
                echo $this->form->textFieldRow($this->model, $this->attribute, $this->htmlOptions);
            } else {
                echo $this->form->textField($this->model, $this->attribute, $this->htmlOptions);
            }
        } else {

            $this->htmlOptions['class'] = 'form-control ' . $this->htmlOptions['class'];

            echo "<div class='form-group'>";
            if ($this->label) {
                echo CHtml::label($this->label, $this->id, array(
                    'class' => 'control-label'
                ));
            }
            echo Chtml::textField($this->name, $this->value, $this->htmlOptions);
            echo "</div>";
        }
    }

}
