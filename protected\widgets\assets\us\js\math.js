class USMath {

    static get ROUND_HALF_UP() {
        return 1;
    }

    static get ROUND_HALF_DOWN() {
        return 2;
    }

    static and(x, y) {
        var a = new BigNumber(x);
        var b = new BigNumber(y);
        var c = a.multipliedBy(b);
        if (c.toFixed(0) > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    static or(x, y) {
        var a = new BigNumber(x);
        var b = new BigNumber(y);
        var c = a.plus(b);
        if (c.toFixed(0) > 0) {
            return 1;
        } else {
            return 0;
        }
    }

    static isEqualTo(x, y) {
        if (x === y) {
            return 1;
        } else {
            return 0;
        }
    }

    static isGreaterThan(x, y) {
        if (x > y) {
            return 1;
        } else {
            return 0;
        }
    }

    static isLessThan(x, y) {
        if (x < y) {
            return 1;
        } else {
            return 0;
        }
    }

    static isGreaterThanOrEqualTo(x, y) {
        if (x > y) {
            return 1;
        } else {
            return 0;
        }
    }

    static isLessThanOrEqualTo(x, y) {
        if (x <= y) {
            return 1;
        } else {
            return 0;
        }
    }

    static isDifferentTo(x, y) {
        if (x === y) {
            return 0;
        } else {
            return 1;
        }
    }

    static plus(x, y, fixed) {
        var a = new BigNumber(x);
        var b = new BigNumber(y);
        var c = a.plus(b);

        return Number(fixed ? c.toFixed(fixed) : c);
    }

    static minus(x, y, fixed) {
        var a = new BigNumber(x);
        var b = new BigNumber(y);
        var c = a.minus(b);
        return Number(fixed ? c.toFixed(fixed) : c);
    }

    static multiply(x, y, fixed) {
        var a = new BigNumber(x);
        var b = new BigNumber(y);
        var c = a.multipliedBy(b);
        return Number(fixed ? c.toFixed(fixed) : c);
    }

    static divide(x, y, fixed) {
        var a = new BigNumber(x);
        var b = new BigNumber(y);
        var c = a.dividedBy(b);
        return Number(fixed ? c.toFixed(fixed) : c);
    }

    static mod(x, y, fixed) {
        var a = new BigNumber(x);
        var b = new BigNumber(y);
        var c = a.modulo(b);
        return Number(fixed ? c.toFixed(fixed) : c);
    }

    static pow(x, y, fixed) {
        var a = new BigNumber(x);
        var b = new BigNumber(y);
        var c = a.exponentiatedBy(b);
        return Number(fixed ? c.toFixed(fixed) : c);
    }

    static floor(value, fixed)
    {
        var factor = Math.pow(10, fixed);
        return USMath.round(Math.floor(value * factor) / factor, fixed);
    }

    static round(value, fixed, mode)
    {
        mode = mode || USMath.ROUND_HALF_UP;

        if (mode == 1)
        {
            return Number(Math.round(value + 'e' + fixed) + 'e-' + fixed);
        }

        if (mode == 2)
        {
            return -Number(Math.round(-value + 'e' + fixed) + 'e-' + fixed);
        }
    }

    static roundHalfAndInteger(value)
    {
        var residue = value % 1;
        var value_final = 0;

        if (residue > 0) {
            var lower_limit = value - residue;
            var upper_limit = lower_limit + 1;
            var rounded = Number(Math.round(value + 'e' + 0) + 'e-' + 0);

            if (rounded !== upper_limit) {
                value_final = rounded + 0.50;
            } else {
                value_final = rounded;
            }

        } else {
            value_final = value;
        }
        return value_final;
    }

    //Es importante el order de los operadores, primero deben estar los compuestos porque se hace split en ese orden.
    static operators()
    {
        return [
            '<>',
            '>=',
            '<=',
            '(',
            ')',
            '||',
            '&&',
            '>',
            '<',
            '=',
            '+',
            '-',
            '*',
            '/',
            '^'
        ];
    }

    static isVariable(value)
    {
        return USMath.operators().indexOf(value) === -1;
    }

    static isOperator(value)
    {
        return USMath.operators().indexOf(value) !== -1;
    }

    static priority(operator) {
        switch (operator) {
            case '(':
                return 0;
            case ')':
                return 0;
                //
            case '||':
                return 1;
            case '&&':
                return 2;
            case '>':
                return 3;
            case '<':
                return 3;
            case '>=':
                return 3;
            case '<=':
                return 3;
            case '=':
                return 3;
            case '<>':
                return 3;
                //
            case '+':
                return 4;
            case '-':
                return 4;
            case '*':
                return 5;
            case '/':
                return 5;
            case '^':
                return 6;
            default:
                return 0;
        }
    }

    static convertToPostFix(a_infix, variables) {

        var operatorsStack = new USStack();
        var postfix = '';

        for (var i = 0; i < a_infix.length; i++) {

            if (a_infix[i].trim() != '') {
                if (USMath.isNumeric(a_infix[i])) {
                    postfix += ' ' + a_infix[i];
                } else if (USMath.isVariable(a_infix[i])) {

                    //Si es una variable lo reemplaza siempre y cuando haya variables para reemplazar
                    if (variables !== false && variables[a_infix[i]] != null) {
                        postfix += ' ' + variables[a_infix[i]];
                    } else//Si no pasa como un operando
                    {
                        if (variables === false) {
                            postfix += ' ' + Math.floor((Math.random() * 10) + 1);
                        } else {
                            postfix += ' ' + a_infix[i];
                        }
                    }

                } else {
                    switch (a_infix[i]) {
                        case '(':
                            operatorsStack.push(a_infix[i]);
                            break;
                        case ')':
                            var j = 0;
                            while (operatorsStack.top() != '(') {//Extrae los operadores hasta que se encuentre un paréntesis izquierdo                                                        
                                if (operatorsStack.top() !== null)
                                {
                                    postfix += ' ' + operatorsStack.pop();
                                    j++;
                                } else
                                {
                                    throw 'Falta un paréntesis de apertura';
                                }
                            }

                            if (j === 0)
                            {
                                throw 'Hay paréntesis sin contenido.';
                            }
                            operatorsStack.pop();
                            break;
                        default:

                            if (!USStack.hasElements(operatorsStack.stack)) {//Verifica si la pila de operadores está vacía                                  
                                operatorsStack.push(a_infix[i]);//De ser así solo colocamos el operador en la pila                            
                            } else
                            {
                                if (USMath.priority(a_infix[i]) <= USMath.priority(operatorsStack.top()))//Compara la prioridad del operador entrante con el que está en la cabecera de la pila
                                {
                                    while (USMath.priority(a_infix[i]) <= USMath.priority(operatorsStack.top()) && operatorsStack.top() != null) {//Extrae los operadores hasta que se encuentre con uno de peso mayor o se vacie la pila                                                        
                                        var operador = operatorsStack.pop();//Si es menor debemos sacar el que está en la cabecera                                        
                                        postfix += ' ' + operador;//y el que salio lo colocamos en postfix
                                    }
                                    operatorsStack.push(a_infix[i]);//Y colocar el entrante en la cabecera
                                } else
                                    operatorsStack.push(a_infix[i]);//Si el operador entrante fuese de mayor prioridad que el de la cabecera, simplemente se coloca en la pila
                            }
                            break;
                    }
                }
            }

        }

        while (USStack.hasElements(operatorsStack.stack))
            postfix += ' ' + operatorsStack.pop();
        return postfix;
    }

    static isNumeric(n) {
        return !isNaN(parseFloat(n)) && isFinite(n);
    }

    static calcExpression(infix, variables, fixed) {
        try {
            //alert(infix);
            var wildcard = ',';

            infix = infix.replace(/ /g, wildcard);

            var a1 = [infix];
            var aux = [];
            var a2 = [];
            var op = USMath.operators();
            op.push(wildcard);

            for (var x = 0; x < op.length; x++) {
                for (var y = 0; y < a1.length; y++) {
                    //Si ya s un operador mandamos al array
                    if (USMath.isOperator(a1[y]))
                    {
                        a2.push(a1[y]);
                    } else
                    {
                        aux = a1[y].split(op[x]);
                        for (var z = 0; z < aux.length; z++) {
                            if (aux[z] != '') {
                                a2.push(aux[z]);
                            }
                            if (z < aux.length - 1)
                            {
                                if (op[x] !== wildcard)
                                {
                                    a2.push(op[x]);
                                }
                            }
                        }
                    }
                }
                a1 = a2;
                a2 = [];
            }
            var postfix = USMath.convertToPostFix(a1, variables);
            var a_postfix = postfix.trim().split(' ');
            var operandsStack = new USStack();

            for (var j = 0; j < a_postfix.length; j++) {
                if (USMath.isNumeric(a_postfix[j])) {
                    operandsStack.push(parseFloat(a_postfix[j]));
                } else if (!USMath.isOperator(a_postfix[j])) {
                    operandsStack.push(a_postfix[j]);
                } else {
                    var num2 = operandsStack.pop();
                    var num1 = operandsStack.pop();

                    if (num2 === null || num1 === null) {
                        if (variables === false)
                        {
                            throw "Ocurrió un error al validar la expresión, posiblemente hay operadores seguidos o falta un paréntesis de cierre.";
                        } else
                        {
                            throw "Ocurrió un error al calcular la expresión, posiblemente hay operadores seguidos o falta un paréntesis de cierre.";
                        }
                    }
                    switch (a_postfix[j]) {
                        case '||':
                            operandsStack.push(USMath.or(num1, num2));
                            break;
                        case '&&':
                            operandsStack.push(USMath.and(num1, num2));
                            break;
                        case '>':
                            operandsStack.push(USMath.isGreaterThan(num1, num2));
                            break;
                        case '<':
                            operandsStack.push(USMath.isLessThan(num1, num2));
                            break;
                        case '>=':
                            operandsStack.push(USMath.isGreaterThanOrEqualTo(num1, num2));
                            break;
                        case '<=':
                            operandsStack.push(USMath.isLessThanOrEqualTo(num1, num2));
                            break;
                        case '=':
                            operandsStack.push(USMath.isEqualTo(num1, num2));
                            break;
                        case '<>':
                            operandsStack.push(USMath.isDifferentTo(num1, num2));
                            break;
                        case '+':
                            operandsStack.push(USMath.plus(num1, num2));
                            break;
                        case '-':
                            operandsStack.push(USMath.minus(num1, num2));
                            break;
                        case '*':
                            operandsStack.push(USMath.multiply(num1, num2));
                            break;
                        case '/':
                            operandsStack.push(USMath.divide(num1, num2));
                            break;
                        case '^':
                            operandsStack.push(USMath.pow(num1, num2));
                            break;
                    }
                }
            }

            var result = new BigNumber(operandsStack.pop()).toFixed(fixed);

            //La pila debe quedar vacia
            if (operandsStack.top() !== null)
            {
                if (variables === false)
                {
                    throw "Ocurrió un error al validar la expresión, posiblemente hay dos operandos seguidos";
                } else
                {
                    throw "Ocurrió un error al calcular la expresión, posiblemente hay dos operandos seguidos";
                }
            }
            return result;
        } catch (ex) {
            throw ex;
        }
    }

    static getMinAndMax(expression, variables, fixed)
    {
        var raw = USMath.calcExpression(expression, variables, 10);
        var initial = USMath.calcExpression(expression, variables, fixed);
        var min = initial;
        var max = initial;
        var step = Math.pow(10, -fixed);

        do {
            min -= step;
        } while (Math.abs(min - raw) <= step);

        do {
            max += step;
        } while (Math.abs(max - raw) <= step);

        return {
            min: min + step,
            max: max - step
        };
    }
}
