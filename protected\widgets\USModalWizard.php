<?php

class USModalWizard extends USModalForm {

    public $transaction;
    public $steps = 1;
    public $step = 1;
    public $footer_html;
    public $before_submit = 'true';
    public $progress_bar_id = null;
    public $previous_id = null;

    public function init() {
        $this->progress_bar_id = isset($this->progress_bar_id) ? $this->progress_bar_id : $this->controller->getServerId();
        $this->previous_id = isset($this->previous_id) ? $this->previous_id : $this->controller->getServerId();
        parent::init();
    }

    public function run() {
        parent::run();
    }

    public function progressBar() {
        echo $this->widget('booster.widgets.TbProgress', array(
            'context' => 'success', // 'success', 'info', 'warning', or 'danger'
            'percent' => (100 / $this->steps * ($this->step - 1)),
            'striped' => true,
            'animated' => true,
            'htmlOptions' => [
                'id' => $this->progress_bar_id
            ]), true);
    }

    protected function getSyncQuery() {
        return "$('#{$this->progress_bar_id} .progress-bar').css('width', '" . ((100 / $this->steps * ($this->step))) . "%');";
    }

    public function footer() {

        echo "<div class='row'>";
        echo "<div class='col-lg-8 col-md-8 col-sm-12 col-xs-12 text-left'>";
        echo $this->footer_html;
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";

        $a_buttons = [
            [
                'label' => 'Anterior',
                'icon' => 'fa fa-arrow-left fa-lg',
                'context' => 'secondary',
                'htmlOptions' => [
                    'id' => $this->previous_id,
                    'disabled' => (int) $this->step === 1
                ]
            ],
            [
                'label' => $this->step === $this->steps ? 'Finalizar' : 'Siguiente',
                'icon' => 'fa fa-arrow-right fa-lg',
                'context' => 'primary',
                'htmlOptions' => [
                    'id' => $this->submit_id,
                ]
            ]
        ];

        $this->widget('booster.widgets.TbButtonGroup', array('buttons' => $a_buttons));

        if ($this->cancel) {
            $this->widget('booster.widgets.TbButtonGroup', array('buttons' => [
                    [
                        'icon' => 'fa fa-lg fa-ban black',
                        'label' => 'Cerrar',
                        'htmlOptions' => array(
                            'id' => $this->cancel_id,
                            'data-dismiss' => 'modal'
                        )
                    ]
            ]));
        }


        echo "</div>";
        echo "</div>";
    }

    protected function getSubmitUrl() {
        $o_query = $_GET;
        $o_query['step'] = $this->step;
        $o_query['transaction'] = $this->transaction;
        $s_url = explode('?', Yii::app()->request->url)[0] . '?' . http_build_query($o_query);
        return $s_url;
    }

    protected function submitScript() {

        //Si el paso es mayor que 1 aplica el previo
        if ($this->step > 1) {

            Yii::app()->clientScript->registerScript($this->controller->getServerId(), "   
            
            $('body').on('click', '#{$this->previous_id}', function() {

                if(window.active_ajax == 0)
                {
                    $(this).prop('disabled', true);

                    waitingDialog.show(STRINGS_WAITING_MESSAGE);

                    $.ajax({
                        type: 'delete',
                        url: '{$this->getSubmitUrl()}',
                        success: function (data) {

                            waitingDialog.hide();

                            $('#' + data.modal_id).empty().html(data.content_html);

                        },
                        error: function(request, status, error) { // if error occured

                            waitingDialog.hide();

                            var s_message = processError(request.responseText);
                            bootbox.alert(us_message(s_message, 'error'));
                        }, 
                        dataType:'json'
                    });        
                    
                    $('#{$this->progress_bar_id} .progress-bar').css('width', '" . ((100 / $this->steps * ($this->step - 2))) . "%');
                }
                else
                {
                    bootbox.alert(us_message('Hay algún proceso ejecutandose, vuelva a intentarlo', 'warning'));
                }    

                return false;
            });
            ", CClientScript::POS_END);
        }

        //Si no es el paso final
        if ($this->step < $this->steps) {
            Yii::app()->clientScript->registerScript($this->controller->getServerId(), "   

            $('body').on('click', '#{$this->submit_id}', function() {
                
                if(window.active_ajax == 0)
                {
                    $(this).prop('disabled', true);
                    
                    if({$this->before_submit})
                    {
                        if ( typeof CKEDITOR !== 'undefined' ) {
                            for (instance in CKEDITOR.instances)
                            {
                                //var value = CKEDITOR.instances[instance].getData();
                                //$('#' + instance ).val(value);
                                CKEDITOR.instances[instance].updateElement()
                            }
                        }

                        waitingDialog.show(STRINGS_WAITING_MESSAGE);

                        $.ajax({
                            type: 'post',
                             url: '{$this->getSubmitUrl()}',
                            data: $('#{$this->getForm()->id}').serialize(),
                            success: function (data) {
                                waitingDialog.hide();

                                if(data.success)
                                {
                                    $('#' + data.modal_id).empty().html(data.content_html);
                                }
                                else
                                {
                                    $('#' + data.modal_id).empty().html(data.content_html);
                                    $('#' + data.modal_id).find('input.error,textarea.error,select.error').filter(':visible:first').focus();
                                }
                            },
                            error: function(request, status, error) { // if error occured
                                waitingDialog.hide();

                                var s_message = processError(request.responseText);
                                bootbox.alert(us_message(s_message, 'error'));
                            }, 
                            dataType:'json'
                        });    
                        
                        {$this->getSyncQuery()}
                    }
                    else
                    {
                        $(this).prop('disabled', false);
                    }
                }
                else
                {
                    bootbox.alert(us_message('Hay algún proceso ejecutandose, vuelva a intentarlo', 'warning'));
                }    

                return false;
            });
        ", CClientScript::POS_END);
        } else {
            parent::submitScript();
        }
    }

}
