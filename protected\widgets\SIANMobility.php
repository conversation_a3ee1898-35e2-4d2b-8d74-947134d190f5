<?php

class SIANMobility extends CWidget {

    public $id;
    public $model;
    public $form;
    public $currency_items;
    public $readonly = false;
    public $fixed = MobilityMovement::TFIXED;
    public $step = MobilityMovement::TSTEP;
    public $validate_combination_input_id = null;
    public $combination_min_input_length = 2;
    //private
    private $autocomplete_id;
    private $item_person_id;
    private $item_name_id;
    private $item_identification_id;
    private $item_station_id;
    private $item_area_id;
    private $item_area_combination_id;
    private $item_combination_id;
    private $add_button_id;
    //---
    //Control
    private $currency_symbol;
    //Controller
    private $controller;
    private $mobilityPerson;
    private $mobilityPersonItem;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->mobilityPerson = MobilityPerson::model();
        $this->mobilityPersonItem = MobilityPersonItem::model();

        //GRID ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->validate_combination_input_id = isset($this->validate_combination_input_id) ? $this->validate_combination_input_id : $this->controller->getServerId();
        //IDS
        $this->autocomplete_id = $this->controller->getServerId();
        $this->item_person_id = $this->controller->getServerId();
        $this->item_name_id = $this->controller->getServerId();
        $this->item_identification_id = $this->controller->getServerId();
        $this->item_station_id = $this->controller->getServerId();
        $this->item_area_id = $this->controller->getServerId();
        $this->item_area_combination_id = $this->controller->getServerId();
        $this->item_combination_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();
        //VAR
        $this->currency_symbol = $this->model->movement->getCurrencySymbol();

        $personItems = [];

        foreach ($this->model->tempItems as $mPerson) {
            $mPersonAttributes = [
                'person_id' => $mPerson->person_id,
                'person_name' => $mPerson->person_name,
                'identification_number' => $mPerson->identification_number,
                'station_name' => $mPerson->station_name,
                'area_name' => $mPerson->area_name,
                'area_combination_id' => $mPerson->area_combination_id,
                'combination_id' => $mPerson->combination_id,
                'combination_name' => $mPerson->combination_name,
            ];

            $mItems = [];
            foreach ($mPerson->tempItems as $mItem) {
                $mItemAttributes = [
                    'register_date' => $mItem->register_date,
                    'reason' => isset($mItem->reason) ? $mItem->reason : '',
                    'displacement' => isset($mItem->displacement) ? $mItem->displacement : '',
                    'type' => $mItem->type,
                    'amount' => $mItem->{"amount_{$this->model->movement->currency}"},
                    'errors' => $mItem->getErrors()
                ];

                $mItems[] = $mItemAttributes;
            }

            $mPersonAttributes['items'] = $mItems;
            $mPersonAttributes['errors'] = $mPerson->getErrors();

            $personItems[] = $mPersonAttributes;
        }


        //Registramos assets
        SIANAssets::registerScriptFile('other/select2/js/select2.min.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
        SIANAssets::registerScriptFile('js/us-span.js');
        SIANAssets::registerScriptFile('js/us-select2.js');
        SIANAssets::registerScriptFile('js/us-cost-level.js');
        SIANAssets::registerScriptFile('js/sian-mobility.js');
        SIANAssets::registerScriptFile("other/jquery.datetimepicker/js/jquery.datetimepicker.js");
        SIANAssets::registerCssFile("other/jquery.datetimepicker/css/jquery.datetimepicker.css");
        SIANAssets::registerCssFile('other/select2/css/select2.css');
        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        //COUNT    
        $('#{$this->id}').data('fixed', {$this->fixed});
        $('#{$this->id}').data('step', {$this->step});
        $('#{$this->id}').data('currency', '{$this->model->movement->currency}');
        $('#{$this->id}').data('readonly', " . CJSON::encode($this->readonly) . ");
        $('#{$this->id}').data('count', 0);
        $('#{$this->id}').data('labels', {
            identification_number: '{$this->mobilityPerson->getAttributeLabel('identification_number')}',
            station_name: '{$this->mobilityPerson->getAttributeLabel('station_name')}',
            area_name: '{$this->mobilityPerson->getAttributeLabel('area_name')}',
            combination_id: '{$this->mobilityPerson->getAttributeLabel('combination_id')}',
            combination_name: '{$this->mobilityPerson->getAttributeLabel('combination_name')}',
            reason: '{$this->mobilityPersonItem->getAttributeLabel('reason')}',
            displacement: '{$this->mobilityPersonItem->getAttributeLabel('displacement')}',
            register_date: '{$this->mobilityPersonItem->getAttributeLabel('register_date')}',
            type: '{$this->mobilityPersonItem->getAttributeLabel('type')}',
            amount: '{$this->mobilityPersonItem->getAttributeLabel("amount_{$this->model->movement->currency}")}',
        });

        $('#{$this->id}').data('type_items_html', '" . Util::arrayToOptions(MobilityPersonItem::getTypeItems(), null, true) . "');

        $(document).ready(function() {                  

            //MOVIMIENTOS
            var array = " . CJSON::encode($personItems) . ";

            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {
                    SIANMobilityAddPerson('{$this->id}', array[i]['person_id'], array[i]['person_name'], array[i]['identification_number'], array[i]['station_name'], array[i]['area_name'], array[i]['area_combination_id'], array[i]['combination_id'], array[i]['combination_name'], array[i]['errors'], " . ($this->readonly ? 'true' : 'false') . ");

                    for (var j = 0; j < array[i]['items'].length; j++)
                    {
                        SIANMobilityAddItem('{$this->id}', array[i]['person_id'], array[i]['items'][j]['register_date'], array[i]['items'][j]['reason'], array[i]['items'][j]['displacement'], array[i]['items'][j]['type'], array[i]['items'][j]['amount'], array[i]['items'][j]['errors'], " . ($this->readonly ? 'true' : 'false') . ");
                    }
                }
            }

            //UPDATE
            SIANMobilityUpdate('{$this->id}');
            unfocusable();
        });
        
        //SI CAMBIA LA OPERACION
        $('#{$this->id}').data('changeOperation', function(changeData){

            //Cargamos CC
            $('#{$this->id}').data('target_ids', changeData.combination_ids);
            $('#{$this->id}').data('validate_combination', changeData.has_combination);
            $('#{$this->validate_combination_input_id}').val(changeData.has_combination ? 1: 0);
            
            USSpanSetData('{$this->item_combination_id}', {
                target_ids: changeData.combination_ids,
            });
            USSpanSetReadonly('{$this->item_combination_id}', !changeData.has_combination || isBlank(USAutocompleteField('{$this->autocomplete_id}', 'value')));
            USSpanSetRequired('{$this->item_combination_id}', changeData.has_combination);
                
            $('#{$this->id}').find('span.sian-mobility-person-combination-id').each(function( index ) {
                var selectObj = $(this);
                var span_id = selectObj.attr('id');
                USSpanSetData(span_id, {
                    target_ids: changeData.combination_ids,
                });
                USSpanSetReadonly(span_id, !changeData.has_combination);
                USSpanSetRequired(span_id, changeData.has_combination);
            });   
        });
        
        //SI CAMBIA LA MONEDA
        $('#{$this->id}').data('changeCurrency', function(currency){

            var div = $('#{$this->id}');
            var exchange_rate = div.data('exchange_rate');

            div.find('tr.sian-mobility-item').each(function(index) {

                var item = $(this);

                //Actualizamos montos (exchange_rate y currency ya son variables predefinidas)
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-mobility-item-total').toPen(exchange_rate, {$this->fixed});
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-mobility-item-total').toUsd(exchange_rate, {$this->fixed});
                    break;
                }
            });
            
            div.data('currency', currency);

            SIANMobilityUpdate('{$this->id}');
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));

        });    
        
        $('#{$this->id}').data('changeExchange', function(exchange_rate){
            var div = $('#{$this->id}');
            //Seteamos
            div.data('exchange_rate', exchange_rate); 
        });
        
        $('#{$this->id}').data('changeBusinessUnit', function(changeData){
           
            var divObj = $('#{$this->id}');
            divObj.data('business_unit_combination_id', changeData.combination_id);
            
            //Obtenemos combinación de area
            SIANMobilityRefreshOverwriteIds('{$this->id}', '{$this->item_combination_id}', $('#{$this->item_area_combination_id}'));
            SIANMobilityRefreshAllOverwriteIds('{$this->id}');
        });
        
        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var person_id = $('#{$this->item_person_id}').val();
            var person_name = $('#{$this->item_name_id}').val();
            var identification_number = $('#{$this->item_identification_id}').val();
            var station_name = $('#{$this->item_station_id}').val();  
            var area_name = $('#{$this->item_area_id}').val(); 
            var area_combination_id = $('#{$this->item_area_combination_id}').val(); 
            var combination_id = USSpanGetValue('{$this->item_combination_id}'); 
            var combination_name = USSpanGetText('{$this->item_combination_id}'); 

            if (person_id.length === 0)
            {
                $('#{$this->item_person_id}').focus();
                return;
            }
                
            SIANMobilityAddPerson('{$this->id}', person_id, person_name, identification_number, station_name, area_name, area_combination_id, combination_id, combination_name, [], false);
            SIANMobilityAddItem('{$this->id}', person_id, now(), '', '', '', 0, [], false);
            //Limpiamos
            USAutocompleteReset('{$this->autocomplete_id}');
            USSpanClear('{$this->item_combination_id}')
                
            //FOCUS
            $('#{$this->item_identification_id}').focus();
            SIANMobilityUpdate('{$this->id}');
            unfocusable();
        });
        
        $('body').on('click', 'span.sian-mobility-person-combination-id', function(e) {

            var spanObj = $(this);
            var span_id = spanObj.attr('id');

            USCostLevelInit(span_id, 0, {$this->combination_min_input_length}, 400);
        });
      
        ", CClientScript::POS_END);

        if (!$this->readonly) {
            Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            $('#{$this->id}').data('changeEmission', function(changeData){

                $('#{$this->id} input.sian-mobility-item-date').datetimepicker({
                     maxDate: changeData.emission_date,
                });
            }); 
        ", CClientScript::POS_END);
        }
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Empleados',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => ($this->model->hasErrors('tempItems') ? 'us-error' : '')
            )
        ));

        if (!$this->readonly) {
            echo "<div class='row'>";
            echo "<div class='col-lg-5 col-md-5 col-sm-5 col-xs-12'>";
            $this->widget('application.widgets.USAutocomplete', array(
                'id' => $this->autocomplete_id,
                'name' => null,
                'label' => $this->mobilityPerson->getAttributeLabel('person_name'),
                'value_id' => $this->item_person_id,
                'aux_id' => $this->item_identification_id,
                'text_id' => $this->item_name_id,
                'view' => array(
                    'model' => 'DpBusinessPartner',
                    'scenario' => DpBusinessPartner::SCENARIO_ASSOCIATED,
                    'attributes' => array(
                        array(
                            'name' => 'person_id',
                            'types' => array('id', 'value'),
                            'hidden' => true,
                            'not_in' => 'window.personIds'
                        ),
                        array(
                            'name' => 'identification_name',
                            'width' => 10,
                            'search' => false
                        ),
                        array(
                            'name' => 'identification_number',
                            'width' => 10,
                            'types' => array('aux'),
                        ),
                        array(
                            'name' => 'person_name',
                            'width' => 40,
                            'types' => array('text')
                        ),
                        array(
                            'name' => 'station_name',
                            'width' => 20,
                            'update' => "$('#{$this->item_station_id}').val(station_name)"
                        ),
                        array(
                            'name' => 'area_name',
                            'width' => 20,
                            'update' => "$('#{$this->item_area_id}').val(area_name)"
                        ),
                        array(
                            'name' => 'combination_id',
                            'hidden' => true,
                        ),
                        array(
                            'name' => 'combination_name',
                            'hidden' => true,
                        ),
                    )
                ),
                'onselect' => "
                        var validate_combination = $('#{$this->id}').data('validate_combination');
                        
                        if(validate_combination == 1)
                        {
                            $('#{$this->item_area_combination_id}').val(combination_id);
                            USSpanSetReadonly('{$this->item_combination_id}', false);
                            SIANMobilityRefreshOverwriteIds('{$this->id}', '{$this->item_combination_id}', $('#{$this->item_area_combination_id}'));
                        }
                    ",
                'onreset' => "
                        $('#{$this->item_station_id}').val(null);
                        $('#{$this->item_area_id}').val(null);
                        USSpanClear('{$this->item_combination_id}');
                        USSpanSetReadonly('{$this->item_combination_id}', true);
                    "
            ));
            echo "</div>";
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
            echo SIANForm::textFieldNonActive($this->mobilityPerson->getAttributeLabel('station_name'), null, null, array(
                'id' => $this->item_station_id,
                'disabled' => true
            ));
            echo "</div>";
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
            echo SIANForm::textFieldNonActive($this->mobilityPerson->getAttributeLabel('area_name'), null, null, array(
                'id' => $this->item_area_id,
                'disabled' => true
            ));
            echo CHtml::hiddenField(null, null, array(
                'id' => $this->item_area_combination_id,
                'class' => 'sian-mobility-person-area-combination-id',
                'disabled' => true
            ));
            echo "</div>";
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
            $this->widget('application.widgets.USCostLevel', array(
                'id' => $this->item_combination_id,
                'form' => $this->form,
                'label' => $this->mobilityPerson->getAttributeLabel('combination_id'),
                'minimumInputLength' => 3,
                'all' => false,
                'readonly' => true
            ));
            echo "</div>";
            echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
            echo CHtml::label('Agregar', $this->add_button_id, []);
            echo "<br/>";
            $this->widget('application.widgets.USButtons', array(
                'buttons' => array(
                    array(
                        'id' => $this->add_button_id,
                        'context' => 'primary',
                        'icon' => 'fa fa-lg fa-plus white',
                        'size' => 'default',
                        'title' => 'Añadir'
                    )
                )
            ));
            echo "</div>";
            echo "</div>";
            echo "<hr>";
        }

        echo "<div id='{$this->id}'><center>" . Strings::NO_DATA . "</center></div>";
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo "<div class='well pull-right extended-summary ' style='padding: 0px !important;'>";
        echo "<table>";
        echo "<tr>";
        echo "<td><b>Total:</b></td>";
        echo "<td style='text-align:right'>";
        echo "<span class='currency-symbol'>{$this->currency_symbol}</span> <span class='sian-mobility-total'>0.00</span>";
        echo $this->form->hiddenField($this->model, 'validate_combination', array(
            'id' => $this->validate_combination_input_id,
            'class' => 'sian-mobility-validate-combination',
            'readonly' => true,
        ));
        echo "</td>";
        echo "</tr>";
        echo "</table>";
        echo "</div>";
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

}
