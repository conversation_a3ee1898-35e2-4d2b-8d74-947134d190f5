<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of USDatabase
 *
 * <AUTHOR>
 */
class USDatabase {

    public static function multipleInsert($modelClass, array $data, $ignore = false) {

        $ignore = ($ignore) ? 'IGNORE' : '';

        if (count($data) > 0) {
            $modelObj = $modelClass::model();

            $templates = array(
                'main' => "INSERT {$ignore} INTO {{tableName}} ({{columnInsertNames}}) VALUES {{rowInsertValues}}",
                'columnInsertValue' => '{{value}}',
                'columnInsertValueGlue' => ', ',
                'rowInsertValue' => '({{columnInsertValues}})',
                'rowInsertValueGlue' => ', ',
                'columnInsertNameGlue' => ', ',
            );

            $table = $modelObj->dbConnection->schema->getTable($modelObj->tableName());
            $tableName = $modelObj->dbConnection->quoteTableName($table->name);
            $params = [];
            $columnInsertNames = [];
            $rowInsertValues = [];

            $columns = [];
            foreach ($data as $rowData) {
                foreach ($rowData as $columnName => $columnValue) {
                    if (!in_array($columnName, $columns, true))
                        if ($table->getColumn($columnName) !== null)
                            $columns[] = $columnName;
                }
            }
            foreach ($columns as $name)
                $columnInsertNames[$name] = $modelObj->dbConnection->quoteColumnName($name);
            $columnInsertNamesSqlPart = implode($templates['columnInsertNameGlue'], $columnInsertNames);

            foreach ($data as $rowKey => $rowData) {
                $columnInsertValues = [];
                foreach ($columns as $columnName) {
                    $column = $table->getColumn($columnName);
                    $columnValue = array_key_exists($columnName, $rowData) ? $rowData[$columnName] : new CDbExpression('NULL');
                    if ($columnValue instanceof CDbExpression) {
                        $columnInsertValue = $columnValue->expression;
                        foreach ($columnValue->params as $columnValueParamName => $columnValueParam)
                            $params[$columnValueParamName] = $columnValueParam;
                    } else {
                        $columnInsertValue = ':' . $columnName . '_' . $rowKey;
                        $params[':' . $columnName . '_' . $rowKey] = $column->typecast($columnValue);
                    }
                    $columnInsertValues[] = strtr($templates['columnInsertValue'], array(
                        '{{column}}' => $columnInsertNames[$columnName],
                        '{{value}}' => $columnInsertValue,
                    ));
                }
                $rowInsertValues[] = strtr($templates['rowInsertValue'], array(
                    '{{tableName}}' => $tableName,
                    '{{columnInsertNames}}' => $columnInsertNamesSqlPart,
                    '{{columnInsertValues}}' => implode($templates['columnInsertValueGlue'], $columnInsertValues)
                ));
            }

            $sql = strtr($templates['main'], array(
                '{{tableName}}' => $tableName,
                '{{columnInsertNames}}' => $columnInsertNamesSqlPart,
                '{{rowInsertValues}}' => implode($templates['rowInsertValueGlue'], $rowInsertValues),
            ));
            $command = $modelObj->dbConnection->createCommand($sql);

            foreach ($params as $name => $value)
                $command->bindValue($name, $value);

            return $command->execute();
        } else {
            return false;
        }
    }

    public static function multipleReplace($modelClass, array $data) {

        if (count($data) > 0) {
            $modelObj = $modelClass::model();

            $templates = array(
                'main' => "REPLACE INTO {{tableName}} ({{columnInsertNames}}) VALUES {{rowInsertValues}}",
                'columnInsertValue' => '{{value}}',
                'columnInsertValueGlue' => ', ',
                'rowInsertValue' => '({{columnInsertValues}})',
                'rowInsertValueGlue' => ', ',
                'columnInsertNameGlue' => ', ',
            );

            $table = $modelObj->dbConnection->schema->getTable($modelObj->tableName());
            $tableName = $modelObj->dbConnection->quoteTableName($table->name);
            $params = [];
            $columnInsertNames = [];
            $rowInsertValues = [];

            $columns = [];
            foreach ($data as $rowData) {
                foreach ($rowData as $columnName => $columnValue) {
                    if (!in_array($columnName, $columns, true))
                        if ($table->getColumn($columnName) !== null)
                            $columns[] = $columnName;
                }
            }
            foreach ($columns as $name)
                $columnInsertNames[$name] = $modelObj->dbConnection->quoteColumnName($name);
            $columnInsertNamesSqlPart = implode($templates['columnInsertNameGlue'], $columnInsertNames);

            foreach ($data as $rowKey => $rowData) {
                $columnInsertValues = [];
                foreach ($columns as $columnName) {
                    $column = $table->getColumn($columnName);
                    $columnValue = array_key_exists($columnName, $rowData) ? $rowData[$columnName] : new CDbExpression('NULL');
                    if ($columnValue instanceof CDbExpression) {
                        $columnInsertValue = $columnValue->expression;
                        foreach ($columnValue->params as $columnValueParamName => $columnValueParam)
                            $params[$columnValueParamName] = $columnValueParam;
                    } else {
                        $columnInsertValue = ':' . $columnName . '_' . $rowKey;
                        $params[':' . $columnName . '_' . $rowKey] = $column->typecast($columnValue);
                    }
                    $columnInsertValues[] = strtr($templates['columnInsertValue'], array(
                        '{{column}}' => $columnInsertNames[$columnName],
                        '{{value}}' => $columnInsertValue,
                    ));
                }
                $rowInsertValues[] = strtr($templates['rowInsertValue'], array(
                    '{{tableName}}' => $tableName,
                    '{{columnInsertNames}}' => $columnInsertNamesSqlPart,
                    '{{columnInsertValues}}' => implode($templates['columnInsertValueGlue'], $columnInsertValues)
                ));
            }

            $sql = strtr($templates['main'], array(
                '{{tableName}}' => $tableName,
                '{{columnInsertNames}}' => $columnInsertNamesSqlPart,
                '{{rowInsertValues}}' => implode($templates['rowInsertValueGlue'], $rowInsertValues),
            ));
            $command = $modelObj->dbConnection->createCommand($sql);

            foreach ($params as $name => $value)
                $command->bindValue($name, $value);

            return $command->execute();
        } else {
            return false;
        }
    }

}
