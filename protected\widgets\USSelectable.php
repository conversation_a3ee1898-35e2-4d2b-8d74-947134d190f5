<?php

class USSelectable extends CWidget {

    public $id;
    public $form;
    public $name = 'Item';
    public $models;
    public $keyAttribute;
    public $textAttribute;
    public $existingKeys = [];
    public $maintenance = null;
    //PRIVATE
    private $controller;
    private $filter_id;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //MAINTENANCE
        if (isset($this->maintenance)) {
            if (!isset($this->maintenance['buttons'])) {
                $this->maintenance['buttons'] = array(
                    'create' => [],
                    'update' => [],
                    'delete' => [],
                    'preview' => []
                );
            }

            //PARA CADA BUTTON
            foreach ($this->maintenance['buttons'] as $action => $parameters) {
                //URL
                $this->maintenance['buttons'][$action]['url'] = $this->controller->createUrl("/{$this->maintenance['module']}/{$this->maintenance['controller']}/{$action}");
                //ID
                if (!isset($parameters['id'])) {
                    $this->maintenance['buttons'][$action]['id'] = $this->controller->getServerId();
                }
                //DATA
                if (!isset($parameters['data'])) {
                    $this->maintenance['buttons'][$action]['data'] = [];
                }

                //ACCESS
                if (!isset($parameters['access'])) {
                    $this->maintenance['buttons'][$action]['access'] = $this->controller->checkRoute("/{$this->maintenance['module']}/{$this->maintenance['controller']}/{$action}");
                }
            }
        }
        //PRIVATE
        $this->filter_id = $this->controller->getServerId();
        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id, "

        $('body').on('keyup', '#{$this->filter_id}', function(event){

            var text = $(this).val();
            
            var div = $('#{$this->id}');

            div.find('div.ui-selectee').each(function(index) {
            
                var item = $(this);
                
                if(item.find('span.us-selectable-text').text().toLowerCase().indexOf(text.toLowerCase()) >= 0)
                {
                    item.show();
                }
                else
                {
                    item.hide();
                }
            });
        });

        $('#{$this->id}').selectable({
            create: function(event, ui){
                $.each(" . json_encode($this->existingKeys) . ", function( index, value ) {
                    var element = $('#{$this->id} #us-selectable-' + value);
                    element.addClass('ui-selected');
                    $('#' + element.data('id')).prop('disabled', false);
                });
            },
            selected: function(event, ui){
                var element = $(ui.selected);
                $('#' + element.data('id')).prop('disabled', false);
            },
            unselected: function(event, ui){
                var element = $(ui.unselected);
                $('#' + element.data('id')).prop('disabled', true);
            }
        });
        
//        $(function(){
//            $.contextMenu({
//                selector: '.us-selectable-item', 
//                items: $.contextMenu.fromMenu($('#html5menu'))
//            });
//        });

//        $(function() {
//            $.contextMenu({
//                selector: '.us-selectable-item', 
//                callback: function(key, options) {
//
//                },
//                items: {
//                    'paste': {name: 'Visualizar', icon: 'paste', type: 'text', disabled: " . json_encode(!isset($this->maintenance) ? $this->maintenance['buttons']['preview']['access'] : false) . "},
//                    'previewx': {name: 'Visualizar', icon: 'eye', disabled: " . json_encode(!isset($this->maintenance) ? $this->maintenance['buttons']['preview']['access'] : false) . "},
//                    'update': {name: 'Editar', icon: 'edit', disabled: " . json_encode(!isset($this->maintenance) ? $this->maintenance['buttons']['update']['access'] : false) . "},
//                    'delete': {name: 'Eliminar', icon: 'delete', disabled: " . json_encode(!isset($this->maintenance) ? $this->maintenance['buttons']['delete']['access'] : false) . "},
//                }
//            }); 
//        });
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Seleccione los elementos, para seleccionar varios o deseleccionar mantenga presionada la tecla Ctrl',
            'headerIcon' => 'list'
        ));

        echo SIANForm::textFieldNonActive(false, null, null, array(
            'id' => $this->filter_id,
            'placeholder' => 'Filtro',
        ));
        echo "<hr>";

        $hidden = '';
        echo "<div id='{$this->id}' class='us-selectable row'>";
        foreach ($this->models as $model) {
            $id = $this->controller->getServerId();
            $key = $model->{$this->keyAttribute};
            $text = $model->{$this->textAttribute};
            echo "<div id='us-selectable-{$key}' class='us-selectable-item col-lg-2 col-md-2 col-sm-2 col-xs-6' data-id='{$id}'>";
            echo "<div class='ui-state-default thumbnail text-center'>";
            echo "<span class='us-selectable-text'>{$text}</span>";
            echo "</div>";
            $hidden .= CHtml::hiddenField("{$this->name}[]", $key, array('id' => $id, 'disabled' => true));
            echo "</div>";
        }
        echo "</div>";
        echo "<div>{$hidden}</div>";
//        echo "<menu type='context'>";
//        echo "<menu id='html5menu' style='display:none' class='showcase'>";
//        echo "<command label='rotate' icon='edit' onclick='alert(\"rotate\")'>";
//        echo "<command label='resize' onclick='alert(\"resize\")'> ";
//        echo "<command label='twitter' onclick='alert(\"twitter\"')'>";
//        echo "<hr> ";
//        echo "<command label='facebook' onclick='alert(\"facebook\")'>";
//        echo "</menu>";
//        echo "</menu>";
        //Completar menú click derecho
        //http://swisnl.github.io/jQuery-contextMenu/demo/html5-import.html#example-html
        
        
        $this->endWidget();
    }

}
