<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'us-constants.php');
include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'us-default.php');

// This is the configuration for yiic console application.
// Any writable CConsoleApplication properties can be configured here.
Yii::setPathOfAlias('us', dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'us');

$environment = YII_ENVIRONMENT_PRODUCTION;

switch ($environment) {
    case YII_ENVIRONMENT_DEVELOPMENT:
        $database_sian_server = '127.0.0.1';
        $database_sian_port = '4040';
        $database_sian_username = 'root';
        $database_sian_password = 'Sist3m@s';
        break;
    case YII_ENVIRONMENT_STAGING:
    case YII_ENVIRONMENT_TESTING:
        $database_sian_server = '161.132.48.88';
        $database_sian_port = '3306';
        $database_sian_username = 'sian_test';
        $database_sian_password = '75nppt6vr57lx4';
        break;
    case YII_ENVIRONMENT_PRODUCTION:
        $database_sian_server = '161.132.48.88';
        $database_sian_port = '3306';
        $database_sian_username = 'sian';
        $database_sian_password = '75nppt6pf1gpje';
        break;
}

return array(
    'basePath' => dirname(__FILE__) . DIRECTORY_SEPARATOR . '..',
    'name' => 'SIAN Console',
    // preloading 'log' component
    'preload' => array('log', 'us'),
    'import' => array(
        'application.apis.*',
        'application.classes.*',
        'application.components.*',
        'application.controllers.*',
        'application.extended.*',
        'application.fake.*',
        'application.traits.*',
        'application.models.*',
        'application.models.forms.*',
        'application.models.procedures.*',
        'application.models.procedures.ability.*',
        'application.models.procedures.control.*',
        'application.models.dataProviders.*',
        'application.models.dataProviders.auto.*',
        'application.models.dataProviders.grid.*',
        'application.models.dataProviders.print.*',
        'application.models.dataProviders.reports.*',
        'application.models.dataProviders.subgrid.*',
        'application.models.dataProviders.ws.*',
        'application.models.extended.*',
        'application.models.procedures.forms.*',
        'application.models.procedures.sh.*',
        'application.models.procedures.validators.*',
        'application.models.procedures.ws.*',
        'application.models.sian.*',
        'application.models.system.*',
        'application.models.views.*',
        'application.models.views.grid.*',
        'application.models.views.system.*',
        'application.models.views.system.ability.*',
        'us.helpers.*',
    ),
    // application components
    'components' => array(
        'dbSian' => array(
            'class' => 'CDbConnection',
            'connectionString' => "mysql:host={$database_sian_server};port={$database_sian_port};dbname=sian",
            'emulatePrepare' => true,
            'username' => $database_sian_username,
            'password' => $database_sian_password,
            'charset' => 'utf8',
            'enableProfiling' => true,
            'enableParamLogging' => true,
            'schemaCachingDuration' => 86400,
            'persistent' => true,
            'initSQLs' => [],
            'charset' => 'utf8',
        ),
        'cache' => array(
            'class' => 'CRedisCache',
            'hostname' => '127.0.0.1',
            'port' => 6379,
            'database' => 0,
            'password' => 'hbs', //The REDIS password
            'options' => STREAM_CLIENT_CONNECT,
        ),
        'log' => array(
            'class' => 'CLogRouter',
            'routes' => array(
                array(
                    'class' => 'CFileLogRoute',
                    'levels' => 'error, warning',
                ),
            ),
        ),
    ),
    'params' => array(
        //FORMATS
        'datetime_php_format' => $datetime_php_format,
        'date_php_format' => $date_php_format,
        'time_php_format' => $time_php_format,
        'datetime_sql_format' => $datetime_sql_format,
        'date_sql_format' => $date_sql_format,
        'date_js_format' => $date_js_format,
        //RUTAS
        'environment' => $environment,
        //
        'us_dir' => $dir . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . $us,
        'hbs_url' => "{$hbs_url}",
        'hbs_username' => "{$hbs_username}",
        'hbs_password' => "{$hbs_password}",
    ),
);
