window.sianComboIds = [];

function SIANCombo2AddItem(div_id, sub_product_id, product_type, product_name, pres_quantity, presentationItems, sub_equivalence, allow_decimals, mprice, imprice, aprice, iaprice, wprice, iwprice, cost, icost, initial, errors)
{
    //VARIABLES DE DIV
    var divObj = $('#' + div_id);
    var access = divObj.data('view-access');
    var url = divObj.data('view-url');
    var ifixed = divObj.data('ifixed');
    var tfixed = divObj.data('tfixed');
    var istep = divObj.data('istep');
    var tstep = divObj.data('tstep');
    var modal_id = divObj.data('modal_id');
    var price_mode = divObj.data('price_mode');
    var link_prices = divObj.data('link_prices');

    //VARIABLES DE TABLA
    var count = parseInt(divObj.data('count'));

    //ID
    var id = getLocalId();
    var product_name_input_id = getLocalId();
    var pres_quantity_input_id = getLocalId();
    var equivalence_input_id = getLocalId();
    var row = '';
    row += '<tr id=\'' + id + '\' class=\'sian-combo2-item sian-combo2-item-' + sub_product_id + '\'>';

    row += '<td style=\'text-align:center\'><span class=\'sian-combo2-item-index\'></span></td>';

    row += '<td class=\'form-group\'>';
    row += '<input type=\'hidden\' class=\'form-control sian-combo2-item-sub-product-id\' name=\'Item[' + id + '][sub_product_id]\' value=\'' + (isset(sub_product_id) ? sub_product_id : '') + '\' readonly>';
    row += '<span class=\'sian-combo2-item-sub-product-id\'>' + sub_product_id + '</span>';
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['product_name'] ? 'has-error' : '') + '\'>';
    row += '<input id=\'' + product_name_input_id + '\' type=\'text\' class=\'form-control sian-combo2-item-product-name\' name=\'Item[' + id + '][product_name]\' value=\'' + product_name + '\' readonly>';
    if (errors['product_name'])
    {
        row += '<span class=\'help-block error\'>' + errors['product_name'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group\'>';
    row += '<input type=\'text\' class=\'form-control sian-combo2-item-product-type\' name=\'Item[' + id + '][product_type]\' value=\'' + product_type + '\' readonly>';
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['pres_quantity'] ? 'has-error' : '') + '\'>';
    row += '<input id=\'' + pres_quantity_input_id + '\' type=\'number\' class=\'form-control sian-combo2-item-pres-quantity ' + (allow_decimals == 1 ? 'us-double2' : 'us-double0') + '\' style=\'text-align:right\' name=\'Item[' + id + '][pres_quantity]\' step=' + toStep(allow_decimals ? 2 : 0) + ' min=' + toStep(allow_decimals ? 2 : 0) + ' value=\'' + pres_quantity + '\' onchange="SIANCombo2UpdateAmounts (\'' + div_id + '\', true, \'i\');">';
    if (errors['pres_quantity'])
    {
        row += '<span class=\'help-block error\'>' + errors['pres_quantity'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group\'>';
    row += '<select id=\'' + equivalence_input_id + '\' class=\'sian-combo2-item-sub-equivalence form-control\' name=\'Item[' + id + '][sub_equivalence]\'></select>';
    row += '<input type=\'hidden\' class=\'sian-combo2-item-allow-decimals\' value=\'' + allow_decimals + '\' name=\'Item[' + id + '][allow_decimals]\'>';
    row += '<input type=\'hidden\' class=\'sian-combo2-item-cost\' value=\'' + cost + '\' name=\'Item[' + id + '][cost]\'>';
    row += '<input type=\'hidden\' class=\'sian-combo2-item-icost\' value=\'' + icost + '\' name=\'Item[' + id + '][icost]\'>';
    row += '</td>';


    row += '<td class=\'form-group sian-combo2-without-igv ' + (errors['mprice'] ? 'has-error' : '') + '\' ' + (price_mode == 0 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo2-price-input sian-combo2-item-mprice us-double' + ifixed + '\' style=\'text-align:right\' value=\'' + mprice + '\' min=\'0\' name=\'Item[' + id + '][mprice]\' step=' + istep + ' onchange="SIANCombo2ChangePrice($(this), \'mprice\'); SIANCombo2UpdateAmounts (\'' + div_id + '\', true, \'i\');" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['mprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['mprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group sian-combo2-with-igv ' + (errors['imprice'] ? 'has-error' : '') + '\' ' + (price_mode == 1 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo2-price-input sian-combo2-item-imprice us-double' + tfixed + '\' style=\'text-align:right\' value=\'' + imprice + '\' min=\'0\' name=\'Item[' + id + '][imprice]\' step=' + tstep + ' onchange="SIANCombo2ChangePrice($(this), \'imprice\'); SIANCombo2UpdateAmounts (\'' + div_id + '\', true, \'i\');" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['imprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['imprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group sian-combo2-without-igv ' + (errors['aprice'] ? 'has-error' : '') + '\' ' + (price_mode == 0 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo2-price-input sian-combo2-item-aprice us-double' + ifixed + '\' style=\'text-align:right\' value=\'' + aprice + '\' min=\'0\' name=\'Item[' + id + '][aprice]\' step=' + istep + ' onchange="SIANCombo2ChangePrice($(this), \'aprice\'); SIANCombo2UpdateAmounts (\'' + div_id + '\', true, \'i\');" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['aprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['aprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group sian-combo2-with-igv ' + (errors['iaprice'] ? 'has-error' : '') + '\' ' + (price_mode == 1 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo2-price-input sian-combo2-item-iaprice us-double' + tfixed + '\' style=\'text-align:right\' value=\'' + iaprice + '\' min=\'0\' name=\'Item[' + id + '][iaprice]\' step=' + tstep + ' onchange="SIANCombo2ChangePrice($(this), \'iaprice\'); SIANCombo2UpdateAmounts (\'' + div_id + '\', true, \'i\');" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['iaprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['iaprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group sian-combo2-without-igv ' + (errors['wprice'] ? 'has-error' : '') + '\' ' + (price_mode == 0 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo2-price-input sian-combo2-item-wprice us-double' + ifixed + '\' style=\'text-align:right\' value=\'' + wprice + '\' min=\'0\' name=\'Item[' + id + '][wprice]\' step=' + istep + ' onchange="SIANCombo2ChangePrice($(this), \'wprice\'); SIANCombo2UpdateAmounts (\'' + div_id + '\', true, \'i\');" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['wprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['wprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group sian-combo2-with-igv ' + (errors['iwprice'] ? 'has-error' : '') + '\' ' + (price_mode == 1 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo2-price-input sian-combo2-item-iwprice us-double' + tfixed + '\' style=\'text-align:right\' value=\'' + iwprice + '\' min=\'0\' name=\'Item[' + id + '][iwprice]\' step=' + tstep + ' onchange="SIANCombo2ChangePrice($(this), \'iwprice\'); SIANCombo2UpdateAmounts (\'' + div_id + '\', true, \'i\');" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['iwprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['iwprice'] + '</span>';
    }
    row += '</td>';

    row += '<td style=\'text-align:right;\'>';
    if (access == 1)
    {
        row += ' <a title=\'Ver producto\' class=\'form\' url=\'' + url + '?id=' + sub_product_id + '\' data-parent_id=\'' + modal_id + '\' data-modal_id=\'' + getLocalId() + '\'><span class=\'fa fa-eye fa-lg black\'></span></a>';
    } else
    {
        row += ' <span class=\'fa fa-eye fa-lg light-grey\'></span>';
    }
    row += ' <a onclick=\'SIANCombo2RemoveItem(\"' + div_id + '\", \"' + id + '\", true)\' title=\'Quitar ítem de la lista\'><span class=\'fa fa-times fa-lg black\'></span></a>';
    row += '</td>';


    row += '</tr>';

    //COUNT DE ITEMS
    if (count === 0)
    {
        divObj.find('table.sian-combo2-items tbody').html(row);
    } else
    {
        divObj.find('table.sian-combo2-items tbody').append(row);
    }

    divObj.data('count', count + 1);

    //Seteamos data original
    $('#' + pres_quantity_input_id).data('original_quantity', pres_quantity);
    //Si no es grupo llenamos el combo
    SIANCombo2FillPresentations(equivalence_input_id, presentationItems, sub_equivalence);
    SIANCombo2SetInfoCosts(div_id, equivalence_input_id);

    if (initial == 0) {
        SIANCombo2SetInfoPresentation(div_id, equivalence_input_id);
    }
}

function SIANCombo2FillPresentations(input_id, presentationItems, equivalence)
{
    var inputObj = $('#' + input_id);
    var selected = null;

    inputObj
            .empty()
            .append('<option value>' + STRINGS_SELECT_OPTION + '</option>')
            .data('presentationItems', presentationItems);

    $.each(presentationItems, function (index, presentationObj) {
        inputObj.append('<option value=\'' + presentationObj['equivalence'] + '\' data-currency=\'' + presentationObj['currency'] + '\' data-mprice=' + presentationObj['mprice'] + ' data-aprice=' + presentationObj['aprice'] + ' data-wprice=' + presentationObj['wprice'] + ' data-imprice=' + presentationObj['imprice'] + ' data-iaprice=' + presentationObj['iaprice'] + ' data-iwprice=' + presentationObj['iwprice'] + ' data-cost=' + presentationObj['cost'] + ' data-icost=' + presentationObj['icost'] + '>' + presentationObj['measure_name'] + '</option>');

        if (presentationObj['equivalence'] == equivalence)
        {
            selected = presentationObj;
        }
    });

    inputObj.val(selected ? selected.equivalence : null);

    return selected;
}

function SIANCombo2SetInfoPresentation(div_id, select_id)
{
    var row = $('#' + select_id).closest('tr');
    var divObj = $('#' + div_id);

    var ifixed = divObj.data('ifixed');
    //
    var equivalenceObj = row.find('select.sian-combo2-item-sub-equivalence');
    var mPriceObj = row.find('input.sian-combo2-item-mprice');
    var imPriceObj = row.find('input.sian-combo2-item-imprice');
    var aPriceObj = row.find('input.sian-combo2-item-aprice');
    var iaPriceObj = row.find('input.sian-combo2-item-iaprice');
    var wPriceObj = row.find('input.sian-combo2-item-wprice');
    var iwPriceObj = row.find('input.sian-combo2-item-iwprice');

    SIANCombo2SetPrices(equivalenceObj, mPriceObj, imPriceObj, aPriceObj, iaPriceObj, wPriceObj, iwPriceObj, ifixed);
    SIANCombo2UpdateAmounts(div_id, true, 'i');
}

function SIANCombo2SetInfoCosts(div_id, select_id)
{
    var row = $('#' + select_id).closest('tr');
    var divObj = $('#' + div_id);

    var ifixed = divObj.data('ifixed');
    //
    var equivalenceObj = row.find('select.sian-combo2-item-sub-equivalence');
    var costObj = row.find('input.sian-combo2-item-cost');
    var icostObj = row.find('input.sian-combo2-item-icost');

    SIANCombo2SetCosts(equivalenceObj, costObj, icostObj, ifixed);
    SIANCombo2UpdateAmounts(div_id, true, 'c');
}

function SIANCombo2GetPresentations(div_id, product_id, equivalence)
{
    var divObj = $('#' + div_id);
    var presentation_url = divObj.data('presentation_url');
    var presentation_mode = divObj.data('presentation_mode');
    var currency = divObj.data('currency');
    var ifixed = divObj.data('ifixed');
    //IDS
    var item_equivalence_id = divObj.data('item_equivalence_id');
    var item_mprice_id = divObj.data('item_mprice_id');
    var item_imprice_id = divObj.data('item_imprice_id');
    var item_aprice_id = divObj.data('item_aprice_id');
    var item_iaprice_id = divObj.data('item_iaprice_id');
    var item_wprice_id = divObj.data('item_wprice_id');
    var item_iwprice_id = divObj.data('item_iwprice_id');
    var item_cost_id = divObj.data('item_cost_id');
    var item_icost_id = divObj.data('item_icost_id');

    if (product_id != undefined)
    {
        $.ajax({
            type: 'post',
            url: presentation_url,
            data: {
                mode: presentation_mode,
                product_ids: [product_id],
                currency: currency,
                get_cost: 1
            },
            beforeSend: function (xhr) {
                window.active_ajax++;
                //Ocultamos los tooltip
                $('div.ui-tooltip').remove();
            },
            success: function (data) {

                $.each(data, function (index, item) {
                    SIANCombo2FillPresentations(item_equivalence_id, item, equivalence);
                });

                SIANCombo2SetPrices($('#' + item_equivalence_id), $('#' + item_mprice_id), $('#' + item_imprice_id), $('#' + item_aprice_id), $('#' + item_iaprice_id), $('#' + item_wprice_id), $('#' + item_iwprice_id), ifixed);
                SIANCombo2SetCosts($('#' + item_equivalence_id), $('#' + item_cost_id), $('#' + item_icost_id), ifixed);
                window.active_ajax--;
            },
            error: function (request, status, error) { // if error occured
                window.active_ajax--;
                bootbox.alert(us_message(request.responseText, 'error'));
            },
            dataType: 'json'
        });
    }
}

function SIANCombo2SetPrices(equivalenceObj, mPriceObj, imPriceObj, aPriceObj, iaPriceObj, wPriceObj, iwPriceObj, ifixed)
{
    var option = equivalenceObj.find(':selected');
    var equivalence = option.val();
    var mprice = option.floatData('mprice', ifixed);
    var imprice = option.floatData('imprice', ifixed);
    var aprice = option.floatData('aprice', ifixed);
    var iaprice = option.floatData('iaprice', ifixed);
    var wprice = option.floatData('wprice', ifixed);
    var iwprice = option.floatData('iwprice', ifixed);
    mprice = (isBlank(mprice) || isNaN(mprice) || mprice === undefined) ? 0 : mprice;
    imprice = (isBlank(imprice) || isNaN(imprice) || imprice === undefined) ? 0 : imprice;
    aprice = (isBlank(aprice) || isNaN(aprice) || aprice === undefined) ? 0 : aprice;
    iaprice = (isBlank(iaprice) || isNaN(iaprice) || iaprice === undefined) ? 0 : iaprice;
    wprice = (isBlank(wprice) || isNaN(wprice) || wprice === undefined) ? 0 : wprice;
    iwprice = (isBlank(iwprice) || isNaN(iwprice) || iaprice === undefined) ? 0 : iwprice;
    equivalenceObj.val(equivalence);
    mPriceObj.floatVal(ifixed, mprice);
    imPriceObj.floatVal(ifixed, imprice);
    aPriceObj.val(aprice.toFixed(ifixed)).prop('min', mprice.toFixed(ifixed));
    iaPriceObj.val(iaprice.toFixed(ifixed)).prop('min', imprice.toFixed(ifixed));
    wPriceObj.floatVal(ifixed, wprice);
    iwPriceObj.floatVal(ifixed, iwprice);
}

function SIANCombo2SetCosts(equivalenceObj, costObj, icostObj, ifixed)
{
    var option = equivalenceObj.find(':selected');
    var cost = option.floatData('cost', ifixed);
    var icost = option.floatData('icost', ifixed);
    cost = (isBlank(cost) || isNaN(cost) || cost === undefined) ? 0 : cost;
    icost = (isBlank(icost) || isNaN(icost) || icost === undefined) ? 0 : icost;
    costObj.floatVal(ifixed, cost);
    icostObj.floatVal(ifixed, icost);
}

function SIANCombo2UpdateAmounts(div_id, forced, origin) {
    //VARIABLES
    var divObj = $('#' + div_id);
    var presentation_item_id = divObj.data('presentation_item_id');
    var price_mode = divObj.data('price_mode');
    var ifixed = divObj.data('ifixed');
    var tfixed = divObj.data('tfixed');
    var igv = divObj.data('igv');
    var price_mode = divObj.data('price_mode', price_mode);
    var allowStockProducts = divObj.data('allowStockProducts');
    var objProducts = [];
    if (origin === 'i') {

        //MONTOS
        var tmprice = 0;
        var timprice = 0;
        var taprice = 0;
        var tiaprice = 0;
        var twprice = 0;
        var tiwprice = 0;

        //Obtenemos tabla principal
        divObj.find('table.sian-combo2-items tr.sian-combo2-item').each(function (index) {

            var rowObj = $(this);
            //
            var sub_pres_quantity = rowObj.find('input.sian-combo2-item-pres-quantity').floatVal(2);
            var mprice = rowObj.find('input.sian-combo2-item-mprice').floatVal(ifixed);
            var imprice = rowObj.find('input.sian-combo2-item-imprice').floatVal(ifixed);
            var aprice = rowObj.find('input.sian-combo2-item-aprice').floatVal(ifixed);
            var iaprice = rowObj.find('input.sian-combo2-item-iaprice').floatVal(ifixed);
            var wprice = rowObj.find('input.sian-combo2-item-wprice').floatVal(ifixed);
            var iwprice = rowObj.find('input.sian-combo2-item-iwprice').floatVal(ifixed);
            var product_type = rowObj.find('input.sian-combo2-item-product-type').val();

            rowObj.find('span.sian-combo2-item-index').floatText(0, index + 1);

            tmprice = USMath.plus(tmprice, USMath.multiply(sub_pres_quantity, mprice));
            timprice = USMath.plus(timprice, USMath.multiply(sub_pres_quantity, imprice));
            taprice = USMath.plus(taprice, USMath.multiply(sub_pres_quantity, aprice));
            tiaprice = USMath.plus(tiaprice, USMath.multiply(sub_pres_quantity, iaprice));
            twprice = USMath.plus(twprice, USMath.multiply(sub_pres_quantity, wprice));
            tiwprice = USMath.plus(tiwprice, USMath.multiply(sub_pres_quantity, iwprice));
            if (product_type == PRODUCT_TYPE_MERCHANDISE) {
                objProducts.push(rowObj.find('input.sian-combo2-item-sub-product-id').val());
            }
        });

        if (allowStockProducts == 1) {
            divObj.find('a.sian-combo2-stock-link').data('product_ids', objProducts.join(','));
            if (objProducts.length == 0) {
                divObj.find('a.sian-combo2-stock-link').attr('disabled', true);
            } else {
                divObj.find('a.sian-combo2-stock-link').removeAttr('disabled');
            }
        }

        if (price_mode == 1)
        {
            tmprice = USMath.divide(timprice, 1 + igv, tfixed);
            taprice = USMath.divide(tiaprice, 1 + igv, tfixed);
            twprice = USMath.divide(tiwprice, 1 + igv, tfixed);
        } else
        {
            timprice = USMath.multiply(tmprice, 1 + igv, tfixed);
            tiaprice = USMath.multiply(taprice, 1 + igv, tfixed);
            tiwprice = USMath.multiply(twprice, 1 + igv, tfixed);
        }

        //Setear en presentation, Deberia ser tequivalence/pres_quantity pero en este caso es 1
        var rowObj = $('#' + presentation_item_id);
        rowObj.find('input.sian-combo2-presentation-mprice').floatVal(ifixed, tmprice);
        rowObj.find('input.sian-combo2-presentation-imprice').floatVal(tfixed, timprice);
        rowObj.find('input.sian-combo2-presentation-aprice').floatVal(ifixed, taprice);
        rowObj.find('input.sian-combo2-presentation-iaprice').floatVal(tfixed, tiaprice);
        rowObj.find('input.sian-combo2-presentation-wprice').floatVal(ifixed, twprice);
        rowObj.find('input.sian-combo2-presentation-iwprice').floatVal(tfixed, tiwprice);

        SIANCombo2UpdateAmounts(div_id, forced, 'c');
    } else {
        if (origin === 'c') {
            var cost_total = 0;
            var icost_total = 0;
            //Obtenemos tabla principal
            divObj.find('table.sian-combo2-items tr.sian-combo2-item').each(function (index) {
                var rowObj = $(this);
                var sub_pres_quantity = rowObj.find('input.sian-combo2-item-pres-quantity').floatVal(2);
                var cost = rowObj.find('input.sian-combo2-item-cost').floatVal(ifixed);
                var icost = rowObj.find('input.sian-combo2-item-icost').floatVal(ifixed);
                cost_total = USMath.plus(cost_total, USMath.multiply(sub_pres_quantity, cost));
                icost_total = USMath.plus(icost_total, USMath.multiply(sub_pres_quantity, icost));
            });
            //var icost_total = USMath.multiply(cost_total, (1 + igv));
            var rowPresentationObj = $('#' + presentation_item_id);
            rowPresentationObj.find('input.sian-combo2-presentation-cost').floatVal(ifixed, cost_total);
            rowPresentationObj.find('input.sian-combo2-presentation-icost').floatVal(ifixed, icost_total);
        } else {
            var tableItemsObj = $('#' + div_id + '_table');
            var combo_items = tableItemsObj.find('tr.sian-combo2-item');
            var margin = $('#' + presentation_item_id).find('input.sian-combo2-presentation-' + origin + 'margin').floatVal(2) / 100;
            var price_total = $('#' + presentation_item_id).find('input.sian-combo2-presentation-' + origin + 'price').floatVal(2);
            var price_acum = 0;

            //Obtenemos tabla principal
            combo_items.each(function (index) {
                var rowObj = $(this);
                var sub_pres_quantity = rowObj.find('input.sian-combo2-item-pres-quantity').floatVal(2);
                var cost = rowObj.find('input.sian-combo2-item-cost').floatVal(ifixed);
                var price_new = 0;
                var iprice_new = 0;

                if (index === combo_items.length - 1) {
                    price_new = USMath.round((price_total - price_acum) / sub_pres_quantity, 2, 1);
                } else {
                    price_new = USMath.multiply(cost, (1 + margin));
                }
                price_acum = USMath.plus(price_acum, USMath.round(USMath.multiply(sub_pres_quantity, price_new), 2, 1));
                iprice_new = USMath.multiply(price_new, (1 + igv));
                rowObj.find('input.sian-combo2-item-' + origin + 'price').val(price_new);
                rowObj.find('input.sian-combo2-item-i' + origin + 'price').val(iprice_new);
            });
        }
    }
}

function SIANCombo2ChangePrice(element, field)
{
    var rowObj = element.closest('tr');
    var divObj = rowObj.closest('div.sian-combo2');
    var igv = divObj.floatData('igv', 3);
    var ifixed = divObj.data('ifixed');
    var tfixed = divObj.data('tfixed');

    switch (field)
    {
        case 'mprice':

            var mprice = rowObj.find('input.sian-combo2-item-mprice').floatVal(ifixed);
            var aprice = rowObj.find('input.sian-combo2-item-aprice').floatVal(ifixed);
            var imprice = mprice * (1 + igv);

            //ACTUALIZAMOS EL PRECIO MINIMO CON IGV
            rowObj.find('input.sian-combo2-item-imprice').floatVal(tfixed, imprice);
            //SI EL PRECIO MINIMO AHORA ES MAYOR QUE EL PRECIO NORMAL
            if (mprice > aprice) {
                rowObj.find('input.sian-combo2-item-aprice').floatVal(ifixed, mprice);
                rowObj.find('input.sian-combo2-item-iaprice').floatVal(tfixed, imprice);
            }
            //EL MINIMO DEL PRECIO PROMEDIO SERA EL PRECIO MINIMO
            rowObj.find('input.sian-combo2-item-aprice').floatAttr('min', ifixed, mprice);
            rowObj.find('input.sian-combo2-item-iaprice').floatAttr('min', tfixed, imprice);

            break;
        case 'imprice':

            var imprice = rowObj.find('input.sian-combo2-item-imprice').floatVal(tfixed);
            var aprice = rowObj.find('input.sian-combo2-item-aprice').floatVal(ifixed);
            var mprice = imprice / (1 + igv);
            //ACTUALIZAMOS EL PRECIO MINIMO
            rowObj.find('input.sian-combo2-item-mprice').floatVal(ifixed, mprice);
            //SI EL PRECIO MINIMO AHORA ES MAYOR QUE EL PRECIO NORMAL
            if (mprice > aprice) {
                rowObj.find('input.sian-combo2-item-aprice').floatVal(ifixed, mprice);
                rowObj.find('input.sian-combo2-item-iaprice').floatVal(tfixed, imprice);
            }
            //EL MINIMO DEL PRECIO PROMEDIO SERA EL PRECIO MINIMO
            rowObj.find('input.sian-combo2-item-aprice').floatAttr('min', ifixed, mprice);
            rowObj.find('input.sian-combo2-item-iaprice').floatAttr('min', tfixed, imprice);

            break;
        case 'aprice':

            var aprice = rowObj.find('input.sian-combo2-item-aprice').floatVal(ifixed);
            var iaprice = aprice * (1 + igv);

            //ACTUALIZAMOS EL PRECIO PROMEDIO CON IGV
            rowObj.find('input.sian-combo2-item-iaprice').floatVal(tfixed, iaprice);

            break;
        case 'iaprice':

            var iaprice = rowObj.find('input.sian-combo2-item-iaprice').floatVal(tfixed);
            var aprice = iaprice / (1 + igv);

            //ACTUALIZAMOS EL PRECIO PROMEDIO
            rowObj.find('input.sian-combo2-item-aprice').floatVal(ifixed, aprice);

            break;
        case 'wprice':

            var wprice = rowObj.find('input.sian-combo2-item-wprice').floatVal(ifixed);
            var iwprice = wprice * (1 + igv);

            //ACTUALIZAMOS EL PRECIO WEB CON IGV
            rowObj.find('input.sian-combo2-item-iwprice').floatVal(tfixed, iwprice);

            break;
        case 'iwprice':

            var iwprice = rowObj.find('input.sian-combo2-item-iwprice').floatVal(tfixed);
            var wprice = iwprice / (1 + igv);

            //ACTUALIZAMOS EL PRECIO PROMEDIO
            rowObj.find('input.sian-combo2-item-wprice').floatVal(ifixed, wprice);

            break;
    }
}

function SIANCombo2ChangePriceMode(div_id, price_mode)
{
    var divObj = $('#' + div_id);
    divObj.data('price_mode', price_mode);

    if (price_mode == 1)
    {
        divObj.find('.sian-combo2-with-igv').show(0);
        divObj.find('.sian-combo2-without-igv').hide(0);
        divObj.find('.sian-combo2-presentation-with-igv').show(0);
        divObj.find('.sian-combo2-presentation-without-igv').hide(0);

    } else
    {
        divObj.find('.sian-combo2-with-igv').hide(0);
        divObj.find('.sian-combo2-without-igv').show(0);
        divObj.find('.sian-combo2-presentation-with-igv').hide(0);
        divObj.find('.sian-combo2-presentation-without-igv').show(0);
    }
}

function SIANCombo2ChangeLinkPrices(div_id, link_prices)
{
    var divObj = $('#' + div_id);
    divObj.data('link_prices', link_prices);

    var ifixed = divObj.data('ifixed');

    divObj.find('input.sian-combo2-price-input').attr('readonly', link_prices == 1);

    //Restauramos precios
    if (link_prices == 1)
    {
        //Obtenemos tabla principal
        divObj.find('table.sian-combo2-items tr.sian-combo2-item').each(function (index) {
            var rowObj = $(this);
            var equivalenceObj = rowObj.find('select.sian-combo2-item-sub-equivalence');
            var mPriceObj = rowObj.find('input.sian-combo2-item-mprice');
            var imPriceObj = rowObj.find('input.sian-combo2-item-imprice');
            var aPriceObj = rowObj.find('input.sian-combo2-item-aprice');
            var iaPriceObj = rowObj.find('input.sian-combo2-item-iaprice');
            var wPriceObj = rowObj.find('input.sian-combo2-item-wprice');
            var iwPriceObj = rowObj.find('input.sian-combo2-item-iwprice');

            SIANCombo2SetPrices(equivalenceObj, mPriceObj, imPriceObj, aPriceObj, iaPriceObj, wPriceObj, iwPriceObj, ifixed);

        });

        SIANCombo2UpdateAmounts(div_id, true, 'i');
    }

}

function SIANCombo2RemoveItem(div_id, instance_id, confirmation)
{
    if (confirmation ? confirm('¿Está seguro de quitar este ítem de la lista?') : true)
    {
        var divObj = $('#' + div_id);

        var rowObj = $('#' + instance_id);
        rowObj.remove();

        var count = parseInt(divObj.data('count'));

        //COUNT DE ITEMS
        divObj.data('count', count - 1);

        SIANCombo2GetIds(div_id);
        SIANCombo2UpdateAmounts(div_id, true, 'i');
    }
}

function SIANCombo2GetIds(div_id)
{

    window.sianComboIds.splice(0, window.sianComboIds.length);

    var divObj = $('#' + div_id);
    //Recorremos rows
    divObj.find('table.sian-combo2-items tr.sian-combo2-item').each(function (index) {

        var rowObj = $(this);
        var product_id = rowObj.find('input.sian-combo2-item-sub-product-id').val();
        window.sianComboIds.push(product_id);
    });

    if (divObj.data('count') === 0)
    {
        divObj.find('table.sian-combo2-items tbody').html(SIANCombo2GetNullRow());
    }
}

function SIANCombo2GetNullRow()
{
    return '<tr><td class=\'empty\' colspan=\'99\'>' + STRINGS_NO_DATA + '</td></tr>';
}



// Funciones para la sección de presentaciones
// ===========================================

function SIANCombo2AddPresentation(div_id, tr_id, equivalence, min_stock, measure_id, abbreviation, measure_name, currency, mmargin, mprice, imprice, amargin, aprice, iaprice, wmargin, wprice, iwprice, xdefault, lock_equivalence, lock_name, lock_combo, errors)
{
    //TABLE
    var divObj = $('#' + div_id);
    var table_id = divObj.data('grid_id');
    var tableObj = $('#' + table_id);
    //
    var count = parseInt(tableObj.data('count'));
    var product_type = tableObj.data('product_type');
    var allow_decimals = tableObj.data('allow_decimals');
    var ifixed = tableObj.data('ifixed');
    var tfixed = tableObj.data('tfixed');
    var istep = tableObj.data('istep');
    var tstep = tableObj.data('tstep');
    var lock_default = tableObj.data('lock_default');
    var price_mode = tableObj.data('price_mode');

    var id = tr_id;
    //HTML
    var row = '';
    row += '<tr id="' + id + '" class="sian-combo2-presentation">';

    row += '<td class="form-group sian-combo2-presentation-basic ' + (errors['measure_id'] ? 'has-error' : '') + '">';
    row += '<input type="hidden" class="form-control sian-presentation-item-measure_id" name="Presentation[' + id + '][measure_id]" value=' + measure_id + '>';
    row += '<input type="text" class="form-control sian-presentation-item-abbreviation" name="Presentation[' + id + '][abbreviation]" value="' + abbreviation + '" readonly>';
    row += '<input type="hidden"  class="form-control sian-presentation-item-measure-name" name="Presentation[' + id + '][measure_name]" value=' + measure_name + '>';
    row += '<input class="form-control sian-combo2-presentation-equivalence ' + (allow_decimals == 1 ? 'us-double2' : 'us-double0') + '" type="hidden" name="Presentation[' + id + '][equivalence]" value="' + equivalence + '" min=' + (allow_decimals == 1 ? '0.01' : '1') + ' step=' + (allow_decimals == 1 ? '0.01' : '1') + ' ' + (lock_equivalence == 1 || lock_combo == 1 ? 'readonly' : '') + '>';
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic sian-combo2-presentation-without-igv " ' + (price_mode == 0 ? '' : 'style="display: none;"') + '>';
    row += '<input class="form-control sian-combo2-presentation-cost us-double2" type="number" name="Presentation[' + id + '][cost]" value="' + 0 + '" min="0" step="0.01" readonly>';    
    row += '</td>';
    
    row += '<td class="form-group sian-combo2-basic sian-combo2-presentation-with-igv " ' + (price_mode == 1 ? '' : 'style="display: none;"') + '>';    
    row += '<input class="form-control sian-combo2-presentation-icost us-double2" type="number" name="Presentation[' + id + '][icost]" value="' + 0 + '" min="0" step="0.01" readonly>';
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic ' + (errors['min_stock'] ? 'has-error' : '') + '" >';
    row += '<input class="form-control sian-combo2-presentation-min-stock" type="number" name="Presentation[' + id + '][min_stock]" value="' + min_stock + '" min=0 ' + (product_type === PRODUCT_TYPE_SERVICE ? 'readonly' : '') + '>';
    if (errors['min_stock'])
    {
        row += '<span class="help-block error">' + errors['min_stock'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic">';
    row += '<input class="form-control sian-combo2-presentation-mmargin us-double2" type="number" name="Presentation[' + id + '][mmargin]" value="' + mmargin + '" min="0" max="100" step="1" onchange="SIANCombo2PresentationUpdateItem(\'' + div_id + '\',\'' + id + '\', \'mprice\');">';
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic sian-combo2-without-igv ' + (errors['mprice'] ? 'has-error' : '') + '" ' + (price_mode == 0 ? '' : 'style="display: none;"') + '>';
    row += '<input class="form-control sian-combo2-presentation-mprice us-double' + ifixed + '" type="number" name="PresentationStore[' + id + '][mprice]" value="' + mprice + '" min=0 step=' + istep + ' onchange="SIANCombo2PresentationChangePrice(\'' + div_id + '\',\'' + id + '\', \'mprice\')">';
    if (errors['mprice'])
    {
        row += '<span class="help-block error">' + errors['mprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic sian-combo2-with-igv ' + (errors['imprice'] ? 'has-error' : '') + '" ' + (price_mode == 1 ? '' : 'style="display: none;"') + '>';
    row += '<input class="form-control sian-combo2-presentation-imprice us-double' + tfixed + '" type="number" name="PresentationStore[' + id + '][imprice]" value="' + imprice + '" min=0 step=' + tstep + ' onchange="SIANCombo2PresentationChangePrice(\'' + div_id + '\',\'' + id + '\', \'imprice\')">';
    if (errors['imprice'])
    {
        row += '<span class="help-block error">' + errors['imprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic">';
    row += '<input class="form-control sian-combo2-presentation-amargin us-double2" type="number" name="PresentationStore[' + id + '][amargin]" value="' + amargin + '" min="0" max="100" step="1"  onchange="SIANCombo2PresentationUpdateItem(\'' + div_id + '\',\'' + id + '\', \'aprice\');">';
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic sian-combo2-without-igv ' + (errors['aprice'] ? 'has-error' : '') + '" ' + (price_mode == 0 ? '' : 'style="display: none;"') + '>';
    row += '<input class="form-control sian-combo2-presentation-aprice us-double' + ifixed + '" type="number" name="PresentationStore[' + id + '][aprice]" value="' + aprice + '" min=0 step=' + istep + ' onchange="SIANCombo2PresentationChangePrice(\'' + div_id + '\',\'' + id + '\', \'aprice\')">';
    if (errors['aprice'])
    {
        row += '<span class="help-block error">' + errors['aprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic sian-combo2-with-igv ' + (errors['iaprice'] ? 'has-error' : '') + '" ' + (price_mode == 1 ? '' : 'style="display: none;"') + '>';
    row += '<input class="form-control sian-combo2-presentation-iaprice us-double' + tfixed + '" type="number" name="PresentationStore[' + id + '][iaprice]" value="' + iaprice + '" min=0 step=' + tstep + ' onchange="SIANCombo2PresentationChangePrice(\'' + div_id + '\',\'' + id + '\', \'iaprice\')">';
    if (errors['iaprice'])
    {
        row += '<span class="help-block error">' + errors['iaprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic">';
    row += '<input class="form-control sian-combo2-presentation-wmargin us-double2" type="number" name="PresentationStore[' + id + '][wmargin]" value="' + wmargin + '" min="0" max="100" step="1"  onchange="SIANCombo2PresentationUpdateItem(\'' + div_id + '\',\'' + id + '\', \'wprice\');">';
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic sian-combo2-without-igv ' + (errors['wprice'] ? 'has-error' : '') + '" ' + (price_mode == 0 ? '' : 'style="display: none;"') + '>';
    row += '<input class="form-control sian-combo2-presentation-wprice us-double' + ifixed + '" type="number" name="PresentationStore[' + id + '][wprice]" value="' + wprice + '" min=0 step=' + istep + ' onchange="SIANCombo2PresentationChangePrice(\'' + div_id + '\',\'' + id + '\', \'wprice\')">';
    if (errors['wprice'])
    {
        row += '<span class="help-block error">' + errors['wprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic sian-combo2-with-igv ' + (errors['iwprice'] ? 'has-error' : '') + '" ' + (price_mode == 1 ? '' : 'style="display: none;"') + '>';
    row += '<input class="form-control sian-combo2-presentation-iwprice us-double' + tfixed + '" type="number" name="PresentationStore[' + id + '][iwprice]" value="' + iwprice + '" min=0 step=' + tstep + ' onchange="SIANCombo2PresentationChangePrice(\'' + div_id + '\',\'' + id + '\', \'iwprice\')">';
    if (errors['iwprice'])
    {
        row += '<span class="help-block error">' + errors['iwprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class="form-group sian-combo2-basic">';
    row += '<input class="sian-combo2-presentation-default" type="radio" name="Presentation[' + id + '][default]" value= 1 ' + (xdefault == 1 ? 'checked' : '') + ' ' + (lock_default ? 'readonly' : '') + '>';
    row += '</td>';

    row += '<td>';
    row += '<input class="sian-combo2-presentation-currency" type="hidden" name="Presentation[' + id + '][currency]" value="' + currency + '">';
    row += '<input type="hidden" name="Presentation[' + id + '][lock_name]" value=' + lock_name + '>';
    row += '<input type="hidden" class="sian-combo2-presentation-lock-equivalence" name="Presentation[' + id + '][lock_equivalence]" value=' + lock_equivalence + '>';
    row += '<input type="hidden" name="Presentation[' + id + '][lock_combo]" value=' + lock_combo + '>';

    row += '<span class="fa fa-times fa-lg light-grey"></span>';
    row += '</td>';
    row += '</tr>';

    //COUNT DE ITEMS
    if (count === 0)
    {
        tableObj.find('tbody').html(row);
    } else
    {
        tableObj.find('tbody').append(row);
    }
    tableObj.data('count', count + 1);
}

function SIANCombo2PresentationUpdateItem(div_id, item_id, field)
{
    var divObj = $('#' + div_id);
    var table_id = divObj.data('grid_id');
    var tableObj = $('#' + table_id);
    var item = $('#' + item_id);
    var tfixed = tableObj.data('tfixed');
    var igv = divObj.floatData('igv', 3);
    var ref_cost = item.find('input.sian-combo2-presentation-cost').val();

    if (field == 'mprice' || field == '') {
        var mmargin = parseFloat(item.find('input.sian-combo2-presentation-mmargin').val()) / 100;
        var imprice = USMath.roundHalfAndInteger(ref_cost * (1 + mmargin) * (1 + (igv)));
        var mprice = USMath.round(imprice / (1 + (igv)), tfixed, USMath.ROUND_HALF_UP);
        item.find('input.sian-combo2-presentation-mprice').val(mprice.toString());
        item.find('input.sian-combo2-presentation-imprice').floatVal(tfixed, imprice);
        SIANCombo2UpdateAmounts(div_id, true, 'm');
    }

    if (field == 'aprice' || field == '') {
        var amargin = parseFloat(item.find('input.sian-combo2-presentation-amargin').val()) / 100;
        var iaprice = USMath.roundHalfAndInteger(ref_cost * (1 + amargin) * (1 + (igv)));
        var aprice = USMath.round(iaprice / (1 + (igv)), tfixed, USMath.ROUND_HALF_UP);
        item.find('input.sian-combo2-presentation-aprice').val(aprice.toString());
        item.find('input.sian-combo2-presentation-iaprice').floatVal(tfixed, iaprice);
        SIANCombo2UpdateAmounts(div_id, true, 'a');
    }

    if (field == 'wprice' || field == '') {
        var wmargin = parseFloat(item.find('input.sian-combo2-presentation-wmargin').val()) / 100;
        var iwprice = USMath.roundHalfAndInteger(ref_cost * (1 + wmargin) * (1 + (igv)));
        var wprice = USMath.round(iwprice / (1 + (igv)), tfixed, USMath.ROUND_HALF_UP);
        item.find('input.sian-combo2-presentation-wprice').val(wprice.toString());
        item.find('input.sian-combo2-presentation-iwprice').floatVal(tfixed, iwprice);
        SIANCombo2UpdateAmounts(div_id, true, 'w');
    }
}

function SIANCombo2PresentationChangePrice(div_id, row_id, field)
{
    var row = $('#' + row_id);
    var table = row.closest('table.sian-combo2-presentation-table');
    var igv = table.data('igv');
    var ifixed = table.data('ifixed');
    var tfixed = table.data('tfixed');

    var cost = row.find('input.sian-combo2-presentation-cost').floatVal(ifixed);
    var icost = row.find('input.sian-combo2-presentation-icost').floatVal(ifixed);

    switch (field)
    {
        case 'mprice':

            var mprice = row.find('input.sian-combo2-presentation-mprice').floatVal(ifixed);
            var aprice = row.find('input.sian-combo2-presentation-aprice').floatVal(ifixed);
            var imprice = mprice * (1 + igv);
            var mmargin = USMath.round(((mprice / cost) - 1) * 100, 2);

            //ACTUALIZAMOS EL PRECIO MINIMO CON IGV
            row.find('input.sian-combo2-presentation-imprice').floatVal(tfixed, imprice);
            row.find('input.sian-combo2-presentation-amargin').floatVal(tfixed, mmargin);

            //SI EL PRECIO MINIMO AHORA ES MAYOR QUE EL PRECIO NORMAL
            if (mprice > aprice) {
                row.find('input.sian-combo2-presentation-aprice').floatVal(ifixed, mprice);
                row.find('input.sian-combo2-presentation-iaprice').floatVal(tfixed, imprice);
            }
            //EL MINIMO DEL PRECIO PROMEDIO SERA EL PRECIO MINIMO
            row.find('input.sian-combo2-presentation-aprice').floatAttr('min', ifixed, mprice);
            row.find('input.sian-combo2-presentation-iaprice').floatAttr('min', tfixed, imprice);
            SIANCombo2UpdateAmounts(div_id, true, 'm');

            break;
        case 'imprice':
            var imprice = row.find('input.sian-combo2-presentation-imprice').floatVal(ifixed);
            var aprice = row.find('input.sian-combo2-presentation-aprice').floatVal(ifixed);
            var mprice = imprice / (1 + igv);
            var mmargin = USMath.round(((imprice / icost) - 1) * 100, 2);
            //ACTUALIZAMOS EL PRECIO MINIMO
            row.find('input.sian-combo2-presentation-mprice').floatVal(ifixed, mprice);
            row.find('input.sian-combo2-presentation-mmargin').floatVal(ifixed, mmargin);
            //SI EL PRECIO MINIMO AHORA ES MAYOR QUE EL PRECIO NORMAL
            if (mprice > aprice) {
                row.find('input.sian-combo2-presentation-aprice').floatVal(ifixed, mprice);
                row.find('input.sian-combo2-presentation-iaprice').floatVal(tfixed, imprice);
            }
            //EL MINIMO DEL PRECIO PROMEDIO SERA EL PRECIO MINIMO
            row.find('input.sian-combo2-presentation-aprice').floatAttr('min', ifixed, mprice);
            row.find('input.sian-combo2-presentation-iaprice').floatAttr('min', tfixed, imprice);
            SIANCombo2UpdateAmounts(div_id, true, 'm');

            break;
        case 'aprice':

            var element = row.find('input.sian-combo2-presentation-aprice');
            var mprice = row.find('input.sian-combo2-presentation-mprice').floatVal(ifixed);
            var aprice = row.find('input.sian-combo2-presentation-aprice').floatVal(ifixed);
            var iaprice = aprice * (1 + igv);
            var amargin = USMath.round(((aprice / cost) - 1) * 100, 2);

            //ACTUALIZAMOS EL PRECIO PROMEDIO CON IGV
            row.find('input.sian-combo2-presentation-iaprice').floatVal(tfixed, iaprice);
            row.find('input.sian-combo2-presentation-amargin').floatVal(tfixed, amargin);

            SIANCombo2PresentationShowAlert(element, mprice, aprice);
            SIANCombo2UpdateAmounts(div_id, true, 'a');
            break;
        case 'iaprice':

            var element = row.find('input.sian-combo2-presentation-iaprice');
            var imprice = row.find('input.sian-combo2-presentation-imprice').floatVal(ifixed);
            var iaprice = row.find('input.sian-combo2-presentation-iaprice').floatVal(tfixed);
            var aprice = iaprice / (1 + igv);
            var amargin = USMath.round(((iaprice / icost) - 1) * 100, 2);

            //ACTUALIZAMOS EL PRECIO PROMEDIO
            row.find('input.sian-combo2-presentation-aprice').floatVal(ifixed, aprice);
            row.find('input.sian-combo2-presentation-amargin').floatVal(tfixed, amargin);

            SIANCombo2PresentationShowAlert(element, imprice, iaprice);
            SIANCombo2UpdateAmounts(div_id, true, 'a');
            break;
        case 'wprice':

            var element = row.find('input.sian-combo2-presentation-wprice');
            var mprice = row.find('input.sian-combo2-presentation-mprice').floatVal(ifixed);
            var wprice = row.find('input.sian-combo2-presentation-wprice').floatVal(ifixed);
            var iwprice = wprice * (1 + igv);
            var wmargin = USMath.round(((wprice / cost) - 1) * 100, 2);

            //ACTUALIZAMOS EL PRECIO WEB CON IGV
            row.find('input.sian-combo2-presentation-iwprice').floatVal(tfixed, iwprice);
            row.find('input.sian-combo2-presentation-wmargin').floatVal(tfixed, wmargin);

            SIANCombo2PresentationShowAlert(element, mprice, wprice);
            SIANCombo2UpdateAmounts(div_id, true, 'w');
            break;
        case 'iwprice':

            var element = row.find('input.sian-combo2-presentation-iwprice');
            var imprice = row.find('input.sian-combo2-presentation-imprice').floatVal(ifixed);
            var iwprice = row.find('input.sian-combo2-presentation-iwprice').floatVal(tfixed);
            var wprice = iwprice / (1 + igv);
            var wmargin = USMath.round(((iwprice / icost) - 1) * 100, 2);

            //ACTUALIZAMOS EL PRECIO PROMEDIO
            row.find('input.sian-combo2-presentation-wprice').floatVal(ifixed, wprice);
            row.find('input.sian-combo2-presentation-wmargin').floatVal(tfixed, wmargin);

            SIANCombo2PresentationShowAlert(element, imprice, iwprice);
            SIANCombo2UpdateAmounts(div_id, true, 'w');
            break;
    }
}

function SIANCombo2PresentationShowAlert(element, source, target)
{
    if (target < source) {
        element.notify('Este valor debería ser mayor o igual a ' + source + '.', {
            className: 'warn',
            showDuration: 50,
            hideDuration: 50,
            autoHideDelay: 5000
        });
    }
}


