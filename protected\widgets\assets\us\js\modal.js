$('body').on('show.bs.modal', '.us-modal', function (e) {

    var modal_id = $(this).attr('id');
    //var parent_id = $(this).attr('parent_id');
    window.current_modal_id = modal_id;
    //$('#' + parent_id).modal('hide');
    $('.us-modal').not(this).modal('hide');
});

$('body').on('shown.bs.modal', '.us-modal', function (e) {

    var modal = $(this);

    //Si el modal tiene definido un data le hacemos focus. De lo contrario hacemos focus al primer input
    if (typeof modal.data('focus') !== 'undefined')
    {
        var input = $('#' + modal.data('focus'));
        input.focus();
        //Sin embargo el focus sirve una sola vez
        modal.removeData('focus');
    } else
    {
        modal.find('input, textarea, select').filter(':visible:first').focus();
    }

    //$('img.lazy').lazyload();
    $("img.lazy").lazyload();
    //Oculta el scrollbar en body
    $('body').css('overflow', 'hidden');
    $('body').addClass('modal-open');
    //Ocultamos los tooltip
    $('div.ui-tooltip').remove();

    ++window.has_beforeunload;
});

$('body').on('hide.bs.modal', '.us-modal', function (e) {
    var modal_id = $(this).attr('id');
    var parent_id = $(this).attr('parent_id');
    if (window.current_modal_id == modal_id)
    {
        $('#' + parent_id).modal('show');
    }
    //Restaura el scrollbar en body
    $('body').css('overflow', 'visible');
});

$('body').on('hidden.bs.modal', '.us-modal', function (e) {
    --window.has_beforeunload;
    //Procesamos data si la hibiera
    var element = $(this);
    if (element.data('processData'))
    {
        element.data('processData')(element.data('data'));
    }
});

$(document).on('hidden.bs.modal', '.modal', function () {
    $('.us-modal:visible').length && $(document.body).addClass('modal-open');
    $(document.body).css('padding-right', 0);

});