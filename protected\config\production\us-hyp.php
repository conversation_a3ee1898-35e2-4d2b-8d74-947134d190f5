<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'hyp.siansystem.com/admin';
$domain = 'https://hyp.siansystem.com';
$domain2 = 'http://hyp.siansystem.com';
$report_domain = 'rhyp.siansystem.com';
$org = 'hyp';
//SIAN 2
$domain_react_app = 'https://hyp2.siansystem.com';
$api_sian = 'https://api.siansystem.com/';
//database enterprise
$database_server = '161.132.48.88';
$database_name = 'hyp';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = 'hyp.p12';
$e_billing_certificate_pass = 'HYZ8syR9Ur7LfsNi';//PIN
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20482117041BILLING8',//usuario
        'password' => 'billingHYP19'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = '75neb01r62mg8c';
$environment_reports = YII_ENVIRONMENT_PRODUCTION;
$environment = YII_ENVIRONMENT_PRODUCTION;
