<?php

class SIANGridToolbar extends CWidget {

    public $id = null;
    public $title;
    public $hint = null;
    public $module;
    public $controller;
    public $grid_id;
    public $buttons = array(
        array('context' => 'primary', 'icon' => 'fa fa-lg fa-plus white', 'label' => "Crear", 'class' => 'form', 'title' => 'Crear', 'action' => 'create'),
        array(
            array('context' => 'success', 'icon' => 'fa fa-lg fa-check-square-o white', 'label' => 'Activar', 'class' => 'multiboolean', 'title' => 'Activar', 'action' => 'multistatus', 'params' => array('value' => 1)),
            array('context' => 'default', 'icon' => 'fa fa-lg fa-square-o', 'label' => 'Desactivar', 'class' => 'multiboolean', 'action' => 'multistatus', 'title' => 'Desactivar', 'params' => array('value' => 0)),
        ),
        array('context' => 'danger', 'icon' => 'fa fa-lg fa-trash-o white', 'label' => 'Eliminar', 'class' => 'multidelete', 'title' => 'Eliminar', 'action' => 'multidelete')
    );
    public $posts = [];
    public $onChangePageSize = '';
    //Private
    private $controllerObj;
    private $post_count;
    private $access = [];
    public $page_size_id = null;
    private $export_id;
    private $clean_id;
    public $export = false;
    public $form = null;

    /**
     * Initializes the widget.
     */
    public function init() {
        $this->id = isset($this->id) ? $this->id : $this->owner->getServerId();
        $this->page_size_id = isset($this->page_size_id) ? $this->page_size_id : $this->owner->getServerId();
        $this->controllerObj = Yii::app()->controller;
        $this->export_id = $this->owner->getServerId();
        $this->clean_id = $this->owner->getServerId();

        $this->post_count = count($this->posts);
        //JS
        SIANAssets::registerScriptFile("other/kayalshri-tableExport/tableExport.js");
        SIANAssets::registerScriptFile("other/kayalshri-tableExport/jquery.base64.js");
        SIANAssets::registerScriptFile("other/kayalshri-tableExport/html2canvas.js");
        SIANAssets::registerScriptFile('js/sian-grid-toolbar.js');
        //Script
        Yii::app()->clientScript->registerScript($this->id . "_script", "

            $(document).ready(function() {                  
                var divObj = $('#{$this->id}');
                divObj.data('grid_id', '{$this->grid_id}');
                divObj.data('setPageSizeUrl', '{$this->controllerObj->createUrl('/widget/setPageSize')}');
            });

            $('#{$this->page_size_id}').change(function(e){
                var page_size = $(this).val();
                SIANGridToolbarChangePageSize('{$this->id}', page_size);
                {$this->onChangePageSize}
            });
            
            " . ($this->export ? "
            
            $('body').on('click', '#{$this->export_id} a', function(e) {
            
                e.preventDefault();
                var table = $('#{$this->grid_id} table');
                var count = table.find('th').length;    
                var ignoreColumn = [];
                $(table.find('th')).each(function( index ) {
                    var column = $(this);
                    if(column.hasClass('checkbox-column') || index == (count - 1))
                    {
                        ignoreColumn.push(index);
                    }
                });

                var element = $(this);
                table.tableExport({
                    filename: '{$this->owner->title}',
                    type: element.data('type'), 
                    escape: 'false', 
                    ignoreColumn: ignoreColumn
                });
            });
            
            $('body').on('click', '#{$this->clean_id}', function(e) {

                e.preventDefault();

                var form = $('#{$this->form->id}');
                //form[0].reset();
                //Reseteamos select
                form.find('input').each(function() {
                    $(this).val(null);
                });
                form.find('select:not(.page-size)').each(function() {
                    $(this).prop('selectedIndex', 0);
                });
                //Resetamos select2 si hubiera
                form.find('.select2-container').each(function() {
                    $(this).select2('val', '');
                });

                //Restauramos por defecto
                $.ajax({
                    type: 'post',
                    url: '{$this->controllerObj->createUrl('/widget/getDefaultFilters')}',
                    data: {
                        underscored_route: '{$this->controllerObj->getUnderscoredRoute()}',
                    },
                    beforeSend: function (xhr) {
                        window.active_ajax++;
                        //Ocultamos los tooltip
                        $('div.ui-tooltip').remove();
                    },
                    success: function (data) {
                        if(data)
                        {
                            $.each(data, function(attribute, value) {
                                $('[attribute=\"' + attribute + '\"]').val(value);
                            });
                        }console.log(data);
                        window.active_ajax--;
                        
                        var data = form.serialize();console.log(data);
                        $.fn.yiiGridView.update('{$this->grid_id}', {
                            url: window.location.href,                        
                            data: data,
                            complete: function(response, status) { 
                                 
                            }
                        });
                    },
                    error: function (request, status, error) { // if error occured
                        bootbox.alert(us_message(request.responseText, 'error'));

                        window.active_ajax--;
                    },
                    dataType: 'json'
                });
            });
        " : "" ));
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $hasInput = (count($this->buttons) + $this->post_count) > 0;

        if ($this->export) {
            $lg = $hasInput ? 3 : 10;
            $md = $hasInput ? 3 : 10;
            $sm = $hasInput ? 3 : 10;
        } else {
            $lg = $hasInput ? 4 : 11;
            $md = $hasInput ? 4 : 10;
            $sm = $hasInput ? 4 : 10;
        }

        echo "<div id='{$this->id}'class='row'>"; //ROW
        echo "<div class='col-lg-{$lg} col-md-{$md} col-sm-{$sm} col-xs-9'>"; //TITLE
        echo "<b style='font-size:16px;'>{$this->title}</b>";
        echo "</div>"; //TITLE
        if ($hasInput) {
            echo "<div class='col-lg-7 col-md-7 col-sm-7 col-xs-12'>";
            $this->renderInputs();
            echo "</div>";
        }

        if ($this->export) {
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-3 text-right' style='display: inline-flex; padding: 0px;'>";
            $this->widget('booster.widgets.TbButtonGroup', array(
                'buttons' => array(
                    array(
                        'buttonType' => 'reset',
                        'context' => 'default',
                        'size' => 'extra_small',
                        'label' => 'Limpiar',
                        'icon' => 'fire',
                        'htmlOptions' => array(
                            'id' => $this->clean_id,
                            'style' => 'margin: 2px 0px;',
                        )
                    ),
                    array(
                        'buttonType' => 'button',
                        'context' => 'success',
                        'size' => 'extra_small',
                        'label' => 'Exportar',
                        'icon' => 'file',
                        'htmlOptions' => array(
                            'style' => 'margin: 2px 0px;',
                        ),
                        'dropdownOptions' => array(
                            'id' => $this->export_id,
                        ),
                        'encodeLabel' => false,
                        'items' => $this->getExportItems()
                    ),
                )
            ));
            echo CHtml::dropDownList(null, SIANUserCache::getPageSize($this->grid_id), array(
                '5' => '5',
                '10' => '10',
                '15' => '15',
                '20' => '20',
                '25' => '25',
                '50' => '50',
                    //'100' => '100',
                    //'200' => '200',
                    //'500' => '500',
                    //'1000' => '1000',
                    //SIANUserCache::NO_PAGINATION => 'Todos'
                    ), array(
                'id' => $this->page_size_id,
                'class' => 'form-control page-size',
                'grid_id' => $this->grid_id,
                'style' => 'width:35%; margin-left:3px;'
            ));
            echo "</div>";
        } else {
            echo "<div class='col-lg-1 col-md-2 col-sm-2 col-xs-3 text-right'>";
            echo CHtml::dropDownList(null, SIANUserCache::getPageSize($this->grid_id), array(
                '5' => '5',
                '10' => '10',
                '15' => '15',
                '20' => '20',
                '25' => '25',
                '50' => '50',
                    //'100' => '100',
                    //'200' => '200',
                    //'500' => '500',
                    //'1000' => '1000',
                    //SIANUserCache::NO_PAGINATION => 'Todos'
                    ), array(
                'id' => $this->page_size_id,
                'class' => 'form-control page-size',
                'grid_id' => $this->grid_id,
            ));
            echo "</div>";
        }
        echo "</div>";
        if (isset($this->hint)) {
            echo $this->hint;
        }
    }

    private function renderInputs() {
        $i_button_quantity = count($this->buttons);
        $i_button_cols = $this->post_count == 0 ? 12 : $i_button_quantity;
        if(($this->post_count + $i_button_quantity) > 0){
            echo "<div class='row'>";
            if ($i_button_quantity > 0) {
                echo "<div class='col-lg-{$i_button_cols} col-md-{$i_button_cols} col-sm-{$i_button_cols} col-xs-{$i_button_cols} d-flex align-items-center'>";
                foreach ($this->buttons as $button) {
                    $buttons = [];    
                    
                    if(isset($button['action']) && $button['action'] == 'group' && Yii::app()->controller->getOrganization()->globalVar->business_line_type != GlobalVar::BUSINESS_LINE_RESTAURANT){
                        continue;
                    }
                    if (USArray::isAssoc($button)) {
                        array_push($buttons, $this->normalizeButton($button));
                    } else {
                        foreach ($button as $subbutton) {
                            array_push($buttons, $this->normalizeButton($subbutton));
                        }
                    }
                    echo $this->widget('application.widgets.USButtons', array('buttons' => $buttons), true);
                }
                echo "</div>";
            }

            if ($this->post_count > 0) {
                if ($this->post_count > 1) {
                    echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-2'>";
                    echo $this->widget('booster.widgets.TbButton', array(
                        'icon' => 'backward',
                        'size' => 'mini',
                        'block' => true,
                        'htmlOptions' => array(
                            'onclick' => "SIANGridToolbarBackward({$this->post_count});"
                        )), true);
                    echo "</div>";
                }

                $lg = ($this->post_count > 1 ? 10 : 12) - $i_button_quantity;
                $md = ($this->post_count > 1 ? 10 : 12) - $i_button_quantity;
                $sm = ($this->post_count > 1 ? 8 : 12) - $i_button_quantity;
                $xs = ($this->post_count > 1 ? 8 : 12) - $i_button_quantity;

                echo "<div class='col-lg-{$lg} col-md-{$md} col-sm-{$sm} col-xs-{$xs}'>";
                for ($i = 0; $i < $this->post_count; $i++) {
                    $this->posts[$i]['index'] = $i;
                    $this->posts[$i]['visible'] = $i == 0;
                    if (!isset($this->posts[$i]['route'])) {
                        $this->posts[$i]['route'] = "/{$this->module}/{$this->controller}/{$this->posts[$i]['action']}";
                    }
                    unset($this->posts[$i]['action']);
                    echo $this->widget('application.widgets.USPost', $this->posts[$i], true);
                }
                echo "</div>";

                if ($this->post_count > 1) {
                    echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-2'>";
                    echo $this->widget('booster.widgets.TbButton', array(
                        'icon' => 'forward',
                        'size' => 'mini',
                        'block' => true,
                        'htmlOptions' => array(
                            'onclick' => "SIANGridToolbarForward({$this->post_count});"
                        )), true);
                    echo "</div>";
                }
            }
            echo "</div>";
        }
    }

    private function normalizeButton(array $button) {
        $button['data'] = isset($button['data']) ? $button['data'] : [];
        $button['data']['type'] = isset($button['data']['type']) ? $button['data']['type'] : 'grid';
        $button['data']['element_id'] = isset($button['data']['element_id']) ? $button['data']['element_id'] : $this->grid_id;

        if (isset($button['action'])) {

            $button['route'] = "/{$this->module}/{$this->controller}/{$button['action']}";

            if (!isset($this->access[$button['route']])) {
                $this->access[$button['route']] = Yii::app()->controller->checkRoute($button['route']);
            }

            $button['disabled'] = isset($button['disabled']) ? $button['disabled'] : !$this->access[$button['route']];
        }
        return $button;
    }

    private function getExportItems() {
        $exportItems = array(
            array(
                'format' => 'xls',
                'label' => 'Excel',
            ),
            array(
                'format' => 'png',
                'label' => 'PNG',
            ),
        );

        $buttons = [];
        foreach ($exportItems as $item) {
            if (is_array($item)) {
                $icon = CHtml::image(Yii::app()->params['admin_url'] . '/images/export/' . $item['format'] . '.png', $item['format'], array('width' => '16'));
                $buttons[] = array(
                    'label' => $icon . ' ' . $item['label'],
                    'url' => Strings::LINK_TEXT,
                    'linkOptions' => array(
                        'data-type' => $item['format'],
                    )
                );
            } else {
                $buttons[] = $item;
            }
        }

        return $buttons;
    }

}
