<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'puntov.siansystem.com/admin';
$domain = 'https://puntov.siansystem.com';
$domain2 = 'http://puntov.siansystem.com';
$report_domain = 'rpuntov.siansystem.com';
$org = 'puntov';
//SIAN 2
$api_sian = 'http://api-staging.siansystem.com/';
//database enterprise
$database_server = '69.10.37.246';
$database_name = 'puntov';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = 'puntov.p12';
$e_billing_certificate_pass = 'TRESTABA2024';
$e_billing_ri = '0620050000126';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20607109169BILLING1',
        'password' => 'Puntovi1234'
    ]
];
$e_billing_rest_credentials = [
    'client_id' => '72729bf2-37ab-40c8-ac53-f5778757662d',
    'client_secret' => 'KbxQ7mVyah6Iq3W2pcAXDg=='
];
$e_billing_send_automatically_to_client = false;
//
$smtp_username = '<EMAIL>';
$smtp_password = '76a9uu7s1159ci';
$environment_reports = YII_ENVIRONMENT_PRODUCTION;
$environment = YII_ENVIRONMENT_PRODUCTION;
