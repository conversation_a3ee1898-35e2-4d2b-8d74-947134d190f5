<?php

class USBase<PERSON>ontroller extends MainController {

    const TYPE_CHILD = 'child';
    const TYPE_PARENT = 'parent';

    private $defaultMerchandiseMeasure = null;
    private $defaultServiceMeasure = null;
    private $defaultConvention = null;
    private $allScenarios = null;
    private $scenarioControls = null;

    public function secureDelete($model) {
        $this->beginTransaction($model);

        try {
            $model->delete();
            $this->commitTransaction($model);
        } catch (Exception $ex) {

            $this->rollbackTransaction($model);

            switch ($ex->getCode()) {
                case 23000 :
                    throw new Exception("No se puede eliminar este elemento porque está siendo usado.");
                default :
                    throw $ex;
            }
        }
    }

    public function secureSave($model) {
        $success = false;

        try {
            //Iniciamos la transacción
            $this->beginTransaction($model);
            //Insertamos/actualizamos el modelo       
            if ($model->isNewRecord) {
                $success = $model->insert();
            } else {
                $success = $model->update();
            }
            //Confirmamos la transacción
            $this->commitTransaction($model);
        } catch (Exception $ex) {
            //Anulamos la transacción
            $this->rollbackTransaction($model);
            throw $ex;
        }

        return $success;
    }

    public function getAllScenarioControls() {
        if (!isset($this->scenarioControls)) {

            $a_allScenarioControls = SIANCache::get('scenarioControls');

            if ($a_allScenarioControls === false) {
                $a_items = ScenarioControl::getAll();
                //Cargamos padres
                $a_controlLinks = [];
                $a_partialParentRoutes = [];
                $a_totalParentRoutes = [];
                $a_kardexUnlockParentRoutes = [];
                $a_referenceRoutes = [];
                $a_parentDispatchableRoutes = [];
                $a_childDispatchableRoutes = [];
                $a_parentPayableRoutes = [];
                $a_childPayableRoutes = [];
                $a_parentApplicableRoutes = [];
                $a_childApplicableRoutes = [];
                $a_parentOverloadableRoutes = [];
                $a_childOverloadableRoutes = [];
                $a_parentTransformableRoutes = [];
                $a_childTransformableRoutes = [];
                $a_parentUpgradeableRoutes = [];
                $a_childUpgradeableRoutes = [];
                $a_parentCreditableRoutes = [];
                $a_childCreditableRoutes = [];
                $a_parentDebitableRoutes = [];
                $a_childDebitableRoutes = [];
                $a_toReceiveRoutes = [];
                $a_toDispatchRoutes = [];
                foreach ($a_items as $a_item) {
                    $parent_route = $a_item['parent_route'];
                    $route = $a_item['route'];
                    $direction = $a_item['direction'];
                    $link = $a_item['link'];
                    $class = $a_item['class'];
                    $kardex_unlock = $a_item['kardex_unlock'];
                    $default_ability = $a_item['default_ability'];

                    $key = "{$parent_route}-{$route}";
                    $a_controlLinks[$key] = $a_item;

                    if ($class == MovementLink::PARCIAL_LINK) {
                        $a_partialParentRoutes[$route][] = $parent_route;
                    } else {
                        $a_totalParentRoutes[$route][] = $parent_route;
                    }

                    if ($kardex_unlock == 1) {
                        $a_kardexUnlockParentRoutes[$route][] = $parent_route;
                    }
                    //Referencias
                    $a_referenceRoutes[$parent_route][] = $route;

                    //Si hay información de ability
                    if (isset($default_ability) && strlen($default_ability) > 0) {

                        switch ($default_ability) {
                            case ScenarioControl::IS_PAYABLE:
                                $a_parentPayableRoutes[$route][] = $parent_route;
                                $a_childPayableRoutes[$parent_route][] = $route;
                                break;
                            case ScenarioControl::IS_APPLICABLE:
                                $a_parentApplicableRoutes[$route][] = $parent_route;
                                $a_childApplicableRoutes[$parent_route][] = $route;
                                break;
                            case ScenarioControl::IS_DISPATCHABLE:
                                $a_parentDispatchableRoutes[$route][] = $parent_route;
                                $a_childDispatchableRoutes[$parent_route][] = $route;
                                //TO_RECEIVE OR TO_DISPATCH
                                switch ($direction) {
                                    case Scenario::DIRECTION_IN:
                                        $a_toReceiveRoutes[] = $parent_route;
                                        break;
                                    case Scenario::DIRECTION_OUT:
                                        $a_toDispatchRoutes[] = $parent_route;
                                        break;
                                }
                                break;
                            case ScenarioControl::IS_OVERLOADABLE:
                                $a_parentOverloadableRoutes[$route][] = $parent_route;
                                $a_childOverloadableRoutes[$parent_route][] = $route;
                                break;
                            case ScenarioControl::IS_TRANSFORMABLE:
                                $a_parentTransformableRoutes[$route][] = $parent_route;
                                $a_childTransformableRoutes[$parent_route][] = $route;
                                break;
                            case ScenarioControl::IS_UPGRADEABLE:
                                $a_parentUpgradeableRoutes[$route][] = $parent_route;
                                $a_childUpgradeableRoutes[$parent_route][] = $route;
                                break;
                            case ScenarioControl::IS_CREDITABLE:
                                $a_parentCreditableRoutes[$route][] = $parent_route;
                                $a_childCreditableRoutes[$parent_route][] = $route;
                                break;
                            case ScenarioControl::IS_DEBITABLE:
                                $a_parentDebitableRoutes[$route][] = $parent_route;
                                $a_childDebitableRoutes[$parent_route][] = $route;
                                break;
                        }
                    }
                }

                $a_allScenarioControls = [
                    'controlLinks' => $a_controlLinks,
                    'partialParentRoutes' => $a_partialParentRoutes,
                    'totalParentRoutes' => $a_totalParentRoutes,
                    'kardexUnlockParentRoutes' => $a_kardexUnlockParentRoutes,
                    'referenceRoutes' => $a_referenceRoutes,
                    'parentDispatchableRoutes' => $a_parentDispatchableRoutes,
                    'childDispatchableRoutes' => $a_childDispatchableRoutes,
                    'parentPayableRoutes' => $a_parentPayableRoutes,
                    'childPayableRoutes' => $a_childPayableRoutes,
                    'parentApplicableRoutes' => $a_parentApplicableRoutes,
                    'childApplicableRoutes' => $a_childApplicableRoutes,
                    'parentOverloadableRoutes' => $a_parentOverloadableRoutes,
                    'childOverloadableRoutes' => $a_childOverloadableRoutes,
                    'parentTransformableRoutes' => $a_parentTransformableRoutes,
                    'childTransformableRoutes' => $a_childTransformableRoutes,
                    'parentUpgradeableRoutes' => $a_parentUpgradeableRoutes,
                    'childUpgradeableRoutes' => $a_childUpgradeableRoutes,
                    'parentCreditableRoutes' => $a_parentCreditableRoutes,
                    'childCreditableRoutes' => $a_childCreditableRoutes,
                    'parentDebitableRoutes' => $a_parentDebitableRoutes,
                    'childDebitableRoutes' => $a_childDebitableRoutes,
                    'toReceiveRoutes' => $a_toReceiveRoutes,
                    'toDispatchRoutes' => $a_toDispatchRoutes,
                ];

                SIANCache::add('scenarioControls', $a_allScenarioControls);
            }

            $this->scenarioControls = $a_allScenarioControls;
        }
        return $this->scenarioControls;
    }

    /**
     * Obtiene los datos faltantes para completar los links
     * @param string $parent_route Ruta padre
     * @param string $route Ruta hijo
     * @return ScenarioControl Instancia de scenario contro,
     */
    public function controlLinks(&$tempLinks) {

        $a_controlLinks = $this->getAllScenarioControls()['controlLinks'];

        foreach ($tempLinks as $link) {
            $key = "{$link->parent_route}-{$link->route}";

            if (isset($a_controlLinks[$key])) {
                $link->valid = true;
                $link->link = $a_controlLinks[$key]['link'];
                $link->class = $a_controlLinks[$key]['class'];
                $link->default_ability = $a_controlLinks[$key]['default_ability'];
            }
        }
    }

    public function controlItems(&$model, $parent_route) {

        $key = "{$parent_route}-{$model->movement->route}";

        $controlLinks = $this->getAllScenarioControls()['controlLinks'];

        if (isset($controlLinks[$key])) {
            if ($controlLinks[$key]['link_item_quantity'] == 1) {
                $model->link_item_quantity = $controlLinks[$key]['link_item_quantity'];
            }
            if ($controlLinks[$key]['link_item_total'] == 1) {
                $model->link_item_total = $controlLinks[$key]['link_item_total'];
            }
        }
    }

    public function processRoutes($p_a_routes, $p_s_action) {

        $a_routes = [];
        foreach ($p_a_routes as $s_route) {
            $a_routes[] = "/{$s_route}/{$p_s_action}";
        }
        return $a_routes;
    }

    public function getChildDispatchableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_DISPATCHABLE, self::TYPE_CHILD, $p_s_direction);
    }

    public function getParentDispatchableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_DISPATCHABLE, self::TYPE_PARENT, $p_s_direction);
    }

    public function getChildPayableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_PAYABLE, self::TYPE_CHILD, $p_s_direction);
    }

    public function getParentPayableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_PAYABLE, self::TYPE_PARENT, $p_s_direction);
    }

    public function getChildApplicableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_APPLICABLE, self::TYPE_CHILD, $p_s_direction);
    }

    public function getParentApplicableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_APPLICABLE, self::TYPE_PARENT, $p_s_direction);
    }

    public function getChildCreditableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_CREDITABLE, self::TYPE_CHILD, $p_s_direction);
    }

    public function getParentCreditableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_CREDITABLE, self::TYPE_PARENT, $p_s_direction);
    }

    public function getChildDebitableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_DEBITABLE, self::TYPE_CHILD, $p_s_direction);
    }

    public function getParentDebitableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_DEBITABLE, self::TYPE_PARENT, $p_s_direction);
    }

    public function getChildUpgradeableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_UPGRADEABLE, self::TYPE_CHILD, $p_s_direction);
    }

    public function getParentOverloadableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_OVERLOADABLE, self::TYPE_PARENT, $p_s_direction);
    }

    public function getChildOverloadableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_OVERLOADABLE, self::TYPE_CHILD, $p_s_direction);
    }

    public function getChildTransformableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_TRANSFORMABLE, self::TYPE_CHILD, $p_s_direction);
    }

    public function getParentUpgradeableRoute($p_s_route = null, $p_s_direction = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getDefaultRoute($p_s_route, ScenarioControl::IS_UPGRADEABLE, self::TYPE_PARENT, $p_s_direction);
    }

    public function getChildDispatchableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_DISPATCHABLE, self::TYPE_CHILD);
    }

    public function getParentDispatchableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_DISPATCHABLE, self::TYPE_PARENT);
    }

    public function getChildPayableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_PAYABLE, self::TYPE_CHILD);
    }

    public function getParentPayableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_PAYABLE, self::TYPE_PARENT);
    }

    public function getChildApplicableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_APPLICABLE, self::TYPE_CHILD);
    }

    public function getParentApplicableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_APPLICABLE, self::TYPE_PARENT);
    }

    public function getParentOverloadableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_OVERLOADABLE, self::TYPE_PARENT);
    }

    public function getChildCreditableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_CREDITABLE, self::TYPE_CHILD);
    }

    public function getParentCreditableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_CREDITABLE, self::TYPE_PARENT);
    }

    public function getChildDebitableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_DEBITABLE, self::TYPE_CHILD);
    }

    public function getParentDebitableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_DEBITABLE, self::TYPE_PARENT);
    }

    public function getChildUpgradeableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_UPGRADEABLE, self::TYPE_CHILD);
    }

    public function getParentUpgradeableRoutes($p_a_routes = null) {
        if (!isset($p_a_routes)) {
            $p_a_routes = ["{$this->module->id}/{$this->id}"];
        }
        return $this->getDefaultRoutes($p_a_routes, ScenarioControl::IS_UPGRADEABLE, self::TYPE_PARENT);
    }

    public function getToReceiveRoutes() {
        return $this->getAllScenarioControls()['toReceiveRoutes'];
    }

    public function getToDispatchRoutes() {
        return $this->getAllScenarioControls()['toDispatchRoutes'];
    }

    private function getDefaultRoute($p_s_route, $p_s_default_ability, $p_s_type = self::TYPE_CHILD, $p_s_direction = null) {

        $a_default_routes = $this->getDefaultRoutes([$p_s_route], $p_s_default_ability, $p_s_type);

        //Mandamos la que primera
        if (count($a_default_routes) > 0) {
            //Si tiene sólo uno no hay problema
            if (count($a_default_routes) === 1) {
                return $a_default_routes[0];
            } else {
                //Si hay más de uno mandamos el de la dirección especificada
                foreach ($a_default_routes as $s_default_route) {
                    if ($this->getRouteDirection($s_default_route) == $p_s_direction) {
                        return $s_default_route;
                    }
                }
            }
        }
        throw new Exception("La ruta " . ($p_s_type === self::TYPE_CHILD ? 'padre' : 'hija') . " '{$p_s_route}' no contiene una ruta " . ($p_s_type === self::TYPE_CHILD ? 'hija' : 'padre') . " (o contiene más de una) para '{$p_s_default_ability}'!");
    }

    private function getDefaultRoutes($p_a_routes, $p_s_default_ability, $p_s_type = self::TYPE_CHILD) {
        if (!is_array($p_a_routes)) {
            $p_a_routes = [$p_a_routes];
        }
        $s_attribute = "";
        switch ($p_s_default_ability) {
            case ScenarioControl::IS_PAYABLE:
                $s_attribute = $p_s_type . "PayableRoutes";
                break;
            case ScenarioControl::IS_APPLICABLE:
                $s_attribute = $p_s_type . "ApplicableRoutes";
                break;
            case ScenarioControl::IS_DISPATCHABLE:
                $s_attribute = $p_s_type . "DispatchableRoutes";
                break;
            case ScenarioControl::IS_OVERLOADABLE:
                $s_attribute = $p_s_type . "OverloadableRoutes";
                break;
            case ScenarioControl::IS_TRANSFORMABLE:
                $s_attribute = $p_s_type . "TransformableRoutes";
                break;
            case ScenarioControl::IS_UPGRADEABLE:
                $s_attribute = $p_s_type . "UpgradeableRoutes";
                break;
            case ScenarioControl::IS_CREDITABLE:
                $s_attribute = $p_s_type . "CreditableRoutes";
                break;
            case ScenarioControl::IS_DEBITABLE:
                $s_attribute = $p_s_type . "DebitableRoutes";
                break;
        }

        $p_a_abilityRoutes = $this->getAllScenarioControls()[$s_attribute];

        $a_routes = [];
        foreach ($p_a_routes as $s_route) {
            $a_default_routes = isset($p_a_abilityRoutes[$s_route]) ? $p_a_abilityRoutes[$s_route] : [];
            $a_routes = array_merge($a_routes, $a_default_routes);
        }

        return USArray::array_unique($a_routes);
    }

    public function getPartialParentRoutes($p_s_route = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getParentRoutes($p_s_route, MovementLink::PARCIAL_LINK);
    }

    public function getTotalParentRoutes($p_s_route = null) {
        if (!isset($p_s_route)) {
            $p_s_route = "{$this->module->id}/{$this->id}";
        }
        return $this->getParentRoutes($p_s_route, MovementLink::TOTAL_LINK);
    }

    public function getParentRoutes($p_s_route, $p_s_class) {

        switch ($p_s_class) {
            case MovementLink::PARCIAL_LINK:
                $attribute = 'partialParentRoutes';
                break;
            case MovementLink::TOTAL_LINK:
                $attribute = 'totalParentRoutes';
                break;
        }

        $a_parentRoutes = $this->getAllScenarioControls()[$attribute];
        return isset($a_parentRoutes[$p_s_route]) ? $a_parentRoutes[$p_s_route] : [];
    }

    public function getReferences($p_a_route) {
        //Convertimos a array si no lo es
        if (!is_array($p_a_route)) {
            $p_a_route = [$p_a_route];
        }
        //Obtenemos controls
        $a_referenceRoutes = $this->getAllScenarioControls()['referenceRoutes'];
        //Por cada ruta obtendremos sus referencias
        $a_routes = [];
        foreach ($p_a_route as $s_route) {
            $a_references = isset($a_referenceRoutes[$s_route]) ? $a_referenceRoutes[$s_route] : [];
            $a_routes = array_merge($a_routes, $a_references);
        }
        //Limpiamos
        return USArray::array_unique($a_routes);
    }

    public function getParentKardexUnlockRoutes($p_a_routes) {
        if (!is_array($p_a_routes)) {
            $p_a_routes = [$p_a_routes];
        }

        $p_a_parent_routes = $this->getAllScenarioControls()['kardexUnlockParentRoutes'];

        $a_routes = [];
        foreach ($p_a_routes as $s_route) {
            $a_default_routes = isset($p_a_parent_routes[$s_route]) ? $p_a_parent_routes[$s_route] : [];
            $a_routes = array_merge($a_routes, $a_default_routes);
        }

        return USArray::array_unique($a_routes);
    }

    public function getAllScenarios() {

        if (!isset($this->allScenarios)) {

            //Obtenemos los scenarios primero de caché
            $a_allScenarios = SIANCache::get('scenarios');

            if ($a_allScenarios === false) {

                $a_items = Scenario::getAll();

                $a_allRoutes = [];
                $a_toApplyRoutes = [];
                $a_toCollectRoutes = [];
                $a_toPayRoutes = [];
                $a_toDoRoutes = [];
                $a_orderRoutes = [];
                $a_commercialRoutes = [];
                $a_buyRoutes = [];
                $a_saleRoutes = [];
                $a_sellerRoutes = [];
                $a_sellerScenarioIds = [];
                $a_billRoutes = [];
                $a_creditNoteRoutes = [];
                $a_provisionRoutes = [];
                $a_ebillingRoutes = [];
                $a_warehouseRoutes = [];
                $a_kardexRoutes = [];
                $a_lockerRequestRoutes = [];
                $a_directions = [];
                $a_productListRoutes = [];

                foreach ($a_items as $o_scenario) {
                    //ALL
                    $a_allRoutes[] = $o_scenario->route;
                    //To apply
                    if (USString::contains($o_scenario->route, 'creditNote') || USString::contains($o_scenario->route, 'treasury/pre')) {
                        $a_toApplyRoutes[] = $o_scenario->route;
                    }
                    //To collect
                    if (in_array($o_scenario->column, [Entry::DYNAMIC_COLUMN, Entry::DEBIT_COLUMN]) && $o_scenario->to_do == 1) {
                        $a_toCollectRoutes[] = $o_scenario->route;
                    }
                    //To pay
                    if (in_array($o_scenario->column, [Entry::DYNAMIC_COLUMN, Entry::CREDIT_COLUMN]) && $o_scenario->to_do == 1) {
                        $a_toPayRoutes[] = $o_scenario->route;
                    }
                    //To do
                    if ($o_scenario->to_do == 1) {
                        $a_toDoRoutes[] = $o_scenario->route;
                    }
                    //Order
                    if ($o_scenario->type == Scenario::TYPE_COMMERCIAL && !isset($o_scenario->accounting_file_id)) {
                        $a_orderRoutes[] = $o_scenario->route;
                    }
                    //Commercial
                    if (in_array($o_scenario->route, ['support/serviceOrder', 'commercial/quoteTemplate', 'commercial/saleQuote', 'commercial/saleOrder', 'commercial/saleBill', 'commercial/debitNote', 'commercial/creditNote3', 'commercial/creditNote2', 'commercial/creditNote1'])) {
                        $a_commercialRoutes[] = $o_scenario->route;
                    }
                    //Buy
                    if ($o_scenario->type == Scenario::TYPE_COMMERCIAL && $o_scenario->direction == Scenario::DIRECTION_IN && isset($o_scenario->accounting_file_id) && !USString::contains($o_scenario->route, 'creditNote')) {
                        $a_buyRoutes[] = $o_scenario->route;
                    }
                    //Buy
                    if ($o_scenario->type == Scenario::TYPE_PRODUCT_LIST && $o_scenario->direction == Scenario::DIRECTION_IN && $o_scenario->route == 'logistic/prePurchaseOrder') {
                        $a_buyRoutes[] = $o_scenario->route;
                    }
                    //Sale
                    if ($o_scenario->type == Scenario::TYPE_COMMERCIAL && $o_scenario->direction == Scenario::DIRECTION_OUT && isset($o_scenario->accounting_file_id) && !USString::contains($o_scenario->route, 'creditNote')) {
                        $a_saleRoutes[] = $o_scenario->route;
                    }
                    //Seller
                    if (in_array($o_scenario->route, ['support/serviceOrder', 'commercial/saleQuote', 'commercial/saleOrder', 'commercial/saleBill', 'commercial/debitNote', 'commercial/creditNote3', 'commercial/creditNote2', 'commercial/creditNote1'])) {
                        $a_sellerRoutes[] = $o_scenario->route;
                        $a_sellerScenarioIds[] = $o_scenario->scenario_id;
                    }
                    //Bill
                    if ($o_scenario->type == Scenario::TYPE_COMMERCIAL && isset($o_scenario->accounting_file_id) && !USString::contains($o_scenario->route, 'creditNote') && !USString::contains($o_scenario->route, 'debitNote')) {
                        $a_billRoutes[] = $o_scenario->route;
                    }
                    //CreditNote
                    if ($o_scenario->type == Scenario::TYPE_COMMERCIAL && isset($o_scenario->accounting_file_id) && USString::contains($o_scenario->route, 'creditNote')) {
                        $a_creditNoteRoutes[] = $o_scenario->route;
                    }
                    //Provision
                    if (isset($o_scenario->accountingFile) && ($o_scenario->accountingFile->autochecked == AccountingFile::PROVISION_MANUAL || $o_scenario->accountingFile->autochecked == AccountingFile::PROVISION_SEMIAUTOMATIC )) {
                        $a_provisionRoutes[] = $o_scenario->route;
                    }
                    //EBilling
                    if ($o_scenario->module == 'commercial' && isset($o_scenario->accounting_file_id)) {
                        $a_ebillingRoutes[] = $o_scenario->route;
                    }
                    //Warehouse Routes
                    if ($o_scenario->type == Scenario::TYPE_WAREHOUSE) {
                        $a_warehouseRoutes[] = $o_scenario->route;
                    }
                    //Kardex Routes
                    if ($o_scenario->type == Scenario::TYPE_WAREHOUSE || $o_scenario->include_kardex_rlock == 1 || $o_scenario->include_kardex_clock == 1 || $o_scenario->kardex_unlock == 1) {
                        $a_kardexRoutes[] = $o_scenario->route;
                    }
                    //LockerRequest Routes
                    if (in_array($o_scenario->route, ['warehouse/saleOut'])) {
                        $a_lockerRequestRoutes[] = $o_scenario->route;
                    }
                    //ProductList Routes
                    if ($o_scenario->type == Scenario::TYPE_PRODUCT_LIST) {
                        $a_productListRoutes[] = $o_scenario->route;
                    }
                    //Directions
                    $a_directions[$o_scenario->route] = $o_scenario->direction;
                }

                $a_allScenarios = [
                    'allRoutes' => $a_allRoutes,
                    'toApplyRoutes' => $a_toApplyRoutes,
                    'toCollectRoutes' => $a_toCollectRoutes,
                    'toPayRoutes' => $a_toPayRoutes,
                    'toDoRoutes' => $a_toDoRoutes,
                    'orderRoutes' => $a_orderRoutes,
                    'commercialRoutes' => $a_commercialRoutes,
                    'buyRoutes' => $a_buyRoutes,
                    'saleRoutes' => $a_saleRoutes,
                    'sellerRoutes' => $a_sellerRoutes,
                    'sellerScenarioIds' => $a_sellerScenarioIds,
                    'billRoutes' => $a_billRoutes,
                    'creditNoteRoutes' => $a_creditNoteRoutes,
                    'provisionRoutes' => $a_provisionRoutes,
                    'ebillingRoutes' => $a_ebillingRoutes,
                    'warehouseRoutes' => $a_warehouseRoutes,
                    'kardexRoutes' => $a_kardexRoutes,
                    'lockerRequestRoutes' => $a_lockerRequestRoutes,
                    'productListRoutes' => $a_productListRoutes,
                    'directions' => $a_directions,
                ];

                SIANCache::add('scenarios', $a_allScenarios);
            }

            $this->allScenarios = $a_allScenarios;
        }

        return $this->allScenarios;
    }

    public function getAllRoutes() {
        return $this->getAllScenarios()['allRoutes'];
    }

    public function getToApplyRoutes() {
        return $this->getAllScenarios()['toApplyRoutes'];
    }

    public function getToCollectRoutes() {
        return $this->getAllScenarios()['toCollectRoutes'];
    }

    public function getToPayRoutes() {
        return $this->getAllScenarios()['toPayRoutes'];
    }

    public function getToDoRoutes() {
        return $this->getAllScenarios()['toDoRoutes'];
    }

    public function getOrderRoutes() {
        return $this->getAllScenarios()['orderRoutes'];
    }

    public function getCommercialRoutes() {
        return $this->getAllScenarios()['commercialRoutes'];
    }

    public function getBuyRoutes() {
        return $this->getAllScenarios()['buyRoutes'];
    }

    public function getSaleRoutes() {
        return $this->getAllScenarios()['saleRoutes'];
    }

    public function getSellerRoutes() {
        return $this->getAllScenarios()['sellerRoutes'];
    }

    public function getSellerScenarioIds() {
        return $this->getAllScenarios()['sellerScenarioIds'];
    }

    public function getBillRoutes() {
        return $this->getAllScenarios()['billRoutes'];
    }

    public function getCreditNoteRoutes() {
        return $this->getAllScenarios()['creditNoteRoutes'];
    }

    public function getProvisionRoutes() {
        return $this->getAllScenarios()['provisionRoutes'];
    }

    public function getEbillingRoutes() {
        return $this->getAllScenarios()['ebillingRoutes'];
    }

    public function getWarehouseRoutes() {
        return $this->getAllScenarios()['warehouseRoutes'];
    }

    public function getKardexRoutes() {
        return $this->getAllScenarios()['kardexRoutes'];
    }

    public function getLockerRequestRoutes() {
        return $this->getAllScenarios()['lockerRequestRoutes'];
    }

    public function getProductListRoutes() {
        return $this->getAllScenarios()['productListRoutes'];
    }

    public function getRouteDirection($p_s_route) {
        return $this->getAllScenarios()['directions'][$p_s_route];
    }

    public function getDefaultMerchandiseMeasure() {

        //Si no está pre-cargado en el controlador
        if (!isset($this->defaultMerchandiseMeasure)) {

            //Obtenemos los scenarios primero de caché
            $a_defaultMerchandiseMeasure = SIANCache::get('defaultMerchandiseMeasure');

            //Si no está en caché
            if ($a_defaultMerchandiseMeasure === false) {
                $a_defaultMerchandiseMeasure = (new DpMultitable(Multitable::MEASURE_UNIT))->addEqualCondition('value', Presentation::DEFAULT_MERCHANDISE_CODE)->find();
                SIANCache::add('defaultMerchandiseMeasure', $a_defaultMerchandiseMeasure);
            }

            $this->defaultMerchandiseMeasure = $a_defaultMerchandiseMeasure;
        }

        return $this->defaultMerchandiseMeasure;
    }

    public function getDefaultServiceMeasure() {

        //Si no está pre-cargado en el controlador
        if (!isset($this->defaultServiceMeasure)) {

            //Obtenemos los scenarios primero de caché
            $a_defaultServiceMeasure = SIANCache::get('defaultServiceMeasure');

            //Si no está en caché
            if ($a_defaultServiceMeasure === false) {
                $a_defaultServiceMeasure = (new DpMultitable(Multitable::MEASURE_UNIT))->addEqualCondition('value', Presentation::DEFAULT_SERVICE_CODE)->find();
                SIANCache::add('defaultServiceMeasure', $a_defaultServiceMeasure);
            }

            $this->defaultServiceMeasure = $a_defaultServiceMeasure;
        }

        return $this->defaultServiceMeasure;
    }

    public function getDefaultConvention() {

        //Si no está pre-cargado en el controlador
        if (!isset($this->defaultConvention)) {

            //Obtenemos los scenarios primero de caché
            $a_defaultConvention = SIANCache::get('defaultConvention');

            //Si no está en caché
            if ($a_defaultConvention === false) {
                $a_defaultConvention = (new DpMultitable(Multitable::CATALOG_25))->addEqualCondition('value', Person::DEFAULT_CONVENTION)->find();
                SIANCache::add('defaultConvention', $a_defaultConvention);
            }

            $this->defaultConvention = $a_defaultConvention;
        }

        return $this->defaultConvention;
    }

    public function getUnderscoredRoute($p_s_route = null) {
        if (!isset($p_s_route)) {
            $p_s_route = $this->route;
        }
        return str_replace('/', '_', $p_s_route);
    }

    public function setSameExchange($p_o_parent_movement, &$p_o_movement) {
        $i_same_exchange = $this->isSameExchange($p_o_parent_movement->route, $p_o_movement->scenario->route);
        $p_o_movement->same_exchange = $i_same_exchange;
        $p_o_movement->exchange_rate = $p_o_parent_movement->exchange_rate;
        return $i_same_exchange;
    }

    public function isSameExchange($p_s_parent_route, $p_s_route) {
        return $this->getAllScenarioControls()['controlLinks']["{$p_s_parent_route}-{$p_s_route}"]['same_exchange'];
    }

}
