<?php

class SIANPayrollView extends CWidget {

    public $form;
    public $id;
    public $model;
    public $factors_employee;
    public $factors_general;
    public $save_button_id;
    public $type_period;
    //PRIVATE
    private $controller;
    private $grid_detail_id;
    private $concepts;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->grid_detail_id = isset($this->grid_detail_id) ? $this->grid_detail_id : $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-payroll.js');
        SIANAssets::registerScriptFile('other/bootstrapValidator/bootstrapValidator.js');
        SIANAssets::registerScriptFile('other/jquery.jqGrid/jquery.jqGrid.js');
        SIANAssets::registerScriptFile('other/jquery.jqGrid/i18n/grid.locale-es.js');
        SIANAssets::registerCssFile('other/jqgrid/css/ui.jqgrid.css');

        //ITEMS
        $details = [];

        foreach ($this->model->tempHeaders as $header) {

            $attributes = [];
            $attributes["person_id"] = $header->person_id;
            $attributes["name"] = $header->name;
            $attributes["typeDoc"] = $header->typeDoc;
            $attributes["assistance_summary_employee_id"] = $header->assistance_summary_employee_id;
            $attributes["combination_id"] = $header->combination_id;
            $attributes["combination_name"] = $header->combination_name;
            $attributes["NRO_MES"] = $header->NRO_MES;
            $attributes["DIAS_LAB"] = $header->DIAS_LAB;
            $attributes["FERIADOS"] = $header->FERIADOS;
            $attributes["DIAS_CALC"] = $header->work_days_calc;
            $attributes["DIAS_TRAB"] = $header->work_days_employee;
            $attributes["INASISTENCIAS"] = $header->absences;
            $attributes["HTRAB"] = $header->work_hours;
            $attributes["HE1"] = $header->extrahours1;
            $attributes["HE2"] = $header->extrahours2;
            $attributes["HNOCTURNAS"] = $header->night_hours;
            $attributes["HDDESC"] = $header->holiday_hours;
            $attributes["MTARDANZAS"] = $header->lateness;
            $attributes["TIENE_AF"] = $header->children;
            $attributes["SPID"] = $header->SPID;
            $attributes["CATEGORIA"] = $header->CATEGORIA;
            $attributes["JORNAL_DIARIO"] = $header->JORNAL_DIARIO;
            $attributes["PORC_BUC"] = $header->PORC_BUC;
            $attributes["BASICO"] = $header->salary;
            $attributes["TIPOCOM"] = $header->type_commision;
            $attributes["APORTE"] = $header->APORTE;
            $attributes["PRIMA"] = $header->PRIMA;
            $attributes["COMISION"] = $header->COMISION; //20

            foreach ($header->tempDetails as $detail) {
                $attributes[$detail->concept_name] = $detail->amount;
            }
            $details[] = $attributes;
        }

        $this->concepts = $this->model->rawConcepts;

        $columns = '';
        $headers = '';
        $concept_result = '';
        $concept_total = '';
        $concepts_for_add = '';

        foreach ($this->concepts as $concept) {
            $headers .= ",'" . $concept['header'] . "'";

            $tam = strlen($concept['header']) > 7 ? '80' : '60';
            if ($concept['is_printable'] == 0) {
                $columns .= ", {name: '" . $concept['header'] . "', index: '" . $concept['header'] . "', hidden: true}";
            } else {
                $columns .= ", {name: '" . $concept['header'] . "', index: '" . $concept['header'] . "', width: " . $tam . ", height:'25px', align: 'right', title: false, editable: false, summaryType: 'sum', editoptions: {dataInit: function (elem) {
                                $(elem).bind('keypress', function (e) {
                                    return SIANPayrollSoloNumeroDecimal(e)
                                })

                                $(elem).bind('keydown', function (e) {
                                    alert('keydown');
                                    return false;
                                })
                            }}}";
            }

            $concept_result .= "      " . $concept['header'] . ":obj." . $concept['header'] . ",\n";
            $concept_total .= " var " . $concept['header'] . "Sum = $('#{$this->grid_detail_id}').jqGrid('getCol', '" .
                    $concept['header'] . "', false, 'sum');\n $('#{$this->grid_detail_id}').jqGrid('footerData', 'set', {" . $concept['header'] . ": " . $concept['header'] . "Sum.toFixed(2)});\n";
            $concepts_for_add .= "       objectRow." . $concept['header'] . " = data[i]." . $concept['header'] . ";\n";
        }

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        $(document).ready(function() { 
            var loadInitial = false;
            var lastsel; 
            var lastFocus;
            gridEmployee =  $('#{$this->grid_detail_id}').jqGrid({
                    datatype: 'local',
                    multiselect: false,                    
                    colNames: ['ID',
                               'N° Doc.',
                               'Colaborador',
                               'ID Centro de Costos',
                               'Centro de Costos',
                               'Resumen de Asistencia Item',
                               'N° Mes',
                               'Dias Lab.',
                               'Feriados',
                               'Dias Calc',
                               'Dias Trab.',
                               'Inasistencias',
                               'Hrs. Trabajadas',
                               'Hrs. Extra 1',
                               'Hrs. Extra2',
                               'Hrs. Nocturnas',
                               'Hrs. D. Descanso',
                               'Tardanzas',
                               'Tiene AF',
                               'SPID',
                               'Categoría',
                               'Jornal Diario',
                               'BUC',
                               'S.Básico',
                               'Tipo Com.',
                               'Aporte',
                               'Prima',
                               'Comisión'" . $headers . "
                    ],
                    colModel: [
                        {name: 'person_id', index: 'person_id', hidden: true,  frozen : true},
                        {name: 'typeDoc', index: 'typeDoc', width: 60,  frozen : true},
                        {name: 'name', index: 'name', width: 200,  frozen : true},
                        {name: 'combination_id', index: 'combination_id', width: 200,  frozen : true, hidden: true},
                        {name: 'combination_name', index: 'combination_name', width: 170,  frozen : true},
                        {name: 'assistance_summary_employee_id', index: 'assistance_summary_employee_id',width: 0, hidden: true},
                        {name: 'NRO_MES', index: 'NRO_MES', hidden: true},
                        {name: 'DIAS_LAB', index: 'DIAS_LAB', hidden: true},
                        {name: 'FERIADOS', index: 'FERIADOS', hidden: true},
                        {name: 'DIAS_CALC', index: 'DIAS_CALC', hidden: true},
                        {name: 'DIAS_TRAB', index: 'DIASTRAB', width: 60, align: 'center'" . ($this->type_period != Regime::TYPE_PERIOD_MONTHLY ? ", hidden: true" : "") . "},
                        {name: 'INASISTENCIAS', index: 'INASISTENCIAS', hidden: true},
                        {name: 'HTRAB', index: 'HTRAB', width: 60 " . ($this->type_period != Regime::TYPE_PERIOD_WEEKLY ? ", hidden: true" : "") . "},
                        {name: 'HE1', index: 'HE1', width: 60 " . ($this->type_period != Regime::TYPE_PERIOD_WEEKLY ? ", hidden: true" : "") . "},
                        {name: 'HE2', index: 'HE2', width: 60 " . ($this->type_period != Regime::TYPE_PERIOD_WEEKLY ? ", hidden: true" : "") . "},
                        {name: 'HNOCTURNAS', index: 'HNOCTURNAS', hidden: true},
                        {name: 'HDDESC', index: 'HDDESC', width: 60 " . ($this->type_period != Regime::TYPE_PERIOD_WEEKLY ? ", hidden: true" : "") . "},                        
                        {name: 'MTARDANZAS', index: 'MTARDANZAS', hidden: true},
                        {name: 'TIENE_AF', index: 'TIENE_AF', hidden: true},
                        {name: 'SPID', index: 'SPID' ,width: 60},
                        {name: 'CATEGORIA', index: 'CATEGORIA', width: 60 " . ($this->type_period != Regime::TYPE_PERIOD_WEEKLY ? ", hidden: true" : "") . "},
                        {name: 'JORNAL_DIARIO', index: 'JORNAL_DIARIO', hidden: true},
                        {name: 'PORC_BUC', index: 'PORC_BUC', hidden: true},
                        {name: 'BASICO', index: 'BASICO', width: 60, align: 'right' " . ($this->type_period != Regime::TYPE_PERIOD_MONTHLY ? ", hidden: true" : "") . "},
                        {name: 'TIPOCOM', index: 'TIPOCOM', hidden: true},
                        {name: 'APORTE', index: 'APORTE', hidden: true},
                        {name: 'PRIMA', index: 'PRIMA', hidden: true},
                        {name: 'COMISION', index: 'COMISION', hidden: true}" . $columns . "
                    ],
                    rowNum: 50,
                    rowList: [50, 100, 200, 300, 400, 500],
                    sortorder: 'asc',
                    width: '',
                    shrinkToFit: false,
                    height: 300,
                    cellsubmit: 'clientArray',
                    sortname: 'name',
                    gridview: true,
                    footerrow: true,
                    userDataOnFooter: true,
                    celledit: false,
                    gridComplete: function () {
                        updateTotalAmounts();
                    },
                    loadComplete: function () {  
                        if(!loadInitial){
                            var data = " . CJSON::encode($details) . ";                     
                            for (var i = 0; i < data.length; i++) {

                                var objectRow = {};
                                objectRow.person_id = data[i].person_id;\n"
                . "objectRow.typeDoc = data[i].typeDoc;\n"
                . "objectRow.name = data[i].name;\n"
                . "objectRow.combination_id = data[i].combination_id;\n"
                . "objectRow.combination_name = data[i].combination_name;\n"
                . "objectRow.assistance_summary_employee_id = data[i].assistance_summary_employee_id;\n"
                . "objectRow.NRO_MES = data[i].NRO_MES;\n"
                . "objectRow.DIAS_LAB = data[i].DIAS_LAB;\n"
                . "objectRow.FERIADOS = data[i].FERIADOS;\n"
                . "objectRow.DIAS_CALC = data[i].DIAS_CALC;\n"
                . "objectRow.DIAS_TRAB = data[i].DIAS_TRAB;\n"
                . "objectRow.INASISTENCIAS = data[i].INASISTENCIAS;\n"
                . "objectRow.HTRAB = data[i].HTRAB;\n"
                . "objectRow.HE1 = data[i].HE1;\n"
                . "objectRow.HE2 = data[i].HE2;\n"
                . "objectRow.HNOCTURNAS = data[i].HNOCTURNAS;\n"
                . "objectRow.HDDESC = data[i].HDDESC;\n"
                . "objectRow.MTARDANZAS = data[i].MTARDANZAS;\n"
                . "objectRow.TIENE_AF = data[i].TIENE_AF;\n"
                . "objectRow.SPID = data[i].SPID;\n"
                . "objectRow.CATEGORIA = data[i].CATEGORIA;\n"
                . "objectRow.JORNAL_DIARIO = data[i].JORNAL_DIARIO;\n"
                . "objectRow.PORC_BUC = data[i].PORC_BUC;\n"
                . "objectRow.BASICO = data[i].BASICO;\n"
                . "objectRow.TIPOCOM = data[i].TIPOCOM;\n"
                . "objectRow.APORTE = data[i].APORTE;\n"
                . "objectRow.PRIMA = data[i].PRIMA;\n"
                . "objectRow.COMISION = data[i].COMISION;\n"
                . $concepts_for_add . "\n" .
                "jQuery('#{$this->grid_detail_id}').jqGrid('addRowData', objectRow.person_id, objectRow);
                            }
                            loadInitial = true;
                        }
                    }
                });
                              
               $('#{$this->grid_detail_id}').jqGrid('setFrozenColumns');
            });
            
            function updateTotalAmounts() {
                " . $concept_total . "
            }
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {
        echo "<div class='panel panel-default'>";
        echo "<div class='panel-heading'>";
        echo "<span class='glyphicon glyphicon-th-list'></span>";
        echo "<h3 class='panel-title' style='display: inline;'> Detalle</h3>";
        echo "</div>";
        echo "<div class=''>";
        echo "<table id='{$this->grid_detail_id}' name='{$this->grid_detail_id}'></table>";
        echo "</div>";
        echo "</div>";
    }

}
