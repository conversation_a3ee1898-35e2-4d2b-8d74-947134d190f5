<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANReportController
 *
 * <AUTHOR>
 */
class SIANReportController extends SIANController {

    protected function beforeAction($action) {
        //REPORTES CSS
        SIANAssets::registerCssFile('other/mcets/css/mcets.reporting.css');
        SIANAssets::registerCssFile('other/mcets/css/mcets.style.reports.css');
        SIANAssets::registerCssFile('other/select2/css/select2.css');
        //JS
        SIANAssets::registerScriptFile('other/mcets/js/mcets.reporting.plugin.js');
        SIANAssets::registerScriptFile('other/mcets/js/reportsComponent.js');
        SIANAssets::registerScriptFile('other/select2/js/select2.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');

        return parent::beforeAction($action);
    }

}
