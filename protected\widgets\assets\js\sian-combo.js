window.sianComboIds = [];

function SIANComboGetPresentations(div_id, product_id, equivalence)
{
    var divObj = $('#' + div_id);
    var presentation_url = divObj.data('presentation_url');
    var presentation_mode = divObj.data('presentation_mode');
    var currency = divObj.data('currency');
    //IDS
    var item_equivalence_id = divObj.data('item_equivalence_id');
    var item_mprice_id = divObj.data('item_mprice_id');
    var item_imprice_id = divObj.data('item_imprice_id');
    var item_aprice_id = divObj.data('item_aprice_id');
    var item_iaprice_id = divObj.data('item_iaprice_id');
    var item_wprice_id = divObj.data('item_wprice_id');
    var item_iwprice_id = divObj.data('item_iwprice_id');

    if (product_id != undefined)
    {
        $.ajax({
            type: 'post',
            url: presentation_url,
            data: {
                mode: presentation_mode,
                product_ids: [product_id],
                currency: currency
            },
            beforeSend: function (xhr) {
                window.active_ajax++;
                //Ocultamos los tooltip
                $('div.ui-tooltip').remove();
            },
            success: function (data) {

                $.each(data, function (index, item) {
                    SIANComboFillPresentations(item_equivalence_id, item, equivalence);
                });

                SIANComboSetPrices(div_id, $('#' + item_equivalence_id), $('#' + item_mprice_id), $('#' + item_imprice_id), $('#' + item_aprice_id), $('#' + item_iaprice_id), $('#' + item_wprice_id), $('#' + item_iwprice_id));
                window.active_ajax--;
            },
            error: function (request, status, error) { // if error occured
                window.active_ajax--;
                bootbox.alert(us_message(request.responseText, 'error'));
            },
            dataType: 'json'
        });
    }
}

function SIANComboFillPresentations(input_id, presentationItems, equivalence)
{
    var inputObj = $('#' + input_id);
    var selected = null;

    inputObj
            .empty()
            .append('<option value>' + STRINGS_SELECT_OPTION + '</option>')
            .data('presentationItems', presentationItems);

    $.each(presentationItems, function (index, presentationObj) {
        inputObj.append('<option value=\'' + presentationObj['equivalence'] + '\' data-currency=\'' + presentationObj['currency'] + '\' data-mprice=' + presentationObj['mprice'] + ' data-aprice=' + presentationObj['aprice'] + ' data-wprice=' + presentationObj['wprice'] + ' data-imprice=' + presentationObj['imprice'] + ' data-iaprice=' + presentationObj['iaprice'] + ' data-iwprice=' + presentationObj['iwprice'] + '>' + presentationObj['measure_name'] + '</option>');

        if (presentationObj['equivalence'] == equivalence)
        {
            selected = presentationObj;
        }
    });

    inputObj.val(selected ? selected.equivalence : null);

    return selected;
}

//Setea los precios
function SIANComboSetPrices(div_id, equivalenceObj, mPriceObj, imPriceObj, aPriceObj, iaPriceObj, wPriceObj, iwPriceObj)
{
    //VARIABLES DE DIV
    var divObj = $('#' + div_id);
    var ifixed = divObj.data('ifixed');
    var tfixed = divObj.data('tfixed');

    var option = equivalenceObj.find(':selected');

    var equivalence = option.val();
    var mprice = option.floatData('mprice', ifixed);
    var imprice = option.floatData('imprice', tfixed);
    var aprice = option.floatData('aprice', ifixed);
    var iaprice = option.floatData('iaprice', tfixed);
    var wprice = option.floatData('wprice', ifixed);
    var iwprice = option.floatData('iwprice', tfixed);

    equivalenceObj.val(equivalence);
    mPriceObj.floatVal(ifixed, mprice);
    imPriceObj.floatVal(tfixed, imprice);
    aPriceObj.val(aprice.toFixed(ifixed)).prop('min', mprice.toFixed(ifixed));
    iaPriceObj.val(iaprice.toFixed(tfixed)).prop('min', imprice.toFixed(tfixed));
    wPriceObj.floatVal(ifixed, wprice);
    iwPriceObj.floatVal(tfixed, iwprice);

}

function SIANComboAddItem(div_id, sub_product_id, product_type, product_name, pres_quantity, presentationItems, sub_equivalence, allow_decimals, mprice, imprice, aprice, iaprice, wprice, iwprice, errors)
{
    //VARIABLES DE DIV
    var divObj = $('#' + div_id);
    var access = divObj.data('view-access');
    var url = divObj.data('view-url');
    var ifixed = divObj.data('ifixed');
    var tfixed = divObj.data('tfixed');
    var istep = divObj.data('istep');
    var tstep = divObj.data('tstep');
    var modal_id = divObj.data('modal_id');
    var price_mode = divObj.data('price_mode');
    var link_prices = divObj.data('link_prices');

    //VARIABLES DE TABLA
    var count = parseInt(divObj.data('count'));

    //ID
    var id = getLocalId();
    var product_name_input_id = getLocalId();
    var pres_quantity_input_id = getLocalId();
    var equivalence_input_id = getLocalId();

    var row = '';
    row += '<tr id=\'' + id + '\' class=\'sian-combo-item sian-combo-item-' + sub_product_id + '\'>';

    row += '<td style=\'text-align:center\'><span class=\'sian-combo-item-index\'></span></td>';

    row += '<td class=\'form-group\'>';
    row += '<input type=\'hidden\' class=\'form-control sian-combo-item-sub-product-id\' name=\'Item[' + id + '][sub_product_id]\' value=\'' + (isset(sub_product_id) ? sub_product_id : '') + '\' readonly>';
    row += '<span class=\'sian-combo-item-sub-product-id\'>' + sub_product_id + '</span>';
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['product_name'] ? 'has-error' : '') + '\'>';
    row += '<input id=\'' + product_name_input_id + '\' type=\'text\' class=\'form-control sian-combo-item-product-name\' name=\'Item[' + id + '][product_name]\' value=\'' + product_name + '\' readonly>';
    if (errors['product_name'])
    {
        row += '<span class=\'help-block error\'>' + errors['product_name'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group\'>';
    row += '<input type=\'text\' class=\'form-control sian-combo-item-product-type\' name=\'Item[' + id + '][product_type]\' value=\'' + product_type + '\' readonly>';
    row += '</td>';

    row += '<td class=\'form-group ' + (errors['pres_quantity'] ? 'has-error' : '') + '\'>';
    row += '<input id=\'' + pres_quantity_input_id + '\' type=\'number\' class=\'form-control sian-combo-item-pres-quantity ' + (allow_decimals == 1 ? 'us-double2' : 'us-double0') + '\' style=\'text-align:right\' name=\'Item[' + id + '][pres_quantity]\' step=' + toStep(allow_decimals ? 2 : 0) + ' min=' + toStep(allow_decimals ? 2 : 0) + ' value=\'' + pres_quantity + '\' onchange="SIANComboUpdateAmounts (\'' + div_id + '\', true);">';
    if (errors['pres_quantity'])
    {
        row += '<span class=\'help-block error\'>' + errors['pres_quantity'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group\'>';
    row += '<select id=\'' + equivalence_input_id + '\' class=\'sian-combo-item-sub-equivalence form-control\' name=\'Item[' + id + '][sub_equivalence]\'></select>';
    row += '<input type=\'hidden\' class=\'sian-combo-item-allow-decimals\' value=\'' + allow_decimals + '\' name=\'Item[' + id + '][allow_decimals]\'>';
    row += '</td>';


    row += '<td class=\'form-group sian-combo-without-igv ' + (errors['mprice'] ? 'has-error' : '') + '\' ' + (price_mode == 0 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo-price-input sian-combo-item-mprice us-double' + ifixed + '\' style=\'text-align:right\' value=\'' + mprice + '\' min=\'0\' name=\'Item[' + id + '][mprice]\' step=' + istep + ' onchange="SIANComboChangePrice($(this), \'mprice\'); SIANComboUpdateAmounts (\'' + div_id + '\', true);" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['mprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['mprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group sian-combo-with-igv ' + (errors['imprice'] ? 'has-error' : '') + '\' ' + (price_mode == 1 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo-price-input sian-combo-item-imprice us-double' + tfixed + '\' style=\'text-align:right\' value=\'' + imprice + '\' min=\'0\' name=\'Item[' + id + '][imprice]\' step=' + tstep + ' onchange="SIANComboChangePrice($(this), \'imprice\'); SIANComboUpdateAmounts (\'' + div_id + '\', true);" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['imprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['imprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group sian-combo-without-igv ' + (errors['aprice'] ? 'has-error' : '') + '\' ' + (price_mode == 0 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo-price-input sian-combo-item-aprice us-double' + ifixed + '\' style=\'text-align:right\' value=\'' + aprice + '\' min=\'0\' name=\'Item[' + id + '][aprice]\' step=' + istep + ' onchange="SIANComboChangePrice($(this), \'aprice\'); SIANComboUpdateAmounts (\'' + div_id + '\', true);" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['aprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['aprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group sian-combo-with-igv ' + (errors['iaprice'] ? 'has-error' : '') + '\' ' + (price_mode == 1 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo-price-input sian-combo-item-iaprice us-double' + tfixed + '\' style=\'text-align:right\' value=\'' + iaprice + '\' min=\'0\' name=\'Item[' + id + '][iaprice]\' step=' + tstep + ' onchange="SIANComboChangePrice($(this), \'iaprice\'); SIANComboUpdateAmounts (\'' + div_id + '\', true);" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['iaprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['iaprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group sian-combo-without-igv ' + (errors['wprice'] ? 'has-error' : '') + '\' ' + (price_mode == 0 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo-price-input sian-combo-item-wprice us-double' + ifixed + '\' style=\'text-align:right\' value=\'' + wprice + '\' min=\'0\' name=\'Item[' + id + '][wprice]\' step=' + istep + ' onchange="SIANComboChangePrice($(this), \'wprice\'); SIANComboUpdateAmounts (\'' + div_id + '\', true);" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['wprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['wprice'] + '</span>';
    }
    row += '</td>';

    row += '<td class=\'form-group sian-combo-with-igv ' + (errors['iwprice'] ? 'has-error' : '') + '\' ' + (price_mode == 1 ? '' : 'style="display: none;"') + '>';
    row += '<input type=\'number\' class=\'form-control sian-combo-price-input sian-combo-item-iwprice us-double' + tfixed + '\' style=\'text-align:right\' value=\'' + iwprice + '\' min=\'0\' name=\'Item[' + id + '][iwprice]\' step=' + tstep + ' onchange="SIANComboChangePrice($(this), \'iwprice\'); SIANComboUpdateAmounts (\'' + div_id + '\', true);" ' + (link_prices == 1 ? 'readonly' : '') + '>';
    if (errors['iwprice'])
    {
        row += '<span class=\'help-block error\'>' + errors['iwprice'] + '</span>';
    }
    row += '</td>';

    row += '<td style=\'text-align:right;\'>';
    if (access == 1)
    {
        row += ' <a title=\'Ver producto\' class=\'form\' url=\'' + url + '?id=' + sub_product_id + '\' data-parent_id=\'' + modal_id + '\' data-modal_id=\'' + getLocalId() + '\'><span class=\'fa fa-eye fa-lg black\'></span></a>';
    } else
    {
        row += ' <span class=\'fa fa-eye fa-lg light-grey\'></span>';
    }
    row += ' <a onclick=\'SIANComboRemoveItem(\"' + div_id + '\", \"' + id + '\", true)\' title=\'Quitar ítem de la lista\'><span class=\'fa fa-times fa-lg black\'></span></a>';
    row += '</td>';


    row += '</tr>';

    //COUNT DE ITEMS
    if (count === 0)
    {
        divObj.find('table.sian-combo-items tbody').html(row);
    } else
    {
        divObj.find('table.sian-combo-items tbody').append(row);
    }

    divObj.data('count', count + 1);

    //Seteamos data original
    $('#' + pres_quantity_input_id).data('original_quantity', pres_quantity);

    //Si no es grupo llenamos el combo
    SIANComboFillPresentations(equivalence_input_id, presentationItems, sub_equivalence);
}

function SIANComboUpdateAmounts(div_id, forced) {

    //VARIABLES
    var divObj = $('#' + div_id);
    var grid_id = divObj.data('grid_id');
    var grid_price_id = divObj.data('grid_price_id');
    var price_mode = divObj.data('price_mode');
    var ifixed = divObj.data('ifixed');
    var tfixed = divObj.data('tfixed');
    var igv = divObj.data('igv');
    var currency = divObj.data('currency');
    //MONTOS
    var tmprice = 0;
    var timprice = 0;
    var taprice = 0;
    var tiaprice = 0;
    var twprice = 0;
    var tiwprice = 0;

    //Obtenemos tabla principal
    divObj.find('table.sian-combo-items tr.sian-combo-item').each(function (index) {

        var rowObj = $(this);
        //
        var sub_pres_quantity = rowObj.find('input.sian-combo-item-pres-quantity').floatVal(2);
        var mprice = rowObj.find('input.sian-combo-item-mprice').floatVal(ifixed);
        var imprice = rowObj.find('input.sian-combo-item-imprice').floatVal(ifixed);
        var aprice = rowObj.find('input.sian-combo-item-aprice').floatVal(ifixed);
        var iaprice = rowObj.find('input.sian-combo-item-iaprice').floatVal(ifixed);
        var wprice = rowObj.find('input.sian-combo-item-wprice').floatVal(ifixed);
        var iwprice = rowObj.find('input.sian-combo-item-iwprice').floatVal(ifixed);

        rowObj.find('span.sian-combo-item-index').floatText(0, index + 1);

        tmprice = USMath.plus(tmprice, USMath.multiply(sub_pres_quantity, mprice));
        timprice = USMath.plus(timprice, USMath.multiply(sub_pres_quantity, imprice));
        taprice = USMath.plus(taprice, USMath.multiply(sub_pres_quantity, aprice));
        tiaprice = USMath.plus(tiaprice, USMath.multiply(sub_pres_quantity, iaprice));
        twprice = USMath.plus(taprice, USMath.multiply(sub_pres_quantity, wprice));
        tiwprice = USMath.plus(tiwprice, USMath.multiply(sub_pres_quantity, iwprice));
    });

    if (price_mode == 1)
    {
        tmprice = USMath.divide(timprice, 1 + igv, ifixed);
        taprice = USMath.divide(tiaprice, 1 + igv, ifixed);
        twprice = USMath.divide(tiwprice, 1 + igv, ifixed);
    } else
    {
        timprice = USMath.multiply(tmprice, 1 + igv, tfixed);
        tiaprice = USMath.multiply(taprice, 1 + igv, tfixed);
        tiwprice = USMath.multiply(twprice, 1 + igv, tfixed);
    }

    //Setear en presentation, Deberia ser tequivalence/pres_quantity pero en este caso es 1
    if (forced)
    {
        SIANPresentationStoreSetForcedPrices(grid_price_id, 0, currency, tmprice, timprice, taprice, tiaprice, twprice, tiwprice);
    }
}

function SIANComboChangePrice(element, field)
{
    var rowObj = element.closest('tr');
    var divObj = rowObj.closest('div.sian-combo');
    var igv = divObj.floatData('igv', 3);
    var ifixed = divObj.data('ifixed');
    var tfixed = divObj.data('tfixed');

    switch (field)
    {
        case 'mprice':

            var mprice = rowObj.find('input.sian-combo-item-mprice').floatVal(ifixed);
            var aprice = rowObj.find('input.sian-combo-item-aprice').floatVal(ifixed);
            var imprice = mprice * (1 + igv);

            //ACTUALIZAMOS EL PRECIO MINIMO CON IGV
            rowObj.find('input.sian-combo-item-imprice').floatVal(tfixed, imprice);
            //SI EL PRECIO MINIMO AHORA ES MAYOR QUE EL PRECIO NORMAL
            if (mprice > aprice) {
                rowObj.find('input.sian-combo-item-aprice').floatVal(ifixed, mprice);
                rowObj.find('input.sian-combo-item-iaprice').floatVal(tfixed, imprice);
            }
            //EL MINIMO DEL PRECIO PROMEDIO SERA EL PRECIO MINIMO
            rowObj.find('input.sian-combo-item-aprice').floatAttr('min', ifixed, mprice);
            rowObj.find('input.sian-combo-item-iaprice').floatAttr('min', tfixed, imprice);

            break;
        case 'imprice':

            var imprice = rowObj.find('input.sian-combo-item-imprice').floatVal(tfixed);
            var aprice = rowObj.find('input.sian-combo-item-aprice').floatVal(ifixed);
            var mprice = imprice / (1 + igv);

            //ACTUALIZAMOS EL PRECIO MINIMO
            rowObj.find('input.sian-combo-item-mprice').floatVal(ifixed, mprice);
            //SI EL PRECIO MINIMO AHORA ES MAYOR QUE EL PRECIO NORMAL
            if (mprice > aprice) {
                rowObj.find('input.sian-combo-item-aprice').floatVal(ifixed, mprice);
                rowObj.find('input.sian-combo-item-iaprice').floatVal(tfixed, imprice);
            }
            //EL MINIMO DEL PRECIO PROMEDIO SERA EL PRECIO MINIMO
            rowObj.find('input.sian-combo-item-aprice').floatAttr('min', ifixed, mprice);
            rowObj.find('input.sian-combo-item-iaprice').floatAttr('min', tfixed, imprice);

            break;
        case 'aprice':

            var aprice = rowObj.find('input.sian-combo-item-aprice').floatVal(ifixed);
            var iaprice = aprice * (1 + igv);

            //ACTUALIZAMOS EL PRECIO PROMEDIO CON IGV
            rowObj.find('input.sian-combo-item-iaprice').floatVal(tfixed, iaprice);

            break;
        case 'iaprice':

            var iaprice = rowObj.find('input.sian-combo-item-iaprice').floatVal(tfixed);
            var aprice = iaprice / (1 + igv);

            //ACTUALIZAMOS EL PRECIO PROMEDIO
            rowObj.find('input.sian-combo-item-aprice').floatVal(ifixed, aprice);

            break;
        case 'wprice':

            var wprice = rowObj.find('input.sian-combo-item-wprice').floatVal(ifixed);
            var iwprice = wprice * (1 + igv);

            //ACTUALIZAMOS EL PRECIO WEB CON IGV
            rowObj.find('input.sian-combo-item-iwprice').floatVal(tfixed, iwprice);

            break;
        case 'iwprice':

            var iwprice = rowObj.find('input.sian-combo-item-iwprice').floatVal(tfixed);
            var wprice = iwprice / (1 + igv);

            //ACTUALIZAMOS EL PRECIO PROMEDIO
            rowObj.find('input.sian-combo-item-wprice').floatVal(ifixed, wprice);

            break;
    }
}

function SIANComboChangePriceMode(div_id, price_mode)
{
    var divObj = $('#' + div_id);
    divObj.data('price_mode', price_mode);

    if (price_mode == 1)
    {
        divObj.find('.sian-combo-with-igv').show(0);
        divObj.find('.sian-combo-without-igv').hide(0);

    } else
    {
        divObj.find('.sian-combo-with-igv').hide(0);
        divObj.find('.sian-combo-without-igv').show(0);
    }
}

function SIANComboChangeLinkPrices(div_id, link_prices)
{
    var divObj = $('#' + div_id);
    divObj.data('link_prices', link_prices);

    divObj.find('input.sian-combo-price-input').attr('readonly', link_prices == 1);

    //Restauramos precios
    if (link_prices == 1)
    {
        //Obtenemos tabla principal
        divObj.find('table.sian-combo-items tr.sian-combo-item').each(function (index) {
            var rowObj = $(this);
            var equivalenceObj = rowObj.find('select.sian-combo-item-sub-equivalence');
            var mPriceObj = rowObj.find('input.sian-combo-item-mprice');
            var imPriceObj = rowObj.find('input.sian-combo-item-imprice');
            var aPriceObj = rowObj.find('input.sian-combo-item-aprice');
            var iaPriceObj = rowObj.find('input.sian-combo-item-iaprice');
            var wPriceObj = rowObj.find('input.sian-combo-item-wprice');
            var iwPriceObj = rowObj.find('input.sian-combo-item-iwprice');

            SIANComboSetPrices(div_id, equivalenceObj, mPriceObj, imPriceObj, aPriceObj, iaPriceObj, wPriceObj, iwPriceObj);

        });

        SIANComboUpdateAmounts(div_id, true);
    }

}

function SIANComboRemoveItem(div_id, instance_id, confirmation)
{
    if (confirmation ? confirm('¿Está seguro de quitar este ítem de la lista?') : true)
    {
        var divObj = $('#' + div_id);

        var rowObj = $('#' + instance_id);
        rowObj.remove();

        var count = parseInt(divObj.data('count'));

        //COUNT DE ITEMS
        divObj.data('count', count - 1);

        SIANComboGetIds(div_id);
        SIANComboUpdateAmounts(div_id, true);
    }
}

function SIANComboGetIds(div_id) {

    window.sianComboIds.splice(0, window.sianComboIds.length);

    var divObj = $('#' + div_id);
    //Recorremos rows
    divObj.find('table.sian-combo-items tr.sian-combo-item').each(function (index) {

        var rowObj = $(this);
        var product_id = rowObj.find('input.sian-combo-item-sub-product-id').val();
        window.sianComboIds.push(product_id);
    });

    if (divObj.data('count') === 0)
    {
        divObj.find('table.sian-combo-items tbody').html(SIANComboGetNullRow());
    }
}

function SIANComboGetNullRow()
{
    return '<tr><td class=\'empty\' colspan=\'99\'>' + STRINGS_NO_DATA + '</td></tr>';
}
