<?php

class OperationRepository implements IOperationRepository {

    /**
     * @param int $scenarioId
     * @param int|null $internal
     * @param string $operationCode
     * @param int|null $default
     * @return array|null
     */
    public function getFirstOperation($scenarioId, $internal = null, $operationCode = null, $default = null) {
        $command = $this->buildQuery($scenarioId, $internal, $operationCode, $default);
        return $command->queryRow();
    }

    /**
     * @param int $scenarioId
     * @param int|null $internal
     * @param string $operationCode
     * @param int|null $default
     * @return array
     */
    public function getAllOperations($scenarioId, $internal = null, $operationCode = null, $default = null) {
        $command = $this->buildQuery($scenarioId, $internal, $operationCode, $default);
        return $command->queryAll();
    }

    /**
     * @param int $scenarioId
     * @param int|null $internal
     * @param string $operationCode
     * @param int|null $default
     * @return CDbCommand
     */
    private function buildQuery($scenarioId, $internal = null, $operationCode = null, $default = null) {

        $conditions = 'OP2.type = :type AND OP2.owner = :owner AND OP2.owner_id = O.operation_id';
        $params = [
            ':type' => OwnerPair::TYPE_SCENARIO_OPERATION,
            ':owner' => Operation::OWNER
        ];

        if ($internal !== null) {
            $conditions .= ' AND O.internal = ' . (int) $internal;
        }

        if ($operationCode !== null) {
            $conditions .= ' AND O.operation_code = :operation_code';
            $params[':operation_code'] = $operationCode;
        }

        if ($default !== null) {
            $conditions .= ' AND OP2.default = ' . (int) $default;
        }

        $command = Yii::app()->db->createCommand()
                ->select([
                    'O.operation_id',
                    'O.operation_code',
                    'O.require_extra_movement',
                    'O.accounting_file_id',
                    'OP2.default',
                ])
                ->from('operation O')
                ->join(
                        'owner_pair OP2',
                        $conditions,
                        $params
                )
                ->join(
                'owner_pair OP1',
                'OP1.pair_id = OP2.parent_id AND OP1.owner_id = :owner_id',
                [
                    ':owner_id' => $scenarioId
                ]
        );

        return $command;
    }

}
