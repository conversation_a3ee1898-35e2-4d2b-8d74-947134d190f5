<?php

class SIANWithholding extends CWidget {

    public $id;
    public $form;
    public $model;
    public $readonly = false;
    public $fixed = 2;
    public $step = 0.01;
    public $parent_routes = [];
    //CONTROL
    private $controller;
    public $aux_person_autocomplete_id = null;
    //PRIVATE
    private $reset_id;
    private $link_movement_id;
    private $link_aux_id;
    private $link_value_id;
    private $link_text_id;
    private $link_account_code_id;
    private $link_owner_input_id;
    private $link_owner_id_input_id;
    private $link_aux_person_id;
    private $link_amount_id;
    private $link_emission_date_id;
    private $link_route_id;
    private $link_currency_id;
    private $link_exchange_id;
    private $link_balance_pen_id;
    private $link_balance_usd_id;
    private $link_balance_total_pen_id;
    private $link_balance_total_usd_id;
    private $link_total_id;
    private $link_sunat_code;
    private $add_button_id;

    public function init() {

        //CONTROL
        if (is_null($this->aux_person_autocomplete_id)) {
            throw new Exception('Debe especificar el ID de buscador de Involucrado!');
        }

        $this->controller = Yii::app()->controller;

        //GRID ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //PRIVATE
        $this->reset_id = $this->controller->getServerId();
        $this->link_aux_id = $this->controller->getServerId();
        $this->link_value_id = $this->controller->getServerId();
        $this->link_text_id = $this->controller->getServerId();
        $this->link_movement_id = $this->controller->getServerId();
        $this->link_account_code_id = $this->controller->getServerId();
        $this->link_owner_input_id = $this->controller->getServerId();
        $this->link_owner_id_input_id = $this->controller->getServerId();
        $this->link_aux_person_id = $this->controller->getServerId();
        $this->link_route_id = $this->controller->getServerId();
        $this->link_currency_id = $this->controller->getServerId();
        $this->link_exchange_id = $this->controller->getServerId();
        $this->link_emission_date_id = $this->controller->getServerId();
        $this->link_balance_pen_id = $this->controller->getServerId();
        $this->link_balance_usd_id = $this->controller->getServerId();
        $this->link_amount_id = $this->controller->getServerId();
        $this->link_balance_total_pen_id = $this->controller->getServerId();
        $this->link_balance_total_usd_id = $this->controller->getServerId();
        $this->link_total_id = $this->controller->getServerId();
        $this->link_sunat_code = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();

        //MOVEMENT ITEMS
        $movementItems = [];
        foreach ($this->model->tempLinks as $link) {
            $row = Yii::app()->db->createCommand()
                    ->select("M.currency, M.exchange_rate as exchange_rate, CM.real_pen as balance_total_pen, CM.real_usd as balance_total_usd, D.sunat_code ")
                    ->from('commercial_movement CM ')
                    ->join('movement M', 'M.movement_id = CM.movement_id ')
                    ->join('document D', 'M.document_code=D.document_code')
                    ->where("M.movement_id = {$link->movement_parent_id}")
                    ->queryRow();

            // transformamos los datos de la consulta al tipo de cambio de la retencion
            $exchange_rate = $this->model->movement->exchange_rate;
            $balance_total_pen = 0;
            $balance_total_usd = 0;

            if ($row['currency'] == "pen") {
                $balance_total_pen = $row['balance_total_pen'];
                $balance_total_usd = round($row['balance_total_pen'] / $exchange_rate, $this->fixed);
            } else {
                $balance_total_pen = round($row['balance_total_usd'] * $exchange_rate, $this->fixed);
                $balance_total_usd = $row['balance_total_usd'];
            }
            if ($this->model->movement->currency == "pen") {
                $row['total'] = $balance_total_pen;
            } else {
                $row['total'] = $balance_total_usd;
            }

            $row['balance_total_pen'] = $balance_total_pen;
            $row['balance_total_usd'] = $balance_total_usd;

            $attributes = [];
            array_push($attributes, "movement_parent_id:'{$link->movement_parent_id}'");
            array_push($attributes, "parent_entry_id:'{$link->parent_entry_id}'");
            array_push($attributes, "parent_account_code:'{$link->parent_account_code}'");
            array_push($attributes, "parent_owner:'{$link->parent_owner}'");
            array_push($attributes, "parent_owner_id:'{$link->parent_owner_id}'");
            array_push($attributes, "parent_route:'{$link->parent_route}'");
            array_push($attributes, "parent_document:'{$link->parent_document}'");
            array_push($attributes, "parent_aux_person_id:'{$link->parent_aux_person_id}'");
            array_push($attributes, "parent_aux_person_name:'{$link->parent_aux_person_name}'");
            array_push($attributes, "parent_emission_date:'{$link->parent_emission_date}'");
            array_push($attributes, "parent_currency:'{$link->parent_currency}'");
            array_push($attributes, "parent_exchange_rate:'{$link->parent_exchange_rate}'");
            array_push($attributes, "balance_pen:" . round($link->balance_pen, $this->fixed));
            array_push($attributes, "balance_usd:" . round($link->balance_usd, $this->fixed));
            array_push($attributes, "amount:" . round($link->amount, $this->fixed));
            array_push($attributes, "amount_error:'{$link->getError('amount')}'");
            array_push($attributes, "balance_total_pen:{$row['balance_total_pen']}");
            array_push($attributes, "balance_total_usd:{$row['balance_total_usd']}");
            array_push($attributes, "total:{$row['total']}");
            array_push($attributes, "sunat_code:'{$row['sunat_code']}'");

            array_push($movementItems, '{' . implode(',', $attributes) . "}");
        }

        $movementItems = '[' . implode(',', $movementItems) . ']';

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-withholding.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        var aux_person_id = USAutocompleteField('{$this->aux_person_autocomplete_id}', 'value');
        window.auxPersonIds = aux_person_id.length > 0 ? [aux_person_id] : [];

        //ACCESSOS
        $('#{$this->id}').data('view-access', " . CJSON::encode($this->getAccess()) . ");
        //COUNT    
        $('#{$this->id}').data('fixed', {$this->fixed});
        $('#{$this->id}').data('step', {$this->step});
        $('#{$this->id}').data('count', 0);
        $('#{$this->id}').data('labels', {
            parent_document: '{$this->model->movement->getAttributeLabel('links.document')}',
            parent_aux_person_name: '{$this->model->movement->getAttributeLabel('links.movement.aux_person_id')}',
            parent_emission_date: '{$this->model->movement->getAttributeLabel('links.emission_date')}',
            amount: '{$this->model->movement->getAttributeLabel('linkParents.amount')}',
        });

        //MONEDA
        $('#{$this->id}').data('currency', '{$this->model->movement->currency}');

        //MOVIMIENTOS
        var array = {$movementItems};

        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANWithholdingAddItem('{$this->id}', array[i]['movement_parent_id'], array[i]['parent_entry_id'], array[i]['parent_account_code'], array[i]['parent_owner'], array[i]['parent_owner_id'], array[i]['parent_route'], array[i]['parent_document'], array[i]['parent_aux_person_id'], array[i]['parent_aux_person_name'], array[i]['parent_emission_date'], array[i]['parent_currency'], array[i]['parent_exchange_rate'], array[i]['balance_pen'], array[i]['balance_usd'], array[i]['amount'], array[i]['amount_error'], array[i]['balance_total_pen'], array[i]['balance_total_usd'], array[i]['total'], array[i]['sunat_code'], " . json_encode($this->readonly) . ");
            }
        }
        else
        {
            $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
        }

        //UPDATE
        SIANWithholdingUpdate('{$this->id}');
        unfocusable();

        //CURRENCY SYMBOL
        $('span.currency-symbol').text('" . Currency::getSymbol($this->model->movement->currency) . "');

        //CHANGE 
        $('#{$this->id}').data('changeAux', function(aux_person_id){

            $('tr.sian-withholding-item').each(function(index) {
                SIANWithholdingRemoveItem('{$this->id}', $(this).attr('id'), false);
            });

            window.auxPersonIds.splice(0, window.auxPersonIds.length);
            if(typeof aux_person_id !== 'undefined')
            {
                window.auxPersonIds.push(aux_person_id);
            }

            {$this->id}Clear();
        });

        $('#{$this->id}').data('changeCurrency', function(currency){

            var table = $('#{$this->id}');
            var exchange_rate = table.data('exchange_rate');

            table.find('tr.sian-withholding-item').each(function(index) {
                var item = $(this);
                var balance_pen = item.find('input.sian-withholding-item-balance-pen').floatVal({$this->fixed});
                var balance_usd = item.find('input.sian-withholding-item-balance-usd').floatVal({$this->fixed});
                var balance_total_pen = item.find('input.sian-withholding-item-balance-total-pen').floatVal({$this->fixed});
                var balance_total_usd = item.find('input.sian-withholding-item-balance-total-usd').floatVal({$this->fixed});

                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-withholding-item-amount').toPen(exchange_rate, {$this->fixed}, {max: balance_pen});
                        item.find('input.sian-withholding-item-total').floatVal({$this->fixed}, balance_total_pen);
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-withholding-item-amount').toUsd(exchange_rate, {$this->fixed}, {max: balance_usd}); 
                        item.find('input.sian-withholding-item-total').floatVal({$this->fixed}, balance_total_usd);
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
            });

            table.data('currency', currency);
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            //Limpiamos
            {$this->id}Clear();
            //Actualizamos los montos
            SIANWithholdingUpdateAmounts('{$this->id}');
        });
        
        $('#{$this->id}').data('changeExchange', function(exchange_rate){

            var table = $('table#{$this->id}');
            //Seteamos
            table.data('exchange_rate', exchange_rate); 
            //Obtenemos
            var currency = table.data('currency');
            //MONTOS
            table.find('tr.sian-withholding-item').each(function(index) {
                var item = $(this);

                var item_currency = item.find('input.sian-withholding-item-currency').val();
                var balance_pen = item.find('input.sian-withholding-item-balance-pen').floatVal({$this->fixed});
                var balance_usd = item.find('input.sian-withholding-item-balance-usd').floatVal({$this->fixed});
                var balance_total_pen = item.find('input.sian-withholding-item-balance-total-pen').floatVal({$this->fixed});
                var balance_total_usd = item.find('input.sian-withholding-item-balance-total-usd').floatVal({$this->fixed});
                    
                //Cambiamos los balances
                switch(item_currency)
                {
                    case '" . Currency::PEN . "':
                        balance_usd = balance_pen / exchange_rate;
                        balance_total_usd = balance_total_pen / exchange_rate;
                    break;
                    case '" . Currency::USD . "':
                        balance_pen = balance_usd * exchange_rate;
                        balance_total_pen = balance_total_usd * exchange_rate;
                     break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
                
                //Actualizamos max
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-withholding-item-amount').floatAttr('max', {$this->fixed}, balance_pen);
                        item.find('input.sian-withholding-item-total').floatVal({$this->fixed}, balance_total_pen);
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-withholding-item-amount').floatAttr('max', {$this->fixed}, balance_usd);
                        item.find('input.sian-withholding-item-total').floatVal({$this->fixed}, balance_total_usd);
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }

                //Seteamos balances
                
                item.find('input.sian-withholding-item-balance-pen').floatVal({$this->fixed}, balance_pen);
                item.find('input.sian-withholding-item-balance-usd').floatVal({$this->fixed}, balance_usd);
                item.find('input.sian-withholding-item-balance-total-pen').floatVal({$this->fixed}, balance_total_pen);
                item.find('input.sian-withholding-item-balance-total-usd').floatVal({$this->fixed}, balance_total_usd);
            });
            
            //Limpiamos
            {$this->id}Clear();
            //Actualizamos los montos
            SIANWithholdingUpdateAmounts('{$this->id}');
        });

        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var movement_parent_id = $('#{$this->link_movement_id}').val();
            var parent_entry_id = $('#{$this->link_value_id}').val();
            var parent_account_code = $('#{$this->link_account_code_id}').val();
            var parent_owner = $('#{$this->link_owner_input_id}').val();
            var parent_owner_id = $('#{$this->link_owner_id_input_id}').val();
            var parent_route = $('#{$this->link_route_id}').val();
            var parent_document = $('#{$this->link_aux_id}').val();
            var parent_aux_person_id = $('#{$this->link_aux_person_id}').val();
            var parent_aux_person_name = $('#{$this->link_text_id}').val();
            var parent_emission_date = $('#{$this->link_emission_date_id}').val();
            var parent_currency = $('#{$this->link_currency_id}').val();
            var parent_exchange_rate = $('#{$this->link_exchange_id}').val();
            var balance_pen = $('#{$this->link_balance_pen_id}').floatVal({$this->fixed});
            var balance_usd = $('#{$this->link_balance_usd_id}').floatVal({$this->fixed});
            var amount = $('#{$this->link_amount_id}').floatVal({$this->fixed});
            var balance_total_pen = $('#{$this->link_balance_total_pen_id}').floatVal({$this->fixed});
            var balance_total_usd = $('#{$this->link_balance_total_usd_id}').floatVal({$this->fixed});
            var total = $('#{$this->link_total_id}').floatVal({$this->fixed});
            var sunat_code = $('#{$this->link_sunat_code}').val();

            if (movement_parent_id.length === 0)
            {
                $('#{$this->link_aux_id}').focus();
                return;
            }            

            SIANWithholdingAddItem('{$this->id}', movement_parent_id, parent_entry_id, parent_account_code, parent_owner, parent_owner_id, parent_route, parent_document, parent_aux_person_id, parent_aux_person_name, parent_emission_date, parent_currency, parent_exchange_rate, balance_pen, balance_usd, amount, '', balance_total_pen, balance_total_usd, total, sunat_code, " . json_encode($this->readonly) . ");
            {$this->id}Clear();
                
            //FOCUS
            $('#{$this->link_aux_id}').focus();
            SIANWithholdingUpdate('{$this->id}');
            unfocusable();
        });
        
        function {$this->id}Clear()
        {
            $('#{$this->reset_id}').click();

            $('#{$this->link_route_id}').val(null);
            $('#{$this->link_emission_date_id}').val(null);
            $('#{$this->link_balance_pen_id}').val(0);
            $('#{$this->link_balance_usd_id}').val(0);
            $('#{$this->link_balance_total_pen_id}').val(0);
            $('#{$this->link_balance_total_usd_id}').val(0);
            $('#{$this->link_amount_id}').val(0).attr('max', 0);
            $('#{$this->link_total_id}').val(0);
            $('#{$this->link_sunat_code}').val(0);
        }
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Comprobantes/Notas de débito',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempLinks') ? 'us-error' : ''
            )
        ));

        if (!$this->readonly) {
            echo "<div class='row'>";
            echo "<div class='col-lg-5 col-md-5 col-sm-5 col-xs-12'>";
            echo $this->widget('application.widgets.USAutocomplete', array(
                'reset_id' => $this->reset_id,
                'label' => 'Comprobante/Nota de débito',
                'name' => null,
                'aux_id' => $this->link_aux_id,
                'value_id' => $this->link_value_id,
                'text_id' => $this->link_text_id,
                'width' => '700px',
                'view' => array(
                    'model' => 'DpMovementsForRetention',
                    'scenario' => $this->model->movement->scenario->direction == Scenario::DIRECTION_OUT ? DpMovementsForRetention::SCENARIO_PURCHASE : DpMovementsForRetention::SCENARIO_SALE,
                    'attributes' => array(
                        array('name' => 'pk', 'hidden' => true, 'types' => array('id', 'value'), 'search' => false, 'not_in' => "window.entry_ids"),
                        array('name' => 'movement_id', 'hidden' => true, 'update' => "$('#{$this->link_movement_id}').val(movement_id);"),
                        array('name' => 'account_code', 'hidden' => true, 'update' => "$('#{$this->link_account_code_id}').val(account_code);"), array('name' => 'document', 'width' => 20, 'types' => array('aux')),
                        array('name' => 'owner', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->link_owner_input_id}').val(owner);"),
                        array('name' => 'owner_id', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->link_owner_id_input_id}').val(owner_id);"),
                        array('name' => 'route', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->link_route_id}').val(route);"),
                        array('name' => 'aux_person_name', 'width' => 35, 'types' => array('text')),
                        array('name' => 'identification_number', 'hidden' => true),
                        array('name' => 'emission_date', 'width' => 15, 'update' => "$('#{$this->link_emission_date_id}').val(emission_date);"),
                        array('name' => 'currency', 'hidden' => true, 'update' => "$('#{$this->link_currency_id}').val(currency);"),
                        array('name' => 'currency_name', 'width' => 10),
                        array('name' => 'exchange_rate', 'hidden' => true, 'update' => "$('#{$this->link_exchange_id}').val(exchange_rate);"),
                        array('name' => 'sunat_code', 'hidden' => true, 'width' => 10, 'update' => "$('#{$this->link_sunat_code}').val(sunat_code);"),
                        array('name' => 'total', 'width' => 10, 'update' => "
                                var form_currency = $('#{$this->id}').data('currency');
                                var exchange_rate = $('#{$this->id}').data('exchange_rate');
                                var balance_total_pen = 0;
                                var balance_total_usd = 0;
                                var amount = 0;
                                

                                if(currency == '" . Currency::PEN . "')
                                {
                                    balance_total_pen = total;
                                    balance_total_usd = USMath.round(total / exchange_rate, {$this->fixed});
                                }
                                else
                                {
                                    balance_total_pen = USMath.round(total * exchange_rate, {$this->fixed});
                                    balance_total_usd = total;
                                }
                                                                
                                if(form_currency == '" . Currency::PEN . "')
                                {
                                    amount = balance_total_pen;
                                }
                                else
                                {
                                    amount = balance_total_usd;
                                }
                                
                                $('#{$this->link_balance_total_pen_id}').floatVal({$this->fixed}, balance_total_pen);
                                $('#{$this->link_balance_total_usd_id}').floatVal({$this->fixed}, balance_total_usd);
                                $('#{$this->link_total_id}').floatAttr('max', {$this->fixed}, amount).floatVal({$this->fixed}, amount);    
                            "),
                        array('name' => 'balance', 'hidden' => true),
                        array('name' => 'ret_balance', 'width' => 10, 'update' => "
    
                                if(parseFloat(ret_balance) < parseFloat(balance))
                                {
                                    balance = ret_balance;
                                }

                                var form_currency = $('#{$this->id}').data('currency');
                                var exchange_rate = $('#{$this->id}').data('exchange_rate');
                                var balance_pen = 0;
                                var balance_usd = 0;
                                var amount = 0;
                                

                                if(currency == '" . Currency::PEN . "')
                                {
                                    balance_pen = balance;
                                    balance_usd = USMath.round(balance / exchange_rate, {$this->fixed});
                                }
                                else
                                {
                                    balance_pen = USMath.round(balance * exchange_rate, {$this->fixed});
                                    balance_usd = balance;
                                }
                                                                
                                if(form_currency == '" . Currency::PEN . "')
                                {
                                    amount = balance_pen;
                                }
                                else
                                {
                                    amount = balance_usd;
                                }
                                
                                $('#{$this->link_balance_pen_id}').floatVal({$this->fixed}, balance_pen);
                                $('#{$this->link_balance_usd_id}').floatVal({$this->fixed}, balance_usd);
                                $('#{$this->link_amount_id}').floatAttr('max', {$this->fixed}, amount).floatVal({$this->fixed}, amount);    
                            "),
                        array('name' => 'aux_person_id', 'width' => 0, 'in' => 'window.auxPersonIds', 'hidden' => true, 'update' => "$('#{$this->link_aux_person_id}').val(aux_person_id);"),
                        array('name' => 'route', 'hidden' => true, 'search' => false, 'in' => CJSON::encode($this->parent_routes)),
                    )
                ),
                'onreset' => "
                    $('#{$this->link_emission_date_id}').val(null);
                    $('#{$this->link_balance_pen_id}').val(0);
                    $('#{$this->link_balance_usd_id}').val(0);
                    $('#{$this->link_amount_id}').val(0).attr('max', 0);
                    $('#{$this->link_balance_total_pen_id}').val(0);
                    $('#{$this->link_balance_total_usd_id}').val(0);
                    $('#{$this->link_total_id}').val(0).attr('max', 0);
                    ",
                'onsearch' => "
                        if(window.auxPersonIds.length === 0)
                        {
                            bootbox.alert(us_message('Debe seleccionar un {$this->model->movement->getAttributeLabel('aux_person_id')}', 'warning'));
                            USAutocompleteFocus('{$this->aux_person_autocomplete_id}');
                            return false;
                        }
                        ",
                    ), true);
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_aux_person_id,
            ));
            echo "</div>";
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo SIANForm::textFieldNonActive($this->model->movement->getAttributeLabel('emission_date'), null, null, array(
                'id' => $this->link_emission_date_id,
                'readonly' => true,
                'placeholder' => $this->model->movement->getAttributeLabel('emission_date'),
            ));
            echo "</div>";
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo SIANForm::numberFieldNonActive("Total <span class='currency-symbol'></span>", null, 0, array(
                'id' => $this->link_total_id,
                'min' => 0,
                'readonly' => true,
            ));
            echo "</div>";
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo SIANForm::numberFieldNonActive("A retener <span class='currency-symbol'></span>", null, 0, array(
                'id' => $this->link_amount_id,
                'min' => 0,
                'step' => $this->step
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-6 text-center'>";

            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_account_code_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_owner_input_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_owner_id_input_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_sunat_code,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_balance_pen_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_balance_usd_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_balance_total_pen_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_balance_total_usd_id,
            ));
            echo CHtml::hiddenField(null, null, array(
                'id' => $this->link_route_id,
            ));
            echo CHtml::hiddenField(null, null, array(
                'id' => $this->link_movement_id,
            ));
            echo CHtml::hiddenField(null, null, array(
                'id' => $this->link_currency_id,
            ));
            echo CHtml::hiddenField(null, null, array(
                'id' => $this->link_exchange_id,
            ));
            echo CHtml::label('Agregar', $this->add_button_id, array(
            ));
            echo "<br/>";
            $this->widget('application.widgets.USButtons', array(
                'buttons' => array(
                    array(
                        'id' => $this->add_button_id,
                        'context' => 'primary',
                        'icon' => 'fa fa-lg fa-plus white',
                        'size' => 'default',
                        'title' => 'Añadir'
                    )
                )
            ));
            echo "</div>";

            echo "</div>";

            echo "<hr>";
        }

        echo "<table id='{$this->id}' class='table table-condensed table-hover'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th width='5%'>#</th>";
        echo "<th width='15%'>{$this->model->movement->getAttributeLabel('links.document')}</th>";
        echo "<th width='40%'>{$this->model->movement->getAttributeLabel('links.movement.aux_person_id')}</th>";
        echo "<th width='15%'>{$this->model->movement->getAttributeLabel('links.emission_date')}</th>";
        echo "<th width='10%'>Total <span class='currency-symbol'></span></th>";
        echo "<th width='10%'>Retención <span class='currency-symbol'></span></th>";
        echo "<th width='5%'></th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody></tbody>";
        echo "<tfoot>";
        echo "<tr>";
        echo "<td colspan='4' style='text-align:right'><b>TOTAL <span class='currency-symbol'></span>:</b></td>";
        echo "<td style='text-align:right'>";
        echo $this->form->numberFieldRow($this->model, 'total', array(
            'label' => false,
            'class' => "form-control sian-withholding-total-amount us-double{$this->fixed}",
            'style' => "text-align:right",
            'value' => 0,
            'readonly' => true
        ));
        echo "</td>";
        echo "<td></td>";
        echo "</tr>";
        echo "</tfoot>";
        echo "</table>";

        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->textFieldRow($this->model->movement, 'observation', array(
            'placeholder' => 'Puede escribir una observación acerca del movimiento',
            'required' => $this->model->movement->isObservationRequired()
        ));
        echo "</div>";
        echo "</div>";
        $this->endWidget();
    }

    private function getAccess() {
        $parent_routes = [];
        foreach ($this->parent_routes as $parent_route) {
            $parent_routes[] = "/{$parent_route}/view";
        }
        return $this->controller->getUrls($parent_routes);
    }

}
