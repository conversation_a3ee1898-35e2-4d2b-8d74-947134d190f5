<?php

//class TestController extends SIANWSController {
class TestController extends MainController {

    public $plural = 'Test';
    public $singular = 'Test';

    public function accessRules() {
        return array(
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('productosRelacionados', 'code', 'raw', 'qz', 'correlative', 'decrypt', 'googleMap', 'kardex', 'test', 'testNoBD', 'testBD', 'pagination', 'mail', 'preview', 'ascii', 'viewtopdf', 'getBarcode', 'correctResponse', 'close', 'log', 'fixsql', 'reniec', 'tc', 'rest', 'personAlias', 'changelog', 'reniec', 'payu', 'paymentLogs', 'freshdesk', 'replace', 'replicateWebName', 'fullRoute', 'version', 'reaccount', 'reaccounted', 'exchange', 'exchangeSunat', 'exchangeRangeSunat', 'exchangeByDateSunat', 'exchangeByDateMigo', 'exchangeFromSian', 'getRuc', 'getMigoRuc', 'getDni', 'getMigoDni', 'getQr', 'axios', 'bitly'),
                'users' => array('*'),
            ),
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('websocket'),
                'users' => array('*'),
            ),
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('clearCache'),
                'users' => array('@'),
            ),
            array('deny', // deny all users
                'users' => array('*'),
            ),
        );
    }

    public function actionCode($id = null) {
        $id = isset($id) ? $id : 1;

        $a_codes = [];
        for ($i = 0; $i < $id; $i++) {
            $a_codes[] = USTime::getDateCode();
        }

        foreach ($a_codes as $s_code) {
            echo $s_code . "</br>";
        }

        echo "<hr>";

        foreach ($a_codes as $s_code) {
            echo "S1@n" . $s_code . "</br>";
        }

        //$micro = microtime();
        //$org = $this->getOrganization()->organization_code;
        //$code = "AB12345678";
        //echo $code;
    }

    public function actionQz() {
        //$this->render('qz');
        $this->render('qz', [], false, true);
    }

    public function actionDecrypt() {
        echo USEncryptation::decrypt('327<66=>9C?=');
    }

    public function actionGoogleMap() {
        $this->renderPartial('googleMap', array(
            'data' => array(
            )
        ));
    }

    public function actionXTest($string, $delimiter = '|', $each = 5) {

        $data = [];
        $words = explode($delimiter, $string);
        $i = 0;
        while ($i < count($words)) {
            $row = [];
            for ($j = 0; $j < $each; $j++) {
                $row[] = $words[$i];
                $i++;
            }
            $data[] = $row;
        }


        echo "<table border=1>";
        foreach ($data as $row) {
            echo "<tr>";
            foreach ($row as $column) {
                echo "<td>{$column}</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    }

    public function actionTestNoBD() {
        echo CJSON::encode(array('code' => USREST::CODE_SUCCESS, 'message' => 'Load test without connection to BD'));
    }

    public function actionTestBD() {

        $criteria = new USCriteria();
        $criteria->select = array('username', 'level');
        $criteria->condition = '`status`';
        $data = User::model()->findToArray($criteria);

        echo CJSON::encode(array('code' => USREST::CODE_SUCCESS, 'message' => 'Load test with connection to BD', 'data' => $data));
    }

    public function actionGetBarcode() {
        USImage::echoBarcode('je678h');
    }

    public function actionClose() {
        Yii::app()->session->clear();
    }

    public function actionLog() {
        var_dump(CJSON::decode("Hola"));
    }

    public function actionFixsql() {
        header('Content-Type: text/html; charset=utf-8');

        if (Yii::app()->params['environment'] === YII_ENVIRONMENT_DEVELOPMENT) {
            $s_path = realpath(Yii::app()->params['admin_dir'] . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'sql.code' . DIRECTORY_SEPARATOR . 'SIAN' . DIRECTORY_SEPARATOR . 'MASTER');
            $this->exploreFolder($s_path);
        } else {
            echo 'Esta funcionalidad sólo está disponible en el ambiente de desarrollo';
        }
    }

    public function exploreFolder($path) {

        $s_jump = "\n";

        $a_basename = scandir($path, SCANDIR_SORT_ASCENDING);

        foreach ($a_basename as $s_basename) {
            if (!in_array($s_basename, [".", ".."])) {

                $s_filename = $path . DIRECTORY_SEPARATOR . $s_basename;

                if (is_file($s_filename)) {
                    //echo "<p>" . $s_filename . "</p>";

                    $s_content = file_get_contents($s_filename);

                    $a_rows = explode($s_jump, $s_content);

                    $s_sql = '';
                    foreach ($a_rows as $row) {

                        if (USString::contains($row, 'DROP') && USString::contains($row, 'IF EXISTS') && USString::contains($row, "$$")) {
                            $s_sql = str_replace("$$", ";", $row) . $s_jump . $s_jump . $s_sql;
                        } else {
                            $s_sql .= $row . $s_jump;
                        }
                    }

                    echo '<pre>' . htmlentities($s_sql) . '</pre>';
                } else {
                    $this->exploreFolder($s_filename);
                }
            }
        }
    }

    public function actionWebsocket() {
        $this->render('websocket');
    }

    /**
     * Esta acción debe estar en un controlador que herede directo de CController
     * De lo contrario nunca se podrá borrar element_id de la caché de redis
     */
    public function actionClearCache($key = null) {

        if (isset($key)) {
            SIANCache::delete($key);
            echo "Clave borrada con éxito!";
        } else {
            SIANCache::flushAll();
            echo "Caché borrada con éxito!";
        }
    }

    public function actionTC() {
        $this->renderPartial('tc');
    }

    public function actionRest() {
        $s_login_url = "http://sian/admin/api/signin";
        $s_ws_url = "http://sian/admin/api/shoppingCart/export/107";

        $jwt = USREST::consumeLogin($s_login_url, [
                    "username" => "hardtechweb",
                    "password" => "H@rdt3chW3b"
                ])->data->token;

        $data = USREST::consumeGet($s_ws_url, [
                    'app-authorization' => $jwt
                        ], []);
        echo CJSON::encode($data);
    }

    public function _insertChangelog($p_s_owner, $p_a_items) {
        $a_changelog = [];
        foreach ($p_a_items as $a_item) {
            $a_changelog[] = [
                'log_code' => USTime::getDateCode(),
                'owner' => $p_s_owner,
                'owner_id' => $a_item['owner_id'],
                'owner_pk' => $a_item['owner_id'],
                'to_string' => $a_item['to_string'],
                'action' => Changelog::CREATE,
                'type' => Changelog::MULTIPLE,
                'date' => SIANTime::now(),
                'ws' => 'TestController (Masivo)',
                'route' => 'test/changelog',
                'person_id' => $this->getOrganization()->person_id,
            ];
        }

        return USDatabase::multipleInsert('Changelog', $a_changelog);
    }

    public function actionChangelog() {
        $i = 0;
        //Completamos changelog para mark
        $a_marks = Yii::app()->db->createCommand()
                ->select([
                    'MA.mark_id AS owner_id',
                    'MA.mark_name as to_string'
                ])
                ->from('mark MA')
                ->leftJoin('changelog CH', "CH.`owner` = '" . Mark::OWNER . "' and CH.owner_id = MA.mark_id AND CH.last_by_object")
                ->where('CH.changelog_id IS NULL')
                ->queryAll();

        $i += $this->_insertChangelog(Mark::OWNER, $a_marks);

        //Completamos changelog para merchandise
        $a_merchandises = Yii::app()->db->createCommand()
                ->select([
                    'M.product_id AS owner_id',
                    'P.product_name as to_string'
                ])
                ->from('merchandise M')
                ->join('product P', 'P.product_id = M.product_id')
                ->leftJoin('changelog CH', "CH.`owner` = '" . Merchandise::OWNER . "' and CH.owner_id = M.product_id AND CH.last_by_object")
                ->where('CH.changelog_id IS NULL')
                ->queryAll();

        $i += $this->_insertChangelog(Merchandise::OWNER, $a_merchandises);

        echo $i . ' filas afectadas';
    }

    public function actionAssocBy() {
        $s_x = "[
        {group_id: 1, item_id: 1, value: 1},
        {group_id: 1, item_id: 1, value: 2},
        {group_id: 1, item_id: 1, value: 3},
        {group_id: 3, item_id: 2, value: 4},
        {group_id: 2, item_id: 2, value: 5},
        {group_id: 3, item_id: 2, value: 6},
        {group_id: 2, item_id: 2, value: 7}
    ]";

        $a_x = CJSON::decode($s_x);

        $a_y = USArray::assocBy($a_x, ['group_id', 'item_id']);

        var_dump($a_y);
    }

    public function actionPayU($consult = true) {

        //Iniciamos Payu
        USPayU::init();

        $s_reference_code = USTime::getDateCode(true);

        $s_card_number = '****************';
        $s_card_expiration = '2023/03';
        $s_card_security_code = '992';
        $s_card_payment_method = 'VISA';
        $s_card_quotes = 1;

        //Seteamos payer name
        if (Yii::app()->params['environment'] === YII_ENVIRONMENT_PRODUCTION) {
            $s_payer_name = 'JEAN PAUL PEREA OLIVARES';
        } else {
            $s_payer_name = 'PENDING';
        }

        $a_parameters = [
            PayUParameters::ACCOUNT_ID => Yii::app()->params['payu_account_id'],
            PayUParameters::REFERENCE_CODE => $s_reference_code,
            PayUParameters::DESCRIPTION => "Compra de productos a través de API",
            PayUParameters::VALUE => "10.00",
            PayUParameters::CURRENCY => "PEN",
            //Comprador
            PayUParameters::BUYER_NAME => 'JEAN PAUL PEREA',
            PayUParameters::BUYER_EMAIL => '<EMAIL>',
            PayUParameters::BUYER_CONTACT_PHONE => '*********',
            PayUParameters::BUYER_DNI => '********',
            PayUParameters::BUYER_STREET => "",
            PayUParameters::BUYER_CITY => "",
            PayUParameters::BUYER_STATE => "",
            PayUParameters::BUYER_COUNTRY => PayUCountries::PE,
            PayUParameters::BUYER_POSTAL_CODE => '00000',
            // -- pagador --
            //Ingrese aquí el nombre del pagador.
            PayUParameters::PAYER_NAME => $s_payer_name,
            PayUParameters::PAYER_BUSINESS_NAME => 'TEST',
            PayUParameters::PAYER_EMAIL => '<EMAIL>',
            // -- Datos de la tarjeta de crédito -- 
            //Ingrese aquí el número de la tarjeta de crédito
            PayUParameters::CREDIT_CARD_NUMBER => $s_card_number,
            PayUParameters::CREDIT_CARD_EXPIRATION_DATE => $s_card_expiration,
            PayUParameters::CREDIT_CARD_SECURITY_CODE => $s_card_security_code,
            PayUParameters::PAYMENT_METHOD => $s_card_payment_method,
            PayUParameters::INSTALLMENTS_NUMBER => $s_card_quotes,
            PayUParameters::COUNTRY => PayUCountries::PE,
            PayUParameters::DEVICE_SESSION_ID => 'vghs6tvkcle931686k1900o6e1',
            PayUParameters::IP_ADDRESS => isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown',
            //Cookie de la sesión actual.        
            PayUParameters::USER_AGENT => $_SERVER['HTTP_USER_AGENT']
        ];

        $o_result = PayUPayments::doAuthorizationAndCapture($a_parameters);

        //Guardamos en BD
//        if (Yii::app()->params['mongo_enabled']) {
//            Yii::app()->mongo->insertOne('payment_logs', [
//                'reference_code' => $s_reference_code,
//                'interface' => 'doAuthorizationAndCapture',
//                'datetime' => SIANTime::now(),
//                'parameters' => $a_parameters,
//                'response' => $o_result,
//            ]);
//        }

        var_dump($o_result);

//        $parameters = array(PayUParameters::REFERENCE_CODE => $s_reference_code);
//        $response = PayUReports::getOrderDetailByReferenceCode($parameters);
//
//        var_dump($response);
    }

    public function actionPaymentLogs() {
        $a_items = Yii::app()->db->createCommand()->select(['*'])
                ->from('payment_logs')
                ->queryAll();

        foreach ($a_items as $a_item) {

            $parameters = unserialize($a_item['sent_data']);
            $response = unserialize($a_item['recieved_data']);

            Yii::app()->mongo->insertOne('payment_logs', [
                'reference_code' => $parameters['referenceCode'],
                'interface' => $a_item['interface'],
                'transaction_id' => $response->transactionResponse->transactionId,
                'datetime' => SIANTime::now(),
                'parameters' => $parameters,
                'response' => $response,
            ]);
        }

        //Eliminamos tabla payment_logs
        $sql = "drop table if exists payment_logs";
        Yii::app()->db->createCommand($sql)->execute();
    }

    public function actionFreshdesk() {
        $api_key = "********************";
        $password = "Sist3m@s";
        $yourdomain = "grupohardtech";
// Return the tickets that are new or opend & assigned to you
// If you want to fetch all tickets remove the filter query param
        $url = "https://$yourdomain.freshdesk.com/api/v2/tickets?order_by=updated_at&order_type=desc";
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_USERPWD, "$api_key:$password");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $server_output = curl_exec($ch);
        $info = curl_getinfo($ch);
        $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $headers = substr($server_output, 0, $header_size);
        $response = substr($server_output, $header_size);
        if ($info['http_code'] == 200) {
            //echo "Tickets fetched successfully, the response is given below \n";
            //echo "Response Headers are \n";
            //echo $headers . "\n";
            //echo "Response Body \n";
            //echo "$response \n";
            header('Content-type: application/json; charset=utf-8');
            echo json_encode(json_decode($response), JSON_PRETTY_PRINT);
        } else {
            if ($info['http_code'] == 404) {
                echo "Error, Please check the end point \n";
            } else {
                echo "Error, HTTP Status Code : " . $info['http_code'] . "\n";
                echo "Headers are " . $headers;
                echo "Response are " . $response;
            }
        }
        curl_close($ch);
    }

    public function actionReplace($id) {
        echo USCreditCard::getCreditCardType($id);
        echo "<br>";
        echo USCreditCard::hideCreditCardNumber($id);
    }

    public function actionReplicateWebName() {
        $a_products = Product::model()->findAll(array(
            'select' => ['P.product_id', 'P.web_name', 'P.product_type'],
            'alias' => 'P',
            'condition' => 'P.status AND P.web_name <> P.product_name'));

        $a_errors = [];

        foreach ($a_products as $o_product) {

            $o_product->product_name = $o_product->web_name;
            $o_product->alias = USString::generateAlias($o_product->web_name);
            $o_product->scenario = Merchandise::SCENARIO_LOGISTIC;

            if ($o_product->validate(['product_name', 'alias', 'product_type'])) {
                $o_product->updateByPk($o_product->primaryKey, array(
                    'product_name' => $o_product->product_name,
                    'alias' => $o_product->alias
                ));
            } else {
                $a_errors[] = $o_product->product_id;
            }
        }
        if (count($a_errors) == 0) {
            echo 'Actualización exitosa';
        } else {
            echo 'No se pudo actualizar los id:<br>';
            echo implode(',', $a_errors);
        }
    }

    public function actionFullRoute() {

        echo $this->createFullRoute('create');
        echo "<br>";
        echo $this->createFullRoute('saleOrder/create');
        echo "<br>";
        echo $this->createFullRoute('api/saleOrder/create');
        echo "<br>";
        echo $this->createFullRoute('/saleOrder/create');
        echo "<br>";
        echo $this->createFullRoute('/api/saleOrder/create');
    }

    public function actionVersion() {
        echo Yii::getVersion();
    }

    public function actionReaccount($mode = 0, $param = 100) {

        ini_set('max_execution_time', 600);
        header('Content-type: application/json');

        try {
            //Iniciamos transacción
            $this->beginTransaction();

            //Ids de movimientos ya contados
            $a_reaccount_ids = SIANCache::get('reaccount_ids3');
            if ($a_reaccount_ids == false) {
                $a_reaccount_ids = [0];
            }

            /*
             * SELECT E.movement_id, M.type
              FROM entry E
              JOIN movement M ON M.`movement_id` = E.`movement_id`
              JOIN accounting_movement AM ON AM.`movement_id` = M.`movement_id` AND AM.`year` >= 2020
              WHERE M.`status`
              GROUP BY E.`movement_id`, E.`entry_number`
              HAVING
              ABS(SUM(IF(E.`column` = 'Debe', E.`amount_pen`, 0)) - SUM(IF(E.`column` = 'Haber', E.`amount_pen`, 0))) > 0
              OR
              ABS(SUM(IF(E.`column` = 'Debe', E.`amount_usd`, 0)) - SUM(IF(E.`column` = 'Haber', E.`amount_usd`, 0))) > 0
             */
            //Obtenemos tipos de cambios temporales de días anteriores
            $o_query = Yii::app()->db->createCommand()
                    ->select([
                        'E.movement_id',
                        'M.type'
                    ])
                    ->from('entry E')
                    ->join('movement M', 'M.movement_id = E.movement_id AND M.status')
                    ->join('accounting_movement AM', 'AM.movement_id = M.movement_id AND AM.year >= 2020')
                    ->group('E.movement_id');

            switch ($mode) {
                case 0:
                    $o_query->where('E.movement_id = :movement_id', [
                        ':movement_id' => $param
                    ]);
                    break;
                case 1:
                    $o_query
                            ->where('E.movement_id NOT IN (' . implode(',', $a_reaccount_ids) . ')')
                            ->having("ROUND(ABS(SUM(IF(E.`column` = '" . Entry::DEBIT_COLUMN . "', E.amount_pen, 0)) - SUM(IF(E.`column` = '" . Entry::CREDIT_COLUMN . "', E.amount_pen, 0))), 4) > 0
                            OR
                            ROUND(ABS(SUM(IF(E.`column` = '" . Entry::DEBIT_COLUMN . "', E.amount_usd, 0)) - SUM(IF(E.`column` = '" . Entry::CREDIT_COLUMN . "', E.amount_usd, 0))), 4) > 0")
                            ->limit($param);
                    break;
                default:
                    throw new Exception('Modo no soportado');
            }

            $a_items = $o_query->queryAll();

            $a_show_movement = [];
            //
            foreach ($a_items as $a_item) {
                $o_xmovement = $this->_loadMovement($a_item['movement_id'], $a_item['type']);
                //Se lee el asiento
                $a_entries = [];
                foreach ($o_xmovement->movement->accountingMovement->entries as $o_entry) {
                    $a_entries[] = [
                        'entry_id' => $o_entry->entry_id,
                        'entry_number' => $o_entry->entry_number,
                        'column' => $o_entry->column,
                        'account_code' => $o_entry->account_code,
                        'account_name' => $o_entry->account_code . ' - ' . $o_entry->account->account_name,
                        'owner' => $o_entry->owner,
                        'owner_id' => $o_entry->owner_id,
                        'owner_name' => $o_entry->ownerName->owner_aux . ' - ' . $o_entry->ownerName->owner_name,
                        'fee_number' => $o_entry->fee_number,
                        'status' => $o_entry->status,
                        'detail' => $o_entry->detail,
                        'exchange_rate' => $o_entry->exchange_rate,
                        'expiration_date' => $o_entry->expiration_date,
                        'amount' => $o_entry->{"amount_{$o_xmovement->movement->currency}"},
                        'dynamic_account' => $o_entry->dynamic_account,
                        'field' => $o_entry->field,
                        'destiny' => $o_entry->destiny,
                        //CC
                        'has_combination' => $o_entry->account->has_combination,
                        'combination_id' => isset($o_entry->combination) ? $o_entry->combination->combination_id : null,
                        'combination_name' => isset($o_entry->combination) ? $o_entry->combination->combination_id . ' - ' . $o_entry->combination->combination_name : null,
                        //
                        'locked' => ($o_entry->entry_link_count > 0 || $o_entry->has_parent == 1 || !in_array($o_entry->owner, [Combination::OWNER])) ? 1 : 0,
                        'distributable' => in_array($o_entry->owner, [Combination::OWNER]) ? 1 : 0
                    ];
                }
                //Seteamos    
                $o_xmovement->movement->accountingMovement->currency = $o_xmovement->movement->currency;
                $o_xmovement->movement->accountingMovement->account_mode = SIANDynamicDataValidator::MODE_EXCHANGE;
                $o_xmovement->movement->accountingMovement->dynamic_data = [
                    'currency' => $o_xmovement->movement->currency,
                    'entries' => $a_entries
                ];
                //Validamos sólo los campos necesarios
                $o_xmovement->movement->accountingMovement->save_validation_log = true;
                if ($o_xmovement->movement->accountingMovement->validate([
                            'dynamic_data',
                            'tempEntries'
                        ])) {
                    ///Actualizamos el modelo       
                    $o_xmovement->movement->accountingMovement->saveEntries();
                    //Ability
                    Ability::generateAbility($o_xmovement->movement_id, [], []);
                    //
                    $a_reaccount_ids[] = $o_xmovement->movement_id;
                } else {
                    throw new Exception("Hubo un error al realizar la validación del movimiento {$o_xmovement->toString()} ({$o_xmovement->movement->route}) con ID {$o_xmovement->movement_id}");
                }

                $a_show_movement[] = [
                    'id' => $a_item['movement_id'],
                    'document' => $o_xmovement->toString(),
                    'emission_date' => $o_xmovement->movement->emission_date,
                    'link' => $this->createAbsoluteUrl("/{$o_xmovement->movement->route}/view", [
                        'id' => $a_item['movement_id']
                    ])
                ];
            }

            SIANCache::add('reaccount_ids3', $a_reaccount_ids);

            echo CJSON::encode([
                'movement' => $a_show_movement
            ]);

            $this->commitTransaction();
        } catch (CDbException $ex) {
            USLog::save($ex);
            $this->rollbackTransaction();
            echo $ex->errorInfo[2];
        } catch (Exception $ex) {
            USLog::save($ex);
            $this->rollbackTransaction();
            echo $ex->getMessage();
        }
    }

    private function _loadMovement($id, $type) {

        $class = $type . 'Movement';

        return $class::model()->with(array(
                    'movement' => [
                        'select' => array('M.operation_code', 'M.type', 'M.`status`', 'M.currency', 'M.document_code', 'M.document_serie', 'M.document_correlative', 'M.route', 'M.emission_date', 'M.expiration_date', 'M.exchange_rate', 'M.aux_person_id'),
                        'alias' => 'M',
                        'with' => [
                            //Empleado
                            'person' => array(
                                'select' => array('P.identification_type', 'P.person_name'),
                                'joinType' => 'join',
                                'alias' => 'P'
                            ),
                            //Proveedor
                            'auxPerson' => array(
                                'select' => array('XP.identification_type', 'XP.identification_number', 'XP.person_name'),
                                'joinType' => 'join',
                                'alias' => 'XP',
                                'with' => array(
                                    'officialAddress.geoloc',
                                )
                            ),
                            'document' => array(
                                'select' => array('D.document_name'),
                                'joinType' => 'join',
                                'alias' => 'D',
                            ),
                            'operation' => array(
                                'select' => array('O.operation_name'),
                                'joinType' => 'join',
                                'alias' => 'O',
                            ),
                            'scenario' => array(
                                'select' => array('S.route', 'S.accounting_file_id', 'S.affect_credit'),
                                'joinType' => 'join',
                                'alias' => 'S',
                            ),
                            'store' => array(
                                'select' => array('S.store_name'),
                                'joinType' => 'join',
                                'alias' => 'ST'
                            ),
                            'accountingMovement' => array(
                                'alias' => 'AM',
                                'with' => array(
                                    'accountingDay' => array(
                                        'alias' => 'AD',
                                    ),
                                    'accountingFile' => array(
                                        'alias' => 'AF',
                                    ),
                                    'entries' => array(
                                        'alias' => 'E',
                                        'order' => 'E.entry_number, E.`order`',
                                        //Los asientos válidos para provisión son los que no son ajustes y que sean mayor que cero (en la moneda)
                                        //'condition' => "/*(E.dynamic_account IS NULL OR E.dynamic_account NOT IN ('" . Account::WILDCARD_MISSING . "', '" . Account::WILDCARD_SURPLUS . "')) AND */IF(M.currency = '" . Currency::PEN . "', E.amount_pen, E.amount_usd) > 0",
                                        'condition' => "E.dynamic_account IS NULL OR E.dynamic_account NOT IN ('" . Account::WILDCARD_MISSING . "', '" . Account::WILDCARD_SURPLUS . "')",
                                        'with' => array(
                                            'entryLinks' => [
                                                'select' => ['EI.entry_parent_id'],
                                                'alias' => 'EI'
                                            ],
                                            'entryChildLinks' => [
                                                'select' => ['ECL.entry_parent_id'],
                                                'alias' => 'ECL'
                                            ],
                                            'account' => array(
                                                'select' => ['account_name', 'owner'],
                                                'alias' => 'A',
                                            ),
                                            'ownerName' => array(
                                                'select' => ['owner_id', 'owner_aux', 'owner_name'],
                                                'alias' => 'OW',
                                            ),
                                            'combination' => [
                                                'select' => ['combination_id', 'combination_name']
                                            ]
                                        )
                                    )
                                )
                            ),
                            'linkParents' => [
                                'select' => ['ML.movement_parent_id'],
                                'alias' => 'ML',
                            ],
                        ]
                    ],
                ))->findByPk([
                    'movement_id' => $id
        ]);
    }

    public function actionReaccounted() {
        $a_reaccount_ids = SIANCache::get('reaccount_ids3');
        echo implode('<br>', $a_reaccount_ids);
        SIANCache::delete('reaccount_ids3');
    }

    public function actionExchange($begin, $end) {
        header('Content-type: application/json');

        echo CJSON::encode(USSBS::getExchangeRate($begin, $end));
    }

    public function actionExchangeSunat($year, $month, $all) {
        header('Content-type: application/json');

        echo CJSON::encode(SIANSunat::getExchangeRateByMonth($year, $month, $all));
    }

    public function actionExchangeRangeSunat($begin, $end, $all) {
        header('Content-type: application/json');

        echo CJSON::encode(SIANSunat::getExchangeRate($begin, $end, $all));
    }

    public function actionExchangeByDateSunat($date) {
        header('Content-type: application/json');

        echo CJSON::encode(SIANSunat::getExchangeRateByDate($date));
    }

    public function actionExchangeByDateMigo($date) {
        header('Content-type: application/json');

        echo CJSON::encode(USMigo::getExchangeRateByDate($date), Yii::app()->params['migo_token']);
    }

    public function actionExchangeFromSian() {
        header('Content-type: application/json');

        $a_tcs = Yii::app()->dbSian->createCommand()
                ->select([
                    'EX.date',
                    'EX.purchase',
                    'EX.sale'
                ])
                ->from('exchange_rate EX')
                ->where("EX.`date` >= '2020-12-01'")
                ->queryAll();

        echo CJSON::encode($a_tcs);
    }

    public function actionGetRuc($ruc) {
        header('Content-type: application/json');
        $a_result = SIANSunat::getRUCData($ruc);
        echo CJSON::encode($a_result);
    }

    public function actionGetMigoRuc($ruc) {
        header('Content-type: application/json');
        $s_token = Yii::app()->params['migo_token'];
        $a_result = USMigo::getRUCData($s_token, $ruc);
        echo CJSON::encode($a_result);
    }

    public function actionGetDni($dni) {
        header('Content-type: application/json');
        $a_result = SIANReniec::getDNIData($dni);
        echo CJSON::encode($a_result);
    }

    public function actionGetMigoDni($dni) {
        header('Content-type: application/json');
        $s_token = Yii::app()->params['migo_token'];
        $a_result = USMigo::getDNIData($s_token, $dni);
        echo CJSON::encode($a_result);
    }

    public function actionGetQr($texto) {
        echo USImage::getBarcodeData($texto);
    }

    public function actionAxios() {
        $this->renderPartial('axios', array(
            'data' => array(
            )
        ));
    }

    public function actionRaw() {
        $o_model = (new DpSaleOrder(DpSaleOrder::SCENARIO_API_POS));
        $o_model->addCondition("[register_date] >= :from AND [register_date] <= :to", [
            ':from' => '30/05/2021 04:30:46 PM',
            ':to' => '30/06/2021 05:30:46 PM'
                ], [
            ':from' => 'register_date',
            ':to' => 'register_date'
        ]);
        echo CJSON::encode($o_model->findAll());
    }

    public function actionBitly() {
        $a_data = USBitly::shortLink("****************************************", "https://hardtech.siansystem.com/admin/public/evaluateCreditNoteRequest.html?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJwaW5fY29kZSI6Ijc1V1JNN1dLNjNUSzk0IiwibW92ZW1lbnRfY29kZSI6Ijc1V1E3MlFOOU5QN0EiLCJ1c2VyX2lkIjoxNTQsInBlcnNvbl9uYW1lIjoiUEVSRUEgT0xJVkFSRVMgSkVBTiBQQVVMIiwicmVhc29uX2NvZGUiOiIwMSIsImNhc2hiYWNrIjoxfQ.52Sp2Y3rzFThSeXBGXtSiTFZvSYBoAuntpk0tAQFXBs&state=ACCEPTED");
        var_dump($a_data);
    }

}
