<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANJWT
 *
 * <AUTHOR>
 */
class SIANJWT {

    public static function encodeToken(array $p_a_data, $p_b_encode_code = true) {
        //Generamos código
        $s_code = USTime::getDateCode();
        //Códificamos token
        $s_jwt = $p_b_encode_code ? USJWT::encode($s_code, Yii::app()->params['jwt_secret']) : $s_code;
        //Guardamos token en Caché
        $p_a_data['jwt'] = $s_jwt;
        SIANCache::add($s_code, (object) $p_a_data);
        //
        return $s_jwt;
    }

    public static function decodeToken($p_s_jwt, $p_b_decode_code = true) {
        //Decodificamos
        $s_code = $p_b_decode_code ? USJWT::decode($p_s_jwt, Yii::app()->params['jwt_secret']) : $p_s_jwt;
        //Verificamos token
        if (is_string($s_code)) {
            $o_token_data = SIANCache::get($s_code);
            return $o_token_data;
        } else {
            return false;
        }
        //
    }

    public static function deleteToken($p_s_jwt, $p_decode_code = true) {
        //Decodificamos
        $s_code = $p_decode_code ? USJWT::decode($p_s_jwt, Yii::app()->params['jwt_secret']) : $p_s_jwt;
        //Eliminamos token
        if (is_string($s_code)) {
            SIANCache::delete($s_code);
        }
    }

}
