function getLocalId() {
    return 'local_' + getLocalIndex();
}

function getLocalIndex() {

    if (typeof window.local_index === 'undefined') {
        window.local_index = 0;
    }

    window.local_index++;
    return window.local_index;
}

function us_formatNumber(number, precision)
{
    number = number.toFixed(precision) + '';
    x = number.split('.');
    x1 = x[0];
    x2 = x.length > 1 ? '.' + x[1] : '';
    var rgx = /(\d+)(\d{3})/;
    while (rgx.test(x1)) {
        x1 = x1.replace(rgx, '$1' + ',' + '$2');
    }
    return x1 + x2;
}

function seriesNoRepeat(text) {
    var seriesArray = text.split("\n");
    var length = seriesArray.length;
    var lastSerie = seriesArray[length - 1];
    for (var i = 0; i < seriesArray.length - 1; i++) {
        if (seriesArray[i] == lastSerie) {
            bootbox.alert(us_message('La serie ' + lastSerie + ' esta repetido.', 'warning'));
            seriesArray.splice(seriesArray.length - 1, 1);
        }
    }

    return seriesArray.join("\n");
}


function lineCount(text) {
    return text.split("\n").length;
}

//STRINGS FUNCTIONS
function toUpper(e, elemento) {
    tecla = (document.all) ? e.keyCode : e.which;
    elemento.value = elemento.value.toUpperCase();
}

function chr(i) {
    return String.fromCharCode(i);
}

String.prototype.repeat = function (n, d) {
    return --n ? this + (d || "") + this.repeat(n, d) : "" + this;
};

function strcmp(a, b) {
    if (a.toString() < b.toString())
        return -1;
    if (a.toString() > b.toString())
        return 1;
    return 0;
}


function isDouble(evt) {
    var charCode = (evt.which) ? evt.which : event.keyCode;
    if (charCode != 46 && charCode > 31 && (charCode < 48 || charCode > 57)) {
        return false;
    } else {
        return true;
    }

    // var validChars = [44,46,48,49,50,51,52,53,54,55,56,57] 
    // for(var i = 0; i < val.length; i++) {
    //     if(validChars.indexOf(val.charAt(i)) == -1)
    //         return false;
    // }
    // return true;
}

function isNumber(evt) {
    var charCode = (evt.which) ? evt.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
        return false;
    } else {
        return true;
    }
}

function noSpace(evt) {
    var charCode = (evt.which) ? evt.which : event.keyCode;
    if (charCode == 32) {
        return false;
    } else {
        return true;
    }
}

function lpad(str, char, max) {
    str = str.toString();
    return str.length < max ? lpad(char + str, char, max) : str;
}

function now()
{
    //return dateFormat(new Date(), 'dd/mm/yyyy h:MM:ss TT');
    return dateFormat(new Date(), 'dd/mm/yyyy hh:MM:ss TT');
}

function buildUrl(base, key, value) {
    var sep = (base.indexOf('?') > -1) ? '&' : '?';
    return base + sep + key + '=' + value;
}


function saveUri(uri, filename, type)
{
    var downloadLink = document.createElement("a");
    downloadLink.href = uri;
    downloadLink.download = filename + '.' + type;
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

function addslashes(str) {
    str = str.replace(/\\/g, '\\\\');
    str = str.replace(/\'/g, '\\\'');
    str = str.replace(/\"/g, '\\"');
    str = str.replace(/\0/g, '\\0');
    return str;
}
function stripslashes(str) {
    str = str.replace(/\\'/g, '\'');
    str = str.replace(/\\"/g, '"');
    str = str.replace(/\\0/g, '\0');
    str = str.replace(/\\\\/g, '\\');
    return str;
}

function isset(value)
{
    return !(typeof value === 'undefined' || value === null);
}

function isBlank(value)
{
    return (typeof value === 'undefined' || value === null || value === '');
}

function inArray(needle, haystack) {
    var length = haystack.length;
    for (var i = 0; i < length; i++) {
        if (haystack[i] == needle)
            return true;
    }
    return false;
}


function isEspecialChar(charCode)
{
    return (charCode >= 0 && charCode <= 31);
}

function isDecimalSeparatorChar(charCode)
{
    return (charCode === 44 || charCode === 46);
}

function isDigitChar(charCode)
{
    return (charCode >= 48 && charCode <= 57);
}

function toPen(value, exchange_rate, digits, options) {

    if (typeof options === 'undefined')
    {
        options = {};
    }

    if (typeof digits === 'undefined')
    {
        digits = 2;
    }

    var tolerance = 1 / Math.pow(10, digits);

    //VALUE
    value = value * exchange_rate;

    if (typeof options.min !== 'undefined')
    {
        value = (USMath.round(Math.abs(value - options.min), digits) <= tolerance || value < options.min) ? options.min : value;
    }

    if (typeof options.max !== 'undefined')
    {
        value = (USMath.round(Math.abs(value - options.max), digits) <= tolerance || value > options.max) ? options.max : value;
    }

    return USMath.round(value, digits).toFixed(digits);
}

function toUsd(value, exchange_rate, digits, options) {

    if (typeof options === 'undefined')
    {
        options = {};
    }

    if (typeof digits === 'undefined')
    {
        digits = 2;
    }

    var tolerance = 1 / Math.pow(10, digits);

    //VALUE
    value = value / exchange_rate;

    if (typeof options.min !== 'undefined')
    {
        value = (USMath.round(Math.abs(value - options.min), digits) <= tolerance || value < options.min) ? options.min : value;
    }

    if (typeof options.max !== 'undefined')
    {
        value = (USMath.round(Math.abs(value - options.max), digits) <= tolerance || value > options.max) ? options.max : value;
    }

    return USMath.round(value, digits).toFixed(digits);
}

String.prototype.strtr = function (replacePairs) {
    var str = this.toString(), key, re;
    for (key in replacePairs) {
        if (replacePairs.hasOwnProperty(key)) {
            re = new RegExp(key, "g");
            str = str.replace(re, replacePairs[key]);
        }
    }
    return str;
};

function generateAlias(str) {
    return str.strtr({
        "À": "A",
        "Á": "A",
        "Â": "A",
        "Ã": "A",
        "Ä": "Ae",
        "Å": "A",
        "Æ": "A",
        "Ă": "A",
        "à": "a",
        "á": "a",
        "â": "a",
        "ã": "a",
        "ä": "ae",
        "å": "a",
        "ă": "a",
        "æ": "ae",
        "þ": "b",
        "Þ": "B",
        "Ç": "C",
        "ç": "c",
        "È": "E",
        "É": "E",
        "Ê": "E",
        "Ë": "E",
        "è": "e",
        "é": "e",
        "ê": "e",
        "ë": "e",
        "Ğ": "G",
        "ğ": "g",
        "Ì": "I",
        "Í": "I",
        "Î": "I",
        "Ï": "I",
        "İ": "I",
        "ı": "i",
        "ì": "i",
        "í": "i",
        "î": "i",
        "ï": "i",
        "Ñ": "N",
        "Ò": "O",
        "Ó": "O",
        "Ô": "O",
        "Õ": "O",
        "Ö": "Oe",
        "Ø": "O",
        "ö": "oe",
        "ø": "o",
        "ð": "o",
        "ñ": "n",
        "ò": "o",
        "ó": "o",
        "ô": "o",
        "õ": "o",
        "Š": "S",
        "š": "s",
        "Ş": "S",
        "ș": "s",
        "Ș": "S",
        "ş": "s",
        "ß": "ss",
        "ț": "t",
        "Ț": "T",
        "Ù": "U",
        "Ú": "U",
        "Û": "U",
        "Ü": "Ue",
        "ù": "u",
        "ú": "u",
        "û": "u",
        "ü": "ue",
        "Ý": "Y",
        "ý": "y",
        "ý": "y",
        "ÿ": "y",
        "Ž": "Z",
        "ž": "z"
    })
            .replace('-', ' ')
            .replace('_', ' ')
            .replace(/\s+/g, '-')
            .replace(/[^A-Za-z0-9\-]/g, '')
            .toLowerCase()
            .trim();
}


/**
 * Genera el step para los campos de texto tipo number u otros
 * @param {Number} decimals Número de decimales
 * @returns {Number} Step
 */
function toStep(decimals)
{
    return 1 / Math.pow(10, decimals);
}

var removeTildes = (function () {
    var from = "ÃÀÁÄÂÈÉËÊÌÍÏÎÒÓÖÔÙÚÜÛãàáäâèéëêìíïîòóöôùúüûÑñÇç",
            to = "AAAAAEEEEIIIIOOOOUUUUaaaaaeeeeiiiioooouuuunncc",
            mapping = {};

    for (var i = 0, j = from.length; i < j; i++)
        mapping[ from.charAt(i) ] = to.charAt(i);

    return function (str) {
        var ret = [];
        for (var i = 0, j = str.length; i < j; i++) {
            var c = str.charAt(i);
            if (mapping.hasOwnProperty(str.charAt(i)))
                ret.push(mapping[ c ]);
            else
                ret.push(c);
        }
        return ret.join('');
    };

})();

function consoleLog()
{
    if (PARAMS_ENVIRONMENT === YII_ENVIRONMENT_DEVELOPMENT)
    {
        var count = arguments.length;

        if (count === 1)
        {
            console.log(arguments[0]);
        } else
        {
            var array = [];
            for (var i = 0; i < count; i++) {
                array.push(arguments[i]);
            }
            console.log(array);
        }
    }
}

function isEquivalent(a, b) {
    return JSON.stringify(a) === JSON.stringify(b);
}

function getRMultiplicator(igv_affection, include_igv, force_igv)
{
    return include_igv == 0 && ((igv_affection == IGV_AFFECTION_AFFECTED || igv_affection == IGV_AFFECTION_FREE) || force_igv == 1);
}

//function iAmount(igv_affection, include_igv, force_igv)
//{
//    var a = (igv_affection == IGV_AFFECTION_AFFECTED || igv_affection == IGV_AFFECTION_FREE);
//    var b = a && include_igv == 1;
//    var c = !a && force_igv == 1;
//    return b || c;
//}

function getCommercialAmounts(params, include_igv, force_igv, igv_global, perception_global, tfixed, ifixed, round_mode)
{
    let igv = params.igv?  params.igv : igv_global;
    var f_crude = USMath.round(USMath.multiply(params.pres_quantity, USMath.minus(params.price, params.discount)), tfixed, round_mode);
    var f_affected1 = USMath.round(params.igv_affection == IGV_AFFECTION_AFFECTED ? USMath.divide(f_crude, USMath.plus(1, (include_igv == 1 ? igv : 0))) : 0, ifixed, round_mode);
    var f_inaffected = USMath.round(params.igv_affection == IGV_AFFECTION_INAFFECTED ? USMath.multiply(f_crude, USMath.plus(1, (include_igv == 0 && force_igv == 1 ? igv : 0))) : 0, tfixed, round_mode);
    var f_nobill = USMath.round(params.igv_affection == IGV_AFFECTION_EXONERATED ? USMath.multiply(f_crude, USMath.plus(1, (include_igv == 0 && force_igv == 1 ? igv : 0))) : 0, tfixed, round_mode);
    var f_export = USMath.round(params.igv_affection == IGV_AFFECTION_EXPORT ? USMath.multiply(f_crude, USMath.plus(1, (include_igv == 0 && force_igv == 1 ? igv : 0))) : 0, tfixed, round_mode);
    var f_free = USMath.round(params.igv_affection == IGV_AFFECTION_FREE ? USMath.divide(f_crude, USMath.plus(1, (include_igv == 1 ? igv : 0))) : (params.igv_affection == IGV_AFFECTION_GIFT ? USMath.multiply(f_crude, USMath.plus(1, (include_igv == 0 && force_igv == 1 ? igv_global : 0))) : 0), tfixed, round_mode);
    var f_nnet = USMath.round(USMath.plus(USMath.plus(USMath.plus(USMath.plus(f_affected1, f_inaffected), f_nobill), f_export), f_free), ifixed, round_mode);
    var f_fnet = f_free;
    var f_net = USMath.round(USMath.minus(f_nnet, f_fnet), ifixed, round_mode);

//    console.log({
//        f_crude: f_crude,
//        f_affected1: f_affected1,
//        f_inaffected: f_inaffected,
//        f_nobill: f_nobill,
//        f_export: f_export,
//        f_free: f_free,
//        f_nnet: f_nnet,
//        f_fnet: f_fnet,
//        f_net: f_net
//    });

    if (include_igv == 1)
    {
        var f_ntotal = USMath.round(f_crude, ifixed, round_mode);
        var f_ftotal = USMath.round(params.igv_affection == IGV_AFFECTION_FREE || params.igv_affection == IGV_AFFECTION_GIFT ? f_crude : 0, tfixed, round_mode);
        var f_nigv = USMath.round(USMath.minus(f_ntotal, f_nnet), ifixed, round_mode);
        var f_figv = USMath.round(params.igv_affection == IGV_AFFECTION_FREE ? USMath.minus(f_ftotal, f_fnet) : 0, tfixed, round_mode);
    } else
    {
        var f_nigv = USMath.round(params.igv_affection == IGV_AFFECTION_AFFECTED || params.igv_affection == IGV_AFFECTION_FREE ? USMath.multiply(f_nnet, igv) : 0, ifixed, round_mode);
        var f_figv = USMath.round(params.igv_affection == IGV_AFFECTION_FREE ? USMath.multiply(f_fnet, igv) : 0, tfixed, round_mode);
        var f_ntotal = USMath.round(USMath.plus(f_nnet, f_nigv), tfixed, round_mode);
        var f_ftotal = USMath.round(params.igv_affection == IGV_AFFECTION_FREE || params.igv_affection == IGV_AFFECTION_GIFT ? USMath.plus(f_fnet, f_figv) : 0, tfixed, round_mode);
    }

//    console.log({
//        f_nigv: f_nigv,
//        f_figv: f_figv,
//        f_ntotal: f_ntotal,
//        f_ftotal: f_ftotal,
//    });

    var f_igv = USMath.round(USMath.minus(f_nigv, f_figv), ifixed, round_mode);
    var f_total = USMath.round(USMath.minus(f_ntotal, f_ftotal), tfixed, round_mode);
    var f_perception = USMath.round(params.perception_affected == 1 && params.direction == SCENARIO_DIRECTION_OUT ? USMath.multiply(f_total, perception_global) : 0, tfixed, round_mode);

    var f_nreal = USMath.round(USMath.plus(f_ntotal, f_perception), tfixed, round_mode);
    var f_freal = USMath.round(f_ftotal, tfixed, round_mode);
    var f_real = USMath.round(USMath.minus(f_nreal, f_freal), tfixed, round_mode);

//    console.log({
//        f_igv: f_igv,
//        f_total: f_total,
//        f_perception: f_perception,
//        f_nreal: f_nreal,
//        f_freal: f_freal,
//        f_real: f_real,
//    });

    var totals = {
        product_ids: [],
        crude: f_crude,
        affected1: f_affected1,
        inaffected: f_inaffected,
        nobill: f_nobill,
        export: f_export,
        free: f_free,
        nnet: f_nnet,
        fnet: f_fnet,
        net: f_net,
        nigv: f_nigv,
        figv: f_figv,
        igv: f_igv,
        ntotal: f_ntotal,
        ftotal: f_ftotal,
        total: f_total,
        perception: f_perception,
        nreal: f_nreal,
        freal: f_freal,
        real: f_real
    };

    return totals;
}

function countdown(id, seconds, hide, finish_label) {

    var countdownTimer = setInterval(function () {
        var days = Math.floor(seconds / 24 / 60 / 60);
        var hoursLeft = Math.floor((seconds) - (days * 86400));
        var hours = Math.floor(hoursLeft / 3600);
        var minutesLeft = Math.floor((hoursLeft) - (hours * 3600));
        var minutes = Math.floor(minutesLeft / 60);
        var remainingSeconds = seconds % 60;
        function pad(n) {
            return (n < 10 ? "0" + n : n);
        }
        var value = "";
        if (hide) {
            if (days > 0) {
                value += pad(days) + ":";
            }
            if (hours > 0) {
                value += pad(hours) + ":";
            }
            if (minutes > 0) {
                value += pad(minutes) + ":";
            }
            value += pad(remainingSeconds);
        } else {
            value = pad(days) + ":" + pad(hours) + ":" + pad(minutes) + ":" + pad(remainingSeconds);
        }
        document.getElementById(id).innerHTML = value;
        if (seconds == 0) {
            clearInterval(countdownTimer);
            document.getElementById(id).innerHTML = (isset(finish_label) ? finish_label : "Completado");
        } else {
            seconds--;
        }
    }, 1000);
}

function formatEquivalence(numberString) {
    return Number(numberString).toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }