<?php

class USSimpleSelect2 extends CWidget {

    public $id;
    public $form;
    public $model;
    public $attribute;
    public $required = null;
    public $asDropDownList = true;
    public $multiple = false;
    public $items = [];
    public $tags = [];
    public $tokenSeparators = [];
    //PRIVATE
    private $controller;
    private $options;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('other/select2/js/select2.min.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
        //
        SIANAssets::registerCssFile('other/select2/css/select2.css');

        //
        $this->options = [
            'tokenSeparators' => $this->tokenSeparators,
            'placeholder' => $this->model->getAttributeLabel($this->attribute),
        ];

        if ($this->asDropDownList) {
            if (count($this->tags) > 0) {
                throw new Exception('No se permite el atributo tags cuando el valor de asDropDownList es true');
            }
        } else {
            $this->options['tags'] = $this->tags;
        }

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id . '_ajax', "
      
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div class='form-group " . ($this->model->hasErrors($this->attribute) ? 'has-error' : '') . "'>";
        echo $this->form->labelEx($this->model, $this->attribute, [
            'class' => 'control-label',
            'required' => isset($this->required) ? $this->required : $this->model->isAttributeRequired($this->attribute)
        ]);
        $this->widget('booster.widgets.TbSelect2', array(
            'asDropDownList' => $this->asDropDownList,
            'model' => $this->model,
            'attribute' => $this->attribute,
            'data' => $this->items,
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => 'form-control',
                'multiple' => $this->multiple ? 'multiple' : null
            ),
            'options' => $this->options
        ));
        echo SIANForm::errorBlock($this->model, $this->attribute);
        echo "</div>";
    }

}
