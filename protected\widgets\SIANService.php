<?php

class SIANService extends CWidget {

    public $id;
    public $grid_id;
    public $form;
    public $model;
    public $modal_id = null;
    public $currency_items = [];
    public $readonly = false;
    public $measure_id = null;
    public $measure_items = [];
    public $custom_measure_items = [];
    public $item_types = [];
    public $store_items = [];
    public $category_items = [];
    //PRIVATE
    private $controller;
    private $price_mode_id;
    private $allow_decimals_id;
    private $print_dispatch_ticket_id;

    //IDS

    public function init() {

        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->grid_id = isset($this->grid_id) ? $this->grid_id : $this->controller->getServerId();
        //
        $this->price_mode_id = $this->controller->getServerId();
        $this->allow_decimals_id = $this->controller->getServerId();
        $this->print_dispatch_ticket_id = $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-service.js');
        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id, " 
        
        //Llamamos
        SIANPresentationChangePriceMode('{$this->grid_id}', {$this->model->product->price_mode});                
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}'>";
        echo "<div class='row'>";
        echo "<div class='col-lg-8 col-md-8 col-sm-6 col-xs-12'>";
        $this->renderGeneral();
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-12'>";
        $this->renderSwitches();
        echo "</div>";
        echo "</div>";
        //Renderizamos presentación
        $this->renderPresentation();
        echo "</div>";
    }

    private function renderGeneral() {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "Datos generales",
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-8 col-md-8 col-sm-12 col-xs-12'>";
        echo $this->form->textFieldRow($this->model->product, 'product_name', []);
        echo $this->form->textAreaRow($this->model->product, 'description', array(
            'rows' => '5',
        ));
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->product, 'item_type_id', $this->item_types, array());
        echo $this->form->dropDownListRow($this->model, 'service_category_id', $this->category_items, array());
        echo $this->form->numberFieldRow($this->model->product, 'expiration', []);        
        echo "</div>";
        echo "</div>";

//        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
//        $this->widget('application.widgets.USSwitch', array(
//            'id' => $this->price_mode_id,
//            'model' => $this->model->product,
//            'attribute' => 'price_mode',
//            'switchChange' => "SIANPresentationChangePriceMode('{$this->grid_id}', (this.checked ? 1 : 0));"
//        ));
//        echo "</div>";       
//        echo "<div class='row'>";
//        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
//        echo $this->form->dropDownListRow($this->model->product, 'price_currency', $this->currency_items, [
//            'onchange' => " var currency = $(this).val();
//                            $('#{$this->grid_id}').data('changeCurrency')(currency);"]);
//        echo '</div>';
//        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
//        echo SIANForm::textFieldNonActive('TC', 'exchangeRate', Yii::app()->controller->getLastExchange(), ['readonly' => 1]);
//        echo '</div>';
//        echo "</div>";

        $this->endWidget();
    }

    private function renderSwitches() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Indicadores',
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
                'class' => 'sian-basic'
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-6'>";
        echo $this->widget('application.widgets.USSwitch', array('model' => $this->model->product, 'attribute' => 'status'), true);
        echo "</div>";        
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-6'>";
        echo $this->widget('application.widgets.USSwitch', array('model' => $this->model->product, 'attribute' => 'in_kiosk'), true);
        echo "</div>";
        echo "</div>";
        
        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-6'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'id' => $this->allow_decimals_id,
            'model' => $this->model->product,
            'attribute' => 'allow_decimals',
            'switchChange' => !$this->model->product->lock_decimals ? "
                $('#{$this->grid_id}').data('allowDecimals')(this.checked);               
            " : "",
            'disabled' => $this->model->product->lock_decimals,
            'hint' => 'En equivalencias'
                ), true);
        echo $this->form->hiddenField($this->model->product, 'lock_decimals');
        echo "</div>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-6'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'id' => $this->print_dispatch_ticket_id,
            'model' => $this->model->product,
            'attribute' => 'print_dispatch_ticket',
            'switchChange' => "",
            'hint' => 'Para despacho'
                ), true);
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

    private function renderPresentation() {

        $this->widget('application.widgets.SIANPresentation', array(
            'id' => $this->grid_id,
            'form' => $this->form,
            'model' => $this->model->product,
            'measure_id' => $this->measure_id,
            'measure_items' => $this->measure_items,
            'custom_measure_items' => $this->custom_measure_items,
            'lock_prices' => true,
        ));
    }

}
