MIIEFTCCAv2gAwIBAgIJAK7CrIz3ApDUMA0GCSqGSIb3DQEBCwUAMIGfMQswCQYDVQQGEwJQRTEUMBIGA1UECAwLTGEgTGliZXJ0YWQxETAPBgNVBAcMCFRydWppbGxvMRQwEgYDVQQKDAtTaWFuIFN5c3RlbTELMAkGA1UECwwCSVQxGTAXBgNVBAMMECouc2lhbnN5c3RlbS5jb20xKTAnBgkqhkiG9w0BCQEWGmFjY291bnRzQGdydXBvaGFyZHRlY2guY29tMCAXDTE4MDgwNzIzMzIyN1oYDzIwNTAwMTMwMjMzMjI3WjCBnzELMAkGA1UEBhMCUEUxFDASBgNVBAgMC0xhIExpYmVydGFkMREwDwYDVQQHDAhUcnVqaWxsbzEUMBIGA1UECgwLU2lhbiBTeXN0ZW0xCzAJBgNVBAsMAklUMRkwFwYDVQQDDBAqLnNpYW5zeXN0ZW0uY29tMSkwJwYJKoZIhvcNAQkBFhphY2NvdW50c0BncnVwb2hhcmR0ZWNoLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALc+vdePg32qy6L3HB3aOxTMYbaISupXPzQ+iW2uidhpduBj4NKFhRRzRZ5dcDde3A4v90eXnxA4LqNcVFKlZER4gIps4AB84f0AO7KxqBxxw+LhxTg5b6B33qqLrVw7I8Qb9DvuCTk1e2OAFmIaMdg7k58wngFP9RZrg4ZPWTXSWLW+++JjXb4iWbW3kKmTU49Pr/WVYt+DuK5Xg3awi2FCJRZZO8WtjqOPPxKt3aZVGFbUz2s5VrTJiPGHY71PTrDB7Q/lr/N2rFHuIATmVj32+i5FDdfCFB7YeXop7d37SFVVCikE7XhtukysKY7sa4Y7wdUo044KKEzJ93hjdbUCAwEAAaNQME4wHQYDVR0OBBYEFGEEClmU++yTLU2yXsRefCLHH9ZqMB8GA1UdIwQYMBaAFGEEClmU++yTLU2yXsRefCLHH9ZqMAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEBAC1ONR3fSsC+LMQ27upxsS7usGJMu0zBCEhn8i4p6JuzuOCNL3GwytmPXTKkjm75s8JkzwC7z3AFbVJLBNElJmaUWyfLkazSkUdoGjhiYrp1alk0O3sJJy+xb+absrz0hWXxK4trb+ARyV0IJaowJSyQHdMGcTA6llPa6Qq+d2Hr3zD162DhtP0QUwii15nhgt8a2mjI2zzjNFJdRbMv9WSoOJQwxD3N4gIV1n+xgyUwKZndYihrwo2SNwoq46YaT1J1i13NypfesWhrODa7ryjqPPmnR8an7xcm8RhHbDjVv0yfzx8YaXRswVnq88gTz1VU68pDFNbU4SHftF2PZsI=