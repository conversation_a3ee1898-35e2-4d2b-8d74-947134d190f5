<?php

class SIANMerchandise extends CWidget {

    public $id;
    public $grid_id;
    public $form;
    public $model;
    public $subline_id;
    public $mark_id;
    public $model_id;
    public $part_number_id;
    public $item_type_id;
    public $size_id;
    public $custom1_id;
    public $custom2_id;
    public $product_name_id;
    public $generate_name_id;
    public $allow_decimals_id;
    public $print_dispatch_ticket_id;
    public $serialized_id;
    public $advanced_id;
    public $modal_id = null;
    public $measure_id = null;
    public $measure_items = [];
    public $custom_measure_items = [];
    public $item_types = [];
    public $item_custom1 = [];
    public $item_custom2 = [];
    public $prices = [];
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->grid_id = isset($this->grid_id) ? $this->grid_id : $this->controller->getServerId();
        $this->subline_id = $this->controller->getServerId();
        $this->mark_id = $this->controller->getServerId();
        $this->model_id = $this->controller->getServerId();
        $this->part_number_id = $this->controller->getServerId();
        $this->item_type_id = $this->controller->getServerId();
        $this->size_id = $this->controller->getServerId();
        $this->custom1_id = $this->controller->getServerId();
        $this->custom2_id = $this->controller->getServerId();
        $this->product_name_id = $this->controller->getServerId();
        $this->generate_name_id = $this->controller->getServerId();
        $this->allow_decimals_id = $this->controller->getServerId();
        $this->print_dispatch_ticket_id = $this->controller->getServerId();
        $this->serialized_id = $this->controller->getServerId();
        $this->advanced_id = $this->controller->getServerId();
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-merchandise.js');

        Yii::app()->clientScript->registerScript($this->id, "
        
        $(document).ready(function() { 
            $('#{$this->id}').data('product_id', '{$this->model->product_id}');
            $('#{$this->id}').data('check_url', '{$this->controller->createUrl('/logistic/merchandise/check')}');
            $('#{$this->id}').data('submit_id', '{$this->form->submit_id}');
        });
        
        " . ($this->model->product->lock_name ? "" : "
        $('body').on('click', '#{$this->generate_name_id}', function(e) {

            if (USAutocompleteField('{$this->subline_id}', 'text') == '' || USAutocompleteField('{$this->mark_id}', 'text') == '' || $('#{$this->model_id}').val()=='') {
                    bootbox.alert(us_message('Asegúrese de ingresar un valor para {$this->model->getAttributeLabel('subline_id')}, {$this->model->getAttributeLabel('mark_id')} y {$this->model->getAttributeLabel('model')} antes de usar esta función', 'warning'));
            } else {
                " . (
                        Yii::app()->controller->getOrganization()->globalVar->business_line_type == GlobalVar::BUSINESS_LINE_FOOTWEAR ?
                                "
                    const custom2Value = USAutocompleteField('{$this->custom2_id}', 'text') === '' ? '' : '-' +  USAutocompleteField('{$this->custom2_id}', 'text');
                    const sizeValue = $('#{$this->size_id}').val().replace(/\s/g,'') === '' ? '' : ' T-' + $('#{$this->size_id}').val().replace(/\s/g,'');
                    $('#{$this->product_name_id}').val(USAutocompleteField('{$this->subline_id}', 'text') + ' ' + USAutocompleteField('{$this->mark_id}', 'text') + ' ' + $('#{$this->model_id}').val().replace(/\s/g,'') + custom2Value + sizeValue);" :
                                "
                    $('#{$this->product_name_id}').val(USAutocompleteField('{$this->subline_id}', 'text') + ' ' + USAutocompleteField('{$this->mark_id}', 'text') + ' ' + $('#{$this->model_id}').val().replace(/\s/g,''));"
                        ) . "
            }
        });    
        ") . "
            
        $('#{$this->id}').data('changeAdvance', function(advanced){

            $('#{$this->advanced_id}').val(advanced);
            
            if(advanced == 1)
            {
                $('.sian-advanced').show(100);
            }
            else
            {
                $('.sian-advanced').hide(100);
            }

            //Llamamos a advanced de presentation
            SIANPresentationChangeAdvance('{$this->grid_id}', advanced);
        });
        
        //Llamamos
        $('#{$this->id}').data('changeAdvance')({$this->model->product->advanced});

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        echo "<div id='{$this->id}'>";
        echo "<div class='row'>";
        echo "<div class='col-lg-8 col-md-8 col-sm-8 col-xs-6'>";
        $this->renderGeneral();
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-6'>";
        $this->renderSwitches();
        if(Yii::app()->controller->getOrganization()->globalVar->business_line_type == GlobalVar::BUSINESS_LINE_RESTAURANT){
            $this->renderRestaurantSwitches();
        }        
        $this->renderAdvanced();
        echo "</div>";
        echo "</div>";
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->renderPresentation();
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }

    private function renderGeneral() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Datos generales',
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
                'class' => 'sian-basic'
            )
        ));

        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->subline_id,
            'model' => $this->model,
            'attribute' => 'subline_id',
            'parent_id' => $this->modal_id,
            'placeholder' => "Por ejemplo 'Celulares'",
            'view' => array(
                'model' => 'DpSubline',
                'scenario' => DpSubline::SCENARIO_NORMAL,
                'attributes' => array(
                    array(
                        'name' => 'subline_id',
                        'width' => 10,
                        'types' => array('id', 'value', 'aux'),
                    ),
                    array(
                        'name' => 'subline_name',
                        'width' => 40,
                        'types' => array('text')
                    ),
                    array(
                        'name' => 'line_name',
                        'width' => 25
                    ),
                    array(
                        'name' => 'division_name',
                        'width' => 25
                    ),
                )
            ),
            'maintenance' => array(
                'module' => 'logistic',
                'controller' => 'subline'
            ),
        ));

        //Marca
        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->mark_id,
            'model' => $this->model,
            'attribute' => 'mark_id',
            'parent_id' => $this->modal_id,
            'placeholder' => "Por ejemplo 'Samsung'",
            'view' => array(
                'model' => 'DpMark',
                'scenario' => DpMark::SCENARIO_NORMAL,
                'attributes' => array(
                    array(
                        'name' => 'mark_id',
                        'width' => 20,
                        'types' => array('id', 'value', 'aux')
                    ),
                    array(
                        'name' => 'mark_name',
                        'width' => 80,
                        'types' => array('text')
                    ),
                )
            ),
            'maintenance' => array(
                'module' => 'logistic',
                'controller' => 'mark'
            ),
            'onselect' => "
                var model = $('#{$this->model_id}').val();
                var part_number = $('#{$this->part_number_id}').val();
                SIANMerchandiseCheck('{$this->id}', mark_id, model, part_number);
            ",
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-6'>";
        echo $this->form->textFieldRow($this->model, 'model', array(
            'id' => $this->model_id,
            'class' => 'us-nospace',
            'placeholder' => "Por ejemplo 'S9'",
            'required' => $this->controller->getOrganization()->globalVar->require_model,
            'onchange' => "
                        var mark_id = USAutocompleteField('{$this->mark_id}', 'value');
                        var part_number = $('#{$this->part_number_id}').val();
                        SIANMerchandiseCheck('{$this->id}', mark_id, this.value, part_number);
                    "
        ));
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-6'>";
        echo $this->form->textFieldRow($this->model, 'part_number', array(
            'id' => $this->part_number_id,
            'class' => 'us-nospace',
            'maxlength' => 25,
            'placeholder' => "Por ejemplo 'SGS920181130'",
            'onchange' => "
                        var mark_id = USAutocompleteField('{$this->mark_id}', 'value');
                        var model = $('#{$this->model_id}').val();
                        SIANMerchandiseCheck('{$this->id}', mark_id, model, this.value);
                    "
        ));
        echo CHtml::hiddenField('similar_id', null, [
            'class' => 'sian-merchandise-similar-id',
        ]);
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-6'>";
        $type_building = Yii::app()->controller->getOrganization()->globalVar->business_line_type;
        echo $this->form->dropDownListRow($this->model->product, 'item_type_id', $this->item_types, array(
            'id' => $this->item_type_id,
            'class' => 'sian-merchandise-item-type-id',
            'empty' => Strings::SELECT_OPTION,
            'disabled' => $type_building == GlobalVar::BUSINESS_LINE_BUILDING && !$this->model->isNewRecord
        ));
        echo "</div>";
        echo "</div>";

        if (Yii::app()->controller->getOrganization()->globalVar->product_custom_name_1 != GlobalVar::PROPERTY_CUSTOM_1_DEFAULT) {
            echo "<div class='row'>";
            echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
            $this->widget('application.widgets.USAutocomplete', array(
                'id' => $this->custom1_id,
                'model' => $this->model,
                'attribute' => 'custom1_id',
                'hideText' => false,
                'parent_id' => $this->modal_id,
                'placeholder' => "Por ejemplo 'Femenino'",
                'view' => array(
                    'model' => 'DpMultitable',
                    'scenario' => Multitable::ITEM_PRODUCT_CUSTOM_1,
                    'attributes' => array(
                        array('name' => 'multi_id', 'hidden' => true, 'types' => array('id', 'value')),
                        array('name' => 'value', 'width' => 20, 'types' => array('aux', 'text')),
                        array('name' => 'abbreviation', 'width' => 20),
                    )
                ),
                'maintenance' => array(
                    'module' => 'logistic',
                    'controller' => 'custom1',
                ),
            ));
            echo "</div>";
            echo "</div>";
        }
        if (Yii::app()->controller->getOrganization()->globalVar->product_custom_name_2 != GlobalVar::PROPERTY_CUSTOM_2_DEFAULT) {
            echo "<div class = 'row'>";
            echo "<div class = 'col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
            $this->widget('application.widgets.USAutocomplete', array(
                'id' => $this->custom2_id,
                'model' => $this->model,
                'attribute' => 'custom2_id',
                'parent_id' => $this->modal_id,
                'hideText' => false,
                'placeholder' => "Por ejemplo 'Azul'",
                'view' => array(
                    'model' => 'DpMultitable',
                    'scenario' => Multitable::ITEM_PRODUCT_CUSTOM_2,
                    'attributes' => array(
                        array('name' => 'multi_id', 'hidden' => true, 'types' => array('id', 'value')),
                        array('name' => 'value', 'width' => 20, 'types' => array('aux', 'text')),
                        array('name' => 'abbreviation', 'width' => 20),
                    )
                ),
                'maintenance' => array(
                    'module' => 'logistic',
                    'controller' => 'custom2'
                ),
            ));
            echo "</div>";
            echo "</div>";
        }

        echo "<div class = 'row'>";
        echo "<div class = 'col-lg-8 col-md-8 col-sm-8 col-xs-8'>";
        echo $this->form->textFieldRow($this->model->product, 'product_name', array(
            'id' => $this->product_name_id,
            'maxlength' => 500,
            'class' => 'us-cleantext',
            'placeholder' => "Por ejemplo 'Celular Galaxy S9 (SGS920181130)'",
            'append' => $this->widget('application.widgets.USLink', array(
                'id' => $this->generate_name_id,
                'title' => 'Sugerir',
                'icon' => 'fa fa-lg fa-star',
                'visible' => !$this->model->product->lock_name,
                    ), true),
            'disabled' => $this->model->product->lock_name,
        ));
        echo $this->form->hiddenField($this->model->product, 'lock_name');
        echo "</div>";
        if (Yii::app()->controller->getOrganization()->globalVar->business_line_type == GlobalVar::BUSINESS_LINE_FOOTWEAR) {
            echo "<div class = 'col-lg-4 col-md-4 col-sm-6 col-xs-6'>";
            echo $this->form->numberFieldRow($this->model, 'size', array(
                'id' => $this->size_id,
                'class' => 'us-integer',
                'maxlength' => 2,
                'placeholder' => "Por ejemplo '42'",
                'min' => 1,
                'step' => 1,
                'max' => 60
            ));
            echo "</div>";
        }
        echo "</div>";

        echo $this->form->textAreaRow($this->model->product, 'description', array(
            'rows' => '3',
        ));

        $this->endWidget();
    }

    private function renderSwitches() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Indicadores',
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
                'class' => 'sian-basic'
            )
        ));
        echo "<div class = 'row'>";
        echo "<div class = 'col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array('model' => $this->model->product, 'attribute' => 'status'), true);
        echo "</div>";
        echo "<div class = 'col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        $b_disabled_serie = Yii::app()->controller->getOrganization()->globalVar->use_series == 0;
        if (!$b_disabled_serie) {
            $b_disabled_serie = Yii::app()->user->level == User::LEVEL_ROOT ? false : $this->model->lock_serie;
        }
        echo $this->widget('application.widgets.USSwitch', array(
            'id' => $this->serialized_id,
            'model' => $this->model,
            'attribute' => 'serialized',
            'readonly' => $this->model->product->allow_decimals == 1,
            'disabled' => $b_disabled_serie,
                ), true);
        echo $this->form->hiddenField($this->model, 'lock_serie');
        echo "</div>";
        echo "</div>";

        echo "<div class = 'row'>";
        if (Yii::app()->controller->getOrganization()->globalVar->business_line_type != GlobalVar::BUSINESS_LINE_FOOTWEAR) {
            echo "<div class = 'col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
            echo $this->widget('application.widgets.USSwitch', array(
                'id' => $this->allow_decimals_id,
                'model' => $this->model->product,
                'attribute' => 'allow_decimals',
                'switchChange' => !$this->model->product->lock_decimals ? "
                        $('#{$this->grid_id}').data('allowDecimals')(this.checked);
                        //$('#{$this->serialized_id}').prop('checked', !this.checked).change().bootstrapSwitch('readonly', this.checked);
                        " : "",
                'disabled' => $this->model->product->lock_decimals,
                'hint' => 'En equivalencias'
                    ), true);
            echo $this->form->hiddenField($this->model->product, 'lock_decimals');
            echo "</div>";
        }
        echo "<div class = 'col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'id' => $this->print_dispatch_ticket_id,
            'model' => $this->model->product,
            'attribute' => 'print_dispatch_ticket',
            'switchChange' => "",
            'hint' => 'Para despacho'
                ), true);
        echo "</div>";
        echo "<div class = 'col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'use_batch',
            'readonly' => Yii::app()->controller->getOrganization()->globalVar->use_batch == GlobalVar::BATCH_USE_BATCH_NO
                ), true);
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

    private function renderRestaurantSwitches() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Restaurant',
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
                'class' => 'sian-basic'
            )
        ));
        echo "<div class = 'row'>";
        echo "<div class = 'col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array('model' => $this->model->product, 'attribute' => 'is_supply'), true);
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

    private function renderAdvanced() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Indicadores avanzados',
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
                'class' => 'sian-advanced'
            )
        ));

        echo "<div class = 'row'>";
        echo "<div class = 'col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model->product, 'expiration');
        echo "</div>";
        echo "<div class = 'col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model->product,
            'attribute' => 'perception_affected',
            'disabled' => $this->model->product->lock_perception_affected,
                ), true);
        echo $this->form->hiddenField($this->model->product, 'lock_perception_affected');
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

    private function renderPresentation() {
        $this->widget('application.widgets.SIANPresentation', array(
            'id' => $this->grid_id,
            'form' => $this->form,
            'model' => $this->model->product,
            'measure_id' => $this->measure_id,
            'measure_items' => $this->measure_items,
            'custom_measure_items' => $this->custom_measure_items,
            'lock_prices' => true,
            'prices' => $this->prices,
        ));
    }

}
