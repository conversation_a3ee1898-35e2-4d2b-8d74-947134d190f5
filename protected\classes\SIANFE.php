<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANFE
 * http://orientacion.sunat.gob.pe/index.php/empresas-menu/comprobantes-de-pago-empresas/comprobantes-de-pago-electronicos-empresas/see-desde-los-sistemas-del-contribuyente/2-comprobantes-que-se-pueden-emitir-desde-see-sistemas-del-contribuyente/factura-electronica-desde-see-del-contribuyente/3548-guias-y-manuales
 * <AUTHOR>
 */
class SIANFE {

    const FE_FOLDER = 'ebilling';
    const TYPE_INVOICE = 'invoice';
    const TYPE_SUMMARY = 'summary';
    const TYPE_SUMMARY_LOW = 'low';
    const TYPE_SELF_BILLED_INVOICE = 'selfBilledInvoice';
    const TYPE_REMISSION_GUIDE = 'remissionGuide';
    const TYPE_BARCODE = 'barcode';
    const TYPE_RESPONSE = 'response';
    const TYPE_PDF = 'pdf';
    const XML_EXTENSION = '.xml';
    const PDF_EXTENSION = '.pdf';
    const PNG_EXTENSION = '.png';
    const ZIP_EXTENSION = '.zip';
    const RESPONSE_PREFFIX = 'R-';
    const SUMMARY_PREFFIX = 'RC';
    const SUMMARY_LOW_PREFFIX = 'RA';
    const SEPARATOR = '-';
    const DEFAULT_CORRELATIVE = '99999';
    const PAYMENT_TERM_COUNTED = 'Contado';
    const PAYMENT_TERM_CREDIT = 'Credito';

    /**
     * Obtiene la carpeta del certificado
     * @return string Carpeta del certificado
     */
    public static function getCertificatePath() {
        return Yii::app()->params['files_dir'] . DIRECTORY_SEPARATOR . self::FE_FOLDER . DIRECTORY_SEPARATOR;
    }

    /**
     * Obtiene la carpeta del XSD
     * @return string Carpeta del XSD
     */
    public static function getXsdPath() {
        return Yii::app()->params['admin_dir'] . DIRECTORY_SEPARATOR . 'files' . DIRECTORY_SEPARATOR . 'xsd_ebilling' . DIRECTORY_SEPARATOR . 'maindoc' . DIRECTORY_SEPARATOR;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los XML
     * @return string Folder
     */
    public static function getDir() {
        $s_path = Yii::app()->params['files_dir'] . DIRECTORY_SEPARATOR . self::FE_FOLDER . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene url de la carpeta principal
     * @return string URL
     */
    public static function getUrl() {
        return Yii::app()->params['files_url'] . DIRECTORY_SEPARATOR . self::FE_FOLDER . DIRECTORY_SEPARATOR;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los PDF de comprobantes
     * @return string Folder
     */
    public static function getPdfDir() {
        $s_path = self::getDir() . self::TYPE_PDF . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los XML de comprobantes
     * @return string Folder
     */
    public static function getInvoiceDir() {
        $s_path = self::getDir() . self::TYPE_INVOICE . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los XML de comprobantes
     * @return string Folder
     */
    public static function getSelfBilledInvoiceDir() {
        $s_path = self::getDir() . self::TYPE_SELF_BILLED_INVOICE . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los XML de GUIAS DE REMISION
     * @return string Folder
     */
    public static function getRemissionGuideDir() {
        $s_path = self::getDir() . self::TYPE_REMISSION_GUIDE . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los XML de respuesta de las GUIAS DE REMISION
     * @return string Folder
     */
    public static function getRRemissionGuideDir() {
        $s_path = self::getRemissionGuideDir() . self::TYPE_RESPONSE . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los XML de resúmenes
     * @return string Folder
     */
    public static function getSummaryDir() {
        $s_path = self::getDir() . self::TYPE_SUMMARY . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los XML de baja
     * @return string Folder
     */
    public static function getSummaryLowDir() {
        $s_path = self::getDir() . self::TYPE_SUMMARY_LOW . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los XML de códigos de ebarras
     * @return string Folder
     */
    public static function getBarcodeDir() {
        $s_path = self::getDir() . self::TYPE_BARCODE . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los XML de respuesta de comprobantes
     * @return string Folder
     */
    public static function getRInvoiceDir() {
        $s_path = self::getInvoiceDir() . self::TYPE_RESPONSE . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los XML de respuesta de resúmenes
     * @return string Folder
     */
    public static function getRSummaryDir() {
        $s_path = self::getSummaryDir() . self::TYPE_RESPONSE . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene (y crea si no existe) la carpeta para guardar los XML de respuesta de bajas
     * @return string Folder
     */
    public static function getRSummaryLowDir() {
        $s_path = self::getSummaryLowDir() . self::TYPE_RESPONSE . DIRECTORY_SEPARATOR;

        if (!file_exists($s_path)) {
            mkdir($s_path, 0755);
        }

        return $s_path;
    }

    /**
     * Obtiene la URL de los archivos PDF
     * @return string URL de los archivos PDF
     */
    public static function getPdfUrl() {
        return self::getUrl() . self::TYPE_PDF . DIRECTORY_SEPARATOR;
    }

    /**
     * Obtiene la URL de los archivos PNG (Códigos de barras)
     * @return string URL de los archivos PDF
     */
    public static function getBarcodeUrl() {
        return self::getUrl() . self::TYPE_BARCODE . DIRECTORY_SEPARATOR;
    }

    /**
     * Genera un archivo ZIP 
     * @param string $p_s_path Carpeta dónde se encuentran los archivos
     * @param array $p_a_filename Lista de archivos que se agregarán al ZIP
     * @param string $p_s_zip_pathname Nombre del archivo ZIP
     * @return array Array de bytes del archivo ZIP
     */
    public static function createZip($p_s_path, array $p_a_filename = [], $p_s_zip_pathname = null) {

        $s_zip_pathname = isset($p_s_zip_pathname) ? $p_s_zip_pathname : USTime::getDateCode() . self::ZIP_EXTENSION;

        //Normalizando
        $a_files = [];
        foreach ($p_a_filename as $s_filename) {
            $a_files[$s_filename] = $p_s_path . $s_filename;
        }
        //Convirtiendo a ZIP
        USFile::createZip($s_zip_pathname, $a_files);
        //Convirtiendo a Array de Bytes el archivo ZIP
        $content_file = file_get_contents($s_zip_pathname);

        return $content_file;
    }

    public static function getInvoiceBasename($p_s_sunat_code = 00, $p_s_document_serie = null, $p_i_document_correlative = null, $p_s_ext = '') {

        if (in_array($p_s_sunat_code, [Document::FACTURA, Document::BOLETA, Document::CREDIT_NOTE, Document::DEBIT_NOTE]) && (($p_s_document_serie != 'E001' && $p_s_document_serie != 'EB01' && $p_s_document_serie[0] != 'B' && $p_s_document_serie[0] != 'F') || strlen($p_s_document_serie) != 4)) {
            throw new Exception("Serie '{$p_s_document_serie}' no cumple con estandar establecido por SUNAT!", USREST::CODE_INTERNAL_SERVER_ERROR);
        }

        if ($p_i_document_correlative > 99999999) {
            throw new Exception("Correlativo {$p_i_document_correlative} es inválido!", USREST::CODE_INTERNAL_SERVER_ERROR);
        }

        $s_identification_number = Yii::app()->controller->getOrganization()->person->identification_number;

        $s_basename = $s_identification_number . self::SEPARATOR . $p_s_sunat_code . self::SEPARATOR . $p_s_document_serie . self::SEPARATOR . substr($p_i_document_correlative, -8);
        return $s_basename . (USString::isBlank($p_s_ext) ? '' : ('.' . $p_s_ext));
    }

    public static function getGuideBasename($p_s_sunat_code = 00, $p_s_document_serie = null, $p_s_document_correlative = null, $p_s_ext = '') {

        if (in_array($p_s_sunat_code, [Document::REMISSION_GUIDE, Document::TRANSPORT_GUIDE]) && (($p_s_sunat_code == Document::REMISSION_GUIDE && $p_s_document_serie[0] != 'T') || ($p_s_sunat_code == Document::TRANSPORT_GUIDE && $p_s_document_serie[0] != 'V') || strlen($p_s_document_serie) != 4)) {
            throw new Exception("Serie '{$p_s_document_serie}' no cumple con estandar establecido por SUNAT!", USREST::CODE_INTERNAL_SERVER_ERROR);
        }
        $i_document_correlative = 0;
        if (isset($p_s_document_correlative)) {
            $i_document_correlative = intval($p_s_document_correlative);
        }

        if ($i_document_correlative > 99999999) {
            throw new Exception("Correlativo {$i_document_correlative} es inválido!", USREST::CODE_INTERNAL_SERVER_ERROR);
        }

        $s_identification_number = Yii::app()->controller->getOrganization()->person->identification_number;

        $s_basename = $s_identification_number . self::SEPARATOR . $p_s_sunat_code . self::SEPARATOR . $p_s_document_serie . self::SEPARATOR . $i_document_correlative;
        return $s_basename . (USString::isBlank($p_s_ext) ? '' : ('.' . $p_s_ext));
    }

    public static function getSummaryBasename($p_s_correlative = self::DEFAULT_CORRELATIVE, $p_s_ext = null) {
        $s_identification_number = Yii::app()->controller->getOrganization()->person->identification_number;

        $s_basename = $s_identification_number . self::SEPARATOR . self::SUMMARY_PREFFIX . self::SEPARATOR . date('Ymd') . self::SEPARATOR . $p_s_correlative;
        return $s_basename . (USString::isBlank($p_s_ext) ? '' : ('.' . $p_s_ext));
    }

    public static function getSummaryLowBasename($p_s_correlative = self::DEFAULT_CORRELATIVE, $p_s_ext = null) {
        $s_identification_number = Yii::app()->controller->getOrganization()->person->identification_number;

        $s_basename = $s_identification_number . self::SEPARATOR . self::SUMMARY_LOW_PREFFIX . self::SEPARATOR . date('Ymd') . self::SEPARATOR . $p_s_correlative;
        return $s_basename . (USString::isBlank($p_s_ext) ? '' : ('.' . $p_s_ext));
    }

    public static function getXMLFilename($p_s_path, $p_s_basename) {
        //Verificamos si no existe el XML de respuesta
        if (!file_exists($p_s_path . $p_s_basename . SIANFE::XML_EXTENSION)) {
            //Verificamos si existe el archivo ZIP
            if (file_exists($p_s_path . $p_s_basename . SIANFE::ZIP_EXTENSION)) {
                //Descomprimimos
                if (!USFile::extractOrReadZip($p_s_path . $p_s_basename . SIANFE::ZIP_EXTENSION, $p_s_path)) {
                    throw new Exception('No fue posible descomprimir el archivo ' . $p_s_basename . SIANFE::ZIP_EXTENSION, 401);
                }
            } else {
                throw new Exception('No existe el archivo ' . $p_s_basename . SIANFE::XML_EXTENSION, 401);
            }
        }

        return $p_s_path . $p_s_basename . SIANFE::XML_EXTENSION;
    }

    /**
     * Obtiene la data XML del archivo especificado
     * @param string $p_s_path PATH del archivo
     * @param string $p_s_basename Nombre del archivo (sin extensión)
     * @return string XML
     * @throws Exception
     */
    public static function getXML($p_s_path, $p_s_basename) {

        $s_filename = self::getXMLFilename($p_s_path, $p_s_basename);

        $s_xml = file_get_contents($s_filename);
        $o_dom = new DOMDocument();
        $o_dom->preserveWhiteSpace = FALSE;
        $o_dom->loadXML($s_xml);
        $o_dom->formatOutput = TRUE;

        return $o_dom->saveXml();
    }

    /**
     * Obtiene los datos necesarios para renderizar la vista previa de factura
     * @param integer $p_m_id_code ID o Código del movimiento
     * @param bool $p_b_is_code TRUE si es código o FALSE si es ID del movimiento (Por defecto false)
     * @return array Array de datos para la vista
     */
    public static function getPDFCommercialData($p_m_id_code, $p_b_is_code = false) {

        $s_col = $p_b_is_code ? "movement_code" : "movement_id";

        $a_model = Yii::app()->db->createCommand()->select([
                    'M.movement_id',
                    'M.observation',
                    "IF(CM.condition = '" . Condition::CREDIT . "', CM.condition, '" . Condition::COUNTED . "') AS `condition`",
                    'Ph.phone_number',
                    'M.emission_date',
                    'M.expiration_date',
                    'M.digest_value',
                    'M.status',
                    'ST.store_name',
                    "IF(STA.address IS NOT NULL, CONCAT(STA.address, ' (', STG.dist_name, ' - ', STG.prov_name, ' - ', STG.dept_name,')'), '-') AS store_address",
                    'STPH.phone_number AS store_phone_number',
                    "IF(A.address IS NOT NULL, CONCAT(A.address, ' (', G.dist_name, ' - ', G.prov_name, ' - ', G.dept_name,')'), '-') AS address",
                    "IF(MA.address IS NOT NULL, CONCAT(MA.address, ' (', MG.dist_name, ' - ', MG.prov_name, ' - ', MG.dept_name,')'), '-') AS delivery_address",
                    "MA.reference as delivery_reference",
                    'CM.affected_pen',
                    'CM.affected_usd',
                    'CM.inaffected_pen',
                    'CM.inaffected_usd',
                    'CM.nobill_pen',
                    'CM.nobill_usd',
                    'CM.export_pen',
                    'CM.export_usd',
                    'CM.free_pen',
                    'CM.free_usd',
                    'CM.net_pen',
                    'CM.net_usd',
                    'CM.igv_usd',
                    'CM.igv_pen',
                    'CM.total_pen',
                    'CM.total_usd',
                    'CM.perception_usd',
                    'CM.perception_pen',
                    'CM.real_pen',
                    'CM.real_usd',
                    'CM.ret_pen',
                    'CM.ret_usd',
                    'CM.no_ret_pen',
                    'CM.no_ret_usd',
                    'CM.retention',
                    'CM.detraction',
                    'CM.det_pen',
                    'CM.det_usd',
                    'MU.value AS detraction_code',
                    '(CM.detraction_percent * 100) AS detraction_percentage',
                    'C.description AS detraction_account',
                    'M.document_serie as document_serie',
                    'RIGHT(M.document_correlative, 8) AS document_correlative',
                    'P.identification_number',
                    'P.person_name',
                    'V.person_name AS vendedor',
                    'M.currency',
                    "D.sunat_code",
                    "D.document_name",
                    "D.person_type",
                    "concat_ws('-', MP.document_code, MP.document_serie, MP.document_correlative) AS document_parent",
                    "EI.purchase_order",
                    "LSM.basename",
                    "LSM.pdf_link",
                    "CH.reason",
                    "CH.reason_code"
                ])
                ->from('commercial_movement CM')
                ->join('movement M', 'CM.movement_id = M.movement_id')
                ->join('store ST', 'ST.store_id = M.store_id')
                ->leftJoin('address STA', "STA.owner = '" . Store::OWNER . "' and STA.owner_id = ST.store_id and STA.`order` = 1")//Dirección  de Tienda
                ->leftJoin('geoloc STG', "STG.dept_code = STA.dept_code and STG.prov_code = STA.prov_code and STG.dist_code = STA.dist_code")//Dirección de tienda
                ->leftJoin('phone STPH', "STPH.`owner` = '" . Store::OWNER . "' AND STPH.owner_id = ST.store_id AND STPH.order = 1")
                ->leftJoin('movement_link li', 'li.movement_id = M.movement_id')
                ->leftJoin('movement MP', 'MP.movement_id = li.movement_parent_id')
                ->join('person P', 'M.aux_person_id = P.person_id')
                ->leftJoin('person V', 'MP.person_id = V.person_id')
                ->join('document D', 'D.document_code = M.document_code')
                ->leftJoin('address A', "A.owner = '" . Person::OWNER . "' AND A.owner_id = P.person_id AND A.order = 1")
                ->leftJoin('geoloc G', "G.dept_code = A.dept_code and G.prov_code = A.prov_code and G.dist_code = A.dist_code")//Dirección de entrega
                ->leftJoin('address MA', "MA.owner = '" . Movement::OWNER . "' and MA.owner_id = M.movement_id and MA.`order` = 1")//Dirección  de Entrega
                ->leftJoin('geoloc MG', "MG.dept_code = MA.dept_code and MG.prov_code = MA.prov_code and MG.dist_code = MA.dist_code")//Dirección de entrega
                ->leftJoin('phone Ph', "Ph.`owner` = '" . Person::OWNER . "' AND Ph.owner_id = P.person_id AND Ph.order = 1")
                ->leftJoin('extra_info EI', 'M.movement_id = EI.movement_id')
                ->leftJoin('sent_movement LSM', 'LSM.movement_id = M.movement_id AND LSM.`last`')
                ->leftJoin('multitable MU', 'MU.multi_id = CM.detraction_code')
                ->leftJoin('cashbox C', 'C.is_detraction = 1')
                ->leftJoin('changelog CH', "CH.owner_id = M.movement_id AND CH.owner = '" . CommercialMovement::OWNER . "' AND CH.action = '" . Changelog::CREATE . "'")
                ->where("M.{$s_col} = :{$s_col}", [
                    ":{$s_col}" => $p_m_id_code
                ])
                ->queryRow();

        if ($a_model !== false) {

            $s_free_mult = "(CMP.igv_affection NOT IN ('" . CommercialMovementProduct::IGV_AFFECTION_FREE . "', '" . CommercialMovementProduct::IGV_AFFECTION_GIFT . "'))";

            $items = Yii::app()->db->createCommand()->select([
                        "I.item_id",
                        "I.product_type",
                        "I.product_id",
                        "I.pres_quantity",
                        "IFNULL(PR.measure_name, '" . Yii::app()->controller->getDefaultMerchandiseMeasure()->abbreviation . "') AS measure_name",
                        "UPPER(I.product_name) AS product_name",
                        "CMP.cprice_{$a_model['currency']} * {$s_free_mult} AS cprice",
                        "CMP.cdiscount_{$a_model['currency']} * {$s_free_mult} AS cdiscount",
                        "CMP.vprice_{$a_model['currency']} AS vprice",
                        "CMP.sprice_{$a_model['currency']} AS sprice",
                        "CMP.include_igv",
                        "CMP.igv_affection",
                        "CMP.crude_{$a_model['currency']} * {$s_free_mult} AS crude",
                        "CMP.affected_{$a_model['currency']} AS affected",
                        "CMP.inaffected_{$a_model['currency']} AS inaffected",
                        "CMP.nobill_{$a_model['currency']} AS nobill",
                        "CMP.export_{$a_model['currency']} AS export",
                        "CMP.free_{$a_model['currency']} AS free",
                        "CMP.net_{$a_model['currency']} AS net",
                        "CMP.total_{$a_model['currency']} AS total",
                        "CMP.group_id",
                    ])
                    ->from('item I')
                    ->join('commercial_movement_product CMP', 'CMP.item_id = I.item_id AND CMP.group_id IS NULL')
                    ->join('commercial_movement CM', 'CM.movement_id = CMP.movement_id')
                    ->join('global_var GV', 'GV.organization_id = 1')
                    ->leftJoin('presentation PR', 'PR.product_id = I.product_id AND PR.equivalence = I.equivalence')
                    ->where('I.movement_id = :movement_id', [
                        ':movement_id' => $a_model['movement_id']
                    ])
                    ->queryAll();

            if (Yii::app()->controller->getOrganization()->globalVar->print_sale_bill_detail == 1) {

                CommercialMovementProduct::setSubItems($items, $a_model['movement_id']);
            }

            //Path
            if ($a_model['person_type'] === Person::TYPE_JURIDICAL) {
                $s_path = SIANFE::getInvoiceDir();
            } else {
                $s_path = SIANFE::getSummaryDir();
            }
            //Basename
            if (isset($a_model['basename']) && !USString::isBlank($a_model['basename'])) {
                $s_basename = $a_model['basename'];
            } else {
                if ($a_model['person_type'] === Person::TYPE_JURIDICAL) {
                    $s_basename = SIANFE::getInvoiceBasename($a_model['sunat_code'], $a_model['document_serie'], $a_model['document_correlative']);
                } else {
                    $s_basename = SIANFE::getSummaryBasename();
                }
            }
            //            
            $s_digest_value = SIANFEXML::getDigestValue($a_model['digest_value'], $a_model['movement_id'], $a_model['sunat_code'], $s_path, $s_basename);
            $s_barcode_filepath = SIANFE::getBarcodeDir() . $s_basename . SIANFE::PNG_EXTENSION;
            USImage::saveBarcode($s_digest_value, $s_barcode_filepath, "PDF417,1,4", 8, 4);
            return array(
                'model' => $a_model,
                'items' => $items,
                'digest_value' => $s_digest_value,
                'amountLetters' => Currency::amountToLetters($a_model['real_' . $a_model['currency']], $a_model['currency']),
                'file' => $s_basename,
                'file_pdf_link' => $a_model['pdf_link'],
                'filename' => $s_basename . SIANFE::PDF_EXTENSION,
                'filepath' => SIANFE::getPdfDir() . $s_basename . SIANFE::PDF_EXTENSION,
                'filepathPNG' => $s_barcode_filepath,
                'fileurl' => SIANFE::getPdfUrl() . $s_basename . SIANFE::PDF_EXTENSION,
                'stores' => Store::get(Store::MODE_PRINT)
            );
        } else {
            return false;
        }
    }

    /**
     * Obtiene los datos necesarios para renderizar la vista previa de factura
     * @param integer $p_m_id_code ID o Código del movimiento
     * @param bool $p_b_is_code TRUE si es código o FALSE si es ID del movimiento (Por defecto false)
     * @return array Array de datos para la vista
     */
    public static function getPDFGuideData($p_m_id_code, $p_movement_type, $p_b_is_code = false) {

        $s_col = $p_b_is_code ? "movement_code" : "movement_id";

        if ($p_movement_type == Scenario::TYPE_PRODUCT_LIST) {

//            $a_model = Yii::app()->db->createCommand()->select([
//                                //Documento
//                                'M.movement_id',
//                                'M.document_serie as document_serie',
//                                'RIGHT(M.document_correlative, 8) as document_correlative',
//                                'M.emission_date',
//                                'M.digest_value',
//                                'M.status',
//                                'M.currency',
//                                "D.sunat_code",
//                                "D.document_name",
//                                "D.person_type",
//                                'M.observation',
//                                //Destinatario
//                                'P.identification_number AS recipient_identification_number',
//                                'P.person_name AS recipient_name',
//                                //Direcciones
//                                "IF(OA.address IS NOT NULL, CONCAT(OA.address, ' (', OG.dist_name, ' - ', OG.prov_name, ' - ', OG.dept_name,')'), '-') AS originAddress",
//                                "IF(DA.address IS NOT NULL, CONCAT(DA.address, ' (', DG.dist_name, ' - ', DG.prov_name, ' - ', DG.dept_name,')'), '-') AS delivery_address",
//                                //Datos de traslado
//                                'MU.description AS reason_transfer',
//                                'WM.reason_transfer_description',
//                                'WM.transfer_start_date',
//                                'WM.delivery_carrier_date',
//                                'WM.number_packages',
//                                'WM.total_weight',
//                                'WM.transport_data AS transport_type_code',
//                                'IF(WM.transport_data = 1,\'TRANSPORTE PRIVADO\',\'TRANSPORTE PÚBLICO\') AS transport_type',
//                                //Datos de trasportista
//                                '(CASE PT.identification_type WHEN \'1\' THEN \'DNI\' WHEN \'6\' THEN \'RUC\' ELSE PT.identification_type END) AS company_identification_type',
//                                'PT.identification_number AS company_identification_number',
//                                'REPLACE(PT.person_name, \',\', \' \') AS company_name',
//                                //Datos de trasporte
//                                '(CASE PS.identification_type WHEN \'1\' THEN \'DNI\' WHEN \'6\' THEN \'RUC\' ELSE PS.identification_type END) AS shipper_identification_type',
//                                'PS.identification_number AS shipper_identification_number',
//                                'REPLACE(PS.person_name, \',\', \' \') AS shipper_name',
//                                'SH.license_number',
//                                'UT.plate',
//                                "LSM.basename",
//                                "LSM.pdf_link"
//                            ])
//                            ->from('warehouse_movement WM')
//                            ->join('movement M', 'WM.movement_id = M.movement_id')
//                            ->join('document D', 'D.document_code = M.document_code')
//                            ->join('person P', 'M.aux_person_id = P.person_id')
//                            ->leftJoin('multitable MU', 'MU.multi_id = WM.reason_transfer_id')
//                            //Direcciones
//                            ->leftJoin('address OA', "OA.owner = '" . Movement::OWNER . "' AND OA.owner_id = M.movement_id AND OA.order = 0")//Dirección de origen
//                            ->leftJoin('geoloc OG', "OG.dept_code = OA.dept_code and OG.prov_code = OA.prov_code and OG.dist_code = OA.dist_code")
//                            ->leftJoin('address DA', "DA.owner = '" . Movement::OWNER . "' and DA.owner_id = M.movement_id and DA.`order` = 1")//Dirección  de Entrega
//                            ->leftJoin('geoloc DG', "DG.dept_code = DA.dept_code and DG.prov_code = DA.prov_code and DG.dist_code = DA.dist_code")
//                            //Transportista
//                            ->leftjoin('person PT', 'PT.person_id = WM.transport_company_id')
//                            //Conductor
//                            ->leftJoin('owner_pair OPPSH', "OPPSH.type = '" . OwnerPair::TYPE_MOVEMENT_SHIPPER . "' AND OPPSH.`owner` = '" . Movement::OWNER . "' AND OPPSH.owner_id = M.movement_id")
//                            ->leftJoin('owner_pair OPSH', "OPSH.type = OPPSH.type AND OPSH.`owner` = '" . Shipper::OWNER . "' AND OPSH.parent_id = OPPSH.pair_id AND OPSH.order = 1")
//                            ->leftJoin('shipper SH', "SH.shipper_id = OPSH.owner_id")
//                            ->leftJoin('person PS', "PS.person_id = SH.person_id")
//                            //Transporte
//                            ->leftJoin('owner_pair OPPUT', "OPPUT.type = '" . OwnerPair::TYPE_MOVEMENT_TRANSPORT_UNIT . "' AND OPPUT.`owner` = '" . Movement::OWNER . "' AND OPPUT.owner_id = M.movement_id")
//                            ->leftJoin('owner_pair OPUT', "OPUT.type = OPPUT.type AND OPUT.`owner` = '" . TransportUnit::OWNER . "' AND OPUT.parent_id = OPPUT.pair_id AND OPUT.order = 1")
//                            ->leftJoin('transport_unit UT', "UT.transport_unit_id = OPUT.owner_id")
//                            //
//                            ->leftJoin('sent_movement LSM', 'LSM.movement_id = M.movement_id AND LSM.`last`')
//                            ->where("M.{$s_col} = :{$s_col}", [
//                                ":{$s_col}" => $p_m_id_code
//                            ])->queryRow();
//        } else {
            $a_model = Yii::app()->db->createCommand()->select([
                                //Documento
                                'M.movement_id',
                                'M.document_serie as document_serie',
                                'RIGHT(M.document_correlative, 8) as document_correlative',
                                'M.emission_date',
                                'M.digest_value',
                                'M.status',
                                'M.currency',
                                "D.sunat_code",
                                "D.document_name",
                                "D.person_type",
                                'M.observation',
                                //Destinatario
                                'XP.identification_type AS recipient_identification_type',
                                'XP.identification_number AS recipient_identification_number',
                                'XP.person_name AS recipient_name',
                                //Comprador o solicitante
                                "RP.identification_type AS buyer_identification_type",
                                "RP.identification_number AS buyer_identification_number",
                                "REPLACE(RP.person_name, ',', ' ') AS buyer_name",
                                //Documento origen
                                "IF(MPP.movement_id IS NOT NULL, DPP.sunat_code, IF(MP.movement_id IS NOT NULL, DP.sunat_code, NULL)) AS parent_reference_sunat_code",
                                "IF(MPP.movement_id IS NOT NULL, IF(DPP.sunat_code = '" . Document::FACTURA . "', 'Factura', 'Boleta'), IF(MP.movement_id IS NOT NULL, IF(DP.sunat_code = '" . Document::FACTURA . "', 'Factura', 'Boleta'), NULL)) AS parent_reference_type",
                                "IF(MPP.movement_id IS NOT NULL, CONCAT(MPP.document_serie, '-', CAST(MPP.document_correlative AS INT)), IF(MP.movement_id IS NOT NULL, CONCAT(MP.document_serie, '-', CAST(MP.document_correlative AS INT)), NULL)) AS parent_reference_document",
                                //Direcciones
                                "IF(OA.address IS NOT NULL, CONCAT(OA.address, ' (', OG.dist_name, ' - ', OG.prov_name, ' - ', OG.dept_name,')'), '-') AS originAddress",
                                "IF(DA.address IS NOT NULL, CONCAT(DA.address, ' (', DG.dist_name, ' - ', DG.prov_name, ' - ', DG.dept_name,')'), '-') AS delivery_address",
                                //Datos de traslado
                                'MU.value AS reason_transfer',
                                'MU.description AS reason_transfer_label',
                                'PL.reason_transfer_description',
                                'PL.transfer_start_date',
                                'PL.delivery_carrier_date',
                                'PL.number_packages',
                                'PL.total_weight',
                                'PL.transport_data AS transport_type_code',
                                'IF(PL.transport_data = 1,\'TRANSPORTE PRIVADO\',\'TRANSPORTE PÚBLICO\') AS transport_type',
                                //Datos de trasportista
                                'PT.identification_type AS company_identification_type',
                                'PT.identification_number AS company_identification_number',
                                'PT.person_name AS company_name',
                                //Datos de trasporte
                                'PS.identification_type AS shipper_identification_type',
                                'PS.identification_number AS shipper_identification_number',
                                'PS.person_name AS shipper_name',
                                'SH.license_number',
                                'UT.plate',
                                "LSM.basename",
                                "LSM.pdf_link"
                            ])
                            ->from('product_list PL')
                            ->join('movement M', 'PL.owner_id = M.movement_id')
                            ->join('document D', 'D.document_code = M.document_code')
                            ->join('person XP', 'XP.person_id = M.aux_person_id')
                            //Comprador
                            ->leftJoin('person RP', 'RP.person_id = PL.recipient_id')
                            //Modalidad de traslado
                            ->leftJoin('multitable MU', 'MU.multi_id = PL.reason_transfer_id')
                            //Padre - SaleBill
                            ->leftJoin('movement_link ML', "ML.movement_id = M.movement_id AND ML.parent_route IN ('commercial/saleBill', 'warehouse/saleOut')")
                            ->leftJoin('movement MP', 'MP.movement_id = ML.movement_parent_id')
                            ->leftJoin('document DP', 'DP.document_code = MP.document_code')
                            //Padre - SaleBill
                            ->leftJoin('movement_link ML2', "ML2.movement_id = MP.movement_id AND ML2.parent_route IN ('commercial/saleBill')")
                            ->leftJoin('movement MPP', 'MPP.movement_id = ML2.movement_parent_id')
                            ->leftJoin('document DPP', 'DPP.document_code = MPP.document_code')
                            //Direcciones
                            ->leftJoin('address OA', "OA.owner = '" . Movement::OWNER . "' AND OA.owner_id = M.movement_id AND OA.order = 0")//Dirección de origen
                            ->leftJoin('geoloc OG', "OG.dept_code = OA.dept_code and OG.prov_code = OA.prov_code and OG.dist_code = OA.dist_code")
                            ->leftJoin('address DA', "DA.owner = '" . Movement::OWNER . "' and DA.owner_id = M.movement_id and DA.`order` = 1")//Dirección  de Entrega
                            ->leftJoin('geoloc DG', "DG.dept_code = DA.dept_code and DG.prov_code = DA.prov_code and DG.dist_code = DA.dist_code")
                            //Transportista
                            ->leftJoin('person PT', 'PT.person_id = PL.transport_company_id')
                            //Conductor
                            ->leftJoin('owner_pair OPPSH', "OPPSH.type = '" . OwnerPair::TYPE_MOVEMENT_SHIPPER . "' AND OPPSH.`owner` = '" . Movement::OWNER . "' AND OPPSH.owner_id = M.movement_id")
                            ->leftJoin('owner_pair OPSH', "OPSH.type = OPPSH.type AND OPSH.`owner` = '" . Shipper::OWNER . "' AND OPSH.parent_id = OPPSH.pair_id AND OPSH.order = 1")
                            ->leftJoin('shipper SH', "SH.shipper_id = OPSH.owner_id")
                            ->leftJoin('person PS', "PS.person_id = SH.person_id")
                            //Transporte
                            ->leftJoin('owner_pair OPPUT', "OPPUT.type = '" . OwnerPair::TYPE_MOVEMENT_TRANSPORT_UNIT . "' AND OPPUT.`owner` = '" . Movement::OWNER . "' AND OPPUT.owner_id = M.movement_id")
                            ->leftJoin('owner_pair OPUT', "OPUT.type = OPPUT.type AND OPUT.`owner` = '" . TransportUnit::OWNER . "' AND OPUT.parent_id = OPPUT.pair_id AND OPUT.order = 1")
                            ->leftJoin('transport_unit UT', "UT.transport_unit_id = OPUT.owner_id")
                            //
                            ->leftJoin('sent_movement LSM', 'LSM.movement_id = M.movement_id AND LSM.`last`')
                            ->where("PL.owner = '" . Movement::OWNER . "' AND M.{$s_col} = :{$s_col}", [
                                ":{$s_col}" => $p_m_id_code
                            ])->queryRow();
        }

        if ($a_model !== false) {

            $items = Yii::app()->db->createCommand()->select([
                        "I.item_id",
                        "I.product_type",
                        "I.product_id",
                        "I.description",
                        "I.pres_quantity",
                        "IFNULL(PR.measure_name, '" . Yii::app()->controller->getDefaultMerchandiseMeasure()->abbreviation . "') AS measure_name",
                        "UPPER(I.product_name) AS product_name",
                    ])
                    ->from('item I')
                    ->join('global_var GV', 'GV.organization_id = 1')
                    ->leftJoin('presentation PR', 'PR.product_id = I.product_id AND PR.equivalence = I.equivalence')
                    ->where('I.movement_id = :movement_id', [
                        ':movement_id' => $a_model['movement_id']
                    ])
                    ->queryAll();
            //Path
            $s_path = SIANFE::getRemissionGuideDir();
            //Basename
            if (isset($a_model['basename']) && !USString::isBlank($a_model['basename'])) {
                $s_basename = $a_model['basename'];
            } else {
                $s_basename = SIANFE::getGuideBasename($a_model['sunat_code'], $a_model['document_serie'], $a_model['document_correlative']);
            }
            //     
            $s_digest_value = "";
            $s_barcode_filepath = "";
            if ($p_movement_type == Scenario::TYPE_WAREHOUSE) {
                $s_digest_value = SIANFEXML::getDigestValue($a_model['digest_value'], $a_model['movement_id'], $a_model['sunat_code'], $s_path, $s_basename);
                $s_barcode_filepath = SIANFE::getBarcodeDir() . $s_basename . SIANFE::PNG_EXTENSION;
                USImage::saveBarcode($s_digest_value, $s_barcode_filepath, "PDF417,1,4", 8, 4);
            }

            return array(
                'model' => $a_model,
                'items' => $items,
                'digest_value' => $s_digest_value,
                'file' => $s_basename,
                'file_pdf_link' => $a_model['pdf_link'],
                'filename' => $s_basename . SIANFE::PDF_EXTENSION,
                'filepath' => SIANFE::getPdfDir() . $s_basename . SIANFE::PDF_EXTENSION,
                'filepathPNG' => $s_barcode_filepath,
                'fileurl' => SIANFE::getPdfUrl() . $s_basename . SIANFE::PDF_EXTENSION,
                'stores' => Store::get(Store::MODE_PRINT)
            );
        } else {
            return false;
        }
    }

    /**
     * Obtiene el número del archivo (SIN RUC)
     * @param string $p_s_basename Nombre base
     * @return string Nombre de archiv sin RUC
     */
    public static function unsetRUC($p_s_basename) {
        return str_replace(Yii::app()->controller->getOrganization()->person->identification_number . '-', '', $p_s_basename);
    }

}
