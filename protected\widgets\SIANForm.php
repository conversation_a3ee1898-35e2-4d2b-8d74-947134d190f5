<?php

class SIANForm extends CWidget {

    public $id;
    public $type = 'vertical';
    public $action = null;
    public $enableAjaxValidation = false;
    public $enableClientValidation = false;
    public $method = 'POST';
    public $clientOptions = null;
    public $htmlOptions = [];
    public $search_mode = false;
    //CONTROL
    public $submit_id;
    public $submit_label = 'Guardar';
    public $submit_icon = 'fa fa-lg fa-check';
    public $cancel_id;
    public $cancel_route = 'index';
    public $beforeunload = true;
    public $continue_call = 'false';
    public $cancel = true;
    //PRIVATE
    private $controller;
    private $form;
    //
    public $custom_code = "";

    /**
     * Initializes the widget.
     */
    public function init() {
        //CONTROL
        $this->controller = Yii::app()->controller;
        //IDS
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->submit_id = isset($this->submit_id) ? $this->submit_id : $this->controller->getServerId();
        $this->cancel_id = isset($this->cancel_id) ? $this->cancel_id : $this->controller->getServerId();

        $this->form = $this->beginWidget('booster.widgets.TbActiveForm', array(
            'id' => $this->id,
            'type' => $this->type,
            'enableAjaxValidation' => $this->enableAjaxValidation,
            'enableClientValidation' => $this->enableClientValidation,
            'method' => $this->method,
            'action' => $this->action,
            'clientOptions' => $this->clientOptions,
            'htmlOptions' => $this->htmlOptions,
        ));

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-form.js');
    }

    public function run() {
        $this->endWidget();
    }

    public static function errorBlock($model, $attribute) {
        if ($model->hasErrors($attribute)) {
            return "<span class='help-block error'>{$model->getError($attribute)}</span>";
        } else {
            return null;
        }
    }

    public function errorSummary($p_m_model) {
        $a_models = [];

        if (is_array($p_m_model)) {
            foreach ($p_m_model as $o_model) {
                if (is_subclass_of($o_model, 'SIANModel')) {
                    $a_models = array_merge($a_models, $o_model->getSubModels());
                } else {
                    $a_models[] = $o_model;
                }
            }
        } else {
            if (is_subclass_of($p_m_model, 'SIANModel')) {
                $a_models = $p_m_model->getSubModels();
            } else {
                $a_models[] = $p_m_model;
            }
        }

        return $this->form->errorSummary($a_models);
    }

    public function captchaRow($model, $attribute, array $htmlOptions = []) {
        $prepend = isset($htmlOptions['prepend']) ? $htmlOptions['prepend'] : null;
        unset($htmlOptions['prepend']);
        //Agregamos atributo de model
        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }

        return $this->form->captchaGroup($model, $attribute, array(
                    'label' => isset($htmlOptions['label']) ? $htmlOptions['label'] : null,
                    'append' => isset($htmlOptions['append']) ? $htmlOptions['append'] : null,
                    'prepend' => $prepend,
                    'widgetOptions' => array(
                        'htmlOptions' => $htmlOptions,
                    ),
                    'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
        ));
    }

    public function checkboxRow($model, $attribute, array $htmlOptions = []) {
        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }
        return $this->form->checkboxGroup($model, $attribute, array(
                    'label' => isset($htmlOptions['label']) ? $htmlOptions['label'] : null,
                    'widgetOptions' => array(
                        'htmlOptions' => $htmlOptions,
                    ),
                    'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
        ));
    }

    public static function checkBoxNonActive($label, $name, $checked, array $htmlOptions = []) {

        $htmlOptions['id'] = isset($htmlOptions['id']) ? $htmlOptions['id'] : Yii::app()->controller->getServerId();

        $html = "<div class='form-group'>";
        $html .= CHtml::label($label, $htmlOptions['id'], array(
                    'class' => 'control-label'
        ));
        $html .= "<br>";
        $html .= CHtml::checkBox($name, $checked, $htmlOptions);
        //Hint
        $html .= self::getHint($htmlOptions);
        $html .= "</div>";
        return $html;
    }

    public function checkboxListRow($model, $attribute, array $data, array $htmlOptions = []) {
        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }
        return $this->form->checkboxListGroup($model, $attribute, array(
                    'label' => isset($htmlOptions['label']) ? $htmlOptions['label'] : null,
                    'widgetOptions' => array(
                        'data' => $data,
                        'htmlOptions' => $htmlOptions,
                    ),
                    'inline' => isset($htmlOptions['inline']) ? $htmlOptions['inline'] : false,
                    'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
        ));
    }

    public function colorPickerRow($model, $attribute, array $htmlOptions = []) {
        $hint = isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null;
        unset($htmlOptions['hint']);
        $id = isset($htmlOptions['id']) ? $htmlOptions['id'] : Yii::app()->controller->getServerId();
        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }

        return $this->form->colorpickerGroup($model, $attribute, array(
                    'append' => '<span class="fa fa-lg fa-tint black"></span>',
                    'widgetOptions' => [
                        'id' => $id,
                    ],
                    'wrapperHtmlOptions' => $htmlOptions,
                    'hint' => $hint,
                        )
        );
    }

    public function radioButtonListRow($model, $attribute, array $data, array $htmlOptions = []) {
        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }
        return $this->form->radioButtonListGroup($model, $attribute, array(
                    'label' => isset($htmlOptions['label']) ? $htmlOptions['label'] : null,
                    'widgetOptions' => array(
                        'data' => $data,
                        'htmlOptions' => $htmlOptions,
                    ),
                    'inline' => isset($htmlOptions['inline']) ? $htmlOptions['inline'] : false,
                    'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
        ));
    }

    public static function radioButtonListNonActive($label, $name, $select, array $data, array $htmlOptions = []) {
        $htmlOptions['id'] = isset($htmlOptions['id']) ? $htmlOptions['id'] : Yii::app()->controller->getServerId();
        $htmlOptions['class'] = 'form-control ' . (isset($htmlOptions['class']) ? $htmlOptions['class'] : '');

        $group_html_options = isset($htmlOptions['group']) ? $htmlOptions['group'] : [];
        unset($htmlOptions['group']);

        $html = "<div class='form-group' " . (isset($group_html_options['style']) ? 'style=' . $group_html_options['style'] : '') . ">";
        //Label
        $html .= self::getLabel($label, $htmlOptions);
        //Prepend/Append
        $html .= self::getPrependAppend(CHtml::radioButtonList($name, $select, $data, $htmlOptions));
        //Hint
        $html .= self::getHint($htmlOptions);
        //
        $html .= "</div>";
        return $html;
    }

    public function ckEditorRow($model, $attribute, $cms, array $htmlOptions = []) {
        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }
        return $this->widget("application.widgets.USCKEditor", array(
                    'id' => isset($htmlOptions['id']) ? $htmlOptions['id'] : Yii::app()->controller->getServerId(),
                    'form' => $this,
                    'model' => $model,
                    'cms' => $cms,
                    'attribute' => $attribute,
                    'htmlOptions' => $htmlOptions,
                        ), true);
    }

    public function dropDownListRow($model, $attribute, array $data, array $htmlOptions = []) {

        $required = isset($htmlOptions['required']) ? $htmlOptions['required'] : $model->isAttributeRequired($attribute);
        unset($htmlOptions['required']);
        $prepend = isset($htmlOptions['prepend']) ? $htmlOptions['prepend'] : null;
        unset($htmlOptions['prepend']);

        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }

        return $this->form->dropDownListGroup($model, $attribute, array(
                    'label' => isset($htmlOptions['label']) ? $htmlOptions['label'] : null,
                    'append' => isset($htmlOptions['append']) ? $htmlOptions['append'] : null,
                    'prepend' => $prepend,
                    'widgetOptions' => array(
                        'data' => $data,
                        'htmlOptions' => $htmlOptions,
                    ),
                    'labelOptions' => array(
                        'required' => $required,
                    ),
                    'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
        ));
    }

    public function dropDownList($model, $attribute, array $data, array $htmlOptions = []) {

        $required = isset($htmlOptions['required']) ? $htmlOptions['required'] : $model->isAttributeRequired($attribute);
        unset($htmlOptions['required']);
        $prepend = isset($htmlOptions['prepend']) ? $htmlOptions['prepend'] : null;
        unset($htmlOptions['prepend']);

        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }

        return $this->form->dropDownListGroup($model, $attribute, array(
                    'label' => false,
                    'append' => isset($htmlOptions['append']) ? $htmlOptions['append'] : null,
                    'prepend' => $prepend,
                    'widgetOptions' => array(
                        'data' => $data,
                        'htmlOptions' => $htmlOptions,
                    ),
                    'labelOptions' => array(
                        'required' => $required,
                    ),
                    'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
        ));
    }

    public function hiddenField($model, $attribute, array $htmlOptions = []) {

        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }

        return $this->form->hiddenField($model, $attribute, $htmlOptions);
    }

    public function numberFieldRow($model, $attribute, array $htmlOptions = []) {
        $required = isset($htmlOptions['required']) ? $htmlOptions['required'] : $model->isAttributeRequired($attribute);
        unset($htmlOptions['required']);
        $prepend = isset($htmlOptions['prepend']) ? $htmlOptions['prepend'] : null;
        unset($htmlOptions['prepend']);

        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }

        return $this->form->numberFieldGroup($model, $attribute, array(
                    'label' => isset($htmlOptions['label']) ? $htmlOptions['label'] : null,
                    'append' => isset($htmlOptions['append']) ? $htmlOptions['append'] : null,
                    'prepend' => $prepend,
                    'widgetOptions' => array(
                        'htmlOptions' => $htmlOptions
                    ), 'labelOptions' => array(
                        'required' => $required,
                    ),
                    'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
        ));
    }

    public function passwordFieldRow($model, $attribute, array $htmlOptions = []) {
        $prepend = isset($htmlOptions['prepend']) ? $htmlOptions['prepend'] : null;
        unset($htmlOptions['prepend']);

        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }

        return $this->form->passwordFieldGroup($model, $attribute, array(
                    'append' => isset($htmlOptions['append']) ? $htmlOptions['append'] : null,
                    'prepend' => $prepend,
                    'widgetOptions' => array(
                        'htmlOptions' => $htmlOptions
                    ),
                    'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
        ));
    }

    public function labelEx($model, $attribute, $options = []) {
        return $this->form->labelEx($model, $attribute, $options);
    }

    public function textAreaRow($model, $attribute, array $htmlOptions = []) {

        $required = isset($htmlOptions['required']) ? $htmlOptions['required'] : $model->isAttributeRequired($attribute);
        unset($htmlOptions['required']);

        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }

        echo $this->form->textAreaGroup($model, $attribute, array(
            'label' => isset($htmlOptions['label']) ? $htmlOptions['label'] : null,
            'widgetOptions' => array(
                'htmlOptions' => $htmlOptions
            ),
            'labelOptions' => array(
                'required' => $required,
            ),
            'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
        ));
    }

    public function textField($model, $attribute, array $htmlOptions = []) {
        $prepend = isset($htmlOptions['prepend']) ? $htmlOptions['prepend'] : null;
        unset($htmlOptions['prepend']);

        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }

        return $this->form->textFieldGroup($model, $attribute, array(
                    'label' => false,
                    'append' => isset($htmlOptions['append']) ? $htmlOptions['append'] : null,
                    'prepend' => $prepend,
                    'widgetOptions' => array(
                        'htmlOptions' => $htmlOptions
                    ),
                    'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
        ));
    }

    public function fileFieldRow($model, $attribute, array $htmlOptions = []) {
        $prepend = isset($htmlOptions['prepend']) ? $htmlOptions['prepend'] : null;
        unset($htmlOptions['prepend']);

        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }

        return $this->form->fileFieldGroup($model, $attribute, array(
                    'label' => isset($htmlOptions['label']) ? $htmlOptions['label'] : null,
                    'append' => isset($htmlOptions['append']) ? $htmlOptions['append'] : null,
                    'prepend' => $prepend,
                    'widgetOptions' => array(
                        'htmlOptions' => $htmlOptions,
                    ),
                    'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
                    'wrapperHtmlOptions' => array(
                        'class' => 'col-sm-5',
                    )
        ));
    }

    public function textFieldRow($model, $attribute, array $htmlOptions = []) {

        $required = isset($htmlOptions['required']) ? $htmlOptions['required'] : $model->isAttributeRequired($attribute);
        unset($htmlOptions['required']);
        $prepend = isset($htmlOptions['prepend']) ? $htmlOptions['prepend'] : null;
        unset($htmlOptions['prepend']);

        if ($this->search_mode) {
            $htmlOptions['model'] = get_class($model);
            $htmlOptions['attribute'] = $attribute;
        }

        return $this->form->textFieldGroup($model, $attribute, array(
                    'label' => isset($htmlOptions['label']) ? $htmlOptions['label'] : null,
                    'append' => isset($htmlOptions['append']) ? $htmlOptions['append'] : null,
                    'prepend' => $prepend,
                    'widgetOptions' => array(
                        'htmlOptions' => $htmlOptions,
                    ),
                    'labelOptions' => array(
                        'required' => $required,
                    ),
                    'hint' => isset($htmlOptions['hint']) ? $htmlOptions['hint'] : null,
        ));
    }

    public static function textFieldNonActive($label, $name, $value, array $htmlOptions = []) {

        $htmlOptions['id'] = isset($htmlOptions['id']) ? $htmlOptions['id'] : Yii::app()->controller->getServerId();
        $htmlOptions['class'] = 'form-control ' . (isset($htmlOptions['class']) ? $htmlOptions['class'] : '');
        $htmlOptions['placeholder'] = isset($htmlOptions['placeholder']) ? $htmlOptions['placeholder'] : $label;

        $html = "<div class='form-group'>";
        //Label
        $html .= self::getLabel($label, $htmlOptions);
        //Prepend/Append
        $html .= self::getPrependAppend(CHtml::textField($name, $value, $htmlOptions), $htmlOptions);
        //Hint
        $html .= self::getHint($htmlOptions);
        //
        $html .= "</div>";
        return $html;
    }

    public static function dropDownListNonActive($label, $name, $value, array $data, array $htmlOptions = []) {

        $htmlOptions['id'] = isset($htmlOptions['id']) ? $htmlOptions['id'] : Yii::app()->controller->getServerId();
        $htmlOptions['class'] = 'form-control ' . (isset($htmlOptions['class']) ? $htmlOptions['class'] : '');

        $html = "<div class='form-group'>";
        //Label
        $html .= self::getLabel($label, $htmlOptions);
        //Prepend/Append
        $html .= self::getPrependAppend(CHtml::dropDownList($name, $value, $data, $htmlOptions), $htmlOptions);
        //Hint
        $html .= self::getHint($htmlOptions);
        //
        $html .= "</div>";
        return $html;
    }

    public static function textAreaNonActive($label, $name, $value, array $htmlOptions = []) {

        $htmlOptions['id'] = isset($htmlOptions['id']) ? $htmlOptions['id'] : Yii::app()->controller->getServerId();
        $htmlOptions['class'] = 'form-control ' . (isset($htmlOptions['class']) ? $htmlOptions['class'] : '');
        $htmlOptions['placeholder'] = isset($htmlOptions['placeholder']) ? $htmlOptions['placeholder'] : $label;

        $html = "<div class='form-group'>";
        //Label
        $html .= self::getLabel($label, $htmlOptions);
        //Prepend/Append
        $html .= self::getPrependAppend(CHtml::textArea($name, $value, $htmlOptions), $htmlOptions);
        //Hint
        $html .= self::getHint($htmlOptions);
        //
        $html .= "</div>";
        return $html;
    }

    public static function numberFieldNonActive($label, $name, $value, array $htmlOptions = []) {

        $htmlOptions['id'] = isset($htmlOptions['id']) ? $htmlOptions['id'] : Yii::app()->controller->getServerId();
        $htmlOptions['class'] = 'form-control ' . (isset($htmlOptions['class']) ? $htmlOptions['class'] : '');

        $html = "<div class='form-group'>";
        //Label
        $html .= self::getLabel($label, $htmlOptions);
        //Prepend/Append
        $html .= self::getPrependAppend(CHtml::numberField($name, $value, $htmlOptions), $htmlOptions);
        //Hint
        $html .= self::getHint($htmlOptions);
        //
        $html .= "</div>";
        return $html;
    }

    public static function numberIntegerFieldNonActive($label, $name, $value, array $htmlOptions = []) {

        $htmlOptions['id'] = isset($htmlOptions['id']) ? $htmlOptions['id'] : Yii::app()->controller->getServerId();
        $htmlOptions['class'] = 'form-control ' . (isset($htmlOptions['class']) ? $htmlOptions['class'] : '');

        $min = isset($htmlOptions['min']) ? $htmlOptions['min'] : 1;
        $max = isset($htmlOptions['max']) ? $htmlOptions['max'] : PHP_INT_MAX; // Usar el máximo entero posible si no se proporciona un máximo específico

        $numberField = CHtml::numberField($name, $value, array_merge($htmlOptions, ['step' => 1, 'min' => $min, 'max' => $max]));

        Yii::app()->getClientScript()->registerScript('roundDown', "
            $('#{$htmlOptions['id']}').on('blur', function() {
                var value = parseFloat(this.value);
                if (!isNaN(value)) {
                    this.value = Math.floor(value);
                }
            });
        ");

        $html = "<div class='form-group'>";
        $html .= self::getLabel($label, $htmlOptions);
        $html .= self::getPrependAppend($numberField, $htmlOptions);
        $html .= self::getHint($htmlOptions);
        $html .= "</div>";

        return $html;
    }

    private static function getLabel($p_s_label, array $p_a_htmlOptions = []) {

        if ($p_s_label) {
            return CHtml::label($p_s_label, $p_a_htmlOptions['id'], array(
                        'class' => 'control-label'
            ));
        } else {
            return '';
        }
    }

    private static function getPrependAppend($p_s_input_html = '', array $p_a_htmlOptions = []) {
        $s_html = '';
        //Agregamos prepend
        if (isset($p_a_htmlOptions['prepend']) || isset($p_a_htmlOptions['append'])) {
            $s_html .= "<div class='input-group'>";
            if (isset($p_a_htmlOptions['prepend'])) {
                $s_html .= "<span class='input-group-addon'>{$p_a_htmlOptions['prepend']}</span>";
            }
        }
        //Agregamos input
        $s_html .= $p_s_input_html;
        //Agregamos apend
        if (isset($p_a_htmlOptions['prepend']) || isset($p_a_htmlOptions['append'])) {
            if (isset($p_a_htmlOptions['append'])) {
                $s_html .= "<span class='input-group-addon'>{$p_a_htmlOptions['append']}</span>";
            }
            $s_html .= "</div>";
        }
        //
        return $s_html;
    }

    private static function getHint(array $htmlOptions = []) {
        if (isset($htmlOptions['hint']) && strlen($htmlOptions['hint']) > 0) {
            return "<span class='help-block'>{$htmlOptions['hint']}</span>";
        } else {
            return "";
        }
    }

    public function getForm() {
        return $this->form;
    }

    public function footer() {

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        $('body').on('click', '#{$this->submit_id}', function() {
            
            if(window.active_ajax == 0)
            {
                var button = $(this);
                button.attr('disabled', true);
                
                {$this->custom_code};
                SIANFormContinue(button, {$this->continue_call})
            }
            else
            {
                bootbox.alert(us_message('Hay algún proceso ejecutandose, vuelva a intentarlo', 'warning'));
                return false;
            }
        });
        ");

        $buttons = [];

        $buttons[] = array(
            'id' => $this->submit_id,
            'context' => 'primary',
            'icon' => $this->submit_icon . ' white',
            'buttonType' => 'button',
            'label' => $this->submit_label,
            'size' => 'default',
        );

        if ($this->cancel) {
            $buttons[] = array(
                'id' => $this->cancel_id,
                'context' => 'default',
                'icon' => 'fa fa-lg fa-times',
                'label' => 'Cancelar',
                'route' => $this->cancel_route,
                'size' => 'default',
            );
        }

        return $this->widget('application.widgets.USButtons', array(
                    'buttons' => $buttons), true);
    }

}
