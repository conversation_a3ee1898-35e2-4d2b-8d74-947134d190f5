<?php

$arrayItemsNavbar = array(
    // array('icon' => 'th-list', 'label' => 'Datos Generales', 'url' => '#', 'items' => array(
    array('label' => 'Datos Generales', 'url' => '#', 'items' => array(
            array('icon' => 'list', 'label' => 'Series', 'route' => 'serie/index'),
        )),
    // array('icon' => 'list', 'label' => 'Movimientos', 'url' => '#', 'items' => array(
    array('label' => 'Movimientos', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'briefcase', 'label' => 'Órdenes de servicio', 'route' => 'serviceOrder/index', 'items' => array(
                        array('icon' => 'plus', 'label' => 'Crear', 'route' => 'serviceOrder/create'),
                    )),
            ),
        )),
    // array('icon' => 'file', 'label' => 'Reportes', 'url' => '#', 'items' => array(               
    array('label' => 'Reportes', 'url' => '#', 'items' => array(
            array('icon' => 'list', 'label' => 'Órdenes por Técnico', 'route' => 'report/ordersPerEmployee'),
            array('icon' => 'gift', 'label' => 'Reporte de Garantías', 'route' => 'report/warranty'),
        )),
);
$this->widget('application.widgets.SIANNavbar', array(
    'brand' => Yii::app()->id,
    'class' => 'navbar-support',
    'items' => $this->items_menu,
));

