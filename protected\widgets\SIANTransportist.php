<?php

class SIANTransportist extends CWidget {

    public $form;
    public $model;
    public $modal_id;
    //IDS
    public $id;
    public $document_sunat_code_id;
    public $transport_data_id;
    public $transport_company_id;
    public $shipper_control_id;
    public $transport_unit_control_id;
    public $reason_transfer_items;
    public $company_reset_id;
    public $company_aux_id;
    public $company_search_id;
    public $weight_input_id;
    public $number_packages_input_id;
    public $panel_main_class; // Clase principal del Panel    
    //PRIVATE
    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;
        // Clase principal del Panel
        $this->panel_main_class = isset($this->panel_main_class) ? $this->panel_main_class : $this->controller->getServerId();
        //IDS
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->document_sunat_code_id = isset($this->document_sunat_code_id) ? $this->document_sunat_code_id : $this->controller->getServerId();
        $this->transport_data_id = isset($this->transport_data_id) ? $this->transport_data_id : $this->controller->getServerId();
        $this->transport_company_id = isset($this->transport_company_id) ? $this->transport_company_id : $this->controller->getServerId();
        $this->shipper_control_id = isset($this->shipper_control_id) ? $this->shipper_control_id : $this->controller->getServerId();
        $this->transport_unit_control_id = isset($this->transport_unit_control_id) ? $this->transport_unit_control_id : $this->controller->getServerId();
        $this->company_reset_id = isset($this->company_reset_id) ? $this->company_reset_id : $this->controller->getServerId();
        $this->company_aux_id = isset($this->company_aux_id) ? $this->company_aux_id : $this->controller->getServerId();
        $this->company_search_id = isset($this->company_search_id) ? $this->company_aux_id : $this->controller->getServerId();
        // Variables para `peso` y `numero de bultos`
        $this->weight_input_id = isset($this->weight_input_id) ? $this->weight_input_id : Yii::app()->controller->getServerId();
        $this->number_packages_input_id = isset($this->number_packages_input_id) ? $this->number_packages_input_id : Yii::app()->controller->getServerId();

        $this->model->number_packages = isset($this->model->number_packages) ? (float) $this->model->number_packages : 0.00;
        $this->model->total_weight = isset($this->model->total_weight) ? (float) $this->model->total_weight : 0.00;
        //Registramos assets
        SIANAssets::registerCssFile('css/sian-transportist.css');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        $(document).ready(function() {
        
        });
        
        function enabledShippersByDocumentAndTransportType(){
            if($('#{$this->document_sunat_code_id}').val() == '" . Document::REMISSION_GUIDE . "'){
                {$this->shipper_control_id}SIANOwnerPairReadonly(true);
                {$this->transport_unit_control_id}SIANOwnerPairReadonly(true);
            } else{
                {$this->shipper_control_id}SIANOwnerPairReadonly(false);
                {$this->transport_unit_control_id}SIANOwnerPairReadonly(false);
            }
        }

        //SI CAMBIA OPERACION
        $('#{$this->id}').data('changeDocument', function(changeData){

            $('#{$this->document_sunat_code_id}').val(changeData.sunat_code);                
        });        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'id' => 'TransferData',
            'title' => 'Datos del traslado',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => $this->panel_main_class
            )
        ));

        echo "<div class = 'section-1'>";

        echo "<div class = 'transfer_start_date'>";
        echo $this->widget('application.widgets.USDatePicker', array(
            'form' => $this->form,
            'model' => $this->model,
            'attribute' => 'transfer_start_date',
            'options' => array(
                'minDate' => SIANTime::formatDate(),
            ),
            'htmlOptions' => array(
                'class' => 'sian-document-selector-emission-date ',
                'required' => true,
            )
                ), true
        );
        echo "</div>";

        echo "<div class = 'delivery_carrier_date'>";
        echo $this->widget('application.widgets.USDatePicker', array(
            'form' => $this->form,
            'model' => $this->model,
            'attribute' => 'delivery_carrier_date',
            'options' => array(
                'minDate' => SIANTime::formatDate(),
            ),
            'htmlOptions' => array(
                'class' => 'sian-document-selector-emission-date ',
                'required' => true,
            )
                ), true
        );
        echo "</div>";

        echo "<div class = 'number_packages'>";
        echo $this->form->numberFieldRow($this->model, 'number_packages', array(
            'id' => $this->number_packages_input_id,
            'min' => '1',
            'step' => '0.50',
            'required' => true,
        ));
        echo "</div>";

        echo "<div class = 'total_weight'>";
        echo $this->form->numberFieldRow($this->model, 'total_weight', array(
            'id' => $this->weight_input_id,
            'append' => 'KG',
            'min' => '1',
            'step' => '0.50',
            'required' => true,
        ));
        echo "</div>";

        $a_reason_transfer_options = [
            'class' => 'sian-transportist-reason-transfer-id',
            'required' => true];

        if (count($this->reason_transfer_items) > 1) {
            $a_reason_transfer_options['empty'] = Strings::SELECT_OPTION;
        }

        echo "<div class = 'reason_transfer'>";
        echo $this->form->dropDownListRow($this->model, 'reason_transfer_id', $this->reason_transfer_items, $a_reason_transfer_options);
        echo "</div>";

        echo "<div class = 'reason_transfer_description'>";
        echo $this->form->textFieldRow($this->model, 'reason_transfer_description', array(
            'placeholder' => 'Descripción del motivo de traslado.',
        ));
        echo "</div>";

        echo "</div>";

        echo "<hr>";

        echo "<div class = 'row'>";
        echo "<div class = 'col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo $this->form->hiddenField($this->model->movement, 'document_sunat_code', array('id' => $this->document_sunat_code_id));
        $this->widget('application.widgets.USSwitch', array(
            'id' => $this->transport_data_id,
            'model' => $this->model,
            'attribute' => 'transport_data',
            'offText' => 'Público',
            'onText' => 'Privado',
            'required' => true,
            'switchChange' => ""
            . " if(this.checked){  //Privado
                
                    $('#{$this->company_aux_id}').val('" . Yii::app()->controller->getOrganization()->person->identification_number . "');
                    $('#{$this->company_search_id}').click();
                    USAutocompleteReadOnly('{$this->transport_company_id}', true);
                    {$this->shipper_control_id}SIANOwnerPairReadonly(false);
                    {$this->transport_unit_control_id}SIANOwnerPairReadonly(false);
                        
                } else{  //Público
                
                    USAutocompleteReadOnly('{$this->transport_company_id}', false);
                    USAutocompleteReset('{$this->transport_company_id}');
                    $('#{$this->company_reset_id}').click();
//                    enabledShippersByDocumentAndTransportType();
                }
                "
        ));
        echo "</div> ";
        echo "<div class = 'col-lg-10 col-md-10 col-sm-10 col-xs-12'>";

        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->transport_company_id,
            'model' => $this->model,
            'attribute' => 'transport_company_id',
            'view' => array(
                'model' => 'DpBusinessPartner',
                'scenario' => DpBusinessPartner::SCENARIO_TRANSPORT_COMPANY,
                'attributes' => array(
                    array('name' => 'identification_number', 'width' => 20, 'types' => array('aux')),
                    array('name' => 'identification_name', 'width' => 20),
                    array('name' => 'person_name', 'width' => 60, 'types' => array('text')),
                    array('name' => 'person_id', 'width' => 0, 'types' => array('id', 'value'), 'hidden' => true),
                )
            ),
            'maintenance' => array(
                'module' => 'administration',
                'controller' => 'person',
                'buttons' => [
                    'create' => ['data' => ['is_transport_company' => 1]],
                    'update' => [],
                    'delete' => [],
                    'preview' => []
                ]
            ),
            'onreset' => "",
            'required' => true,
            'readonly' => $this->model->transport_data,
            'reset_id' => $this->company_reset_id,
            'search_id' => $this->company_search_id,
            'aux_id' => $this->company_aux_id,
        ));

        echo "</div>";
        echo "</div>";
        echo "<br>";

        echo "<div class = 'row'>";
        echo "<div class = 'col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        $this->widget('SIANOwnerPair', array(
            'id' => $this->shipper_control_id,
            'model' => $this->model,
            'viewClass' => 'DpOwnerChildren',
            'viewScenario' => DpOwnerChildren::SCENARIO_DRIVERS_FOR_MOVEMENT,
            'owner' => Shipper::OWNER,
            'property' => 'tempShippers',
            'title' => "Conductores",
            'has_panel' => false,
            'limit' => 3,
            'extraViewAttributes' => [
                [
                    'name' => 'identification_name',
                    'width' => 20
                ],
                [
                    'name' => 'identification_number',
                    'width' => 20
                ]
            ],
        ));
        echo "</div>";
        echo "<div class = 'col-lg-6 col-md-6 col-sm-6 col-xs-12'>";

        $this->widget('SIANOwnerPair', array(
            'id' => $this->transport_unit_control_id,
            'model' => $this->model,
            'viewClass' => 'DpOwnerChildren',
            'viewScenario' => DpOwnerChildren::SCENARIO_TRANSPORT_UNITS_FOR_MOVEMENT,
            'owner' => TransportUnit::OWNER,
            'property' => 'tempTransportUnits',
            'title' => "Unidades de transporte",
            'has_panel' => false,
            'limit' => 3,
        ));

        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

}
