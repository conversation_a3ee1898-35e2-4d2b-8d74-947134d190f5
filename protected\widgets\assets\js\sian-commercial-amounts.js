function SIANCommercialAmountsCalculate(div_id, field)
{
    var divObj = $('#' + div_id);
    var fixed = divObj.data('fixed');
    var global_igv = divObj.data('global_igv');
    global_igv = divObj.find('select.sian-commercial-amounts-igv-percent  option:selected').val();
    var as_leasing = divObj.data('as_leasing');
    var currency = divObj.data('currency');
    var is_sale = divObj.data('is_sale');
    var is_buy = divObj.data('is_buy');
    var affected1 = 0;
    var inaffected = 0;
    var as_bill = divObj.find('input.sian-commercial-amounts-as-bill').is(':checked');
    //Evitamos valores nulos
    var value = divObj.find('input.sian-commercial-amounts-' + field).val();
    if (isBlank(value)) {
        divObj.find('input.sian-commercial-amounts-' + field).val(0);
    }
    if (field === 'total') {
        SIANCommercialAmountsIterate(div_id);
    }
    if (as_leasing == 1) {
        var amortization = divObj.find('input.sian-commercial-amounts-amortization').floatVal(fixed);
        var interest = divObj.find('input.sian-commercial-amounts-interest').floatVal(fixed);
        var desgravamen = divObj.find('input.sian-commercial-amounts-desgravamen').floatVal(fixed);
        var oae_adm = divObj.find('input.sian-commercial-amounts-oae_adm').floatVal(fixed);
        var oae_financial = divObj.find('input.sian-commercial-amounts-oae_financial').floatVal(fixed);
        var oae_sales = divObj.find('input.sian-commercial-amounts-oae_sales').floatVal(fixed);
        var porte = divObj.find('input.sian-commercial-amounts-porte').floatVal(fixed);
        var itf = divObj.find('input.sian-commercial-amounts-itf').floatVal(fixed);
        var other_expenses = divObj.find('input.sian-commercial-amounts-other_expenses').floatVal(fixed);
        var base = amortization + interest + desgravamen + oae_adm + oae_financial + oae_sales + porte + itf + other_expenses;

        if (as_bill) {
            affected1 = base;            
        } else {
            inaffected = base;            
        }
        divObj.find('input.sian-commercial-amounts-affected1').floatVal(fixed, affected1);
        divObj.find('input.sian-commercial-amounts-inaffected').floatVal(fixed, inaffected);
    } else {
        affected1 = divObj.find('input.sian-commercial-amounts-affected1').floatVal(fixed);
        inaffected = divObj.find('input.sian-commercial-amounts-inaffected').floatVal(fixed);
    }
    var affected2 = divObj.find('input.sian-commercial-amounts-affected2').floatVal(fixed);
    var affected3 = divObj.find('input.sian-commercial-amounts-affected3').floatVal(fixed);
    var nobill = divObj.find('input.sian-commercial-amounts-nobill').floatVal(fixed);
    var perception = divObj.find('input.sian-commercial-amounts-perception').floatVal(fixed);
    var icbp = divObj.find('input.sian-commercial-amounts-icbp').floatVal(fixed);

    var net = affected1 + affected2 + affected3 + inaffected + nobill;
    if (field === 'igv') {
        var igv = divObj.find('input.sian-commercial-amounts-igv').floatVal(fixed);
    } else {
        var igv = USMath.round((affected1 + affected2 + affected3) * global_igv, fixed);
    }

    var total = net + igv + icbp;
    var real = total + perception;
    var crude = total;
    //
    divObj.find('input.sian-commercial-amounts-crude').floatVal(fixed, crude);
    divObj.find('input.sian-commercial-amounts-net').floatVal(fixed, net);
    divObj.find('input.sian-commercial-amounts-igv').floatVal(fixed, igv);
    divObj.find('input.sian-commercial-amounts-total').floatVal(fixed, total).floatData('prev', fixed, total);
    divObj.find('input.sian-commercial-amounts-real').floatVal(fixed, real);

    var amount_allow_retention = 0;
    var amount_allow_detraction = 0;
    var no_retention_max_amount_pen = divObj.data('no_retention_max_amount_pen');
    var no_retention_max_amount_usd = divObj.data('no_retention_max_amount_usd');
    var no_detraction_max_amount_pen = divObj.data('no_detraction_max_amount_pen');
    var no_detraction_max_amount_usd = divObj.data('no_detraction_max_amount_usd');
    var retention_input_id = divObj.data('retention_input_id');
    var detraction_input_id = divObj.data('detraction_input_id');
    //Detracción
    if (is_buy || is_sale) {
        //Retención
        if (currency == CURRENCY_PEN) {
            amount_allow_retention = (total >= no_retention_max_amount_pen) ? 1 : 0;
        }
        if (currency == CURRENCY_USD) {
            amount_allow_retention = (total >= no_retention_max_amount_usd) ? 1 : 0;
        }
        divObj.data('amount_allow_retention', amount_allow_retention);
        if (amount_allow_retention == 0)
            $('#' + retention_input_id).prop('checked', false);
        if (currency == CURRENCY_PEN) {
            amount_allow_detraction = (total >= no_detraction_max_amount_pen) ? 1 : 0;
        }
        if (currency == CURRENCY_USD) {
            amount_allow_detraction = (total >= no_detraction_max_amount_usd) ? 1 : 0;
        }
        divObj.data('amount_allow_detraction', amount_allow_detraction);
        if (amount_allow_detraction == 0)
            $('#' + detraction_input_id).prop('checked', false);
    }
    //Calculamos detracción y retención
    SIANCommercialAmountsEnableOrDisabledRetentionDetraction(div_id);
    SIANCommercialAmountsCalculateRetentionAndDetraction(div_id);
    //Actualizamos montos
    SIANCommercialAmountsUpdate(div_id);
}

function SIANCommercialAmountsCalculateTotals(div_id, omit_igv)
{
    var divObj = $('#' + div_id);
    var fixed = divObj.data('fixed');
    var global_igv = divObj.data('global_igv');
    global_igv = divObj.find('select.sian-commercial-amounts-igv-percent option:selected').text();
    var as_leasing = divObj.data('as_leasing');
    var currency = divObj.data('currency');
    var is_sale = divObj.data('is_sale');
    var is_buy = divObj.data('is_buy');
    //
    var affected1 = 0;
    if (as_leasing == 1) {
        var amortization = divObj.find('input.sian-commercial-amounts-amortization').floatVal(fixed);
        var interest = divObj.find('input.sian-commercial-amounts-interest').floatVal(fixed);
        var desgravamen = divObj.find('input.sian-commercial-amounts-desgravamen').floatVal(fixed);
        var oae_adm = divObj.find('input.sian-commercial-amounts-oae_adm').floatVal(fixed);
        var oae_financial = divObj.find('input.sian-commercial-amounts-oae_financial').floatVal(fixed);
        var oae_sales = divObj.find('input.sian-commercial-amounts-oae_sales').floatVal(fixed);
        var porte = divObj.find('input.sian-commercial-amounts-porte').floatVal(fixed);
        var itf = divObj.find('input.sian-commercial-amounts-itf').floatVal(fixed);
        var other_expenses = divObj.find('input.sian-commercial-amounts-other_expenses').floatVal(fixed);
        affected1 = amortization + interest + desgravamen + oae_adm + oae_financial + oae_sales + porte + itf + other_expenses;
        divObj.find('input.sian-commercial-amounts-affected1').floatVal(fixed, affected1);
    } else {
        affected1 = divObj.find('input.sian-commercial-amounts-affected1').floatVal(fixed);
    }

    var affected2 = divObj.find('input.sian-commercial-amounts-affected2').floatVal(fixed);
    var affected3 = divObj.find('input.sian-commercial-amounts-affected3').floatVal(fixed);
    var inaffected = divObj.find('input.sian-commercial-amounts-inaffected').floatVal(fixed);
    var nobill = divObj.find('input.sian-commercial-amounts-nobill').floatVal(fixed);
    var perception = divObj.find('input.sian-commercial-amounts-perception').floatVal(fixed);
    //Calculamos
    var net = affected1 + affected2 + affected3 + inaffected + nobill;
    if (isset(omit_igv) && omit_igv == 1) {
        var igv = divObj.find('input.sian-commercial-amounts-igv').floatVal(fixed);
    } else {
        var igv = USMath.round((affected1 + affected2 + affected3) * global_igv, fixed);
    }
    var icbp = divObj.find('input.sian-commercial-amounts-icbp').floatVal(fixed);
    var total = net + igv + icbp;
    var real = total + perception;
    var crude = total;
    //
    divObj.find('input.sian-commercial-amounts-crude').floatVal(fixed, crude);
    divObj.find('input.sian-commercial-amounts-net').floatVal(fixed, net);
    divObj.find('input.sian-commercial-amounts-igv').floatVal(fixed, igv);
    divObj.find('input.sian-commercial-amounts-total').floatVal(fixed, total);
    divObj.find('input.sian-commercial-amounts-real').floatVal(fixed, real);

    var amount_allow_retention = 0;
    var amount_allow_detraction = 0;
    var no_retention_max_amount_pen = divObj.data('no_retention_max_amount_pen');
    var no_retention_max_amount_usd = divObj.data('no_retention_max_amount_usd');
    var no_detraction_max_amount_pen = divObj.data('no_detraction_max_amount_pen');
    var no_detraction_max_amount_usd = divObj.data('no_detraction_max_amount_usd');
    var retention_input_id = divObj.data('retention_input_id');
    var detraction_input_id = divObj.data('detraction_input_id');

    if (is_buy || is_sale) {
        //Retención
        if (currency == CURRENCY_PEN) {
            amount_allow_retention = (total >= no_retention_max_amount_pen) ? 1 : 0;
        }
        if (currency == CURRENCY_USD) {
            amount_allow_retention = (total >= no_retention_max_amount_usd) ? 1 : 0;
        }
        divObj.data('amount_allow_retention', amount_allow_retention);
        if (amount_allow_retention == 0)
            $('#' + retention_input_id).prop('checked', false);
        //Detracción
        if (currency == CURRENCY_PEN) {
            amount_allow_detraction = (total >= no_detraction_max_amount_pen) ? 1 : 0;
        }
        if (currency == CURRENCY_USD) {
            amount_allow_detraction = (total >= no_detraction_max_amount_usd) ? 1 : 0;
        }
        divObj.data('amount_allow_detraction', amount_allow_detraction);
        if (amount_allow_detraction == 0)
            $('#' + detraction_input_id).prop('checked', false);
    }
    //Calculamos detracción y retención
    SIANCommercialAmountsEnableOrDisabledRetentionDetraction(div_id);
    SIANCommercialAmountsCalculateRetentionAndDetraction(div_id);
    //Guardamos dato previo
    divObj.find('input.sian-commercial-amounts-total').floatData('prev', fixed, total);
}

function SIANCommercialAmountsIterate(div_id)
{
    var divObj = $('#' + div_id);
    var fixed = divObj.data('fixed');
    var step = divObj.data('step');
    var max_iterations = divObj.data('max_iterations');
    var max_adjust_index = divObj.data('max_adjust_index');

    var prev = divObj.find('input.sian-commercial-amounts-total').floatData('prev', fixed);
    var current = divObj.find('input.sian-commercial-amounts-total').floatVal(fixed);

    var multiplicator = current / prev;
    var adjust = step;
    var adjustIndex = 0;

    var result = SIANCommercialAmountsAdjustTotal(div_id, (isFinite(multiplicator) ? multiplicator : 0), 0, adjustIndex).total;
    var increase = false;
    var decrease = false;

    var iterations = 0;
    while (result != current && iterations < max_iterations)
    {
        if (result < current && !decrease)
        {
            result = SIANCommercialAmountsAdjustTotal(div_id, 1, adjust, adjustIndex).total;
            increase = true;
        } else
        {
            if (result > current && !increase)
            {
                result = SIANCommercialAmountsAdjustTotal(div_id, 1, -adjust, adjustIndex).total;
                decrease = true;
            } else
            {
                break;
            }
        }

        if (++adjustIndex > max_adjust_index)
        {
            adjustIndex = 0;
        }
        iterations++;
    }

    divObj.find('input.sian-commercial-amounts-total').floatData('prev', fixed, result);

    //consoleLog('Amount: ' + current + ', Iterations ' + iterations);
}

function SIANCommercialAmountsGetAmount(obj, fixed, multiplicator, adjust, adjustIndex, makeZero)
{
    if (makeZero) {
        return 0;
    } else {
        return obj.floatVal(fixed) * multiplicator + (obj.floatVal(fixed) > 0 && obj.intAttr('adjustIndex') === adjustIndex ? adjust : 0);
    }
}

function SIANCommercialAmountsAdjustTotal(div_id, multiplicator, adjust, adjustIndex)
{
    var divObj = $('#' + div_id);

    var as_leasing = divObj.data('as_leasing');
    var global_igv = divObj.data('global_igv');
    global_igv = divObj.find('select.sian-commercial-amounts-igv-percent option:selected').text();
    var fixed = divObj.data('fixed');

    var crudeObj = divObj.find('input.sian-commercial-amounts-crude');
    var affected1Obj = divObj.find('input.sian-commercial-amounts-affected1');
    var affected2Obj = divObj.find('input.sian-commercial-amounts-affected2');
    var affected3Obj = divObj.find('input.sian-commercial-amounts-affected3');
    var inaffectedObj = divObj.find('input.sian-commercial-amounts-inaffected');
    var nobillObj = divObj.find('input.sian-commercial-amounts-nobill');
    var netObj = divObj.find('input.sian-commercial-amounts-net');
    var igvObj = divObj.find('input.sian-commercial-amounts-igv');
    var totalObj = divObj.find('input.sian-commercial-amounts-total');
    var perceptionObj = divObj.find('input.sian-commercial-amounts-perception');
    var realObj = divObj.find('input.sian-commercial-amounts-real');

    var affected1 = SIANCommercialAmountsGetAmount(affected1Obj, fixed, multiplicator, adjust, adjustIndex, false);
    var affected2 = SIANCommercialAmountsGetAmount(affected2Obj, fixed, multiplicator, adjust, adjustIndex, !as_leasing);
    var affected3 = SIANCommercialAmountsGetAmount(affected3Obj, fixed, multiplicator, adjust, adjustIndex, !as_leasing);
    var inaffected = SIANCommercialAmountsGetAmount(inaffectedObj, fixed, multiplicator, adjust, adjustIndex, as_leasing);
    var nobill = SIANCommercialAmountsGetAmount(nobillObj, fixed, multiplicator, adjust, adjustIndex, as_leasing);
    var perception = SIANCommercialAmountsGetAmount(perceptionObj, fixed, multiplicator, adjust, adjustIndex, false);

    var igv = (affected1 + affected2 + affected3) * global_igv;

    var net = affected1 + affected2 + affected3 + inaffected + nobill;
    var total = net + igv;
    var real = total + perception;
    var crude = total;

    crudeObj.floatVal(fixed, crude);
    affected1Obj.floatVal(fixed, affected1);
    affected2Obj.floatVal(fixed, affected2);
    affected3Obj.floatVal(fixed, affected3);
    inaffectedObj.floatVal(fixed, inaffected);
    nobillObj.floatVal(fixed, nobill);
    netObj.floatVal(fixed, net);
    igvObj.floatVal(fixed, igv);
    totalObj.floatVal(fixed, total).floatData('prev', fixed, total);
    perceptionObj.floatVal(fixed, perception);
    realObj.floatVal(fixed, real);

    return {total: USMath.round(total, fixed)};
}

function SIANCommercialAmountsUpdate(div_id)
{
    var divObj = $('#' + div_id);
    var fixed = divObj.data('fixed');
    var operationChangeData = divObj.data('operationChangeData');
    var warehouse_id = divObj.find('input.sian-commercial-amounts-warehouse-id').val();
    var independent_mode = divObj.data('independent_mode');
    var inherit_mode = divObj.data('inherit_mode');
    var last_operation_code = divObj.data('last_operation_code');
    var lastGridTotals = divObj.data('lastGridTotals');

    //Obtenemos valores
    var amortization = divObj.find('input.sian-commercial-amounts-amortization').floatVal(fixed);
    var interest = divObj.find('input.sian-commercial-amounts-interest').floatVal(fixed);
    var desgravamen = divObj.find('input.sian-commercial-amounts-desgravamen').floatVal(fixed);
    var oae_adm = divObj.find('input.sian-commercial-amounts-oae_adm').floatVal(fixed);
    var oae_financial = divObj.find('input.sian-commercial-amounts-oae_financial').floatVal(fixed);
    var oae_sales = divObj.find('input.sian-commercial-amounts-oae_sales').floatVal(fixed);
    var porte = divObj.find('input.sian-commercial-amounts-porte').floatVal(fixed);
    var itf = divObj.find('input.sian-commercial-amounts-itf').floatVal(fixed);
    var other_expenses = divObj.find('input.sian-commercial-amounts-other_expenses').floatVal(fixed);
    //
    var crude = divObj.find('input.sian-commercial-amounts-crude').floatVal(fixed);
    var affected1 = divObj.find('input.sian-commercial-amounts-affected1').floatVal(fixed);
    var affected2 = divObj.find('input.sian-commercial-amounts-affected2').floatVal(fixed);
    var affected3 = divObj.find('input.sian-commercial-amounts-affected3').floatVal(fixed);
    var inaffected = divObj.find('input.sian-commercial-amounts-inaffected').floatVal(fixed);
    var nobill = divObj.find('input.sian-commercial-amounts-nobill').floatVal(fixed);
    var net = divObj.find('input.sian-commercial-amounts-net').floatVal(fixed);
    var igv_percent = divObj.find('select.sian-commercial-amounts-igv-percent  option:selected').text();
    var igv = divObj.find('input.sian-commercial-amounts-igv').floatVal(fixed);
    var icbp = divObj.find('input.sian-commercial-amounts-icbp').floatVal(fixed);
    var total = divObj.find('input.sian-commercial-amounts-total').floatVal(fixed);
    var perception = divObj.find('input.sian-commercial-amounts-perception').floatVal(fixed);
    var real = divObj.find('input.sian-commercial-amounts-real').floatVal(fixed);

    var gridTotals = {
        amortization: amortization,
        interest: interest,
        desgravamen: desgravamen,
        oae_adm: oae_adm,
        oae_financial: oae_financial,
        oae_sales: oae_sales,
        porte: porte,
        itf: itf,
        other_expenses: other_expenses,
        crude: crude,
        affected1: affected1,
        affected2: affected2,
        affected3: affected3,
        inaffected: inaffected,
        nobill: nobill,
        net: net,
        igv_percent: igv_percent,
        igv: igv,
        icbp: icbp,
        total: total,
        perception: perception,
        real: real
    };

    //Verificamos si ya cargo data 
    if (isset(operationChangeData) && (!isEquivalent(operationChangeData.operation_code, last_operation_code) || !isEquivalent(lastGridTotals, gridTotals)))
    {
        consoleLog('Entró a SIANCommercialAmountsUpdate y ya había operationChangeData');

        operationChangeData.cc_dependence = false;
        $.each(operationChangeData.combination_fields, function (index, field) {
            if (isset(gridTotals[field]) && gridTotals[field] > 0)
            {
                operationChangeData.cc_dependence = true;
            }
        });

        //Si tiene comodines
        if ((operationChangeData.independent_wildcards.length > 0 || operationChangeData.inherit_wildcards.length > 0)) {
            //Aqui se debe llamar al ajax para que devuelva la configuración de las cuentas de los comodines por productos.
            $.ajax({
                type: 'post',
                url: URL_ITEM_WILDCARD,
                data: {
                    target_movement_id: operationChangeData.target_movement_id,
                    warehouse_id: isset(warehouse_id) ? warehouse_id : null,
                    is_reversal: operationChangeData.is_reversal,
                    independent_mode: independent_mode,
                    inherit_mode: inherit_mode,
                    independent_wildcards: operationChangeData.independent_wildcards,
                    inherit_wildcards: operationChangeData.inherit_wildcards
                },
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },
                success: function (response) {

                    if (response.code === REST_CODE_SUCCESS)
                    {
                        $.each(response.data.wildcards, function (index, wildcard) {

                            var field = isset(operationChangeData.wildcard_association[wildcard]) ? operationChangeData.wildcard_association[wildcard] : null;

                            if (isset(field) && isset(gridTotals[field]) && gridTotals[field] > 0)
                            {
                                operationChangeData.cc_dependence = true;
                            }
                        });

                        //Actualizamos data
                        operationChangeData.combination_ids = operationChangeData.combination_ids.concat(response.data.combination_ids);

                        divObj.data('changeCCDependence')(operationChangeData);
                    } else
                    {
                        bootbox.alert(us_message(response.message, 'warning'));
                    }
                    window.active_ajax--;
                },
                error: function (request, status, error) { // if error occured
                    bootbox.alert(us_message(request.responseText, 'error'));
                    window.active_ajax--;
                },
                dataType: 'json'
            });
        } else
        {
            divObj.data('changeCCDependence')(operationChangeData);
        }
        //Debe estar dentro del IF
        divObj.data('last_operation_code', operationChangeData.operation_code);
        divObj.data('lastGridTotals', gridTotals);

    } else
    {
        consoleLog('Entró a SIANCommercialAmountsUpdate, pero aún no había operationChangeData');
    }
}

function SIANCommercialAmountsEnableOrDisable(div_id, ids, readonly)
{
    $.each(ids, function (i, id) {
        $('#' + id).readonly(readonly);
        if (readonly)
        {
            $('#' + id).val(0).change();
        }
    });
}

function SIANCommercialAmountsEnableOrDisableAll(div_id, as_bill)
{
    var divObj = $('#' + div_id);
    var is_commercial_route = divObj.data('is_commercial_route');
    var scope = divObj.data('scope');
    var igv_affection_map = divObj.data('igv_affection_map');
    var id_map = divObj.data('id_map');
    var items = getIGVAffectionItems(is_commercial_route, as_bill, scope, igv_affection_map, true, true, false);

    $.each(id_map, function (igv_affection, ids) {
        var readonly = !isset(items[igv_affection]);
        SIANCommercialAmountsEnableOrDisable(div_id, ids, readonly);
    });
}

function SIANCommercialAmountsFillIgv(select_id, table_id)
{
    var selectObj = $('#' + select_id);
    var tableObj = $('#' + table_id);
    var a_igv_percent = tableObj.data('igv_list');
    var igv_percent = tableObj.data('igv_percent');
    var default_igv = tableObj.data('global_igv');
    var found = false;

    selectObj.children('option').remove();

    $.each(a_igv_percent, function (key, value) {
        var optionObj = $('<option>').val(key).text(value);

        selectObj.append(optionObj)
        if (igv_percent == key)
        {
            found = true;
        }
    });

    if (found)
    {
        selectObj.val(igv_percent);
    } else {
        selectObj.val(default_igv);
    }
    //Seteamos last
    selectObj.change();
}

function SIANCommercialAmountsFillDetraction(select_id, table_id)
{
    var selectObj = $('#' + select_id);
    var tableObj = $('#' + table_id);
    var detraction_global = tableObj.data('detraction_global');
    var detraction_value = tableObj.data('detraction_value');

    var a_detraction_percent = [];
    if (detraction_global.indexOf(",") >= 0) {
        a_detraction_percent = detraction_global.split(",");
    } else {
        a_detraction_percent = [detraction_global];
    }
    var found = false;

    selectObj.children('option').remove();

    var optionObj = $('<option>').val(0)
            .text('(Seleccione una opción)');
    selectObj.append(optionObj);
    $.each(a_detraction_percent, function (index, item) {
        var optionObj = $('<option>').val(item * 100).text(item * 100);

        selectObj.append(optionObj)
        if (detraction_value == item * 100)
        {
            found = true;
        }
    });
    if (found)
    {
        selectObj.val(detraction_value);
    }
    //Seteamos last
    selectObj.change();
}

function SIANCommercialAmountsFillDetractionCode(select_id, table_id)
{
    var selectObj = $('#' + select_id);
    var tableObj = $('#' + table_id);
    var detraction_code = tableObj.data('detraction_code');
    var detraction_items = tableObj.data('detraction_items');
    var found = false;

    selectObj.children('option').remove();

    var optionObj = $('<option>').val('')
            .text('(Seleccione una opción)');
    selectObj.append(optionObj);
    $.each(detraction_items, function (index, item) {
        var optionObj = $('<option>').val(index).text(item);

        selectObj.append(optionObj)
        if (detraction_code == index)
        {
            found = true;
        }
    });

    if (found)
    {
        selectObj.val(detraction_code);
    }
    //Seteamos last
    selectObj.change();
}

function SIANCommercialAmountsEnableOrDisabledRetentionDetraction(div_id) {

    var divObj = $('#' + div_id);
    var is_buy = divObj.data('is_buy');
    var retention_allowed_document = divObj.data('retention_allowed_document');
    var detraction_allowed_document = divObj.data('detraction_allowed_document');
    var amount_allow_retention = divObj.data('amount_allow_retention');
    var amount_allow_detraction = divObj.data('amount_allow_detraction');
    //
    var retention_input_id = divObj.data('retention_input_id');
    var detraction_input_id = divObj.data('detraction_input_id');
    var retention_checked = $('#' + retention_input_id).is(':checked');
    var detraction_checked = $('#' + detraction_input_id).is(':checked');

    if (is_buy) {
        //retention
        if (retention_allowed_document && amount_allow_retention && !detraction_checked) {
            $('#' + retention_input_id).removeAttr('readonly');
        } else {
            $('#' + retention_input_id).attr('readonly', true);
        }
        //detraction
        if (detraction_allowed_document && amount_allow_detraction && !retention_checked) {
            $('#' + detraction_input_id).removeAttr('readonly');
        } else {
            $('#' + detraction_input_id).attr('readonly', true);
        }
    } else {
        $('#' + retention_input_id).attr('readonly', true);
        $('#' + detraction_input_id).attr('readonly', true);
    }
    SIANCommercialAmountsActivateRetention();
    SIANCommercialAmountsActivateDetraction();
}

function SIANCommercialAmountsCalculateRetentionAndDetraction(div_id)
{
    var divObj = $('#' + div_id);
    var fixed = divObj.data('fixed');
    var is_buy = divObj.data('is_buy');
    var is_sale = divObj.data('is_sale');
    var currency = divObj.data('currency');

    if (is_sale || is_buy)
    {
        var real = divObj.find('input.sian-commercial-amounts-real').floatVal(fixed);
        var retention_percent = divObj.find('input.sian-commercial-amounts-retention-percent').floatVal(2);
        var detraction_percent = divObj.find('select.sian-commercial-amounts-detraction-percent').floatVal(2);
        //
        var ret = USMath.round(real * (retention_percent / 100), fixed);
        var det = 0;

        if (currency == CURRENCY_PEN) {
            det = USMath.round(real * (detraction_percent / 100), 0);
        } else {
            det = USMath.round(real * (detraction_percent / 100), fixed);
        }
        divObj.find('input.sian-commercial-amounts-ret').floatVal(fixed, ret);
        divObj.find('input.sian-commercial-amounts-det').floatVal(fixed, det);

        SIANCommercialAmountsSetNoRetDet(div_id);
    }
}

function SIANCommercialAmountsSetNoRetDet(div_id) {

    var divObj = $('#' + div_id);
    var fixed = divObj.data('fixed');

    var real = divObj.find('input.sian-commercial-amounts-real').floatVal(fixed);
    var ret = divObj.find('input.sian-commercial-amounts-ret').floatVal(fixed, ret);
    var det = divObj.find('input.sian-commercial-amounts-det').floatVal(fixed, det);
    var no_ret = real - ret - det;
    divObj.find('input.sian-commercial-amounts-no-ret').floatVal(fixed, no_ret);
}