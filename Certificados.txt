-- GODADDY
https://pe.godaddy.com/help/solicitar-mi-certificado-ssl-y-aprender-a-instalarlo-si-eres-nuevo-para-ssl-comienza-aqui-32151?isc=sshlpe04
-- CERTIFICADOS GRATIS
https://www.sslforfree.com/certificates


-- URLS
https://gist.github.com/edgardo001/9acf2101f516593cac6110ce5858ab38
https://oracle-base.com/articles/linux/apache-tomcat-enable-https

-- Crear almacén de datos
keytool -genkey -alias tomcat -keyalg RSA -keystore <keystore.jks>


-- Crear P12 a partir del certificado
 openssl pkcs12 -export -in certificate.crt -inkey private.key -out certificate.p12
 keytool -importkeystore -srckeystore certificate.p12 -srcstoretype PKCS12 -destkeystore <keystore.jks> -deststoretype JKS

-- Para java
keytool -trustcacerts -keystore "/usr/lib/jvm/java-7-oracle/jre/lib/security/cacerts" -storepass changeit -importcert -alias <alias> -file /etc/tomcat8/cert/siansystem.com/certificate.crt
