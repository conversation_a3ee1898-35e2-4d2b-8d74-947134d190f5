<?php

class SIANMovementReferences extends CWidget {

    public $dataProvider;
    public $reference_routes = [];
    public $trace_mode = false;
    //PRIVATE
    private $id;
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = str_replace('_', '', $this->controller->getServerId());
        //
        $this->dataProvider->pagination = false;
        if (is_array($this->dataProvider->keyField) && count($this->dataProvider->keyField) == 1) {
            $this->dataProvider->keyField = $this->dataProvider->keyField[0];
        }
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if (!$this->trace_mode) {
            echo "<h4>Referencia en:</h4>";
        }
        //Accesos
        $access1 = $this->controller->checkRoute('/accounting/operation/preview');
        $access2 = $this->controller->checkActionRoutes(['view' => $this->reference_routes]);
        $access3 = $this->controller->checkRoute('/administration/person/preview');
        //GRID
        $columns = [];
        $counter = 0;

        array_push($columns, array(
            'class' => 'booster.widgets.TbRelationalColumn',
            'headerHtmlOptions' => array('style' => 'width:1%;'),
            'htmlOptions' => array('style' => 'width:1%;'),
            'header' => '',
            'url' => Yii::app()->createUrl("/movement/getReferences"),
            'cacheData' => false,
            'value' => function ($row) {
                if ($row->child_count > 0) {
                    return '+';
                } else {
                    return '';
                }
            },
            'labelOpen' => '+',
            'labelClose' => '-',
        ));

        array_push($columns, array(
            'header' => $this->trace_mode ? '' : 'Item',
            'headerHtmlOptions' => array('style' => 'text-align:center;width:4%;'),
            'htmlOptions' => array('style' => 'text-align:center;width:4%;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) use (&$counter) {
                return ++$counter;
            },
        ));

        array_push($columns, array(
            'header' => $this->trace_mode ? '' : 'Operación',
            'headerHtmlOptions' => array('style' => 'width:15%;'),
            'htmlOptions' => array('style' => 'width:15%;'),
            'name' => 'operation_code',
            'type' => 'raw',
            'value' => function ($row) use ($access1) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                    'route' => "/accounting/operation/preview",
                    'label' => $row->operation_name,
                    'title' => 'Ver operación',
                    'class' => 'form',
                    'data' => array(
                        'id' => $row->operation_code
                    ),
                    'visible' => $access1,
                        ), true);
            },
        ));

        array_push($columns, array(
            'header' => $this->trace_mode ? '' : 'Documento',
            'headerHtmlOptions' => array('style' => 'width:22%;'),
            'htmlOptions' => array('style' => 'width:22%;'),
            'name' => 'document',
            'type' => 'raw',
            'value' => function ($row) use ($access2) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                    'route' => "/{$row->route}/view",
                    'label' => $row->document,
                    'title' => 'Ver documento',
                    'params' => array(
                        'id' => $row->movement_id
                    ),
                    'target' => $row->movement_id,
                    'visible' => $access2["/{$row->route}/view"],
                        ), true) . (isset($row->extra_movement_id) ? ' ' . Strings::MANUAL_MOVEMENT_LINK : '');
            },
        ));

        array_push($columns, array(
            'header' => $this->trace_mode ? '' : 'Socio de Negocio',
            'headerHtmlOptions' => array('style' => 'width:29%;'),
            'htmlOptions' => array('style' => 'width:29%;'),
            'name' => 'aux_person_id',
            'type' => 'raw',
            'value' => function ($row) use ($access3) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                    'route' => "/administration/person/preview",
                    'label' => $row->aux_person_name,
                    'title' => 'Ver socio de negocio',
                    'class' => 'form',
                    'data' => array(
                        'id' => $row->aux_person_id
                    ),
                    'visible' => $access3,
                        ), true);
            },
        ));

        array_push($columns, array(
            'header' => $this->trace_mode ? '' : 'F.Emisión',
            'headerHtmlOptions' => array('style' => 'width:7%;'),
            'htmlOptions' => array('style' => 'width:7%;'),
            'name' => 'emission_date',
            'type' => 'raw'
        ));

        array_push($columns, array(
            'header' => $this->trace_mode ? '' : 'Usuario',
            'headerHtmlOptions' => array('style' => 'width:22%;'),
            'htmlOptions' => array('style' => 'width:22%;'),
            'name' => 'person_id',
            'type' => 'raw',
            'value' => function ($row) use ($access3) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                    'route' => "/administration/person/preview",
                    'label' => $row->person_name,
                    'title' => 'Ver socio de negocio',
                    'class' => 'form',
                    'data' => array(
                        'id' => $row->person_id
                    ),
                    'visible' => $access3,
                        ), true);
            },
        ));

        $gridParams = array(
            'id' => $this->id,
            'type' => 'hover condensed',
            'dataProvider' => $this->dataProvider,
            'enableSorting' => true,
            'selectableRows' => 0,
            'columns' => $columns,
            'template' => '{items}',
            'nullDisplay' => Strings::NONE,
            'showHeader' => $this->trace_mode ? false : true,
        );
        $this->widget('application.widgets.USExtendedGridView', $gridParams);
    }

}
