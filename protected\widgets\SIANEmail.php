<?php

class SIANEmail extends CWidget {

    public $id;
    public $model;
    public $attribute;
    public $items = [];
    public $types = [];
    public $advanced = 1;
    public $title = 'Emails';
    public $hint = false;
    public $class = '';
    public $class_panel = '';
    public $name_preffix = 'Email';
    public $data = [];
    public $disabled = false;
    public $limit = 10;
    //PRIVATE
    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;
        //Verificamos si hay tipos
        if (count($this->types) == 0) {
            throw new Exception('Debe especificar los tipos de correo');
        }

        //Si hay model seteamos items
        if (isset($this->model, $this->attribute)) {
            $this->items = $this->model->{$this->attribute};
        }
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        $a_emailItems = [];
        foreach ($this->items as $item) {
            $a_attributes = [];
            $a_attributes['email_address'] = isset($item->email_address) ? $item->email_address : '';
            $a_attributes['email_type'] = isset($item->email_type) ? $item->email_type : '';
            $a_attributes['errors'] = $item->getAllErrors();

            $a_emailItems[] = $a_attributes;
        }

        //Registramos script
        SIANAssets::registerScriptFile('js/sian-email.js');

        Yii::app()->clientScript->registerScript($this->id, "

        //COUNT
        var divObj = $('#{$this->id}');
        divObj.data('count', 0);
        divObj.data('advanced', {$this->advanced});
        divObj.data('name_preffix', '{$this->name_preffix}');
        divObj.data('disabled', " . CJSON::encode($this->disabled) . ");
        divObj.data('limit', " . CJSON::encode($this->limit) . ");
        divObj.data('email_type_options', " . CJSON::encode($this->types) . ");
        {$this->_renderData('divObj')}
            
        var array = " . CJSON::encode($a_emailItems) . ";

        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANEmailAddItem('{$this->id}', array[i]['email_address'], array[i]['email_type'], array[i]['errors']);
            }
        }
        else
        {
            $('#{$this->id}').html('<p>" . Strings::NO_DATA . "</p>');
        }
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->title . ($this->hint ? '*' : ''),
            'headerIcon' => 'envelope',
            'htmlOptions' => array(
                'class' => $this->class_panel . ' ' . ( isset($this->model, $this->attribute) && $this->model->hasErrors($this->attribute) ? 'us-error' : '')
            )
        ));

        echo "<div id='{$this->id}' class='{$this->class}'></div>";
        echo CHtml::link("<span class='fa fa-plus fa-lg black'></span> Agregar nuevo", null, array(
            'onclick' => "SIANEmailAddItem('{$this->id}', '', '{$this->_getFirstType()}', [])",
            'title' => 'Agregar'
        ));

        if ($this->hint) {
            echo "<hr>";
            echo "<p>(*) {$this->hint}</p>";
        }
        $this->endWidget();
    }

    private function _renderData($p_s_name) {
        $a_script = [];
        foreach ($this->data as $s_key => $m_value) {
            $a_script[] = $p_s_name . ".data('{$s_key}', " . CJSON::encode($m_value) . ");";
        }

        return implode("\n", $a_script);
    }

    private function _getFirstType() {
        return array_keys($this->types)[0];
    }

}
