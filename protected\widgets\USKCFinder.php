<?php

class USKCFinder extends CWidget {

    public $id;
    public $form;
    public $model;
    public $attribute;
    public $label;
    public $name;
    public $value = null;
    public $readonly = true;
    public $parent_id = null;
    public $buttons = array(
        'files',
        'images',
        'snapshot',
        'flash'
    );
    public $cms = 'default';
    public $placeholder = 'Presione un botón de la derecha para explorar';
    public $remove_button_id;
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->remove_button_id = isset($this->remove_button_id) ? $this->remove_button_id : $this->controller->getServerId();

        SIANAssets::registerScriptFile("other/cam/js/webcam.js");
        SIANAssets::registerScriptFile("js/us-kcfinder.js");

        Yii::app()->clientScript->registerScript($this->id . '_scripts', "
            
        $(document).ready(function() {  
            var inputObj = $('#{$this->id}');
            inputObj.data('parent_id', " . CJSON::encode($this->parent_id) . ");
            inputObj.data('media_url', '" . Yii::app()->params ['media_url'] . "');
        });
    	", CClientScript::POS_HEAD);
    }

    /**
     * Runs the widget.
     */
    public function run() {
        if (isset($this->model, $this->attribute)) {
            echo $this->form->textFieldRow($this->model, $this->attribute, array(
                'id' => $this->id,
                'readonly' => $this->readonly,
                'placeholder' => 'Presione un botón de la derecha para explorar',
                'append' => $this->renderLinks(),
                'label' => $this->label,
            ));
        } else {
            echo "<div class='form-group'>";
            echo CHtml::label($this->label, $this->id);
            echo "<div class='input-group'>";
            echo CHtml::textField($this->name, $this->value, array(
                'id' => $this->id,
                'readonly' => $this->readonly,
                'placeholder' => 'Presione un botón de la derecha para explorar',
                'class' => 'form-control',
            ));
            echo "<span class='input-group-addon'>";
            echo $this->renderLinks();
            echo "</span>";
            echo "</div>";
            echo "</div>";
        }
    }

    private function renderLinks() {
        $links = [];

        if (in_array('images', $this->buttons) || in_array('snapshot', $this->buttons)) {

            $links[] = $this->controller->widget('application.widgets.USLink', array(
                'id' => $this->id . '_preview',
                'icon' => 'fa fa-lg fa-eye',
                'title' => 'Visualizar',
                'onclick' => "USKCFinderPreview('{$this->id}')",
                'visible' => !USString::isBlank($this->value),
                    ), true);
        }


        if (in_array('files', $this->buttons)) {
            $links[] = $this->controller->widget('application.widgets.USLink', array(
                'icon' => 'fa fa-lg fa-file',
                'title' => 'Archivos',
                'onclick' => "USKCFinderOpen('{$this->id}', 'files', '{$this->cms}')"
                    ), true);
        }

        if (in_array('images', $this->buttons)) {

            $links[] = $this->controller->widget('application.widgets.USLink', array(
                'icon' => 'fa fa-lg fa-picture-o',
                'title' => 'Imágenes',
                'onclick' => "USKCFinderOpen('{$this->id}', 'images', '{$this->cms}')"
                    ), true);
        }

        if (in_array('snapshot', $this->buttons)) {

            $links[] = $this->controller->widget('application.widgets.USLink', array(
                'icon' => 'fa fa-lg fa-camera',
                'title' => 'Tomar foto',
                'class' => 'form',
                'url' => $this->controller->createUrl('/common/snapshot'),
                'data' => array(
                    'id' => $this->id,
                    'modal_id' => $this->controller->getServerId(),
                    'parent_id' => $this->parent_id,
                    'type' => 'field',
                    'element_id' => $this->id,
                    'cms' => $this->cms,
                ),
                    ), true);
        }

        if (in_array('flash', $this->buttons)) {

            $links[] = $this->controller->widget('application.widgets.USLink', array(
                'icon' => 'fa fa-lg fa-film',
                'title' => 'Flash',
                'onclick' => "USKCFinderOpen('{$this->id}', 'flash', '{$this->cms}')"
                    ), true);
        }

        $links[] = $this->controller->widget('application.widgets.USLink', array(
            'id' => $this->remove_button_id,
            'icon' => 'fa fa-lg fa-times',
            'title' => 'Limpiar',
            'onclick' => "USKCFinderClean('{$this->id}')"
                ), true);

        return implode('&nbsp', $links);
    }

}
