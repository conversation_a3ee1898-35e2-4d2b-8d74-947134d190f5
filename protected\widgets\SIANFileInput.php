<?php

class SIANFileInput extends CWidget {

    //CONST
    public $id;
    public $input_id;
    public $loader_id;
    public $errors_div_id;
    public $confirm_button_id;
    public $name;
    public $accept = "*";
    public $readonly = false;
    public $onUpload = '';
    public $onConvert = '';

    public $url_api_process_data = '';

    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        $this->input_id = isset($this->input_id) ? $this->input_id : $this->controller->getServerId();
        $this->loader_id = isset($this->loader_id) ? $this->loader_id : $this->controller->getServerId();
        $this->errors_div_id = isset($this->errors_div_id) ? $this->errors_div_id : $this->controller->getServerId();

        $this->registerStyles();
        Yii::app()->clientScript->registerScript($this->controller->getServerId(),"
            $(document).ready(function() {
                $('#{$this->confirm_button_id}').click(function(){
                    var errorsDiv = $('#{$this->errors_div_id}');
                    if (errorsDiv.length) {
                        errorsDiv.css('display', 'none');
                    }
            
                    var file = $('#{$this->input_id}')[0].files[0];
                
                    if(file){
                        var fileName = file.name;
                        var fileExtension = fileName.split('.').pop().toLowerCase();
            
                        if (fileExtension === 'xls' || fileExtension === 'xlsx') {
                            
                            $('#{$this->loader_id}').css({
                                'display': 'block'
                            });
            
                            var formData = new FormData();
                            formData.append('file', file);
            
                            $.ajax({
                                url: '{$this->url_api_process_data}',
                                type: 'POST',
                                data: formData,
                                processData: false,
                                contentType: false,
                                success: function(response) {
                                    window.active_ajax--;
                                    {$this->onConvert}
                                },
                                beforeSend: function(xhr) {
                                    window.active_ajax++;
                                    $('div.ui-tooltip').remove();
                                },
                                error: function(xhr, status, error) {
                                    alert('No se pudo procesar el archivo');
                                    $('#{$this->loader_id}').css({                   
                                        'display': 'none'
                                    });
                                    window.active_ajax--;
                                }
                            });
            
                        }
            
                        $('#{$this->input_id}').val('');
                    }else {
                        alert('No se seleccionó ningún archivo.');
                    }
                })
            });
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Archivo',
            'headerIcon' => 'file',
            'htmlOptions' => [
                'id' => $this->id
            ]
        ));
        echo "<input id='{$this->input_id}' type='file' accept='{$this->accept}' " . " style='width: 100%; border: 1px solid #ff6b00; padding: 0.5rem; border-radius: 0.5rem;' >";
        echo "<div style = 'display:flex; justify-content:center; width : 100%'>";
        echo "<div id='{$this->loader_id}' style='display:none; padding: 2rem;'>";
        echo "<svg viewBox='0 0 400 400' xmlns='http://www.w3.org/2000/svg' style='width: 8rem;' >";
        echo "<circle class='spin' cx='200' cy='200' fill='none' r='104' stroke-width='16' stroke='#E387FF' stroke-dasharray='417.5 700' stroke-linecap='round' />";
        echo "</svg>";
        echo "<h4>Cargando...</h4>";
        echo "</div>";
        echo "</div>";
        echo "<div id='{$this->errors_div_id}'>";
        echo "</div>";

        $this->endWidget();
    }

    private function registerStyles() {
        Yii::app()->clientScript->registerCss($this->controller->getServerId(), "
            input[type=file]::file-selector-button {
                padding: 0.8rem;
                color: white;
                background-color: #ff6b00;
                border: 1px solid; /* Fixed the border width */
                border-radius: 0.5rem;
            }
        
            @keyframes spin {
                to {
                    transform: rotate(360deg);
                }
            }
          
            .spin {
                transform-origin: center;
                animation: spin 2s linear infinite;
            }
        ");
    }

}
