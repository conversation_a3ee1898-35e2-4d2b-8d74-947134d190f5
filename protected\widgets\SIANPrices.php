<?php

class SIANPrices extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id = null;
    public $movement;
    public $tfixed = 2;
    public $ifixed = 4;
    public $currency_items = [];
    public $checked_items = [];
    public $items = [];
    public $price_presentations = [];
    //PRIVATE
    private $controller;
    private $tstep;
    private $currency;
    private $exchange_rate;
    private $dataProvider;
    private $currency_input_id;
    private $additional_cost_id;
    private $additional_cost_pen_id;
    private $additional_cost_usd_id;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->currency_input_id = $this->controller->getServerId();
        $this->additional_cost_id = $this->controller->getServerId();
        $this->additional_cost_pen_id = $this->controller->getServerId();
        $this->additional_cost_usd_id = $this->controller->getServerId();
        //variable global
        $this->currency = $this->controller->getOrganization()->globalVar->display_currency;
        $this->exchange_rate = Yii::app()->controller->getLastExchange();
        //VAR
        $this->tstep = USMath::toStep($this->tfixed);

        //CHECKED ITEMS
        foreach ($this->model->tempPresentations as $o_item) {

            if (USString::isBlank($o_item->product_id) || USString::isBlank($o_item->equivalence)) {
                throw new Exception('No está seteado el campo el product_id  o  equivalence para uno o más ítems que deben ir en la grilla.');
            }

            $this->checked_items[$o_item->product_id . "-" . round($o_item->equivalence * 100, 0)]['Presentation'] = $o_item;
        }

        //CHECKED ITEMS
        foreach ($this->model->tempPrices as $o_itemPrice) {

            if (USString::isBlank($o_item->product_id) || USString::isBlank($o_item->equivalence)) {
                throw new Exception('No está seteado el campo el product_id  o  equivalence para uno o más ítems que deben ir en la grilla.');
            }

            $this->checked_items[$o_item->product_id . "-" . round($o_item->equivalence * 100, 0)]['Price'] = $o_itemPrice;
        }

        foreach ($this->items as $spItem) {

            if (isset($this->checked_items[$spItem->pk]) && isset($this->price_presentations[$spItem->pk])) {
                $spItem->checked = true;
                $spItem->mprice = $this->price_presentations[$spItem->pk]['mprice'];
                $spItem->imprice = $this->price_presentations[$spItem->pk]['imprice'];
                $spItem->aprice = $this->price_presentations[$spItem->pk]['aprice'];
                $spItem->iaprice = $this->price_presentations[$spItem->pk]['iaprice'];
                $spItem->wprice = $this->price_presentations[$spItem->pk]['wprice'];
                $spItem->iwprice = $this->price_presentations[$spItem->pk]['iwprice'];
                $spItem->cost = $this->checked_items[$spItem->pk]['Presentation']->cost;
                $spItem->ref_cost = $this->checked_items[$spItem->pk]['Presentation']->ref_cost;
                $spItem->iref_cost = $this->checked_items[$spItem->pk]['Presentation']->iref_cost;

//                if ($this->currency == Currency::PEN) {
//
//                    if ($this->checked_items[$spItem->pk]->hasErrors('mprice_pen')) {
//                        $spItem->addError('mprice', $this->checked_items[$spItem->pk]->getError('mprice_pen'));
//                    }
//                    if ($this->checked_items[$spItem->pk]->hasErrors('imprice_pen')) {
//                        $spItem->addError('imprice', $this->checked_items[$spItem->pk]->getError('imprice_pen'));
//                    }
//                    if ($this->checked_items[$spItem->pk]->hasErrors('aprice_pen')) {
//                        $spItem->addError('aprice', $this->checked_items[$spItem->pk]->getError('aprice_pen'));
//                    }
//                    if ($this->checked_items[$spItem->pk]->hasErrors('iaprice_pen')) {
//                        $spItem->addError('iaprice', $this->checked_items[$spItem->pk]->getError('iaprice_pen'));
//                    }
//                    if ($this->checked_items[$spItem->pk]->hasErrors('wprice_pen')) {
//                        $spItem->addError('wprice', $this->checked_items[$spItem->pk]->getError('wprice_pen'));
//                    }
//                    if ($this->checked_items[$spItem->pk]->hasErrors('iwprice_pen')) {
//                        $spItem->addError('iwprice', $this->checked_items[$spItem->pk]->getError('iwprice_pen'));
//                    }
//                } else {
//                    if ($this->checked_items[$spItem->pk]->hasErrors('mprice_usd')) {
//                        $spItem->addError('mprice', $this->checked_items[$spItem->pk]->getError('mprice_usd'));
//                    }
//                    if ($this->checked_items[$spItem->pk]->hasErrors('imprice_usd')) {
//                        $spItem->addError('imprice', $this->checked_items[$spItem->pk]->getError('imprice_usd'));
//                    }
//                    if ($this->checked_items[$spItem->pk]->hasErrors('aprice_usd')) {
//                        $spItem->addError('aprice', $this->checked_items[$spItem->pk]->getError('aprice_usd'));
//                    }
//                    if ($this->checked_items[$spItem->pk]->hasErrors('iaprice_usd')) {
//                        $spItem->addError('iaprice', $this->checked_items[$spItem->pk]->getError('iaprice_usd'));
//                    }
//                    if ($this->checked_items[$spItem->pk]->hasErrors('wprice_usd')) {
//                        $spItem->addError('wprice', $this->checked_items[$spItem->pk]->getError('wprice_usd'));
//                    }
//                    if ($this->checked_items[$spItem->pk]->hasErrors('iwprice_usd')) {
//                        $spItem->addError('iwprice', $this->checked_items[$spItem->pk]->getError('iwprice_usd'));
//                    }
//                }

                foreach ($this->model->tempSublines as $o_item_subline) {

                    if ($o_item_subline->referential == $spItem->pk) {
                        $spItem->mmargin = $o_item_subline->min_margin;
                        $spItem->mmargin_label = round($o_item_subline->min_margin * 100, 2);
                        $spItem->amargin = $o_item_subline->avg_margin;
                        $spItem->amargin_label = round($o_item_subline->avg_margin * 100, 2);
                        $spItem->wmargin = $o_item_subline->web_margin;
                        $spItem->wmargin_label = round($o_item_subline->web_margin * 100, 2);
                        if ($o_item_subline->hasErrors('min_margin')) {
                            $spItem->addError('mmargin_label', $o_item_subline->getError('min_margin'));
                        }
                        if ($o_item_subline->hasErrors('avg_margin')) {
                            $spItem->addError('amargin_label', $o_item_subline->getError('avg_margin'));
                        }
                        if ($o_item_subline->hasErrors('web_margin')) {
                            $spItem->addError('wmargin_label', $o_item_subline->getError('web_margin'));
                        }
                    }
                }
            } else {
                $spItem->checked = false;
            }
        }



        $this->dataProvider = new USArrayDataProvider($this->items, array(
            'keyField' => ['pk'], // PRIMARY KEY
            'sort' => false,
        ));
        $this->dataProvider->pagination = false;

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-prices.js');

        //Registramos scripts
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        $(document).ready(function() {
        
            $('#{$this->id}').data('tfixed', '{$this->tfixed}');
            $('#{$this->id}').data('ifixed', '{$this->ifixed}');
            $('#{$this->id}').data('igv', '{$this->controller->getOrganization()->globalVar->igv}');
                
            " . ( count($this->model->tempPresentations) > 0 ? "SIANPricesUpdateRefcost" : "SIANPricesUpdatePrices" ) .
                "('{$this->id}', " . ($this->currency == Currency::PEN ? $this->movement->additional_cost_pen : $this->movement->additional_cost_usd ) . ");
        
            $('#{$this->additional_cost_pen_id}').change(function(){" .
                ($this->currency == Currency::PEN ? "SIANPricesUpdatePrices('{$this->id}', $(this).val());" : "") . "
            })
            
            $('#{$this->additional_cost_usd_id}').change(function(){" .
                ($this->currency == Currency::USD ? "SIANPricesUpdatePrices('{$this->id}', $(this).val());" : "") . "
            })
        });        
       
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Costo Adicional Referencial',
            'headerIcon' => 'list',
            'htmlOptions' => array(
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->movement, 'currency_additional_cost', $this->currency_items, [
            'id' => $this->currency_input_id,
            'onchange' => "var currency = $(this).val();
                           if(currency == '" . Currency::PEN . "'){
                                $('#{$this->additional_cost_pen_id}').val($('#{$this->additional_cost_id}').val()).trigger('change');
                                $('#{$this->additional_cost_usd_id}').val(USMath.round($('#{$this->additional_cost_id}').val() / {$this->exchange_rate}, {$this->tfixed})).trigger('change');
                           }else{
                                $('#{$this->additional_cost_pen_id}').val(USMath.round($('#{$this->additional_cost_id}').val() * {$this->exchange_rate}, {$this->tfixed})).trigger('change');
                                $('#{$this->additional_cost_usd_id}').val($('#{$this->additional_cost_id}').val()).trigger('change');
                           }
                "
        ]);
        echo '</div>';
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldNonActive('Importe neto <span class="sian-prices-currency"></span>:', 'currency_additional_cost', $this->currency == Currency::PEN ? $this->movement->additional_cost_pen : $this->movement->additional_cost_usd, [
            'id' => $this->additional_cost_id,
            'style' => 'text-align:right',
            'class' => 'us-double2',
            'min' => 0,
            'step' => 1,
            'onchange' => "var currency = $('#{$this->currency_input_id}').val();
                           if(currency == '" . Currency::PEN . "'){
                                $('#{$this->additional_cost_pen_id}').val($(this).val()).trigger('change');
                                $('#{$this->additional_cost_usd_id}').val(USMath.round($(this).val() / {$this->exchange_rate}, {$this->tfixed})).trigger('change');
                           }else{
                                $('#{$this->additional_cost_pen_id}').val(USMath.round($(this).val() * {$this->exchange_rate}, {$this->tfixed})).trigger('change');
                                $('#{$this->additional_cost_usd_id}').val($(this).val()).trigger('change');
                           }
                "
        ]);
        echo $this->form->hiddenField($this->movement, 'additional_cost_pen', [
            'id' => $this->additional_cost_pen_id
        ]);
        echo $this->form->hiddenField($this->movement, 'additional_cost_usd', [
            'id' => $this->additional_cost_usd_id,
        ]);
        echo '</div>';
        echo '</div>';

        $this->endWidget();

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Precios por productos',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => 'sian-prices ' . ($this->model->hasErrors('tempPresentations') ? 'us-error' : '')
            )
        ));
        echo 'Todos los montos <b>incluyen IGV</b> y están en <b>' . Currency::getName($this->currency) . ".</b>";

        $this->_renderItems();
        echo '<br>';
        echo '<span style="color:red;">**</span> Precios calculados a partir del costo y los márgenes de sublínea.';

        $this->endWidget();
    }

    private function _renderItems() {

        $form = $this->form;

        $columns = [];

        array_push($columns, array(
            'header' => CHtml::checkBox("All", true, array(
                'class' => 'sian-prices-check',
                'onchange' => 'SIANPricesCheck($(this));',
            )),
            'headerHtmlOptions' => array('style' => 'width:3%; text-align:center;'),
            'type' => 'raw',
            'htmlOptions' => array('style' => 'width:3%; text-align:center'),
            'value' => function ($row) {
                return CHtml::checkBox("Checked[{$row->pk}]", $row->checked, array(
                    'class' => 'sian-prices-item-check',
                    'onchange' => 'SIANPricesRowCheck($(this));',
                ));
            }
        ));

        array_push($columns, array(
            'header' => 'ID',
            'headerHtmlOptions' => array('style' => 'width:3%;'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = "<span class='sian-prices-item-product-id'>{$row->product_id}</span>";
                return $html;
            },
        ));

        array_push($columns, array(
            'header' => 'Nombre',
            'headerHtmlOptions' => array('style' => 'width:15%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = "<span class='sian-prices-item-product-name'>{$row->product_name}</span>";
                return $html;
            },
        ));

        array_push($columns, array(
            'header' => 'Pres.',
            'headerHtmlOptions' => array('style' => 'width:3%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = "<span class='sian-prices-item-measure-name'>{$row->measure_name}</span>";
                return $html;
            },
            'htmlOptions' => array('style' => 'text-align:center;'),
        ));

        array_push($columns, array('header' => 'Hidden',
            'headerHtmlOptions' => array('style' => 'display:none'),
            'htmlOptions' => array('style' => 'display:none'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) {

                $html = CHtml::hiddenField("PresentationStore[{$row->pk}][price_id]", $row->price_id, [
                            'class' => "sian-prices-item-price-id",
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("PresentationStore[{$row->pk}][product_id]", $row->product_id, [
                            'class' => "sian-prices-item-product-id",
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("PresentationStore[{$row->pk}][equivalence]", $row->equivalence, [
                            'class' => "sian-prices-item-equivalence",
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Presentation[{$row->pk}][product_id]", $row->product_id, [
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Presentation[{$row->pk}][equivalence]", $row->equivalence, [
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Presentation[{$row->pk}][proportion]", $row->proportion, [
                            'class' => "sian-prices-item-proportion",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Subline[{$row->pk}][subline_id]", $row->subline_id, [
                            'class' => "sian-prices-item-subline-subline_id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Subline[{$row->pk}][pk]", $row->pk, [
                            'class' => "sian-prices-item-subline-subline_id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Subline[{$row->pk}][mmargin]", $row->mmargin, [
                            'class' => "sian-prices-item-mmargin",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Subline[{$row->pk}][amargin]", $row->amargin, [
                            'class' => "sian-prices-item-amargin",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Subline[{$row->pk}][wmargin]", $row->wmargin, [
                            'class' => "sian-prices-item-wmargin",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Presentation[{$row->pk}][cost]", $row->cost, [
                            'class' => "sian-prices-item-cost",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Presentation[{$row->pk}][ref_cost]", $row->ref_cost, [
                            'class' => "sian-prices-item-ref_cost",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Presentation[{$row->pk}][iref_cost]", $row->iref_cost, [
                            'class' => "sian-prices-item-iref_cost",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);

                $html .= CHtml::hiddenField("Presentation[{$row->pk}][currency]", $this->currency, [
                            'class' => "sian-prices-item-currency",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);
                return $html;
            },
        ));

        array_push($columns, array(
            'header' => 'Costo',
            'headerHtmlOptions' => array('style' => 'width:7%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = "<span class='sian-prices-item-iref_cost-label'>{$row->iref_cost}</span>";
                return $html;
            },
            'htmlOptions' => array('style' => 'text-align:right;'),
        ));

        // ---------- Precio Mínimo --------------

        array_push($columns, array(
            'header' => 'Mín. %',
            'headerHtmlOptions' => array('style' => 'width:7%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->numberFieldRow($row, "mmargin_label", array(
                    'label' => false,
                    'name' => "Presentation[{$row->pk}][mmargin_label]",
                    'class' => "sian-prices-item-mmargin-label us-integer",
                    'min' => 0,
                    'max' => 100,
                    'step' => 1,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right',
                    'onchange' => " SIANPricesChangeMargin($(this), 'mmargin');
                                            var item = $(this).closest('tr');
                                            SIANPricesUpdateItem('{$this->id}', item, 'mprice');"
                ));
            },
            'htmlOptions' => array('style' => 'text-align:right; border-left: 1px solid #A9A9A9;'),
        ));

        array_push($columns, array(
            'header' => 'P. Mín.',
            'headerHtmlOptions' => array('style' => 'width:5%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = "<span class='sian-prices-item-imprice-old'>{$row->imprice_old}</span>";
                return $html;
            },
            'htmlOptions' => array('style' => 'text-align:right'),
        ));

        array_push($columns, array('header' => 'Hidden',
            'headerHtmlOptions' => array('style' => 'display:none'),
            'htmlOptions' => array('style' => 'display:none'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) {

                $html = CHtml::hiddenField("PresentationStore[{$row->pk}][mprice]", $row->mprice, [
                            'class' => "sian-prices-item-mprice",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);
                return $html;
            },
        ));

        array_push($columns, array(
            'header' => 'P. Mín. <span style="color:red;">**</span>',
            'headerHtmlOptions' => array('style' => 'width:10%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->numberFieldRow($row, "imprice", array(
                    'label' => false,
                    'name' => "PresentationStore[{$row->pk}][imprice]",
                    'class' => "sian-prices-item-imprice us-double2",
                    'min' => 0.00,
                    'step' => 0.01,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right',
                    'onchange' => " SIANPricesChangePrice($(this), 'imprice');"
                ));
            }
        ));

        // ---------- Precio Regular o Promedio--------------
        array_push($columns, array(
            'header' => 'Pro. %',
            'headerHtmlOptions' => array('style' => 'width:7%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->numberFieldRow($row, "amargin_label", array(
                    'label' => false,
                    'name' => "Presentation[{$row->pk}][amargin_label]",
                    'class' => "sian-prices-item-amargin-label us-integer",
                    'min' => 0,
                    'max' => 100,
                    'step' => 1,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right',
                    'onchange' => " SIANPricesChangeMargin($(this), 'amargin');
                                            var item = $(this).closest('tr');
                                            SIANPricesUpdateItem('{$this->id}', item, 'aprice');"
                ));
            },
            'htmlOptions' => array('style' => 'text-align:right; border-left: 1px solid #A9A9A9;'),
        ));

        array_push($columns, array(
            'header' => 'P. Pro.',
            'headerHtmlOptions' => array('style' => 'width:5%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = "<span class='sian-prices-item-iaprice-old'>{$row->iaprice_old}</span>";
                return $html;
            },
            'htmlOptions' => array('style' => 'text-align:right'),
        ));

        array_push($columns, array('header' => 'Hidden',
            'headerHtmlOptions' => array('style' => 'display:none'),
            'htmlOptions' => array('style' => 'display:none'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) {

                $html = CHtml::hiddenField("PresentationStore[{$row->pk}][aprice]", $row->aprice, [
                            'class' => "sian-prices-item-aprice",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);
                return $html;
            },
        ));

        array_push($columns, array(
            'header' => 'P. Pro. <span style="color:red;">**</span>',
            'headerHtmlOptions' => array('style' => 'width:10%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->numberFieldRow($row, "iaprice", array(
                    'label' => false,
                    'name' => "PresentationStore[{$row->pk}][iaprice]",
                    'class' => "sian-prices-item-iaprice us-double2",
                    'min' => 0.00,
                    'step' => 0.01,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right',
                    'onchange' => " SIANPricesChangePrice($(this), 'iaprice');"
                ));
            }
        ));

        // ---------- Precio Web --------------

        array_push($columns, array(
            'header' => 'Web %',
            'headerHtmlOptions' => array('style' => 'width:7%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->numberFieldRow($row, "wmargin_label", array(
                    'label' => false,
                    'name' => "Presentation[{$row->pk}][wmargin_label]",
                    'class' => "sian-prices-item-wmargin-label us-integer",
                    'min' => 0,
                    'max' => 100,
                    'step' => 1,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right',
                    'onchange' => " SIANPricesChangeMargin($(this), 'wmargin');
                                            var item = $(this).closest('tr');
                                            SIANPricesUpdateItem('{$this->id}', item, 'wprice');"
                ));
            },
            'htmlOptions' => array('style' => 'text-align:right; border-left: 1px solid #A9A9A9;'),
        ));

        array_push($columns, array(
            'header' => 'P. Web.',
            'headerHtmlOptions' => array('style' => 'width:5%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = "<span class='sian-prices-item-iwprice-old'>{$row->iwprice_old}</span>";
                return $html;
            },
            'htmlOptions' => array('style' => 'text-align:right'),
        ));

        array_push($columns, array('header' => 'Hidden',
            'headerHtmlOptions' => array('style' => 'display:none'),
            'htmlOptions' => array('style' => 'display:none'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) {

                $html = CHtml::hiddenField("PresentationStore[{$row->pk}][wprice]", $row->wprice, [
                            'class' => "sian-prices-item-wprice",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ]);
                return $html;
            },
        ));

        array_push($columns, array(
            'header' => 'P. Web <span style="color:red;">**</span>',
            'headerHtmlOptions' => array('style' => 'width:10%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->numberFieldRow($row, "iwprice", array(
                    'label' => false,
                    'name' => "PresentationStore[{$row->pk}][iwprice]",
                    'class' => "sian-prices-item-iwprice us-double2",
                    'min' => 0.00,
                    'step' => 0.01,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right',
                    'onchange' => "SIANPricesChangePrice($(this), 'iwprice');"
                ));
            }
        ));

        array_push($columns, array(
            'header' => '',
            'headerHtmlOptions' => array('style' => 'width:10%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) {
                return $this->form->widget('application.widgets.USLink', array(
                    'label' => "<span class='fa fa-lg fa-retweet black'></span> ",
                    'title' => 'Calcular',
                    'class' => 'sian-prices-item-calc',
                    'onclick' => " var item = $(this).closest('tr');
                                            SIANPricesUpdateItem('{$this->id}', item, '');",
                        ), true);
            }
        ));

        array_push($columns, array(
            'header' => '',
            'headerHtmlOptions' => array('style' => 'width:10%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) {
                return $this->form->widget('application.widgets.USLink', array(
                    'label' => "<span class='fa fa-lg fa-eye black'></span> ",
                    'title' => 'Ver Costos',
                    'route' => '/logistic/merchandise/preview',
                    'class' => 'form sian-prices-item-previe',
                    'visible' => true,
                    'data' => array(
                        'id' => $row->product_id,
                        'parent_id' => $this->modal_id,
                        'only_last_buys' => 1
                    )
                        ), true);
            }
        ));

        $gridParams = array(
            'id' => $this->controller->getServerId(),
            'type' => 'condensed',
            'dataProvider' => $this->dataProvider,
            'enableSorting' => false,
            'selectableRows' => 0,
            'columns' => $columns,
            'template' => '{items}',
            'rowCssClassExpression' => '"sian-prices-item " . ($data->checked == 1 ? "success" : "danger")',
            'htmlOptions' => [
                'class' => 'sian-prices-table sian-prices-item-table',
            ]
        );
        $this->widget('application.widgets.USGridView', $gridParams);
    }

}
