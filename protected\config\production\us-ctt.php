<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'ctt.siansystem.com/admin';
$domain = 'https://ctt.siansystem.com';
$domain2 = 'http://ctt.siansystem.com';
$report_domain = 'rconvial.siansystem.com';
$org = 'ctt';
//SIAN 2
$api_sian = 'https://api.siansystem.com/';
//database enterprise
$database_server = '161.132.48.88';
$database_name = 'ctt';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = 'ctt.p12';
$e_billing_certificate_pass = 'TERRAPUERTO24';//CTT22092023
$e_billing_ri = '0620050000126';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20549149112CONTA123',
        'password' => 'contador123'
    ]
];
$smtp_username = '<EMAIL>';
$smtp_password = '75s81ysyogkop';
$environment_reports = YII_ENVIRONMENT_PRODUCTION;
$environment = YII_ENVIRONMENT_PRODUCTION;
