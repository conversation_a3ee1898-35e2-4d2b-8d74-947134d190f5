<?php

class SIANTransformationResultGrid extends CWidget {

    public $id;
    public $model;
    public $dataProvider;
    public $route = '/logistic/product/preview';
    public $title = 'Lista de Productos Transformados';
    public $show_amounts = false;
    public $show_bar_code = false;
    public $show_quantity_balance = false;
    public $show_combinations = false;
    public $show_parents = false;
    public $modal_id = null;
    public $delivery_date_by_item = false;
    //PRIVATE
    private $controller;

    public $show_transformation_type = false;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //CONTROL
        if (is_null($this->model)) {
            throw new Exception("Debe especificar la instancia del modelo 'WarehouseMovement'!");
        }
        if (is_null($this->dataProvider)) {
            throw new Exception("Debe especificar el dataProvider!");
        }

        //
        $this->dataProvider->pagination = false;
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->dataProvider->getTotalItemCount() > 0) {

            echo "<h4>{$this->title}:</h4>";
            //Accesos
            $route = $this->route;
            $access1 = $this->controller->checkRoute($route);
            $modal_id = $this->modal_id;

            //GRID
            $columns = [];
            $counter = 0;


            array_push($columns, array(
                'name' => '#',
                'headerHtmlOptions' => array('style' => 'width:2%; text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center;'),
                'type' => 'raw',
                'value' => function ($row) use (&$counter) {
                    $counter++;
                    return $counter;
                },
            ));

            array_push($columns, array(
                'name' => 'ID',
                'headerHtmlOptions' => array('style' => 'width:3%; text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->itemObj->product_id;
                },
            ));
//            if ($this->show_parents) {
//                array_push($columns, array(
//                    'name' => 'ID Padres',
//                    'headerHtmlOptions' => array('style' => 'width:18%; text-align:center;'),
//                    'htmlOptions' => array('style' => 'text-align:center'),
//                    'type' => 'raw',
//                    'value' => function ($row) {
//                        $parents_ids = "";
//                        $item_links = ItemLink::findByItemId($row->itemObj->item_id);
//
//                        foreach ($item_links as $item_link) {
//                            $item_parent = Item::model()->findByPK(array('item_id' => $item_link->parent_item_id));
//                            if ($item_link && $item_parent) {
//                                if($parents_ids != ""){
//                                    $parents_ids = $parents_ids . " | " . $item_parent->product_id;
//                                }else {
//                                    $parents_ids = $item_parent->product_id;
//                                }
//                            }
//                        }
//                        return $parents_ids;
//                    },
//                ));
//            }

            array_push($columns, array(
                'name' => 'Nombre',
                'headerHtmlOptions' => array('style' => 'text-align:left;'),
                'type' => 'raw',
                'value' => function ($row) use ($access1, $route, $modal_id) {
                    return $row->itemObj->product_type == Product::TYPE_NEW ? $row->itemObj->product_name : Yii::app()->controller->widget('application.widgets.USLink', array (
                        'route' => $route,
                        'label' => $row->itemObj->product_name,
                        'title' => 'Ver producto',
                        'class' => 'form',
                        'data' => array (
                            'id' => $row->itemObj->product_id,
                            'parent_id' => $modal_id,
                        ),
                        'visible' => $access1,
                    ), true);
                },
            ));
            if ($this->show_bar_code) {

                array_push($columns, array(
                    'name' => 'Codbar.',
                    'headerHtmlOptions' => array('style' => 'text-align:left;'),
                    'htmlOptions' => array('style' => 'text-align:left'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        return $row->itemObj->presentation->barcode;
                    },
                ));
            }

            if ($this->show_combinations) {
                array_push($columns, array(
                    'name' => 'combination_name',
                    'headerHtmlOptions' => array('style' => 'width:10%;text-align:center;'),
                    'htmlOptions' => array('style' => 'text-align:center'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        return $row->combination_name;
                    },
                ));
            }

            if ($this->show_quantity_balance) {
                array_push($columns, array(
                    'name' => 'pres_balance',
                    'headerHtmlOptions' => array('style' => 'width:5%;text-align:center;'),
                    'htmlOptions' => array('style' => 'text-align:center'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        if ($row->pres_balance > 0 && $row->delivery_date <= SIANTime::formatDate()) {
                            return '<p style="color:red;">' . $row->pres_balance . '</p>';
                        } else {
                            return $row->pres_balance;
                        }
                    }
                ));
            }

            if ($this->delivery_date_by_item) {
                array_push($columns, array(
                    'name' => 'delivery_date',
                    'headerHtmlOptions' => array('style' => 'width:5%;text-align:center;'),
                    'htmlOptions' => array('style' => 'text-align:center'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        if ($row->pres_balance > 0 && $row->delivery_date <= SIANTime::formatDate()) {
                            return '<p style="color:red;">' . $row->delivery_date . '</p>';
                        } else {
                            return $row->delivery_date;
                        }
                    },
                ));
            }

            array_push($columns, array(
                'name' => 'Cantidad',
                'headerHtmlOptions' => array('style' => 'width:10%;text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->itemObj->pres_quantity;
                },
            ));

            array_push($columns, array(
                'name' => 'Pres.',
                'headerHtmlOptions' => array('style' => 'width:10%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right'),
                'type' => 'raw',
                'value' => function ($row) {
                    return isset ($row->itemObj->presentation->measure_name) ? $row->itemObj->presentation->measure_name : 'No Definido';
                },
            ));

            $gridParams = array(
                'id' => $this->id,
                'type' => 'hover condensed',
                'dataProvider' => $this->dataProvider,
                'enableSorting' => true,
                'selectableRows' => 0,
                'columns' => $columns,
                'template' => '{items}',
                'nullDisplay' => Strings::NONE,
            );

            $this->widget('application.widgets.USGridView', $gridParams);
        } else {
            echo Strings::NO_DATA;
        }
    }

}
