<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'testgrupohardtech.siansystem.com/admin';
$domain = 'https://testgrupohardtech.siansystem.com';
$api_sian = 'https://api-test.siansystem.com/';
$report_domain = 'rtest.siansystem.com';
$org = 'grupohardtech';
$us = 'us_test';
//SIAN 2
$domain_react_app = 'https://testgrupohardtech2.siansystem.com';
$api_sian = 'https://api-test.siansystem.com/';

$database_server = '161.132.48.88';
$database_name = 'grupohardtech_test';
$database_username = 'sian_test';
$database_password = '75nppt6vr57lx4';
$database_sian = 'sian_staging';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'grupohardtech.p12';
$e_billing_certificate_pass = 'DJUbkWxZMcrVvWfU';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20560020903BILLING7',
        'password' => 'Billing7'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_TESTING;
$environment = YII_ENVIRONMENT_TESTING;
