<?php

class PersonController extends SIANController {

    /**
     * @var string the default layout for the views. Defaults to '//layouts/main', meaning
     * using two-column layout. See 'protected/views/layouts/column2.php'.
     */
//    public $layout = '//layouts/main';
    public $plural = "Personas";
    public $singular = "persona";
    public $modelClass = 'Person';
    public $viewClass = 'DpAdministrationPersonIndex';

    /**
     * Specifies the access control rules.
     * This method is used by the 'accessControl' filter.
     * @return array access control rules
     */
    public function accessRules() {
        return array(
            array('allow',
                'actions' => array('index', 'preview', 'create', 'update', 'editCredit', 'delete', 'multistatus', 'multidelete'),
                'expression' => 'Yii::app()->controller->checkCurrent()',
            ),
            array('allow', // deny all users
                'actions' => array('getDNIData', 'getRUCData'),
                'users' => array('@'),
            ),
            array('deny', // deny all users
                'users' => array('*'),
            ),
        );
    }

    public function actionCreate($id = null, $modal_id = null, $parent_id = null, $type = null, $element_id = null, $person_type = Person::TYPE_ANY, $is_transport_company = 0) {

        $this->title = "Crear {$this->singular}";

        $identification_type_readonly = false;
        
        $model = new Person(Person::SCENARIO_NORMAL);
        $model->is_transport_company = $is_transport_company;

        if ($is_transport_company == 1) {
            $model->identification_type = USIdentificationValidator::RUC;
            $identification_type_readonly = true;
        }

        $modal_html = "";
        $content_html = "";
        $success = false;

        SIANLog::print($_POST);
        if (isset($_POST['Person'])) {  


            $model->attributes = $_POST['Person'];

            if (isset($_POST ['Phone'])) {
                foreach ($_POST ['Phone'] as $phone) {
                    $model->addPhone($phone);
                }
            }

            if (isset($_POST ['Email'])) {
                foreach ($_POST ['Email'] as $email) {
                    $model->addEmail($email);
                }
            }

            if (isset($_POST ['Address'])) {
                foreach ($_POST ['Address'] as $address) {
                    $model->addAddress($address);
                }
            }

            if (isset($_POST ['BankAccount'])) {
                foreach ($_POST ['BankAccount'] as $bankAccount) {
                    $model->addBankAccount($bankAccount);
                }
            }

            if ($model->validate()) {
                try {
                    //Iniciamos la transacción
                    $this->beginTransaction($model);
                    $success = $model->savingExternal()->insert();
                    //Confirmamos la transacción
                    $this->commitTransaction($model);
                } catch (Exception $ex) {
                    //Anulamos la transacción
                    $this->rollbackTransaction($model);
                    throw $ex;
                }
            }
        } else {
            $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('modal_id' => $modal_id, 'parent_id' => $parent_id)), true, true);
            $model->addAddress();
        }

        if (!$success) {
            $a_data = $this->createPersonData($model, $modal_id, $person_type);
            $a_data['identification_type_readonly'] = $identification_type_readonly;
            $content_html = $this->renderPartial("_form", array(
                'data' => $a_data
            ), true, true);
        } else {
            $content_html = $this->afterSuccess($element_id, $type, $model);
        }

        echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $model);
    }

    public function actionUpdate($id, $type = null, $modal_id = null, $parent_id = null, $element_id = null, $person_type = Person::TYPE_ANY) {

        $model = $this->loadUpdate($id);

        $this->title = "Editar {$this->singular} '{$model->toString()}'";

        $modal_html = "";
        $content_html = "";
        $success = false;

        if (isset($_POST['Person'])) {

            $model->attributes = $_POST['Person'];

            if (isset($_POST ['Phone'])) {
                foreach ($_POST ['Phone'] as $phone) {
                    $model->addPhone($phone);
                }
            }

            if (isset($_POST ['Email'])) {
                foreach ($_POST ['Email'] as $email) {
                    $model->addEmail($email);
                }
            }

            if (isset($_POST ['Address'])) {
                foreach ($_POST ['Address'] as $address) {
                    $model->addAddress($address);
                }
            }

            if (isset($_POST ['BankAccount'])) {
                foreach ($_POST ['BankAccount'] as $bankaccount) {
                    $model->addBankAccount($bankaccount);
                }
            }

            if ($model->validate()) {
                try {
                    //Iniciamos la transacción
                    $this->beginTransaction($model);
                    $success = $model->savingExternal()->update();
                    //Confirmamos la transacción
                    $this->commitTransaction($model);
                } catch (Exception $ex) {
                    //Anulamos la transacción
                    $this->rollbackTransaction($model);
                    throw $ex;
                }
            }
        } else {
            $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('modal_id' => $modal_id, 'parent_id' => $parent_id)), true, true);

            $model->loadExternal();
        }

        if (!$success) {
            $content_html = $this->renderPartial("_form", array(
                'data' => $this->updatePersonData($model, $modal_id, $person_type)
                    ), true, true);
        } else {
            $content_html = $this->afterSuccess($element_id, $type, $model);
        }

        echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $model);
    }

    public function actionEditCredit($id, $type = null, $modal_id = null, $parent_id = null, $element_id = null) {

        $model = $this->loadCredit($id);

        $this->title = "Editar créditos de {$this->singular} '{$model->toString()}'";

        $modal_html = "";
        $content_html = "";
        $success = false;

        if (isset($_POST['Person'])) {

            $model->attributes = $_POST['Person'];

            $validate_attributes = [
                'is_trusted',
                'is_gov',
                'client_credit',
                'client_line_pen',
                'client_line_usd',
                'client_balance_pen',
                'client_balance_usd',
                'client_credit_days',
                'provider_credit',
                'provider_line_pen',
                'provider_line_usd',
                'provider_balance_pen',
                'provider_balance_usd',
                'provider_credit_days',
            ];

            if ($model->validate($validate_attributes)) {
                $success = $this->secureSave($model);
            }
        } else {
            $modal_html = $this->renderPartial('application.views.common._modal', array('data' => array('modal_id' => $modal_id, 'parent_id' => $parent_id)), true, true);
        }

        if (!$success) {
            $content_html = $this->renderPartial('_edit_credit', array(
                'data' => array(
                    'modal_id' => $modal_id,
                    'model' => $model,
                )
                    ), true, true);
        } else {
            $content_html = $this->afterSuccess($element_id, $type, $model);
        }

        echo $this->defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $model);
    }

    public function actionPreview($id, $modal_id = null, $parent_id = null) {

        $model = $this->loadPreview($id);

        $this->title = "Visualizando {$this->singular} '{$model->toString()}'";

        $modal_html = $this->renderPartial('application.views.common._modal', array(
            'data' => array(
                'parent_id' => $parent_id,
                'modal_id' => $modal_id
            )
                ), true, true);

        $data = [
            'model' => $model,
            'modal_id' => $modal_id,
            'toCollectRoutes' => $this->getToCollectRoutes(),
            'toPayRoutes' => $this->getToPayRoutes()
        ];

        if ($this->checkRoute('showCredit')) {
            $data['clientDataProvider'] = (new SpGetExpiredAccounts())->setParams(['mode' => SpGetExpiredAccounts::MODE_CLIENT, 'person_id' => $id])->getDataProvider(array('movement_id'));
            $data['providerDataProvider'] = (new SpGetExpiredAccounts())->setParams(['mode' => SpGetExpiredAccounts::MODE_PROVIDER, 'person_id' => $id])->getDataProvider(array('movement_id'));
        }

        $content_html = $this->renderPartial("_preview", array(
            'data' => $data,
                ), true, true);

        echo CJSON::encode(array(
            'modal_id' => $modal_id,
            'modal_html' => $modal_html,
            'content_html' => $content_html
        ));
    }

    public function createPersonData($model, $modal_id, $person_type) {

        $category_items = (new DpMultitable(Multitable::LICENSE_CATEGORY_CODE))->getListData('multi_id', 'description');
        $convention_items = (new DpMultitable(Multitable::CATALOG_25))->getListData('multi_id', 'description');

        switch ($person_type) {
            case Person::TYPE_ANY:
                return array(
                    'modal_id' => $modal_id,
                    'model' => $model,
                    'identification_type_items' => USIdentificationValidator::getItems(),
                    'phone_type_items' => Phone::getPhoneTypeItems(),
                    'email_type_items' => DpEmail::getTypeItems(DpEmail::SCENARIO_PERSON),
                    'bank_account_bank_items' => (new DpMultitable(Multitable::CATALOG_3))->getListData('multi_id', 'description'),
                    'bank_account_type_items' => BankAccount::getTypeItems(),
                    'bank_account_currency_items' => Currency::getListData(),
                    'category_items' => $category_items,
                    'convention_items' => $convention_items,
                    'type_items' => Person::getPersonTypeCRM(),
                    'user_items' => (new DpSeller)->getListData('seller_id', 'person_name'),
                    'category_person_items' => (new DpMultitable(Multitable::CATEGORY_PERSON))->getListData('multi_id', 'description'),
                );
            case Person::TYPE_JURIDICAL:
                return array(
                    'modal_id' => $modal_id,
                    'model' => $model,
                    'identification_type_items' => USIdentificationValidator::getJuridicalItems(),
                    'phone_type_items' => Phone::getPhoneTypeItems(),
                    'email_type_items' => DpEmail::getTypeItems(DpEmail::SCENARIO_PERSON),
                    'bank_account_bank_items' => (new DpMultitable(Multitable::CATALOG_3))->getListData('multi_id', 'description'),
                    'bank_account_type_items' => BankAccount::getTypeItems(),
                    'bank_account_currency_items' => Currency::getListData(),
                    'category_items' => $category_items,
                    'convention_items' => $convention_items,
                    'type_items' => Person::getPersonTypeCRM(),
                    'user_items' => (new DpSeller)->getListData('seller_id', 'person_name'),
                    'category_person_items' => (new DpMultitable(Multitable::CATEGORY_PERSON))->getListData('multi_id', 'description'),
                );
            case Person::TYPE_NATURAL:
                return array(
                    'modal_id' => $modal_id,
                    'model' => $model,
                    'identification_type_items' => USIdentificationValidator::getNaturalItems(),
                    'phone_type_items' => Phone::getPhoneTypeItems(),
                    'email_type_items' => DpEmail::getTypeItems(DpEmail::SCENARIO_PERSON),
                    'bank_account_bank_items' => (new DpMultitable(Multitable::CATALOG_3))->getListData('multi_id', 'description'),
                    'bank_account_type_items' => BankAccount::getTypeItems(),
                    'bank_account_currency_items' => Currency::getListData(),
                    'category_items' => $category_items,
                    'convention_items' => $convention_items,
                    'type_items' => Person::getPersonTypeCRM(),
                    'user_items' => (new DpSeller)->getListData('seller_id', 'person_name'),
                    'category_person_items' => (new DpMultitable(Multitable::CATEGORY_PERSON))->getListData('multi_id', 'description'),
                );
        }
    }

    public function updatePersonData($model, $modal_id, $person_type) {

        $category_items = (new DpMultitable(Multitable::LICENSE_CATEGORY_CODE))->getListData('multi_id', 'description');
        $convention_items = (new DpMultitable(Multitable::CATALOG_25))->getListData('multi_id', 'description');

        switch ($person_type) {
            case Person::TYPE_ANY:
                return array(
                    'modal_id' => $modal_id,
                    'model' => $model,
                    'identification_type_items' => USIdentificationValidator::getItems(),
                    'phone_type_items' => Phone::getPhoneTypeItems(),
                    'email_type_items' => DpEmail::getTypeItems(DpEmail::SCENARIO_PERSON),
                    'bank_account_bank_items' => (new DpMultitable(Multitable::CATALOG_3))->getListData('multi_id', 'description'),
                    'bank_account_type_items' => BankAccount::getTypeItems(),
                    'bank_account_currency_items' => Currency::getListData(),
                    'category_items' => $category_items,
                    'convention_items' => $convention_items,
                    'type_items' => Person::getPersonTypeCRM(),
                    'user_items' => (new DpSeller)->getListData('seller_id', 'person_name'),
                    'category_person_items' => (new DpMultitable(Multitable::CATEGORY_PERSON))->getListData('multi_id', 'description'),
                );
            case Person::TYPE_JURIDICAL:
                return array(
                    'modal_id' => $modal_id,
                    'model' => $model,
                    'identification_type_items' => USIdentificationValidator::getJuridicalItems(),
                    'phone_type_items' => Phone::getPhoneTypeItems(),
                    'email_type_items' => DpEmail::getTypeItems(DpEmail::SCENARIO_PERSON),
                    'bank_account_bank_items' => (new DpMultitable(Multitable::CATALOG_3))->getListData('multi_id', 'description'),
                    'bank_account_type_items' => BankAccount::getTypeItems(),
                    'bank_account_currency_items' => Currency::getListData(),
                    'category_items' => $category_items,
                    'convention_items' => $convention_items,
                    'type_items' => Person::getPersonTypeCRM(),
                    'user_items' => (new DpSeller)->getListData('seller_id', 'person_name'),
                    'category_person_items' => (new DpMultitable(Multitable::CATEGORY_PERSON))->getListData('multi_id', 'description'),
                );
            case Person::TYPE_NATURAL:
                return array(
                    'modal_id' => $modal_id,
                    'model' => $model,
                    'identification_type_items' => USIdentificationValidator::getNaturalItems(),
                    'phone_type_items' => Phone::getPhoneTypeItems(),
                    'email_type_items' => DpEmail::getTypeItems(DpEmail::SCENARIO_PERSON),
                    'bank_account_bank_items' => (new DpMultitable(Multitable::CATALOG_3))->getListData('multi_id', 'description'),
                    'bank_account_type_items' => BankAccount::getTypeItems(),
                    'bank_account_currency_items' => Currency::getListData(),
                    'category_items' => $category_items,
                    'convention_items' => $convention_items,
                    'type_items' => Person::getPersonTypeCRM(),
                    'user_items' => (new DpSeller)->getListData('seller_id', 'person_name'),
                    'category_person_items' => (new DpMultitable(Multitable::CATEGORY_PERSON))->getListData('multi_id', 'description'),
                );
        }
    }

    public function loadPreview($id) {
        return Person::model()->with(array(
                    'addresses' => array(
                        'with' => array(
                            'geoloc'
                        )
                    ),
                    'emails',
                    'phones',
                    'bankAccounts',
                    'conventionObj',
                    'sellerCatchment' => array(
                        'with' => array(
                            'person' => array(
                                'select' => ('person_name')
                            )
                        )
                    ),
                    'category',
                    'countryObj'
                ))->findByPk(array('person_id' => $id));
    }

    public function loadUpdate($id) {
        $model = Person::model()->with(array(
                    'phones' => [],
                    'emails' => [],
                    'bankAccounts' => [],
                    'addresses.geoloc' => []
                ))->findByPk(array('person_id' => $id), array(
            'order' => 'PA.`order`, PE.`order`, PP.`order`'
        ));

        $model->scenario = Person::SCENARIO_NORMAL;

        return $model;
    }

    public function loadCredit($id) {
        $model = Person::model()->with(array(
                ))->findByPk(array('person_id' => $id));

        $model->scenario = Person::SCENARIO_CREDIT;

        return $model;
    }

    public function loadDelete($id) {
        return Person::model()->with()->findByPk(array('person_id' => $id));
    }

    public function actionGetDNIData($dni) {
        echo CJSON::encode(SIANReniec::getDNIData($dni));
    }

    public function actionGetRUCData($ruc) {
        echo CJSON::encode(SIANSunat::getRUCData($ruc));
    }

}
