<?php

class SIANBuyInItems extends CWidget {

    //CONST
    public $id;
    public $form;
    public $model;
    public $attribute;
    public $a_items = [];
    public $title = 'Listado de Mercaderias';
    public $name_preffix = 'BuyIn';
    public $hint = false;
    public $disabled = false;
    public $readonly = true;
    public $class_panel = '';
    //PRIVATE
    private $controller;

    //

    public function init() {
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        $a_items = $this->a_items;

        SIANAssets::registerScriptFile('js/sian-buy-in-items.js');

        Yii::app()->clientScript->registerScript($this->id, "

        //COUNT
        var divObj = $('#{$this->id}');
        divObj.data('readonly', " . CJSON::encode($this->readonly) . ");
        divObj.data('name_preffix', '{$this->name_preffix}');
            
        var array = " . CJSON::encode($a_items) . ";
        //alert(JSON.stringify(array));
        
        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANBuyInItemsAddItem('{$this->id}', array[i]['movement_id'], array[i]['item_number'], array[i]['product_id'], array[i]['product_name'], array[i]['pres_quantity'], array[i]['product_type'], array[i]['alias']);                
            }
        }
        else
        {
            $('#{$this->id}').html('<p>" . Strings::NO_DATA . "</p>');
        }
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}'>";

        echo "<div class='row sian-buy-in-items-products'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Listado de mercaderias',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id . '_products_panel'
            )
        ));
        echo $this->_renderProductTable();
        $this->endWidget();
        echo "</div>";
        echo "</div>";

        echo "</div>";
    }

    private function _renderProductTable() {
        $s_html = "<table id='{$this->id}_table' class='products_table items table table-hover table-condensed'>";
        $s_html .= "<thead>";
        $s_html .= "<tr>";
        $s_html .= "<th width='5%'><b>Check</b></th>";
        $s_html .= "<th width='5%'><b>Item</b></th>";
        $s_html .= "<th width='10%'><b>Id Prod.</b></th>";
        $s_html .= "<th width='70%'><b>Producto</b></th>";
        $s_html .= "<th width='15%'><b>Cantidad</b></th>";
        $s_html .= "</tr>";
        $s_html .= "</thead>";
        $s_html .= "<tbody>";
        $s_html .= "</tbody>";
        $s_html .= "</table>";

        return $s_html;
    }

}
