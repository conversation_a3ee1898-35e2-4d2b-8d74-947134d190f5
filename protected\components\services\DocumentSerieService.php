<?php

class DocumentSerieService {

    protected $repository;

    public function __construct($repository) {
        $this->repository = $repository;
    }

    public function getSerieForSystem($documentId, $storeId) {

        $serie = $this->repository->getFirstSerie($documentId, $storeId);

        if (!$serie) {
            throw new Exception("No se encuentra una serie adecuada para el documento con ID {$documentId}.");
        }

        return $serie;
    }

    public function getSerieForApi($documentId, $storeId) {

        $serie = $this->repository->getFirstSerie($documentId, $storeId);

        if (!$serie) {
            USREST::sendResponse(USREST::CODE_NOT_FOUND, "No se encuentra una serie adecuada para el documento con ID {$documentId}. Comuníquese con el proveedor del API.");
            Yii::app()->end();
        }

        return $serie;
    }

}
