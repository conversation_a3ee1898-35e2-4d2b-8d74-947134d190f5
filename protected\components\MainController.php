<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Este controlador no debe tener referencia a un usuario logueado
 * No se debe usar acá Yii::app()->user->
 *
 * <AUTHOR>
 */
class MainController extends CController {

//Private
    private $organization = null;
    private $businessUnitDivisions = null;
    private $storeBusinessUnits = null;
    private $default_warehouse_id = null;
    private $commercial_warehouse_ids = null;
    private $last_exchange = null;
    private $element_id = 0;
//Microtime
    private $begin_time;
    private $end_time;
    private $begin_transaction = null;
    private $end_transaction = null;

//Public
    protected function beforeAction($action) {
        $this->element_id = SIANCache::get('element_id');

        if ($this->element_id === false) {
            $this->element_id = 0;
        }
//
        $this->begin_time = microtime(true);
//
        return parent::beforeAction($action);
    }

    protected function afterAction($action) {
        SIANCache::add('element_id', $this->element_id);
//
        $this->_saveTimes();
        return parent::afterAction($action);
    }

    public function redirect($url, $terminate = true, $statusCode = 302) {
        $this->_saveTimes(1);
        return parent::redirect($url, $terminate, $statusCode);
    }

    private function _saveTimes($p_i_redirected = 0) {
        $this->end_time = microtime(true);
        $f_time = $this->end_time - $this->begin_time;
        $f_transaction = null;
        if (isset($this->begin_transaction) && isset($this->end_transaction)) {
            $f_transaction = $this->end_transaction - $this->begin_transaction;
        }
        //Guardamos tiempo de ejecución
        ExecutionTime::saveTime($this->route, $f_time, $f_transaction, $p_i_redirected);
    }

    public function getServerId() {
        $this->element_id++;
        return "element_" . $this->element_id;
    }

    public function getOrganization() {
        if (!isset($this->organization)) {

            $o_organization = SIANCache::get(SIANCache::ORGANIZATION);

            if ($o_organization === false) {
                $o_organization = Organization::get();
                SIANCache::add(SIANCache::ORGANIZATION, $o_organization);
            }

            $this->organization = $o_organization;
        }
        return $this->organization;
    }

    public function getBusinessUnitDivisions() {
        if (!isset($this->businessUnitDivisions)) {

            $a_businessUnitDivisions = SIANCache::get(SIANCache::BUSINESS_UNIT_DIVISIONS);

            if ($a_businessUnitDivisions === false) {

                $a_raw_items = Yii::app()->db->createCommand()
                        ->select([
                            'OP1.owner_id AS business_unit_id',
                            'OP2.owner_id AS division_id'
                        ])
                        ->from('owner_pair OP1')
                        ->join('owner_pair OP2', 'OP2.parent_id = OP1.pair_id AND OP2.`owner` = :owner2', [
                            ':owner2' => Division::OWNER
                        ])
                        ->where("OP1.type = :type AND OP1.`owner` = :owner1", [
                            'type' => OwnerPair::TYPE_BUSINESS_UNIT_DIVISION,
                            ':owner1' => BusinessUnit::OWNER
                        ])
                        ->queryAll();
//Normalizamos
                $a_businessUnitDivisions = USArray::listData($a_raw_items, 'business_unit_id', 'division_id', false);
//
                SIANCache::add(SIANCache::BUSINESS_UNIT_DIVISIONS, $a_businessUnitDivisions);
            }

            $this->businessUnitDivisions = $a_businessUnitDivisions;
        }
        return $this->businessUnitDivisions;
    }

    public function getStoreBusinessUnits() {
        if (!isset($this->storeBusinessUnits)) {

            $a_storeBusinessUnits = SIANCache::get(SIANCache::STORE_BUSINESS_UNIT);

            if ($a_storeBusinessUnits === false) {

                $a_raw_items = Yii::app()->db->createCommand()
                        ->select([
                            'S.store_id',
                            'S.business_unit_id'
                        ])
                        ->from('store S')
                        ->where("S.business_unit_id IS NOT NULL")
                        ->queryAll();
//Normalizamos
                $a_storeBusinessUnits = USArray::listData($a_raw_items, 'store_id', 'business_unit_id');
//
                SIANCache::add(SIANCache::STORE_BUSINESS_UNIT, $a_storeBusinessUnits);
            }

            $this->storeBusinessUnits = $a_storeBusinessUnits;
        }
        return $this->storeBusinessUnits;
    }

    public function getDefaultWarehouseId() {
        if (!isset($this->default_warehouse_id)) {

            $i_default_warehouse_id = SIANCache::get(SIANCache::DEFAULT_WAREHOUSE_ID);

            if ($i_default_warehouse_id === false) {
                $i_default_warehouse_id = Yii::app()->db->createCommand()
                        ->select(['W.warehouse_id'])
                        ->from('warehouse W')
                        ->where("W.`default`")
                        ->queryScalar();
                SIANCache::add(SIANCache::DEFAULT_WAREHOUSE_ID, $i_default_warehouse_id);
            }

            $this->default_warehouse_id = $i_default_warehouse_id;
        }
        return $this->default_warehouse_id;
    }

    public function getCommercialWarehouseIds($p_b_implode = false, $p_i_store_id = NULL) {

        if (!isset($this->commercial_warehouse_ids)) {

            if (isset($p_i_store_id) && $p_i_store_id > 0) {

                $s_show_stock_of = Yii::app()->db->createCommand()
                                ->select(['ST.show_stock_of'])
                                ->from('store ST')
                                ->where("ST.`status` AND ST.store_id = " . $p_i_store_id)->queryScalar();

                switch ($s_show_stock_of) {
                    case Store::SHOW_STOCK_OF_ALL:
                        $query = Yii::app()->db->createCommand()
                                ->select(['W.warehouse_id'])
                                ->from('warehouse W')
                                ->where("W.`status` AND W.outgoing AND W.commercial_treatment");
                        break;
                    case Store::SHOW_STOCK_OF_OWN:
                        $query = Yii::app()->db->createCommand()
                                ->select(['W.warehouse_id'])
                                ->from('warehouse W')
                                ->where("W.`status` AND W.outgoing AND W.commercial_treatment AND  W.store_id = " . $p_i_store_id);
                        break;
                    case Store::SHOW_STOCK_OF_CUSTOME:
                        $query = Yii::app()->db->createCommand()
                                ->select(['W.warehouse_id'])
                                ->from('store ST')
                                ->join('owner_pair OPP', "OPP.type = '" . OwnerPair::TYPE_STOCK_STORE_WAREHOUSE . "' AND OPP.owner = '" . Store::OWNER . "' AND OPP.owner_id = ST.store_id")
                                ->join('owner_pair OPH', "OPH.type = OPP.type AND OPH.owner = '" . Warehouse::OWNER . "' AND OPH.parent_id = OPP.pair_id")
                                ->join('warehouse W', "W.warehouse_id = OPH.owner_id")
                                ->where("W.`status` AND W.outgoing AND W.commercial_treatment AND ST.store_id  = " . $p_i_store_id);
                        break;
                }
                $a_rows = $query->queryAll();
                $i_commercial_warehouse_ids = USArray::array_column($a_rows, 'warehouse_id');
            } else {

                $i_commercial_warehouse_ids = SIANCache::get(SIANCache::COMMERCIAL_WAREHOUSE_IDS);

                if ($i_commercial_warehouse_ids === false) {

                    $query = Yii::app()->db->createCommand()
                            ->select(['W.warehouse_id'])
                            ->from('warehouse W')
                            ->where("W.`status` AND W.outgoing AND W.commercial_treatment");
                    $a_rows = $query->queryAll();

                    $i_commercial_warehouse_ids = USArray::array_column($a_rows, 'warehouse_id');
                    SIANCache::add(SIANCache::COMMERCIAL_WAREHOUSE_IDS, $i_commercial_warehouse_ids);
                }
            }
            $this->commercial_warehouse_ids = $i_commercial_warehouse_ids;
        }

        //En modo texto
        if ($p_b_implode) {
            if (count($this->commercial_warehouse_ids) == 0) {
                return "0";
            } else {
                return implode(',', $this->commercial_warehouse_ids);
            }
        } else {
            return $this->commercial_warehouse_ids;
        }
    }

    /**
     * Obtiene el TC actual
     * @return double TC
     */
    public function getLastExchange() {
        if (!isset($this->last_exchange)) {
            $this->last_exchange = ExchangeRate::getLastExchangeRate();
        }

        return $this->last_exchange;
    }

    /**
     * Convierte una vista en un archivo determinado
     * @param mixed $view Vista a renderizar
     * @param array $data Array de datos a pasar a la vista
     * @param mixed $filepath Nombre de archivo donde se guardará
     * @return mixed Nombre de archivo donde se guardará
     */
    public function convertToPdf($view, $data, $filepath, $overwrite = false) {

        if ($overwrite || !file_exists($filepath)) {
            $s_html = $this->renderPartial($view, $data, true, false);
            USPDF::convertHtml($s_html, $filepath);
        }

        return $filepath;
    }

    protected function beginTransaction($p_o_model = null) {
        if (isset($p_o_model) && $p_o_model instanceof SIANModel) {
            $p_o_model->beginCachedTransaction();
        } else {
            Yii::app()->db->beginTransaction();
        }
        //
        $this->begin_transaction = microtime(true);
    }

    protected function commitTransaction($p_o_model = null) {
        if (isset($p_o_model) && $p_o_model instanceof SIANModel) {
            $p_o_model->commitCachedTransaction();
        } else {
            Yii::app()->db->getCurrentTransaction()->commit();
        }

        $this->end_transaction = microtime(true);
    }

    protected function rollbackTransaction($p_o_model = null) {
        if (isset($p_o_model) && $p_o_model instanceof SIANModel) {
            $p_o_model->rollbackCachedTransaction();
        } else {
            Yii::app()->db->getCurrentTransaction()->rollback();
        }

        $this->end_transaction = null;
    }

}
