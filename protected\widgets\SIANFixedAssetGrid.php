<?php

class SIANFixedAssetGrid extends CWidget {

    public $id;
    public $form;
    public $model;
    public $readonly = false;
    //PRIVATE
    private $controller;
    
    public function init() {

        $this->controller = Yii::app()->controller;

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-fixed-asset-grid.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        var divObj = $('#{$this->id}');

        $('#{$this->id}').data('changeOperation', function(changeData){
            
            var divObj = $('#{$this->id}');
            //GUARDAMOS INFO DE OPERACIÓN
            divObj.data('operationChangeData', changeData);
            alert(JSON.stringify(changeData));
        });
        
        $(document).ready(function() {
        

        });
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "Detalle de activos fijos",
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => ($this->model->hasErrors('tempItems') ? 'us-error' : '')
            )
        ));
      
        echo "<div class='sian-fixed-asset-grid'></div>";
       

        echo "</div>"; //ROW1

        $this->endWidget();
    }
}
