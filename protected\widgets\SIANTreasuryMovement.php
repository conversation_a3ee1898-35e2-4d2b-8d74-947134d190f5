<?php

class SIANTreasuryMovement extends CWidget {

    public $id;
    public $model;
    public $form;
    public $type_items;
    public $currency_items;
    public $lockType = false;
    public $lockFeeAmounts = false;
    public $lockCurrency = false;
    public $lockCashbox = false;
    public $lockInterest = 0;
    public $movement_type_input_id = null;
    public $real_input_id = null;
    public $interest_input_id = null;
    public $desgravamen_input_id = null;
    public $port_input_id = null;
    public $itf_input_id = null;
    public $cash_round_input_id = null;
    public $cash_round_income_input_id = null;
    public $total_input_id = null;
    public $cash_input_id = null;
    public $parent_currency_input_id = null;
    public $balance_pen_input_id = null;
    public $balance_usd_input_id = null;
    public $readonly = false;
    public $real_readonly = false;
    public $fixed = 2;
    public $step = 0.01;
    public $onchangeCurrency = '';
    public $hint = null;
    public $onChangeCCDependence = ''; //Para hacer un puente
    //PRIVATE
    private $controller;
    private $currency_input_id;
    private $is_pay_detraction_input_id;
    private $cashbox_input_id;
    private $reference_number_id;
    private $credit_card_id;
    private $calculate_cash_round_id;
    private $calculate_change_id;
    private $currency_symbol;
    private $banks;
    private $has_parent = false;

    public function init() {
        //CONTROL
        $this->has_parent = isset($this->model->parent_currency) && isset($this->model->balance_pen) && isset($this->model->balance_usd);
        //ID
        $this->controller = Yii::app()->controller;
        //
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->movement_type_input_id = isset($this->movement_type_input_id) ? $this->movement_type_input_id : $this->controller->getServerId();
        $this->real_input_id = isset($this->real_input_id) ? $this->real_input_id : $this->controller->getServerId();
        $this->interest_input_id = isset($this->interest_input_id) ? $this->interest_input_id : $this->controller->getServerId();
        $this->desgravamen_input_id = isset($this->desgravamen_input_id) ? $this->desgravamen_input_id : $this->controller->getServerId();
        $this->itf_input_id = isset($this->itf_input_id) ? $this->itf_input_id : $this->controller->getServerId();
        $this->port_input_id = isset($this->port_input_id) ? $this->port_input_id : $this->controller->getServerId();
        $this->cash_round_input_id = isset($this->cash_round_input_id) ? $this->cash_round_input_id : $this->controller->getServerId();
        $this->cash_round_income_input_id = isset($this->cash_round_income_input_id) ? $this->cash_round_income_input_id : $this->controller->getServerId();
        $this->total_input_id = isset($this->total_input_id) ? $this->total_input_id : $this->controller->getServerId();
        $this->cash_input_id = isset($this->cash_input_id) ? $this->cash_input_id : $this->controller->getServerId();
        $this->parent_currency_input_id = isset($this->parent_currency_input_id) ? $this->parent_currency_input_id : $this->controller->getServerId();
        $this->balance_pen_input_id = isset($this->balance_pen_input_id) ? $this->balance_pen_input_id : $this->controller->getServerId();
        $this->balance_usd_input_id = isset($this->balance_usd_input_id) ? $this->balance_usd_input_id : $this->controller->getServerId();
        //PRIVATE
        $this->currency_input_id = $this->controller->getServerId();
        $this->cashbox_input_id = $this->controller->getServerId();
        $this->is_pay_detraction_input_id = $this->controller->getServerId();
        $this->reference_number_id = $this->controller->getServerId();
        $this->credit_card_id = $this->controller->getServerId();
        $this->calculate_cash_round_id = $this->controller->getServerId();
        $this->calculate_change_id = $this->controller->getServerId();
        //VAR
        $this->currency_symbol = $this->model->movement->getCurrencySymbol();
        //Access
        $this->banks = $this->controller->checkRoute('banks');

        //TYPES
        $typeItems = [];
        foreach ($this->type_items as $key => $value) {
            if (!in_array($key, array(CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT, CashboxMovement::PAYMENT_METHOD_BANK_CHECK)) || $this->banks) {
                $typeItems[$key] = $value;
            }
        }
        $this->type_items = $typeItems;

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-treasury-movement.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
                
        var divObj = $('#{$this->id}');
        divObj.data('store_id', '{$this->model->movement->store_id}');
        divObj.data('direction', '{$this->model->movement->scenario->direction}');
        divObj.data('currency', '{$this->model->movement->currency}');
        divObj.data('is_pay_detraction', '{$this->model->is_pay_detraction}');
        divObj.data('movement_type', '{$this->model->type}');
        divObj.data('cashbox_id', '{$this->model->cashbox_id}');
        divObj.data('calculate_cash_round', " . CJSON::encode($this->model->calculate_cash_round == 1) . ");
        divObj.data('cashboxUrl', '{$this->controller->createUrl("/movement/getCashboxes")}');
        divObj.data('is_internal', " . (isset($this->model->movement->parent_is_internal) ? $this->model->movement->parent_is_internal : 'null') . ");
        divObj.data('banks', " . CJSON::encode($this->banks) . ");
        divObj.data('fixed', '{$this->fixed}');
        divObj.data('lockInterest', '{$this->lockInterest}');
        divObj.data('real_readonly', '{$this->real_readonly}');
            
        $(document).ready(function() {     
        
            //AL INICIAR
            var balancePenInput = $('#{$this->balance_pen_input_id}');
            var balanceUsdInput = $('#{$this->balance_usd_input_id}');

            switch('{$this->model->movement->currency}')
            {
                case '" . Currency::PEN . "':
                    balancePenInput.parents('div.form-group').show();
                    balanceUsdInput.parents('div.form-group').hide();
                break;
                case '" . Currency::USD . "':
                    balancePenInput.parents('div.form-group').hide();
                    balanceUsdInput.parents('div.form-group').show();
                break;
                default:
                    bootbox.alert(us_message('Moneda Inválida!', 'error'));
                break;
            }            

            SIANTreasuryMovementUpdateCashboxes('{$this->id}');
            //updateRound()
        });

        $('#{$this->id}').data('changeOperation', function(changeData){
            " . (!$this->readonly ? "SIANTreasuryMovementAbility('{$this->id}', changeData.dynamics, " . CJSON::encode($this->lockFeeAmounts) . ");" : "") . "

            var divObj = $('#{$this->id}');

            //GUARDAMOS INFO DE OPERACIÓN
            divObj.data('operationChangeData', changeData);

            //Actualizamos montos
            SIANTreasuryMovementUpdate('{$this->id}');
        });
        
        $('#{$this->id}').data('changeCCDependence', function(operationChangeData){
            {$this->onChangeCCDependence};
        });

        $('#{$this->id}').data('changeStore', function(changeData){
            $('#{$this->id}').data('store_id', changeData.store_id);
            SIANTreasuryMovementUpdateCashboxes('{$this->id}');        
        }); 
        
        $('#{$this->id}').data('changeExchange', function(exchange_rate){

            var div = $('#{$this->id}');
            var is_pay_detraction = $('#{$this->is_pay_detraction_input_id}').prop('checked');            
            div.data('exchange_rate_header', exchange_rate);
            
            if(is_pay_detraction){
                exchange_rate = {$this->model->movement->exchange_rate};
            }             
            updateExchangeRate(exchange_rate);     
        });

        $('body').on('change', '#{$this->currency_input_id}', function(e) {

            var div = $('#{$this->id}');
            var currency = $('#{$this->currency_input_id}').val();
            var exchange_rate = div.data('exchange_rate');
            
            //Guardamos
            div.data('currency', currency);

            {$this->onchangeCurrency};
                
            var realInput = $('#{$this->real_input_id}');               
            var interestInput = $('#{$this->interest_input_id}');
            var desgravamenInput = $('#{$this->desgravamen_input_id}');
            var portInput = $('#{$this->port_input_id}');
            var itfInput = $('#{$this->itf_input_id}');
            var cashRoundInput = $('#{$this->cash_round_input_id}');
            var cashRoundIncomeInput = $('#{$this->cash_round_income_input_id}');
            var totalInput = $('#{$this->total_input_id}');
            var balancePenInput = $('#{$this->balance_pen_input_id}');
            var balanceUsdInput = $('#{$this->balance_usd_input_id}');
            switch(currency)
            {
                case '" . Currency::PEN . "':
                    " . ($this->model->is_massive ? "" :
                        "realInput.toPen(exchange_rate, {$this->fixed}, {max: balancePenInput.floatVal({$this->fixed})});") . "
                    interestInput.toPen(exchange_rate, {$this->fixed});
                    desgravamenInput.toPen(exchange_rate, {$this->fixed});
                    portInput.toPen(exchange_rate, {$this->fixed});
                    itfInput.toPen(exchange_rate, {$this->fixed});
                    cashRoundInput.toPen(exchange_rate, {$this->fixed});
                    cashRoundIncomeInput.toPen(exchange_rate, {$this->fixed});
                    totalInput.toPen(exchange_rate, {$this->fixed});
                    balancePenInput.parents('div.form-group').show();
                    balanceUsdInput.parents('div.form-group').hide();
                break;
                case '" . Currency::USD . "':
                    " . ($this->model->is_massive ? "" :
                        "realInput.toUsd(exchange_rate, {$this->fixed}, {max: balanceUsdInput.floatVal({$this->fixed})});") . "
                    interestInput.toUsd(exchange_rate, {$this->fixed});
                    desgravamenInput.toUsd(exchange_rate, {$this->fixed});
                    portInput.toUsd(exchange_rate, {$this->fixed});
                    itfInput.toUsd(exchange_rate, {$this->fixed});
                    cashRoundInput.toUsd(exchange_rate, {$this->fixed});
                    cashRoundIncomeInput.toUsd(exchange_rate, {$this->fixed});
                    totalInput.toUsd(exchange_rate, {$this->fixed});
                    balancePenInput.parents('div.form-group').hide();
                    balanceUsdInput.parents('div.form-group').show();
                break;
                default:
                    bootbox.alert(us_message('Moneda Inválida!', 'error'));
                break;
            }

            SIANTreasuryMovementUpdateCashboxes('{$this->id}');
        });
        
        $('body').on('change', '#{$this->movement_type_input_id}', function(e) {
            
            var movement_type = $('#{$this->movement_type_input_id}').val();
            //Guardamos
            $('#{$this->id}').data('movement_type', movement_type);
        
            var calculate_change = $('#{$this->calculate_change_id}').bootstrapSwitch('state');
                
            //Seteamos
            $('#{$this->reference_number_id}')
                            .makeRequired(inArray(movement_type, ['" . CashboxMovement::PAYMENT_METHOD_CREDIT_CARD . "', '" . CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT . "', '" . CashboxMovement::PAYMENT_METHOD_BANK_CHECK . "']))
                            .prop('disabled', !inArray(movement_type, ['" . CashboxMovement::PAYMENT_METHOD_CREDIT_CARD . "', '" . CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT . "', '" . CashboxMovement::PAYMENT_METHOD_BANK_CHECK . "']));
                                
            $('#{$this->credit_card_id}')
                            .makeRequired(movement_type === '" . CashboxMovement::PAYMENT_METHOD_CREDIT_CARD . "')
                            .prop('disabled', movement_type !== '" . CashboxMovement::PAYMENT_METHOD_CREDIT_CARD . "'); 
            
            $('#{$this->calculate_change_id}')
                            .bootstrapSwitch('readonly', movement_type !== '" . CashboxMovement::PAYMENT_METHOD_CASH . "'); 
                            
            $('#{$this->cash_input_id}')
                            .prop('disabled', !calculate_change || movement_type !== '" . CashboxMovement::PAYMENT_METHOD_CASH . "'); 
            $('#{$this->cash_round_input_id}').val(0.00);
            $('#{$this->cash_round_income_input_id}').val(0.00);
            updateRound();
            SIANTreasuryMovementUpdateCashboxes('{$this->id}');
        });
        
        $('body').on('change', '#{$this->cashbox_input_id}', function(e) {
            
            var cashboxOption = $('#{$this->cashbox_input_id} option:selected');
            //Guardamos
            $('#{$this->id}').data('cashbox_id', cashboxOption.val());
            $('#{$this->id}').data('is-detraction', cashboxOption.data('is-detraction'));

            if(typeof cashboxOption.data('var-fee') !== 'undefined' && typeof cashboxOption.data('fix-fee') !== 'undefined')
            {   
                var portInput = $('#{$this->port_input_id}');
                    
                if(!portInput.prop('readonly'))
                {
                    var real = portInput.floatVal({$this->fixed});
                    var var_fee = cashboxOption.data('var-fee');
                    var fix_fee = cashboxOption.data('fix-fee');
                    
                    if(var_fee > 0 && fix_fee > 0)
                    {
                        $('#{$this->port_input_id}').floatVal({$this->fixed}, real * var_fee + fix_fee);   
                    }
                }
            }       
            SIANTreasuryMovementUpdate('{$this->id}');
        });

        $('body').on('change', '#{$this->real_input_id}, #{$this->interest_input_id}, #{$this->desgravamen_input_id}, #{$this->port_input_id}, #{$this->itf_input_id}, #{$this->cash_round_input_id}, #{$this->cash_round_income_input_id}', function(e) {
            SIANTreasuryMovementUpdate('{$this->id}');
        });
        
        $('body').on('change', '#{$this->cash_input_id}', function(e) {

            var elementObj = $(this);
            var cash = elementObj.floatVal({$this->fixed});
            var total = $('#{$this->total_input_id}').floatVal({$this->fixed});

            if(cash >= total)
            {
                elementObj.notify('Vuelto: ' + USMath.minus(cash, total, {$this->fixed}).toFixed({$this->fixed}), {
                    className: 'info',
                    showDuration: 30,
                    hideDuration: 50,
                    autoHideDelay: 5000,
                    position: 'top'
                });  
            }           
        });
        
        function updateExchangeRate(exchange_rate){
            //Seteamos
            var div = $('#{$this->id}'); 
            div.data('exchange_rate', exchange_rate); 

            if(" . json_encode($this->has_parent) . ")
            {
                var realInput = $('#{$this->real_input_id}');
                var balancePenInput = $('#{$this->balance_pen_input_id}');
                var balanceUsdInput = $('#{$this->balance_usd_input_id}');
                var currency = $('#{$this->currency_input_id}').val();

                var parent_currency = $('#{$this->parent_currency_input_id}').val();

                switch(parent_currency)
                {
                    case '" . Currency::PEN . "':
                        balanceUsdInput.floatVal({$this->fixed}, balancePenInput.floatVal({$this->fixed}) / exchange_rate);
                    break;
                    case '" . Currency::USD . "':
                        balancePenInput.floatVal({$this->fixed}, balanceUsdInput.floatVal({$this->fixed}) * exchange_rate);
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }

                //Actualizamos el max
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        realInput.floatAttr('max', {$this->fixed}, balancePenInput.floatVal({$this->fixed}));
                    break;
                    case '" . Currency::USD . "':
                        realInput.floatAttr('max', {$this->fixed}, balanceUsdInput.floatVal({$this->fixed}));
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }

                //Actualizamos montos
                SIANTreasuryMovementUpdate('{$this->id}');
            }
        }
        
        function updateRound(){
            var is_pay_detraction = $('#{$this->is_pay_detraction_input_id}').prop('checked');
            var movement_type = $('#{$this->movement_type_input_id}').val();
            var span_hint = $('#{$this->calculate_cash_round_id}').parent().parent().parent().children('span.help-block')[0];

            if(is_pay_detraction){
                if(movement_type == '" . CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT . "' || movement_type == '" . CashboxMovement::PAYMENT_METHOD_BANK_CHECK . "'){
                    $('#{$this->calculate_cash_round_id}').bootstrapSwitch('readonly', false);                    
                    $(span_hint).text('Redondeará a entero');                    
                }else{
                    $('#{$this->calculate_cash_round_id}').bootstrapSwitch('state', true);
                    $('#{$this->calculate_cash_round_id}').bootstrapSwitch('readonly', true);                    
                    $('#{$this->cash_input_id}').val(0);
                    $('#{$this->calculate_change_id}').bootstrapSwitch('readonly', true);
                    $('#{$this->calculate_change_id}').bootstrapSwitch('state', false);
                    $(span_hint).text('Redondeo manual');                    
                }
                $('#{$this->cash_round_input_id}').attr('readonly', true);
                $('#{$this->cash_round_income_input_id}').attr('readonly', true);
            }else{                
                if(movement_type == '" . CashboxMovement::PAYMENT_METHOD_CASH . "'){
                    
                    $('#{$this->calculate_cash_round_id}').bootstrapSwitch('readonly', false);
                    $('#{$this->calculate_change_id}').bootstrapSwitch('readonly', false);
                    $(span_hint).text('Redondeará el monto en múltiplos de 0.10');
                    $('#{$this->cash_round_input_id}').attr('readonly', true);
                    $('#{$this->cash_round_income_input_id}').attr('readonly', true);
                }else{        
                    $('#{$this->calculate_cash_round_id}').bootstrapSwitch('state', true);
                    $('#{$this->calculate_cash_round_id}').bootstrapSwitch('readonly', true);                        
                    $('#{$this->calculate_cash_round_id}').closest('span.help-block').text('');
                    $('#{$this->cash_input_id}').val(0);                    
                    $('#{$this->calculate_change_id}').bootstrapSwitch('state', false);
                    $(span_hint).text('Redondeo manual');
                    $('#{$this->cash_round_input_id}').removeAttr('readonly');
                    $('#{$this->cash_round_income_input_id}').removeAttr('readonly');
                }
            }
        }

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Detalle de movimiento',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
            )
        ));

        //PRINCIPAL
        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        $this->_renderColumn1();
        echo "</div>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        $this->_renderColumn2();
        echo "</div>";
        echo "</div>";

        echo "<hr>";

        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        $this->_renderColumn3();
        echo "</div>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        $this->_renderColumn4();
        echo "</div>";
        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        $this->_renderColumn5();
        echo "</div>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        $this->_renderColumn6();
        echo "</div>";
        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        echo $this->hint;
        echo "</div>";
        echo "</div>";

        if ($this->model->movement->scenario->auto_person_id == 1) {
            echo $this->form->hiddenField($this->model->movement, 'aux_person_id');
        }

        $this->endWidget();
    }

    private function _renderColumn1() {

        echo "<div class='row'>";
        if ($this->model->movement->direction == Scenario::DIRECTION_IN) {
            $b_show_pay_detraction = in_array($this->model->parent_route, CashboxMovement::a_routes_to_collect_detraction);
        } else {
            $b_show_pay_detraction = in_array($this->model->parent_route, CashboxMovement::a_routes_to_pay_detraction);
        }

        if ($b_show_pay_detraction) {
            echo "<div class='col-lg-2 col-md-4 col-sm-6 col-xs-12'>";
            echo $this->form->hiddenField($this->model, 'parent_route', array(
            ));
            echo $this->widget('application.widgets.USSwitch', array(
                'id' => $this->is_pay_detraction_input_id,
                'model' => $this->model,
                'attribute' => 'is_pay_detraction',
                'switchChange' => "
                                    var divObj = $('#{$this->id}');
                                    divObj.data('is_pay_detraction', this.checked? '1': '0');                                    
                                    updateRound();
                                    if(this.checked){
                                        updateExchangeRate({$this->model->movement->exchange_rate}); 
                                    }else{
                                        updateExchangeRate(divObj.data('exchange_rate_header')); 
                                    }"
                .
                ($this->model->movement->direction == Scenario::DIRECTION_IN ?
                        "SIANTreasuryMovementUpdateCashboxes('{$this->id}');" : "")
                    ), true);
            echo "</div>";
        }
        $col = $b_show_pay_detraction ? 2 : 3;
        $col2 = $b_show_pay_detraction ? 4 : 6;
        echo "<div class='col-lg-{$col} col-md-{$col2} col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'type', $this->type_items, array(
            'id' => $this->movement_type_input_id,
            'readonly' => $this->lockType || $this->readonly
        ));
        echo "</div>";

        echo "<div class='col-lg-{$col} col-md-{$col2} col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->movement, 'currency', $this->currency_items, array(
            'id' => $this->currency_input_id,
            'readonly' => $this->lockCurrency || $this->readonly
        ));
        echo "</div>";

        echo "<div class='col-lg-6 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'cashbox_id', [], array(
            'id' => $this->cashbox_input_id,
            'class' => 'sian-treasury-movement-cashbox-id',
            'label' => "{$this->model->getAttributeLabel('cashbox_id')} ({$this->model->movement->scenario->direction})",
            'empty' => Strings::SELECT_OPTION,
            'readonly' => $this->lockCashbox || $this->readonly
        ));
        echo "</div>";
        echo "</div>";
    }

    private function _renderColumn2() {

        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->textFieldRow($this->model, 'reference_number', array(
            'id' => $this->reference_number_id,
            'placeholder' => 'N° operación',
            'readonly' => $this->readonly,
            'disabled' => !in_array($this->model->type, [CashboxMovement::PAYMENT_METHOD_CREDIT_CARD, CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT, CashboxMovement::PAYMENT_METHOD_BANK_CHECK]),
            'required' => in_array($this->model->type, [CashboxMovement::PAYMENT_METHOD_CREDIT_CARD, CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT, CashboxMovement::PAYMENT_METHOD_BANK_CHECK]),
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->textFieldRow($this->model, 'credit_card', array(
            'id' => $this->credit_card_id,
            'placeholder' => 'Últ. 4 dígitos',
            'readonly' => $this->readonly,
            'disabled' => $this->model->type !== CashboxMovement::PAYMENT_METHOD_CREDIT_CARD,
            'required' => $this->model->type === CashboxMovement::PAYMENT_METHOD_CREDIT_CARD
        ));
        echo "</div>";
        echo "</div>";
    }

    private function _renderColumn3() {

        echo "<div class='row'>";
        if ($this->has_parent) {
            echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
            echo $this->form->hiddenField($this->model, 'parent_currency', array(
                'id' => $this->parent_currency_input_id,
            ));
            echo $this->form->numberFieldRow($this->model, 'balance_pen', array(
                'id' => $this->balance_pen_input_id,
                'class' => "us-double{$this->fixed}",
                'readonly' => true
            ));
            echo $this->form->numberFieldRow($this->model, 'balance_usd', array(
                'id' => $this->balance_usd_input_id,
                'class' => "us-double{$this->fixed}",
                'readonly' => true
            ));
            echo "</div>";
        }
        $col = $this->has_parent ? 3 : 4;
        echo "<div class='col-lg-{$col} col-md-{$col} col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "real_{$this->model->movement->currency}", array(
            'id' => $this->real_input_id,
            'label' => "Real <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[real]',
            'class' => "sian-treasury-movement-real us-double{$this->fixed}",
            'min' => 0,
            'max' => $this->model->{"balance_{$this->model->movement->currency}"},
            'step' => $this->step,
            'hint' => 'Monto principal',
            'readonly' => true
        ));
        echo "</div>";

        echo "<div class='col-lg-{$col} col-md-{$col} col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "interest_{$this->model->movement->currency}", array(
            'id' => $this->interest_input_id,
            'label' => "Interés <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[interest]',
            'class' => "sian-treasury-movement-interest us-double{$this->fixed}",
            'min' => 0,
            'step' => $this->step,
            'hint' => 'Interés',
            'readonly' => true
        ));
        echo "</div>";

        echo "<div class='col-lg-{$col} col-md-{$col} col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "desgravamen_{$this->model->movement->currency}", array(
            'id' => $this->desgravamen_input_id,
            'label' => "Seguro <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[desgravamen]',
            'class' => "sian-treasury-movement-desgravamen us-double{$this->fixed}",
            'min' => 0,
            'step' => $this->step,
            'hint' => 'Seguro',
            'readonly' => true
        ));
        echo "</div>";
        echo "</div>";
    }

    private function _renderColumn4() {

        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "port_{$this->model->movement->currency}", array(
            'id' => $this->port_input_id,
            'label' => "Porte <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[port]',
            'class' => "sian-treasury-movement-port us-double{$this->fixed}",
            'min' => 0,
            'step' => $this->step,
            'hint' => 'Portes',
            'readonly' => true
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "itf_{$this->model->movement->currency}", array(
            'id' => $this->itf_input_id,
            'label' => "ITF <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[itf]',
            'class' => "sian-treasury-movement-itf us-double{$this->fixed}",
            'min' => 0,
            'step' => $this->step,
            'hint' => 'ITF',
            'readonly' => true
        ));
        echo "</div>";

        $readonly_round = true;
        if ($this->model->is_pay_detraction == 0) {
            if ($this->model->type != CashboxMovement::PAYMENT_METHOD_CASH) {
                $readonly_round = false;
            }
        }

        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "cash_round_{$this->model->movement->currency}", array(
            'id' => $this->cash_round_input_id,
            'label' => ($this->model->movement->direction == Scenario::DIRECTION_IN ? "-" : "+" ) . "Per<span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[cash_round]',
            'class' => "sian-treasury-movement-cash-round us-double{$this->fixed}",
            'min' => 0,
            'step' => $this->step,
            'hint' => 'Redon.',
            'readonly' => $readonly_round
        ));
        echo "</div>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "cash_round_income_{$this->model->movement->currency}", array(
            'id' => $this->cash_round_income_input_id,
            'label' => ($this->model->movement->direction == Scenario::DIRECTION_IN ? "+" : "-" ) . "Gan<span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[cash_round_income]',
            'class' => "sian-treasury-movement-cash-round-income us-double{$this->fixed}",
            'min' => 0,
            'step' => $this->step,
            'hint' => 'Redon.',
            'readonly' => $readonly_round
        ));
        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "total_{$this->model->movement->currency}", array(
            'id' => $this->total_input_id,
            'label' => "Total <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[total]',
            'class' => "sian-treasury-movement-total us-double{$this->fixed}",
            'hint' => 'Monto caja/banco',
            'step' => $this->step,
            'readonly' => true
        ));
        echo "</div>";
        echo "</div>";
    }

    private function _renderColumn5() {
        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->textFieldRow($this->model->movement, 'observation', array(
            'placeholder' => 'Puede escribir una observación acerca del movimiento',
            'required' => $this->model->movement->isObservationRequired()
        ));
        echo "</div>";
        echo "</div>";
    }

    private function _renderColumn6() {
        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'id' => $this->calculate_cash_round_id,
            'model' => $this->model,
            'attribute' => 'calculate_cash_round',
            'switchChange' => "
                $('#{$this->id}').data('calculate_cash_round', this.checked);
                var is_pay_detraction = $('#{$this->is_pay_detraction_input_id}').prop('checked');
                var movement_type = $('#{$this->movement_type_input_id}').val();                    
                if(!is_pay_detraction){
                
                    if(movement_type == '" . CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT . "' || movement_type == '" . CashboxMovement::PAYMENT_METHOD_BANK_CHECK . "'){
                        $('#$this->cash_round_input_id').removeAttr('readonly');
                        $('#$this->cash_round_income_input_id').removeAttr('readonly');
                    }else{
                        $('#$this->cash_round_input_id').attr('readonly');
                        $('#$this->cash_round_income_input_id').attr('readonly');
                    }                    
                    
                }else{
                    $('#$this->cash_round_input_id').attr('readonly');
                    $('#$this->cash_round_income_input_id').attr('readonly');
                }
                //Actualizamos montos
                SIANTreasuryMovementUpdate('{$this->id}');
                ",
            'readonly' => !(($this->model->type == CashboxMovement::PAYMENT_METHOD_CASH && $this->model->is_pay_detraction == 0) || ($this->model->is_pay_detraction == 1 && ($this->model->type == CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT || $this->model->type == CashboxMovement::PAYMENT_METHOD_BANK_CHECK))),
            'hint' => $this->model->is_pay_detraction == 1 ? ($this->model->type == CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT || $this->model->type == CashboxMovement::PAYMENT_METHOD_BANK_CHECK ? 'Redondeará a entero' : 'Redondeo manual') : ($this->model->type == CashboxMovement::PAYMENT_METHOD_CASH ? 'Redondeará el monto en múltiplos de 0.10' : 'Redondeo manual'),
                ), true);
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo $this->widget('application.widgets.USSwitch', array(
            'id' => $this->calculate_change_id,
            'model' => $this->model,
            'attribute' => 'calculate_change',
            'readonly' => !($this->model->type == CashboxMovement::PAYMENT_METHOD_CASH && $this->model->is_pay_detraction == 0),
            'switchChange' => "
                var movement_type = $('#{$this->movement_type_input_id}').val();
                $('#{$this->cash_input_id}').prop('disabled', !this.checked || movement_type !== '" . CashboxMovement::PAYMENT_METHOD_CASH . "');
                //Si se deshabilita reiniciamos
                if(!this.checked)
                {
                    $('#{$this->cash_input_id}').val(0);
                }
                ",
            'hint' => 'Se verá impreso'
                ), true);
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "cash_{$this->model->movement->currency}", array(
            'id' => $this->cash_input_id,
            'label' => "Efectivo <span class='currency-symbol'>{$this->currency_symbol}</span>",
            'name' => 'Amounts[cash]',
            'class' => "sian-treasury-movement-cash us-double{$this->fixed}",
            'step' => $this->step,
            'hint' => 'Monto pagado en efectivo',
            'disabled' => $this->model->calculate_change == 0 || $this->model->type !== CashboxMovement::PAYMENT_METHOD_CASH,
        ));
        echo "</div>";
        echo "</div>";
    }

}
