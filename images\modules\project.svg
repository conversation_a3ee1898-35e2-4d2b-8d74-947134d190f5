<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.3.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 680 619.69" style="enable-background:new 0 0 680 619.69;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FF6B00;}
	.st1{fill:#FFFFFF;}
	.st2{fill:#3DAE2B;}
	.st3{fill:#5F6368;}
	.st4{clip-path:url(#SVGID_4_);}
	.st5{clip-path:url(#SVGID_6_);}
	.st6{fill:none;stroke:#EDEDED;stroke-width:0.5;stroke-miterlimit:2;}
	.st7{fill:#EDEDED;}
	.st8{clip-path:url(#SVGID_8_);}
	.st9{clip-path:url(#SVGID_10_);}
	.st10{fill:#F2F2F2;}
	.st11{fill:#878787;}
	.st12{fill:#706F6F;}
	.st13{fill:#2ECC71;}
	.st14{clip-path:url(#SVGID_22_);}
	.st15{clip-path:url(#SVGID_24_);}
	.st16{clip-path:url(#SVGID_26_);}
	.st17{clip-path:url(#SVGID_28_);}
	.st18{fill:#F6F6F6;}
	.st19{fill:#FF6600;}
	.st20{fill:#575756;}
	.st21{fill:#1F140F;}
	.st22{fill:#48A72D;}
	.st23{fill:#FFFFFF;stroke:#878787;stroke-width:0.25;stroke-miterlimit:2;}
	.st24{clip-path:url(#SVGID_36_);}
	.st25{clip-path:url(#SVGID_38_);}
	.st26{clip-path:url(#SVGID_40_);}
	.st27{clip-path:url(#SVGID_42_);}
	.st28{fill:#27AF5E;}
	.st29{display:none;}
	.st30{display:inline;fill:#E7EAED;}
	.st31{display:inline;}
	.st32{fill:none;}
	.st33{fill:#FFFFFF;stroke:#EDEDED;stroke-miterlimit:10;}
	.st34{fill:#F6F6F6;stroke:#EDEDED;stroke-miterlimit:10;}
	.st35{fill:#FFFFFF;stroke:#DADADA;stroke-miterlimit:10;}
	.st36{fill:#18678E;}
	.st37{fill:#F39C12;}
	.st38{fill:#C0392B;}
	.st39{fill:#2C3E50;}
	.st40{fill:#56A959;}
	.st41{fill:#F5F5F5;}
	.st42{fill:#3498DB;}
	.st43{fill:#AA784D;}
	.st44{fill:#8E44AD;}
	.st45{fill:#7578F6;}
	.st46{fill:#F45F47;}
	.st47{fill:#FA6487;}
	.st48{fill:#FEAA4A;}
	.st49{fill:#32C9DC;}
	.st50{fill:#6A7DE4;}
	.st51{fill:#1D1D1B;}
	.st52{fill:#00983A;}
	.st53{clip-path:url(#SVGID_54_);}
	.st54{clip-path:url(#SVGID_56_);}
	.st55{clip-path:url(#SVGID_58_);}
	.st56{clip-path:url(#SVGID_60_);}
	.st57{fill:#9D9D9C;}
	.st58{fill:#E2E9ED;}
	.st59{clip-path:url(#SVGID_66_);}
	.st60{clip-path:url(#SVGID_68_);}
	.st61{clip-path:url(#SVGID_70_);}
	.st62{clip-path:url(#SVGID_72_);}
	.st63{clip-path:url(#SVGID_82_);}
	.st64{clip-path:url(#SVGID_84_);}
	.st65{clip-path:url(#SVGID_86_);}
	.st66{clip-path:url(#SVGID_88_);}
	.st67{clip-path:url(#SVGID_102_);}
	.st68{clip-path:url(#SVGID_104_);}
	.st69{clip-path:url(#SVGID_106_);}
	.st70{clip-path:url(#SVGID_108_);}
	.st71{fill:#5D70C1;}
	.st72{clip-path:url(#SVGID_122_);}
	.st73{clip-path:url(#SVGID_124_);}
	.st74{clip-path:url(#SVGID_126_);}
	.st75{clip-path:url(#SVGID_128_);}
	.st76{fill:#B17F4A;}
	.st77{fill:#93603B;}
	.st78{fill:#FFFFFF;stroke:#002FF7;stroke-miterlimit:10;}
	.st79{fill:#1D71B8;}
	.st80{fill:none;stroke:#575756;stroke-miterlimit:10;}
	.st81{fill:#DADADA;}
	.st82{fill:#CE5300;}
	.st83{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
</style>
<g>
	<path class="st36" d="M655,619.69H25c-13.81,0-25-11.19-25-25V25C0,11.19,11.19,0,25,0h630c13.81,0,25,11.19,25,25v569.69
		C680,608.5,668.81,619.69,655,619.69z"/>
</g>
<path class="st83" d="M284.91,393.38l5.91-142.73c2.16,2.19,4.63,4.06,7.36,5.54l-2.32,139.12h2.68l7.42-136.17
	c2.27,0.53,4.64,0.81,7.08,0.81c10.21,0,19.28-4.91,24.97-12.5l2.4,20.17c-2.38,4.13-4.29,8.68-5.87,13.24
	c-12.36,35.51-20.16,65.67-23.57,103.19c-0.36,3.96,0.14,7.75,1.33,11.27h7.29c-1.47-3.25-2.17-6.86-1.82-10.65
	c3.44-37.78,10.29-64.43,23.21-101.57c4.59-13.19,11.43-25.39,25.39-25.39h93.91c13.97,0,25.39,11.43,25.39,25.39v67.57h7.37
	c7.43,0,13.51,6.07,13.51,13.5v11.38v21c0,7.43-6.08,13.51-13.51,13.51h-32.76h-5.68H343.14h-34.05c-10.92,0-21.07-7.03-24.36-16.75
	L284.91,393.38L284.91,393.38z M385.02,414.68h57.33v9.83h-57.33V414.68L385.02,414.68z M151.5,276.84h2.54V157.44v-0.35h0.12
	l13.2-39h10.95l10.21,35.19l-8.57,128.59l4.98,6.58h60.02l12.24-11.61h8.52l-30.96,44.85h-52.3L151.5,276.84L151.5,276.84z
	 M160.19,276.84h6.53l-6.53-61.54V276.84L160.19,276.84z M313.03,204.39c-13.46,0-24.36,10.91-24.36,24.36
	c0,13.46,10.9,24.36,24.36,24.36c13.45,0,24.36-10.91,24.36-24.36C337.39,215.3,326.48,204.39,313.03,204.39L313.03,204.39z
	 M298.23,201.3c3.4-1.84,7.18-3.06,11.2-3.52l-123.98-74.17l2.72,9.38L298.23,201.3L298.23,201.3z M281.84,228.79v-0.04
	c0-11.44,6.16-21.44,15.35-26.87l-106.13-58.91l2.82,9.72l-0.47,7.09L281.84,228.79L281.84,228.79z M286.31,431.47h9.83v70.13
	h-53.65C267.1,485,278.34,468.14,286.31,431.47L286.31,431.47z M373.24,269.46c-11.24,0-16.75,9.82-20.44,20.44
	c-10.4,29.9-11.28,40.52-14.05,70.93c-1.02,11.19,9.21,20.44,20.44,20.44h59.29c37.53-0.41,51.61-34.24,50.79-47.62V289.9
	c0-11.23-9.2-20.44-20.44-20.44H373.24L373.24,269.46z M413.44,446.01c10.56,0,19.11,8.55,19.11,19.11
	c0,10.55-8.55,19.11-19.11,19.11c-10.56,0-19.11-8.56-19.11-19.11C394.34,454.56,402.89,446.01,413.44,446.01L413.44,446.01z
	 M488.06,446.01c10.55,0,19.11,8.55,19.11,19.11c0,10.55-8.56,19.11-19.11,19.11c-10.56,0-19.11-8.56-19.11-19.11
	C468.95,454.56,477.5,446.01,488.06,446.01L488.06,446.01z M339.31,446.01c10.55,0,19.11,8.55,19.11,19.11
	c0,10.55-8.56,19.11-19.11,19.11c-10.56,0-19.12-8.56-19.12-19.11C320.2,454.56,328.76,446.01,339.31,446.01L339.31,446.01z
	 M335.36,428.63c-20.07,0-36.49,16.42-36.49,36.49l0,0c0,20.07,16.42,36.49,36.49,36.49h156.65c20.07,0,36.49-16.42,36.49-36.49l0,0
	c0-20.07-16.42-36.49-36.49-36.49H335.36L335.36,428.63z M339.12,438.16c-16.21,0-29.47,12.13-29.47,26.96l0,0
	c0,14.83,13.26,26.96,29.47,26.96h149.14c16.21,0,29.47-12.13,29.47-26.96l0,0c0-14.83-13.26-26.96-29.47-26.96H339.12z"/>
</svg>
