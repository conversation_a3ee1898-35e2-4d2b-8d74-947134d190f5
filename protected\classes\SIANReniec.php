<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANReniec
 *
 * <AUTHOR>
 */
class SIANReniec {

    private static function getDNIFromJNE($p_s_dni) {
        $a_response = USJNE::getDNIData($p_s_dni);

        if ($a_response['code'] !== USREST::CODE_SUCCESS) {
            return $a_response;
        }

        return [
            'code' => $a_response['code'],
            'message' => $a_response['message'],
            'data' => USJNE::processDNIData($a_response['data']),
        ];
    }

    private static function getDNIFromMigo($p_s_dni) {
        $s_token = Yii::app()->params['migo_token'];
        $a_response = USMigo::getDNIData($s_token, $p_s_dni);

        if ($a_response['code'] !== USREST::CODE_SUCCESS) {
            //Si falla se intenta de JNE por si acasp
            return self::getDNIFromJNE($p_s_dni);
        }

        return [
            'code' => $a_response['code'],
            'message' => $a_response['message'],
            'data' => USMigo::processDNIData($a_response['data']),
        ];
    }

    public static function getDNIData($p_s_dni) {
        switch (Yii::app()->params['dni_source']) {
            case YII_DNI_SOURCE_JNE:
                return self::getDNIFromJNE($p_s_dni);
            case YII_DNI_SOURCE_MIGO:
                return self::getDNIFromMigo($p_s_dni);
            default:
                throw new Exception('Origen de datos de DNI desconocido.');
        }
    }

    public static function dniExists($p_i_dni, &$p_a_data = null) {
        $a_result = self::getDNIData($p_i_dni);
        if (isset($a_result['data'])) {
            $p_a_data = $a_result['data'];
        }
        return $a_result['code'] === USREST::CODE_SUCCESS;
    }

}
