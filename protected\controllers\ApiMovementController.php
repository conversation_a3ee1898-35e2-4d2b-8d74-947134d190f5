<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of ApiMovementController
 *
 * <AUTHOR>
 */
class ApiMovementController extends ApiController {

    const CONDITION_COUNTED = 'COUNTED';
    const CONDITION_UPON_DELIVERY = 'UPON_DELIVERY';
    const CONDITION_CREDIT = 'CREDIT';
    const CONDITION_ON_ACCOUNT = 'ON_ACCOUNT';

    protected function _getDefaultOperation($p_i_scenario_id) {
        $service = new OperationService(new OperationRepository());
        return $service->getOperationForApi($p_i_scenario_id, null, null, 1);
    }

    protected function _getOperation($p_i_scenario_id, $p_s_operation_code) {
        $service = new OperationService(new OperationRepository());
        return $service->getOperationForApi($p_i_scenario_id, null, $p_s_operation_code);
    }

    protected function _getDefaultDocument($p_i_scenario_id) {
        $service = new DocumentService(new DocumentRepository());
        return $service->getDocumentForApi($p_i_scenario_id, null, null, null, null, null, 1);
    }

    protected function _getNoSerializedDocument($p_i_scenario_id) {
        $service = new DocumentService(new DocumentRepository());
        return $service->getDocumentForApi($p_i_scenario_id, null, 0);
    }

    protected function _getDocument($p_i_scenario_id, $p_s_sunat_code = null, $p_s_person_type = Document::PERSON_TYPE_ANY) {
        $service = new DocumentService(new DocumentRepository());
        return $service->getDocumentForApi($p_i_scenario_id, null, null, $p_s_sunat_code, null, $p_s_person_type);
    }

    protected function _getDefaultDocumentSerie($p_i_document_id, $p_i_store_id) {
        $service = new DocumentSerieService(new DocumentSerieRepository());
        return $service->getSerieForApi($p_i_document_id, $p_i_store_id);
    }

    protected function _getApiUserVariant($p_i_api_user_variant_id) {

        $a_apiUserVariant = Yii::app()->db->createCommand()->select([
                    "AUV.api_user_variant_id",
                    "AUV.api_user_id",
                    "AU.username",
                    "AUV.store_id",
                    "AUV.warehouse_id",
                    "W.warehouse_name",
                    "AUV.cashbox_id",
                    "C.cashbox_name",
                    "AUV.business_unit_id",
                    "AU.person_id",
                    "AU.seller_id",
                    "AUV.cashbox_variant_count"
                ])
                ->from('api_user_variant AUV')
                ->join('api_user AU', 'AU.api_user_id = AUV.api_user_id')
                ->leftJoin('warehouse W', 'W.warehouse_id = AUV.warehouse_id')
                ->leftJoin('cashbox C', 'C.cashbox_id = AUV.cashbox_id')
                ->where("AUV.api_user_variant_id = :api_user_variant_id", [
                    ':api_user_variant_id' => $p_i_api_user_variant_id,
                ])
                ->queryRow();

        //Verificamos que existe
        $this->exists($a_apiUserVariant, 'Hubo un problema al procesar su petición.');

        //Si dice que debe tener variantes de caja, las traemos
        if ($a_apiUserVariant['cashbox_variant_count'] > 0) {
            $a_apiUserVariant['cashboxVariants'] = $this->_getApiUserCashboxVariants($p_i_api_user_variant_id);
        }

        return $a_apiUserVariant;
    }

    protected function _getApiUserCashboxVariants($p_i_api_user_variant_id) {

        return Yii::app()->db->createCommand()->select([
                            "AUCV.api_user_cashbox_variant_id",
                            "AUCV.type",
                            "AUCV.currency",
                            "AUCV.card_type",
                            "AUCV.cashbox_id",
                            "AUCV.agreement_id",
                            "C.cashbox_name",
                        ])
                        ->from('api_user_cashbox_variant AUCV')
                        ->join('cashbox C', 'C.cashbox_id = AUCV.cashbox_id')
                        ->where("AUCV.api_user_variant_id = :api_user_variant_id", [
                            ':api_user_variant_id' => $p_i_api_user_variant_id,
                        ])
                        ->order('AUCV.type DESC, AUCV.currency DESC, AUCV.card_type DESC')
                        ->queryAll();
    }

    protected function _getAddress($p_s_shipping_type, $p_i_shipping_id) {

        $o_query = Yii::app()->db->createCommand()->select([
                    "A.dept_code",
                    "A.prov_code",
                    "A.dist_code",
                    "G.dept_name",
                    "G.prov_name",
                    "G.dist_name",
                    "A.address",
                    "A.reference",
                ])
                ->from('address A')
                ->join('geoloc G', 'G.dept_code = A.dept_code AND G.prov_code = A.prov_code AND G.dist_code = A.dist_code');

        $a_address = null;
        //SWTICH para el tipo de enví­o
        switch ($p_s_shipping_type) {
            case ExtraInfo::SHIPPING_TYPE_STORE:
                $a_address = $o_query->where("A.`owner` = :owner AND A.owner_id = :owner_id", [
                            ':owner' => Store::OWNER,
                            ':owner_id' => $p_i_shipping_id
                        ])->queryRow();
                //Verificamos si existe
                $this->exists($a_address, 'No se puede reconocer la dirección de la tienda.');
                break;
            case ExtraInfo::SHIPPING_TYPE_DELIVERY:
                $a_address = $o_query->where("A.address_id = :address_id AND A.`owner` = :owner", [
                            ':address_id' => $p_i_shipping_id,
                            ':owner' => WebUser::OWNER
                        ])->queryRow();
                //Verificamos si existe
                $this->exists($a_address, 'No se puede reconocer la dirección de enví­o.');
                break;
            case ExtraInfo::SHIPPING_TYPE_LOCKER:
                $a_address = $o_query->where("A.`owner` = :owner AND A.owner_id = :owner_id", [
                            ':owner' => Locker::OWNER,
                            ':owner_id' => $p_i_shipping_id
                        ])->queryRow();
                //Verificamos si existe
                $this->exists($a_address, 'No se puede reconocer la dirección del locker.');
                break;
            default:
                USREST::sendResponse(USREST::CODE_NOT_FOUND, 'No se puede reconocer el tipo de enví­o.');
                Yii::app()->end();
                break;
        }

        return $a_address;
    }

    protected function _getPaymentMethodLabel($p_s_payment_method) {
        switch ($p_s_payment_method) {
            case ApiUserCashboxVariant::PAYMENT_METHOD_CARD:
                return 'Pago con tarjeta';
            case ApiUserCashboxVariant::PAYMENT_METHOD_DEPOSIT:
                return 'Pago con depósito';
            case ApiUserCashboxVariant::PAYMENT_METHOD_CASH:
                return 'Pago en efectivo';
            case ApiUserCashboxVariant::PAYMENT_METHOD_AGREEMENT:
                return 'Pago en convenio';
            default:
                throw new Exception('Tipo de método de pago no soportado');
        }
    }

    protected function _getShippingService() {
        $a_service = Yii::app()->db->createCommand()
                ->select([
                    'S.product_id',
                    'P.item_type_id',
                    'PD.equivalence',
                    'P.product_type',
                    'UPPER(P.web_name) AS product_name',
                ])
                ->from('service S')
                ->join('product P', 'P.product_id = S.product_id')
                ->join('presentation PD', 'PD.product_id = P.product_id AND PD.`default`')
                ->where('S.product_id = :product_id', [
                    ':product_id' => Service::SHIPPING_COST_ID
                ])
                ->queryRow();
        //Verificamos si existe
        $this->exists($a_service, 'No se ha podido cargar el costo de enví­o');

        return $a_service;
    }

    protected function _getCollectEntry($p_i_movement_id) {
        $o_model = (new DpNotPaidOrCollectedAccounts(DpNotPaidOrCollectedAccounts::SCENARIO_CLIENT_COLLECT_OR_REDEEM))->addEqualCondition('movement_id', $p_i_movement_id)->find();
        $this->exists($o_model, 'Hubo un problema al procesar el pago. Por favor intente más tarde.');
        return $o_model;
    }

    protected function _getPayEntry($p_i_movement_id) {
        $o_model = (new DpNotPaidOrCollectedAccounts(DpNotPaidOrCollectedAccounts::SCENARIO_PROVIDER_PAY))->addEqualCondition('movement_id', $p_i_movement_id)->find();
        $this->exists($o_model, 'Hubo un problema al procesar el pago. Por favor intente más tarde.');
        return $o_model;
    }

    protected function _loadRawPrinter($p_i_raw_printer_id) {
        $o_rawPrinter = RawPrinter::model()->findByAutoIncrement($p_i_raw_printer_id, [
            'select' => [
                'type',
                'columns',
                'line_spacing',
                'default_font',
                'raw_printer_serie',
                'authorization_number',
                'finish_mode',
                'open_cashbox',
                'owner',
                'owner_id'
            ],
            'alias' => 'A',
            'condition' => 'A.owner = :owner',
            'params' => [
                ':owner' => ApiUserVariant::OWNER
            ]
        ]);
        //Verificamos si existe
        $this->exists($o_rawPrinter, 'La impresora especificada no existe');
        //Verificamos si pertenece a la tienda
        if ((int) $o_rawPrinter->owner_id !== (int) $this->apiUserToken->api_user_variant_id) {
            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                'attributes' => [
                    'raw_printer_id' => "La impresora no pertenece al punto de venta"
                ]
            ]);
            Yii::app()->end();
        }
        //
        return $o_rawPrinter;
    }

    protected function _loadUser($p_i_user_id) {
        $o_User = User::model()->findByAutoIncrement($p_i_user_id, [
            'select' => [
                'user_id',
                'username'
            ],
            'with' => [
                'person' => [
                    'select' => [
                        'person_name',
                        'identification_type'
                    ]
                ]
            ],
            'alias' => 'U'
        ]);
        //Verificamos si existe
        $this->exists($o_User, 'El usuario no existe');
        //
        return $o_User;
    }

    protected function _mapPaymentMethod($p_s_payment_method) {
        switch ($p_s_payment_method) {
            case ApiUserCashboxVariant::PAYMENT_METHOD_CASH:
                return CashboxMovement::PAYMENT_METHOD_CASH;
            case ApiUserCashboxVariant::PAYMENT_METHOD_CARD:
                return CashboxMovement::PAYMENT_METHOD_CREDIT_CARD;
            case ApiUserCashboxVariant::PAYMENT_METHOD_DIGITAL_WALLET:
                return CashboxMovement::PAYMENT_METHOD_DIGITAL_WALLET;
            case ApiUserCashboxVariant::PAYMENT_METHOD_DEPOSIT:
                return CashboxMovement::PAYMENT_METHOD_BANK_DEPOSIT;
            case ApiUserCashboxVariant::PAYMENT_METHOD_CREDIT_NOTE:
                return CashboxMovement::PAYMENT_METHOD_OTHER;
            case ApiUserCashboxVariant::PAYMENT_METHOD_AGREEMENT:
                return CashboxMovement::PAYMENT_METHOD_AGREEMENT;
        }
    }

    protected function _mapCondition($p_s_condition) {
        switch ($p_s_condition) {
            case self::CONDITION_COUNTED:
                return Condition::COUNTED;
            case self::CONDITION_CREDIT:
                return Condition::CREDIT;
            case self::CONDITION_UPON_DELIVERY:
                return Condition::UPON_DELIVERY;
            case self::CONDITION_ON_ACCOUNT:
                return Condition::ON_ACCOUNT;
        }
    }

    public static function getCondition($p_s_field) {
        switch ($p_s_field) {
            case Condition::CREDIT:
                return self::CONDITION_CREDIT;
            case Condition::COUNTED:
            case Condition::UPON_DELIVERY:
            case Condition::ON_ACCOUNT:
                return self::CONDITION_COUNTED;
        }
    }

    public static function getConditionSQLCase($p_s_field) {
        return "CASE {$p_s_field} 
            WHEN '" . Condition::COUNTED . "' THEN '" . self::CONDITION_COUNTED . "'
            WHEN '" . Condition::CREDIT . "' THEN '" . self::CONDITION_CREDIT . "'
            WHEN '" . Condition::UPON_DELIVERY . "' THEN '" . self::CONDITION_UPON_DELIVERY . "'
            WHEN '" . Condition::ON_ACCOUNT . "' THEN '" . self::CONDITION_ON_ACCOUNT . "'
            ELSE NULL 
        END";
    }

}
