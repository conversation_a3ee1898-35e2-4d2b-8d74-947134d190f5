//################################################//
// Método para peticiones POST
//################################################//
// Ejemplo implementando el metodo POST:
async function postData(url = '', data = {}) {
    // Opciones por defecto estan marcadas con un *
    const response = await fetch(url, {
        method: 'POST', // *GET, POST, PUT, DELETE, etc.
        mode: 'cors', // no-cors, *cors, same-origin
        cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
        credentials: 'same-origin', // include, *same-origin, omit
        headers: {
            //'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json'
                    // 'Content-Type': 'application/x-www-form-urlencoded',
        },
        redirect: 'follow', // manual, *follow, error
        referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
        body: JSON.stringify(data) // body data type must match "Content-Type" header
    });

    if (response.ok) {
        return response.json(); // parses JSON response into native JavaScript objects 
    } else {
        throw "Error en la llamada Ajax";
}
}
//################################################//
// mostrar las coordenadas de un elemento
//################################################//
const showCoordinatesOfAnElement = (element) => {
    let coords = element.getBoundingClientRect();
    console.log('distancia al borde superior del viewport:', coords.top);
    console.log('distancia al borde derecho del viewport:', coords.right);
    console.log('distancia al borde inferior del viewport:', coords.bottom);
    console.log('distancia al borde izquierdo del viewport:', coords.left);

    console.log('ancho del elemento:', coords.width);
    console.log('alto del elemento:', coords.height);

    console.log('distancia del borde derecho del elemento al borde derecho del viewport:', coords.right - coords.width);
    console.log('distancia del borde inferior del elemento al borde inferior del viewport:', coords.bottom - coords.height);

    console.log('distancia del borde superior del elemento al inicio del documento (cuando se ha hecho scroll):', coords.top + scrollY);
}
//################################################//
// Posicionar un elemento
//################################################//
const positionAnElement = (element, x_pos, y_pos) => {
    element.style.position = "absolute";
    element.style.left = x_pos + 'px';
    element.style.top = y_pos + 'px';
}
//################################################//
//################################################//
// Redimensionar el Footer (start)
//################################################//
const resizeFooter = () => {
    // console.log("resizeFooter");
    // var canvas = document.getElementById('canvas');
    let navbarPage = document.querySelector('.navbar-sian');
    let coordsNavbar = navbarPage.getBoundingClientRect();
    let pagePrincipal = document.querySelector('#page');
    //alineamos el contenido de la página, en el nivel en donde termina el header
    pagePrincipal.style.marginTop = coordsNavbar.height + 'px';

    let pageHeight = document.documentElement.scrollHeight;
    let windowHeight = window.innerHeight;

    let SianBody = document.querySelector('#SianBody');
    let coordsBody = SianBody.getBoundingClientRect();

    pagePrincipal = document.querySelector('#page');
    let coordsPage = pagePrincipal.getBoundingClientRect();

    let contentPage = document.querySelector('#SianContent');
    let coordsContent = contentPage.getBoundingClientRect();

    let footerPage = document.querySelector('#SianFooter');
    let coordsFooter = footerPage.getBoundingClientRect();

    // canvas.style.left = 0+'px'; 
    // canvas.style.top = 0+'px';

    // distancia del borde inferior del footer al inicio del documento (cuando se ha hecho scroll)
    let heightToValidate = Math.ceil(coordsFooter.bottom + scrollY);
    let contentHeightToValidate = pageHeight - coordsFooter.height;

    // console.log(pageHeight+" <= "+windowHeight);
    //    if (pageHeight <= windowHeight) {
    //        // console.log("Es menor");
    //        // canvas.style.height = (heightToValidate-coordsfooter.height)+'px';
    //        // console.log("--Level 2");
    //        // console.log("--"+heightToValidate+" <= "+pageHeight);
    //        if (heightToValidate <= pageHeight) {
    //            console.log("--Es menor");
    //            footerPage.classList.add('footer-abs-bottom');
    //            if (coordsFooter.top < coordsContent.bottom) {
    //                console.log("-- other");
    //                footerPage.removeClass("footer-abs-bottom");
    //            }
    //        } else {
    //            console.log("-- NO Es menor");
    //            footerPage.classList.remove('footer-abs-bottom');
    //        }
    //    } else {
    //        console.log(`pageHeight ${pageHeight} > windowHeight ${windowHeight}`);
    //        footerPage.classList.remove('footer-abs-bottom');
    //    }
    //LNH
    if (pageHeight > windowHeight) {
        console.log(`heightVal ${heightToValidate} pageH ${pageHeight} > windowH ${windowHeight}`);
        footerPage.classList.remove('footer-abs-bottom');
        return;
    }

    footerPage.classList.add('footer-abs-bottom');

//    if (heightToValidate <= pageHeight) {
//        console.log("--Es menor");
//        footerPage.classList.add('footer-abs-bottom');
//        if (coordsFooter.top < coordsContent.bottom) {
//            console.log("-- other");
//            footerPage.removeClass("footer-abs-bottom");
//        }
//    } else {
//        console.log("-- NO Es menor");
//        footerPage.classList.remove('footer-abs-bottom');
//    }
}
//################################################//
// Redimensionar el Footer (end)
//################################################//
