/**
 * Lithuanian translation for bootstrap-datepicker
 * <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 */

;(function($){
    $.fn.datepicker.dates['lt'] = {
        days: ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pirmadienis", "<PERSON>tra<PERSON><PERSON>", "Trečiadienis", "Ketvirtadien<PERSON>", "Penktadienis", "Šeštadien<PERSON>", "Sekmadien<PERSON>"],
        daysShort: ["S", "Pr", "A", "T", "K", "Pn", "Š", "S"],
        daysMin: ["Sk", "Pr", "An", "Tr", "Ke", "Pn", "Št", "Sk"],
        months: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "G<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>ji<PERSON>", "<PERSON><PERSON>", "<PERSON>p<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"],
        monthsShort: ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>ugp", "R<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"],
        today: "Šiandien",
        weekStart: 1
    };
}(jQuery));
