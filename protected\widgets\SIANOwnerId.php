<?php

class SIANOwnerId extends CWidget {

    public $id;
    public $form;
    public $model;
    public $owner_attribute;
    public $key_attribute;
    public $text_attribute;
    public $key_name;
    public $text_name;
    public $owner_items = [];
    public $dps = [];
    public $required = null;
    public $readonly = false;
    public $minimumInputLength = 2;
    //PRIVATE
    private $controller;
    private $owner_input_id;
    private $owner_container_idx;
    private $owner_span_idx;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //
        $this->owner_input_id = $this->controller->getServerId();
        $this->owner_container_idx = $this->controller->getServerId();
        $this->owner_span_idx = $this->controller->getServerId();
        //NULOS
        if (!isset($this->required)) {
            $this->required = $this->model->isAttributeRequired($this->key_attribute);
        }
        if (!isset($this->key_name)) {
            $this->key_name = get_class($this->model) . '[' . $this->key_attribute . ']';
        }
        if (!isset($this->text_name)) {
            $this->text_name = get_class($this->model) . '[' . $this->text_attribute . ']';
        }
        //Registramos assets
        SIANAssets::registerScriptFile('js/us-span.js');
        SIANAssets::registerScriptFile('other/select2/js/select2.min.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
        SIANAssets::registerScriptFile('js/us-select2.js');
        //
        SIANAssets::registerCssFile('other/select2/css/select2.css');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id, "
            
        $(document).ready(function() {                  
            //Owner
            $('#{$this->id}').data('dps', " . CJSON::encode($this->dps) . ");
            //
            var spanHtml = USSpanInit('{$this->owner_span_idx}', '', '{$this->model->{$this->key_attribute}}', '{$this->model->{$this->text_attribute}}', '{$this->key_name}', '{$this->text_name}', '{$this->model->getAttributeLabel($this->key_attribute)}', [], '{$this->model->getError($this->key_attribute)}', " . CJSON::encode($this->required) . ", " . CJSON::encode($this->readonly) . ");
            $('#{$this->owner_container_idx}').html(spanHtml);
        });
        
        $('body').on('click', '#{$this->owner_span_idx}', function(e) {

            var divObj = $('#{$this->id}');
            var dps = divObj.data('dps');
            //Obtenemos owner
            var owner = $('#{$this->owner_input_id}').val();
                    
            USSelect2Init('{$this->owner_span_idx}', dps[owner].dp, dps[owner].scenario, [], {$this->minimumInputLength});

        });
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}' class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, $this->owner_attribute, $this->owner_items, array(
            'id' => $this->owner_input_id,
            'empty' => Strings::SELECT_OPTION,
            'onchange' => "
                //Limpiamos
                USSpanClear('{$this->owner_span_idx}');
            "
        ));
        echo "</div>";
        echo "<div id='{$this->owner_container_idx}' class='col-lg-8 col-md-8 col-sm-12 col-xs-12'>";
        echo "</div>";
        //
        echo "</div>";
    }

}
