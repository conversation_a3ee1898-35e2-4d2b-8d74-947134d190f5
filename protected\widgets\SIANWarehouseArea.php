<?php

class SIANWarehouseArea extends CWidget {

    public $id;
    public $model;
    public $title = 'Objetos enlazados';
    public $viewClass = null;
    public $viewScenario = null;
    public $viewParams = null;
    public $extraViewAttributes = [];
    public $owner = null;
    public $input_name = null;
    public $property = null;
    public $modal_id = null;
    public $readonly = false;
    public $exclude_ids = [];
    public $limit = false;
    public $warehouse_id = null;
    //PRIVATE
    private $controller;
    private $preview_route = null;
    private $viewAttributes = [];
    //IDS
    private $autocomplete_id;
    private $warehouse_area_id_input_id;
    private $warehouse_area_name_input_id;
    private $search_code_input_id;
    private $add_button_id;

    public function init() {

        //CONTROL
        if (!isset($this->property)) {
            throw new Exception('Debe especificar la propiedad.');
        }

        if (!isset($this->input_name)) {
            $this->input_name = 'MerchandiseMasterAreas';
        }

        $this->controller = Yii::app()->controller;

        //IDS
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->autocomplete_id = $this->controller->getServerId();
        $this->warehouse_area_id_input_id = $this->controller->getServerId();
        $this->warehouse_area_name_input_id = $this->controller->getServerId();
        $this->search_code_input_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();

        $this->preview_route = "/logistic/warehouseArea/preview";
        //Recorremos los ítems
        $a_items = [];
        foreach ($this->model->{$this->property} as $item) {
            $a_attributes = [];
            $a_attributes['warehouse_area_id'] = $item->warehouse_area_id;
            $a_attributes['warehouse_area_name'] = (isset($item->warehouse_area_name)) ? $item->warehouse_area_name : '';
            $a_attributes['search_code'] = (isset($item->search_code)) ? $item->search_code : '';
            $a_attributes['default'] = $item->default;
            $a_attributes['errors'] = $item->getAllErrors();

            $a_items[] = $a_attributes;
        }

        //Atributos extra
        $i_extra_width = 0;
        foreach ($this->extraViewAttributes as $a_extra) {
            $i_extra_width += isset($a_extra['width']) ? $a_extra['width'] : 0;
        }

        //Atributos
        $this->viewAttributes = array(
            array(
                'name' => 'warehouse_area_id',
                'width' => floor((100 - $i_extra_width) * 0.2),
                'update' => "$('#{$this->warehouse_area_id_input_id}').val(warehouse_area_id);",
                'types' => array('id', 'value'),
                'not_in' => "window.sianWarehouseAreaIds.{$this->id}"
            ),
            array(
                'name' => 'search_code',
                'width' => floor((100 - $i_extra_width) * 0.2),
                'update' => "$('#{$this->search_code_input_id}').val(search_code);",
                'types' => array('aux')
            ),
            array(
                'name' => 'warehouse_area_name',
                'width' => floor((100 - $i_extra_width) * 0.6),
                'update' => "$('#{$this->warehouse_area_name_input_id}').val(warehouse_area_name);",
                'types' => array('text')
            ),
        );
        //Agregamos atributos extra
        foreach ($this->extraViewAttributes as $a_extra) {
            $this->viewAttributes[] = $a_extra;
        }

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-warehouse-area.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id, "

        $(document).ready(function() {                  

            //COUNT y LIMIT
            var tableObj = $('#{$this->id}');
            tableObj.data('count', 0);
            tableObj.data('preview_url', '{$this->controller->createUrl($this->preview_route)}');
            tableObj.data('access_preview', " . CJSON::encode($this->controller->checkRoute($this->preview_route)) . ");
            tableObj.data('modal_id', '{$this->modal_id}');
            tableObj.data('input_name', '{$this->input_name}');
            tableObj.data('readonly', " . CJSON::encode($this->readonly) . ");
            tableObj.data('exclude_ids', " . CJSON::encode($this->exclude_ids) . ");
            tableObj.data('limit', " . CJSON::encode($this->limit) . ");

            var array = " . CJSON::encode($a_items) . ";

            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {
                    SIANWarehouseAreaAddItem('{$this->id}', array[i]['warehouse_area_id'], array[i]['warehouse_area_name'], array[i]['search_code'], array[i]['default'], array[i]['errors']);
                }
            }
            else
            {
                tableObj.find('tbody').html('<tr><td class=\'empty\' colspan=\'10\'>" . Strings::NO_DATA . "</td></tr>');
            }
            
            SIANWarehouseAreaGetIds('{$this->id}');
            SIANWarehouseAreaSortable('{$this->id}');
        });
        
        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var tableObj = $('#{$this->id}');
            var count = tableObj.data('count');

            var warehouse_area_id = $('#{$this->warehouse_area_id_input_id}').val();
            var warehouse_area_name = $('#{$this->warehouse_area_name_input_id}').val();
            var search_code = $('#{$this->search_code_input_id}').val();
            var xdefault = (count == 0);
            
            if(isBlank(warehouse_area_id))
            {
                bootbox.alert(us_message('Debe elegir un(a) ubicación!', 'warning'));
                USAutocompleteFocus('{$this->autocomplete_id}');
                return;
            }

            SIANWarehouseAreaAddItem('{$this->id}', warehouse_area_id, warehouse_area_name, search_code, xdefault, []);
            USAutocompleteReset('{$this->autocomplete_id}');
            SIANWarehouseAreaGetIds('{$this->id}');
        });
        
        $('body').on('click', '#{$this->id} input.sian-warehouse-area-item-default', function() {
            $('#{$this->id} input[type=radio]:checked').not(this).prop('checked', false);
        });
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "{$this->title}" . (isset($this->aclaration) ? ' *' : ''),
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors($this->property) ? 'us-error' : ''
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-9 col-md-9 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->autocomplete_id,
            'name' => 'warehouseAreas',
            'label' => 'Ubicaciones',
            'parent_id' => $this->modal_id,
            'view' => array(
                'model' => $this->viewClass,
                'scenario' => $this->viewScenario,
                'params' => $this->viewParams,
                'attributes' => $this->viewAttributes
            ),
            'readonly' => $this->readonly,
            'onreset' => "
                $('#{$this->warehouse_area_id_input_id}').val('');
                $('#{$this->warehouse_area_name_input_id}').val('');
                $('#{$this->search_code_input_id}').val('');
            ",
            'maintenance' => array(
                'module' => 'logistic',
                'controller' => 'WarehouseArea',
            )
        ));
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->warehouse_area_id_input_id,
        ));
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->warehouse_area_name_input_id,
        ));
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->search_code_input_id,
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo CHtml::label('Agregar', $this->add_button_id, array(
        ));
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_button_id,
            'context' => 'primary',
            'icon' => 'fa fa-lg fa-plus white',
            'size' => 'default',
            'title' => 'Añadir',
            'block' => true,
        ));
        echo "</div>";
        echo "</div>";

        echo "<hr>";

        echo CHtml::tag('table', array(
            'id' => $this->id,
            'class' => 'table table-condensed table-hover',
                ), $this->renderTable(), true);

        $this->endWidget();
    }

    private function renderTable() {

        $o_model = new $this->viewClass($this->viewScenario);

        $html = '';
        //Según el modo
        $html .= "<thead>";
        $html .= "<tr>";
        $html .= "<th width='5%'>#</th>";
        $html .= "<th width='15%'>Código Búsq.</th>";
        $html .= "<th width='55%'>{$o_model->getAttributeLabel('warehouse_area_name')}</th>";
        $html .= "<th width='15%'>Pred?</th>";
        $html .= "<th width='10%'>Opciones</th>";
        $html .= "</tr>";
        $html .= "</thead>";
        $html .= "<tbody></tbody>";
        $html .= "<tfoot>";
        $html .= "</tfoot>";

        return $html;
    }

}
