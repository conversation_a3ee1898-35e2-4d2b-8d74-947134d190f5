<?php

// class Anulacion extends DocumentContract
class Annulment extends DocumentContract
{
    protected $data;

    public function __construct()
    {
        $this->data['operacion'] = 'generar_anulacion';
    }

    /**
     * ATRIBUTO: tipo_de_comprobante
     * VALOR: Tipo de COMPROBANTE que desea generar: 1 = FACTURA, 2 = BOLETA, 3 = NOTA DE CRÉDITO, 4 = NOTA DE DÉBITO
     * TIPO DE DATO: Integer
     * REQUISITO: Obligatorio
     * LONGITUD: 1 exacto
     */
    public function setVoucherType($i_voucher_type)
    {
        $voucher_type = isset($i_voucher_type) ? $i_voucher_type : null;
        if (is_null($voucher_type)) {
            throw new Exception("El Tipo de Comprobante es obligatorio");
        }
        $this->data['tipo_de_comprobante'] = $voucher_type;
    }

    /**
     * ATRIBUTO: serie
     * VALOR: Empieza con "F" para FACTURAS y NOTAS ASOCIADAS.
     *   Empieza con "B" para BOLETAS DE VENTA y NOTAS ASOCIADAS
     *   Si está comunicando un comprobante emitido en contingencia, la serie debe empezar NO debe empezar con "F" ni con "B". Debería empezar con "0", ejemplo: "0001"
     * TIPO DE DATO: String
     * REQUISITO: Obligatorio
     * LONGITUD: 4 exactos
     */
    public function setSeries($s_serie)
    {
        $serie = isset($s_serie) ? $s_serie : "";
        if ($serie === "") {
            throw new Exception("La Serie es requerida");
        }
        $this->data['serie'] = $serie;
    }

    /**
     * ATRIBUTO: número
     * VALOR: Número correlativo del documento, sin ceros a la izquierda
     * TIPO DE DATO: Integer
     * REQUISITO: Obligatorio
     * LONGITUD: 1 hasta 8
     */
    public function setCorrelativeNumber($s_correlative_number)
    {
        $correlative_number = isset($s_correlative_number) ? $s_correlative_number : "";
        if ($correlative_number === "") {
            throw new Exception("El número de correlativo es requerido");
        }
        $this->data['numero'] = $correlative_number;
    }

    /**
     * Motivo de anulación, ejemplo: "ERROR DE SISTEMA"
     */
    public function setReason($reason_annulment)
    {
        $this->data['motivo'] = $reason_annulment;
    }

    /**
     * ATRIBUTO: codigo_unico
     * VALOR: Usarlo sólo si deseas que controlemos la generación de documentos. Código único generado y asignado por tu sistema. Por ejemplo puede estar compuesto por el tipo de documento, serie y número correlativo.
     * TIPO DE DATO: String
     * REQUISITO: Opcional
     * LONGITUD: 1 hasta 20
     */
    public function setUniqueCode($s_code)
    {
        $this->data['codigo_unico'] = $s_code;
    }

    function getArray()
    {
        return $this->data;
    }
}