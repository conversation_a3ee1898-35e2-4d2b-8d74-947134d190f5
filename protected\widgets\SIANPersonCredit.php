<?php

class SIANPersonCredit extends CWidget {

    public $form;
    public $model;
    //IDS
    public $trusted_id;
    public $client_line_pen_id;
    public $client_line_usd_id;
    public $client_credit_days_id;
    public $provider_line_pen_id;
    public $provider_line_usd_id;
    public $provider_credit_days_id;
    //PRIVATE
    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;

        //IDS
        $this->trusted_id = isset($this->trusted_id) ? $this->trusted_id : $this->controller->getServerId();
        $this->client_line_pen_id = isset($this->client_line_pen_id) ? $this->client_line_pen_id : $this->controller->getServerId();
        $this->client_line_usd_id = isset($this->client_line_usd_id) ? $this->client_line_usd_id : $this->controller->getServerId();
        $this->client_credit_days_id = isset($this->client_credit_days_id) ? $this->client_credit_days_id : $this->controller->getServerId();
        $this->provider_line_pen_id = isset($this->provider_line_pen_id) ? $this->provider_line_pen_id : $this->controller->getServerId();
        $this->provider_line_usd_id = isset($this->provider_line_usd_id) ? $this->provider_line_usd_id : $this->controller->getServerId();
        $this->provider_credit_days_id = isset($this->provider_credit_days_id) ? $this->provider_credit_days_id : $this->controller->getServerId();
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Campos básicos',
            'headerIcon' => 'certificate',
        ));

        echo '<div class="row">';
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-2'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'is_trusted',
            'hint' => 'Permite facturar a cliente con deuda vencida.',
        ));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-2'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'is_gov',
            'hint' => 'Indica si es una entidad del estado.'
        ));
        echo "</div>";
        echo '</div>';

        $this->endWidget();


        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Info. Créd. (Cliente)',
            'headerIcon' => 'dollar',
            'htmlOptions' => array(
                'class' => 'sian-advanced'
            )
        ));
        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'client_credit',
            'switchChange' => "$('#{$this->client_line_pen_id}, #{$this->client_line_usd_id}').attr('readonly', !this.checked);$('#{$this->client_credit_days_id}').attr('readonly', !this.checked);"
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "client_line_pen", array(
            'id' => $this->client_line_pen_id,
            'readonly' => !$this->model->client_credit,
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "client_line_usd", array(
            'id' => $this->client_line_usd_id,
            'readonly' => !$this->model->client_credit,
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, 'client_credit_days', array(
            'id' => $this->client_credit_days_id, 
            'readonly' => !$this->model->client_credit, 'min' => 0, 'max' => 365));
        echo "</div>";
        echo "</div>";
        $this->endWidget();
        echo "</div>";
        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Info. Créd. (Proveedor)',
            'headerIcon' => 'dollar',
            'htmlOptions' => array(
                'class' => 'sian-advanced'
            )
        ));
        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'provider_credit',
            'switchChange' => "$('#{$this->provider_line_pen_id}, #{$this->provider_line_usd_id}').attr('readonly', !this.checked);$('#{$this->provider_credit_days_id}').attr('readonly', !this.checked);"
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "provider_line_pen", array(
            'id' => $this->provider_line_pen_id,
            'readonly' => !$this->model->provider_credit,
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "provider_line_usd", array(
            'id' => $this->provider_line_usd_id,
            'readonly' => !$this->model->provider_credit,
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, 'provider_credit_days', array(
            'id' => $this->provider_credit_days_id, 
            'readonly' => !$this->model->provider_credit, 'min' => 0, 'max' => 365));
        echo "</div>";
        echo "</div>";
        $this->endWidget();
        echo "</div>";
        echo "</div>";
    }

}
