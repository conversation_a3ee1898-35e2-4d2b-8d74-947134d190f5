<?php

class SIANTabFrame extends CWidget {

    public $id;
    public $items = [];
    public $scrolling = 0;
    //PRIVATE
    private $clientScript;
    private $controller;
    private $normalized = [];

    public function init() {

        $this->clientScript = Yii::app()->clientScript;
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //Recorremos los ítems
        $this->_normalize();
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-tab-frame.js');

        //SCRIPTS
        $this->clientScript->registerScript($this->controller->getServerId(), "

        $(document).ready(function() {     
            var targetObj = $('#{$this->id} li.active').find('a');
            SIANTabFrameLoadContent(targetObj, " . CJSON::encode($this->scrolling) . "); 
                    
            $('body').on('shown.bs.tab', 'a[data-toggle=\'tab\']', function (e) {
                var id = $(this).attr('id');
                var o_frame = $('#' + id + '_frame').fitToContent();;
            });
        });
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->widget('booster.widgets.TbTabs', array(
            'id' => $this->id,
            'type' => 'tabs', // 'tabs' or 'pills'
            'tabs' => $this->normalized,
        ));
    }

    /**
     * Obtiene el índice de la pestaña activa
     * @return int Índice de la pestaña activa
     */
    private function _getActive() {

        $a_actives = [];
        //Recorremos para llenar todas las pestañas que tienen active = true
        foreach ($this->items as $i => $a_item) {
            if (isset($a_item['active']) && $a_item['active'] == true) {
                $a_actives[] = $i;
            }
        }

        $i_count = count($a_actives);
        //De las obtenidas retornamos el último active = true
        if ($i_count === 0) {
            return 0;
        } else {
            if ($i_count === 1) {
                return $a_actives[0];
            } else {
                return $a_actives[$i_count - 1];
            }
        }
    }

    private function _normalize() {

        $p_i_active = $this->_getActive();

        foreach ($this->items as $i => $a_item) {

            $a_params = isset($a_item['params']) ? $a_item['params'] : [];

            if (isset($a_item['route'])) {
                $this->normalized[] = [
                    'icon' => $a_item['icon'],
                    'label' => $a_item['label'],
                    'content' => Strings::LOADING,
                    'active' => $i === $p_i_active,
                    'linkOptions' => [
                        'id' => 'tab' . $i,
                        'url' => $this->controller->createUrl($a_item['route'], $a_params),
                        'onclick' => 'SIANTabFrameLoadContent($(this), ' . CJSON::encode($this->scrolling) . ')'
                    ],
                ];
            } else {
                $this->normalized[] = [
                    'icon' => $a_item['icon'],
                    'label' => $a_item['label'],
                    'content' => isset($a_item['content']) ? $a_item['content'] : Strings::NO_DATA,
                    'active' => $i === $p_i_active,
                    'linkOptions' => [
                        'id' => 'tab' . $i,
                        'data-loaded' => true
                    ],
                ];
            }
        }
    }

}
