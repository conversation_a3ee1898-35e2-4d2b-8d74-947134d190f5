<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of ApiController
 *
 * <AUTHOR>
 * http://www.yiiframework.com/wiki/175/how-to-create-a-rest-api/
 * https://www.restapitutorial.com/httpstatuscodes.html
 */
class ApiController extends USBaseController {

    const TOKEN_TYPE_PROXY = 'Proxy';
    const TOKEN_TYPE_USER = 'User';

    //
    public $dp_class = '';
    public $m_class = '';
    public $sync_owner = '';
    public $dp_scenario = null;
    //CONTROL
    public $apiUserToken = null;

    // Members

    /**
     * Key which has to be in HTTP USERNAME and PASSWORD headers 
     */
    //
    const AUTH_CODE_SUCCESS = 1;
    const AUTH_CODE_INVALID_USERNAME = 1;
    const AUTH_CODE_INCORRECT_PASSWORD = 2;
    //
    const FILTER_TYPE_EQUAL = 'equal';
    const FILTER_TYPE_NOT_EQUAL = 'not_equal';
    const FILTER_TYPE_LIKE = 'like';
    const FILTER_TYPE_IN = 'in';
    const FILTER_TYPE_NOT_IN = 'not_in';
    const FILTER_TYPE_MATH = 'math';
//    const FILTER_TYPE_RAW = 'raw';
    const FILTER_TYPE_SEARCH = 'search';
    const FILTER_TYPE_NULL = 'null';
    const FILTER_TYPE_NOT_NULL = 'not_null';
    //
    const FILTER_MATH_EQUAL = 'equal';
    const FILTER_MATH_MINOR = 'minor';
    const FILTER_MATH_MAJOR = 'major';
    const FILTER_MATH_MINOR_EQUAL = 'minor_equal';
    const FILTER_MATH_MAJOR_EQUAL = 'major_equal';

//    /**
//     * @return array action filters
//     */
    public function filters() {
        return [
            'accessControl'
        ];
    }

//    public function beforeAction($action) {
//        //Chequeamos autorización
//        if (!(in_array($this->module->id, ['api', 'apiPos']) && $this->id === 'signin')) {
//            $thisbeforeRestCreate->checkAppAuth();
//        }
//
//        return parent::beforeAction($action);
//    }

    public function actionRestList($id2 = null, $id3 = null) {

        try {
            $a_required = $this->listRequiredAttributes();

            $a_default = $this->listDefaultAttributes();

            $a_filtered = $this->getAndFilterAttributes($a_required, $a_default, ['id2', 'id3']);

            //Inicializamos
            $o_model = (new $this->dp_class($this->dp_scenario));
            //Verificamos filtros
            $a_filters = $a_filtered['filters'];
            $this->verifyFilters($a_filters, $id2, $id3);
            //Verificamos page and limit
            $this->_verifyCount($a_filtered['count']);
            $this->_verifyPage($a_filtered['page']);
            $this->_verifyLimit($a_filtered['limit']);
            //Verificamos ordenamiento
            $a_order = $this->_getOrder($o_model, $a_filtered);
            //Verificamos atributos
            $this->beforeRestList($o_model, $a_filtered, $id2, $id3);
            //Procesamos filtros
            $this->_processFilters($o_model, $a_filters);
            //Obtenemos atributos permitidos
            $a_allowed = $this->getListAllowedAttributes();
            $a_response = $o_model->setOrder($a_order)->findPage($a_filtered['page'], $a_filtered['limit'], $a_filtered['count'] == 1, true, $a_allowed);
            //Procesamos lo que pasa después de la búsqueda
            $this->afterRestList($a_response, $a_filtered, $id2, $id3);
            //Enviamos respuesta
            USREST::sendResponse(USREST::CODE_SUCCESS, '', $a_response);
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }

    public function actionRestSyncCount() {

        try {
            $a_required = [
                'last_sync' => true,
            ];

            $a_default = [
                'last_sync' => true,
            ];

            $a_attributes = $this->getAndFilterAttributes($a_required, $a_default, []);
            //Inicializamos
            $i_count = $this->_countLastChanges($this->sync_owner, $a_attributes['last_sync']);
            //Enviamos respuesta
            USREST::sendResponse(USREST::CODE_SUCCESS, '', [
                'count' => $i_count
            ]);
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }

    private function _processChangeIds(&$p_a_data, $p_a_change_ids, $p_s_action, $p_a_attributes) {
        if (count($p_a_change_ids[$p_s_action]) > 0) {

            switch ($p_s_action) {
                case 'created':
                case 'updated':
                    $a_loaded = $this->loadSync($p_a_change_ids[$p_s_action], $p_a_attributes['params']);
                    if ($a_loaded === false) {
                        USREST::sendResponse(USREST::CODE_NOT_IMPLEMENTED, sprintf('El modo de <b>sincronización</b> no está implementado para el objeto <b>%s</b>', $this->id));
                        Yii::app()->end();
                    }
                    //Procesamos
                    foreach ($a_loaded as $s_owner => $a_items) {
                        foreach ($a_items as $i_id => $a_item) {
                            $p_a_data[$s_owner][$p_s_action][] = $a_item;
                            $p_a_data[$s_owner]['ids'][] = $i_id;
                        }
                    }
                    break;
                case 'deleted':
                    $p_a_data[$this->sync_owner]['deteled'] = $p_a_change_ids[$p_s_action];
                    foreach ($p_a_change_ids[$p_s_action] as $i_id) {
                        $p_a_data[$this->sync_owner]['ids'][] = $i_id;
                    }
                    break;
            }
        }
    }

    public function actionRestSync() {

        try {
            $a_required = [
                'last_sync' => true,
                'page' => true,
                'limit' => true,
                'params' => false
            ];

            $a_default = [
                'count' => 1,
                'params' => []
            ];

            $a_attributes = $this->getAndFilterAttributes($a_required, $a_default, []);
            //Obtenemos ID's
            $a_change_ids = $this->_getChangeIds($this->sync_owner, $a_attributes['last_sync'], $a_attributes['page'], $a_attributes['limit']);
            //Creamos data
            $a_data = [];
            //Procesamos creados
            $this->_processChangeIds($a_data, $a_change_ids, 'created', $a_attributes);
            //Procesamos actualizados
            $this->_processChangeIds($a_data, $a_change_ids, 'updated', $a_attributes);
            //Procesamos eliminados
            $this->_processChangeIds($a_data, $a_change_ids, 'deleted', $a_attributes);
            //Enviamos respuesta
            USREST::sendResponse(USREST::CODE_SUCCESS, '', $a_data);
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }

    protected function loadSync($p_a_owner_ids, $p_a_params) {
        return false;
    }

    protected function loadView(&$p_a_filtered, $p_i_id, $p_i_id2 = null, $p_i_id3 = null) {
        return false;
    }

    protected function loadSecret($p_s_secret, $p_a_params) {
        return false;
    }

    public function actionRestView($id, $id2 = null, $id3 = null) {

        try {
            //Atributos permitidos
            $a_required = $this->viewRequiredAttributes();
            //Defecto
            $a_default = $this->viewDefaultAttributes();
            //Verificamos
            $a_filtered = $this->getAndFilterAttributes($a_required, $a_default, ['id', 'id2', 'id3']);
            //Instanciamos            
            $a_data = $this->loadView($a_filtered, $id, $id2, $id3);
            //
            if ($a_data === false) {
                USREST::sendResponse(USREST::CODE_NOT_IMPLEMENTED, sprintf('El modo de <b>visualización</b> no está implementado para el objeto <b>%s</b>', $this->id));
                Yii::app()->end();
            }
            // Did we find the requested model? If not, raise an error
            if (is_null($a_data)) {
                USREST::sendResponse(USREST::CODE_NOT_FOUND, 'No se encontró ningún registro con el ID ' . $id);
            } else {
                USREST::sendResponse(USREST::CODE_SUCCESS, '', $a_data);
            }
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }

    public function actionRestSecret($secret) {

        try {
            //Atributos permitidos
            $a_required = $this->secretRequiredAttributes();
            //Defecto
            $a_default = $this->secretDefaultAttributes();
            //Unset
            $a_unset = ['secret'];
            //Verificamos
            $a_filtered = $this->getAndFilterAttributes($a_required, $a_default, $a_unset);
            //Instanciamos            
            $a_data = $this->loadSecret($secret, $a_filtered);

            if ($a_data === false) {
                USREST::sendResponse(USREST::CODE_NOT_IMPLEMENTED, sprintf('El modo de <b>secreto</b> no está implementado para el objeto <b>%s</b>', $this->id));
                Yii::app()->end();
            }

            // Did we find the requested model? If not, raise an error
            if (is_null($a_data)) {
                USREST::sendResponse(USREST::CODE_NOT_FOUND, 'No se encontró ningún registro con el Secreto ' . $secret);
            } else {
                USREST::sendResponse(USREST::CODE_SUCCESS, '', $a_data);
            }
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }

    public function actionRestCreate($id2 = null, $id3 = null) {

        try {
            //Atributos permitidos
            $a_required = $this->createRequiredAttributes();
            //Atributos por defecto
            $a_default = $this->createDefaultAttributes();
            //Verificamos
            $a_filtered = $this->getAndFilterAttributes($a_required, $a_default, ['id2', 'id3']);
            //Vefificamos parámetros
            $this->createAttributesVerification($a_filtered);
            //Creamos
            $o_model = $this->loadCreate($a_filtered, $id2, $id3);
            //Cargamos modelo 2
            $o_model2 = $this->loadCreate2($a_filtered, $o_model, $id2, $id3);
            //Cargamos modelo 3
            $o_model3 = $this->loadCreate3($a_filtered, $o_model, $o_model2, $id3);
            //Before
            $this->beforeRestCreate($a_filtered, $o_model, $o_model2, $o_model3);
            //Seteamos atributos
            $this->createAssignment($a_filtered, $o_model, $o_model2, $o_model3);
            //Validamos
            if ($this->validateCreate($o_model)) {
                try {
                    //Iniciamos la transacción
                    $this->beginTransaction($o_model);
                    //
                    $this->restCreate($o_model);
                    $this->afterRestCreate($a_filtered, $o_model, $o_model2, $o_model3);
                    //Confirmamos la transacción
                    $this->commitTransaction($o_model);
                } catch (CDbException $ex) {
                    //Anulamos la transacción
                    $this->rollbackTransaction($p_o_model);
                    throw $ex;
                } catch (Exception $ex) {
                    //Anulamos la transacción
                    $this->rollbackTransaction($p_o_model);
                    throw $ex;
                }
                //
                USREST::sendResponse(USREST::CODE_SUCCESS, '', $this->createResponse($o_model));
            } else {
                USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                    'attributes' => $o_model->getAllAssociativeErrors()
                ]);
                Yii::app()->end();
            }
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }

    public function actionRestUpdate($id, $id2 = null, $id3 = null) {
        try {
            //Atributos permitidos
            $a_required = $this->updateRequiredAttributes();
            //Atributos defecto
            $a_default = $this->updateDefaultAttributes();
            //Verificamos
            $a_filtered = $this->getAndFilterAttributes($a_required, $a_default, ['id', 'id2', 'id3']);
            //Vefificamos parámetros
            $this->updateAttributesVerification($a_filtered);
            //Instanciamos
            $o_model = $this->loadUpdate($a_filtered, $id, $id2, $id3);
            //Verificamos existencia
            $this->exists($o_model);
            //Cargamos modelo 2
            $o_model2 = $this->loadUpdate2($a_filtered, $o_model, $id2, $id3);
            //Cargamos modelo 3
            $o_model3 = $this->loadUpdate3($a_filtered, $o_model, $o_model2, $id3);
            //Before
            $this->beforeRestUpdate($a_filtered, $o_model, $o_model2, $o_model3);
            //Seteamos atributos
            $this->updateAssignment($a_filtered, $o_model, $o_model2, $o_model3);
            //Validamos
            if ($this->validateUpdate($o_model)) {
                try {
                    //Iniciamos la transacción
                    $this->beginTransaction($o_model);
                    //
                    $this->restUpdate($o_model);
                    //After
                    $this->afterRestUpdate($a_filtered, $o_model, $o_model2, $o_model3);
                    //Confirmamos la transacción
                    $this->commitTransaction($o_model);
                } catch (CDbException $ex) {
                    //Anulamos la transacción
                    $this->rollbackTransaction($p_o_model);
                    throw $ex;
                } catch (Exception $ex) {
                    //Anulamos la transacción
                    $this->rollbackTransaction($p_o_model);
                    throw $ex;
                }
                //
                USREST::sendResponse(USREST::CODE_SUCCESS, '', $this->updateResponse($o_model));
            } else {
                USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                    'attributes' => $o_model->getAllAssociativeErrors()
                ]);
                Yii::app()->end();
            }
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }

    public function actionRestDelete($id, $id2 = null, $id3 = null) {

        try {
            //Verificamos
            $a_filtered = $this->getAndFilterAttributes([], [], ['id', 'id2', 'id3']);
            //Vefificamos parámetros
            $this->deleteAttributesVerification($a_filtered);
            //Instanciamos
            $o_model = $this->loadDelete($a_filtered, $id, $id2, $id3);
            //Verificamos existencia
            $this->exists($o_model);
            //Cargamos modelo2
            $o_model2 = $this->loadDelete2($a_filtered, $o_model, $id2, $id3);
            //Cargamos modelo3
            $o_model3 = $this->loadDelete3($a_filtered, $o_model, $o_model2, $id3);
            //Before
            $this->beforeRestDelete($a_filtered, $o_model, $o_model2, $o_model3);
            //Seteamos atributos
            $this->deleteAssignment($a_filtered, $o_model, $o_model2, $o_model3);
            try {
                //Iniciamos la transacción
                $this->beginTransaction($o_model);
                //Eliminamos
                $this->restDelete($o_model);
                //After
                $this->afterRestDelete($a_filtered, $o_model, $o_model2, $o_model3);
                //Confirmamos la transacción
                $this->commitTransaction($o_model);
            } catch (CDbException $ex) {
                //Anulamos la transacción
                $this->rollbackTransaction($p_o_model);
                throw $ex;
            } catch (Exception $ex) {
                //Anulamos la transacción
                $this->rollbackTransaction($p_o_model);
                throw $ex;
            }
            //
            USREST::sendResponse(USREST::CODE_SUCCESS, '', $this->deleteResponse($o_model));
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }

    protected function checkAppAuth() {
        //Obtenemos headers
        $a_headers = USREST::getInsensitiveHeaders();
        //Verificamos que exista Authorization
        if (!isset($a_headers['app-authorization'])) {
            USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'No se ha especificado el token de aplicación');
            Yii::app()->end();
        }
        $s_jwt = $a_headers['app-authorization'];
        //Decodificamos
        $o_token_data = SIANJWT::decodeToken($s_jwt);
        //Verificamos token
        if ($o_token_data == false) {
            SIANJWT::deleteToken($s_jwt);
            USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'EL TOKEN no es válido!');
            Yii::app()->end();
        }

        //Obtenemos data del token
        $this->apiUserToken = $o_token_data;

        //Verificamos que sea el tipo de token correcto
        if ($this->apiUserToken->token_type !== self::TOKEN_TYPE_PROXY) {
            USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'EL TOKEN de autorización de la aplicación no es correcto, posiblemente envió el de usuario!');
            Yii::app()->end();
        }

        //Verificamos que el usuario API corresponda con el API que se está usando
        switch ($this->module->id) {
            case 'api':
                if ($this->apiUserToken->type !== ApiUser::TYPE_WEB) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La autenticación no corresponde a API WEB. Autentíquese en el API WEB!');
                    Yii::app()->end();
                }
                break;
            case 'apiLocker':
                if ($this->apiUserToken->type !== ApiUser::TYPE_LOCKER) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La autenticación no corresponde a API LOCKER. Autentíquese en el API LOCKER.');
                    Yii::app()->end();
                }
                break;
            case 'apiPos':
                if ($this->apiUserToken->type !== ApiUser::TYPE_POS) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La autenticación no corresponde a API POS. Autentíquese en el API POS.');
                    Yii::app()->end();
                }
                break;
            case 'apiKiosk':
                if ($this->apiUserToken->type !== ApiUser::TYPE_KIOSK) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La autenticación no corresponde a API KIOSK. Autentíquese en el API KIOSK.');
                    Yii::app()->end();
                }
                break;
            case 'sharedApi':
                if (!in_array($this->apiUserToken->type, [ApiUser::TYPE_WEB, ApiUser::TYPE_POS])) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La autenticación no corresponde a un API válida. Autentíquese en un API válida.');
                    Yii::app()->end();
                }
                break;
            default:
                break;
        }

        $i_expiration = SIANTime::substract($this->apiUserToken->expiration, SIANTime::now());

        if ($i_expiration < 0) {
            SIANJWT::deleteToken($s_jwt);
            USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'EL TOKEN ha expirado!');
            Yii::app()->end();
        }

        //Verificamos si tiene acceso para la ruta
        $s_route = $this->route;
        $b_access = ApiUser::checkRoute($this->apiUserToken->api_user_id, $s_route);

        if (!$b_access) {
            USREST::sendResponse(USREST::CODE_FORBIDDEN, 'Las credenciales de aplicación no permiten realizar esta acción!');
            Yii::app()->end();
        }

        return true;
    }

    protected function loadCreate($p_a_filtered, $p_i_id2 = null, $p_i_id3 = null) {
        return new $this->m_class();
    }

    protected function loadCreate2($p_a_filtered, $p_o_model, $p_i_id2 = null, $p_i_id3 = null) {
        return null;
    }

    protected function loadCreate3($a_filtered, $p_o_model, $p_o_model2 = null, $p_i_id3 = null) {
        return null;
    }

    protected function loadUpdate($p_a_filtered, $p_i_id, $p_i_id2 = null, $p_i_id3 = null) {
        $s_class = $this->m_class;
        return $s_class::model()->findByPk($p_i_id);
    }

    protected function loadUpdate2($p_a_filtered, $p_o_model, $p_i_id2 = null, $p_i_id3 = null) {
        return null;
    }

    protected function loadUpdate3($p_a_filtered, $p_o_model, $p_o_model2 = null, $p_i_id3 = null) {
        return null;
    }

    protected function loadDelete($p_a_filtered, $p_i_id, $p_i_id2 = null, $p_i_id3 = null) {
        $s_class = $this->m_class;
        return $s_class::model()->findByPk($p_i_id);
    }

    protected function loadDelete2($p_a_filtered, $p_o_model, $p_i_id2 = null, $p_i_id3 = null) {
        return null;
    }

    protected function loadDelete3($a_filtered, $p_o_model, $p_o_model2 = null, $p_i_id3 = null) {
        return null;
    }

    protected function listRequiredAttributes() {
        return [
            'page' => false,
            'limit' => false,
            'sidx' => false,
            'sord' => false,
            'order' => false,
            'count' => false,
            'filters' => false,
        ];
    }

    protected function listDefaultAttributes() {
        return [
            'page' => 1,
            'limit' => false,
            'sidx' => false,
            'sord' => 'ASC',
            'order' => false,
            'count' => 1,
            'filters' => [],
        ];
    }

    protected function createRequiredAttributes() {
        return false;
    }

    protected function createDefaultAttributes() {
        return [];
    }

    protected function updateRequiredAttributes() {
        return false;
    }

    protected function updateDefaultAttributes() {
        return [];
    }

    protected function viewRequiredAttributes() {
        return false;
    }

    protected function secretRequiredAttributes() {
        return false;
    }

    protected function viewDefaultAttributes() {
        return [];
    }

    protected function secretDefaultAttributes() {
        return [];
    }

    protected function beforeRestList(&$p_o_model, &$p_a_filtered, $p_i_id2 = null, $p_i_id3 = null) {
        
    }

    protected function afterRestList(&$p_a_response, $p_a_filtered, $p_i_id2 = null, $p_i_id3 = null) {
        
    }

    protected function createAttributesVerification(&$p_a_filtered) {
        
    }

    protected function beforeRestCreate(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        
    }

    protected function createAssignment(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        $p_o_model->attributes = $p_a_filtered;
    }

    protected function beforeConditionAndParams(&$p_o_model, &$p_a_filters, $p_i_id2 = null, $p_i_id3 = null) {
        
    }

    protected function validateCreate(&$p_o_model) {
        return $p_o_model->validate();
    }

    protected function restCreate(&$p_o_model) {
        $p_o_model->insert();
    }

    protected function afterRestCreate(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        
    }

    protected function createResponse($p_o_model) {
        return $p_o_model->getAutoIncrementAssociation();
    }

    protected function updateAttributesVerification(&$p_a_filtered) {
        
    }

    protected function beforeRestUpdate(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        
    }

    protected function updateAssignment(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        $p_o_model->attributes = $p_a_filtered;
    }

    protected function validateUpdate(&$p_o_model) {
        return $p_o_model->validate();
    }

    protected function restUpdate(&$p_o_model) {
        $p_o_model->update();
    }

    protected function afterRestUpdate(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        
    }

    protected function updateResponse($p_o_model) {
        return null;
    }

    protected function deleteAttributesVerification(&$p_a_filtered) {
        
    }

    protected function beforeRestDelete(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        
    }

    protected function deleteAssignment(&$p_a_filtered, &$p_o_model, $p_o_model2 = null, $p_o_model3 = null) {
        $p_o_model->attributes = $p_a_filtered;
    }

    protected function restDelete($p_o_model) {
        $p_o_model->delete();
    }

    protected function afterRestDelete(&$p_a_filtered, &$model, $p_o_model2 = null, $p_o_model3 = null) {
        
    }

    protected function deleteResponse($p_o_model) {
        return null;
    }

    public function required($p_a_attributes, $p_s_attribute, $p_s_focus = null, $p_s_in = null) {
        if (!isset($p_a_attributes[$p_s_attribute])) {

            $s_message = "No se ha especificado el atributo: {$p_s_attribute}" . (isset($p_s_in) ? ' en ' . $p_s_in : '');

            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                'attributes' => [
                    (isset($p_s_focus) ? $p_s_focus : $p_s_attribute) => $s_message
                ]
            ]);
            Yii::app()->end();
        }
    }

    public function verifyAndDecodeToken($p_s_token, $p_s_attribute = 'token') {
        //Decodificamos
        $o_token_data = SIANJWT::decodeToken($p_s_token);
        if ($o_token_data == false) {
            SIANJWT::deleteToken($p_s_token);
            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                'attributes' => [
                    $p_s_attribute => 'EL TOKEN no es válido!'
                ]
            ]);
            Yii::app()->end();
        }
        //Verificamos validez
        if (SIANTime::substract($o_token_data->expiration, SIANTime::now()) < 0) {
            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                'attributes' => [
                    $p_s_attribute => 'EL TOKEN ha expirado!'
                ]
            ]);
            Yii::app()->end();
        }

        return $o_token_data;
    }

    protected function verifyFilters(&$p_a_filters, $p_i_id2 = null, $p_i_id3 = null) {
        
    }

    protected function _verifyCount($p_i_count) {

        if ($p_i_count !== 0 && $p_i_count !== 1 && $p_i_count !== '0' && $p_i_count !== '1' && $p_i_count !== false && $p_i_count !== true) {
            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                'attributes' => [
                    'count' => "La valor de count no es válido"
                ]
            ]);
            Yii::app()->end();
        }
    }

    protected function _verifyPage($p_i_page) {

        if (!is_numeric($p_i_page)) {
            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                'attributes' => [
                    'page' => "La valor de page debe ser un entero"
                ]
            ]);
            Yii::app()->end();
        }
    }

    protected function _verifyLimit($p_i_limit) {

        if (!is_numeric($p_i_limit) && $p_i_limit !== false) {
            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                'attributes' => [
                    'limit' => "La valor de limit no es válido"
                ]
            ]);
            Yii::app()->end();
        }
    }

    protected function _verifySord($p_s_sord) {

        if (isset($p_s_sord) && !in_array(strtoupper($p_s_sord), ['ASC', 'DESC'])) {
            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                'attributes' => [
                    'sord' => "La dirección de ordenamiento '{$p_s_sord}' no es válida"
                ]
            ]);
            Yii::app()->end();
        }
    }

    /**
     * Verifica si un objeto o array existe
     * @param mixed $p_m_value Objeto o array
     * @param string $p_s_message Mensaje
     */
    public function exists($p_m_value, $p_s_message = "No existe el objeto con el ID/Clave especificado.") {
        if (!isset($p_m_value) || $p_m_value === false) {
            USREST::sendResponse(USREST::CODE_NOT_FOUND, $p_s_message);
            Yii::app()->end();
        }
    }

    /**
     * Verifica si un array no está vacío
     * @param mixed $p_a_array Array
     * @param string $p_s_message Mensaje
     */
    public function isNotEmpty(array $p_a_array, $p_s_message = "El array con el ID/Clave especificado está vacío.") {
        if (count($p_a_array) === 0) {
            USREST::sendResponse(USREST::CODE_NOT_FOUND, $p_s_message);
            Yii::app()->end();
        }
    }

    private function _copy(&$p_a_from, &$p_a_to, $p_s_attribute) {
        $p_a_to[$p_s_attribute] = $p_a_from[$p_s_attribute];
    }

    private function _move(&$p_a_from, &$p_a_to, $p_s_attribute) {
        $this->_copy($p_a_from, $p_a_to, $p_s_attribute);
        unset($p_a_from[$p_s_attribute]);
    }

    private function _getAttributes($p_a_unset) {

        $s_request_type = Yii::app()->request->requestType;
        $p_attributes = [];

        switch ($s_request_type) {
            case 'POST':
            case 'PUT':
            case 'PATCH':
            case 'DELETE':
                $s_raw = Yii::app()->request->getRawBody();
                if (!USString::isBlank($s_raw)) {
                    $p_attributes = CJSON::decode($s_raw);
                }
                break;
            case 'GET';
                $p_attributes = $_GET;
                break;
            default:
        }

        foreach ($p_a_unset as $s_unset_attribute) {
            if (isset($p_attributes[$s_unset_attribute])) {
                unset($p_attributes[$s_unset_attribute]);
            }
        }

        return $p_attributes;
    }

    /**
     * Procesamos todos los filtros
     * @param DpBase $p_o_model Modelo que contendrá los filtros
     * @param array $p_a_filters Todos los filtros
     */
    protected function _processFilters(&$p_o_model, $p_a_filters) {
        foreach ($p_a_filters as $s_type => $a_filters_by_type) {

            if (!in_array($s_type, [
                        self::FILTER_TYPE_EQUAL,
                        self::FILTER_TYPE_NOT_EQUAL,
                        self::FILTER_TYPE_LIKE,
                        self::FILTER_TYPE_IN,
                        self::FILTER_TYPE_NOT_IN,
                        self::FILTER_TYPE_MATH,
//                        self::FILTER_TYPE_RAW,
                        self::FILTER_TYPE_SEARCH,
                        self::FILTER_TYPE_NULL,
                        self::FILTER_TYPE_NOT_NULL,
                    ])) {
                USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                    'attributes' => [
                        'filters' => "El tipo de filtro '{$s_type}' no está permitido."
                    ]
                ]);
                Yii::app()->end();
            }

            $this->_processFilterType($p_o_model, $s_type, $a_filters_by_type);
        }
    }

    /**
     * Procesa los filtros por tipo
     * @param DpBase $p_o_model Modelo que contendrá los filtros
     * @param string $p_s_type Tipo de filtro
     * @param array $p_a_filters Filtros por tipo
     */
    private function _processFilterType(&$p_o_model, $p_s_type, $p_a_filters) {
        if (is_array($p_a_filters)) {
            switch ($p_s_type) {
//                case self::FILTER_TYPE_RAW:
//                    //Si el filtro es RAW tiene un tratamiento diferente
//                    foreach ($p_a_filters as $p_m_value) {
//                        $a_columns = USString::getAllBetween($p_m_value, "[", "]");
//                        foreach ($a_columns as $s_attribute) {
//                            if (!$p_o_model->columnIsPresent($s_attribute)) {
//                                USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
//                                    'attributes' => [
//                                        'filters' => "La columna '{$s_attribute}' no existe o no está permitida para usarse como filtro."
//                                    ]
//                                ]);
//                                Yii::app()->end();
//                            }
//                        }
//                        //Agregamos filtro
//                        $p_o_model->addCondition($p_m_value);
//                    }
//                    break;
                case self::FILTER_TYPE_SEARCH:
                    //Si el filtro es TERM tiene un tratamiento diferente
                    if (!isset($p_a_filters['value'])) {
                        USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                            'attributes' => [
                                'filters' => "Debe especificar un atributo 'value' dentro de un filtro '{$p_s_type}'."
                            ]
                        ]);
                        Yii::app()->end();
                    }

                    //Si el filtro es TERM tiene un tratamiento diferente
                    if (!isset($p_a_filters['attributes']) || !is_array($p_a_filters['attributes'])) {
                        USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                            'attributes' => [
                                'filters' => "Debe especificar un atributo 'attributes' de tipo array dentro de un filtro '{$p_s_type}'."
                            ]
                        ]);
                        Yii::app()->end();
                    }

                    foreach ($p_a_filters['attributes'] as $s_attribute) {
                        if (!$p_o_model->columnIsPresent($s_attribute)) {
                            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                                'attributes' => [
                                    'filters' => "El atributo '{$s_attribute}' no existe o no está permitida para usarse como filtro."
                                ]
                            ]);
                            Yii::app()->end();
                        }
                    }
                    //Agregamos filtro
                    $p_o_model->addTermCondition($p_a_filters['attributes'], $p_a_filters['value']);
                    break;
                case self::FILTER_TYPE_NULL:
                    if (!is_array($p_a_filters)) {
                        USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                            'attributes' => [
                                'filters' => "Los valores para un filtro de tipo '{$p_s_type}' deben estar dentro de un array."
                            ]
                        ]);
                        Yii::app()->end();
                    }
                    //En este tipo cada filtro es la coluna directa 
                    foreach ($p_a_filters as $s_attribute) {
                        if (!$p_o_model->columnIsPresent($s_attribute)) {
                            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                                'attributes' => [
                                    'filters' => "El atributo '{$s_attribute}' no existe o no está permitida para usarse como filtro."
                                ]
                            ]);
                            Yii::app()->end();
                        }
                    }
                    //Agregamos filtro
                    $p_o_model->addCondition("[{$s_attribute}] IS NULL");
                    break;
                case self::FILTER_TYPE_NOT_NULL:
                    if (!is_array($p_a_filters)) {
                        USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                            'attributes' => [
                                'filters' => "Los valores para un filtro de tipo '{$p_s_type}' deben estar dentro de un array."
                            ]
                        ]);
                        Yii::app()->end();
                    }
                    //En este tipo cada filtro es la coluna directa 
                    foreach ($p_a_filters as $s_attribute) {
                        if (!$p_o_model->columnIsPresent($s_attribute)) {
                            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                                'attributes' => [
                                    'filters' => "El atributo '{$s_attribute}' no existe o no está permitida para usarse como filtro."
                                ]
                            ]);
                            Yii::app()->end();
                        }
                    }
                    //Agregamos filtro
                    $p_o_model->addCondition("[{$s_attribute}] IS NOT NULL");
                    break;
                default:
                    foreach ($p_a_filters as $s_attribute => $m_value) {
                        //Verificamos si la columna está presente
                        if (!$p_o_model->columnIsPresent($s_attribute)) {
                            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                                'attributes' => [
                                    'filters' => "El filtro '{$s_attribute}' no está permitido para este objeto."
                                ]
                            ]);
                            Yii::app()->end();
                        }

                        switch ($p_s_type) {
                            case self::FILTER_TYPE_EQUAL:
                                $p_o_model->addEqualCondition($s_attribute, $m_value);
                                break;
                            case self::FILTER_TYPE_NOT_EQUAL:
                                $p_o_model->addNotEqualCondition($s_attribute, $m_value);
                                break;
                            case self::FILTER_TYPE_LIKE:
                                $p_o_model->addLikeCondition($s_attribute, $m_value);
                                break;
                            case self::FILTER_TYPE_IN:
                                if (!is_array($m_value)) {
                                    USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                                        'attributes' => [
                                            'filters' => "El valor del filtro {$s_attribute} debe ser un array."
                                        ]
                                    ]);
                                    Yii::app()->end();
                                }
                                $p_o_model->addInCondition($s_attribute, $m_value);
                                break;
                            case self::FILTER_TYPE_NOT_IN:
                                if (!is_array($m_value)) {
                                    USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                                        'attributes' => [
                                            'filters' => "El valor del filtro {$s_attribute} debe ser un array."
                                        ]
                                    ]);
                                    Yii::app()->end();
                                }
                                $p_o_model->addNotInCondition($s_attribute, $m_value);
                                break;
                            case self::FILTER_TYPE_MATH:
                                if (!is_array($m_value)) {
                                    USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                                        'attributes' => [
                                            'filters' => "El valor del filtro {$s_attribute} debe ser un array asociativo operator=>value"
                                        ]
                                    ]);
                                    Yii::app()->end();
                                }

                                foreach ($m_value as $s_operator => $s_value) {

                                    if (!in_array($s_operator, [
                                                self::FILTER_MATH_EQUAL,
                                                self::FILTER_MATH_MINOR,
                                                self::FILTER_MATH_MAJOR,
                                                self::FILTER_MATH_MINOR_EQUAL,
                                                self::FILTER_MATH_MAJOR_EQUAL,
                                            ])) {
                                        USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                                            'attributes' => [
                                                'filters' => "El tipo de operator '{$s_operator}' no está permitido para un filtro tipo math."
                                            ]
                                        ]);
                                        Yii::app()->end();
                                    }

                                    $s_dp_operator = '';
                                    switch ($s_operator) {
                                        case self::FILTER_MATH_EQUAL:
                                            $s_dp_operator = '=';
                                            break;
                                        case self::FILTER_MATH_MINOR:
                                            $s_dp_operator = '<';
                                            break;
                                        case self::FILTER_MATH_MAJOR:
                                            $s_dp_operator = '>';
                                            break;
                                        case self::FILTER_MATH_MINOR_EQUAL:
                                            $s_dp_operator = '<=';
                                            break;
                                        case self::FILTER_MATH_MAJOR_EQUAL:
                                            $s_dp_operator = '>=';
                                            break;
                                    }

                                    $p_o_model->addMathCondition($s_attribute, $s_value, $s_dp_operator);
                                }
                                break;
                        }
                    }
                    break;
            }
        } else {
            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                'attributes' => [
                    'filters' => "Los filtros de tipo '{$p_s_type}' deben ser arrays"
                ]
            ]);
            Yii::app()->end();
        }
    }

    private function _verifyOrder($o_model, $p_a_order) {
        if (is_array($p_a_order)) {
            foreach ($p_a_order as $s_sidx => $s_sord) {
                switch ($s_sidx) {
                    case DpBase::SORT_RAND:
                    case DpBase::SORT_NULL:
                        break;
                    default:
                        if (!$o_model->columnIsPresent($s_sidx)) {
                            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                                'attributes' => [
                                    'sidx' => "La ordenación por '{$s_sidx}' no está permitida para este objeto."
                                ]
                            ]);
                            Yii::app()->end();
                        }
                        break;
                }

                $this->_verifySord($s_sord);
            }
        } else {
            USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                'attributes' => [
                    'order' => "El atributo 'order' debe ser un array."
                ]
            ]);
            Yii::app()->end();
        }

        return $p_a_order;
    }

    protected function _getOrder($o_model, $a_filtered) {

        if ($a_filtered['order'] === false) {
            if (!USString::isBlank($a_filtered['sidx'])) {
                return $this->_verifyOrder($o_model, [
                            $a_filtered['sidx'] => $a_filtered['sord']
                ]);
            } else {
                return false;
            }
        } else {
            //Verificamos direction de ordenamiento
            return $this->_verifyOrder($o_model, $a_filtered['order']);
        }
    }

    public function filterAttributes(&$p_a_attributes, $p_a_required, $p_a_defaults = [], $p_s_focus = null, $p_s_in = null) {

        if ($p_a_required !== false) {
            //
            $a_filtered = [];
            //Recorremos atributos seguros
            foreach ($p_a_required as $s_safe_attribute => $b_required) {
                //Si es requerido verificamos
                if ($b_required) {
                    $this->required($p_a_attributes, $s_safe_attribute, $p_s_focus, $p_s_in);
                    //Movemos
                    $this->_move($p_a_attributes, $a_filtered, $s_safe_attribute);
                } else {
                    //Movemos
                    if (in_array($s_safe_attribute, array_keys($p_a_attributes))) {
                        //No es null
                        if (isset($p_a_attributes[$s_safe_attribute])) {
                            $this->_move($p_a_attributes, $a_filtered, $s_safe_attribute);
                        } else {
                            //Si no tiene valor, pero sí por defecto
                            if (isset($p_a_defaults[$s_safe_attribute])) {
                                $this->_copy($p_a_defaults, $a_filtered, $s_safe_attribute);
                            }
                            //Borramos para que no vaya a salir en los No permitidos
                            unset($p_a_attributes[$s_safe_attribute]);
                        }
                    } else {
                        //Si no tiene valor, pero sí por defecto
                        if (isset($p_a_defaults[$s_safe_attribute])) {
                            $this->_copy($p_a_defaults, $a_filtered, $s_safe_attribute);
                        }
                    }
                }
            }

            //Atributos especiales permitidos en desarrollo y GET
            if (Yii::app()->params['environment'] === YII_ENVIRONMENT_DEVELOPMENT && Yii::app()->request->requestType === 'GET') {
                $a_system = [
                    'XDEBUG_SESSION_START'
                ];

                foreach ($a_system as $s_system_attribute) {
                    if (isset($p_a_attributes[$s_system_attribute])) {
                        unset($p_a_attributes[$s_system_attribute]);
                    }
                }
            }

            //Si quedan atributos, son los no permitidos
            if (count($p_a_attributes) > 0) {
                $a_validations = [];
                foreach ($p_a_attributes as $s_attribute => $m_value) {
                    $s_focus = (isset($p_s_focus) ? $p_s_focus : $s_attribute);
                    $a_validations[$s_focus] = "El parámetro {$s_attribute} no está permitido" . (isset($p_s_in) ? ' en ' . $p_s_in : '');
                }

                USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                    'attributes' => $a_validations
                ]);
                Yii::app()->end();
            }

            $p_a_attributes = $a_filtered;
        } else {

            foreach ($p_a_defaults as $s_default_attribute => $m_default_value) {
                if (!isset($p_a_attributes[$s_default_attribute])) {
                    $this->_copy($p_a_defaults, $p_a_attributes, $s_default_attribute);
                }
            }
        }
    }

    public function getAndFilterAttributes($p_a_required, $p_a_defaults = [], $p_a_unset = []) {

        $a_attributes = $this->_getAttributes($p_a_unset);
        $this->filterAttributes($a_attributes, $p_a_required, $p_a_defaults);
        return $a_attributes;
    }

    protected function getListAllowedAttributes() {
        return [];
    }

    public function setDefaults($p_attributes, $p_a_defaults) {
        foreach ($p_a_defaults as $s_default_attribute => $m_value) {
            if (!isset($p_attributes[$s_default_attribute])) {
                $p_attributes[$s_default_attribute] = $m_value;
            }
        }
    }

    public function getContactHtml() {

        $o_organization = $this->getOrganization();
        $a_stores = Store::get(Store::MODE_API);

        //Recorremos tiendas
        $s_contact = "";
        foreach ($a_stores as $store) {

            //Teléfonos
            $a_phones = [];
            foreach ($store->phones as $phone) {
                $a_phones[] = $phone->phone_number;
            }

            $s_contact .= "<div><p>";
            $s_contact .= "<b>{$store->address->address}</b><br>";
            $s_contact .= "<b>Teléfono:</b> " . implode(' / ', $a_phones);
            $s_contact .= "</p></div>";
        }

        $a_emails = [];
        foreach ($o_organization->person->emails as $email) {
            $a_emails[] = '<span>' . $email->email_address . '</span>';
        }

        $s_contact .= "<div><p>";
        $s_contact .= "<b>Correo: </b>" . implode(' / ', $a_emails);
        $s_contact .= "</p></div>";

        return $s_contact;
    }

    /**
     * Cuenta cuantos cambios hay
     * @param string $p_s_owner Propietario
     * @param string $p_s_last_sync Fecha de última sincronización yyyy-mm-dd hh:mm:ss
     * @return integer Número de cambios
     */
    private function _countLastChanges($p_s_owner, $p_s_last_sync) {
        return (int) Yii::app()->db->createCommand()
                        ->select(['COUNT(CH.owner_id)'])
                        ->from('changelog CH')
                        ->where("CH.owner = :owner AND CH.last_by_object AND CH.`date` > :last_sync")
                        ->queryScalar([
                            ':owner' => $p_s_owner,
                            ':last_sync' => $p_s_last_sync
        ]);
    }

    /**
     * Obtiene los N cambios de la n página
     * @param string $p_s_owner Propietario
     * @param string $p_s_last_sync Fecha de última sincronización yyyy-mm-dd hh:mm:ss
     * @param integer $p_i_page Número de página
     * @param integer $p_i_limit N resultados por página
     * @return array N elementos
     */
    private function _getChangeIds($p_s_owner, $p_s_last_sync, $p_i_page, $p_i_limit) {
        $a_items = Yii::app()->db->createCommand()
                ->select(['CH.owner_id', 'CH.action'])
                ->from('changelog CH')
                ->where("CH.owner = :owner AND CH.`date` > :last_sync AND CH.last_by_object")
                ->order('CH.owner_id')
                ->group('CH.owner_id')
                ->limit($p_i_limit)
                ->offset(($p_i_limit * $p_i_page) - $p_i_limit)
                ->queryAll(true, [
            ':owner' => $p_s_owner,
            ':last_sync' => $p_s_last_sync
                ]
        );

        $a_created = [];
        $a_updated = [];
        $a_deleted = [];
        foreach ($a_items as $a_item) {
            switch ($a_item['action']) {
                case Changelog::CREATE:
                    $a_created[] = $a_item['owner_id'];
                    break;
                case Changelog::DELETE:
                    $a_deleted[] = $a_item['owner_id'];
                    break;
                default:
                    $a_updated[] = $a_item['owner_id'];
                    break;
            }
        }

        return [
            'created' => $a_created,
            'updated' => $a_updated,
            'deleted' => $a_deleted,
        ];
    }

    protected function getConditionAndParams($p_a_filters, $p_i_id2 = null, $p_i_id3 = null) {
        $o_model = (new $this->dp_class($this->dp_scenario));
        //Verificamos filtros
        $this->verifyFilters($p_a_filters, $p_i_id2, $p_i_id3);
        //
        $this->beforeConditionAndParams($o_model, $p_a_filters, $p_i_id2, $p_i_id3);
        //Procesamos filtros
        $this->_processFilters($o_model, $p_a_filters);
        //
        return $o_model->getConditionAndParams();
    }

    protected function normalizeText($p_s_string) {
        return strtoupper(str_replace("'", "", USString::replaceTildes($p_s_string)));
    }

    /**
     * Envía un correo cuando se crea un movimiento mediante API
     * @param integer $p_i_movement_id ID de movimiento
     * @param ApiUser $p_a_apiUserVariant Objeto APIUSER
     * @param array $p_a_email_addresses Array de correos a los que se enviará la notificación
     */
    protected function _sendMovementEmail($p_i_movement_id, $p_a_apiUserVariant, $p_a_email_addresses) {
        //Si hay correos
        if (count($p_a_email_addresses) > 0) {
            $o_model = Movement::model()->with([
                        'operation' => [
                            'select' => ['O.operation_name'],
                            'alias' => 'O'
                        ],
                        'auxPerson' => [
                            'select' => ['XP.identification_type', 'XP.person_name'],
                            'alias' => 'XP',
                        ],
                    ])->findByPk([
                'movement_id' => $p_i_movement_id
                    ], [
                'select' => ['M.movement_id', 'M.document_code', 'M.document_serie', 'M.document_correlative', 'M.route', 'M.emission_date', 'M.register_date'],
                'alias' => 'M'
            ]);

            $s_subject = "Nuevo(a) {$o_model->operation->toString()} generado(a) desde {$p_a_apiUserVariant['username']}";
            $s_from_email = Yii::app()->params['senderEmail'];
            $s_from_name = $this->getOrganization()->person->person_name;
            $s_message = $this->renderPartial('//email/template', [
                'icon' => 'check',
                'color' => $this->getOrganization()->globalVar->color,
                'header' => 'NUEVO(A) ' . strtoupper($o_model->operation->toString()) . ' CREADO(A)',
                'view' => "//email/_api_web_create_movement",
                'footer' => CHtml::link('Visitenos', $this->getOrganization()->web, [
                    'target' => '_BLANK',
                ]),
                'stores' => Store::get(Store::MODE_API),
                'data' => [
                    'model' => $o_model,
                    'apiUserVariant' => $p_a_apiUserVariant
                ]
                    ], true, true);
            $a_to_emails = [];
            foreach ($p_a_email_addresses as $i => $s_email_address) {
                $a_to_emails['Notificación ' . ($i + 1)] = $s_email_address;
            }
            //Agregamos a pila de correos
            EmailQueue::push($s_from_email, $s_from_name, $s_subject, $s_message, $a_to_emails);
        }
    }

    protected function _createOwnerPair($p_s_type, $p_s_owner, $p_s_owner_id, $p_s_child_owner, $p_s_child_owner_id, $p_i_default) {

        $a_pair = Yii::app()->db->createCommand()
                ->select([
                    'OP1.pair_id',
                ])
                ->from('owner_pair OP1')
                ->join('owner_pair OP2', 'OP2.parent_id = OP1.pair_id')
                ->where('OP1.type = :type AND OP1.`owner` = :owner AND OP1.owner_id = :owner_id1 AND OP2.owner_id = :owner_id2', [
                    ':type' => $p_s_type,
                    ':owner' => $p_s_owner,
                    ':owner_id1' => $p_s_owner_id,
                    ':owner_id2' => $p_s_child_owner_id,
                ])
                ->queryRow();

        //Si ya existe rebotamos
        if ($a_pair !== false) {
            return false;
        }

        $s_pair_code = USTime::getDateCode();
        //Seteamos atributos
        $a_pair_ids = OwnerPair::insertOnePair($p_s_type, $p_s_owner, $p_s_owner_id, [
                    [
                        'pair_code' => $s_pair_code,
                        'owner' => $p_s_child_owner,
                        'owner_id' => $p_s_child_owner_id,
                        'default' => $p_i_default ? 1 : 0
                    ]
        ]);

        return $a_pair_ids[$s_pair_code];
    }

    public static function deniedCallback() {
        USREST::sendResponse(USREST::CODE_NOT_IMPLEMENTED, 'El recurso solicitado no está implementado o no está permitido');
        Yii::app()->end();
    }

}
