<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANFinancial
 *
 * <AUTHOR>
 */
class SIANFinancial {

    public static function PVIF($rate, $nper) {
        return pow(1 + $rate, $nper);
    }

    public static function FVIFA($rate, $nper) {
        return $rate == 0 ? $nper : (self::PVIF($rate, $nper) - 1) / $rate;
    }

    public static function PMT($rate, $nper, $pv, $fv = null, $type = null) {
        if (!isset($fv)) {
            $fv = 0;
        }
        if (!isset($type)) {
            $type = 0;
        }

        if ($rate == 0) {
            return -($pv + $fv) / $nper;
        }

        $pvif = pow(1 + $rate, $nper);
        $pmt = $rate / ($pvif - 1) * -($pv * $pvif + $fv);

        if ($type == 1) {
            $pmt /= (1 + $rate);
        }

        return $pmt;
    }

    public static function IPMT($pv, $pmt, $rate, $per) {
        $tmp = pow(1 + $rate, $per);
        return 0 - ($pv * $tmp * $rate + $pmt * ($tmp - 1));
    }

    public static function PPMT($rate, $per, $nper, $pv, $fv, $type) {
        if ($per < 1 || ($per >= $nper + 1)) {
            return null;
        }
        $pmt = self::PMT($rate, $nper, $pv, $fv, $type);
        $ipmt = self::IPMT($pv, $pmt, $rate, $per - 1);
        return $pmt - $ipmt;
    }

    public static function DaysBetween($date1, $date2) {
        $oneDay = 24 * 60 * 60 * 1000;
        return round(abs(($date1 . getTime() - $date2 . getTime()) / $oneDay));
    }

    public static function XNPV($rate, $values) {
        $xnpv = 0.0;
        $firstDate = new DateTime($values[0]->Date);
        foreach ($values as $key) {
            $tmp = $values[$key];
            $value = $tmp->Flow;
            $date = new DateTimr($tmp->Date);
            $xnpv += $value / pow(1 + $rate, SIANTime::getDaysBetween($firstDate, $date) / 365);
        }
        return $xnpv;
    }

    public static function XIRR($values, $guess) {
        if (!$guess) {
            $guess = 0.1;
        }

        $x1 = 0.0;
        $x2 = $guess;
        $f1 = self::XNPV(x1, $values);
        $f2 = self::XNPV(x2, $values);

        for ($i = 0; $i < 100; $i++) {
            if (($f1 * $f2) < 0.0) {
                break;
            }
            if (abs($f1) < abs($f2)) {
                $f1 = self::XNPV($x1 += 1.6 * ($x1 - $x2), $values);
            } else {
                $f2 = self::XNPV($x2 += 1.6 * ($x2 - $x1), $values);
            }
        }

        if (($f1 * $f2) > 0.0) {
            return null;
        }

        $f = self::XNPV($x1, $values);
        if ($f < 0.0) {
            $rtb = $x1;
            $dx = $x2 - $x1;
        } else {
            $rtb = $x2;
            $dx = $x1 - $x2;
        }

        for ($i = 0; $i < 100; $i++) {
            $dx *= 0.5;
            $x_mid = $rtb + $dx;
            $f_mid = self::XNPV($x_mid, $values);
            if ($f_mid <= 0.0) {
                $rtb = $x_mid;
            }
            if ((abs($f_mid) < 1.0e-6) || (abs($dx) < 1.0e-6)) {
                return $x_mid;
            }
        }

        return null;
    }

    public static function IRR($values, $guess = null) {

        // Initialize dates and check that $values contains at least one positive value and one negative value
        $dates = [];
        $positive = false;
        $negative = false;
        for ($i = 0; $i < count($values); $i++) {
            $dates[$i] = ($i === 0) ? 0 : $dates[$i - 1] + 365;
            if ($values[$i] > 0) {
                $positive = true;
            }
            if ($values[$i] < 0) {
                $negative = true;
            }
        }

        // Return error if $values does not contain at least one positive value and one negative value
        if (!$positive || !$negative) {
            return NULL;
        }

        // Initialize $guess and resultRate
        $guess = !isset($guess) ? 0.1 : $guess;
        $resultRate = $guess;

        // Set maximum epsilon for end of iteration
        $epsMax = 1e-10;

        // Set maximum number of iterations
        $iterMax = 50;

        // Implement Newton's method
        $iteration = 0;
        $contLoop = true;
        do {
            $resultValue = self::irrResult($values, $dates, $resultRate);
            $irrResultDeriv = self::irrResultDeriv($values, $dates, $resultRate);
            $newRate = $resultRate - ($irrResultDeriv != 0 ? $resultValue / $irrResultDeriv : 0);
            $epsRate = abs($newRate - $resultRate);
            $resultRate = $newRate;
            $contLoop = ($epsRate > $epsMax) && (abs($resultValue) > $epsMax);
        } while ($contLoop && ( ++$iteration < $iterMax));

        if ($contLoop) {
            return NULL;
        }

        // Return internal rate of return
        return $resultRate;
    }

    // Calculates the resulting amount
    private static function irrResult($values, $dates, $rate) {
        $r = $rate + 1;
        $result = $values[0];
        for ($i = 1; $i < count($values); $i++) {
            $result += $values[$i] / pow($r, ($dates[$i] - $dates[0]) / 365);
        }
        return $result;
    }

    // Calculates the first derivation
    private static function irrResultDeriv($values, $dates, $rate) {
        $r = $rate + 1;
        $result = 0;
        for ($i = 1; $i < count($values); $i++) {
            $frac = ($dates[$i] - $dates[0]) / 365;
            $result -= $frac * $values[$i] / pow($r, $frac + 1);
        }
        return $result;
    }

}
