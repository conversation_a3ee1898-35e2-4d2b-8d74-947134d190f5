SELECT CONCAT_WS(
'§',
'Código/ID',
'División',
'<PERSON><PERSON><PERSON>',
'Sublínea',
'Nombre',
'<PERSON><PERSON>',
'<PERSON><PERSON>',
'N° Parte',
'Activo',
'Marcado para web',
'Imagen?',
'Características',
'U. Medida',
'Equivalencia',
'Afecto a IGV',
'Precio Web',
'Precio Mín',
'Precio',
'Atributos',
(SELECT GROUP_CONCAT(W.warehouse_name ORDER BY W.warehouse_id SEPARATOR '§') FROM warehouse W)
) AS valor
UNION
SELECT * FROM (
SELECT
CONCAT_WS(
'§',
M.product_id,
D.division_name,
L.line_name,
S.subline_name,
P.product_name,
MA.mark_name,
IFNULL(M.model, '-'),
IFNULL(M.part_number, '-'),
IF(P.`status`, 'SI', 'NO'),
IF(P.frontend, 'SI', 'NO'),
IF(R.resource_number IS NOT NULL, 'SI', 'NO'),
REPLACE(REPLACE(IFNULL(M.characteristic, '-'), '\n', ' '), '\r', ' '),
PR.measure_name,
PR.equivalence,
IF(P.igv_affected, 'SI', 'NO'),
round(PR.wprice_pen * (1 + GV.igv * P.igv_affected), 2),
round(PR.mprice_pen * (1 + GV.igv * P.igv_affected), 2),
round(PR.aprice_pen * (1 + GV.igv * P.igv_affected), 2),
REPLACE(REPLACE(IFNULL((
SELECT GROUP_CONCAT(CONCAT(F.feature_name, ': ', IF(LENGTH(OP2.`value`) > 0, OP2.`value`, '(Vacío)')) SEPARATOR '|')
FROM owner_pair OP1
JOIN owner_pair OP2 ON OP2.parent_id = OP1.pair_id
JOIN feature F on F.feature_id = OP2.owner_id
where OP1.type = 'MerchandiseFeature' AND OP1.`owner` = 'Merchandise' AND OP1.owner_id = M.product_id
), '-'), '\n', ' '), '\r', ' '),
(
SELECT GROUP_CONCAT(IFNULL(K.default_stock, 0) ORDER BY MM.warehouse_id ASC SEPARATOR '§')
FROM merchandise_master MM
LEFT JOIN kardex K on K.kardex_id = MM.kardex_id
where MM.product_id = M.product_id
)
)

FROM merchandise M
JOIN product P on P.product_id = M.product_id
JOIN subline S on S.subline_id = M.subline_id
JOIN line L on L.line_id = S.line_id
JOIN division D on D.division_id = L.division_id
JOIN mark MA on MA.mark_id = M.mark_id
JOIN presentation PR on PR.product_id = P.product_id AND PR.`default`
JOIN global_var GV on GV.organization_id = 1
LEFT JOIN resource R on R.`owner` = 'Product' AND R.owner_id = M.product_id AND R.type = 'Image' AND R.resource_number = 1
ORDER BY D.division_name, L.line_name, S.subline_name, P.product_name
) X;