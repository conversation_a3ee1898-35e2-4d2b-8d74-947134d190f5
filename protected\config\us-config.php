<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'us-constants.php');

$message = "<meta charset='UTF-8'><h1>SIAN se está actualizando</h1>"
        . "<p>El sistema no estará disponible hasta el día de mañana 17/10/2024</p>"
        . "<br>"
        . "Gracias por su comprensión.</p>";

//echo $message;
//die();

switch ($_SERVER["SERVER_NAME"]) {
    //PRODUCTION
    case 'adjeirl.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-adjeirl.php');
        break;
    case 'demo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-demo.php');
        break;
    case 'bolivar.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-bolivar.php');
        break;
    case 'chanchan.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-chanchan.php');
        break;
    case 'concatuay.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-concatuay.php');
        break;
    case 'coneduascope.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-coneduascope.php');
        break;
    case 'coneduchepen.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-coneduchepen.php');
        break;
    case 'conedunorte.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-conedunorte.php');
        break;
    case 'conedupacasmayo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-conedupacasmayo.php');
        break;
    case 'conlaredo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-conlaredo.php');
        break;
    case 'consolnaciente.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-consolnaciente.php');
        break;
    case 'consorcio.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-consorcio.php');
        break;
    case 'consuhuanchaco.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-consuhuanchaco.php');
        break;
    case 'convial.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-convial.php');
        break;
    case 'ctt.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-ctt.php');
        break;
    case 'cuenca.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-cuenca.php');
        break;
    case 'econova.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-econova.php');
        break;
    case 'grupohardtech.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-grupohardtech.php');
        break;
    case 'hardtech.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-hardtech.php');
        break;
    case 'hardtechretail.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-hardtechretail.php');
        break;
    case 'hyp.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-hyp.php');
        break;
    case 'komiza.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-komiza.php');
        break;
    case 'logisystem.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-logisystem.php');
        break;
    case 'myd.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-myd.php');
        break;
    case 'pacanga.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-pacanga.php');
        break;
    case 'puntov.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-puntov.php');
        break;
    case 'sanfernando.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-sanfernando.php');
        break;
    case 'sanlorenzo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-sanlorenzo.php');
        break;
    case 'santafe.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-santafe.php');
        break;
    case 'segurimport.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-segurimport.php');
        break;
    case 'sipe.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-sipe.php');
        break;
    case 'supermercadosmia.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-supermercadosmia.php');
        break;
    case 'tambopampamarca.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-tambopampamarca.php');
        break;
    case 'totoropampa.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-totoropampa.php');
        break;
    case 'viensac.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'production' . DIRECTORY_SEPARATOR . 'us-viensac.php');
        break;
    //STAGING
    case 'stagingadjeirl.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingadjeirl.php');
        break;
    case 'stagingbolivar.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingbolivar.php');
        break;
    case 'stagingchanchan.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingchanchan.php');
        break;
    case 'stagingconcatuay.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingconcatuay.php');
        break;
    case 'stagingconeduascope.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingconeduascope.php');
        break;
    case 'stagingconeduchepen.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingconeduchepen.php');
        break;
    case 'stagingconedunorte.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingconedunorte.php');
        break;
    case 'stagingconedupacasmayo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingconedupacasmayo.php');
        break;
    case 'stagingconlaredo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingconlaredo.php');
        break;
    case 'stagingconsolnaciente.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingconsolnaciente.php');
        break;
    case 'stagingconsorcio.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingconsorcio.php');
        break;
    case 'stagingconsuhuanchaco.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingconsuhuanchaco.php');
        break;
    case 'stagingconvial.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingconvial.php');
        break;
    case 'stagingctt.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingctt.php');
        break;
    case 'stagingcuenca.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingcuenca.php');
        break;
    case 'stagingeconova.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingeconova.php');
        break;
    case 'stagingmyd.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingmyd.php');
        break;
    case 'staginggrupohardtech.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-staginggrupohardtech.php');
        break;
    case 'staginghardtech.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-staginghardtech.php');
        break;
    case 'staginghardtechretail.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-staginghardtechretail.php');
        break;
    case 'staginghyp.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-staginghyp.php');
        break;
    case 'staginglogisystem.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-staginglogisystem.php');
        break;
    case 'stagingkomiza.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingkomiza.php');
        break;
    case 'stagingpacanga.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingpacanga.php');
        break;
    case 'stagingpuntov.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingpuntov.php');
        break;
    case 'stagingsanfernando.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingsanfernando.php');
        break;
    case 'stagingsanlorenzo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingsanlorenzo.php');
        break;
    case 'stagingsantafe.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingsantafe.php');
        break;
    case 'stagingsegurimport.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingsegurimport.php');
        break;
    case 'stagingsipe.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingsipe.php');
        break;
    case 'stagingsupermercadosmia.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingsupermercadosmia.php');
        break;
    case 'stagingtambopampamarca.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingtambopampamarca.php');
        break;
    case 'stagingtotoropampa.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingtotoropampa.php');
        break;
    case 'stagingviensac.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging' . DIRECTORY_SEPARATOR . 'us-stagingviensac.php');
        break;
    //STAGING 2
    case 'staging2adjeirl.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2adjeirl.php');
        break;
    case 'staging2bolivar.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2bolivar.php');
        break;
    case 'staging2chanchan.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2chanchan.php');
        break;
    case 'staging2concatuay.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2concatuay.php');
        break;
    case 'staging2coneduascope.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2coneduascope.php');
        break;
    case 'staging2coneduchepen.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2coneduchepen.php');
        break;
    case 'staging2conedunorte.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2conedunorte.php');
        break;
    case 'staging2conedupacasmayo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2conedupacasmayo.php');
        break;
    case 'staging2conlaredo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2conlaredo.php');
        break;
    case 'staging2consolnaciente.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2consolnaciente.php');
        break;
    case 'staging2consorcio.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2consorcio.php');
        break;
    case 'staging2consuhuanchaco.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2consuhuanchaco.php');
        break;
    case 'staging2convial.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2convial.php');
        break;
    case 'staging2ctt.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2ctt.php');
        break;
    case 'staging2cuenca.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2cuenca.php');
        break;
    case 'staging2econova.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2econova.php');
        break;
    case 'staging2myd.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2myd.php');
        break;
    case 'staging2grupohardtech.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2grupohardtech.php');
        break;
    case 'staging2hardtech.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2hardtech.php');
        break;
    case 'staging2hardtechretail.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2hardtechretail.php');
        break;
    case 'staging2hyp.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2hyp.php');
        break;
    case 'staging2logisystem.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2logisystem.php');
        break;
    case 'staging2komiza.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2komiza.php');
        break;
    case 'staging2pacanga.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2pacanga.php');
        break;
    case 'staging2puntov.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2puntov.php');
        break;
    case 'staging2sanfernando.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2sanfernando.php');
        break;
    case 'staging2sanlorenzo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2sanlorenzo.php');
        break;
    case 'staging2santafe.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2santafe.php');
        break;
    case 'staging2segurimport.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2segurimport.php');
        break;
    case 'staging2sipe.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2sipe.php');
        break;
    case 'staging2supermercadosmia.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2supermercadosmia.php');
        break;
    case 'staging2tambopampamarca.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2tambopampamarca.php');
        break;
    case 'staging2totoropampa.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2totoropampa.php');
        break;
    case 'staging2viensac.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging2' . DIRECTORY_SEPARATOR . 'us-staging2viensac.php');
        break;
    //STAGING 3
    case 'staging3adjeirl.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3adjeirl.php');
        break;
    case 'staging3bolivar.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3bolivar.php');
        break;
    case 'staging3chanchan.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3chanchan.php');
        break;
    case 'staging3concatuay.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3concatuay.php');
        break;
    case 'staging3coneduascope.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3coneduascope.php');
        break;
    case 'staging3coneduchepen.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3coneduchepen.php');
        break;
    case 'staging3conedunorte.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3conedunorte.php');
        break;
    case 'staging3conedupacasmayo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3conedupacasmayo.php');
        break;
    case 'staging3conlaredo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3conlaredo.php');
        break;
    case 'staging3consolnaciente.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3consolnaciente.php');
        break;
    case 'staging3consorcio.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3consorcio.php');
        break;
    case 'staging3consuhuanchaco.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3consuhuanchaco.php');
        break;
    case 'staging3convial.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3convial.php');
        break;
    case 'staging3ctt.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3ctt.php');
        break;
    case 'staging3cuenca.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3cuenca.php');
        break;
    case 'staging3econova.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3econova.php');
        break;
    case 'staging3myd.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3myd.php');
        break;
    case 'staging3grupohardtech.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3grupohardtech.php');
        break;
    case 'staging3hardtech.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3hardtech.php');
        break;
    case 'staging3hardtechretail.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3hardtechretail.php');
        break;
    case 'staging3hyp.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3hyp.php');
        break;
    case 'staging3logisystem.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3logisystem.php');
        break;
    case 'staging3komiza.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3komiza.php');
        break;
    case 'staging3pacanga.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3pacanga.php');
        break;
    case 'staging3puntov.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3puntov.php');
        break;
    case 'staging3sanfernando.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3sanfernando.php');
        break;
    case 'staging3sanlorenzo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3sanlorenzo.php');
        break;
    case 'staging3santafe.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3santafe.php');
        break;
    case 'staging3segurimport.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3segurimport.php');
        break;
    case 'staging3sipe.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3sipe.php');
        break;
    case 'staging3supermercadosmia.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3supermercadosmia.php');
        break;
    case 'staging3tambopampamarca.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3tambopampamarca.php');
        break;
    case 'staging3totoropampa.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3totoropampa.php');
        break;
    case 'staging3viensac.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'staging3' . DIRECTORY_SEPARATOR . 'us-staging3viensac.php');
        break;
    //TEST
    case 'testadjeirl.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testadjeirl.php');
        break;
    case 'testbolivar.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testbolivar.php');
        break;
    case 'testchanchan.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testchanchan.php');
        break;
    case 'testconcatuay.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testconcatuay.php');
        break;
    case 'testconeduascope.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testconeduascope.php');
        break;
    case 'testconeduchepen.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testconeduchepen.php');
        break;
    case 'testconedunorte.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testconedunorte.php');
        break;
    case 'testconedupacasmayo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testconedupacasmayo.php');
        break;
    case 'testconlaredo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testconlaredo.php');
        break;
    case 'testconsolnaciente.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testconsolnaciente.php');
        break;
    case 'testconsorcio.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testconsorcio.php');
        break;
    case 'testconsuhuanchaco.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testconsuhuanchaco.php');
        break;
    case 'testconvial.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testconvial.php');
        break;
    case 'testctt.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testctt.php');
        break;
    case 'testcuenca.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testcuenca.php');
        break;
    case 'testeconova.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testeconova.php');
        break;
    case 'testmyd.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testmyd.php');
        break;
    case 'testgrupohardtech.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testgrupohardtech.php');
        break;
    case 'testhardtech.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testhardtech.php');
        break;
    case 'testhardtechretail.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testhardtechretail.php');
        break;
    case 'testhyp.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testhyp.php');
        break;
    case 'testkomiza.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testkomiza.php');
        break;
    case 'testlogisystem.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testlogisystem.php');
        break;
    case 'testpacanga.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testpacanga.php');
        break;
    case 'testpuntov.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testpuntov.php');
        break;
    case 'testsanfernando.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testsanfernando.php');
        break;
    case 'testsanlorenzo.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testsanlorenzo.php');
        break;
    case 'testsantafe.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testsantafe.php');
        break;
    case 'testsegurimport.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testsegurimport.php');
        break;
    case 'testsipe.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testsipe.php');
        break;
    case 'testsupermercadosmia.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testsupermercadosmia.php');
        break;
    case 'testtambopampamarca.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testtambopampamarca.php');
        break;
    case 'testtotoropampa.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testtotoropampa.php');
        break;
    case 'testviensac.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'test' . DIRECTORY_SEPARATOR . 'us-testviensac.php');
        break;
    //LOCAL
    case 'sian':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'us-sian.php');
        break;
    case 'sianmaster':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'us-sianmaster.php');
        break;
    //TEMP
    case 'temp.siansystem.com':
        include(dirname(__FILE__) . DIRECTORY_SEPARATOR . 'us-temp.php');
        break;
    default:
        throw new Exception('Dominio inválido!');
}