<?php

class SIANMovementPaymentLinks extends CWidget {

    public $id;
    public $form;
    public $model;
    public $movement_code;
    public $readonly = false;
    public $api_user_items = [];
    public $select_id;
    public $input_id;
    //PRIVATE
    private $clientScript;
    private $controller;

    public function init() {

        $this->clientScript = Yii::app()->clientScript;
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->select_id = isset($this->select_id) ? $this->select_id : $this->controller->getServerId();
        $this->input_id = isset($this->input_id) ? $this->input_id : $this->controller->getServerId();

        //SCRIPTS
        $this->clientScript->registerScript($this->id, "
           
         $('body').on('change','#{$this->select_id}', function(){
            var optionObj = $(this).find('option:selected');
            if(optionObj.val() != ''){
                var s_link = optionObj.data('pay_url_pattern');
                $('#{$this->input_id}').val(s_link.replace('{{MOVEMENT_CODE}}', '{$this->movement_code}'));
            }            
        }); 
                    
        function SIANMovementPaymentLinksFillApiUser(select_id, api_user_items, api_user_id)
        {
            var selectObj = $('#' + select_id);
            var found = false;
                      
            $.each(api_user_items, function (index, api_user_item) {
                var optionObj = $('<option>').val(api_user_item.api_user_id)
                        .text(api_user_item.username)
                        .data('pay_url_pattern', api_user_item.pay_url_pattern);

                selectObj.append(optionObj);

                if (api_user_id == api_user_item.api_user_id)
                {
                    found = true;
                }
            });

            if (found)
            {
                selectObj.val(api_user_id);
            }

            //Seteamos last
            selectObj.change();
        }
            
        $(document).ready(function() {
            var apiUserItems = " . CJSON::encode($this->api_user_items) . ";
            SIANMovementPaymentLinksFillApiUser('{$this->select_id}', apiUserItems, 0);
        });

        ", CClientScript::POS_END);
        
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "Generar link",
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id
            )
        ));
        echo '<div class="row">';
        echo '<div class="col-lg-4 col-md-4 col-sm-6 col-xs-12">';
        echo $this->form->dropDownListRow($this->model, 'api_user_id', [], array(
            'id' => $this->select_id
        ));
        echo '</div>';
        echo '<div class="col-lg-8 col-md-8 col-sm-6 col-xs-12">';
        echo $this->form->textFieldRow($this->model, 'link', array(
            'readonly' => true,
            'id' => $this->input_id
        ));
        echo '</div>';
        echo '</div>';
    }
}
