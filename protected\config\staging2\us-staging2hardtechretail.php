<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'staging2hardtechretail.siansystem.com/admin';
$domain = 'https://staging2hardtechretail.siansystem.com';
$report_domain = 'rstaging2.siansystem.com';
$org = 'hardtechretail';
$us = 'us_staging2';
$database_server = '69.10.37.246';
$database_name = 'hardtechretail_staging2';
$database_username = 'sian_test';
$database_password = '75nppt6vr57lx4';
$mongo_enabled = true;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_EFACT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'hardtechretail.p12';
$e_billing_certificate_pass = 'aWxHiS3BsxU4LqEu';
$e_billing_ri = '';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20481066094BILLING7',
        'password' => 'EXITO123'
    ],
    YII_OSE_EFACT => [
        'username' => '20481066094',
        'password' => 'CzUKk08RZ3'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_STAGING;
$environment = YII_ENVIRONMENT_STAGING;
