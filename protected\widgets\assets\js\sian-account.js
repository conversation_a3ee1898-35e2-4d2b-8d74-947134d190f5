function SIANAccountLoadDestinies(table_id, destiny_ids, destiny_names, destiny_defaults)
{
    var tableObj = $('#' + table_id);
    var owner = tableObj.data('owner');
    var sian_owner_pair_id = tableObj.data('sian_owner_pair_id');

    var ownerPairTableObj = $('#' + sian_owner_pair_id);
    var count = parseInt(ownerPairTableObj.data('count'));

    if (count === 0 && destiny_ids.length > 0)
    {
        destiny_ids = destiny_ids.split('&');
        destiny_names = destiny_names.split('&');
        destiny_defaults = destiny_defaults.split('&');

        for (var i = 0; i < destiny_ids.length; i++) {
            SIANOwnerPairAddItem(sian_owner_pair_id, owner, destiny_ids[i], destiny_names[i], destiny_defaults[i], []);
        }

        //UPDATE
        SIANOwnerPairGetIds(sian_owner_pair_id);
    }
}