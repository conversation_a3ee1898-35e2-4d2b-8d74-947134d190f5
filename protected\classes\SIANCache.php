<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANCache
 *
 * <AUTHOR>
 */
class SIANCache {

    const GEOLOC = 'geoloc';
    const SCENARIOS = 'scenarios';
    const SCENARIO_CONTROLS = 'scenarioControls';
    const ORGANIZATION = 'organization';
    const SKILL_MOVEMENT = 'skillMovement';
    const SKILL_ENTRY_GROUP = 'skillEntryGroup';
    const SKILL_DIVISION = 'skillDivision';
    const SKILL_LINE = 'skillLine';
    const SKILL_SUBLINE = 'skillSubline';
    const SKILL_MARK = 'skillMark';
    const SKILL_MERCHANDISE = 'skillMerchandise';
    const SKILL_COMBO = 'skillCombo';
    const SKILL_OBJECT = 'skillObject';
    const SKILL_SUBCATEGORY = 'skillSubcategory';
    const SKILL_CATEGORY = 'skillCategory';
    const SKILL_WAREHOUSE = 'skillWarehouse';
    const SKILL_STORE = 'skillStore';
    const SKILL_LOCKER_BOX_REQUEST = 'skillLockerBoxRequest';
    const SKILL_LOCKER_REQUEST = 'skillLockerRequest';
    const DEFAULT_WAREHOUSE_ID = 'defaultWarehouse';
    const COMMERCIAL_WAREHOUSE_IDS = 'commercialWarehouses';
    const BUSINESS_UNIT_DIVISIONS = 'businessUnitDivision';
    const STORE_BUSINESS_UNIT = 'storeBusinessUnit';

    public static function getPreffix() {
        return str_replace('-', '_', USString::generateAlias(Util::getServerName())) . '_';
    }

    public static function get($key) {
        $preffix = self::getPreffix();
        return Yii::app()->cache->get($preffix . $key);
    }

    public static function add($key, $value) {
        $preffix = self::getPreffix();
        Yii::app()->cache->delete($preffix . $key);
        return Yii::app()->cache->add($preffix . $key, $value);
    }

    public static function delete($key) {
        $preffix = self::getPreffix();
        return Yii::app()->cache->delete($preffix . $key);
    }

    public static function flushAll() {
        return Yii::app()->cache->flush();
    }

}
