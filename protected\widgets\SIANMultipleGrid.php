<?php

class SIANMultipleGrid extends CWidget {

    public $id;
    public $form;
    public $model;
    public $readonly = false;
    public $title = 'Movimientos';
    public $label = 'Movimiento';
    public $view = null;
    public $scenario = null;
    public $routes = [];
    public $manual = false;
    public $fixed = 2;
    public $step = 0.01;
    public $showManual = true;
    public $showObservation = true;
    public $showBalance = false;
    //CONTROL
    private $controller;
    public $aux_person_autocomplete_id = null;
    //PRIVATE
    private $manual_id;
    private $reset_id;
    private $link_movement_id;
    private $link_aux_id;
    private $link_value_id;
    private $link_text_id;
    private $link_account_code_id;
    private $link_owner_input_id;
    private $link_owner_id_input_id;
    private $link_aux_person_id;
    private $link_amount_id;
    private $link_emission_date_id;
    private $link_route_id;
    private $link_currency_id;
    private $link_exchange_id;
    private $link_detail_id;
    private $link_balance_pen_id;
    private $link_balance_usd_id;
    private $balance_pen_input_id;
    private $balance_usd_input_id;
    private $total_input_id;
    private $parent_currency_input_id;
    private $add_button_id;
    public $onChangeTotal = '';

    public function init() {

        //CONTROL
        if (is_null($this->aux_person_autocomplete_id)) {
            throw new Exception('Debe especificar el ID de buscador de Involucrado!');
        }

        $this->controller = Yii::app()->controller;

        //GRID ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //PRIVATE
        $this->manual_id = $this->controller->getServerId();
        $this->reset_id = $this->controller->getServerId();
        $this->link_aux_id = $this->controller->getServerId();
        $this->link_value_id = $this->controller->getServerId();
        $this->link_text_id = $this->controller->getServerId();
        $this->link_movement_id = $this->controller->getServerId();
        $this->link_account_code_id = $this->controller->getServerId();
        $this->link_owner_input_id = $this->controller->getServerId();
        $this->link_owner_id_input_id = $this->controller->getServerId();
        $this->link_aux_person_id = $this->controller->getServerId();
        $this->link_amount_id = $this->controller->getServerId();
        $this->link_route_id = $this->controller->getServerId();
        $this->link_currency_id = $this->controller->getServerId();
        $this->link_exchange_id = $this->controller->getServerId();
        $this->link_detail_id = $this->controller->getServerId();
        $this->link_emission_date_id = $this->controller->getServerId();
        $this->link_balance_pen_id = $this->controller->getServerId();
        $this->link_balance_usd_id = $this->controller->getServerId();
        $this->balance_pen_input_id = $this->controller->getServerId();
        $this->balance_usd_input_id = $this->controller->getServerId();
        $this->parent_currency_input_id = $this->controller->getServerId();
        $this->total_input_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();

        //MOVEMENT ITEMS
        $movementItems = [];
        foreach ($this->model->tempLinks as $link) {
            if ($link->origin == MovementLink::ORIGIN_FORM) {
                $attributes = [];
                $attributes['movement_parent_id'] = $link->movement_parent_id;
                $attributes['parent_entry_id'] = $link->parent_entry_id;
                $attributes['parent_account_code'] = $link->parent_account_code;
                $attributes['parent_owner'] = $link->parent_owner;
                $attributes['parent_owner_id'] = $link->parent_owner_id;
                $attributes['parent_route'] = $link->parent_route;
                $attributes['parent_document'] = $link->parent_document;
                $attributes['parent_aux_person_id'] = $link->parent_aux_person_id;
                $attributes['parent_aux_person_name'] = $link->parent_aux_person_name;
                $attributes['parent_emission_date'] = $link->parent_emission_date;
                $attributes['parent_currency'] = $link->parent_currency;
                $attributes['parent_exchange_rate'] = $link->parent_exchange_rate;
                $attributes['parent_detail'] = $link->parent_detail;
                $attributes['balance_pen'] = round($link->balance_pen, $this->fixed);
                $attributes['balance_usd'] = round($link->balance_usd, $this->fixed);
                $attributes['amount'] = round($link->amount, $this->fixed);
                $attributes['errors'] = $link->getErrors();

                $movementItems[] = $attributes;
            }
        }

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-multiple-grid.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        " . ($this->manual ? "
            window.auxPersonIds = [];
        " : "
            var aux_person_id = USAutocompleteField('{$this->aux_person_autocomplete_id}', 'value');
            window.auxPersonIds = aux_person_id.length > 0 ? [aux_person_id] : [];
        ") . "

        //ACCESSOS
        $('#{$this->id}').data('view-access', " . CJSON::encode($this->getAccess()) . ");
        //COUNT
        $('#{$this->id}').data('fixed', {$this->fixed});
        $('#{$this->id}').data('step', {$this->step});
        $('#{$this->id}').data('count', 0);
        //COUNT
        $('#{$this->id}').data('labels', {
            parent_document: '{$this->model->movement->getAttributeLabel('links.document')}',
            parent_aux_person_name: '{$this->model->movement->getAttributeLabel('links.movement.aux_person_id')}',
            parent_emission_date: '{$this->model->movement->getAttributeLabel('links.emission_date')}',
            amount: '{$this->model->movement->getAttributeLabel('linkParents.amount')}',
        });
        //MONEDA
        $('#{$this->id}').data('currency', '{$this->model->movement->currency}');

        //MOVIMIENTOS
        var array = " . CJSON::encode($movementItems) . "

        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANMultipleGridAddItem('{$this->id}', array[i]['movement_parent_id'], array[i]['parent_entry_id'], array[i]['parent_account_code'], array[i]['parent_owner'], array[i]['parent_owner_id'], array[i]['parent_route'], array[i]['parent_document'], array[i]['parent_aux_person_id'], array[i]['parent_aux_person_name'], array[i]['parent_emission_date'], array[i]['parent_currency'], array[i]['parent_exchange_rate'], array[i]['parent_detail'],  array[i]['balance_pen'], array[i]['balance_usd'], array[i]['amount'], array[i]['errors'], " . json_encode($this->readonly) . ");
            }
        }
        else
        {
            $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
        }

        //UPDATE
        SIANMultipleGridUpdate('{$this->id}');
        unfocusable();

        //CURRENCY SYMBOL
        $('span.currency-symbol').text('" . Currency::getSymbol($this->model->movement->currency) . "');

        " . ($this->showBalance ? "
        switch('{$this->model->movement->currency}')
        {
            case '" . Currency::PEN . "':
                $('input#{$this->balance_pen_input_id}').parents('div.form-group').show();
                $('input#{$this->balance_usd_input_id}').parents('div.form-group').hide();
            break;
            case '" . Currency::USD . "':
                $('input#{$this->balance_pen_input_id}').parents('div.form-group').hide();
                $('input#{$this->balance_usd_input_id}').parents('div.form-group').show();
            break;
            default:
                bootbox.alert(us_message('Moneda Inválida!', 'error'));
            break;
        }
        " : "") . "

        
        //CHANGE
        $('#{$this->id}').data('changeCurrency', function(currency){

            var table = $('#{$this->id}');
            var exchange_rate = table.data('exchange_rate');

            table.find('tr.sian-multiple-grid-item').each(function(index) {
                var item = $(this);
                var balance_pen = item.find('input.sian-multiple-grid-item-balance-pen').floatVal({$this->fixed});
                var balance_usd = item.find('input.sian-multiple-grid-item-balance-usd').floatVal({$this->fixed});

                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-multiple-grid-item-amount').toPen(exchange_rate, {$this->fixed}, {max: balance_pen});
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-multiple-grid-item-amount').toUsd(exchange_rate, {$this->fixed}, {max: balance_usd}); 
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
            });

            " . ($this->showBalance ? "
            //Actualizamos max
            switch(currency)
            {
                case '" . Currency::PEN . "':
                    $('input#{$this->balance_pen_input_id}').parents('div.form-group').show();
                    $('input#{$this->balance_usd_input_id}').parents('div.form-group').hide();
                break;
                case '" . Currency::USD . "':
                    $('input#{$this->balance_pen_input_id}').parents('div.form-group').hide();
                    $('input#{$this->balance_usd_input_id}').parents('div.form-group').show();
                break;
                default:
                    bootbox.alert(us_message('Moneda Inválida!', 'error'));
                break;
            }
            " : "") . "

            table.data('currency', currency);
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            {$this->id}Clear();
            //Actualizamos los montos
            SIANMultipleGridUpdateAmounts('{$this->id}');
        });
        
        //CHANGE 
        $('#{$this->id}').data('changeAux', function(aux_person_id){

            var manual = " . ($this->manual ? "$('#{$this->manual_id}').bootstrapSwitch('state')" : "false") . ";
            
            if(!manual)
            {
                SIANMultipleGridRemoveAll('{$this->id}');

                window.auxPersonIds.splice(0, window.auxPersonIds.length);
                if(typeof aux_person_id !== 'undefined')
                {
                    window.auxPersonIds.push(aux_person_id);
                }

                {$this->id}Clear();
            }
        });
        
        //CHANGE 
        $('#{$this->id}').data('changeAux', function(aux_person_id){

            var manual = " . ($this->manual ? "$('#{$this->manual_id}').bootstrapSwitch('state')" : "false") . ";
            
            if(!manual)
            {
                SIANMultipleGridRemoveAll('{$this->id}');

                window.auxPersonIds.splice(0, window.auxPersonIds.length);
                if(typeof aux_person_id !== 'undefined')
                {
                    window.auxPersonIds.push(aux_person_id);
                }

                {$this->id}Clear();
            }
        });
       
        $('#{$this->id}').data('changeExchange', function(exchange_rate){

            var table = $('table#{$this->id}');
                
            //Seteamos
            table.data('exchange_rate', exchange_rate); 
            
            //Obtenemos
            var currency = table.data('currency');
            //MONTOS
            table.find('tr.sian-multiple-grid-item').each(function(index) {
                var item = $(this);

                var item_currency = item.find('input.sian-multiple-grid-item-currency').val();
                var balance_pen = item.find('input.sian-multiple-grid-item-balance-pen').floatVal({$this->fixed});
                var balance_usd = item.find('input.sian-multiple-grid-item-balance-usd').floatVal({$this->fixed});
                
                //Cambiamos los balances
                switch(item_currency)
                {
                    case '" . Currency::PEN . "':
                        balance_usd = balance_pen / exchange_rate;
                    break;
                    case '" . Currency::USD . "':
                        balance_pen = balance_usd * exchange_rate;
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
                
                //Actualizamos max
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-multiple-grid-item-amount').floatAttr('max', {$this->fixed}, balance_pen);
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-multiple-grid-item-amount').floatAttr('max', {$this->fixed}, balance_usd);
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
                
                //Seteamos balances
                item.find('input.sian-multiple-grid-item-balance-pen').floatVal({$this->fixed}, balance_pen);
                item.find('input.sian-multiple-grid-item-balance-usd').floatVal({$this->fixed}, balance_usd);

            });
            
            " . ($this->showBalance ? "
            var parent_currency = $('#{$this->parent_currency_input_id}').val();
            var balance_pen = $('#{$this->balance_pen_input_id}').floatVal({$this->fixed});
            var balance_usd = $('#{$this->balance_usd_input_id}').floatVal({$this->fixed});
                
            //Cambiamos los balances
            switch(parent_currency)
            {
                case '" . Currency::PEN . "':
                    $('#{$this->balance_usd_input_id}').floatVal({$this->fixed}, balance_pen / exchange_rate);
                break;
                case '" . Currency::USD . "':
                    $('#{$this->balance_pen_input_id}').floatVal({$this->fixed}, balance_usd * exchange_rate);
                break;
                default:
                    bootbox.alert(us_message('Moneda Inválida!', 'error'));
                break;
            }
            " : "") . "

            //Limpiamos
            {$this->id}Clear();
            //Actualizamos los montos
            SIANMultipleGridUpdateAmounts('{$this->id}');
        });

        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var movement_parent_id = $('#{$this->link_movement_id}').val();
            var parent_entry_id = $('#{$this->link_value_id}').val();
            var parent_account_code = $('#{$this->link_account_code_id}').val();
            var parent_owner = $('#{$this->link_owner_input_id}').val();
            var parent_owner_id = $('#{$this->link_owner_id_input_id}').val();
            var parent_route = $('#{$this->link_route_id}').val();
            var parent_document = $('#{$this->link_aux_id}').val();
            var parent_aux_person_id = $('#{$this->link_aux_person_id}').val();
            var parent_aux_person_name = $('#{$this->link_text_id}').val();
            var parent_emission_date = $('#{$this->link_emission_date_id}').val();
            var parent_currency = $('#{$this->link_currency_id}').val();
            var parent_exchange_rate = $('#{$this->link_exchange_id}').val();
            var parent_detail = '';
            var balance_pen = parseFloat($('#{$this->link_balance_pen_id}').val());
            var balance_usd = parseFloat($('#{$this->link_balance_usd_id}').val());
            var amount = parseFloat($('#{$this->link_amount_id}').val());

            if (parent_entry_id.length === 0)
            {
                $('#{$this->link_aux_id}').focus();
                return;
            }            

            SIANMultipleGridAddItem('{$this->id}', movement_parent_id, parent_entry_id, parent_account_code, parent_owner, parent_owner_id, parent_route, parent_document, parent_aux_person_id, parent_aux_person_name, parent_emission_date, parent_currency, parent_exchange_rate, parent_detail, balance_pen, balance_usd, amount, [], " . json_encode($this->readonly) . ");
            {$this->id}Clear();
                
            //FOCUS
            $('#{$this->link_aux_id}').focus();
            SIANMultipleGridUpdate('{$this->id}');
            unfocusable();
        });
       
        function {$this->id}Clear()
        {
            $('#{$this->reset_id}').click();

            $('#{$this->link_account_code_id}').val(null);
            $('#{$this->link_route_id}').val(null);
            $('#{$this->link_emission_date_id}').val(null);
            $('#{$this->link_balance_pen_id}').val(0);
            $('#{$this->link_balance_usd_id}').val(0);
            $('#{$this->link_amount_id}').val(0);
            $('#{$this->link_amount_id}').attr('max', 0);
        }
        
        function SIANMultipleGridChangeTotal()
        {
            var total = $('#{$this->total_input_id}').val();
            {$this->onChangeTotal}
        } 
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->title,
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempLinks') ? 'us-error' : ''
            )
        ));

        if (!$this->readonly) {
            echo "<div class='row'>";
            $colX = 7;
            //Si está activado elcheck de manual
            if ($this->manual) {
                $colX = 5;
                echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12' " . (!$this->showManual ? "style='display:none;'" : "") . ">";
                $this->widget('application.widgets.USSwitch', array(
                    'id' => $this->manual_id,
                    'model' => $this->model,
                    'attribute' => 'manual',
                    'switchChange' => "if (this.checked) {
                        window.auxPersonIds.splice(0, window.auxPersonIds.length);
                    }else{
                        SIANMultipleGridRemoveAll('{$this->id}');
                        window.auxPersonIds.push(USAutocompleteField('{$this->aux_person_autocomplete_id}', 'value'));
                    }",
                ));
                echo "</div>";
            }
            echo "<div class='col-lg-{$colX} col-md-{$colX} col-sm-6 col-xs-12'>";
            echo $this->widget('application.widgets.USAutocomplete', array(
                'reset_id' => $this->reset_id,
                'label' => $this->label,
                'name' => 'newLink[movement_id]',
                'aux_id' => $this->link_aux_id,
                'value_id' => $this->link_value_id,
                'text_id' => $this->link_text_id,
                'width' => '700px',
                'view' => array(
                    'model' => $this->view,
                    'scenario' => $this->scenario,
                    'attributes' => array(
                        array('name' => 'pk', 'hidden' => true, 'types' => array('id', 'value'), 'search' => false, 'not_in' => "window.entry_ids"),
                        array('name' => 'movement_id', 'hidden' => true, 'update' => "$('#{$this->link_movement_id}').val(movement_id);"),
                        array('name' => 'account_code', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->link_account_code_id}').val(account_code);"),
                        array('name' => 'owner', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->link_owner_input_id}').val(owner);"),
                        array('name' => 'owner_id', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->link_owner_id_input_id}').val(owner_id);"),
                        array('name' => 'document', 'width' => 20, 'types' => array('aux')),
                        array('name' => 'route', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->link_route_id}').val(route);"),
                        array('name' => 'aux_person_name', 'width' => 35, 'types' => array('text')),
                        array('name' => 'emission_date', 'width' => 15, 'update' => "$('#{$this->link_emission_date_id}').val(emission_date);"),
                        array('name' => 'currency', 'hidden' => true, 'update' => "$('#{$this->link_currency_id}').val(currency);"),
                        array('name' => 'currency_name', 'width' => 10),
                        array('name' => 'exchange_rate', 'hidden' => true, 'update' => "$('#{$this->link_exchange_id}').val(exchange_rate);"),
                        array('name' => 'total', 'width' => 10),
                        array('name' => 'balance', 'width' => 10, 'search' => false, 'update' => "

                                var form_currency = $('#{$this->id}').data('currency');
                                var exchange_rate = $('#{$this->id}').data('exchange_rate');
                                var balance_pen = 0;
                                var balance_usd = 0;
                                var amount = 0;
                                
                                if(currency == '" . Currency::PEN . "')
                                {
                                    balance_pen = balance;
                                    balance_usd = USMath.round(balance / exchange_rate, {$this->fixed});
                                }
                                else
                                {
                                    balance_pen = USMath.round(balance * exchange_rate, {$this->fixed});
                                    balance_usd = balance;
                                }
                                if(form_currency == '" . Currency::PEN . "')
                                {
                                    amount = balance_pen;
                                }
                                else
                                {
                                    amount = balance_usd;
                                }
                                $('#{$this->link_balance_pen_id}').floatVal({$this->fixed}, balance_pen);
                                $('#{$this->link_balance_usd_id}').floatVal({$this->fixed}, balance_usd);
                                $('#{$this->link_amount_id}').floatAttr('max', {$this->fixed}, amount).floatVal({$this->fixed}, amount);                                
                            "),
                        array('name' => 'aux_person_id', 'in' => "window.auxPersonIds", 'hidden' => true, 'update' => "$('#{$this->link_aux_person_id}').val(aux_person_id);"),
                        array('name' => 'route', 'hidden' => true, 'search' => false, 'in' => CJSON::encode($this->routes)),
                    ),
                    'params' => "{
                            invoker_scenario_id: {$this->model->movement->scenario->scenario_id},                            
                        }"
                ),
                'onsearch' => "

                    var manual = " . ($this->manual ? "$('#{$this->manual_id}').bootstrapSwitch('state')" : "false") . ";

                    if(!manual && window.auxPersonIds.length === 0)
                    {
                        bootbox.alert(us_message('Debe seleccionar un {$this->model->movement->getAttributeLabel('aux_person_id')}', 'warning'));
                            
                        USAutocompleteFocus('{$this->aux_person_autocomplete_id}');
                        return false;
                    }
                    ",
                'onreset' => "
                    $('#{$this->link_emission_date_id}').val(null);
                    $('#{$this->link_balance_pen_id}').val(0);
                    $('#{$this->link_balance_usd_id}').val(0);
                    $('#{$this->link_amount_id}').val(0).attr('max', 0);
                    ",), true);
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_aux_person_id,
            ));
            echo "</div>";

            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo SIANForm::textFieldNonActive($this->model->movement->getAttributeLabel('emission_date'), null, null, array(
                'id' => $this->link_emission_date_id,
                'readonly' => true,
                'placeholder' => $this->model->movement->getAttributeLabel('emission_date'),
            ));
            echo "</div>";

            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo SIANForm::numberFieldNonActive("Monto <span class='currency-symbol'></span>", null, 0, array(
                'id' => $this->link_amount_id,
                'class' => 'sian-multiple-grid-amount'
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_account_code_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_owner_input_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_owner_id_input_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_balance_pen_id,
                'class' => 'sian-multiple-grid-balance-pen'
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_balance_usd_id,
                'class' => 'sian-multiple-grid-balance-usd'
            ));
            echo CHtml::hiddenField(null, null, array(
                'id' => $this->link_route_id,
            ));
            echo CHtml::hiddenField(null, null, array(
                'id' => $this->link_movement_id,
            ));
            echo CHtml::hiddenField(null, null, array(
                'id' => $this->link_currency_id,
            ));
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->link_exchange_id,
            ));
            echo "</div>";

            echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-6 text-center'>";
            echo CHtml:: label('Agregar', $this->add_button_id, array(
            ));
            echo "<br/>";
            $this->widget('application.widgets.USButtons', array(
                'buttons' => array(
                    array(
                        'id' => $this->add_button_id,
                        'context' => 'primary',
                        'icon' => 'fa fa-lg fa-plus white',
                        'size' => 'default',
                        'title' => 'Añadir'
                    )
                )
            ));
            echo "</div>";

            echo "</div>";

            echo "<hr>";
        }

        echo "<table id='{$this->id}' class='table table-condensed table-hover'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th width='5%'>#</th>";
        echo "<th width='15%'>{$this->model->movement->getAttributeLabel('links.document')}</th>";
        echo "<th width='25%'>{$this->model->movement->getAttributeLabel('links.movement.aux_person_id')}</th>";
        echo "<th width='25%'>{$this->model->movement->getAttributeLabel('links.observation')}</th>";
        echo "<th width='15%'>{$this->model->movement->getAttributeLabel('links.emission_date')}</th>";
        echo "<th width='10%'>Monto <span class='currency-symbol'></span></th>";
        echo "<th width='5%'></th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody></tbody>";
        echo "<tfoot>";
        echo "<tr>";
        echo "<td colspan='5' style='text-align:right'><b>TOTAL <span class='currency-symbol'></span>:</b></td>";
        echo "<td style='text-align:right'>";
        if (isset($this->model->amount)) {
            echo $this->form->numberFieldRow($this->model, 'amount', array(
                'id' => $this->total_input_id,
                'label' => false,
                'class' => 'sian-multiple-grid-total-amount',
                'style' => 'text-align:right;',
                'readonly' => true,
            ));
        } else {
            echo $this->form->numberFieldNonActive('', 'amount', 0, array(
                'id' => $this->total_input_id,
                'label' => false,
                'class' => 'sian-multiple-grid-total-amount',
                'style' => 'text-align:right;',
                'disabled' => true,
            ));
        }

        echo "</td>";
        echo "<td></td>";
        echo "</tr>";
        if ($this->showBalance) {
            echo "<tr>";
            echo "<td colspan='5' style='text-align:right'><b>Disponible <span class='currency-symbol'></span>:</b></td>";
            echo "<td style='text-align:right'>";
            echo $this->form->numberFieldRow($this->model, 'balance_pen', array(
                'label' => false,
                'id' => $this->balance_pen_input_id,
                'style' => 'text-align:right;',
                'readonly' => true,
            ));
            echo $this->form->numberFieldRow($this->model, 'balance_usd', array(
                'label' => false,
                'id' => $this->balance_usd_input_id,
                'style' => 'text-align:right;',
                'readonly' => true,
            ));
            echo $this->form->hiddenField($this->model, 'parent_currency', array(
                'id' => $this->parent_currency_input_id,
            ));
            echo "</td>";
            echo "<td></td>";
            echo "</tr>";
        }
        echo "</tfoot>";
        echo "</table>";

        if ($this->showObservation) {
            echo "<div class='row'>";
            echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
            echo $this->form->textFieldRow($this->model->movement, 'observation', array(
                'placeholder' => 'Puede escribir una observación acerca del movimiento',
                'required' => $this->model->movement->isObservationRequired()
            ));
            echo "</div>";
            echo "</div>";
        }

        $this->endWidget();
    }

    private function getAccess() {
        $routes = [];
        foreach ($this->routes as $route) {
            $routes[] = "/{$route}/view";
        }
        return $this->controller->getUrls($routes);
    }

}
