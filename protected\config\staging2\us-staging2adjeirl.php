<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'staging2adjeirl.siansystem.com/admin';
$domain = 'https://staging2adjeirl.siansystem.com';
$report_domain = 'rstaging2.siansystem.com';
$org = 'adjeirl';
$us = 'us_staging2';
$database_server = '69.10.37.246';
$database_name = 'adjeirl_staging2';
$database_username = 'sian_test';
$database_password = '75nppt6vr57lx4';
$mongo_enabled = true;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = '';
$e_billing_certificate_pass = '';//PIN
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '',//usuario
        'password' => ''
    ]
];
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_STAGING;
$environment = YII_ENVIRONMENT_STAGING;
