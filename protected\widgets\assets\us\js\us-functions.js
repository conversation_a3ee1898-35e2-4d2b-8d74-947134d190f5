function getOptionsHtml(object, selected_value, empty, sort) {

    var options_html = (typeof empty === 'undefined' || empty === true) ? '<option value>' + STRINGS_SELECT_OPTION + '</option>' : '';

    if (typeof object !== 'undefined') {

        var sorted = [];

        $.each(object, function (key, value) {
            sorted.push({key: key, value: value});
        });

        //Requiere ordernar
        if (typeof sort === 'undefined' || sort === true)
        {
            sorted.sort(function (a, b) {
                if (a['value'] < b['value'])
                    return -1;
                if (a['value'] > b['value'])
                    return 1;
                return 0;
            });
        }

        for (var i = 0; i < sorted.length; i++) {
            options_html += '<option value="' + sorted[i]['key'] + '"'
                    + ((sorted[i]['key'] == selected_value && !isBlank(selected_value)) ? ' selected="selected"' : '')
                    + '>' + sorted[i]['value'] + '</option>';
        }
    }
    return options_html;
}

function getComplexOptionsHtml(array, value_field, text_field, selected_value, empty, sort, data_fields) {

    var options_html = (typeof empty === 'undefined' || empty === true) ? '<option value>' + STRINGS_SELECT_OPTION + '</option>' : '';

    if (typeof array !== 'undefined') {

        var sorted = [];

        $.each(array, function (index, object) {
            sorted.push(object);
        });

        //Requiere ordernar
        if (typeof sort === 'undefined' || sort === true)
        {
            sorted.sort(function (a, b) {
                if (a[text_field] < b[text_field])
                    return -1;
                if (a[text_field] > b[text_field])
                    return 1;
                return 0;
            });
        }

        for (var i = 0; i < sorted.length; i++) {

            var s_data = '';
            if (isset(data_fields)) {
                $.each(data_fields, function (index, data_field) {
                    var data_value = isset(sorted[i][data_field]) ? '"' + sorted[i][data_field] + '"' : 'null';
                    s_data += ' data-' + data_field + '=' + data_value;
                });
            }

            options_html += '<option value="' + sorted[i][value_field] + '"'
                    + ((sorted[i][value_field] == selected_value && !isBlank(selected_value)) ? ' selected="selected"' : '')
                    + s_data
                    + '>' + sorted[i][text_field] + '</option>';
        }
    }
    return options_html;
}

function unfocusable()
{
    $('input[readonly]').attr('tabIndex', -1);
    $('select[readonly]').attr('tabIndex', -1);
    $('textarea[readonly]').attr('tabIndex', -1);
}

$.fn.serializeObject = function ()
{
    var o = {};
    var a = this.serializeArray();
    $.each(a, function () {
        if (o[this.name] !== undefined) {
            if (!o[this.name].push) {
                o[this.name] = [o[this.name]];
            }
            o[this.name].push(this.value || '');
        } else {
            o[this.name] = this.value || '';
        }
    });
    return o;
};

function processError(response)
{
    var element = $('<div>' + response + '</div>');
    element.find('h1').remove();
    return element.html();

}

function copyToClipboard(element_id) {

    var doc = document, text = doc.getElementById(element_id), range, selection;

    if (doc.body.createTextRange)
    {
        range = doc.body.createTextRange();
        range.moveToElementText(text);
        range.select();
    } else if (window.getSelection)
    {
        selection = window.getSelection();
        range = doc.createRange();
        range.selectNodeContents(text);
        selection.removeAllRanges();
        selection.addRange(range);
    }
    document.execCommand('copy');
    window.getSelection().removeAllRanges();
    $.notify('Copiado al portapapeles', {
        className: 'info',
        showDuration: 30,
        hideDuration: 50,
        autoHideDelay: 1000
    });
}

function normalizeAjaxParams(p_o_params)
{
    var o_params = {};
    $.each(p_o_params, function (key, value) {
        var resolved = null;
        //Si es función resolvemos
        if (typeof value === 'function') {
            resolved = value();
        } else {
            resolved = value;
        }

        if (isset(resolved)) {
            o_params[key] = resolved;
        }
    });

    return o_params;
}

function getIGVAffectionItems(is_commercial_route, as_bill, scope, map, use_inaffected, use_free, is_gift)
{
    var key = (is_commercial_route == 1 ? '1' : '0') + '.' +
            (as_bill == 1 ? '1' : '0') + '.' +
            scope;

    var filtered = {};
    $.each(map[key], function (key, value) {

        var s_key = String(key);
        if (is_gift) {
            if (s_key === IGV_AFFECTION_GIFT) {
                filtered[key] = value;
            }
        } else {
            switch (String(key)) {
                case IGV_AFFECTION_EXONERATED:
                case IGV_AFFECTION_INAFFECTED:
                    if (use_inaffected == 1) {
                        filtered[key] = value;
                    }
                    break;
                case IGV_AFFECTION_FREE:
                case IGV_AFFECTION_GIFT:
                    if (use_free == 1) {
                        filtered[key] = value;
                    }
                    break;
                default:
                    filtered[key] = value;
                    break;
            }
        }
    });
    return filtered;
}

function getIGVAffectionPurchaseItems(is_commercial_route, as_bill, scope, map, use_inaffected, use_free, use_gift)
{
    var key = (is_commercial_route == 1 ? '1' : '0') + '.' +
            (as_bill == 1 ? '1' : '0') + '.' +
            scope;

    var filtered = {};
    $.each(map[key], function (key, value) {

        var s_key = String(key);
        switch (String(key)) {
            case IGV_AFFECTION_EXONERATED:
            case IGV_AFFECTION_INAFFECTED:
                if (use_inaffected == 1) {
                    filtered[key] = value;
                }
                break;
            case IGV_AFFECTION_FREE:
                if (use_free == 1) {
                    filtered[key] = value;
                }
                break;
            case IGV_AFFECTION_GIFT:
                if (use_gift == 1) {
                    filtered[key] = value;
                }
                break;
            default:
                filtered[key] = value;
                break;
        }
    });
    return filtered;
}

function getIGVAffectionOptions(items, selected_value, orders)
{
    if (!isset(selected_value) && isset(orders)) {
        $.each(orders, function (index, igv_affection) {
            if (isset(items[igv_affection])) {
                selected_value = igv_affection;
                return false;
            }
        });
    }

    return getOptionsHtml(items, selected_value, true, true);
}

function showFormMessage(p_a_messages, p_o_button, p_func_callback) {

    var a_messages = [];
    $.each(p_a_messages, function (index, m_message) {
        if (m_message) {
            a_messages.push("<li>" + m_message + "</li>");
        }
    });
    //Vemos si realmente hay mensajes
    if (a_messages.length > 0) {
        var title = '<h3>Confirme lo siguiente antes de continuar:</h3><br>';

        bootbox.confirm({
            message: us_message(title + '<ul>' + a_messages.join('') + '</ul><br><b>¿Está seguro que desea continuar?</b>', 'question'),
            className: 'us-confirm-modal',
            callback: function (p_b_confirmed)
            {
                p_func_callback(p_o_button, p_b_confirmed);
            }
        });
    } else {
        p_func_callback(p_o_button, true);
    }
}

jQuery.fn.extend({
    insertAtCaret: function (myValue) {
        return this.each(function (i) {
            if (document.selection) {
                //For browsers like Internet Explorer
                this.focus();
                var sel = document.selection.createRange();
                sel.text = myValue;
                this.focus();
            } else if (this.selectionStart || this.selectionStart == '0') {
                //For browsers like Firefox and Webkit based
                var startPos = this.selectionStart;
                var endPos = this.selectionEnd;
                var scrollTop = this.scrollTop;
                this.value = this.value.substring(0, startPos) + myValue + this.value.substring(endPos, this.value.length);
                this.focus();
                this.selectionStart = startPos + myValue.length;
                this.selectionEnd = startPos + myValue.length;
                this.scrollTop = scrollTop;
            } else {
                this.value += myValue;
                this.focus();
            }
        });
    }
});