<?php

class USButtons extends CWidget {

    public $buttons = null;
    //Private
    private $controller;

    /**
     * Initializes the widget.
     */
    public function init() {
        $this->controller = Yii::app()->controller;
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $buttons = [];

        foreach ($this->buttons as $button) {
            array_push($buttons, $this->generateButton($button));
        }

        echo $this->widget('booster.widgets.TbButtonGroup', array('buttons' => $buttons), true);
    }

    private function generateButton(array $array) {

        $array['id'] = isset($array['id']) ? $array['id'] : $this->controller->getServerId();
        $array['params'] = isset($array['params']) ? $array['params'] : [];
        $array['data'] = isset($array['data']) ? $array['data'] : [];
        $array['title'] = isset($array['title']) ? $array['title'] : null;
        $array['context'] = isset($array['context']) ? $array['context'] : null;
        $array['size'] = isset($array['size']) ? $array['size'] : 'extra_small';
        $array['buttonType'] = isset($array['buttonType']) ? $array['buttonType'] : 'button';
        $array['block'] = isset($array['block']) ? $array['block'] : false;
        $array['target'] = isset($array['target']) ? $array['target'] : null;
        $array['onclick'] = isset($array['onclick']) ? $array['onclick'] : null;
        $array['htmlOptions'] = isset($array['htmlOptions']) ? $array['htmlOptions'] : [];
        $array['visible'] = isset($array['visible']) ? $array['visible'] : true;

        //DISABLED
        if (!isset($array['disabled'])) {
            $array['disabled'] = !(isset($array['route']) ? $this->controller->checkRoute($array['route']) : true);
        }

        //URL
        if ($array['disabled']) {
            $array['url'] = null;
        } else {
            if (isset($array['route'])) {
                $array['url'] = $this->getUrl($array);
            } else {
                $array['url'] = isset($array['url']) ? $array['url'] : null;
            }
        }

        $items = null;

        if (isset($array['items'])) {
            $items = [];
            foreach ($array['items'] as $item) {
                $items[] = $this->generateDropdown($item);
            }
        }


        if (isset($array['class'])) {

            //Add necessary information if not exist
            $array['data']['modal_id'] = isset($array['data']['modal_id']) ? $array['data']['modal_id'] : $this->controller->getServerId();
            //Mezclamos con los HTML Options
            $array['htmlOptions']['id'] = $array['id'];
            $array['htmlOptions']['class'] = isset($array['htmlOptions']['class']) ? ($array['htmlOptions']['class'] . ' ' . $array['class']) : $array['class'];
            $array['htmlOptions']['title'] = $array['title'];
            $array['htmlOptions']['url'] = $array['url'];
            $array['htmlOptions']['disabled'] = $array['disabled'];
            $array['htmlOptions']['onclick'] = $array['onclick'];
            //Agregamos la data
            foreach ($array['data'] as $key => $value) {
                $array['htmlOptions']["data-{$key}"] = $value;
            }

            return array(
                'context' => $array['context'],
                'size' => $array['size'],
                'block' => $array['block'],
                'encodeLabel' => false,
                'label' => (isset($array['icon']) ? "<span class='{$array['icon']}'></span> " : "") . (isset($array['label']) ? $array['label'] : ""),
                'htmlOptions' => $array['htmlOptions'],
                'visible' => $array['visible'],
                'items' => $items,
            );
        } else {
            $array['htmlOptions']['id'] = $array['id'];
            $array['htmlOptions']['title'] = $array['title'];
            $array['htmlOptions']['disabled'] = $array['disabled'];
            $array['htmlOptions']['target'] = $array['target'];
            $array['htmlOptions']['onclick'] = $array['onclick'];

            return array(
                'context' => $array['context'],
                'size' => $array['size'],
                'block' => $array['block'],
                'encodeLabel' => false,
                'label' => (isset($array['icon']) ? "<span class='{$array['icon']}'></span> " : "") . (isset($array['label']) ? $array['label'] : ""),
                'buttonType' => isset($array['url']) ? 'link' : $array['buttonType'],
                'url' => $array['url'],
                'htmlOptions' => $array['htmlOptions'],
                'visible' => $array['visible'],
                'items' => $items,
            );
        }
    }

    private function generateDropdown(array $array) {
        return array(
            'label' => $array['label'],
            'url' => $array['url'],
            'linkOptions' => isset($array['linkOptions']) ? $array['linkOptions'] : null,
            'encodeLabel' => false,
        );
    }

    /**
     * Retorna la URL
     * @return string URL
     */
    private function getUrl($data) {
//        if (isset($data['class'])) {
//            return $this->controller->createUrl($data['route']);
//        } else {
        return $this->controller->createUrl($data['route'], $data['params']);
//        }
    }

}
