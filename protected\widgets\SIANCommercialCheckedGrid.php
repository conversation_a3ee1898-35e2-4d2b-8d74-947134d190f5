<?php

class SIANCommercialCheckedGrid extends CWidget {

    public $id;
    public $form;
    public $model;
    public $items;
    public $checked_items;
    public $detraction_items = [];
    //
    public $readonly = false;
    public $ifixed = CommercialMovement::IFIXED;
    public $tfixed = CommercialMovement::TFIXED;
    public $istep = CommercialMovement::ISTEP;
    public $tstep = CommercialMovement::TSTEP;
    public $tolerance = CommercialMovement::TOLERANCE;
    public $onChangeCCDependence = ''; //Para hacer un puente
    //PROTECTED
    protected $controller;
    protected $dataProvider;
    protected $presentationMode = SpGetProductPresentations::MODE_COMBOBOX_WITH_PRICES;
    protected $presentationItems = [];
    protected $preview_access;
    protected $include_igv_checkbox_id;
    protected $as_bill_checkbox_id;
    protected $force_igv_checkbox_id;
    protected $allow_duplicate_checkbox_id;
    protected $round_mode_select_id;
    protected $observation_input_id;
    protected $detraction_code_id;
    protected $retention_percent_field_id;
    protected $detraction_percent_field_id;
    protected $ret_field_id;
    protected $no_ret_field_id;
    protected $retention_checkbox_id;
    protected $detraction_checkbox_id;
    protected $warehouse_input_id;
    protected $summary_id;
    protected $grid_number;
    protected $is_commercial_route;
    protected $as_bill;
    protected $scope;
    protected $igv_affections;
    protected $igv_list;
    protected $div_retention_id;
    protected $div_detraction_id;
    protected $div_no_ret_pen_id;

    public function init() {

        //PRIVATE
        $this->controller = Yii::app()->controller;
        //ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->include_igv_checkbox_id = isset($this->include_igv_checkbox_id) ? $this->include_igv_checkbox_id : $this->controller->getServerId();
        $this->as_bill_checkbox_id = $this->controller->getServerId();
        $this->force_igv_checkbox_id = $this->controller->getServerId();
        $this->allow_duplicate_checkbox_id = $this->controller->getServerId();
        $this->round_mode_select_id = $this->controller->getServerId();
        $this->observation_input_id = $this->controller->getServerId();
        $this->retention_checkbox_id = Yii::app()->controller->getServerId();
        $this->detraction_checkbox_id = Yii::app()->controller->getServerId();
        $this->retention_percent_field_id = Yii::app()->controller->getServerId();
        $this->ret_field_id = Yii::app()->controller->getServerId();
        $this->no_ret_field_id = Yii::app()->controller->getServerId();
        $this->warehouse_input_id = $this->controller->getServerId();
        $this->summary_id = $this->controller->getServerId();
        $this->div_retention_id = Yii::app()->controller->getServerId();
        $this->div_detraction_id = Yii::app()->controller->getServerId();
        $this->div_no_ret_pen_id = Yii::app()->controller->getServerId();
        $this->detraction_code_id = Yii::app()->controller->getServerId();
        $this->retention_percent_field_id = Yii::app()->controller->getServerId();
        $this->detraction_percent_field_id = Yii::app()->controller->getServerId();
        //Valores
        $this->is_commercial_route = $this->model->movement->scenario->isCommercialRoute();
        $this->as_bill = $this->model->as_bill;
        $this->scope = $this->model->movement->scope;
        $this->igv_affections = CommercialMovementProduct::getIGVAffectionItems();
        $this->igv_list= GlobalVar::getIgvList();
    }

    /**
     * Runs the widget.
     */
    public function run() {
        
    }

    protected function renderProductCell($row) {
        if ($row->product_type === Product::TYPE_GROUP) {
            return 'Grupo';
        } else {
            return $this->form->textFieldRow($row, 'product_id', array(
                        'label' => false,
                        'name' => "Item[{$row->parent_item_id}][product_id]",
                        'class' => "sian-commercial-checked-grid-{$this->grid_number}-item-product-id",
                        'readonly' => true,
                        'disabled' => !$row->checked,
                        'style' => 'text-align:center'
            ));
        }
    }

    protected function renderPresentationCell($p_o_row, $p_s_name, $p_s_class, $p_b_readonly, $p_b_disabled) {
        if ($p_o_row->product_type === Product::TYPE_GROUP) {
            return CHtml::hiddenField($p_s_name, $p_o_row->equivalence, array(
                        'class' => $p_s_class,
                        'readonly' => $p_b_readonly,
                        'disabled' => $p_b_disabled,
                    )) . "<span>{$this->controller->getDefaultMerchandiseMeasure()->abbreviation}</span>";
        } else {
            return $this->renderPresentationSelect($p_o_row, $this->presentationItems, $p_s_name, $p_s_class, $p_b_readonly, $p_b_disabled);
        }
    }

    protected function renderPresentationSelect($p_o_row, $p_a_items, $p_s_name, $p_s_class, $p_b_readonly = false, $p_b_disabled = false) {

        $s_html = '';
        $s_html .= "<div class='form-group " . ($p_o_row->hasErrors('equivalence') ? 'has-error' : '') . "'>";

        $s_html .= "<select class='form-control {$p_s_class}' data-equivalence={$p_o_row->equivalence} name='{$p_s_name}' " . ($p_b_readonly ? 'readonly' : '') . " " . ($p_b_disabled ? 'disabled' : '') . ">";
        if ($p_o_row['product_type'] == Product::TYPE_NEW) {
            $s_html .= "<option value='1' data-mprice=0 data-imprice=0 data-aprice=0 data-iaprice=0 selected>UNI.</option>";
        } else {
            foreach ($p_a_items[$p_o_row->product_id] as $item) {
                $selected = $item['equivalence'] == $p_o_row->equivalence ? 'selected' : '';
                $s_html .= "<option value='{$item['equivalence']}' data-mprice={$item['mprice']} data-imprice={$item['imprice']} data-aprice={$item['aprice']} data-iaprice={$item['iaprice']} {$selected}>{$item['measure_name']}</option>";
            }
        }
        $s_html .= '</select>';
        if ($p_o_row->hasErrors('equivalence')) {
            $s_html .= "<span class='help-block error'>{$p_o_row->getFirstError()['error']}</span>";
        }
        $s_html .= "</div>";
        return $s_html;
    }

    protected function renderIGVAffectionSelect($p_o_row, $p_a_items, $p_s_name, $p_s_class, $p_b_readonly, $p_b_disabled, $p_s_onchange = null) {
        return $this->form->dropDownListRow($p_o_row, 'igv_affection', $p_a_items, array(
                    'label' => false,
                    'name' => $p_s_name,
                    'class' => $p_s_class,
                    'readonly' => $p_b_readonly == 1,
                    'disabled' => $p_b_disabled == 1,
                    'empty' => Strings::SELECT_OPTION,
                    'onchange' => $p_s_onchange
        ));
    }
    
    protected function renderIGVListSelect($p_o_row, $p_a_items, $p_s_name, $p_s_class, $p_b_readonly, $p_b_disabled, $p_s_onchange = null) {
        return $this->form->dropDownListRow($p_o_row, 'igv_percentage', $p_a_items, array(
                    'label' => false,
                    'name' => $p_s_name,
                    'class' => $p_s_class,
                    'readonly' => $p_b_readonly == 1,
                    'disabled' => $p_b_disabled == 1,
                    'onchange' => $p_s_onchange
        ));
    }

    protected function getGlobalCheckBoxState() {
        return count($this->model->tempItems) > 0;
    }

}
