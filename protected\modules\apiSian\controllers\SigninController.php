<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SigninController
 *
 * <AUTHOR>
 */
class SigninController extends ApiController {

    // use ApiLockerControllerTrait;

    public function accessRules() {
        return array(
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('restLogin'),
                'users' => array('*')
            ),
            array('deny', // El resto de acciones se deniega
                'users' => array('*'),
                'deniedCallback' => array('ApiController', 'deniedCallback')
            ),
        );
    }

    public function actionRestLogin() {

        try {
            $a_required = [
                'username' => true,
                'password' => true,
                'lifetime' => false
            ];
            $a_default = [
                'lifetime' => Yii::app()->params['jwt_lifetime']
            ];
            //Obtenemos
            $a_filtered = $this->getAndFilterAttributes($a_required, $a_default);
            //
            if ($a_filtered['lifetime'] <= 0) {
                USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                    'attributes' => [
                        'lifetime' => 'Debe especificar un tiempo en segundos mayor que cero'
                    ]
                ]);
                Yii::app()->end();
            }
            //Validar
            $o_model = ApiUser::model()->findByAttributes([
                'username' => $a_filtered['username'],
                'type' => ApiUser::TYPE_SIAN
            ]);

            if (!isset($o_model) || $o_model->status == 0) {
                USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La cuenta de API no existe o está inhabilitada!');
                Yii::app()->end();
            } else {
                //Verificamos contraseña
                if ($o_model->password !== md5($a_filtered['password'])) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'Contraseña API incorrecta!');
                    Yii::app()->end();
                }
                $s_expiration = SIANTime::addSeconds(SIANTime::now(), $a_filtered['lifetime']);
                //TOken
                $a_token_data = [
                    'token_type' => self::TOKEN_TYPE_PROXY,
                    'api_user_id' => (int) $o_model->api_user_id,
                    'pe' => $o_model->type,
                    'expiration' => $s_expiration,
                    'lang' => 'es',
                ];
                //Codificamos
                $s_jwt = SIANJWT::encodeToken($a_token_data);
                //
                USREST::sendResponse(USREST::CODE_SUCCESS, '', [
                    'token' => $s_jwt,
                    'expires' => $s_expiration
                ]);
                Yii::app()->end();
            }
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }

    public function actionRestRegisterClubMia() {

        try {
            $a_required = [               
                "firstName"=> true,
                "lastName"=> true,
                "email"=> true,
                "password"=> true,
                "phone"=> true,
                "address"=> true,
                "birthDate"=> true,
                "dni"=> true,
                "ruc"=> false,
                "addresses" => false,
                "phones" => false,
                "companyName"=> false
            ];

            $a_default = [];
            //Obtenemos
            $a_filtered = $this->getAndFilterAttributes($a_required, $a_default);

            $o_person = Person::model()->findByAttributes([
                'identification_number' => $a_filtered['dni'],
            ]);
            
            $o_model = ApiUser::model()->findByAttributes([
                'username' => $a_filtered['username'],
                'type' => ApiUser::TYPE_SIAN
            ]);

            $o_cl_mia = Agreement::model()->findByAttributes(['agreement_code' => Agreement::AGREEMENT_TYPE_CLUB_MIA]);

            if (!isset($o_model) || $o_model->status == 0) {
                $o_person = new Person();
                $o_person->verification = "Verificado";
                $o_person->advanced = 1;
                $o_person->identification_type = 1;
                $o_person->identification_number = $a_filtered['dni'];
                $o_person->birthday = $a_default['birthDate'];
                $o_person->not_domiciled = 0;
                $o_person->convention = Yii::app()->controller->getDefaultConvention()->multi_id;;
                $o_person->country = "398";
                $o_person->status = 1;
                $o_person->paternal = $a_filtered["paternal"];
                $o_person->maternal = $a_filtered["maternal"];
                $o_person->firstname = $a_filtered["firstName"];
                $o_person->description = "";
                $o_person->person_name = $a_filtered["fullName"];
                $o_person->is_gov = 0;
                $o_person->is_associated = 0;
                $o_person->is_dealer = 0;
                $o_person->retention = 0;
                $o_person->perception = 0;
                $o_person->good_contributor = 0;
                $o_person->is_transport_company = 0;
                $o_person->catchment = "Otro";
                $o_person->category_id = "";
                $o_person->user_catchment_id = "";
                $o_person->agreement_id = $o_cl_mia->agreement_id;




            
                Yii::app()->end();
            
            } else {
                //Verificamos contraseña
                if ($o_model->password !== md5($a_filtered['password'])) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'Contraseña API incorrecta!');
                    Yii::app()->end();
                }
                $s_expiration = SIANTime::addSeconds(SIANTime::now(), $a_filtered['lifetime']);
                //TOken
                $a_token_data = [
                    'token_type' => self::TOKEN_TYPE_PROXY,
                    'api_user_id' => (int) $o_model->api_user_id,
                    'pe' => $o_model->type,
                    'expiration' => $s_expiration,
                    'lang' => 'es',
                ];
                //Codificamos
                $s_jwt = SIANJWT::encodeToken($a_token_data);
                //
                USREST::sendResponse(USREST::CODE_SUCCESS, '', [
                    'token' => $s_jwt,
                    'expires' => $s_expiration
                ]);
                Yii::app()->end();
            }
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }


}
