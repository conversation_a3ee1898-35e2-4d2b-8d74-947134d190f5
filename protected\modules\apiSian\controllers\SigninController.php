<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SigninController
 *
 * <AUTHOR>
 */
class SigninController extends ApiController {

    // use ApiLockerControllerTrait;

    public function accessRules() {
        return array(
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('restLogin', 'restRegisterClubMia'),
                'users' => array('*')
            ),
            array('deny', // El resto de acciones se deniega
                'users' => array('*'),
                'deniedCallback' => array('ApiController', 'deniedCallback')
            ),
        );
    }

    public function actionRestLogin() {

        try {
            $a_required = [
                'username' => true,
                'password' => true,
                'lifetime' => false
            ];
            $a_default = [
                'lifetime' => Yii::app()->params['jwt_lifetime']
            ];
            //Obtenemos
            $a_filtered = $this->getAndFilterAttributes($a_required, $a_default);
            //
            if ($a_filtered['lifetime'] <= 0) {
                USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                    'attributes' => [
                        'lifetime' => 'Debe especificar un tiempo en segundos mayor que cero'
                    ]
                ]);
                Yii::app()->end();
            }
            //Validar
            $o_model = ApiUser::model()->findByAttributes([
                'username' => $a_filtered['username'],
                'type' => ApiUser::TYPE_SIAN
            ]);

            if (!isset($o_model) || $o_model->status == 0) {
                USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La cuenta de API no existe o está inhabilitada!');
                Yii::app()->end();
            } else {
                //Verificamos contraseña
                if ($o_model->password !== md5($a_filtered['password'])) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'Contraseña API incorrecta!');
                    Yii::app()->end();
                }
                $s_expiration = SIANTime::addSeconds(SIANTime::now(), $a_filtered['lifetime']);
                //TOken
                $a_token_data = [
                    'token_type' => self::TOKEN_TYPE_PROXY,
                    'api_user_id' => (int) $o_model->api_user_id,
                    'pe' => $o_model->type,
                    'expiration' => $s_expiration,
                    'lang' => 'es',
                ];
                //Codificamos
                $s_jwt = SIANJWT::encodeToken($a_token_data);
                //
                USREST::sendResponse(USREST::CODE_SUCCESS, '', [
                    'token' => $s_jwt,
                    'expires' => $s_expiration
                ]);
                Yii::app()->end();
            }
        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }

    public function actionRestRegisterClubMia() {

        try {
            $a_required = [
                "firstName"=> true,
                "lastName"=> true,
                "email"=> true,
                "password"=> true,
                "phone"=> true,
                "address"=> true,
                "birthDate"=> true,
                "dni"=> true,
                "ruc"=> false,
                "addresses" => false,
                "phones" => false,
                "companyName"=> false,
                "username"=> true,
                "lifetime"=> false
            ];

            $a_default = [
                'lifetime' => Yii::app()->params['jwt_lifetime']
            ];
            //Obtenemos
            $a_filtered = $this->getAndFilterAttributes($a_required, $a_default);

            //Validar lifetime
            if ($a_filtered['lifetime'] <= 0) {
                USREST::sendResponse(USREST::CODE_BAD_REQUEST, '', [
                    'attributes' => [
                        'lifetime' => 'Debe especificar un tiempo en segundos mayor que cero'
                    ]
                ]);
                Yii::app()->end();
            }

            //Validar credenciales de API
            $o_api_user = ApiUser::model()->findByAttributes([
                'username' => $a_filtered['username'],
                'type' => ApiUser::TYPE_SIAN
            ]);

            if (!isset($o_api_user) || $o_api_user->status == 0) {
                USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'La cuenta de API no existe o está inhabilitada!');
                Yii::app()->end();
            } else {
                //Verificamos contraseña de API
                if ($o_api_user->password !== md5($a_filtered['password'])) {
                    USREST::sendResponse(USREST::CODE_APP_UNAUTHORIZED, 'Contraseña API incorrecta!');
                    Yii::app()->end();
                }
            }

            // Verificar si ya existe una persona con este DNI
            $o_existing_person = Person::model()->findByAttributes([
                'identification_number' => $a_filtered['dni'],
            ]);

            // Obtener el convenio Club Mia
            $o_cl_mia = Agreement::model()->findByAttributes(['agreement_code' => Agreement::AGREEMENT_TYPE_CLUB_MIA]);

            if (!isset($o_cl_mia)) {
                USREST::sendResponse(USREST::CODE_INTERNAL_SERVER_ERROR, 'Convenio Club Mia no encontrado');
                Yii::app()->end();
            }

            if (isset($o_existing_person)) {
                // Si la persona ya existe, verificar si ya tiene el convenio Club Mia
                if ($o_existing_person->agreement_id == $o_cl_mia->agreement_id) {
                    USREST::sendResponse(USREST::CODE_BAD_REQUEST, 'Esta persona ya está registrada en Club Mia');
                    Yii::app()->end();
                } else {
                    // Actualizar la persona existente con el convenio Club Mia
                    $o_existing_person->agreement_id = $o_cl_mia->agreement_id;
                    $o_existing_person->scenario = Person::SCENARIO_NORMAL;

                    if ($o_existing_person->validate()) {
                        $o_existing_person->update();

                        // Generar token de login después de la actualización exitosa
                        $s_expiration = SIANTime::addSeconds(SIANTime::now(), $a_filtered['lifetime']);
                        $a_token_data = [
                            'token_type' => self::TOKEN_TYPE_PROXY,
                            'api_user_id' => (int) $o_api_user->api_user_id,
                            'pe' => $o_api_user->type,
                            'expiration' => $s_expiration,
                            'lang' => 'es',
                        ];
                        //Codificamos
                        $s_jwt = SIANJWT::encodeToken($a_token_data);

                        USREST::sendResponse(USREST::CODE_SUCCESS, 'Persona actualizada exitosamente en Club Mia', [
                            'person_id' => $o_existing_person->person_id,
                            'person_name' => $o_existing_person->person_name,
                            'agreement_id' => $o_existing_person->agreement_id,
                            'token' => $s_jwt,
                            'expires' => $s_expiration
                        ]);
                    } else {
                        USREST::sendResponse(USREST::CODE_BAD_REQUEST, 'Error al actualizar la persona', [
                            'errors' => $o_existing_person->getErrors()
                        ]);
                    }
                    Yii::app()->end();
                }
            }

            // Crear nueva persona
            $o_person = new Person(Person::SCENARIO_NORMAL);
            $o_person->verification = Person::VERIFICATION_VERIFIED;
            $o_person->advanced = 1;
            $o_person->identification_type = 1; // DNI
            $o_person->identification_number = $a_filtered['dni'];
            $o_person->birthday = $a_filtered['birthDate'];
            $o_person->not_domiciled = 0;
            $o_person->convention = Yii::app()->controller->getDefaultConvention()->multi_id;
            $o_person->country = "398"; // Perú
            $o_person->status = 1;

            // Nombres para persona natural (DNI)
            $a_names = explode(' ', trim($a_filtered['lastName']));
            $o_person->paternal = isset($a_names[0]) ? $a_names[0] : '';
            $o_person->maternal = isset($a_names[1]) ? $a_names[1] : '';
            $o_person->firstname = $a_filtered["firstName"];

            $o_person->description = "";
            $o_person->is_gov = 0;
            $o_person->is_associated = 0;
            $o_person->is_dealer = 0;
            $o_person->retention = 0;
            $o_person->perception = 0;
            $o_person->good_contributor = 0;
            $o_person->is_transport_company = 0;
            $o_person->catchment = Person::CATCH_WEB;
            $o_person->category_id = null;
            $o_person->user_catchment_id = null;
            $o_person->agreement_id = $o_cl_mia->agreement_id;

            // Agregar teléfono
            if (!empty($a_filtered['phone'])) {
                $o_person->addPhone([
                    'phone_number' => $a_filtered['phone'],
                    'phone_type' => 'Otro'
                ]);
            }

            // Agregar email
            if (!empty($a_filtered['email'])) {
                $o_person->addEmail([
                    'email_address' => $a_filtered['email'],
                    'email_type' => 'Otro'
                ]);
            }

            // Agregar dirección
            if (!empty($a_filtered['address'])) {
                $o_person->addAddress([
                    'address' => $a_filtered['address'],
                    'reference' => null
                ]);
            }

            // Validar y guardar
            if ($o_person->validate()) {
                $o_person->savingExternal()->insert();

                // Generar token de login después del registro exitoso
                $s_expiration = SIANTime::addSeconds(SIANTime::now(), $a_filtered['lifetime']);
                $a_token_data = [
                    'token_type' => self::TOKEN_TYPE_PROXY,
                    'api_user_id' => (int) $o_api_user->api_user_id,
                    'pe' => $o_api_user->type,
                    'expiration' => $s_expiration,
                    'lang' => 'es',
                ];
                //Codificamos
                $s_jwt = SIANJWT::encodeToken($a_token_data);

                USREST::sendResponse(USREST::CODE_SUCCESS, 'Persona registrada exitosamente en Club Mia', [
                    'person_id' => $o_person->person_id,
                    'person_name' => $o_person->person_name,
                    'agreement_id' => $o_person->agreement_id,
                    'token' => $s_jwt,
                    'expires' => $s_expiration
                ]);
            } else {
                USREST::sendResponse(USREST::CODE_BAD_REQUEST, 'Error en la validación de datos', [
                    'errors' => $o_person->getErrors()
                ]);
            }

            Yii::app()->end();

        } catch (CDbException $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        } catch (Exception $ex) {
            USLog::save($ex);
            USREST::sendException($ex);
        }
    }


}
