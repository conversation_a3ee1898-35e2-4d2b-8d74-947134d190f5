<?php

class SIANMovementLog extends CWidget {

    public $id;
    public $model;
    public $readonly = false;
    public $person_items = [];
    //PRIVATE
    private $clientScript;
    private $controller;
    private $person_id = null;
    private $first_id = null;

    public function init() {

        $this->clientScript = Yii::app()->clientScript;
        $this->controller = Yii::app()->controller;

        if (!Yii::app()->user->isGuest) {
            $this->person_id = Yii::app()->user->getState('person_id');
        }


        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //Recorremos los ítems
        $a_items = [];
        foreach ($this->model->tempMovementLogs as $o_item) {

            $a_errors = $o_item->getErrors();

            $a_items[] = [
                'movement_log_type_id' => $o_item->movement_log_type_id,
                'description' => isset($o_item->description) ? $o_item->description : '',
                'person_id' => $o_item->person_id,
                'register_date' => $o_item->register_date,
                'errors' => $a_errors
            ];

            //Si tiene errores
            if (!isset($this->first_id) && count($a_errors) > 0) {
                $this->first_id = $o_item->movement_log_type_id;
            }
        }

        //Registramos assets
        SIANAssets::registerScriptFile("other/jquery.datetimepicker/js/jquery.datetimepicker.js");
        SIANAssets::registerCssFile("other/jquery.datetimepicker/css/jquery.datetimepicker.css");
        SIANAssets::registerScriptFile('js/sian-movement-log.js');

        //SCRIPTS
        $this->clientScript->registerScript($this->id, "

        var array = " . CJSON::encode($a_items) . ";

        //COUNT y LIMIT
        $('#{$this->id}').data('personItems', " . CJSON::encode($this->person_items) . ");

        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANMovementLogAddItem('{$this->id}', array[i]['movement_log_type_id'], array[i]['description'], array[i]['person_id'], array[i]['register_date'], array[i]['errors']);
            }
        }
        
        SIANMovementLogUpdate('{$this->id}');
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "Registro de eventos",
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => $this->model->hasErrors('tempMovementLogs') ? 'us-error' : ''
            )
        ));

        if (count($this->model->log_type_items) > 0) {

            $i_active_id = $this->_getActiveId();

            //Recorremos los tipos de log
            $a_tabs = [];

            foreach ($this->model->log_type_items as $i => $o_item) {

                $a_tabs[] = [
                    'id' => $this->id . '_pane_' . $i,
                    'label' => $o_item->title,
                    'content' => $this->_renderPanel($o_item),
                    'active' => $i_active_id == $o_item->movement_log_type_id,
                    'class' => $o_item->alias,
                    'paneOptions' => [
                        'class' => 'sian-movement-log-type-pane-' . $o_item->alias
                    ],
                    'itemOptions' => [
                        'id' => $this->id . '_item_' . $i,
                    ],
                    'linkOptions' => [
                        'id' => $this->id . '_link_' . $i,
                    ]
                ];
            }

            $this->widget('booster.widgets.TbTabs', array(
                'type' => 'tabs', // 'tabs' or 'pills'
                'placement' => 'left',
                'tabs' => $a_tabs
            ));
        } else {
            echo Strings::NO_LOG_TYPE;
        }
        
        $this->endWidget();
    }

    private function _renderPanel($p_o_item) {
        //Renderizar
        $html = '';

        $html .= "<table class='table table-condensed table-hover sian-movement-log-type-table sian-movement-log-type-{$p_o_item->movement_log_type_id}' data-count=0>";
        $html .= "<thead>";
        $html .= "<tr>";
        $html .= "<th width='5%'>#</th>";
        $html .= "<th width='50%'>{$this->model->getAttributeLabel('movementLogs.description')}</th>";
        $html .= "<th width='25%'>{$this->model->getAttributeLabel('movementLogs.person_id')}</th>";
        $html .= "<th width='15%'>{$this->model->getAttributeLabel('movementLogs.register_date')}</th>";
        $html .= "<th width='5%'></th>";
        $html .= "</tr>";
        $html .= "</thead>";
        $html .= "<tbody></tbody>";
        $html .= "<tfoot>";
        $html .= "<tr>";
        $html .= "<td colspan='99'>";
        if (!$this->readonly) {
            $html .= CHtml::link("<span class='fa fa-plus fa-lg black'></span> Agregar nuevo", Strings::LINK_TEXT, array(
                        'onclick' => "SIANMovementLogAddItem('{$this->id}', '{$p_o_item->movement_log_type_id}', '', '{$this->person_id}', now(), [])",
                        'title' => 'Agregar'
            ));
        }
        $html .= "</td>";
        $html .= "</tr>";
        $html .= "</tfoot>";
        $html .= "</table>";

        return $html;
    }

    private function _getActiveId() {
        return isset($this->first_id) ? $this->first_id : (count($this->model->log_type_items) > 0 ? $this->model->log_type_items[0]->movement_log_type_id : null);
    }

}
