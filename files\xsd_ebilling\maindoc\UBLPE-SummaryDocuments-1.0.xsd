<?xml version="1.0" encoding="UTF-8"?>
<!--
  Autor:			       SUNAT
  Document Type:     Resumen Diario
  Generated On:      01 Mayo 2012
-->
<!-- ===== xsd:schema Element With Namespaces Declarations ===== -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:sunat:names:specification:ubl:peru:schema:xsd:SummaryDocuments-1" xmlns:sac="urn:sunat:names:specification:ubl:peru:schema:xsd:SunatAggregateComponents-1" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:udt="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" xmlns:ccts="urn:un:unece:uncefact:documentation:2" xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" xmlns:qdt="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2" targetNamespace="urn:sunat:names:specification:ubl:peru:schema:xsd:SummaryDocuments-1" elementFormDefault="qualified" attributeFormDefault="unqualified" version="2.0">
	<!-- ===== Imports ===== -->
	<xsd:import namespace="urn:sunat:names:specification:ubl:peru:schema:xsd:SunatAggregateComponents-1" schemaLocation="../common/UBLPE-SunatAggregateComponents-1.0.xsd"/>
	<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" schemaLocation="../common/UBL-CommonAggregateComponents-2.0.xsd"/>
	<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" schemaLocation="../common/UBL-CommonBasicComponents-2.0.xsd"/>
	<xsd:import namespace="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" schemaLocation="../common/UnqualifiedDataTypeSchemaModule-2.0.xsd"/>
	<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" schemaLocation="../common/UBL-CommonExtensionComponents-2.0.xsd"/>
	<xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2" schemaLocation="../common/UBL-QualifiedDatatypes-2.0.xsd"/>
	<!-- ===== Root Element ===== -->
	<xsd:element name="SummaryDocuments" type="SummaryDocumentsType">
		<xsd:annotation>
			<xsd:documentation>This element MUST be conveyed as the root element in any instance document based on this Schema expression</xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:complexType name="SummaryDocumentsType">
		<xsd:annotation>
			<xsd:documentation>
				<ccts:Component>
					<ccts:ComponentType>ABIE</ccts:ComponentType>
					<ccts:DictionaryEntryName>Consolidated Invoice. Details</ccts:DictionaryEntryName>
					<ccts:Definition>The document used to request payment.</ccts:Definition>
					<ccts:ObjectClass>ConsolidatedInvoice</ccts:ObjectClass>
				</ccts:Component>
			</xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element ref="ext:UBLExtensions" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>A container for all extensions present in the document.</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:UBLVersionID" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Invoice. UBL Version Identifier. Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>The earliest version of the UBL 2 schema for this document type that defines all of the elements that might be encountered in the current instance.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Invoice</ccts:ObjectClass>
							<ccts:PropertyTerm>UBL Version Identifier</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
							<ccts:Examples>2.0.5</ccts:Examples>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:CustomizationID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Invoice. Customization Identifier. Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>Identifies a user-defined customization of UBL for a specific use.</ccts:Definition>
							<ccts:Cardinality>0..1</ccts:Cardinality>
							<ccts:ObjectClass>Invoice</ccts:ObjectClass>
							<ccts:PropertyTerm>Customization Identifier</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
							<ccts:Examples>NES</ccts:Examples>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:ID">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Identifier</ccts:DictionaryEntryName>
							<ccts:Definition>An identifier for the Invoice assigned by the Creditor.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice</ccts:ObjectClass>
							<ccts:PropertyTerm>Identifier</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
							<ccts:DataType>Identifier. Type</ccts:DataType>
							<ccts:AlternativeBusinessTerms>Invoice Number</ccts:AlternativeBusinessTerms>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:ReferenceDate"/>
			<xsd:element ref="cbc:IssueDate">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Issue Date. Date</ccts:DictionaryEntryName>
							<ccts:Definition>The date assigned by the Creditor on which the Invoice was issued.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice</ccts:ObjectClass>
							<ccts:PropertyTerm>Issue Date</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Date</ccts:RepresentationTerm>
							<ccts:DataType>Date. Type</ccts:DataType>
							<ccts:AlternativeBusinessTerms>Invoice Date</ccts:AlternativeBusinessTerms>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cbc:Note" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>BBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Invoice. Note. Text</ccts:DictionaryEntryName>
							<ccts:Definition>Free-form text applying to the Invoice. This element may contain notes or any other similar information that is not contained explicitly in another structure.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice</ccts:ObjectClass>
							<ccts:PropertyTerm>Note</ccts:PropertyTerm>
							<ccts:RepresentationTerm>Text</ccts:RepresentationTerm>
							<ccts:DataType>Text. Type</ccts:DataType>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cac:Signature" minOccurs="0" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Signature</ccts:DictionaryEntryName>
							<ccts:Definition>An association to Signature.</ccts:Definition>
							<ccts:Cardinality>0..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice</ccts:ObjectClass>
							<ccts:PropertyTerm>Signature</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Signature</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="cac:AccountingSupplierParty">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Accounting_ Supplier Party. Supplier Party</ccts:DictionaryEntryName>
							<ccts:Definition>An association to the Accounting Supplier Party.</ccts:Definition>
							<ccts:Cardinality>1</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice</ccts:ObjectClass>
							<ccts:PropertyTermQualifier>Accounting</ccts:PropertyTermQualifier>
							<ccts:PropertyTerm>Supplier Party</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Supplier Party</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element ref="sac:SummaryDocumentsLine" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>
						<ccts:Component>
							<ccts:ComponentType>ASBIE</ccts:ComponentType>
							<ccts:DictionaryEntryName>Consolidated Invoice Invoice Line</ccts:DictionaryEntryName>
							<ccts:Definition>An association to one or more Invoice Lines.</ccts:Definition>
							<ccts:Cardinality>1..n</ccts:Cardinality>
							<ccts:ObjectClass>Consolidated Invoice</ccts:ObjectClass>
							<ccts:PropertyTerm>Consolidated Invoice Line</ccts:PropertyTerm>
							<ccts:AssociatedObjectClass>Consolidated Invoice Line</ccts:AssociatedObjectClass>
						</ccts:Component>
					</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<!-- ===== Element Declarations ===== -->
	<!-- ===== Type Definitions ===== -->
	<!-- ===== Basic Business Information Entity Type Definitions ===== -->
</xsd:schema>
