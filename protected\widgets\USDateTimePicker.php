<?php

/**
 * @preserve jQuery DateTimePicker plugin v2.4.1
 * @homepage http://xdsoft.net/jqplugins/datetimepicker/
 * (c) 2014, Chupurnov Valeriy.
 */
class USDateTimePicker extends CWidget {

    public $id = null;
    public $form;
    public $name;
    public $label;
    public $value;
    public $model;
    public $icon = true;
    public $attribute;
    public $options = [];
    public $htmlOptions = [];
    //PRIVATE
    private $controller;

    /**
     * Initializes the widget.
     */
    public function init() {

        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //OPTIONS
        $this->options['lang'] = isset($this->options['lang']) ? $this->options['lang'] : Yii::app()->language;
        //Para que se cierre al primer click
        $this->options['closeOnDateSelect'] = isset($this->options['closeOnDateSelect']) ? $this->options['closeOnDateSelect'] : true;
        //para que no valide al perder foco
        $this->options['validateOnBlur'] = isset($this->options['validateOnBlur']) ? $this->options['validateOnBlur'] : false;
        //Formatos
        $this->options['format'] = isset($this->options['format']) ? $this->options['format'] : Yii::app()->params['datetime_php_format'];
        $this->options['formatDate'] = isset($this->options['formatDate']) ? $this->options['formatDate'] : Yii::app()->params['date_php_format'];
        $this->options['formatTime'] = isset($this->options['formatTime']) ? $this->options['formatTime'] : Yii::app()->params['time_php_format'];
        //SCROLL
        $this->options['scrollMonth'] = isset($this->options['scrollMonth']) ? $this->options['scrollMonth'] : false;
        $this->options['scrollInput'] = isset($this->options['scrollInput']) ? $this->options['scrollInput'] : false;

        $this->options = Util::jsonEncode($this->options);

        //HTML OPTIONS
        $this->htmlOptions['id'] = $this->id;
        $this->htmlOptions['class'] = (isset($this->htmlOptions['class']) ? $this->htmlOptions['class'] : '') . ' us-datepicker';
        if (isset($this->model, $this->attribute)) {
            $this->htmlOptions['placeholder'] = $this->model->getAttributeLabel($this->attribute);
        } else {
            $this->htmlOptions['placeholder'] = $this->label;
        }

        SIANAssets::registerScriptFile("other/jquery.datetimepicker/js/jquery.datetimepicker.js");
        SIANAssets::registerCssFile("other/jquery.datetimepicker/css/jquery.datetimepicker.css");

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        jQuery(function(){
            jQuery('#{$this->id}').datetimepicker({$this->options});
        });    
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->icon) {
            $this->htmlOptions['append'] = '<span class="fa fa-lg fa-calendar black"></span>';
        }
//
        if (isset($this->model, $this->attribute)) {
            echo $this->form->textFieldRow($this->model, $this->attribute, $this->htmlOptions);
        } else {

            $this->htmlOptions['class'] = 'form-control ' . $this->htmlOptions['class'];

            echo "<div class='form-group'>";
            echo CHtml::label($this->label, $this->id, array(
                'class' => 'control-label'
            ));
            echo Chtml::textField($this->name, $this->value, $this->htmlOptions);
            echo "</div>";
        }
    }

}
