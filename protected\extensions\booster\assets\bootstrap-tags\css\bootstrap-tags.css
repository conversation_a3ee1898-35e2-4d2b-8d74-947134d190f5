/* bootstrap-tags styles */
.bootstrap-tags.bootstrap-3 .tag a {
  margin: 0 0 0 .3em; }
.bootstrap-tags.bootstrap-3 .glyphicon-white {
  color: #fff; }

.bootstrap-tags.bootstrap-2 .tag.md {
  padding: .3em .4em .4em; }
.bootstrap-tags.bootstrap-2 .tag.lg {
  padding: .4em .4em .5em; }

.bootstrap-tags {
  position: relative; }
  .bootstrap-tags .tags {
    width: inherit;
    height: 0;
    position: absolute;
    padding: 0;
    margin: 0; }
  .bootstrap-tags .tag-data {
    display: none; }
  .bootstrap-tags .tags-input {
    width: 100%;
    margin: 0;
    padding: 0;
    height: 1.7em;
    box-sizing: content-box;
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box; }
  .bootstrap-tags .tag-list {
    width: 280px;
    height: auto;
    min-height: 26px;
    left: 2px;
    top: 2px;
    position: relative; }
  .bootstrap-tags .tag {
    padding: .4em .4em .4em;
    margin: 0 .1em;
    float: left; }
  .bootstrap-tags .tag.sm {
    padding: .4em .4em .5em;
    font-size: 12px; }
  .bootstrap-tags .tag.md {
    font-size: 14px; }
  .bootstrap-tags .tag.lg {
    font-size: 18px;
    padding: .4em .4em .4em;
    margin: 0 .2em .2em 0; }
  .bootstrap-tags .tag a {
    color: #bbb;
    cursor: pointer;
    opacity: .5; }
  .bootstrap-tags .tag .remove {
    vertical-align: bottom;
    top: 0; }
  .bootstrap-tags ul.tags-suggestion-list {
    width: 300px;
    height: auto;
    list-style: none;
    margin: 0;
    z-index: 2;
    max-height: 160px;
    overflow: scroll; }
    .bootstrap-tags ul.tags-suggestion-list li.tags-suggestion {
      padding: 3px 20px;
      height: auto; }
    .bootstrap-tags ul.tags-suggestion-list li.tags-suggestion-highlighted {
      color: white;
      text-decoration: none;
      background-color: #0081C2;
      background-image: -moz-linear-gradient(top, #0088cc, #0077b3);
      background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0077b3));
      background-image: -webkit-linear-gradient(top, #0088cc, #0077b3);
      background-image: -o-linear-gradient(top, #0088cc, #0077b3);
      background-image: linear-gradient(to bottom, #0088cc, #0077b3);
      background-repeat: repeat-x;
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0077b3', GradientType=0); }
