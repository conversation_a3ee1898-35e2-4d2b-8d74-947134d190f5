<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'demo.siansystem.com/admin';
$domain = 'https://demo.siansystem.com';
$domain2 = 'http://demo.siansystem.com';
$report_domain = 'demo.siansystem.com';
$org = 'demo';
$us = 'us_test';
//SIAN 2
//$domain_react_app = 'https://demo2.siansystem.com';
$api_sian = 'https://api.siansystem.com/';
//Database
//demo_construct -> data viensac
$database_server = '161.132.48.88';
$database_name = 'demo_construction';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
//Mongo
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = 'demo.p12';
$e_billing_certificate_pass = 'GMYa4JVQmY2xg4W';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20477239901BILLING7',
        'password' => 'Billing7'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_TESTING;
$environment = YII_ENVIRONMENT_TESTING;

