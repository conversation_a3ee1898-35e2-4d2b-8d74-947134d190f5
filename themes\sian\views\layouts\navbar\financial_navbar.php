<?php

$arrayItemsNavbar = array(
    // array('icon' => 'th-list', 'label' => 'Datos Generales', 'url' => '#', 'items' => array(
    array('label' => 'Datos Generales', 'url' => '#', 'items' => array(
            array('icon' => 'briefcase', 'label' => 'Cajas/Bancos', 'route' => 'cashbox/index'),
            array('icon' => 'list', 'label' => 'Series', 'route' => 'serie/index'),
            array(
                array('icon' => 'list', 'label' => 'Saldo diario', 'route' => 'cashboxVerification/index'),
            ),
            array('icon' => 'user', 'label' => 'Clientes', 'route' => 'client/index'),
        )),
    // array('icon' => 'list-alt', 'label' => 'Cuentas', 'url' => '#', 'items' => array(
    array('label' => 'Cuentas', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'plus-sign', 'label' => 'Por Cobrar', 'route' => 'toCollect/index'),
                array('icon' => 'minus-sign', 'label' => 'Por Pagar', 'route' => 'toPay/index'),
            ),
            array(
                array('icon' => 'list', 'label' => 'Programación de pagos', 'route' => 'paymentSchedule/index'),
            )
        )),
    // array('icon' => 'list-alt', 'label' => 'Movimientos', 'url' => '#', 'items' => array(
    array('label' => 'Movimientos', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'arrow-left', 'label' => 'Ingreso a Caja/Bancos', 'route' => 'incoming/index'),
                array('icon' => 'arrow-left', 'label' => 'Ingreso masivo a Caja/Bancos', 'route' => 'massiveIncoming/index'),
                array('icon' => 'arrow-right', 'label' => 'Egreso de Caja/Bancos', 'route' => 'outgoing/index'),
                array('icon' => 'arrow-right', 'label' => 'Egreso masivo de Caja/Bancos', 'route' => 'massiveOutgoing/index'),
            ),
            array(
                array('icon' => 'plus-sign', 'label' => 'Letras y cheques de clientes', 'route' => 'clientLetter/index'),
                array('icon' => 'minus-sign', 'label' => 'Letras y cheques de proveedores', 'route' => 'providerLetter/index'),
            ),
            array(
                array('icon' => 'usd', 'label' => 'Pago de Obligaciones Financieras', 'route' => 'feePay/index'),
            ),
        )),
    // array('icon' => 'file', 'label' => 'Reportes', 'url' => '#', 'items' => array(
    array('label' => 'Reportes', 'url' => '#', 'items' => array(
            array('icon' => 'minus-sign', 'label' => 'Cuentas por Pagar', 'route' => 'report/toPay'),
            array('icon' => 'plus-sign', 'label' => 'Cuentas por Cobrar', 'route' => 'report/toCollect'),
            array('icon' => 'usd', 'label' => 'Estado de Cuentas', 'route' => 'report/accountStatus'),
            array('icon' => 'user', 'label' => 'Proveedores', 'route' => 'report/providers'),
            array('icon' => 'user', 'label' => 'Clientes', 'route' => 'report/clients'),
            array('icon' => 'usd', 'label' => 'Pagos Efectuados', 'route' => 'report/payments'),
            array('icon' => 'usd', 'label' => 'Cobros Efectuados', 'route' => 'report/collects'),
            array('icon' => 'usd', 'label' => 'Proyección de Cuentas por Pagar', 'route' => 'report/toPayProjection'),
            array('icon' => 'usd', 'label' => 'Proyección de Cuentas por Cobrar', 'route' => 'report/toCollectProjection'),
            array('icon' => 'usd', 'label' => 'Comparativo de Cuentas', 'route' => 'report/accountsComparative'),
            array('icon' => 'usd', 'label' => 'Clientes Ag. de Retención', 'route' => 'report/clientRetention'),
            array('icon' => 'usd', 'label' => 'Detracciones', 'route' => 'report/detraction'),
            array('icon' => 'eye-open', 'label' => 'Control de Operaciones', 'route' => 'report/operationsControl'),
            array('icon' => 'fa fa-calendar', 'label' => 'Calendario de Pagos', 'route' => 'report/paymentCalendar'),
            array('icon' => 'fa fa-cubes', 'label' => 'Arqueo de Caja', 'route' => 'report/dailyCashFlow'),
        )),
);

$this->widget('application.widgets.SIANNavbar', array(
    'brand' => Yii::app()->id,
    'class' => 'navbar-financial',
    'items' => $this->items_menu, //$arrayItemsNavbar,
));
