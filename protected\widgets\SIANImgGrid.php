<?php

class SIANImgGrid extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $image_input_id;
    public $add_button_id;
    public $remove_button_id;
    public $box = true;
    public $limit = 3;
    public $cms = 'default';
    public $show_target_url = false;
    //PRIVATE

    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->image_input_id = isset($this->image_input_id) ? $this->image_input_id : $this->controller->getServerId();
        $this->add_button_id = isset($this->add_button_id) ? $this->add_button_id : $this->controller->getServerId();
        $this->remove_button_id = isset($this->remove_button_id) ? $this->remove_button_id : $this->controller->getServerId();

        $a_array = [];

        foreach ($this->model->tempImgs as $item) {
            $a_attributes = [];
            $a_attributes['title'] = isset($item->title) ? $item->title : '';
            $a_attributes['description'] = isset($item->description) ? $item->description : '';
            $a_attributes['url'] = $item->url;
            $a_attributes['target_url'] = isset($item->target_url) ? $item->target_url : '';
            $a_attributes['width'] = $item->width;
            $a_attributes['height'] = $item->height;
            $a_attributes['errors'] = $item->getErrors();

            $a_array[] = $a_attributes;
        }

        SIANAssets::registerScriptFile('js/sian-img-grid.js');

        Yii::app()->clientScript->registerScript($this->id, "

        $(document).ready(function() {
            var array = " . CJSON::encode($a_array) . ";
            //COUNT
            var divObj = $('#{$this->id}');
            divObj.data('count', 0);
            //LIMIT
            divObj.data('limit', {$this->limit});
            divObj.data('preffix', '" . Yii::app()->params['org_url'] . "/upload/{$this->cms}');
            divObj.data('show_target_url', " . CJSON::encode($this->show_target_url) . ");

            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {
                    SIANImgGridAddItem('{$this->id}', array[i]['url'], array[i]['title'], array[i]['description'], array[i]['target_url'], array[i]['width'], array[i]['height'], array[i]['errors']);
                }
            }
            else
            {
                $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
            }

            SIANImgGridUpdate('{$this->id}');

            //Para hacer reordenable las filas
            $(function() {
                $('#{$this->id}').find('tbody').sortable({
                    stop: function( event, ui ) {
                        SIANImgGridUpdate('{$this->id}');
                    }
                });
            });
        });

        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var image = $('#{$this->image_input_id}').val();

            if (image.length === 0)
            {
                $('#{$this->image_input_id}').focus();
                return;
            }

            var imgObj = new Image();
            imgObj.src = image;
            $(imgObj).one('load', function(){
                SIANImgGridAddItem('{$this->id}', image, '', '', '', imgObj.width, imgObj.height, []);
                SIANImgGridUpdate('{$this->id}');
                unfocusable();
                //FOCUS
                $('#{$this->remove_button_id}').click();
                $('#{$this->image_input_id}').focus();
            });
        });
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->box) {
            $this->beginWidget('booster.widgets.TbPanel', array(
                'title' => $this->model->getAttributeLabel('tempImgs'),
                'headerIcon' => 'picture',
                'htmlOptions' => array(
                    'class' => $this->model->hasErrors('tempImgs') ? 'us-error' : ''
                )
            ));
        }

        echo "<div class = 'row'>";
        echo "<div class = 'col-lg-10 col-md-10 col-sm-10 col-xs-10'>";
        echo $this->widget("application.widgets.USKCFinder", array(
            'id' => $this->image_input_id,
            'form' => $this->form,
            'name' => 'newImg[url]',
            'label' => 'Imagen',
            'parent_id' => $this->modal_id,
            'buttons' => array('images', 'snapshot'),
            'cms' => $this->cms,
            'remove_button_id' => $this->remove_button_id,
                ), true);
        echo "</div>";

        echo "<div class = 'col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo CHtml::label('Agregar', $this->add_button_id, array(
        ));
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_button_id,
            'context' => 'primary',
            'icon' => 'fa fa-lg fa-plus white',
            'size' => 'default',
            'title' => 'Añadir',
            'block' => true,
        ));
        echo "</div>";
        echo "</div>";

        echo CHtml::label("<h4>{$this->model->getAttributeLabel('tempImgs')}</h4>", $this->id, array(
            'class' => $this->model->hasErrors('tempImgs') && !$this->box ? 'error' : ''
        ));

        echo "<table id = '{$this->id}' class = 'table table-condensed table-hover " . ($this->model->hasErrors('tempImgs') && !$this->box ? 'us-error' : '') . "' data-modal_id = '{$this->modal_id}'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th width = '10%'>#</th>";
        echo "<th width='30%'>Imagen</th>";
        if ($this->show_target_url) {
            echo "<th width='50%'>Título/descripción/URL</th>";
        } else {
            echo "<th width='50%'>Título/descripción</th>";
        }
        echo "<th width='10%'>Vista</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody></tbody>";
        echo "<tfoot>";
        echo "</tfoot>";
        echo "</table>";

        if ($this->box) {
            $this->endWidget();
        }
    }

}
