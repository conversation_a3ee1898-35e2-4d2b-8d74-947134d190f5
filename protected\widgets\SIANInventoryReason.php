<?php

class SIANInventoryReason extends CWidget {

    //CONST
    public $id;
    public $form;
    public $model;
    public $result_items = [];
    public $reason_items = [];
    public $readonly = false;
    public $disabled = false;
    public $required = false;
    public $onChange = '';
    //PRIVATE
    private $controller;
    //
    public $auto_type_result_id;
    public $auto_action_type_id;
    public $auto_operation_id;
    public $auto_document_id;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->auto_type_result_id = isset($this->auto_type_result_id) ? $this->auto_type_result_id : $this->controller->getServerId();
        $this->auto_action_type_id = isset($this->auto_action_type_id) ? $this->auto_action_type_id : $this->controller->getServerId();
        $this->auto_operation_id = isset($this->auto_operation_id) ? $this->auto_operation_id : $this->controller->getServerId();
        $this->auto_document_id = isset($this->auto_document_id) ? $this->auto_document_id : $this->controller->getServerId();
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        $a_reason = [
            InventoryReason::TYPE_ACTION_ADJUST_IN => "warehouse/adjustIn",
            InventoryReason::TYPE_ACTION_ADJUST_OUT => "warehouse/adjustOut",
        ];

        SIANAssets::registerScriptFile('js/sian-inventory-reason.js');

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        var divObj = $('#{$this->id}');
        divObj.data('operation_url', '{$this->controller->createUrl('/widget/getOperations')}');
        divObj.data('reasons', " . CJSON::encode($a_reason) . ");                        
        divObj.data('document_url', '{$this->controller->createUrl('/widget/getDocuments')}');
        
        //Evento load 
        $(document).ready(function() {
            var a_result_items = " . CJSON::encode($this->result_items) . ";
            SIANInventoryReasonFillTypeResult('{$this->auto_type_result_id}', a_result_items, '" . $this->model->type_result . "', true);
        });
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}'>";

        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        echo $this->form->textFieldRow($this->model, 'inventory_reason_name', array(
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'inventory_type_id', $this->reason_items, array(
            'empty' => Strings::SELECT_OPTION,
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'type_result', [], array(
            'id' => $this->auto_type_result_id,
            'empty' => Strings::SELECT_OPTION,
            'onchange' => "
                            if ($(this).val() != null){
                                if ($(this).val() != ''){
                                    var optionObj = $(this).find('option:selected');
                                    var actionsItems = optionObj.data('actions');
                                    SIANInventoryReasonFillActions('{$this->auto_action_type_id}', actionsItems, '{$this->model->action_type}', false);
                                } else {
                                    $('#{$this->auto_action_type_id}').empty().append('<option value>' + STRINGS_SELECT_OPTION + '</option>');
                                    $('#{$this->auto_operation_id}').empty().append('<option value>' + STRINGS_SELECT_OPTION + '</option>');
                                    $('#{$this->auto_document_id}').empty().append('<option value>' + STRINGS_SELECT_OPTION + '</option>');
                                }
                            }
                            "
                ,));
        echo "</div>";

        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'action_type', [], array(
            'id' => $this->auto_action_type_id,
            'empty' => Strings::SELECT_OPTION,
            'onchange' => "
                            if ($(this).val() != null){
                                if ($(this).val() != ''){
                                    SIANInventoryReasonGetOperations('{$this->id}', this.value, '{$this->model->operation_id}');   
                                    SIANInventoryReasonGetDocuments('{$this->id}', this.value, '{$this->model->document_id}');
                                }else{
                                    $('#{$this->auto_operation_id}').empty().append('<option value>' + STRINGS_SELECT_OPTION + '</option>');
                                    $('#{$this->auto_document_id}').empty().append('<option value>' + STRINGS_SELECT_OPTION + '</option>');
                                }
                            }
                          "
                ,));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'operation_id', [], array(
            'id' => $this->auto_operation_id,
            'class' => 'sian-inventory-reason-operation',
            'empty' => Strings::SELECT_OPTION,
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'document_id', [], array(
            'id' => $this->auto_document_id,
            'class' => 'sian-inventory-reason-document',
            'empty' => Strings::SELECT_OPTION,
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-6 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'default',
        ));
        echo "</div>";


        echo "<div class='col-lg-4 col-md-6 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'status',
        ));
        echo "</div>";

        echo "</div>";
        echo "</div>";
    }

}
