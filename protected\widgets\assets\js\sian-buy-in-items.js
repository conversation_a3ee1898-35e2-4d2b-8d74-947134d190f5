function SIANBuyInItemsAddItem(div_id, movement_id, item_number, product_id, product_name, pres_quantity, product_type, alias)
{
    //DIV
    var divObj = $('#' + div_id);
    var name_preffix = divObj.data('name_preffix');
    var readonly = divObj.data('readonly');
    var count = parseInt(divObj.data('count'));

    var row_id = getLocalId();
    //
    var row = '<tr id=\'' + row_id + '\' class=\'sian-buy-in-items-products\'>';
    //
    row += '<td class=\'form-group\'>';
    row += '<input type=\'hidden\' class=\'form-control sian-buy-in-items-products-movement-id\' name=\'BuyIn[' + row_id + '][movement_id]\' value=\'' + movement_id + '\' readonly=\'readonly\' placeholder=\'Movement ID\'>';
    row += '<input type=\'checkbox\' checked=\'checkbox\' class=\'sian-commercial-checked-grid-2-item-checkform-control sian-buy-in-items-products-movement-id\' name=\'BuyIn[' + row_id + '][movement_id]\' value=\'1\' placeholder=\'Check\'>';
    row += "</td>";
    //
    row += '<td class=\'form-group\'>';
    row += '<input type=\'text\' class=\'form-control sian-buy-in-items-products-item-number\' name=\'BuyIn[' + row_id + '][item_number]\' value=\'' + item_number + '\' readonly=\'readonly\' placeholder=\'Item\'>';
    row += "</td>";
    //
    row += '<td class=\'form-group\'>';
    row += '<input type=\'text\' class=\'form-control sian-buy-in-items-products-product-id\' name=\'BuyIn[' + row_id + '][product_id]\' value=\'' + product_id + '\' readonly=\'readonly\'  placeholder=\'Id Prod.\'>';
    row += "</td>";
    //
    row += '<td class=\'form-group\'>';
    row += '<input type=\'text\' class=\'form-control sian-buy-in-items-products-product-name\' name=\'BuyIn[' + row_id + '][product_name]\' value=\'' + product_name + '\' readonly=\'readonly\' placeholder=\'Productos\'>';
    row += "</td>";
    //
    row += '<td class=\'form-group\'>';
    row += '<input type=\'number\' class=\'form-control sian-buy-in-items-products-pres-quantity us-double\' name=\'BuyIn[' + row_id + '][pres_quantity]\' value=\'' + pres_quantity + '\' min=\'1\' max=\'' + pres_quantity + '\' step=\'1\'>';
    row += '<input type=\'hidden\' class=\'form-control sian-buy-in-items-products-pres-quantity-old us-double\' name=\'BuyIn[' + row_id + '][pres_quantity_old]\' value=\'' + pres_quantity + '\' min=\'1\' max=\'' + pres_quantity + '\' step=\'1\'>';
    row += '<input type=\'hidden\' class=\'form-control sian-buy-in-items-products-product-type\' name=\'BuyIn[' + row_id + '][product_type]\' value=\'' + product_type + '\' readonly=\'readonly\' placeholder=\'Tipo Producto\'>';
    row += '<input type=\'hidden\' class=\'form-control sian-buy-in-items-products-alias\' name=\'BuyIn[' + row_id + '][alias]\' value=\'' + alias + '\' readonly=\'readonly\' placeholder=\'Alias\'>';
    row += "</td>";

    row += "</tr>";

//COUNT DE ITEMS
    if (count === 0)
    {
        divObj.find('table.products_table tbody').html(row);
    } else
    {
        divObj.find('table.products_table tbody').append(row);
    }
    //
    divObj.data('count', count + 1);
}