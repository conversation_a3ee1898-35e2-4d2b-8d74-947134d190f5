<?php

class S<PERSON>NAddress extends CWidget {

    public $id;
    public $model;
    public $name = 'Address';
    public $property = 'tempAddresses';
    public $title = 'Direcciones';
    public $aclaration = null;
    public $disabled = false;
    public $readonly = false;
    public $removable = true;
    public $visible = true;
    public $limit = 3;
    public $zero_warning = '';
    //PRIVATE
    private $clientScript;
    private $controller;

    public function init() {

        $this->clientScript = Yii::app()->clientScript;
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //Recorremos los ítems
        $a_addressItems = [];
        foreach ($this->model[$this->property] as $o_address) {
            $a_attributes = [];
            $a_attributes['dept_code'] = isset($o_address->dept_code) ? $o_address->dept_code : '';
            $a_attributes['prov_code'] = isset($o_address->prov_code) ? $o_address->prov_code : '';
            $a_attributes['dist_code'] = isset($o_address->dist_code) ? $o_address->dist_code : '';
            $a_attributes['address'] = isset($o_address->address) ? $o_address->address : '';
            $a_attributes['reference'] = isset($o_address->reference) ? $o_address->reference : '';
            $a_attributes['lat'] = isset($o_address->lat) ? $o_address->lat : '';
            $a_attributes['lng'] = isset($o_address->lng) ? $o_address->lng : '';
            $a_attributes['type'] = isset($o_address->type) ? $o_address->type : '';
            $a_attributes['errors'] = $o_address->getAllErrors();

            $a_addressItems[] = $a_attributes;
        }

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-address.js');

        //SCRIPTS
        $this->clientScript->registerScript($this->id, "

        window.geoloc = " . CJSON::encode($this->controller->getGeoloc()) . ";

        var array = " . CJSON::encode($a_addressItems) . ";

        //COUNT y LIMIT
        var divObj = $('#{$this->id}');
        divObj.data('count', 0);
        divObj.data('limit', {$this->limit});
        divObj.data('name', '{$this->name}');
        divObj.data('visible', " . CJSON::encode($this->visible) . ");
        divObj.data('readonly', " . CJSON::encode($this->readonly) . ");
        divObj.data('disabled', " . CJSON::encode($this->disabled) . ");
        divObj.data('removable', " . CJSON::encode($this->removable) . ");
        divObj.data('zero_warning', '{$this->zero_warning}');

        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANAddressAddItem('{$this->id}', array[i]['dept_code'], array[i]['prov_code'], array[i]['dist_code'], array[i]['address'], array[i]['reference'], array[i]['lat'], array[i]['lng'], array[i]['type'], array[i]['errors']);
            }
        }
        else
        {
            $('#{$this->id}').find('div.sian-address-items').html('<p>" . Strings::NO_DATA . "</p>');
        }
        ", CClientScript::POS_END);

        /*
          $.getScript('" . USGoogleMap::getAPIUrl(true, "f{$this->id}") . "')
          .done(function(script, textStatus) {
          alert('No registered!');
          $.cookie('usgooglemap_js', true);
          })
          .fail(function(jqxhr, settings, exception) {
          alert('Falló la carga del mapa');
          });
         */
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "{$this->title}" . (isset($this->aclaration) ? ' *' : ''),
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => $this->model->hasErrors($this->property) ? 'us-error' : '',
                'style' => $this->visible ? 'display:block;' : 'display:none;',
            )
        ));
        echo "<div class='sian-address-items'></div>";

        if ($this->removable && !$this->readonly) {
            echo '<p>' . CHtml::link("<span class='fa fa-plus fa-lg black'></span> Agregar nuevo", Strings::LINK_TEXT, array(
                'onclick' => "
                    
                    var disabled = $('#{$this->id}').prop('disabled');
                    if(disabled)
                    {
                        bootbox.alert(us_message('No se puede \'Agregar\' en este momento porque el componente está deshabilitado.', 'warning'));
                    }
                    else
                    {
                        SIANAddressAddItem('{$this->id}', null, null, null, '', '', null, null, '', [])
                    }
                    ",
                'title' => 'Agregar'
            )) . '</p>';
        }

        if (isset($this->aclaration)) {
            echo "<p><em>(*) {$this->aclaration}</em></p>";
        }
        $this->endWidget();
    }

}
