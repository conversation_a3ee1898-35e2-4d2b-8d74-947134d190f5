<?xml version="1.0" encoding="UTF-8"?>
<!--
  Document Type:     QualifiedDatatypes
  Generated On:      Wed Sep 13 09:16:50 2006

-->
<!-- ===== xsd:schema Element With Namespaces Declarations ===== -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    targetNamespace="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2"
    xmlns="urn:oasis:names:specification:ubl:schema:xsd:QualifiedDatatypes-2"
    xmlns:udt="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2"
    xmlns:ccts="urn:un:unece:uncefact:documentation:2"
    elementFormDefault="qualified"
    attributeFormDefault="unqualified"
    version="2.0">
<!-- ===== Imports ===== -->
  <xsd:import namespace="urn:un:unece:uncefact:data:specification:UnqualifiedDataTypesSchemaModule:2" schemaLocation="UnqualifiedDataTypeSchemaModule-2.0.xsd"/>
<!-- ===== Type Definitions ===== -->
  <xsd:complexType name="AllowanceChargeReasonCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Allowance Charge Reason_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of possible reasons for an allowance or charge.</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Allowance Charge Reason</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
          <ccts:UsageRule>Used under the terms of the UNECE  policy stated at http://www.unece.org/ece_legal.htm.</ccts:UsageRule>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="UN/ECE 4465" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Allowance Charge Reason_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="6" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Allowance Charge Reason_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition>Defaults to the UN/EDIFACT data element 3055 code list.</ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="United Nations Economic Commission for Europe" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Allowance Charge Reason_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Adjustment Reason Description" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Allowance Charge Reason_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="D03A" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Allowance Charge Reason_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition>Identifies the Directory of the UN/EDIFACT code list.</ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Allowance Charge Reason_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Allowance Charge Reason_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/AllowanceChargeReasonCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Allowance Charge Reason_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:AllowanceChargeReasonCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Allowance Charge Reason_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="ChannelCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Channel_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of possible ways in which communication can be made (eg. Phone, email, etc).</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Channel</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
          <ccts:UsageRule>Used under the terms of the UNECE  policy stated at http://www.unece.org/ece_legal.htm.</ccts:UsageRule>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="UN/ECE 3155" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Channel_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="6" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Channel_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition>Defaults to the UN/EDIFACT data element 3055 code list.</ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="United Nations Economic Commission for Europe" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Channel_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Communication Address Code Qualifier" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Channel_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="D03A" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Channel_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition>Identifies the Directory of the UN/EDIFACT code list.</ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Channel_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Channel_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/ChannelCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Channel_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:ChannelCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Channel_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="ChipCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Chip_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>Distinction between CHIP and MAG STRIPE cards</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Chip</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="Chip" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Chip_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="UBL" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Chip_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="OASIS Universal Business Language" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Chip_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Chip" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Chip_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Chip_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Chip_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Chip_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/ChipCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Chip_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:ChipCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Chip_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="ContainerSizeTypeCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Container Size Type_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of code values for classifying series 1 freight containers based on external dimensions and specifies the assoziated ratings and, where appropriate, the minimum internal and door opening dimensions for certain types of containers.</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Container Size Type</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="ISO 668" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Container Size Type_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="5" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Container Size Type_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="ISO" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Container Size Type_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Container Size Type" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Container Size Type_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="1995" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Container Size Type_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Container Size Type_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Container Size Type_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/special-purpose/ContainerSizeTypeCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Container Size Type_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:ContainerSizeTypeCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Container Size Type_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="CountryIdentificationCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Country Identification_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of countries of the world.</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Country Identification</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
          <ccts:UsageRule>Derived from the ISO 3166-1-alpha-2 code elements used under the terms of the ISO policy stated at http://www.iso.org/iso/en/commcentre/pressreleases/2003/Ref871.html.</ccts:UsageRule>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="ISO3166-1" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Country Identification_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="6" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Country Identification_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition>Defaults to the UN/ECE rec 3 (Code for the Representation of Names of Countries)</ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="United Nations Economic Commission for Europe" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Country Identification_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Country" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Country Identification_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="0.3" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Country Identification_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Country Identification_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Country Identification_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/CountryIdentificationCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Country Identification_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:CountryIdentificationCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Country Identification_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="CurrencyCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Currency_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of world currencies.</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Currency</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
          <ccts:UsageRule>Derived from the ISO 4217 currency code list and used under the terms of the ISO policy stated at 
http://www.iso.org/iso/en/commcentre/pressreleases/2003/Ref871.html</ccts:UsageRule>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="ISO 4217 Alpha" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Currency_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="6" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Currency_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition>Defaults to the UN/EDIFACT data element 3055 code list.</ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="United Nations Economic Commission for Europe" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Currency_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Currency" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Currency_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="2001" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Currency_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Currency_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Currency_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/cefact/CurrencyCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Currency_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:un:unece:uncefact:codelist:specification:54217:2001" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Currency_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="DocumentStatusCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Document Status_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of possible statuses of a document with regard to its original state.</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Document Status</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="Document Status" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Document Status_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="UBL" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Document Status_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="OASIS Universal Business Language" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Document Status_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Document Status" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Document Status_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Document Status_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Document Status_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Document Status_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/DocumentStatusCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Document Status_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:DocumentStatusCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Document Status_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LatitudeDirectionCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Latitude Direction_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The possible directions of latitude</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Latitude Direction</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="Latitude Direction" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Latitude Direction_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="UBL" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Latitude Direction_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="OASIS Universal Business Language" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Latitude Direction_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Latitude Direction" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Latitude Direction_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Latitude Direction_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Latitude Direction_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Latitude Direction_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/LatitudeDirectionCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Latitude Direction_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:LatitudeDirectionCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Latitude Direction_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LineStatusCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Line Status_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of possible statuses of a line in a transaction with regard to its original state.</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Line Status</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="Line Status" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Line Status_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="UBL" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Line Status_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="OASIS Universal Business Language" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Line Status_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Line Status" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Line Status_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Line Status_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Line Status_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Line Status_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/LineStatusCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Line Status_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:LineStatusCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Line Status_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LongitudeDirectionCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Longitude Direction_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The possible directions of longitude</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Longitude Direction</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="Longitude Direction" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Longitude Direction_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="UBL" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Longitude Direction_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="OASIS Universal Business Language" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Longitude Direction_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Longitude Direction" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Longitude Direction_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Longitude Direction_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Longitude Direction_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Longitude Direction_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/LongitudeDirectionCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Longitude Direction_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:LongitudeDirectionCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Longitude Direction_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="OperatorCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Operator_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of valid arithmetic operators</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Operator</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="Operator" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Operator_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="UBL" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Operator_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="OASIS Universal Business Language" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Operator_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Operator" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Operator_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Operator_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Operator_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Operator_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/OperatorCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Operator_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:OperatorCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Operator_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="PackagingTypeCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Packaging Type_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of code values for Passengers, Types of Cargo, Packages and Packaging Materials (with Complementary Codes for Package Names)</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Packaging Type</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="UN/ECE rec 21" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Packaging Type_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="6" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Packaging Type_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="United Nations Economic Commission for Europe" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Packaging Type_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Packaging Type" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Packaging Type_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="Revision 5" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Packaging Type_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Packaging Type_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Packaging Type_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/PackagingTypeCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Packaging Type_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:PackagingTypeCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Packaging Type_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="PaymentMeansCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Payment Means_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of valid means of paying the debt incurred.</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Payment Means</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
          <ccts:UsageRule>Used under the terms of the UNECE  policy stated at http://www.unece.org/ece_legal.htm.</ccts:UsageRule>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="UN/ECE 4461" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Payment Means_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="6" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Payment Means_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition>Defaults to the UN/EDIFACT data element 3055 code list.</ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="United Nations Economic Commission for Europe" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Payment Means_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Payment Means" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Payment Means_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="D03A" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Payment Means_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition>Identifies the Directory of the UN/EDIFACT code list.</ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Payment Means_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Payment Means_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/PaymentMeansCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Payment Means_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:PaymentMeansCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Payment Means_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="PortCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Port_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of code values for Trade and Transport Locations</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Port</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="UN/ECE rec 16" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Port_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="6" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Port_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="United Nations Economic Commission for Europe" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Port_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Port" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Port_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="Third Edition" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Port_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Port_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Port_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/special-purpose/PortCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Port_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:PortCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Port_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="SubstitutionStatusCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Substitution Status_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of values that indicate the status of an OrderItem in relation to substitution, denoting an acceptable substitute in the Order, an original for which an alternative is offered in the Order Confirmation, or an original for which an actual replacement is advised in the Despatch Advice.</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Substitution Status</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="Substitution Status" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Substitution Status_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="UBL" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Substitution Status_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="OASIS Universal Business Language" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Substitution Status_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Substitution Status" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Substitution Status_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Substitution Status_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Substitution Status_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Substitution Status_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/SubstitutionStatusCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Substitution Status_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:SubstitutionStatusCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Substitution Status_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="TransportationStatusCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Transportation Status_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of code values for Trade and Transport Status</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Transportation Status</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="UN/ECE rec 24" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transportation Status_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="6" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transportation Status_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="United Nations Economic Commission for Europe" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transportation Status_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Transportation Status" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transportation Status_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="Third Revision" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transportation Status_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transportation Status_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transportation Status_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/TransportationStatusCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transportation Status_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:TransportationStatusCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transportation Status_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="TransportEquipmentTypeCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Transport Equipment Type_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of code values for qualifying a type of equipment used in the transportation of goods.</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Transport Equipment Type</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="UN/ECE 8053" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Equipment Type_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="6" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Equipment Type_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="United Nations Economic Commission for Europe" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Equipment Type_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Equipment type code qualifier" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Equipment Type_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="D.05B" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Equipment Type_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Equipment Type_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Equipment Type_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/TransportEquipmentTypeCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Equipment Type_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:TransportEquipmentTypeCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Equipment Type_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="TransportModeCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Transport Mode_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of code values for modes of transport</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Transport Mode</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="UN/ECE rec 16" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Mode_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="6" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Mode_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="United Nations Economic Commission for Europe" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Mode_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Transport Mode" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Mode_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="Presented by the CDWG" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Mode_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Mode_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Mode_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/default/TransportModeCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Mode_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:oasis:names:specification:ubl:codelist:gc:TransportModeCode-2.0" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Transport Mode_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="UnitOfMeasureCodeType">
    <xsd:annotation>
      <xsd:documentation>
        <ccts:Component>
          <ccts:DictionaryEntryName>Unit Of Measure_ Code. Type</ccts:DictionaryEntryName>
          <ccts:Version>2.0</ccts:Version>
          <ccts:Definition>The set of code values for Units of Measure Used in International Trade</ccts:Definition>
          <ccts:RepresentationTerm>Code</ccts:RepresentationTerm>
          <ccts:QualifierTerm>Unit Of Measure</ccts:QualifierTerm>
          <ccts:UniqueID></ccts:UniqueID>
        </ccts:Component>
      </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:restriction base="udt:CodeType">
        <xsd:attribute name="listID" type="xsd:normalizedString" default="UN/ECE rec 20" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Unit Of Measure_ Code List. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyID" type="xsd:normalizedString" default="6" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Unit Of Measure_ Code List. Agency. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listAgencyName" type="xsd:string" default="United Nations Economic Commission for Europe" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Unit Of Measure_ Code List. Agency Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listName" type="xsd:string" default="Unit Of Measure" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Unit Of Measure_ Code List. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listVersionID" type="xsd:normalizedString" default="Revision 4" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Unit Of Measure_ Code List. Version. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="name" type="xsd:string" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Unit Of Measure_ Code. Name. Text</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="languageID" type="xsd:language" default="en" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Unit Of Measure_ Language. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listURI" type="xsd:anyURI" default="http://docs.oasis-open.org/ubl/os-ubl-2.0/cl/gc/cefact/UnitOfMeasureCode-2.0.gc" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Unit Of Measure_ Code List. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
        <xsd:attribute name="listSchemeURI" type="xsd:anyURI" default="urn:un:unece:uncefact:codelist:specification:66411:2001" use="optional">
          <xsd:annotation>
            <xsd:documentation>
              <ccts:Name>Unit Of Measure_ Code List Scheme. Uniform Resource. Identifier</ccts:Name>
              <ccts:Definition></ccts:Definition>
              <ccts:PrimitiveType>String</ccts:PrimitiveType>
            </xsd:documentation>
          </xsd:annotation>
        </xsd:attribute>
      </xsd:restriction>
    </xsd:simpleContent>
  </xsd:complexType>
</xsd:schema>
<!-- ===== Copyright Notice ===== -->
<!--
  OASIS takes no position regarding the validity or scope of any 
  intellectual property or other rights that might be claimed to pertain 
  to the implementation or use of the technology described in this 
  document or the extent to which any license under such rights 
  might or might not be available; neither does it represent that it has 
  made any effort to identify any such rights. Information on OASIS's 
  procedures with respect to rights in OASIS specifications can be 
  found at the OASIS website. Copies of claims of rights made 
  available for publication and any assurances of licenses to be made 
  available, or the result of an attempt made to obtain a general 
  license or permission for the use of such proprietary rights by 
  implementors or users of this specification, can be obtained from 
  the OASIS Executive Director.

  OASIS invites any interested party to bring to its attention any 
  copyrights, patents or patent applications, or other proprietary 
  rights which may cover technology that may be required to 
  implement this specification. Please address the information to the 
  OASIS Executive Director.
  
  Copyright (C) OASIS Open 2001-2006. All Rights Reserved.

  This document and translations of it may be copied and furnished to 
  others, and derivative works that comment on or otherwise explain 
  it or assist in its implementation may be prepared, copied, 
  published and distributed, in whole or in part, without restriction of 
  any kind, provided that the above copyright notice and this 
  paragraph are included on all such copies and derivative works. 
  However, this document itself may not be modified in any way, 
  such as by removing the copyright notice or references to OASIS, 
  except as needed for the purpose of developing OASIS 
  specifications, in which case the procedures for copyrights defined 
  in the OASIS Intellectual Property Rights document must be 
  followed, or as required to translate it into languages other than 
  English. 

  The limited permissions granted above are perpetual and will not be 
  revoked by OASIS or its successors or assigns. 

  This document and the information contained herein is provided on 
  an "AS IS" basis and OASIS DISCLAIMS ALL WARRANTIES, 
  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY 
  WARRANTY THAT THE USE OF THE INFORMATION HEREIN 
  WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED 
  WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A 
  PARTICULAR PURPOSE.
-->
