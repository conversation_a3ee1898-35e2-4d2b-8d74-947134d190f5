<!DOCTYPE html>
<html lang="es-us">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>   
        <title><?php echo CHtml::encode($this->pageTitle); ?></title>

        <meta name="description" content="overview &amp; stats"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
        <link rel="shortcut icon"  href="<?php echo Yii::app()->params['admin_url']; ?>/images/icons/favicon.svg">
        <link rel="stylesheet" href="<?php echo Yii::app()->theme->getBaseUrl(); ?>/bootstrap/css/bootstrap.min.css"/>        
        <?php
        SIANAssets::registerCoreCss();
        ?>
    </head>

    <body>
        <?php
        if (Yii::app()->params['environment'] !== YII_ENVIRONMENT_PRODUCTION) {
        ?>
        <div id="development">
            <div class="img-1" style="position: absolute; width: 28px; height: 100%; top: 0; left: 0;"></div>
            <div class="img-2" style="position: absolute; width: 28px; height: 100%; top: 0; right: 0;"></div>
        </div>
        <?php } ?>

        <div class="container-fluid" id="page">
            <?php
            if (isset($this->module)) {
                include("navbar.php");
            }
            ?>
            <div id="SianContent" class="page-content">
                <div class="breadcrumbs" style="display: none;">
                    <?php 
                    if (isset($this->breadcrumbs)) {
                        $this->widget('booster.widgets.TbBreadcrumbs', array(
                            'homeLink' => CHtml::link("<i class='fa fa-square fa-1x' style='color:{$this->getOrganization()->globalVar->color};'></i> {$this->getOrganization()->person->person_name}", array('/site/index')),
                            'links' => $this->breadcrumbs,
                        ));
                    }
                    ?>
                </div>
                <div class="wrapper-grid">
                    <?php
                    $this->renderPartial("application.views.common._flashes");  
                    ?>
                    <h1 class="text-center" style="margin-top: 0px;"><?php echo $this->title; ?></h1>
                    <br>
                    <?php
                    echo $content;
                    ?>
                    <hr>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 text-center inline">
                            <?php
                            echo $this->viewButtons;
                            ?>
                        </div>
                    </div>
                </div>
            </div>
            <div id="SianFooter">
                <div class="page-prefooter">
                    <div class="prefooter-sections">
                        <div class="prefooter-section-1">
                            <img class="prefooter-logo-sian" src="<?php echo Yii::app()->params['admin_url']; ?>/images/logos/logo-sian-gris.svg" alt="sian-banner-gris.svg">
                        </div>
                        <div class="prefooter-section-2">
                            <p class="text-version"><?= CHtml::link("Versión " . Yii::app()->params['version'], Yii::app()->params['domain'] . "/changelog", ['target' => '_CHANGELOG']) ?></p>
                        </div>
                    </div>
                </div>
                <hr class="page-divider-pre-footer" style="color: #DADADA; margin: 0px; padding: 0px; background-color: #DADADA; height: 2px; width: 100%;">
                <div id="index-footer" class="page-footer">
                    <div class="text-center">
                        <p class="text-copyright">SIAN ERP | © <?php $today = getdate(); echo $today['year']; ?> Todos los derechos reservados</p>
                    </div>
                </div>
            </div>
            <div class="clear"></div>
            <div class="modal_dynamic"></div>
        </div>
    </body>
    <?php
    if (Yii::app()->params['environment'] === YII_ENVIRONMENT_PRODUCTION) {
//        $this->widget('application.widgets.SIANFreshChat');
    }
    $this->widget('application.widgets.USBackToTop');
    SIANAssets::registerCoreJs();
    ?>
</html>