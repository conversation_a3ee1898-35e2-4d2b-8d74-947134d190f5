<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANSunat
 *
 * <AUTHOR>
 */
class SIANSunat {

    use TCTrait;

    const NO_PERIOD_DATA = 'No existe Información que mostrar. Puede Consultar otro Periodo';

//    public static function getExternalRUCData($p_s_ruc) {
//
//        $a_response = USSunat::getRUCData($p_s_ruc);
//
//        if ($a_response['code'] !== USREST::CODE_SUCCESS) {
//            return $a_response;
//        }
//
//        $a_data = [];
//        foreach ($a_response['data'] as $s_key => $s_text) {
//
//            switch ($s_key) {
//                case USSunat::FIELD_RUC:
//                    $a_text_parts = explode('-', $s_text);
//                    $a_data[USSunat::FIELD_RUC] = trim($a_text_parts[0]);
//                    $a_data[USSunat::FIELD_BUSINESS_NAME] = trim($a_text_parts[1]);
//                    break;
//                case USSunat::FIELD_DOC_TYPE:
//                    $a_text_parts = explode('-', $s_text);
//                    $a_data[USSunat::FIELD_DNI] = trim(substr($a_text_parts[0], 3));
//                    $a_data[USSunat::FIELD_NAME] = trim($a_text_parts[1]);
//                    break;
//                case USSunat::FIELD_BIRTHDAY:
//                    $a_data['birthday'] = $s_text;
//                    break;
//                case USSunat::FIELD_STATUS:
//                    $a_data['status'] = ($s_text === 'ACTIVO' ? 1 : 0);
//                    break;
//                case USSunat::FIELD_OFFICIAL_ADDRESS:
//                    $a_text_parts = trim($s_text) === '-' ? [] : explode('-', trim($s_text));
//
//                    if (count($a_text_parts) > 0) {
//
//                        $a_data[USSunat::FIELD_DIST] = trim(array_pop($a_text_parts));
//                        $a_data[USSunat::FIELD_PROV] = trim(array_pop($a_text_parts));
//                        //Obtenemos el resto del array
//                        $s_rest = trim(array_pop($a_text_parts));
//                        //Obtenemos los departamentos de la base de datos
//                        $a_depts = Ubigeo::model()->findAll(array(
//                            'select' => array('dept_name'),
//                            'distinct' => true,
//                        ));
//                        //La longitud maxima del nombre de un departamento es 13 (MADRE DE DIOS)
//                        //Obtenemos esos caracteres del final
//                        $s_search = substr($s_rest, -13);
//                        foreach ($a_depts as $o_dept) {
//                            if (strpos($s_search, $o_dept->dept_name) !== false) {
//                                $a_data[USSunat::FIELD_DEPT] = $o_dept->dept_name;
//                                break;
//                            }
//                        }
//                        $a_data[USSunat::FIELD_ADDRESS] = substr($s_rest, 0, strlen($s_rest) - strlen($a_data[USSunat::FIELD_DEPT]));
//                        //Ubigeo
//                        $o_ubigeo = Ubigeo::model()->findByAttributes(array(
//                            'dept_name' => $a_data[USSunat::FIELD_DEPT],
//                            'prov_name' => $a_data[USSunat::FIELD_PROV],
//                            'dist_name' => $a_data[USSunat::FIELD_DIST],
//                                ), array(
//                            'select' => array('dept_code', 'prov_code', 'dist_code')
//                        ));
//                        if (isset($o_ubigeo)) {
//                            $a_data[USSunat::FIELD_UBIGEO] = $o_ubigeo->dept_code . $o_ubigeo->prov_code . $o_ubigeo->dist_code;
//                        }
//                    }
//                    break;
//                default:
//                    $a_data[$s_key] = $s_text;
//                    break;
//            }
//        }
//
//        return [
//            'code' => $a_response['code'],
//            'message' => $a_response['message'],
//            'data' => $a_data,
//        ];
//    }

    public static function getRUCData($p_s_ruc) {

        switch (Yii::app()->params['ruc_source']) {
            case YII_RUC_SOURCE_SUNAT:
                $a_response = USSunat::getRUCData($p_s_ruc);

                if ($a_response['code'] !== USREST::CODE_SUCCESS) {
                    return $a_response;
                }

                return [
                    'code' => $a_response['code'],
                    'message' => $a_response['message'],
                    'data' => USSunat::processRUCData($a_response['data']),
                ];
            case YII_RUC_SOURCE_MIGO:
                $s_token = Yii::app()->params['migo_token'];
                $a_response = USMigo::getRUCData($s_token, $p_s_ruc);

                if ($a_response['code'] !== USREST::CODE_SUCCESS) {
                    return $a_response;
                }

                return [
                    'code' => $a_response['code'],
                    'message' => $a_response['message'],
                    'data' => USMigo::processRUCData($a_response['data']),
                ];
            default:
                throw new Exception('Origen de datos de RUC desconocido.');
        }
    }

    /**
     * Determina si el RUC existe o no
     * @param string $p_s_ruc RUC
     * @param array $p_a_data (Opcional) Array dónde se guardarán los datos del RUC
     * @return boolean TRUE si existe o FALSE si no
     */
    public static function rucExists($p_s_ruc, &$p_a_data = null) {
        $a_result = self::getRUCData($p_s_ruc);
        if (isset($a_result['data'])) {
            $p_a_data = $a_result['data'];
        }
        return $a_result['code'] === USREST::CODE_SUCCESS;
    }

//    /**
//     * Obtiene los TC del mes de la página de SUNAT
//     * @param integer $p_i_year Año
//     * @param integer $p_i_period Periodo o mes
//     * @return array Array de datos
//     * @throws Exception Error en caso haya
//     */
//    public static function getExchangeRate($p_i_year, $p_i_period) {
//        //Verificamos si hay la data solicitada
//        if (isset($p_i_year) && isset($p_i_period)) {
//            //Verificamos si es un mes pasado o presente (no futuro)
//            if (SIANTime::getYear() * 12 + SIANTime::getMonth() >= $p_i_year * 12 + $p_i_period) {
//                //URL
//                $s_url = "https://e-consulta.sunat.gob.pe/cl-at-ittipcam/tcS01Alias?anho={$p_i_year}&mes={$p_i_period}";
//
//                $a_replace = array(
//                    "<table class=class=\"form-table\" border='1' cellpadding='0' cellspacing='0' width='81%' >" => '<table border="1">',
//                    " class='beta' width='4%' align='center' class=\"ht\"" => '',
//                    " class='beta' width='10%' align='center' class=\"ht\"" => '',
//                    " width='8%' align='center' class=\"tne10\"" => '',
//                    " width='4%' align='center' class=\"H3\"" => '',
//                    '>  <' => '><',
//                    '> ' => '>',
//                    ' <' => '<',
//                    "</tr><tr>" => '',
//                    "<td><strong>" => '</tr><tr><td><strong>'
//                );
//
//                $o_content = StaffLibrary::obtener_contenidos($s_url, "<table class", "</table>");
//
//                if ($o_content) {
//                    $table = StaffLibrary::filtrar($o_content, $a_replace);
//
//                    $head = "<td>Venta</td></tr>";
//                    $table_final = StaffLibrary::substract_inicio($table, $head);
//
//                    $limpiar = array(
//                        "<tr><td>" => '',
//                        "<td>" => '',
//                        "<strong>" => '',
//                        "</strong>" => ''
//                    );
//
//                    if ($table_final) {
//                        $a_data = preg_split('#</tr>#i', $table_final);
//
//                        if (strpos($a_data[0], self::NO_PERIOD_DATA) === false) {
//                            $i = 0;
//
//                            foreach ($a_data as $row) {
//                                $a_data[$i] = array_filter(StaffLibrary::filtrar(preg_split('#</td>#i', $row), $limpiar));
//
//                                if ($a_data[$i]) {
//                                    if ($a_data[$i][0]) {
//                                        $a_data[$i]["year"] = $p_i_year;
//                                        $a_data[$i]["month"] = str_pad($p_i_period, 2, "0", STR_PAD_LEFT);
//                                        $a_data[$i]["day"] = str_pad($a_data[$i][0], 2, "0", STR_PAD_LEFT);
//                                        $a_data[$i]["date"] = $p_i_year . "-" . $a_data[$i]["month"] . "-" . $a_data[$i]["day"];
//
//
//                                        unset($a_data[$i][0]);
//                                    }
//
//                                    $a_data[$i]["purchase"] = $a_data[$i][1];
//                                    unset($a_data[$i][1]);
//                                    $a_data[$i]["sale"] = $a_data[$i][2];
//                                    unset($a_data[$i][2]);
//                                }
//
//                                $i++;
//                            }
//                        } else {
//                            return false;
//                        }
//                    }
//
//                    return $a_data;
//                } else {
//                    throw new Exception('No se pudo obtener el TC de SUNAT', USREST::CODE_INTERNAL_SERVER_ERROR);
//                }
//            } else {
//                throw new Exception('No se puede obtener el TC de un mes futuro', USREST::CODE_INTERNAL_SERVER_ERROR);
//            }
//        } else {
//            throw new Exception('Debe especificar el año y/o el mes', USREST::CODE_INTERNAL_SERVER_ERROR);
//        }
//    }
//    /**
//     * Obtiene todos los tipos de cambio del periodo especificado, si no hay en un día tomará el del anterior
//     * @param integer $p_i_year Año
//     * @param integer $p_i_period Periodo
//     * @return array Array de datos
//     */
//    public static function getAllExchangeRate($p_i_year, $p_i_period) {
//
//        $p_i_year = (int) $p_i_year;
//        $p_i_period = (int) $p_i_period;
//
//        //Data normal
//        $nData = self::getExchangeRate($p_i_year, $p_i_period);
//
//        if ($nData) {
//            //Toda la data
//            $aData = [];
//
//            //Límite
//            if (SIANTime::getYear() == $p_i_year && SIANTime::getMonth() == $p_i_period) {
//                $limit = SIANTime::getDay();
//            } else {
//                $limit = USTime::getLastDayOfMonth($p_i_year, $p_i_period);
//            }
//            $j = 0;
//            for ($i = 1; $i <= $limit; $i++) {
//                //SIANFile::writeString($i . ' - ' . $nData[$j]['day'], $i);
//                if ($j < count($nData) && $i === $nData[$j]['day']) {
//                    $aData[] = $nData[$j];
//                    $j++;
//                } else {
//                    //Si no hay y es el primero
//                    if ($j === 0) {
//                        $last = self::getExchangeRateByDate("{$p_i_year}-{$p_i_period}-{$i}");
//                        $aData[] = array(
//                            'year' => $p_i_year,
//                            'period' => $p_i_period,
//                            'day' => $i,
//                            'date' => SIANTime::getDate($p_i_year, $p_i_period, $i),
//                            'purchase' => $last['purchase'],
//                            'sale' => $last['sale'],
//                        );
//                    }
//                    //Si no obtenemos el último
//                    else {
//                        $aData[] = array(
//                            'year' => $p_i_year,
//                            'period' => $p_i_period,
//                            'day' => $i,
//                            'date' => SIANTime::getDate($p_i_year, $p_i_period, $i),
//                            'purchase' => $nData[$j - 1]['purchase'],
//                            'sale' => $nData[$j - 1]['sale'],
//                        );
//                    }
//                }
//            }
//
//            return $aData;
//        } else {
//            return false;
//        }
//    }
//    /**
//     * Obtiene el tipo de cambio de una fecha indicada
//     * @param type $s_date Fecha
//     * @param type $b_reverse Indicador de que si la fecha está en formato amigable o no
//     * @return array Array con los campos del objecto ExchangeRate
//     */
//    public static function getExchangeRateByDate($s_date, $b_reverse = false) {
//
//        if ($b_reverse) {
//            $s_date = SIANTime::formatDate($s_date, $b_reverse);
//        }
//
//        $a_date = explode('-', $s_date);
//
//        $a_data = self::getExchangeRate($a_date[0], $a_date[1]);
//
//        //Si no es falso es porque sí se obtuvo el tipo de cambio
//        if ($a_data) {
//
//            for ($i = 0; $i < count($a_data); $i++) {
//
//                //Si la fecha es la misma...
//                if ($a_data[$i]['date'] == $s_date) {
//                    $a_data[$i]['is_temp'] = 0;
//                    return $a_data[$i];
//                } else {
//
//                    //Si no es la misma comparamos si el actual es mayor que el consultado
//                    if ((int) $a_data[$i]['day'] > (int) $a_date[2]) {
//                        //Si es así quiere decir que no hubo cambio el dia actual y hay que tomar el anterior
//                        if (isset($a_data[$i - 1])) {
//                            //Indicador del que tipo de cambio es del día anterior
//                            $a_data[$i - 1]['is_temp'] = 1;
//                            return $a_data[$i - 1];
//                        } else {
//                            //Si no hay el dia anterior este mes se obtiene el ultimo del mes anterior
//                            if ((int) $a_date[1] == 1) {
//                                return self::getLastExchangeRateOfMonth((int) $a_date[0] - 1, '12');
//                            } else {
//                                return self::getLastExchangeRateOfMonth($a_date[0], (int) $a_date[1] - 1);
//                            }
//                        }
//                    } else {
//                        //Si es el ultimo quiere decir que la fecha solicitada es mayor que la ultima obtenida
//                        if ($i == count($a_data) - 1) {
//                            $a_data[$i]['is_temp'] = 1;
//                            return $a_data[$i];
//                        }
//                    }
//                }
//            }
//
//            return false;
//        } else {
//            //Si no hay tipo de cambio probamos con el anterior mes
//            if ((int) $a_date[1] == 1) {
//                return self::getLastExchangeRateOfMonth((int) $a_date[0] - 1, '12');
//            } else {
//                return self::getLastExchangeRateOfMonth($a_date[0], (int) $a_date[1] - 1);
//            }
//        }
//    }
//    /**
//     * Obtiene el último tipo de cambio del año y periodo indicado
//     * @param type $year Año
//     * @param type $period Periodo o mes
//     * @return array Array con los campos del objecto ExchangeRate
//     */
//    public static function getLastExchangeRateOfMonth($year, $period) {
//        $data = self::getExchangeRate($year, $period);
//        //Indicador del que tipo de cambio es del día anterior
//        $count = count($data);
//        $data[$count - 1]['is_temp'] = 1;
//        return $data[$count - 1];
//    }

    protected static function _getExchangeRate($p_s_begin_date, $p_s_end_date, $p_s_format = USTime::SYSTEM_DATE_FORMAT) {
        $a_response = USMigo::getExchangeRateByRange($p_s_begin_date, $p_s_end_date, "DUGTbjslOBwTVtdz1ZGQlCwi3SqSqaYX0egcTmWhPdlR9sct1RM5pvpQ5ar7");

        $a_tcs = [];
        foreach ($a_response->data as $tc) {
            $a_tc = [];
            $a_tc['date'] = $tc->fecha;
            $a_tc['purchase'] = $tc->precio_compra;
            $a_tc['sale'] = $tc->precio_venta;
            $a_tcs [] = $a_tc;
        }
        return $a_tcs;
    }
}
