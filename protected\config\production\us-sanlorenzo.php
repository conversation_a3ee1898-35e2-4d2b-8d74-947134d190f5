<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'sanlorenzo.siansystem.com/admin';
$domain = 'https://sanlorenzo.siansystem.com';
$domain2 = 'http://sanlorenzo.siansystem.com';
$report_domain = 'rsanlorenzo.siansystem.com';
$org = 'sanlorenzo';
//SIAN 2
$api_sian = 'http://api-staging.siansystem.com/';
//database enterprise
$database_server = '161.132.48.88';
$database_name = 'sanlorenzo';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_EFACT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = '';
$e_billing_certificate_pass = '';//PIN
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '',//usuario
        'password' => ''
    ],
    YII_OSE_EFACT => [
        'username' => '',
        'password' => ''
    ]
];
$smtp_username = '<EMAIL>';
$smtp_password = 'V*4Y<5b%367S4hA=1';
$environment_reports = YII_ENVIRONMENT_PRODUCTION;
$environment = YII_ENVIRONMENT_PRODUCTION;
