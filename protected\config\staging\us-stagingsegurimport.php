<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'stagingsegurimport.siansystem.com/admin';
$domain = 'https://stagingsegurimport.siansystem.com';
$report_domain = 'rstaging.siansystem.com';
$org = 'segurimport';
$us = 'us_staging';

//SIAN 2
$domain2 = 'https://stagingsegurimport.siansystem.com';
$domain_react_app = 'http://stagingsegurimport2.siansystem.com';
$api_sian = 'http://api-staging.siansystem.com/';

$database_server = '161.132.48.88';
$database_name = 'segurimport_staging';
$database_username = 'sian_test';
$database_password = '75nppt6vr57lx4';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'C20042027757.pfx';
$e_billing_certificate_pass = '75ry0m2wm5sndg';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20477239901BILLING7',
        'password' => 'Billing7'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_STAGING;
$environment = YII_ENVIRONMENT_STAGING;
$admin_emails = array(
    'Segurimport SIAN' => '<EMAIL>',
);

