<?php

class SIANPaymentSchedule extends CWidget {

    const DATE_TYPE_EMISSION = 'emission';
    const DATE_TYPE_EXPIRATION = 'expiration';

    public $id;
    public $form;
    public $model;
    public $readonly = false;
    public $title = 'Movimientos';
    public $label = 'Movimiento';
    public $view = null;
    public $routes = [];
    public $fixed = 2;
    public $step = 0.01;
    public $sunat_code_items = [];
    public $scenario;
    //CONTROL
    private $controller;
    //
    private $schedule_sunat_code_id;
    private $schedule_document_id;
    private $schedule_identification_number_id;
    private $schedule_person_name_id;
    private $schedule_date_type_id;
    private $schedule_begin_date_id;
    private $schedule_end_date_id;
    private $search_button_id;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        //GRID ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //
        $this->schedule_sunat_code_id = $this->controller->getServerId();
        $this->schedule_document_id = $this->controller->getServerId();
        $this->schedule_identification_number_id = $this->controller->getServerId();
        $this->schedule_person_name_id = $this->controller->getServerId();
        $this->schedule_date_type_id = $this->controller->getServerId();
        $this->schedule_begin_date_id = $this->controller->getServerId();
        $this->schedule_end_date_id = $this->controller->getServerId();
        $this->search_button_id = $this->controller->getServerId();

        //MOVEMENT ITEMS
        $movementItems = [];
        foreach ($this->model->tempScheduledEntryGroups as $o_seg) {
            $attributes = [];
            $attributes['movement_parent_id'] = $o_seg->movement_parent_id;
            $attributes['parent_entry_id'] = $o_seg->parent_entry_id;
            $attributes['parent_account_code'] = $o_seg->parent_account_code;
            $attributes['parent_owner'] = $o_seg->parent_owner;
            $attributes['parent_owner_id'] = $o_seg->parent_owner_id;
            $attributes['parent_route'] = $o_seg->parent_route;
            $attributes['parent_aux_person_id'] = $o_seg->parent_aux_person_id;
            $attributes['parent_aux_person_name'] = $o_seg->parent_aux_person_name;
            $attributes['parent_emission_date'] = $o_seg->parent_emission_date;
            $attributes['parent_currency'] = $o_seg->parent_currency;
            $attributes['parent_exchange_rate'] = $o_seg->parent_exchange_rate;
            $attributes['balance_pen'] = round($o_seg->balance_pen, $this->fixed);
            $attributes['balance_usd'] = round($o_seg->balance_usd, $this->fixed);
            $attributes['pay_date'] = $o_seg->pay_date;
            $attributes['amount'] = round($o_seg->amount, $this->fixed);
            $attributes['errors'] = $o_seg->getErrors();

            $movementItems[] = $attributes;
        }

        //Registramos assets
        SIANAssets::registerScriptFile("other/jquery.datetimepicker/js/jquery.datetimepicker.js");
        SIANAssets::registerCssFile("other/jquery.datetimepicker/css/jquery.datetimepicker.css");
        SIANAssets::registerScriptFile('js/sian-payment-schedule.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        //ACCESSOS
        $('#{$this->id}').data('url', '{$this->controller->createUrl('/widget/getAccountsToSchedule')}');
        //
        $('#{$this->id}').data('view-access', " . CJSON::encode($this->getAccess()) . ");
        //COUNT
        $('#{$this->id}').data('fixed', {$this->fixed});
        $('#{$this->id}').data('step', {$this->step});
        $('#{$this->id}').data('count', 0);
        //COUNT
        //EXCHANGE
        $('#{$this->id}').data('exchange_rate', '{$this->model->movement->exchange_rate}');
        //MONEDA
        $('#{$this->id}').data('currency', '{$this->model->movement->currency}');
        //Checks
        $('#{$this->id}').data('checks', " . CJSON::encode($movementItems) . ");
                
        $(document).ready(function() {     
            //Buscar
            {$this->id}Search();
            //CURRENCY SYMBOL
            $('span.currency-symbol').text('" . Currency::getSymbol($this->model->movement->currency) . "');
        });

        //CHANGE
        $('#{$this->id}').data('changeCurrency', function(currency){

            var table = $('#{$this->id}');
            var exchange_rate = table.data('exchange_rate');

            table.find('tr.sian-payment-schedule-item').each(function(index) {
                var item = $(this);
                var balance_pen = item.find('input.sian-payment-schedule-item-balance-pen').floatVal({$this->fixed});
                var balance_usd = item.find('input.sian-payment-schedule-item-balance-usd').floatVal({$this->fixed});

                //Montos
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-payment-schedule-item-amount').toPen(exchange_rate, {$this->fixed}, {max: balance_pen});
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-payment-schedule-item-amount').toUsd(exchange_rate, {$this->fixed}, {max: balance_usd}); 
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
                
                //Balances
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-payment-schedule-item-balance-pen').show();
                        item.find('input.sian-payment-schedule-item-balance-usd').hide();
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-payment-schedule-item-balance-pen').hide();
                        item.find('input.sian-payment-schedule-item-balance-usd').show();
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
            });

            table.data('currency', currency);
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            //Actualizamos los montos
            SIANPaymentScheduleCalculate('{$this->id}');
        });
        
        //CHANGE 
        $('#{$this->id}').data('changeExchange', function(exchange_rate){

            var table = $('table#{$this->id}');
                
            //Seteamos
            table.data('exchange_rate', exchange_rate); 
            
            //Obtenemos
            var currency = table.data('currency');
            //MONTOS
            table.find('tr.sian-payment-schedule-item').each(function(index) {
                var item = $(this);

                var item_currency = item.find('input.sian-payment-schedule-item-currency').val();
                var balance_pen = item.find('input.sian-payment-schedule-item-balance-pen').floatVal({$this->fixed});
                var balance_usd = item.find('input.sian-payment-schedule-item-balance-usd').floatVal({$this->fixed});
                
                //Cambiamos los balances
                switch(item_currency)
                {
                    case '" . Currency::PEN . "':
                        balance_usd = balance_pen / exchange_rate;
                    break;
                    case '" . Currency::USD . "':
                        balance_pen = balance_usd * exchange_rate;
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
                
                //Actualizamos max
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-payment-schedule-item-amount').floatAttr('max', {$this->fixed}, balance_pen);
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-payment-schedule-item-amount').floatAttr('max', {$this->fixed}, balance_usd);
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
                
                //Seteamos balances
                item.find('input.sian-payment-schedule-item-balance-pen').floatVal({$this->fixed}, balance_pen);
                item.find('input.sian-payment-schedule-item-balance-usd').floatVal({$this->fixed}, balance_usd);

            });

            //Actualizamos los montos
            SIANPaymentScheduleCalculate('{$this->id}');
        });

        $('body').on('click', '#{$this->search_button_id}', function(e) {
            {$this->id}Search();
        });
        
        function {$this->id}Search()
        {
            var schedule_sunat_code = $('#{$this->schedule_sunat_code_id}').val();
            var schedule_document = $('#{$this->schedule_document_id}').val();
            var schedule_identification_number = $('#{$this->schedule_identification_number_id}').val();
            var schedule_person_name = $('#{$this->schedule_person_name_id}').val();
            var schedule_date_type = $('#{$this->schedule_date_type_id}').val();
            var schedule_begin_date = $('#{$this->schedule_begin_date_id}').val();
            var schedule_end_date = $('#{$this->schedule_end_date_id}').val();

            if (schedule_begin_date.length === 0 || schedule_end_date.length === 0)
            {
                bootbox.alert(us_message('Debe elegir un rango de fechas para evitar que la lista de movimientos sea muy larga', 'warning'));
                return;
            }            
            SIANPaymentScheduleSearch('{$this->id}', schedule_sunat_code, schedule_document, schedule_identification_number, schedule_person_name, schedule_date_type, schedule_begin_date, schedule_end_date);
            //FOCUS
            unfocusable();
        }
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->title,
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempScheduledEntryGroups') ? 'us-error' : ''
            )
        ));

        if (!$this->readonly) {

            echo "<div class='row'>";
            echo "<div class='col-lg-8 col-md-8 col-sm-8 col-xs-12'>";
            $this->renderColumn1();
            echo "</div>";
            echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
            $this->renderColumn2();
            echo "</div>";
            echo "</div>";

            echo "<hr>";
        }

        echo "<table id='{$this->id}' class='table table-condensed table-hover'>";
        echo "<thead>";
        echo "<tr>";
        echo "<th width='2%'>{$this->renderGlobalCheckBox()}</th>";
        echo "<th width='12%'>Documento</th>";
        echo "<th width='12%'>Detalle</th>";
        echo "<th width='23%'>{$this->model->movement->getAttributeLabel('links.movement.aux_person_id')}</th>";
        echo "<th width='7%'>{$this->model->movement->getAttributeLabel('links.emission_date')}</th>";
        echo "<th width='7%'>{$this->model->movement->getAttributeLabel('links.movement.expiration_date')}</th>";
        echo "<th width='10%'>Saldo <span class='currency-symbol'></span></th>";
        echo "<th width='8%'>F. Pago</th>";
        echo "<th width='10%'>Monto <span class='currency-symbol'></span></th>";
        echo "<th width='7%'>Restante <span class='currency-symbol'></span></th>";
        echo "<th width='2%'></th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody></tbody>";
        echo "<tfoot>";
        echo "<tr>";
        echo "<td colspan='8' style='text-align:right'><b>TOTAL <span class='currency-symbol'></span>:</b></td>";
        echo "<td style='text-align:right'><span class='sian-payment-schedule-total-amount'>0.00<span>";
        echo "</td>";
        echo "<td></td>";
        echo "</tr>";
        echo "</tfoot>";
        echo "</table>";

        echo "<div class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->textFieldRow($this->model->movement, 'observation', array(
            'placeholder' => 'Puede escribir una observación acerca del movimiento',
            'required' => $this->model->movement->isObservationRequired()
        ));
        echo "</div>";
        echo "</div>";
        $this->endWidget();
    }

    private function renderGlobalCheckBox() {
        return CHtml::checkBox("All", $this->getGlobalCheckBoxState(), array(
                    'class' => 'sian-payment-schedule-check',
                    'onchange' => "SIANPaymentScheduleGridCheck('{$this->id}', this.checked); SIANPaymentScheduleCalculate('{$this->id}');",
                    'readonly' => $this->readonly,
        ));
    }

    private function renderColumn1() {
        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-6'>";
        $this->widget('application.widgets.USSimpleSelect2', array(
            'id' => $this->schedule_sunat_code_id,
            'form' => $this->form,
            'model' => $this->model,
            'attribute' => 'schedule_sunat_code',
            'asDropDownList' => true,
            'items' => $this->sunat_code_items,
            'multiple' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-6'>";
        echo $this->form->textFieldRow($this->model, 'schedule_document', array(
            'id' => $this->schedule_document_id,
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-6'>";
        $this->widget('application.widgets.USSimpleSelect2', array(
            'id' => $this->schedule_identification_number_id,
            'form' => $this->form,
            'model' => $this->model,
            'attribute' => 'schedule_identification_number',
            'asDropDownList' => false,
            'tags' => [],
            'tokenSeparators' => array(',', ' '),
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-6'>";
        echo $this->form->textFieldRow($this->model, 'schedule_person_name', array(
            'id' => $this->schedule_person_name_id,
        ));
        echo "</div>";
        echo "</div>";
    }

    private function renderColumn2() {

        echo "<div class='row'>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-6'>";
        echo $this->form->dropDownListRow($this->model, 'schedule_date_type', [
            self::DATE_TYPE_EXPIRATION => 'F. Expiración',
            self::DATE_TYPE_EMISSION => 'F. Emisión',
                ], [
            'id' => $this->schedule_date_type_id,
        ]);
        echo "</div>";

        echo "<div class='col-lg-7 col-md-7 col-sm-7 col-xs-12'>";
        $this->widget('application.widgets.USDateRange', array(
            'begin_id' => $this->schedule_begin_date_id,
            'end_id' => $this->schedule_end_date_id,
            'form' => $this->form,
            'model' => $this->model,
            'begin_attribute' => 'schedule_begin_date',
            'end_attribute' => 'schedule_end_date',
            'grid' => true
        ));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6 text-center'>";
        echo CHtml:: label('Buscar', $this->search_button_id, array(
        ));
        echo "<br/>";
        $this->widget('application.widgets.USButtons', array(
            'buttons' => array(
                array(
                    'id' => $this->search_button_id,
                    'context' => 'primary',
                    'icon' => 'fa fa-lg fa-search white',
                    'size' => 'default',
                    'title' => 'Buscar'
                )
            )
        ));
        echo "</div>";

        echo "</div>";
    }

    private function getGlobalCheckBoxState() {
        return count($this->model->tempScheduledEntryGroups) > 0;
    }

    private function getAccess() {
        $routes = [];
        foreach ($this->routes as $route) {
            $routes[] = "/{$route}/view";
        }
        return $this->controller->getUrls($routes);
    }

}
