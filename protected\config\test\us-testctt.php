<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'testctt.siansystem.com/admin';
$domain = 'https://testctt.siansystem.com';
$report_domain = 'rtest.siansystem.com';
$api_sian = 'https://api-test.siansystem.com/';
$org = 'ctt';
$us = 'us_test';
$database_server = '161.132.48.88';
$database_name = 'ctt_test';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = 'ctt.p12';
$e_billing_certificate_pass = 'CTT22092023';
$e_billing_ri = '0620050000126';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20549149112CONTA123',
        'password' => 'contador123'
    ]
];
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_TESTING;
$environment = YII_ENVIRONMENT_TESTING;
