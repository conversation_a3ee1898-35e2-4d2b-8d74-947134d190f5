<?php

class CertController extends USBaseController {

    public function accessRules() {
        return array(
            array('allow', // allow authenticated user to perform 'create' and 'update' actions
                'actions' => array('signMessage'),
                'users' => array('*'),
            ),
            array('deny', // deny all users
                'users' => array('*'),
            ),
        );
    }

    public function actionSignMessage() {
// #########################################################
// #                     PHP Signing                       #
// #########################################################
// Sample key.  Replace with one used for CSR generation
        $s_key_filepath = Yii::app()->params['admin_dir'] . '/files/cert/key.pem';

        $req = $_GET['request']; //GET method
//$req = $_POST['request']; //POST method

        $privateKey = openssl_get_privatekey(file_get_contents($s_key_filepath));

        $signature = null;
        openssl_sign($req, $signature, $privateKey);

        if ($signature) {
            header("Content-type: text/plain");
            echo base64_encode($signature);
            exit(0);
        }

        echo '<h1>Error signing message</h1>';
        exit(1);
    }

}
