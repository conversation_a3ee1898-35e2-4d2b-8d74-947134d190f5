$('body').on('click', 'a.form, button.form', function (e) {

    var element = $(this);

    //Si está desactivado cortamos
    if (element.hasAttr('disabled'))
    {
        return false;
    } else // Si no lo está lo desactivamos para evitar doble click
    {
        element.prop('disabled', true);
    }
    waitingDialog.show(STRINGS_WAITING_MESSAGE);

    $.ajax({
        type: 'get',
        url: element.attr('url'),
        data: element.getDataParams(),
        beforeSend: function (xhr) {
            window.active_ajax++;
            //Ocultamos los tooltip
            $('div.ui-tooltip').remove();
        },
        success: function (data) {

            if ($('#' + data.modal_id).length > 0)
            {
                $('#' + data.modal_id).remove();
            }

            waitingDialog.hide();

            $('div.modal_dynamic').append(data.modal_html);
            $('#' + data.modal_id).empty().html(data.content_html).fadeIn(100);
            $('#' + data.modal_id).modal('show');
            //Reactivamos cuando aparece el modal
            element.prop('disabled', false);
            window.active_ajax--;
        },
        error: function (request, status, error) { // if error occured

            waitingDialog.hide();

            var s_message = processError(request.responseText);
            bootbox.alert(us_message(s_message, 'error'));

            const gridId = element.data('grid_id');
            if (gridId) {
                $.fn.yiiGridView.update(gridId);
            }
            //Reactivamos cuando aparece el modal
            element.prop('disabled', false);
            window.active_ajax--;
        },
        dataType: 'json'
    });

    return false;
});

function _confirmed(element, show_waiting_dialog)
{
    if (show_waiting_dialog)
    {
        waitingDialog.show(STRINGS_WAITING_MESSAGE);
    }

    var a_data = element.getDataParams();
    a_data['checked'] = 1;

    $.ajax({
        type: 'get',
        url: element.attr('url'),
        data: a_data,
        beforeSend: function (xhr) {
            window.active_ajax++;
            //Ocultamos los tooltip
            $('div.ui-tooltip').remove();
        },
        success: function (data) {
            waitingDialog.hide();

            if ((isset(a_data['reason']) && a_data['reason'] == 1) || (isset(a_data['who_confirm']) && a_data['who_confirm'] == 1))
            {
                if ($('#' + data.modal_id).length > 0)
                {
                    $('#' + data.modal_id).remove();
                }
                $('div.modal_dynamic').append(data.modal_html);
                $('#' + data.modal_id).empty().html(data.content_html).fadeIn(100);
                $('#' + data.modal_id).modal('show');
            } else
            {
                switch (data.type)
                {
                    case 'grid':
                        $.fn.yiiGridView.update(data.element_id);
                        break;
                    case 'jqGrid':
                        $('#' + data.element_id).trigger('reloadGrid');
                        break;
                    case 'select':
                        $('#' + data.element_id).html(data.content_html);
                        $('#' + data.element_id).change();
                        break;
                    case 'html':
                        $('#' + data.element_id).html(data.content_html);
                        break;
                    case 'message':
                        bootbox.alert(us_message(data.content_html, data.element_id));
                        break;
                    case 'callback':
                        eval(data.callback + ';');
                        break;
                    case 'redirect':
                        window.location = data.content_html;
                        break;
                    default:
                        $('#' + data.element_id).data(data.type)(data);
                        break;

                }

                if (typeof data.message !== 'undefined')
                {
                    bootbox.alert(us_message(data.message, typeof data.message_type !== 'undefined' ? data.message_type : 'success'));
                }
            }

            element.prop('disabled', false);
            window.active_ajax--;
        },
        error: function (request, status, error) { // if error occured

            waitingDialog.hide();

            var s_message = processError(request.responseText);
            bootbox.alert(us_message(s_message, 'error'));
            //Reactivamos cuando aparece el modal
            element.prop('disabled', false);
            window.active_ajax--;
        },
        dataType: 'json'
    });
}

$('body').on('click', 'a.confirm, button.confirm', function () {

    var element = $(this);

    if (element.hasAttr('disabled') == true)
    {
        return false;
    } else
    {
        element.prop('disabled', true);
    }

    var message = element.data('message') ? element.data('message') : '¿Está seguro que desea proceder con esta acción?';

    bootbox.confirm(us_message(message, 'question'),
            function (confirmed)
            {
                if (confirmed == true)
                {
                    waitingDialog.show(STRINGS_WAITING_MESSAGE);

                    $.ajax({
                        type: 'get',
                        url: element.attr('url'),
                        data: element.getDataParams(),
                        beforeSend: function (xhr) {
                            window.active_ajax++;
                        },
                        success: function (data) {

                            waitingDialog.hide();

                            switch (data.type)
                            {
                                case 'grid':
                                    $.fn.yiiGridView.update(data.element_id);
                                    break;
                                case 'jqGrid':
                                    $('#' + data.element_id).trigger('reloadGrid');
                                    break;
                                case 'select':
                                    $('#' + data.element_id).html(data.content_html);
                                    $('#' + data.element_id).change();
                                    break;
                                case 'html':
                                    $('#' + data.element_id).html(data.content_html);
                                    break;
                                case 'message':
                                    bootbox.alert(us_message(data.content_html, data.element_id));
                                    break;
                                case 'callback':
                                    eval(data.callback + ';');
                                    break;
                                case 'redirect':
                                    window.location = data.content_html;
                                    break;
                                default:
                                    $('#' + data.element_id).data(data.type)(data);
                                    break;

                            }

                            if (typeof data.message !== 'undefined')
                            {
                                bootbox.alert(us_message(data.message, typeof data.message_type !== 'undefined' ? data.message_type : 'success'));
                            }

                            element.prop('disabled', false);
                            window.active_ajax--;
                        },
                        error: function (request, status, error) {

                            waitingDialog.hide();

                            var s_message = processError(request.responseText);
                            bootbox.alert(us_message(s_message, 'error'));

                            element.prop('disabled', false);
                            window.active_ajax--;
                        },
                        dataType: 'json'
                    });
                } else
                {
                    element.prop('disabled', false);
                }
            }
    );

    return false;
});

$('body').on('click', 'a.prompt, button.prompt', function () {

    var element = $(this);

    if (element.hasAttr('disabled') == true)
    {
        return false;
    } else
    {
        element.prop('disabled', true);
    }

    //Obtienes la data
    var o_data = element.getDataParams();
    var o_options = element.data('options');

    var message = element.data('message') ? element.data('message') : 'Elija una opción para proceder:';

    var a_options = [];
    if (isset(o_options))
    {
        $.each(o_options, function (s_key, s_value) {
            a_options.push({
                value: s_key,
                text: s_value
            });
        });
        delete o_data.options;
    }

    if (a_options.length > 0)
    {
        bootbox.prompt({
            title: message,
            inputType: 'select',
            inputOptions: a_options,
            callback: function (result) {

                if (isset(result))
                {
                    o_data['selected_option'] = result;
                    waitingDialog.show(STRINGS_WAITING_MESSAGE);

                    $.ajax({
                        type: 'get',
                        url: element.attr('url'),
                        data: o_data,
                        beforeSend: function (xhr) {
                            window.active_ajax++;
                        },
                        success: function (data) {

                            waitingDialog.hide();

                            switch (data.type)
                            {
                                case 'grid':
                                    $.fn.yiiGridView.update(data.element_id);
                                    break;
                                case 'jqGrid':
                                    $('#' + data.element_id).trigger('reloadGrid');
                                    break;
                                case 'select':
                                    $('#' + data.element_id).html(data.content_html);
                                    $('#' + data.element_id).change();
                                    break;
                                case 'html':
                                    $('#' + data.element_id).html(data.content_html);
                                    break;
                                case 'message':
                                    bootbox.alert(us_message(data.content_html, data.element_id));
                                    break;
                                case 'callback':
                                    eval(data.callback + ';');
                                    break;
                                case 'redirect':
                                    window.location = data.content_html;
                                    break;
                                case 'newtab':
                                    window.open(data.content_html, data.element_id);
                                    break;
                                default:
                                    $('#' + data.element_id).data(data.type)(data);
                                    break;

                            }

                            if (typeof data.message !== 'undefined')
                            {
                                bootbox.alert(us_message(data.message, typeof data.message_type !== 'undefined' ? data.message_type : 'success'));
                            }

                            element.prop('disabled', false);

                            //Volvemos a setear opciones
                            o_data['options'] = o_options;

                            window.active_ajax--;
                        },
                        error: function (request, status, error) {

                            waitingDialog.hide();

                            var s_message = processError(request.responseText);
                            bootbox.alert(us_message(s_message, 'error'));

                            element.prop('disabled', false);

                            //Volvemos a setear opciones
                            o_data['options'] = o_options;

                            window.active_ajax--;
                        },
                        dataType: 'json'
                    });
                } else
                {
                    element.prop('disabled', false);
                }

            }
        });

    } else
    {

        //Volvemos a setear opciones
        o_data['options'] = o_options;

        bootbox.alert(us_message('No hay opciones disponibles', 'error'));
        element.prop('disabled', false);
    }


    return false;
});

$('body').on('click', 'a.ajax-check, button.ajax-check', function () {

    var element = $(this);

    if (element.hasAttr('disabled') == true)
    {
        return false;
    } else
    {
        element.prop('disabled', true);
    }

    waitingDialog.show(STRINGS_WAITING_MESSAGE);

    $.ajax({
        type: 'get',
        url: element.attr('url'),
        data: element.getDataParams(),
        beforeSend: function (xhr) {
            window.active_ajax++;
            //Ocultamos los tooltip
            $('div.ui-tooltip').remove();
        },
        success: function (data) {

            window.active_ajax--;

            if (data.show_confirm)
            {
                waitingDialog.hide();

                bootbox.confirm(us_message(data.confirm_message, 'question'),
                        function (confirmed)
                        {
                            if (confirmed == true)
                            {
                                _confirmed(element, true);
                            } else
                            {
                                element.prop('disabled', false);
                            }
                        }
                );
            } else
            {
                _confirmed(element, false);
            }
        },
        error: function (request, status, error) {

            waitingDialog.hide();

            var s_message = processError(request.responseText);
            bootbox.alert(us_message(s_message, 'error'));

            element.prop('disabled', false);
            window.active_ajax--;
        },
        dataType: 'json'
    });

    return false;
});

$('body').on('click', 'a.simple, button.simple', function () {

    var element = $(this);
    var html = element.html();

    //Si está desactivado cortamos
    if (element.hasAttr('disabled'))
    {
        return false;
    } else // Si no lo está lo desactivamos para evitar doble click
    {
        if (element.hasAttr('once'))
        {
            element.html('<span>Procesando...</span>');
        }

        element.prop('disabled', true);
    }

    waitingDialog.show(STRINGS_WAITING_MESSAGE);

    $.ajax({
        type: 'get',
        url: element.attr('url'),
        data: element.getDataParams(),
        beforeSend: function (xhr) {
            window.active_ajax++;
            //Ocultamos los tooltip
            $('div.ui-tooltip').remove();
        },
        success: function (data) {

            waitingDialog.hide();
            switch (data.type)
            {
                case 'grid':
                    $.fn.yiiGridView.update(data.element_id);
                    break;
                case 'jqGrid':
                    $('#' + data.element_id).trigger('reloadGrid');
                    break;
                case 'select':
                    $('#' + data.element_id).html(data.content_html);
                    $('#' + data.element_id).change();
                    break;
                case 'html':
                    $('#' + data.element_id).html(data.content_html);
                    break;
                case 'message':
                    bootbox.alert(us_message(data.content_html, data.element_id));
                    break;
                case 'callback':
                    eval(data.callback + ';');
                    break;
                case 'redirect':
                    window.location = data.content_html;
                    break;
                default:
                    $('#' + data.element_id).data(data.type)(data);
                    break;

            }

            if (typeof data.message !== 'undefined')
            {
                bootbox.alert(us_message(data.message, typeof data.message_type !== 'undefined' ? data.message_type : 'success'));
            }

            element.html(html);
            element.prop('disabled', false);

            window.active_ajax--;
        },
        error: function (request, status, error) {

            waitingDialog.hide();

            var s_message = processError(request.responseText);
            bootbox.alert(us_message(s_message, 'error'));

            element.prop('disabled', false);

            window.active_ajax--;
        },
        dataType: 'json'
    });


    return false;
});

$('body').on('submit', 'form.miniform', function (e) {

    var form = $(this);
    var element = form.find('button:submit');

    //Si está desactivado cortamos
    if (element.hasAttr('disabled'))
    {
        return false;
    }

    //Cambiamos label
    var buttonHtml = element.html();
    element.prop('disabled', true);
    element.html('<span>Procesando...</span>');

    if (!form[0].checkValidity())
    {
        e.preventDefault();

        form.find(':input:visible[required="required"]').each(function ()
        {
            if (!this.validity.valid)
            {
                $(this).focus();
                element.html(buttonHtml);
                return false;
            }
        });

    } else
    {
        waitingDialog.show(STRINGS_WAITING_MESSAGE);

        $.ajax({
            type: form.attr('method'),
            url: form.attr('action'),
            data: form.serialize(),
            beforeSend: function (xhr) {
                window.active_ajax++;
                //Ocultamos los tooltip
                $('div.ui-tooltip').remove();
            },
            success: function (data) {

                waitingDialog.hide();

                switch (data.type)
                {
                    case 'grid':
                        $.fn.yiiGridView.update(data.element_id);
                        break;
                    case 'jqGrid':
                        $('#' + data.element_id).trigger('reloadGrid');
                        break;
                    case 'select':
                        $('#' + data.element_id).html(data.content_html);
                        $('#' + data.element_id).change();
                        break;
                    case 'html':
                        $('#' + data.element_id).html(data.content_html);
                        break;
                    case 'message':
                        bootbox.alert(us_message(data.content_html, data.element_id));
                        break;
                    case 'callback':
                        eval(data.callback + ';');
                        break;
                    case 'redirect':
                        window.location = data.content_html;
                        break;
                    default:
                        $('#' + data.element_id).data(data.type)(data);
                        break;

                }

                if (typeof data.message !== 'undefined')
                {
                    bootbox.alert(us_message(data.message, typeof data.message_type !== 'undefined' ? data.message_type : 'success'));
                }

                element.html(buttonHtml);

                window.active_ajax--;
            },
            error: function (request, status, error) {

                waitingDialog.hide();

                var s_message = processError(request.responseText);
                bootbox.alert(us_message(s_message, 'error'));
                element.html(buttonHtml);

                window.active_ajax--;
            },
            dataType: 'json'
        });
    }

    return false;

});

$('body').on('click', 'button.multiboolean, a.multiboolean', function () {

    var element = $(this);

    if (element.hasAttr('disabled') == true)
    {
        return false;
    } else
    {
        element.prop('disabled', true);
    }

    var selection = $.fn.yiiGridView.getSelection(element.data('element_id'));

    var data = element.getDataParams();
    data.selection = selection;

    if (selection.length > 0)
    {
        waitingDialog.show(STRINGS_WAITING_MESSAGE);
        $.ajax({
            type: 'post',
            url: element.attr('url'),
            data: data,
            beforeSend: function (xhr) {
                window.active_ajax++;
                //Ocultamos los tooltip
                $('div.ui-tooltip').remove();
            },
            success: function (data) {
                waitingDialog.hide();
                $.fn.yiiGridView.update(element.data('element_id'));

                if (typeof data.message !== 'undefined')
                {
                    bootbox.alert(us_message(data.message, typeof data.message_type !== 'undefined' ? data.message_type : 'success'));
                }
                element.prop('disabled', false);
                window.active_ajax--;
            },
            error: function (request, status, error) {
                waitingDialog.hide();
                var s_message = processError(request.responseText);
                bootbox.alert(us_message(s_message, 'error'));
                element.prop('disabled', false);
                window.active_ajax--;
            },
            dataType: 'json'
        });
    } else
    {
        bootbox.alert(us_message('No ha seleccionado ninguna fila', 'warning'));
        element.prop('disabled', false);
    }

    return false;
});

$('body').on('click', 'button.get-multiboolean, a.get-multiboolean', function () {

    var element = $(this);

    if (element.hasAttr('disabled') == true)
    {
        return false;
    } else
    {
        element.prop('disabled', true);
    }
    var url = element.attr('url');
    var selection = $.fn.yiiGridView.getSelection(element.data('element_id'));
    var params = jQuery.param({selection: selection});

    if (params.length > 0) {
        url = url + '?' + params;
    }

    if (selection.length > 0)
    {
        $(window).attr('location', url);
    } else
    {
        bootbox.alert(us_message('No ha seleccionado ninguna fila', 'warning'));
        element.prop('disabled', false);
    }
    return false;
});

$('body').on('click', 'a.multidelete, button.multidelete', function () {
    var element = $(this);

    if (element.hasAttr('disabled') == true)
    {
        return false;
    } else
    {
        element.prop('disabled', true);
    }

    var selection = $.fn.yiiGridView.getSelection(element.data('element_id'));

    if (selection.length > 0)
    {
        var message = element.data('message') ? element.data('message') : '¿Está seguro que desea eliminar los ítems seleccionados?';

        bootbox.confirm(us_message(message, 'question'),
                function (confirmed)
                {
                    if (confirmed == true)
                    {
                        waitingDialog.show(STRINGS_WAITING_MESSAGE);

                        $.ajax({
                            type: 'post',
                            url: element.attr('url'),
                            data: {
                                selection: selection
                            },
                            beforeSend: function (xhr) {
                                window.active_ajax++;
                            },
                            success: function (data) {

                                waitingDialog.hide();

                                if (data.code == REST_CODE_SUCCESS) {
                                    $.fn.yiiGridView.update(element.data('element_id'));
                                }

                                //Mostramos mensaje si hubiera
                                if (isset(data.message_type) && isset(data.message)) {
                                    bootbox.alert(us_message(data.message, data.message_type));
                                }

                                element.prop('disabled', false);
                                window.active_ajax--;
                            },
                            error: function (request, status, error) {

                                waitingDialog.hide();

                                var s_message = processError(request.responseText);
                                bootbox.alert(us_message(s_message, 'error'));

                                element.prop('disabled', false);
                                window.active_ajax--;
                            },
                            dataType: 'json'
                        });
                    } else
                    {
                        element.prop('disabled', false);
                    }
                });
    } else
    {
        bootbox.alert(us_message('No ha seleccionado ninguna fila', 'warning'));
        element.prop('disabled', false);
    }

    return false;
});

$('body').on('click', 'a.multiform, button.multiform', function (e) {

    var element = $(this);

    //Si está desactivado cortamos
    if (element.hasAttr('disabled'))
    {
        return false;
    } else // Si no lo está lo desactivamos para evitar doble click
    {
        element.prop('disabled', true);
    }

    var selection = $.fn.yiiGridView.getSelection(element.data('element_id'));

    if (selection.length > 0)
    {
        var params = jQuery.param(element.getDataParams());
        var baseUrl = element.attr('url');

        waitingDialog.show(STRINGS_WAITING_MESSAGE);

        $.ajax({
            type: 'post',
            url: baseUrl + '?' + params,
            data: {
                selection: selection
            },
            beforeSend: function (xhr) {
                window.active_ajax++;
                //Ocultamos los tooltip
                $('div.ui-tooltip').remove();
            },
            success: function (data) {

                if ($('#' + data.modal_id).length)         // use this if you are using id to check
                {
                    $('#' + data.modal_id).remove();
                }

                waitingDialog.hide();

                $('.modal_dynamic').append(data.modal_html);
                $('#' + data.modal_id).empty().html(data.content_html).fadeIn(100);
                $('#' + data.modal_id).modal('show');
                //Reactivamos cuando aparece el modal
                element.prop('disabled', false);
                window.active_ajax--;
            },
            error: function (request, status, error) { // if error occured

                waitingDialog.hide();

                var s_message = processError(request.responseText);
                bootbox.alert(us_message(s_message, 'error'));
                //Reactivamos cuando aparece el modal
                element.prop('disabled', false);
                window.active_ajax--;
            },
            dataType: 'json'
        });
    } else
    {
        bootbox.alert(us_message('No ha seleccionado ninguna fila', 'warning'));
        element.prop('disabled', false);
    }

    return false;
});

$('body').on('click', 'a.toggle', function (e) {

    var element = $(this);

    //Si está desactivado cortamos
    if (element.hasAttr('disabled'))
    {
        return false;
    } else // Si no lo está lo desactivamos para evitar doble click
    {
        element.prop('disabled', true);
    }

    $.ajax({
        type: 'get',
        url: element.attr('url'),
        data: element.getDataParams(),
        beforeSend: function (xhr) {
            window.active_ajax++;
            //Ocultamos los tooltip
            $('div.ui-tooltip').remove();
        },
        success: function (data) {

            switch (data.type)
            {
                case 'grid':
                    $.fn.yiiGridView.update(data.element_id);
                    break;
                case 'select':
                    $('#' + data.element_id).html(data.element_html);
                    $('#' + data.element_id).change();
                    break;
                case 'div':
                    $('#' + data.element_id).html(data.element_html);
                    break;

            }

            element.prop('disabled', false);
            window.active_ajax--;
        },
        error: function (request, status, error) {
            bootbox.alert(us_message(request.responseText, 'error'));

            element.prop('disabled', false);
            window.active_ajax--;
        },
        dataType: 'json'
    });

    return false;
});