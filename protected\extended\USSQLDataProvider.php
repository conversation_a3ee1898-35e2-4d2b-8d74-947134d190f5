<?php

class USSQLDataProvider extends CSqlDataProvider {

    //En caso de setear un modelo para poder etiquetar las columnas
    public $model = null;
    public $fetchMode = PDO::FETCH_OBJ;
    public $afterFind = null;

    protected function fetchData() {

        if (!($this->sql instanceof CDbCommand)) {
            $db = $this->db === null ? Yii::app()->db : $this->db;
            $command = $db->createCommand($this->sql);
        } else
            $command = clone $this->sql;

        if (($sort = $this->getSort()) !== false) {
            $raw_order = $sort->getOrderBy();
            //Normalizar 
            if (!empty($raw_order)) {

                $order = $this->normalizeOrderBy($raw_order);

                if (preg_match('/\s+order\s+by\s+[\w\s,\.]+$/i', $command->text))
                    $command->text .= ', ' . $order;
                else
                    $command->text .= ' ORDER BY ' . $order;
            }
            else {
                $command->text .= ' ORDER BY NULL';
            }
        }

        if (($pagination = $this->getPagination()) !== false) {
            $pagination->setItemCount($this->getTotalItemCount());
            $limit = $pagination->getLimit();
            $offset = $pagination->getOffset();
            $command->text = $command->getConnection()->getCommandBuilder()->applyLimit($command->text, $limit, $offset);
        }

        foreach ($this->params as $name => $value)
            $command->bindValue($name, $value);

        $a_items = $command->setFetchMode($this->fetchMode)->queryAll();

        if (isset($this->afterFind)) {
            $a_models = [];
            foreach ($a_items as $a_item) {
                $a_models[] = $this->afterFind($a_item);
            }
            return $a_models;
        } else {
            return $a_items;
        }
    }

    private function normalizeOrderBy($p_s_order) {

        if (strlen(trim($p_s_order)) > 0) {
            //Obtenemos partes
            $a_parts = [];
            foreach (explode(',', trim($p_s_order)) as $s_part) {

                $a_sub_parts = explode(" ", trim($s_part), 2);

                $s_column = str_replace('`', '', $a_sub_parts[0]);

                if (isset($a_sub_parts[1])) {
                    $s_direction = $a_sub_parts[1];
                } else {
                    $s_direction = 'ASC';
                }

                $a_parts[] = "`{$s_column}` {$s_direction}";
            }

            return implode(", ", $a_parts);
        } else {
            return $p_s_order;
        }
    }

}
