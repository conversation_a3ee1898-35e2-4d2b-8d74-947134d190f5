<?php

class SIANCommercialSelectGrid extends CWidget {

    public $id;
    public $group_id;
    public $product_list_id;
    public $parent_autocomplete_id;
    public $parent_value_id;
    public $bussiness_partner_autocomplete_id;
    public $bussiness_partner_value_id;
    public $bussiness_partner_aux_id;
    public $bussiness_partner_search_id;

    public $form;
    public $model;
    public $readonly = false;
    public $advanced = false;
    public $as_bill_checkbox_id;
    public $force_igv_checkbox_id;
    public $allow_duplicate_checkbox_id;
    public $retention_checkbox_id;
    public $lock_discount = true;
    public $ifixed = CommercialMovement::IFIXED;
    public $tfixed = CommercialMovement::TFIXED;
    public $istep = CommercialMovement::ISTEP;
    public $tstep = CommercialMovement::TSTEP;
    public $astep = CommercialMovement::ASTEP;
    public $tolerance = CommercialMovement::TOLERANCE;
    public $allow_groups = false;
    public $is_template = false;
    public $only_allowed_divisions = false;
    public $main_title = 'Mercaderías, servicios y combos (Listado principal)';
    public $context = 'success';
    public $onChangeCCDependence = ''; //Para hacer un puente
    public $viewCosts = false;
    public $price_type_items = [];
    public $hide_panel = false;
    public $currency_value_id ;
    //PRIVATE
    private $controller;
    private $presentationMode;
    private $presentationUrl;
    private $presentationItems = [];
    private $comboItems = [];
    private $promotionGiftOptions = [];
    private $include_igv_checkbox_id;
    private $round_mode_select_id;
    private $autocomplete_id;
    private $item_aux_id;
    private $item_value_id;
    private $item_text_id;
    private $item_item_type_id;
    private $item_product_type_id;
    private $item_expiration_id;
    private $item_pres_quantity_id;
    private $item_equivalence_id;
    private $item_warehouse_id;
    private $item_allow_decimals_id;
    private $item_unit_stock_id;
    private $item_mixed_stock_label_id;
    private $item_skip_cost_validation_id;
    private $item_min_price_pen_id;
    private $item_min_price_usd_id;
    private $item_avg_price_pen_id;
    private $item_avg_price_usd_id;
    private $item_web_price_pen_id;
    private $item_web_price_usd_id;
    private $item_igv_affection_id;
    private $item_price_id;
    private $item_discount_id;
    private $item_promotion_item_id;
    private $item_perception_id;
    private $add_button_id;
    private $batch_add_button_id;
    private $summary_id;
    private $sales_specifications_input_id;
    private $observation_input_id;
    private $retention_percent_field_id;
    private $ret_field_id;
    private $no_ret_field_id;
    private $warehouse_input_id;
    private $cost_link_id;
    private $preview_access;
    private $stockUrl;
    private $commercialStockUrl;
    private $explainCommercialStockUrl;
    private $comboItemsUrl;
    private $promotionGiftsUrl;
    private $promotionGiftOptionsUrl;
    private $routesPurchaseValidate = ["logistic/purchaseOrder"];
    private $routesValidate = ["commercial/saleOrder", "commercial/saleQuote", "support/serviceOrder"];
    private $is_commercial_route;
    private $dispatch_multiple_origin;
    private $business_line_type;
    private $route;
    //Totales
    private $summary_crude_input_id;
    private $summary_affected1_input_id;
    private $summary_affected_input_id;
    private $summary_inaffected_input_id;
    private $summary_nobill_input_id;
    private $summary_export_input_id;
    private $summary_free_input_id;
    private $summary_nnet_input_id;
    private $summary_fnet_input_id;
    private $summary_net_input_id;
    private $summary_nigv_input_id;
    private $summary_figv_input_id;
    private $summary_igv_input_id;
    private $summary_ntotal_input_id;
    private $summary_ftotal_input_id;
    private $summary_total_input_id;
    private $summary_perception_input_id;
    private $summary_nreal_input_id;
    private $summary_freal_input_id;
    public $summary_real_input_id;
    private $summary_is_editable;
    private $summary_advance_percentage_input_id;
    private $summary_advance_amount_input_id;
    private $summary_advance_balance_input_id;
    private $batch_autocomplete_id;
    private $batch_item_aux_id;
    private $batch_item_value_id;
    private $batch_item_text_id;
    private $batch_loader_id;
    private $total_children_id;
    private $pen_symbol = Currency::PEN_SYMBOL;
    private $usd_symbol = Currency::USD_SYMBOL;


    public function init() {

        $this->controller = Yii::app()->controller;
        //
        $this->presentationMode = SpGetProductPresentations::MODE_COMBOBOX_WITH_PRICES;
        $this->presentationUrl = $this->controller->createUrl("/movement/getPresentations");
        $this->is_commercial_route = $this->model->movement->scenario->isCommercialRoute();
        //GRID ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->group_id = isset($this->group_id) ? $this->group_id : $this->controller->getServerId();
        $this->bussiness_partner_autocomplete_id = isset($this->bussiness_partner_autocomplete_id) ? $this->bussiness_partner_autocomplete_id : $this->controller->getServerId();
        $this->parent_autocomplete_id = isset($this->parent_autocomplete_id) ? $this->parent_autocomplete_id : $this->controller->getServerId();
        $this->currency_value_id = isset($this->currency_value_id) ? $this->currency_value_id : $this->controller->getServerId();
        $this->parent_value_id = isset($this->parent_value_id) ? $this->parent_value_id : $this->controller->getServerId();
        $this->bussiness_partner_value_id = isset($this->bussiness_partner_value_id) ? $this->bussiness_partner_value_id : $this->controller->getServerId();
        $this->bussiness_partner_aux_id = isset($this->bussiness_partner_aux_id) ? $this->bussiness_partner_aux_id : $this->controller->getServerId();

        $this->bussiness_partner_search_id = isset($this->bussiness_partner_search_id) ? $this->bussiness_partner_search_id : $this->controller->getServerId();


        $this->as_bill_checkbox_id = Yii::app()->controller->getServerId();
        $this->force_igv_checkbox_id = Yii::app()->controller->getServerId();
        $this->allow_duplicate_checkbox_id = Yii::app()->controller->getServerId();
        //PRIVATE
        $this->include_igv_checkbox_id = $this->controller->getServerId();
        $this->retention_checkbox_id = Yii::app()->controller->getServerId();
        $this->round_mode_select_id = $this->controller->getServerId();
        $this->autocomplete_id = $this->controller->getServerId();
        $this->item_aux_id = $this->controller->getServerId();
        $this->item_value_id = $this->controller->getServerId();
        $this->item_text_id = $this->controller->getServerId();
        $this->item_item_type_id = $this->controller->getServerId();
        $this->item_product_type_id = $this->controller->getServerId();
        $this->item_pres_quantity_id = $this->controller->getServerId();
        $this->item_equivalence_id = $this->controller->getServerId();
        $this->item_warehouse_id = $this->controller->getServerId();
        $this->item_unit_stock_id = $this->controller->getServerId();
        $this->item_mixed_stock_label_id = $this->controller->getServerId();
        $this->item_allow_decimals_id = $this->controller->getServerId();
        $this->item_skip_cost_validation_id = $this->controller->getServerId();
        $this->item_min_price_pen_id = $this->controller->getServerId();
        $this->item_min_price_usd_id = $this->controller->getServerId();
        $this->item_avg_price_pen_id = $this->controller->getServerId();
        $this->item_avg_price_usd_id = $this->controller->getServerId();
        $this->item_web_price_pen_id = $this->controller->getServerId();
        $this->item_web_price_usd_id = $this->controller->getServerId();
        $this->item_igv_affection_id = $this->controller->getServerId();
        $this->item_price_id = $this->controller->getServerId();
        $this->item_discount_id = $this->controller->getServerId();
        $this->item_promotion_item_id = $this->controller->getServerId();
        $this->item_perception_id = $this->controller->getServerId();
        $this->item_expiration_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();
        $this->batch_add_button_id = $this->controller->getServerId();
        $this->observation_input_id = $this->controller->getServerId();
        $this->retention_percent_field_id = Yii::app()->controller->getServerId();
        $this->ret_field_id = Yii::app()->controller->getServerId();
        $this->no_ret_field_id = Yii::app()->controller->getServerId();
        $this->warehouse_input_id = $this->controller->getServerId();
        $this->cost_link_id = $this->controller->getServerId();
        //DIVS
        $this->summary_id = $this->controller->getServerId();
        //
        $this->summary_crude_input_id = $this->controller->getServerId();
        $this->summary_affected1_input_id = $this->controller->getServerId();
        $this->summary_affected_input_id = $this->controller->getServerId();
        $this->summary_inaffected_input_id = $this->controller->getServerId();
        $this->summary_nobill_input_id = $this->controller->getServerId();
        $this->summary_export_input_id = $this->controller->getServerId();
        $this->summary_free_input_id = $this->controller->getServerId();
        $this->summary_nnet_input_id = $this->controller->getServerId();
        $this->summary_fnet_input_id = $this->controller->getServerId();
        $this->summary_net_input_id = $this->controller->getServerId();
        $this->summary_nigv_input_id = $this->controller->getServerId();
        $this->summary_figv_input_id = $this->controller->getServerId();
        $this->summary_igv_input_id = $this->controller->getServerId();
        $this->summary_ntotal_input_id = $this->controller->getServerId();
        $this->summary_ftotal_input_id = $this->controller->getServerId();
        $this->summary_total_input_id = $this->controller->getServerId();
        $this->summary_perception_input_id = $this->controller->getServerId();
        $this->summary_nreal_input_id = $this->controller->getServerId();
        $this->summary_freal_input_id = $this->controller->getServerId();
        $this->summary_advance_percentage_input_id = $this->controller->getServerId();
        $this->summary_advance_amount_input_id = $this->controller->getServerId();
        $this->summary_advance_balance_input_id = $this->controller->getServerId();
        $this->summary_real_input_id = isset($this->summary_real_input_id) ? $this->summary_real_input_id : $this->controller->getServerId();
        $this->summary_is_editable = in_array($this->model->movement->route, ['logistic/purchaseOrder']);
        $this->model->totals_editable = $this->summary_is_editable ? 1 : 0;
        $this->dispatch_multiple_origin = $this->controller->getOrganization()->globalVar->allow_dispatch_multi_origin && $this->model->movement->route == 'commercial/saleOrder' ? 1 : 0;
        $this->business_line_type = Yii::app()->controller->getOrganization()->globalVar->business_line_type == GlobalVar::BUSINESS_LINE_FOOTWEAR;

        $this->batch_autocomplete_id = $this->controller->getServerId();
        $this->batch_item_aux_id = $this->controller->getServerId();
        $this->batch_item_value_id = $this->controller->getServerId();
        $this->batch_item_text_id = $this->controller->getServerId();
        $this->batch_loader_id = $this->controller->getServerId();
        $this->total_children_id = $this->controller->getServerId();

        $this->route = Yii::app()->getController()->getRoute();

        //
        $a_product_ids = [];
        $a_combo_ids = [];
        $a_promotion_gift_option_ids = [];
        $a_instance_ids = [];

        $total_affected1 = 0;
        $total_inaffected = 0;
        $total_nobill = 0;
        $total_igv = 0;
        $total_perception = 0;


        //Datos comunes
        $i_store_id = $this->is_commercial_route == 1 && $this->is_template == 0 ? $this->model->movement->store_id : null;
        $s_emission_date = $this->is_commercial_route == 1 && $this->is_template == 0 ? $this->model->movement->emission_date : null;
        $i_exclude_id = $this->is_commercial_route == 1 && $this->is_template == 0 ? $this->model->movement->kardex_unlock_exclude_id : null;
        //Si hay productos...
        if (count($a_product_ids) > 0) {
            $this->presentationItems = SpGetProductPresentations::getAssociative($this->presentationMode, array_unique($a_product_ids), $this->controller->getOrganization()->globalVar->display_currency, 0, $i_store_id, $s_emission_date, $i_exclude_id);
        }
        if (count($a_combo_ids) > 0) {
            $this->comboItems = DpComboItems::getAssociative(array_unique($a_combo_ids));
        }
        if (count($a_promotion_gift_option_ids) > 0) {
            $this->promotionGiftOptions = SpGetPromotionGiftOptions::getAssociative(SpGetPromotionGiftOptions::MODE_PROMOTION_GIFT_OPTION_WIDGET, array_unique($a_promotion_gift_option_ids), $this->controller->getOrganization()->globalVar->display_currency, $i_store_id, $s_emission_date, $i_exclude_id);
        }
        //PRODUCT ITEMS
        $a_items = [];


        $this->model->totals_editable = in_array($this->model->movement->route, ['logistic/purchaseOrder']) ? 1 : 0;
        //URL
        $this->stockUrl = $this->controller->createUrl("/movement/loadCostsAndStocks");
        $this->commercialStockUrl = $this->controller->createUrl("/movement/loadCommercialStocks");
        $this->explainCommercialStockUrl = $this->controller->createUrl("/logistic/merchandise/explainCommercialStock");
        $this->comboItemsUrl = $this->controller->createUrl('/widget/getComboItems');
        $this->promotionGiftsUrl = $this->controller->createUrl('/widget/getPromotionGifts');
        $this->promotionGiftOptionsUrl = $this->controller->createUrl('/widget/getPromotionGiftOptions');
        //ACCESS
        $this->preview_access = $this->controller->checkRoute('/logistic/product/preview');
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-commercial-select-grid.js');
        SIANAssets::registerCssFile('css/sian-commercial-select-grid.css');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        function initDiv(id) {
            var divObj = $('#' + id);
            divObj.data('view-access', " . json_encode($this->preview_access) . ");//ACCESS
            divObj.data('view-url', '{$this->controller->createUrl('/logistic/product/preview')}');//URL
            divObj.data('include_igv',1);
            divObj.data('as_bill', 1);
            divObj.data('force_igv', $('#{$this->force_igv_checkbox_id}').prop('checked') ? 1 : 0);
            divObj.data('allow_duplicate', 1);
            divObj.data('is_commercial_route', " . CJSON::encode($this->is_commercial_route) . ");
            divObj.data('is_template', " . CJSON::encode($this->is_template) . ");
            divObj.data('scope', '{$this->model->movement->scope}');
            divObj.data('from_exchange', '{$this->model->movement->from_exchange}');
            divObj.data('is_buy', " . CJSON::encode($this->model->isBuy()) . ");
            divObj.data('is_purchase_order', true);
            divObj.data('summary_is_editable', " . CJSON::encode($this->summary_is_editable) . ");
            divObj.data('route', '{$this->model->movement->route}');
            divObj.data('igv_affection_map', " . CJSON::encode(CommercialMovementProduct::getIGVAffectionMap()) . ");
            divObj.data('igv_affection_order', " . CJSON::encode(CommercialMovementProduct::getIGVAffectionOrder()) . ");
            divObj.data('currency', '{$this->model->movement->currency}');
            divObj.data('currency_symbol', '" . Currency::getSymbol($this->model->movement->currency) . "');
            divObj.data('exchange_rate', {$this->model->movement->exchange_rate});
            divObj.data('round_mode', {$this->model->round_mode});
            divObj.data('ifixed', {$this->ifixed});
            divObj.data('tfixed', {$this->tfixed});
            divObj.data('istep', {$this->istep});
            divObj.data('astep', {$this->astep});
            divObj.data('tolerance', {$this->tolerance});
            divObj.data('no_detraction_max_amount_pen', {$this->controller->getOrganization()->globalVar->no_detraction_max_amount_pen});
            divObj.data('no_detraction_max_amount_usd', {$this->controller->getOrganization()->globalVar->no_detraction_max_amount_usd});
            divObj.data('variation_purchase_cost_percentage', {$this->controller->getOrganization()->globalVar->variation_purchase_cost_percentage});
            divObj.data('readonly', " . CJSON::encode($this->readonly) . ");
            divObj.data('lock_discount', " . CJSON::encode($this->lock_discount) . ");
            divObj.data('use_inaffected', " . CJSON::encode($this->model->use_inaffected) . ");
            divObj.data('use_free', " . CJSON::encode($this->model->use_free) . ");
            divObj.data('advanced', " . CJSON::encode($this->advanced) . ");
            divObj.data('allow_groups', " . CJSON::encode($this->allow_groups) . ");
            divObj.data('igv_global', {$this->controller->getOrganization()->globalVar->igv});
            divObj.data('perception_global', {$this->controller->getOrganization()->globalVar->perception});
            divObj.data('display_currency_global', '{$this->controller->getOrganization()->globalVar->display_currency}');
            divObj.data('price_mode', '{$this->controller->getOrganization()->globalVar->price_mode}');               
            divObj.data('retention4', {$this->controller->getOrganization()->globalVar->retention4});
            divObj.data('main_title', '{$this->main_title}');
            divObj.data('context', '{$this->context}');
            divObj.data('igv_affection_id', '{$this->item_igv_affection_id}');
            divObj.data('summary_id', '{$this->summary_id}');
            divObj.data('autocomplete_id', '{$this->autocomplete_id}');
            divObj.data('allow_duplicate_checkbox_id', '{$this->allow_duplicate_checkbox_id}');
            divObj.data('submit_id', '{$this->form->submit_id}');
            divObj.data('default_merchandise_measure', '{$this->controller->getDefaultMerchandiseMeasure()->abbreviation}');
            divObj.data('validate_stock', " . CJSON::encode($this->model->movement->validate_stock == 1) . ");
            divObj.data('presentationMode', '{$this->presentationMode}');
            divObj.data('presentationUrl', '{$this->presentationUrl}');
            divObj.data('stockUrl', '{$this->stockUrl}');
            divObj.data('commercialStockUrl', '{$this->commercialStockUrl}');
            divObj.data('explainCommercialStockUrl', '{$this->explainCommercialStockUrl}');
            divObj.data('comboItemsUrl', '{$this->comboItemsUrl}');
            divObj.data('promotionGiftsUrl', '{$this->promotionGiftsUrl}');
            divObj.data('promotionGiftOptionsUrl', '{$this->promotionGiftOptionsUrl}');
            divObj.data('stock_mode', " . CJSON::encode($this->model->movement->scenario->stock_mode) . ");
            divObj.data('exclude_id', " . CJSON::encode($this->model->movement->kardex_unlock_exclude_id) . ");
            divObj.data('a_product_type_to_validate_prices'," . CJSON::encode($this->controller->getOrganization()->globalVar->a_product_type_to_validate_prices) . ");
            divObj.data('routesValidate'," . CJSON::encode($this->routesValidate) . "); 
            divObj.data('routesPurchaseValidate'," . CJSON::encode($this->routesPurchaseValidate) . ");
            divObj.data('acceptance_message', '" . Combo::ACCEPTANCE_MESSAGE . "');
            divObj.data('independent_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INDEPENDENT . "');
            divObj.data('inherit_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INHERIT . "');
            divObj.data('parent_type', null);
            divObj.data('count', 0);//COUNT
            divObj.data('show_description', " . CJSON::encode((Yii::app()->controller->getOrganization()->globalVar->business_line_type == GlobalVar::BUSINESS_LINE_RESTAURANT && $this->model->movement->route === 'commercial/saleOrder')) . ");
            divObj.data('default_price','" . Yii::app()->controller->getOrganization()->globalVar->default_price . "');
            divObj.data('business_line_type','" . Yii::app()->controller->getOrganization()->globalVar->business_line_type . "');
            divObj.data('direction', '{$this->model->movement->direction}');
            divObj.data('dispatch_multiple_origin', {$this->dispatch_multiple_origin});
            divObj.data('modifiers', " . CJSON::encode(Document::getModifiers()) . ");
            divObj.data('price_type_items', " . CJSON::encode($this->price_type_items) . ");
            " . (in_array($this->model->movement->route, ['logistic/purchaseOrder']) ? "divObj.data('set_summary_total', 0);" : "divObj.data('set_summary_total', 1);") . " 
            divObj.data('oc_div_id', '{$this->id}');
            divObj.data('group_div_id', '{$this->group_id}');
            divObj.data('igv_list', " . CJSON::encode(GlobalVar::getIgvList()) . ");
            return divObj;
        }

        function addItemsToCommercialGrid(divId, main_table_id, items) {
            if (!Array.isArray(items) || items.length === 0) return;
            items.forEach(item => {
                switch (item.product_type) {
                    case '" . Product::TYPE_COMBO ."':
                        console.log(`*COMBO* => case:` + item.product_type);
                        const comboSubtableId = SIANCommercialSelectGridAddCombo(
                            divId,
                            item.product_id,
                            item.item_type_id,
                            item.product_name,
                            item.pres_quantity,
                            item.presentationItems,
                            item.equivalence,
                            item.igv_affection,
                            item.price,
                            item.discount,
                            item.description,
                            item.is_locked,
                            item.movement_id,
                            item.route,
                            item.document,
                            item.include_igv,
                            item.errors
                        );

                        item.subitems.forEach(subitem => {

                        const newSubitem = {
                            'giftOfInstance': subitem.gift_of_instance,
                            'productId': subitem.product_id,
                            'itemTypeId': subitem.item_type_id,
                            'productType': subitem.product_type,
                            'productName': subitem.product_name,
                            'unitStock': subitem.unit_stock,
                            'mixedStock': subitem.mixed_stock,
                            'presQuantity': subitem.pres_quantity,
                            'presentationItems': subitem.presentationItems,
                            'equivalence': subitem.equivalence,
                            'warehouseId': item.warehouse_id,
                            'allowDecimals': subitem.allow_decimals,
                            'skipCostValidation': subitem.skip_cost_validation,
                            'minPricePen': subitem.min_price_pen,
                            'minPriceUsd': subitem.min_price_usd,
                            'avgPricePen': subitem.avg_price_pen,
                            'avgPriceUsd': subitem.avg_price_usd,
                            'webPricePen': subitem.web_price_pen,
                            'webPriceUsd': subitem.web_price_usd,
                            'price': subitem.price,
                            'discount': subitem.discount,
                            'description': subitem.description ? subitem.description : '',
                            'promotionItemId': subitem.promotion_item_id,
                            'promotionGiftOptionId': subitem.promotion_gift_option_id,
                            'igvAffection': subitem.igv_affection,
                            'priceType': '',
                            'perceptionAffected': subitem.perception_affected,
                            'expiration': subitem.expiration,
                            'isLocked': subitem.is_locked,
                            'parentType': '" . Product::TYPE_COMBO. "',
                            'parentRoute': subitem.parent_route,
                            'quantityItemId': subitem.parent_route,
                            'totalItemId': subitem.quantity_item_id,
                            'giftCost': 0,
                            'movement_id' : item.movement_id, 
                            'movement_route' : item.route,
                            'movement_document' : item.document, 
                            'include_igv' : item.include_igv,
                            'igvPercentage' : subitem.igv_percentage,
                            'errors': item.errors
                        }
                            SIANCommercialSelectGridAddItem(
                                divId,
                                comboSubtableId,
                                subitem.instance_id,
                                newSubitem
                            );
                        });
                        break;

                    case '" . Product::TYPE_GROUP . "':
                        console.log(`*GROUP* => case:` + item.product_type + ' ' + item.price_type);
                        const groupSubtableId = SIANCommercialSelectGridAddGroup(
                            divId,
                            item.product_name,
                            item.pres_quantity,
                            item.equivalence,
                            item.igv_affection,
                            item.min_price_pen,
                            item.min_price_usd,
                            item.avg_price_pen,
                            item.avg_price_usd,
                            item.web_price_pen,
                            item.web_price_usd,
                            item.price,
                            item.discount,
                            item.description,
                            item.is_locked,
                            item.price_type,
                            item.movement_id,
                            item.route,
                            item.document,
                            item.include_igv,
                            item.errors
                        );

                        item.subitems.forEach(subitem => {

                            const newSubitem = {
                                'giftOfInstance': subitem.gift_of_instance,
                                'productId': subitem.product_id,
                                'itemTypeId': subitem.item_type_id,
                                'productType': subitem.product_type,
                                'productName': subitem.product_name,
                                'unitStock': subitem.unit_stock,
                                'mixedStock': subitem.mixed_stock,
                                'presQuantity': subitem.pres_quantity,
                                'presentationItems': subitem.presentationItems,
                                'equivalence': subitem.equivalence,
                                'warehouseId': item.warehouse_id,
                                'allowDecimals': subitem.allow_decimals,
                                'skipCostValidation': subitem.skip_cost_validation,
                                'minPricePen': subitem.min_price_pen,
                                'minPriceUsd': subitem.min_price_usd,
                                'avgPricePen': subitem.avg_price_pen,
                                'avgPriceUsd': subitem.avg_price_usd,
                                'webPricePen': subitem.web_price_pen,
                                'webPriceUsd': subitem.web_price_usd,
                                'price': subitem.price,
                                'discount': subitem.discount,
                                'description': subitem.description ? subitem.description : '',
                                'promotionItemId': subitem.promotion_item_id,
                                'promotionGiftOptionId': subitem.promotion_gift_option_id,
                                'igvAffection': subitem.igv_affection,
                                'priceType': '',
                                'perceptionAffected': subitem.perception_affected,
                                'expiration': subitem.expiration,
                                'isLocked': subitem.is_locked,
                                'parentType': '" . Product::TYPE_COMBO. "',
                                'parentRoute': subitem.parent_route,
                                'quantityItemId': subitem.parent_route,
                                'totalItemId': subitem.quantity_item_id,
                                'giftCost': 0,
                                'movement_id' : item.movement_id, 
                                'movement_route' : item.route,
                                'movement_document' : item.document, 
                                'include_igv' : item.include_igv,
                                'igvPercentage' : subitem.igv_percentage,
                                'errors' : item.errors
                            }

                            SIANCommercialSelectGridAddItem(
                                divId,
                                groupSubtableId,
                                subitem.instance_id,
                                newSubitem
                            );
                        });
                        break;

                    default:
                        const newItem = {
                            'giftOfInstance': item.gift_of_instance,
                            'productId': item.product_id,
                            'itemTypeId': item.item_type_id,
                            'productType': item.product_type,
                            'productName': item.product_name,
                            'unitStock': item.unit_stock,
                            'mixedStock': item.mixed_stock,
                            'presQuantity': item.pres_quantity,
                            'presentationItems': item.presentationItems,
                            'equivalence': item.equivalence,
                            'warehouseId': item.warehouse_id,
                            'allowDecimals': item.allow_decimals,
                            'skipCostValidation': item.skip_cost_validation,
                            'minPricePen': item.min_price_pen,
                            'minPriceUsd': item.min_price_usd,
                            'avgPricePen': item.avg_price_pen,
                            'avgPriceUsd': item.avg_price_usd,
                            'webPricePen': item.web_price_pen,
                            'webPriceUsd': item.web_price_usd,
                            'price': item.price,
                            'discount': item.discount,
                            'description': item.description ? item.description : '',
                            'promotionItemId': item.promotion_item_id,
                            'promotionGiftOptionId': item.promotion_gift_option_id,
                            'igvAffection': item.igv_affection,
                            'priceType': '',
                            'perceptionAffected': item.perception_affected,
                            'expiration': item.expiration,
                            'isLocked': item.is_locked,
                            'parentType': null,
                            'parentRoute': item.parent_route,
                            'quantityItemId': item.parent_route,
                            'totalItemId': item.quantity_item_id,
                            'giftCost': 0,
                            'movement_id' : item.movement_id, 
                            'movement_route' : item.route,
                            'movement_document' : item.document, 
                            'include_igv' : item.include_igv,
                            'igvPercentage' : item.igv_percentage,
                            'errors' : item.errors
                        }

                        SIANCommercialSelectGridAddItem(
                            divId,
                            main_table_id,
                            item.instance_id,
                            newItem
                        );
                        break;
                }
                });
            }


        function initializeSIANCommercialSelectGrid(divId, items, errors, currencySymbol) {
            var divObj = $('#' + divId);
            divObj.data('loaded', 1);
            divObj.data('allowCostProducts', 0);

            // Generamos tabla principal
            var main_table_id = SIANCommercialSelectGridAddTable(divId, '', errors);
            divObj.data('main_table_id', main_table_id);
            divObj.data('active_table_id', main_table_id);

            addItemsToCommercialGrid(divId,main_table_id,[]);

            // UPDATE
            SIANCommercialSelectGridUpdateIn(divId, true);
            SIANCommercialSelectGridGetIds(divId);
            SIANCommercialSelectGridUpdateAmounts(divId);
            unfocusable();

            // CURRENCY SYMBOL
            $('span.currency-symbol').text(currencySymbol);
            divObj.data('set_summary_total', 1);
        }
        
        initDiv('{$this->id}');
        initDiv('{$this->group_id}');

        $(document).ready(function() {
            initializeSIANCommercialSelectGrid('{$this->id}',[],[],'{$this->pen_symbol}');
            initializeSIANCommercialSelectGrid('{$this->group_id}',[],[],'{$this->pen_symbol}');
        });

        function updateOcLabel(oc_document, oc_route) {
            const h2 = $('.sian-commercial-select-grid-label-oc');
            const a = h2.find('.sian-commercial-select-grid-ancore');

            h2.contents().filter(function () {
                return this.nodeType === 3;
            }).first().replaceWith('Orden de Compra ');

            const svgIcon = ` 
                <svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='currentColor' class='bi bi-box-arrow-up-right' viewBox='0 0 16 16' style='margin-left: 0.3rem; vertical-align: middle;'>
                <path fill-rule='evenodd' d='M8.636 3.5a.5.5 0 0 0-.5-.5H1.5A1.5 1.5 0 0 0 0 4.5v10A1.5 1.5 0 0 0 1.5 16h10a1.5 1.5 0 0 0 1.5-1.5V7.864a.5.5 0 0 0-1 0V14.5a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h6.636a.5.5 0 0 0 .5-.5'/>
                <path fill-rule='evenodd' d='M16 .5a.5.5 0 0 0-.5-.5h-5a.5.5 0 0 0 0 1h3.793L6.146 9.146a.5.5 0 1 0 .708.708L15 1.707V5.5a.5.5 0 0 0 1 0z'/>
                </svg>`;

            a.html(oc_document + svgIcon);
            a.attr('href', oc_route);
            a.show();
        }

        function resetOcLabel() {
            const h2 = $('.sian-commercial-select-grid-label-oc');
            const a = h2.find('.sian-commercial-select-grid-ancore');

            a.html('').attr('href', '#').hide();

            h2.contents().filter(function () {
                return this.nodeType === 3;
            }).first().replaceWith('Selecciona una Orden de Compra ');
        }

        function addItemsFromPurchaseOrder(movementId){
            const url = 'getItemsForPurchaseOrder/' + movementId;
            $.ajax({
                type: 'get',
                url: url,
                beforeSend: function(xhr) {
                    window.active_ajax++;
                    $('div.ui-tooltip').remove();
                },
                success: function(odata) {
                    window.active_ajax--;

                    $('#{$this->batch_loader_id}').css({
                        'width': '4rem',
                        'display': 'none'
                    });
                    SIANCommercialSelectGridRemoveAllItems('{$this->id}');
                    const items = odata; 
                    const div_id = '{$this->id}';
                    const divObj = $('#'+ div_id);
                    const main_table_id = divObj.data('main_table_id');
                    addItemsToCommercialGrid(div_id,main_table_id,items)
                    SIANCommercialSelectGridUpdateIn(div_id, true);
                    SIANCommercialSelectGridGetIds(div_id);
                    SIANCommercialSelectGridUpdateAmounts(div_id);
                },
                error: function(request, status, error) {
                    bootbox.alert(us_message(request.responseText, 'error'));
                    window.active_ajax--;
                },
                dataType: 'json'
            });
        }
       
        $('#{$this->id}').data('changeCCDependence', function(operationChangeData){
            {$this->onChangeCCDependence};
        });
        
        $('body').on('change', '#{$this->item_equivalence_id}', function(e) {
            //
            if(!SIANCommercialSelectGridSetPrices('{$this->id}', '', $('#{$this->item_pres_quantity_id}'), $('#{$this->item_equivalence_id}'), $('#{$this->item_allow_decimals_id}'), $('#{$this->item_igv_affection_id}'), $('#{$this->item_price_id}'), $('#{$this->item_discount_id}'), $('#{$this->item_min_price_pen_id}'), $('#{$this->item_min_price_usd_id}'), $('#{$this->item_avg_price_pen_id}'), $('#{$this->item_avg_price_usd_id}'), $('#{$this->item_web_price_pen_id}'), $('#{$this->item_web_price_usd_id}'), $('#{$this->item_promotion_item_id}'), $('#{$this->item_warehouse_id}'), 0, true)){
                e.preventDefault();
                return false;
            }
            var product_id = USAutocompleteField('{$this->autocomplete_id}', 'value');
            //Si se cambia
            " . ($this->model->movement->validate_stock == 1 ? "
            SIANCommercialSelectGridLoadCommercialStock('{$this->id}', product_id, $('#{$this->item_equivalence_id}'), $('#{$this->item_unit_stock_id}'), null, $('#{$this->item_mixed_stock_label_id}'));
            " : "") . "
        });

        $('body').on('change', '#{$this->item_igv_affection_id}', function(e) {
            SIANCommercialSelectGridSetPrices('{$this->id}', '', $('#{$this->item_pres_quantity_id}'), $('#{$this->item_equivalence_id}'), $('#{$this->item_allow_decimals_id}'), $('#{$this->item_promotion_item_id}'), $('#{$this->item_igv_affection_id}'), $('#{$this->item_price_id}'), $('#{$this->item_discount_id}'), $('#{$this->item_min_price_pen_id}'), $('#{$this->item_min_price_usd_id}'), $('#{$this->item_avg_price_pen_id}'), $('#{$this->item_avg_price_usd_id}'), $('#{$this->item_web_price_pen_id}'), $('#{$this->item_web_price_usd_id}'), 1, 0);
        });

        $('body').on('change', '#{$this->id} select.sian-commercial-select-grid-item-equivalence', function(e) {
            var rowObj = $(this).closest('tr');
            //
            if(SIANCommercialSelectGridGetAndChangePresentation('{$this->id}', rowObj.attr('id'), true)) {
                //Actualizar montos
                SIANCommercialSelectGridUpdateAmounts('{$this->id}'); 
            } else {
                e.preventDefault();
                return false;
            }
            " . ($this->model->movement->validate_stock == 1 ? "
            SIANCommercialSelectGridGetAndLoadCommercialStock('{$this->id}', rowObj.attr('id'));
            " : "") . "
            
        });
        
        $('body').on('change', '#{$this->id} select.sian-commercial-select-grid-item-igv-affection', function(e) {
            var rowObj = $(this).closest('tr');
            var instance_id = rowObj.attr('id');
            //Propagamos IGV (solo tiene efecto si es grupo o combo)
            SIANCommercialSelectGridPropagateIGVAffection('{$this->id}', instance_id);
            //
            " . ($this->is_commercial_route ? "
            SIANCommercialSelectGridGetAndSetPrices('{$this->id}', instance_id, 1);
            " : "") .
            ($this->model->movement->route == 'logistic/purchaseOrder' ? "
            SIANCommercialSelectGridSetPurchaseGift('{$this->id}', instance_id);
            " : "") .
            "
            SIANCommercialSelectGridUpdateAmounts('{$this->id}'); 
        });
       
        
        function SIANCommercialSelectGridCalculateTotals(div_id){
            var divObj = $('#{$this->id}');
            var include_igv = divObj.data('include_igv');
                
            var summary_affected1 = $('#{$this->summary_affected1_input_id}').double2();
            var summary_inaffected = $('#{$this->summary_inaffected_input_id}').double2();
            var summary_nobill = $('#{$this->summary_nobill_input_id}').double2();
            var summary_free = $('#{$this->summary_free_input_id}').double2();
            var summary_igv = $('#{$this->summary_igv_input_id}').double2();
            var summary_perception = $('#{$this->summary_perception_input_id}').double2();
            var summary_advance_amount = parseFloat($('#{$this->summary_advance_amount_input_id}').val());
            
            var summary_nnet  = summary_affected1 + summary_inaffected + summary_nobill + summary_free;
            var summary_fnet  = summary_free;
            var summary_net   = summary_nnet - summary_fnet;
            var summary_affected = summary_affected1;
            
            var summary_nigv = summary_igv;
            var summary_figv = 0;
            
            var summary_ntotal = summary_nnet + summary_nigv;
            var summary_ftotal = summary_fnet + summary_figv;
            var summary_total  = summary_net  + summary_igv;
            
            var summary_nreal  = summary_total + summary_perception;
            var summary_freal  = summary_ftotal;
            var summary_real   = summary_nreal;
            
            var summary_crude = 0;
            
            if(include_igv == 1){
                summary_crude = summary_total;
            }else{
                summary_crude = summary_net;
            }
            
            var new_sumary_advance_percentage = (summary_advance_amount / summary_real) * 100
            new_sumary_advance_percentage = isNaN(new_sumary_advance_percentage) ? 0 : new_sumary_advance_percentage;
    
            $('#{$this->summary_crude_input_id}').floatVal({$this->tfixed}, summary_crude);
            $('#{$this->summary_affected_input_id}').floatVal({$this->tfixed}, summary_affected);            
            $('#{$this->summary_nnet_input_id}').floatVal({$this->tfixed}, summary_nnet);
            $('#{$this->summary_fnet_input_id}').floatVal({$this->tfixed}, summary_fnet);
            $('#{$this->summary_net_input_id}').floatVal({$this->tfixed}, summary_net);
            $('#{$this->summary_nigv_input_id}').floatVal({$this->tfixed}, summary_nigv);
            $('#{$this->summary_figv_input_id}').floatVal({$this->tfixed}, summary_figv);
            $('#{$this->summary_ntotal_input_id}').floatVal({$this->tfixed}, summary_ntotal);
            $('#{$this->summary_ftotal_input_id}').floatVal({$this->tfixed}, summary_ftotal);
            $('#{$this->summary_total_input_id}').floatVal({$this->tfixed}, summary_total);
            $('#{$this->summary_nreal_input_id}').floatVal({$this->tfixed}, summary_nreal);
            $('#{$this->summary_freal_input_id}').floatVal({$this->tfixed}, summary_freal);
            $('#{$this->summary_real_input_id}').floatVal({$this->tfixed}, summary_real);
            $('#{$this->summary_advance_percentage_input_id}').floatVal({$this->tfixed},new_sumary_advance_percentage);
        }
        
        // EVENTO ENTER CAMPO CANTIDAD
        $('body').on('keydown', '#{$this->item_pres_quantity_id}', function(e) {
            if(e.which === 13) {
                $('#{$this->add_button_id}').click();
            }
        })
        
        // EVENTO ENTER CAMPO PRECIO
        $('body').on('keydown', '#{$this->item_price_id}', function(e) {
            if(e.which === 13) {
                $('#{$this->add_button_id}').click();
            }
        })
        
        // EVENTO ENTER CAMPO DESCUENTO
        $('body').on('keydown', '#{$this->item_discount_id}', function(e) {
            if(e.which === 13) {
                $('#{$this->add_button_id}').click();
            }
        })
        
        function SIANCommercialSelectGridChangeCurrency(element) {
            const previousValue = element.dataset.prev || element.value;
            const newValue = element.value;

            const countGroupTable = SIANCommercialSelectGridGetCurrentItems('{$this->group_id}');

            if (countGroupTable > 0) {
                const confirmed = confirm('¿Está seguro que desea cambiar de moneda? Se eliminarán los ítems seleccionados');

                if (!confirmed) {
                    element.value = previousValue;
                    return;
                }
            }

            SIANCommercialSelectGridRemoveAllItems('{$this->id}');
            SIANCommercialSelectGridRemoveAllItems('{$this->group_id}');
            resetOcLabel();
            USAutocompleteReset('{$this->parent_autocomplete_id}');

            element.dataset.prev = newValue;
        }
       
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        
        echo "<div class='row'>";
        echo "<div class='col-lg-5 col-md-5 col-sm-5 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->bussiness_partner_autocomplete_id,
            'name' => 'bussiness_partner_id',
            'value_id' => $this->bussiness_partner_value_id,
            'aux_id' => $this->bussiness_partner_aux_id,
            'search_id' => $this->bussiness_partner_search_id,
            'required' => true,
            'label' => "Proveedor",
            'view' => array(
                'model' => 'DpBusinessPartner',
                'scenario' => DpBusinessPartner::SCENARIO_PROVIDER,
                'attributes' => array(
                    array('name' => 'identification_name', 'width' => 5, 'search' => false),
                    array('name' => 'identification_number', 'width' => 15, 'types' => array('aux')),
                    array('name' => 'person_id', 'width' => 0, 'types' => array('id', 'value'), 'hidden' => true),
                    array('name' => 'person_name', 'width' => 40, 'types' => array('text')),
                    array('name' => 'official_address', 'width' => 40, 'search' => false)
                ),
            ),
            'readonly' => false,
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo SIANForm::dropDownListNonActive('Moneda', 'currency', Currency::PEN, Currency::getListData(), array(
            'id' => $this->currency_value_id,
            'readonly' => false,
            'onchange' => "SIANCommercialSelectGridChangeCurrency(this);",
        ));
        echo "</div>";
        echo "<div class='col-lg-5 col-md-5 col-sm-5 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->parent_autocomplete_id,
            'name' => 'reference_movement_id',
            'value_id' => $this->parent_value_id,
            'required' => true,
            'label' => "Orden de Compra",
            'onselect' => "
                SIANCommercialSelectGridRemoveAllItems('{$this->id}');
                resetOcLabel();
                if(ui.item){
                    addItemsFromPurchaseOrder(ui.item.movement_id);
                    updateOcLabel(ui.item.document,'/admin/' + ui.item.route + '/' + ui.item.movement_id);
                    const business_partner_id = $('#{$this->bussiness_partner_value_id}').val();
                    if(!business_partner_id){
                        $('#{$this->bussiness_partner_aux_id}').val(ui.item.identification_number);
                        $('#{$this->bussiness_partner_search_id}').click();
                    }
                }
            ",
            'onreset' => "
                SIANCommercialSelectGridRemoveAllItems('{$this->id}');
                resetOcLabel();
                ",
            'hideText' => true,
            'view' => array(
                'model' => 'DpNotBilledOrders',
                'scenario' => DpNotBilledOrders::SCENARIO_PURCHASE_ORDER,
                'attributes' => array(
                    array('name' => 'movement_id', 'hidden' => true, 'types' => array('id', 'value')),
                    array('name' => 'document', 'width' => 25, 'types' => array('aux','text')),
                    array('name' => 'identification_name', 'width' => 5, 'search' => false),
                    array('name' => 'identification_number', 'width' => 15),
                    array('name' => 'aux_person_name', 'width' => 45),
                    array('name' => 'emission_date', 'width' => 10),
                ),
                'params' => 
                    "{
                        filter_currency : function () {
                            return $('#{$this->currency_value_id}').val();
                        },

                        filter_aux_person : function () {
                            return $('#{$this->bussiness_partner_value_id}').val();
                        },
                    }",
            ),
            'readonly' => false,
        ));
        echo "</div>";
        echo "</div>";
        echo "<hr/>";
        echo "<div class='row'>";
        echo "<div class='col-lg-5 col-md-5 col-sm-5 col-xs-12 table-division'>";
        echo "<div id='{$this->id}' >";
        echo "<h2 class='text-center sian-commercial-select-grid-label-oc' style='margin-bottom:2rem;'>Selecciona una Orden de Compra <a class='sian-commercial-select-grid-ancore' target='_blank' href=''> </a></h2>";
        echo "<div id='SaleOrderListProductForMobile' class='list-products'><ul class='accordion-products'><ul/></div>";
        echo "<div id='SaleOrderListProductsFooterForMobile'><div id='TotalsListProductsFooterForMobile'></div></div>";
        echo "<div id='SaleOrderListProduct' class='sian-commercial-select-grid-tables'></div>";
        echo "</div>";
        echo "</div>";
        echo "<div class='col-lg-7 col-md-7 col-sm-7 col-xs-12'>";
        echo "<div id='{$this->group_id}'>";
        echo "<h2 class='text-center' style='margin-bottom:2rem;'>Items Seleccionados</h2>";
        echo "<div id='SaleOrderListProductForMobile' class='list-products'><ul class='accordion-products'><ul/></div>";
        echo "<div id='SaleOrderListProductsFooterForMobile'><div id='TotalsListProductsFooterForMobile'></div></div>";
        echo "<div id='SaleOrderListProduct' class='sian-commercial-select-grid-tables'></div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
  
    }
}
