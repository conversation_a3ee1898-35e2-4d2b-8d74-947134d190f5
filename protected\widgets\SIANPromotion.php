<?php

class SIANPromotion extends CWidget {

    //Var
    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $store_items = null;
    public $currency_items = null;
    public $context = 'success';
    public $main_title = 'Productos promocionados';
    public $ifixed = Promotion::IFIXED;
    public $tfixed = Promotion::TFIXED;
    public $show_web_price = false;
    //PRIVATE
    private $controller;
    private $currency_input_id;
    private $autocomplete_id;
    private $item_product_type_id;
    private $item_equivalence_id;
    private $item_sum_unit_stock_id;
    private $item_sum_mixed_stock_id;
    private $item_allow_decimals_id;
    private $item_cost_id;
    private $item_icost_id;
    private $item_mprice_id;
    private $item_imprice_id;
    private $item_aprice_id;
    private $item_iaprice_id;
    private $item_wprice_id;
    private $item_iwprice_id;
    private $preview_access;
    private $add_button_id;
    private $istep;
    private $tstep;
    private $presentation_mode;
    private $presentation_url;
    private $presentationItems = [];
    private $example_url;
    private $stock_url;
    private $mprice_label;
    private $aprice_label;
    private $wprice_label;

    public function init() {
        $this->controller = Yii::app()->controller;
        //
        if (!isset($this->store_items)) {
            throw new Exception('Debe especificar la lista de tiendas');
        }
        //
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //
        $this->currency_input_id = $this->controller->getServerId();
        $this->autocomplete_id = $this->controller->getServerId();
        $this->item_product_type_id = $this->controller->getServerId();
        $this->item_equivalence_id = $this->controller->getServerId();
        $this->item_sum_unit_stock_id = $this->controller->getServerId();
        $this->item_sum_mixed_stock_id = $this->controller->getServerId();
        $this->item_allow_decimals_id = $this->controller->getServerId();
        $this->item_cost_id = $this->controller->getServerId();
        $this->item_icost_id = $this->controller->getServerId();
        $this->item_mprice_id = $this->controller->getServerId();
        $this->item_imprice_id = $this->controller->getServerId();
        $this->item_aprice_id = $this->controller->getServerId();
        $this->item_iaprice_id = $this->controller->getServerId();
        $this->item_wprice_id = $this->controller->getServerId();
        $this->item_iwprice_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();
        //
        $this->preview_access = $this->controller->checkRoute('/logistic/product/preview');
        $this->presentation_mode = SpGetProductPresentations::MODE_COMBOBOX_WITH_PRICES;
        $this->presentation_url = $this->controller->createUrl("/movement/getPresentations");
        //VAR
        $this->istep = USMath::toStep($this->ifixed);
        $this->tstep = USMath::toStep($this->tfixed);

        $this->mprice_label = 'P.Min.';
        $this->aprice_label = Yii::app()->controller->getOrganization()->globalVar->aprice_label;
        $this->wprice_label = Yii::app()->controller->getOrganization()->globalVar->wprice_label;
        //
        $a_product_ids = [];
        foreach ($this->model->tempItems as $o_item) {
            $a_product_ids[] = $o_item->product_id;
            foreach ($o_item->tempGifts as $o_gift) {
                foreach ($o_gift->tempOptions as $o_option) {
                    $a_product_ids[] = $o_option->product_id;
                }
            }
        }
        //Productos
        if (count($a_product_ids) > 0) {
            //Si hay productos...
            $this->presentationItems = SpGetProductPresentations::getAssociative($this->presentation_mode, $a_product_ids, $this->model->price_currency, 1);
        }
        //Recuperamos variaciones
        $a_items = [];
        foreach ($this->model->tempItems as $o_item) {

            $s_item_equivalence = number_format($o_item->equivalence, 2);
            $a_itemPresentation = isset($this->presentationItems[$o_item->product_id]) && isset($this->presentationItems[$o_item->product_id][$s_item_equivalence]) ? $this->presentationItems[$o_item->product_id][$s_item_equivalence] : null;

            $a_item = [];
            $a_item['promotion_item_id'] = isset($o_item->promotion_item_id) ? $o_item->promotion_item_id : '';
            $a_item['product_id'] = isset($o_item->product_id) ? $o_item->product_id : '';
            $a_item['product_name'] = isset($o_item->product_name) ? $o_item->product_name : '';
            $a_item['product_type'] = isset($o_item->product_type) ? $o_item->product_type : '';
            $a_item['equivalence'] = $o_item->equivalence;
            $a_item['allow_decimals'] = isset($o_item->allow_decimals) ? $o_item->allow_decimals : '';
            $a_item['cost'] = isset($a_itemPresentation) ? $a_itemPresentation['cost'] : 0;
            $a_item['icost'] = isset($a_itemPresentation) ? $a_itemPresentation['icost'] : 0;
            $a_item['mprice'] = isset($a_itemPresentation) ? $a_itemPresentation['mprice'] : 0;
            $a_item['imprice'] = isset($a_itemPresentation) ? $a_itemPresentation['imprice'] : 0;
            $a_item['aprice'] = isset($a_itemPresentation) ? $a_itemPresentation['aprice'] : 0;
            $a_item['iaprice'] = isset($a_itemPresentation) ? $a_itemPresentation['iaprice'] : 0;
            $a_item['wprice'] = isset($a_itemPresentation) ? $a_itemPresentation['wprice'] : 0;
            $a_item['iwprice'] = isset($a_itemPresentation) ? $a_itemPresentation['iwprice'] : 0;
            $a_item['carry'] = isset($o_item->carry) ? $o_item->carry : 1;
            $a_item['pay'] = isset($o_item->pay) ? $o_item->pay : 1;
            $a_item['discount_percent'] = isset($o_item->discount_percent) ? $o_item->discount_percent : 0;
            $a_item['rprice'] = $o_item->{"rprice_{$this->model->price_currency}"};
            $a_item['irprice'] = $o_item->{"irprice_{$this->model->price_currency}"};
            $a_item['pprice'] = $o_item->{"pprice_{$this->model->price_currency}"};
            $a_item['ipprice'] = $o_item->{"ipprice_{$this->model->price_currency}"};
            $a_item['sum_unit_stock'] = isset($o_item->sum_unit_stock) ? $o_item->sum_unit_stock : 0;
            $a_item['sum_mixed_stock'] = isset($o_item->sum_mixed_stock) ? $o_item->sum_mixed_stock : 0;
            $a_item['pres_stock'] = isset($o_item->pres_stock) ? $o_item->pres_stock : 0;
            $a_item['presentationItems'] = isset($this->presentationItems[$o_item->product_id]) ? $this->presentationItems[$o_item->product_id] : [];

            $a_gifts = [];
            foreach ($o_item->tempGifts as $o_gift) {
                $a_gift = [];
                $a_gift['promotion_gift_id'] = isset($o_gift->promotion_gift_id) ? $o_gift->promotion_gift_id : '';
                $a_gift['gift_number'] = $o_gift->gift_number;

                $a_options = [];
                foreach ($o_gift->tempOptions as $o_option) {

                    $s_option_equivalence = number_format($o_option->equivalence, 2);
                    $a_optionPresentation = isset($this->presentationItems[$o_option->product_id]) && isset($this->presentationItems[$o_option->product_id][$s_option_equivalence]) ? $this->presentationItems[$o_option->product_id][$s_option_equivalence] : null;

                    $a_option = [];
                    $a_option['promotion_gift_option_id'] = isset($o_option->promotion_gift_option_id) ? $o_option->promotion_gift_option_id : '';
                    $a_option['product_id'] = isset($o_option->product_id) ? $o_option->product_id : '';
                    $a_option['product_name'] = isset($o_option->product_name) ? $o_option->product_name : '';
                    $a_option['product_type'] = isset($o_option->product_type) ? $o_option->product_type : '';
                    $a_option['equivalence'] = isset($o_option->equivalence) ? $o_option->equivalence : '';
                    $a_option['allow_decimals'] = isset($o_option->allow_decimals) ? $o_option->allow_decimals : '';
                    $a_option['cost'] = isset($a_optionPresentation) ? $a_optionPresentation['cost'] : 0;
                    $a_option['icost'] = isset($a_optionPresentation) ? $a_optionPresentation['icost'] : 0;
                    $a_option['mprice'] = isset($a_optionPresentation) ? $a_optionPresentation['mprice'] : 0;
                    $a_option['imprice'] = isset($a_optionPresentation) ? $a_optionPresentation['imprice'] : 0;
                    $a_option['aprice'] = isset($a_optionPresentation) ? $a_optionPresentation['aprice'] : 0;
                    $a_option['iaprice'] = isset($a_optionPresentation) ? $a_optionPresentation['iaprice'] : 0;
                    $a_option['wprice'] = isset($a_optionPresentation) ? $a_optionPresentation['wprice'] : 0;
                    $a_option['iwprice'] = isset($a_optionPresentation) ? $a_optionPresentation['iwprice'] : 0;
                    $a_option['carry'] = isset($o_option->carry) ? $o_option->carry : 0;
                    $a_option['discount_percent'] = isset($o_option->discount_percent) ? $o_option->discount_percent : 0;
                    $a_option['rprice'] = $o_option->{"rprice_{$this->model->price_currency}"};
                    $a_option['irprice'] = $o_option->{"irprice_{$this->model->price_currency}"};
                    $a_option['pprice'] = $o_option->{"pprice_{$this->model->price_currency}"};
                    $a_option['ipprice'] = $o_option->{"ipprice_{$this->model->price_currency}"};
                    $a_option['sum_unit_stock'] = isset($o_option->sum_unit_stock) ? $o_option->sum_unit_stock : 0;
                    $a_option['sum_mixed_stock'] = isset($o_option->sum_mixed_stock) ? $o_option->sum_mixed_stock : 0;
                    $a_option['pres_stock'] = isset($o_option->pres_stock) ? $o_option->pres_stock : 0;
                    $a_option['presentationItems'] = isset($this->presentationItems[$o_option->product_id]) ? $this->presentationItems[$o_option->product_id] : [];
                    $a_option['errors'] = [
                        'product_name' => $o_option->getError('product_name'),
                        'carry' => $o_option->getError('carry'),
                        'discount_percent' => $o_option->getError('discount_percent'),
                        'rprice' => $o_option->getError("rprice_{$this->model->price_currency}"),
                        'irprice' => $o_option->getError("irprice_{$this->model->price_currency}"),
                        'pprice' => $o_option->getError("pprice_{$this->model->price_currency}"),
                        'ipprice' => $o_option->getError("ipprice_{$this->model->price_currency}"),
                        'pres_stock' => $o_option->getError('pres_stock'),
                    ];

                    $a_options[] = $a_option;
                }
                $a_gift['options'] = $a_options;
                $a_gift['errors'] = $o_gift->getAllErrors();
                //
                $a_gifts[] = $a_gift;
            }

            $a_item['gifts'] = $a_gifts;
            $a_item['errors'] = [
                'product_name' => $o_item->getError('product_name'),
                'carry' => $o_item->getError('carry'),
                'pay' => $o_item->getError('pay'),
                'discount_percent' => $o_item->getError('discount_percent'),
                'rprice' => $o_item->getError("rprice_{$this->model->price_currency}"),
                'irprice' => $o_item->getError("irprice_{$this->model->price_currency}"),
                'pprice' => $o_item->getError("pprice_{$this->model->price_currency}"),
                'ipprice' => $o_item->getError("ipprice_{$this->model->price_currency}"),
                'pres_stock' => $o_item->getError('pres_stock'),
                'tempGifts' => $o_item->getError('tempGifts'),
            ];
            //
            $a_items[] = $a_item;
        }

        //URL
        $this->example_url = Yii::app()->params['admin_url'] . "/images/promotion/example.png";
        $this->stock_url = $this->controller->createUrl('getStocks');

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-promotion.js');

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
        $(document).ready(function() {  
            //
            var divObj = $('#{$this->id}');
            divObj.data('view-access', " . json_encode($this->preview_access) . ");//ACCESS
            divObj.data('view-url', '{$this->controller->createUrl('/logistic/product/preview')}');//URL                
            divObj.data('store_items', " . CJSON::encode($this->store_items) . ");
            divObj.data('presentation_mode', '{$this->presentation_mode}');
            divObj.data('presentation_url', '{$this->presentation_url}');
            divObj.data('price_mode', {$this->model->price_mode});
            divObj.data('show_web_price', " . CJSON::encode($this->show_web_price) . ");
            divObj.data('currency', '{$this->controller->getOrganization()->globalVar->display_currency}');
            divObj.data('igv', {$this->controller->getOrganization()->globalVar->igv});
            divObj.data('ifixed', {$this->ifixed});
            divObj.data('tfixed', {$this->tfixed});
            divObj.data('istep', {$this->istep});
            divObj.data('tstep', {$this->tstep});
            divObj.data('main_title', '{$this->main_title}');                
            divObj.data('context', '{$this->context}');   
            divObj.data('example_url', '{$this->example_url}');
            divObj.data('stock_url', '{$this->stock_url}');
            //IDS
            divObj.data('autocomplete_id', '{$this->autocomplete_id}');
            divObj.data('submit_id', '{$this->form->submit_id}');            
            divObj.data('modal_id', '{$this->modal_id}');            
            divObj.data('item_equivalence_id', '{$this->item_equivalence_id}');
            divObj.data('item_cost_id', '{$this->item_cost_id}');
            divObj.data('item_icost_id', '{$this->item_icost_id}');
            divObj.data('item_mprice_id', '{$this->item_mprice_id}');
            divObj.data('item_imprice_id', '{$this->item_imprice_id}');
            divObj.data('item_aprice_id', '{$this->item_aprice_id}');
            divObj.data('item_iaprice_id', '{$this->item_iaprice_id}');
            divObj.data('item_wprice_id', '{$this->item_wprice_id}');
            divObj.data('item_iwprice_id', '{$this->item_iwprice_id}');
            divObj.data('mprice_label', '{$this->mprice_label}');
            divObj.data('aprice_label', '{$this->aprice_label}');
            divObj.data('wprice_label', '{$this->wprice_label}');
            
            //Generamos tabla principal
            divObj.data('main_group_id', '{$this->id}_group');//Tabla principal
            divObj.data('main_panel_id', '{$this->id}_panel');//Tabla principal
            divObj.data('active_panel_id', '{$this->id}_panel');//Tabla a dónde se agregan los productos
            //
            SIANPromotionInit('{$this->id}', " . CJSON::encode($a_items) . ");
            SIANPromotionChangeStores('{$this->id}');
                
            //Llamamos
            SIANPromotionChangePriceMode('{$this->id}', {$this->model->price_mode});      
        });
        
        $('#{$this->id}').data('changeCurrency', function(currency){
                        
            var table = $('#{$this->id}').find('table.sian-promotion-table'); 
            var exchange_rate = " . Yii::app()->controller->getLastExchange() . ";
            var ifixed = {$this->ifixed};
            var tfixed = {$this->tfixed};
            $('#{$this->id}').data('currency', currency);
            $('#{$this->id}').find('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            USAutocompleteReset('{$this->autocomplete_id}');
                
            $.each(table.find('tr.sian-promotion-item'), function( index, row ) {
            
                var rowObj = $(row);                
                
                if(currency == '" . Currency::USD . "'){  
                    
                    rowObj.find('input.sian-promotion-item-cost').toUsd(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-icost').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-promotion-item-mprice').toUsd(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-imprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-promotion-item-aprice').toUsd(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-iaprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-promotion-item-wprice').toUsd(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-iwprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-promotion-item-rprice').toUsd(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-irprice').toUsd(exchange_rate, tfixed);       
                    rowObj.find('input.sian-promotion-item-pprice').toUsd(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-ipprice').toUsd(exchange_rate, tfixed);                    
                }else{

                    rowObj.find('input.sian-promotion-item-cost').toPen(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-icost').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-promotion-item-mprice').toPen(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-imprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-promotion-item-aprice').toPen(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-iaprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-promotion-item-wprice').toPen(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-iwprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-promotion-item-rprice').toPen(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-irprice').toPen(exchange_rate, tfixed); 
                    rowObj.find('input.sian-promotion-item-pprice').toPen(exchange_rate, ifixed);
                    rowObj.find('input.sian-promotion-item-ipprice').toPen(exchange_rate, tfixed);                    
                }
            });
        });
        
        $('body').on('change', '#{$this->id} input.sian-promotion-store-checkbox', function(e) {
            USAutocompleteReset('{$this->autocomplete_id}');
            SIANPromotionChangeStores('{$this->id}');
        });
        
        $('body').on('change', '#{$this->item_equivalence_id}', function(e) {
            var divObj = $('#{$this->id}');
            //
            SIANPromotionSetCostAndPrices('{$this->id}', $('#{$this->item_equivalence_id}'), $('#{$this->item_cost_id}'), $('#{$this->item_icost_id}'), $('#{$this->item_mprice_id}'), $('#{$this->item_imprice_id}'), $('#{$this->item_aprice_id}'), $('#{$this->item_iaprice_id}'), $('#{$this->item_wprice_id}'), $('#{$this->item_iwprice_id}'));
        });

        $('body').on('change', '#{$this->id} select.sian-promotion-item-equivalence', function(e) {

            var row = $(this).closest('tr');
            
            var divObj = $('#{$this->id}');
            //
            var equivalenceObj = row.find('select.sian-promotion-item-equivalence');
            var costObj = row.find('input.sian-promotion-item-cost');
            var iCostObj = row.find('input.sian-promotion-item-icost');            
            var mPriceObj = row.find('input.sian-promotion-item-mprice');
            var imPriceObj = row.find('input.sian-promotion-item-imprice');
            var aPriceObj = row.find('input.sian-promotion-item-aprice');
            var iaPriceObj = row.find('input.sian-promotion-item-iaprice');
            var wPriceObj = row.find('input.sian-promotion-item-wprice');
            var iwPriceObj = row.find('input.sian-promotion-item-iwprice');
            //
            SIANPromotionSetCostAndPrices('{$this->id}', equivalenceObj, costObj, iCostObj, mPriceObj, imPriceObj, aPriceObj, iaPriceObj, wPriceObj, iwPriceObj);
       });
       
        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var divObj = $('#{$this->id}');

            var active_panel_id = divObj.data('active_panel_id');
            
            if(active_panel_id !== false) {

                var product_id = USAutocompleteField('{$this->autocomplete_id}', 'value');
                var product_name = USAutocompleteField('{$this->autocomplete_id}', 'text');
                var product_type = $('#{$this->item_product_type_id}').val();
                var equivalence = $('#{$this->item_equivalence_id}').floatVal(2);
                var presentationItems = $('#{$this->item_equivalence_id}').data('presentationItems');
                var sum_unit_stock = $('#{$this->item_sum_unit_stock_id}').floatVal(2);
                var sum_mixed_stock = $('#{$this->item_sum_mixed_stock_id}').text();   
                var allow_decimals = $('#{$this->item_allow_decimals_id}').integer();
                var cost = $('#{$this->item_cost_id}').floatVal({$this->ifixed});
                var icost = $('#{$this->item_icost_id}').floatVal({$this->tfixed});
                var mprice = $('#{$this->item_mprice_id}').floatVal({$this->ifixed});
                var imprice = $('#{$this->item_imprice_id}').floatVal({$this->tfixed});
                var aprice = $('#{$this->item_aprice_id}').floatVal({$this->ifixed});
                var iaprice = $('#{$this->item_iaprice_id}').floatVal({$this->tfixed});
                var wprice = $('#{$this->item_wprice_id}').floatVal({$this->ifixed});
                var iwprice = $('#{$this->item_iwprice_id}').floatVal({$this->tfixed});

                if (product_id.length === 0) {
                    USAutocompleteFocus('{$this->autocomplete_id}');
                    return;
                }
                
                if(isNaN(equivalence)){
                    $('#{$this->item_equivalence_id}').notify('Debe elegir una presentación!', 
                        {   className: 'warn',
                            showDuration: 50,
                            hideDuration: 50,
                            autoHideDelay: 5000
                        }
                    );
                    $('#{$this->item_equivalence_id}').focus().select();
                    return;
                }

                SIANPromotionAddItem('{$this->id}', active_panel_id, '', product_id, product_name, product_type, equivalence, presentationItems, allow_decimals, cost, icost, mprice, imprice, aprice, iaprice, wprice, iwprice, 1, 1, 0, aprice, iaprice, aprice, iaprice, sum_unit_stock, sum_mixed_stock, 0, []);

                USAutocompleteReset('{$this->autocomplete_id}');

                //FOCUS
                USAutocompleteFocus('{$this->autocomplete_id}');
                SIANPromotionGetIds('{$this->id}', active_panel_id);
                SIANPromotionRefreshPanel('{$this->id}', active_panel_id);
                unfocusable();
            } else {
                bootbox.alert(us_message('No se puede agregar en este momento porque no hay ningún bloque de regalo agregado.', 'warning'));
            }
        });
                  
       
    ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}'>";

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Datos básicos',
            'headerIcon' => 'barcode',
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->form->textFieldRow($this->model, 'promotion_name', array(
            'maxlength' => 255,
        ));
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->widget('application.widgets.USDateRange', array(
            'form' => $this->form,
            'model' => $this->model,
            'grid' => true,
            'begin_attribute' => 'begin_date',
            'end_attribute' => 'end_date',
            'beginOptions' => [
                'timepicker' => true,
                'closeOnDateSelect' => false,
                'format' => Promotion::DATETIME_FORMAT,
                'formatDate' => Promotion::DATE_FORMAT,
                'formaTime' => Promotion::TIME_FORMAT,
            ],
            'endOptions' => array(
                'timepicker' => true,
                'closeOnDateSelect' => false,
                'format' => Promotion::DATETIME_FORMAT,
                'formatDate' => Promotion::DATE_FORMAT,
                'formaTime' => Promotion::TIME_FORMAT,
            ),
                ), true);
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        echo $this->renderSwitches();
        echo "</div>";
        echo "</div>";
        //
        $this->endWidget();

        //Renderizamos
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Tiendas dónde se aplicará la promoción *',
            'headerIcon' => 'star',
            'htmlOptions' => [
                'class' => $this->model->hasErrors('tempStores') ? 'us-error' : ''
            ]
        ));
        echo $this->renderStores();
        $this->endWidget();

        //Renderizamos variaciones
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->main_title,
            'context' => $this->context,
            'headerIcon' => 'barcode',
            'headerHtmlOptions' => [
                'class' => 'sian-promotion-panel-title',
            ],
            'htmlOptions' => [
                'class' => $this->model->hasErrors('tempItems') ? 'us-error' : ''
            ]
        ));
        echo $this->renderBar();
        echo "<hr>";
        echo "<div id='{$this->id}_group' class='sian-promotion-gift-group'>";
        echo "<div id='{$this->id}_panel' class='sian-promotion-gift-panel'>";
        echo CHtml::tag('table', [
            'class' => 'table table-condensed table-hover sian-promotion-table ' . ($this->model->hasErrors('tempItems') ? 'us-error' : ''),
                ], $this->renderTable(), true);
        echo "</div>";
        echo "</div>";

        //Grupos de regalos
        echo "<div class='sian-promotion-gift-groups'></div>";
        //
        echo "<hr>";
        //
        $s_img = CHtml::image(Yii::app()->params['admin_url'] . "/images/promotion/assistant.png", 'Asistente de promociones', [
                    'class' => 'img-responsive',
        ]);
        echo "<div class='row'>";
        echo "<div class='col-lg-8 col-md-8 col-sm-12 col-sm-12'>";
        echo "<p>(*) Tomando como referencia el precio de lista o promedio contra el precio (1).</p>";
        echo "<p><b>(1) Precio real:</b> Es el precio si no hubiera promoción, es decir lo que terminaria pagando el cliente por cada unidad definida en '{$this->model->getAttributeLabel('items.carry')}'.</p>";
        echo "<p><b>(2) Precio cliente:</b> Es el precio con promoción, es lo que el cliente pagara por cada unidad definida en '{$this->model->getAttributeLabel('items.pay')}'. Este precio es el que se mostrará en el sistema, siempre a partir de una unidad, sin importar si se aplica un 3x2, 2x1, etc.</p>";
        echo "<p><b>(3) Total:</b> Es lo que pagará el cliente por todo lo definido en '{$this->model->getAttributeLabel('items.carry')}'.</p>";
        echo "<p><b>(4) Sumatoria de stocks:</b> Suma de stock de cada producto en las tiendas seleccionadas </p>";
        echo "</div>";
        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-sm-12'>";
        echo CHtml::link($s_img, Strings::LINK_TEXT, [
            'onclick' => "SIANPromotionExamplePreview('{$this->id}')"
        ]);
        echo "</div>";
        echo "</div>";
        //Cerramos el widget
        $this->endWidget();
        //
        echo "</div>";
    }

    private function renderSwitches() {
        $s_html = '';
        $s_html .= "<div class='row'>";
        $s_html .= "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        $s_html .= $this->widget('application.widgets.USSwitch', array('model' => $this->model, 'attribute' => 'status'), true);
        $s_html .= "</div>";
        $s_html .= "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        $s_html .= $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'price_mode',
            'switchChange' => "
                //Llamamos a price mode de presentation
                SIANPromotionChangePriceMode('{$this->id}', (this.checked ? 1 : 0));
            "
                ), true);
        $s_html .= "</div>";
        $s_html .= "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-12'>";
        $s_html .= $this->form->dropDownListRow($this->model, 'price_currency', $this->currency_items, [
            'id' => $this->currency_input_id,
            'onchange' => " var currency = $(this).val();                            
                            $('#{$this->id}').data('changeCurrency')(currency);",
            'class' => 'sian-promotion-currency']);
        $s_html .= '</div>';
        $s_html .= "</div>";

        return $s_html;
    }

    private function renderStores() {

        $s_html = '';
        if (count($this->store_items) > 0) {
            $s_html .= "<div class='row'>";
            foreach ($this->store_items as $o_store) {
                $s_html .= "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
                $b_checked = isset($this->model->tempStores[$o_store->store_id]);
                $s_html .= $this->renderStoreCheckbox($o_store, $b_checked);
                $s_html .= '</div>';
            }
            $s_html .= "</div>";
            $s_html .= "<hr>";
            $s_html .= "<p>(*) Se están mostrando las tiendas de las unidades de negocio que tiene asignadas</p>";
        } else {
            $s_html .= Strings::NO_DATA;
        }

        return $s_html;
    }

    private function renderStoreCheckbox($p_o_store, $p_b_checked) {

        $s_id = $this->controller->getServerId();

        $s_content = CHtml::label(CHtml::checkBox("Store[{$p_o_store->store_id}]", $p_b_checked, array(
                            'id' => $s_id,
                            'class' => 'sian-promotion-store-checkbox',
                            'data-store_id' => $p_o_store->store_id
                        )) . $p_o_store->store_name, $s_id);

        return CHtml::tag('div', array('class' => 'checkbox'), $s_content, true);
    }

    private function renderBar() {
        echo "<div class='row'>";
        echo "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>";
        echo $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->autocomplete_id,
            'label' => 'Productos',
            'name' => null,
            'view' => array(
                'model' => 'DpAllMerchandisePresentations',
                'scenario' => DpAllMerchandisePresentations::SCENARIO_WITHOUT_STOCK,
                'attributes' => array(
                    array('name' => 'product_id', 'width' => 10, 'types' => array('id', 'value', 'aux'), 'not_in' => "window.sianPromotionIds"),
                    array('name' => 'product_type', 'width' => 5),
                    array('name' => 'product_name', 'width' => 40, 'types' => array('text')),
                    array('name' => 'model', 'hidden' => true),
                    array('name' => 'part_number', 'hidden' => true),
                    array('name' => 'currency', 'hidden' => true, 'search' => false),
                    array('name' => 'currency_name', 'width' => 5, 'search' => false),
                    array('name' => 'equivalence', 'hidden' => true, 'search' => false),
                    array('name' => 'allow_decimals', 'hidden' => true, 'search' => false),
                    array('name' => 'measure_name', 'width' => 10, 'search' => false),
                    array('name' => 'mprice', 'hidden' => true),
                    array('name' => 'imprice', 'width' => 10),
                    array('name' => 'aprice', 'hidden' => true),
                    array('name' => 'iaprice', 'width' => 10),
                    array('name' => 'wprice', 'hidden' => true),
                    array('name' => 'iwprice', 'width' => 10),
                ),
                'params' => "{
                        currency: function () { 
                            return $('#{$this->currency_input_id}').val()
                        }
                    }",
            ),
            'maintenance' => array(
                'module' => 'logistic',
                'controller' => 'product',
                'buttons' => array(
                    'preview' => array(
                        'access' => $this->preview_access,
                    ),
                    'create' => [],
                    'update' => [],
                ),
            ),
            "onselect" => "
                    var divObj = $('#{$this->id}');
                    
                    var step = (allow_decimals == 0 ? 1 : 0.01);

                    $('#{$this->item_product_type_id}').val(product_type);
                    $('#{$this->item_allow_decimals_id}').val(allow_decimals);

                    SIANPromotionGetPresentations('{$this->id}', product_id, equivalence);
                        
                    SIANPromotionBeforeAdd('{$this->id}', product_id, function(data) {
                        if(isset(data) && isset(data[product_id]))
                        {
                            $('#{$this->item_sum_unit_stock_id}').floatVal(2, data[product_id].sstock);
                            $('#{$this->item_sum_mixed_stock_id}').text(data[product_id].mixed_sstock);
                        } else {
                            $('#{$this->item_sum_unit_stock_id}').val(0);
                            $('#{$this->item_sum_mixed_stock_id}').text(0);                        
                        }
                    });
                    ",
            'onreset' => "
                    $('#{$this->item_equivalence_id}').html('<option value>" . Strings::SELECT_OPTION . "</option>'); 
                    $('#{$this->item_sum_unit_stock_id}').val(0);
                    $('#{$this->item_sum_mixed_stock_id}').text(0);
                    $('#{$this->item_allow_decimals_id}').val(0);
                    $('#{$this->item_cost_id}').val(0);
                    $('#{$this->item_icost_id}').val(0);
                    $('#{$this->item_mprice_id}').val(0);
                    $('#{$this->item_imprice_id}').val(0);
                    $('#{$this->item_aprice_id}').val(0);
                    $('#{$this->item_iaprice_id}').val(0).attr('min', 0);
                    $('#{$this->item_wprice_id}').val(0);
                    $('#{$this->item_iwprice_id}').val(0);
                "
                ), true);
        echo "</div>";

        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-6'>";
        $this->renderCol2();
        echo "</div>";

        echo "</div>";
    }

    private function renderCol2() {

        echo "<div class=row'>";
        //
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-6'>";
        echo SIANForm::dropDownListNonActive('Tipo', null, null, Product::getTypeItems(), array(
            'id' => $this->item_product_type_id,
            'disabled' => true
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_allow_decimals_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_cost_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_icost_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_mprice_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_imprice_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_aprice_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_iaprice_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_wprice_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_iwprice_id,
        ));

        echo "</div>";
        //
        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-6'>";
        echo SIANForm::dropDownListNonActive('Pres.', null, null, [], array(
            'id' => $this->item_equivalence_id,
            'empty' => Strings::SELECT_OPTION,
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_sum_unit_stock_id,
            'disabled' => true,
        ));
        echo CHtml::label('Stock', $this->item_sum_mixed_stock_id);
        echo "<br><span id='{$this->item_sum_mixed_stock_id}'>0</span>";
        echo "</div>";
        //
        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12 text-center'>";
        echo CHtml::label('Agregar', $this->add_button_id, []);
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_button_id,
            'context' => 'primary',
            'block' => true,
            'icon' => 'fa fa-lg fa-plus white',
            'size' => 'default',
            'title' => 'Añadir'
        ));
        echo "</div>";
        //
        echo "</div>";
    }

    private function renderTable() {

        $s_product_name_width = $this->show_web_price ? 0 : 5;

        $s_html = '';
        $s_html .= '<thead>';
        $s_html .= "<tr>";
        $s_html .= "<th width='2%'>#</th>";
        $s_html .= "<th width='3%'>{$this->model->getAttributeLabel('items.product_id')}</th>";
        $s_html .= "<th width='" . (18 + $s_product_name_width) . "%'>{$this->model->getAttributeLabel('items.product_name')}</th>";
        $s_html .= "<th width='6%'>{$this->model->getAttributeLabel('items.equivalence')}</th>";
        $s_html .= "<th width='5%' class='sian-promotion-without-igv' " . ($this->model->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">Costo <span class = 'currency-symbol'></span></th>";
        $s_html .= "<th width='5%' class='sian-promotion-with-igv' " . ($this->model->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">Costo <span class = 'currency-symbol'></span> (Inc. IGV)</th>";
        $s_html .= "<th width='5%' class='sian-promotion-without-igv' " . ($this->model->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">" . $this->mprice_label . " <span class = 'currency-symbol'></span></th>";
        $s_html .= "<th width='5%' class='sian-promotion-with-igv' " . ($this->model->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">" . $this->mprice_label . " <span class = 'currency-symbol'></span> (Inc. IGV)</th>";
        $s_html .= "<th width='5%' class='sian-promotion-without-igv' " . ($this->model->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">" . $this->aprice_label . " <span class = 'currency-symbol'></span></th>";
        $s_html .= "<th width='5%' class='sian-promotion-with-igv' " . ($this->model->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">" . $this->aprice_label . " <span class = 'currency-symbol'></span> (Inc. IGV)</th>";
        $s_html .= "<th width='5%' class='sian-promotion-without-igv sian-promotion-web' " . ($this->model->price_mode == 0 && $this->show_web_price ? '' : 'style=\'display: none;\'') . ">P. Web. <span class = 'currency-symbol'></span></th>";
        $s_html .= "<th width='5%' class='sian-promotion-with-igv sian-promotion-web' " . ($this->model->price_mode == 1 && $this->show_web_price ? '' : 'style=\'display: none;\'') . ">P. Web. <span class = 'currency-symbol'></span> (Inc. IGV)</th>";
        $s_html .= "<th width='5%'>{$this->model->getAttributeLabel('items.carry')}</th>";
        $s_html .= "<th width='5%'>{$this->model->getAttributeLabel('items.pay')}</th>";
        $s_html .= "<th width='6%'>{$this->model->getAttributeLabel('items.discount_percent')}*</th>";
        $s_html .= "<th width='7%' class='sian-promotion-without-igv' " . ($this->model->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">Prec real <span class = 'currency-symbol'></span> (1)</th>";
        $s_html .= "<th width='7%' class='sian-promotion-with-igv' " . ($this->model->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">Prec real <span class = 'currency-symbol'></span> (Inc. IGV) (1)</th>";
        $s_html .= "<th width='7%' class='sian-promotion-without-igv' " . ($this->model->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">Prec cliente <span class = 'currency-symbol'></span> (2)</th>";
        $s_html .= "<th width='7%' class='sian-promotion-with-igv' " . ($this->model->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">Prec cliente <span class = 'currency-symbol'></span> (Inc. IGV) (2)</th>";
        $s_html .= "<th width='5%' class='sian-promotion-without-igv' " . ($this->model->price_mode == 0 ? '' : 'style=\'display: none;\'') . ">Total <span class = 'currency-symbol'></span> (3)</th>";
        $s_html .= "<th width='5%' class='sian-promotion-with-igv' " . ($this->model->price_mode == 1 ? '' : 'style=\'display: none;\'') . ">Total <span class = 'currency-symbol'></span> (Inc. IGV) (3)</th>";
        $s_html .= "<th width='5%'>Σ stock (4)</th>";
        $s_html .= "<th width='6%'>{$this->model->getAttributeLabel('items.pres_stock')}</th>";
        $s_html .= "<th width='5%' style='text-align:right'></th>";
        $s_html .= "</tr>";
        $s_html .= "</thead>";
        $s_html .= "<tbody>";
        $s_html .= "</tbody>";
        $s_html .= "<tfoot>";
        $s_html .= "<tr>";
        $s_html .= "<td colspan=8 style='text-align:right'><b>Masivo:</b></td>";
        $s_html .= "<td>";
        $s_html .= SIANForm::numberFieldNonActive(false, null, 1, [
                    'class' => 'form-control sian-promotion-total-carry us-double0',
                    'style' => 'text-align:right',
                    'step' => 1,
                    'min' => 1,
                    'onchange' => "SIANPromotionChangeTotalPrice('{$this->id}', 'carry')"
        ]);
        $s_html .= "</td>";
        $s_html .= "<td>";
        $s_html .= SIANForm::numberFieldNonActive(false, null, 1, [
                    'class' => 'form-control sian-promotion-total-pay us-double0',
                    'style' => 'text-align:right',
                    'step' => 1,
                    'min' => 1,
                    'onchange' => "SIANPromotionChangeTotalPrice('{$this->id}', 'pay')"
        ]);
        $s_html .= "</td>";
        $s_html .= "<td>";
        $s_html .= SIANForm::numberFieldNonActive(false, null, 0, [
                    'class' => 'form-control sian-promotion-total-discount-percent us-integer',
                    'style' => 'text-align:right',
                    'min' => -100,
                    'max' => 100,
                    'onchange' => "SIANPromotionChangeTotalDiscountPercent('{$this->id}')"
        ]);
        $s_html .= "</td>";
        $s_html .= "<td colspan=99>← Los valores que especifique aquí se replicarán en todos los ítems</td>";
        $s_html .= "</tr>";
        $s_html .= "</tfoot>";

        return $s_html;
    }

}
