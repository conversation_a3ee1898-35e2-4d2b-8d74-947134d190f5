<?php

$arrayItemsNavbar = array(
    // array('icon' => 'fa fa-star', 'label' => 'Organización', 'url' => '#', 'items' => array(
    array('label' => 'Organización', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'fa fa-star', 'label' => 'Organización', 'route' => 'organization/index'),
            ),
            array(
                array('icon' => 'list', 'label' => 'Campañas web', 'route' => 'webCampaing/index'),
            ),
            array(
                array('icon' => 'fa fa-inbox', 'label' => 'Boletines', 'route' => 'newsletter/index'),
            ),
        )
    ),
    // array('icon' => 'list', 'label' => 'Clasificación', 'url' => '#', 'items' => array(
    array('label' => 'Clasificación', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'list', 'label' => 'Divisiones', 'route' => 'division/index'),
                array('icon' => 'list', 'label' => 'Líneas', 'route' => 'line/index'),
                array('icon' => 'list', 'label' => 'Sublíneas', 'route' => 'subline/index'),
            ),
            array(
                array('icon' => 'star', 'label' => 'Marcas', 'route' => 'mark/index'),
            ),
            array(
                array('icon' => 'barcode', 'label' => 'Presentaciones', 'route' => 'presentation/index'),
                array('icon' => 'barcode', 'label' => 'Mercaderías', 'route' => 'merchandise/index'),
                array('icon' => 'wrench', 'label' => 'Servicios', 'route' => 'service/index'),
                array('icon' => 'qrcode', 'label' => 'Combos', 'route' => 'combo/index'),
            ),
            array(
                array('icon' => 'ok-circle', 'label' => 'Atributos', 'route' => 'feature/index'),
            ),
        )
    ),
);
$this->widget('application.widgets.SIANNavbar', array(
    'brand' => Yii::app()->id,
    'class' => 'navbar-marketing',
    'items' => $this->items_menu, //$arrayItemsNavbar,
));
