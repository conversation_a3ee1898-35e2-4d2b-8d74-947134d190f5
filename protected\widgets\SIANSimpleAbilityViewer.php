<?php

class SIANSimpleAbilityViewer extends CWidget {

    public $id;
    public $model;
    public $items = [];
    public $title;
    public $has_access = null;
    public $true_label = 'Cumple';
    public $false_label = 'No cumple';
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->true_label = 'Cumple';
        $this->false_label = 'No cumple';

        $a_attributes = [];

        if (isset($this->has_access)) {
            $a_attributes[] = [
                'label' => 'Tienes acceso?',
                'type' => 'raw',
                'value' => $this->widget('application.widgets.USBooleanLabel', array(
                    'value' => $this->has_access,
                    'true_label' => $this->true_label,
                    'false_label' => $this->false_label,
                        ), true)
            ];
        }

        foreach ($this->items as $a_item) {

            $s_value = "";
            if (is_array($a_item)) {
                $label = 'Al menos una de estas condiciones';
                foreach ($a_item as $m_subitem) {

                    $a_subitem = [];
                    if (is_array($m_subitem)) {
                        $a_subitem = $m_subitem;
                    } else {
                        $a_subitem = [$m_subitem];
                    }

                    $a_sublabels = [];
                    $m_subvalue = 1;
                    foreach ($a_subitem as $part) {
                        $a_sublabels[] = $this->model->getAttributeLabel($part);
                        $m_subvalue = $m_subvalue && $this->model->getRecursiveAttribute($part);
                    }

                    $s_value .= "<p>" . $this->widget('application.widgets.USBooleanLabel', array(
                                'value' => $m_subvalue,
                                'true_label' => $this->true_label,
                                'false_label' => $this->false_label,
                                    ), true) . "   " . implode(' y ', $a_sublabels) . "</p>";
                }
            } else {
                $label = $this->model->getAttributeLabel($a_item);
                $s_value = $this->widget('application.widgets.USBooleanLabel', array(
                    'value' => $this->model->getRecursiveAttribute($a_item),
                    'true_label' => $this->true_label,
                    'false_label' => $this->false_label,
                        ), true);
            }

            if (strlen($s_value) > 0) {
                $a_attributes[] = [
                    'label' => $label,
                    'type' => 'raw',
                    'value' => $s_value
                ];
            }
        }

        echo "<h5>{$this->title}</h5>";
        $this->widget('booster.widgets.TbDetailView', array(
            'id' => $this->id,
            'data' => $this->model,
            'attributes' => $a_attributes
        ));
    }

}
