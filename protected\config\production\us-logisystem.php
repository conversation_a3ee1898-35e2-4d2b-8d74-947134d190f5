<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'logisystem.siansystem.com/admin';
$domain = 'https://logisystem.siansystem.com';
$domain2 = 'http://logisystem.siansystem.com';
$report_domain = 'rlogisystem.siansystem.com';
$org = 'logisystem';
//SIAN 2
$domain_react_app = 'https://logisystem2.siansystem.com';
$api_sian = 'https://api.siansystem.com/';
//database enterprise
$database_server = '161.132.48.88';
$database_name = 'logisystem';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_EFACT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = 'logisystem.p12';
$e_billing_certificate_pass = '6ESXHQkBvcSzDRFD';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20481818991BILLING7',
        'password' => 'Billing7'
    ],
    YII_OSE_EFACT => [
        'username' => '20481818991',
        'password' => 'TCfToUsYV0'
    ]
];
$e_billing_rest_credentials = [
    'client_id' => '8abdcc8a-7dcc-4438-a6c4-b234c74a2e2a',
    'client_secret' => 'kmaAgaj/J7ktMdMdAWWGtw=='
];
$e_billing_send_automatically_to_client = false;
//
$smtp_username = '<EMAIL>';
$smtp_password = '75neazv782ca38';
$environment_reports = YII_ENVIRONMENT_PRODUCTION;
$environment = YII_ENVIRONMENT_PRODUCTION;
$admin_emails = array(
    'Logisystem SIAN' => '<EMAIL>',
);
