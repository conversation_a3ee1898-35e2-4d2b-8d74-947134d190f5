<?php

class SIANPayrollSelector extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $child_modal_id;
    //PRIVATE
    private $controller;
    private $regime_input_id;
    private $year_input_id;
    private $month_input_id;
    private $week_input_id;
    private $split_week_input_id;
    private $payroll_input_id;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->modal_id = isset($this->modal_id) ? $this->modal_id : $this->controller->getServerId();
        $this->child_modal_id = isset($this->child_modal_id) ? $this->child_modal_id : $this->controller->getServerId();
        //--      
        $this->regime_input_id = $this->controller->getServerId();
        $this->year_input_id = $this->controller->getServerId();
        $this->week_input_id = $this->controller->getServerId();
        $this->split_week_input_id = $this->controller->getServerId();
        $this->month_input_id = $this->controller->getServerId();
        $this->payroll_input_id = $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-assistance-summary.js');
        SIANAssets::registerScriptFile('other/select2/js/select2.js');
        SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
        SIANAssets::registerCssFile('other/select2/css/select2.css');

        $regimeList = CJSON::encode(Regime::getListData());
        $weekList = CJSON::encode(Calendar::getListWeekYear());

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        //Seteamos data
        var div = $('#{$this->id}');
        div.data('regimeList', {$regimeList});
        div.data('weekList', {$weekList});
      
        $(document).ready(function() {    
                
            var regime_id =  $('#{$this->regime_input_id}').val();
            if(regime_id != null){
                var data = $('#{$this->id}').data('regimeList')[regime_id];
                $('#{$this->id}').data('type_period',data['type_period']);
                $('#{$this->id}').find('thead').remove();
                if(data['type_period'] == '" . Regime::TYPE_PERIOD_MONTHLY . "'){
                    $('#{$this->week_input_id}').find('option').remove();
                    $('#{$this->week_input_id}').attr('readonly', true);
                    $('#{$this->split_week_input_id}').prop('checked', false);                         
                }
                if(data['type_period'] == '" . Regime::TYPE_PERIOD_WEEKLY . "'){
                    $('#{$this->week_input_id}').removeAttr('readonly');
                    SIANLoadWeeks('');
                }                
                
            }
            // ------------------------------

            var xyear  = $('#{$this->year_input_id}').val();
            var xmonth = $('#{$this->month_input_id}').val();
            var xweek  = $('#{$this->week_input_id}').val();

            $.ajax({
                type: 'post',
                url: '" . Yii::app()->controller->createUrl('/project/period/datesInPeriod') . "',
                data: {
                    year: xyear, month: xmonth, week: xweek
                },
                async: false,
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },                  
                success: function(data) {
                    if(data.code == " . USREST::CODE_SUCCESS . ")
                    {
                        $('#{$this->id}').data('datesInPeriod', data.data);                                 
                    }
                    else
                    {
                        bootbox.alert(us_message(data.message, 'warning'));
                    }
                    window.active_ajax--;
                },
                error: function(request, status, error) { // if error occured
                    bootbox.alert(us_message(request.responseText, 'error'));
                    window.active_ajax--;
                },
                dataType: 'json'
            }); 
            
            if(!$('#{$this->week_input_id}').hasAttr('readonly')){
                SIANLoadWeeks('');
                //$('#{$this->week_input_id}').change();
            } 

        });
        
        // ----------------------------------
        // ------- Eventos de elementos -----
        // ----------------------------------      
        
        $('body').on('change','#{$this->regime_input_id}', function(){
            var regime_id =  $('#{$this->regime_input_id}').val();               
            if(regime_id != null){
                var data = $('#{$this->id}').data('regimeList')[regime_id];
                $('#{$this->id}').data('type_period',data['type_period']);
                $('#{$this->id}').find('thead').remove();
                if(data['type_period'] == '" . Regime::TYPE_PERIOD_MONTHLY . "'){
                    $('#{$this->week_input_id}').find('option').remove();
                    $('#{$this->week_input_id}').attr('readonly', true);
                    $('#{$this->split_week_input_id}').prop('checked', false);                                    
                }
                if(data['type_period'] == '" . Regime::TYPE_PERIOD_WEEKLY . "'){
                    $('#{$this->week_input_id}').removeAttr('readonly');
                    SIANLoadWeeks('');                   
                }               
            }
        });            

        $('body').on('change','#{$this->week_input_id}', function(){
            var year =  $('#{$this->year_input_id}').val();
            var month =  $('#{$this->month_input_id}').val();
            var data = $('#{$this->id}').data('weekList');
            var split_week = data[year][month][this.value] == 1? true : false;
            $('#{$this->split_week_input_id}').prop('checked', split_week);
        });

        // ----------------------------------
        // ------- Eventos de clases --------
        // ----------------------------------

        $('body').on('change','#{$this->id} .sian-change-week', function(){
            if(!$('#{$this->week_input_id}').hasAttr('readonly')){
                SIANLoadWeeks('');   
                //$('#{$this->week_input_id}').change();
            }               
        });

        $('body').on('change','#{$this->id} .sian-change-dates', function(){
            var xyear  = $('#{$this->year_input_id}').val();
            var xmonth = $('#{$this->month_input_id}').val();
            var xweek  = $('#{$this->week_input_id}').val();

            $.ajax({
                type: 'post',
                url: '" . Yii::app()->controller->createUrl('/project/period/datesInPeriod') . "',
                data: {
                    year: xyear, month: xmonth, week: xweek
                },
                async: true,
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },                   
                success: function(data) {
                    if(data.code == " . USREST::CODE_SUCCESS . ")
                    {
                        $('#{$this->id}').data('datesInPeriod', data.data);                                 
                    }
                    else
                    {
                        bootbox.alert(us_message(data.message, 'warning'));
                    }
                    window.active_ajax--;
                },
                error: function(request, status, error) { // if error occured
                    bootbox.alert(us_message(request.responseText, 'error'));
                    window.active_ajax--;
                },
                dataType: 'json'
            });  
            
            SIANLoadPayroll();
        });
        
        ", CClientScript::POS_END);

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "    
      
        function SIANLoadWeeks(current){
        
            var xyear = $('#{$this->year_input_id}').val();
            var xmonth = $('#{$this->month_input_id}').val();
                
            $.ajax({
                    type: 'post',
                    url: '" . Yii::app()->controller->createUrl('/components/listWeek') . "',
                    data: {
                        year: xyear, month: xmonth,
                    },
                    beforeSend: function (xhr) {
                        window.active_ajax++;
                        //Ocultamos los tooltip
                        $('div.ui-tooltip').remove();
                    },                      
                    success: function(data) {
                        if(data.code == " . USREST::CODE_SUCCESS . ")
                        {
                            var xdata = data.data,  l = xdata.length, i;
                            $('#{$this->week_input_id}').find('option').remove();
                            if (l > 0) {
                                $('#{$this->week_input_id}').append($(document.createElement('option')).attr('value', 0).text('Todas'));
                                var found = false;
                                for (i = 0; i < l; i++) {   
                                   $('#{$this->week_input_id}').append($(document.createElement('option')).attr('value', xdata[i].id).text(xdata[i].value));
                                    
                                    if (xdata[i].id === current)
                                    {
                                        found = true;
                                    }    
                                }
                                if (found)
                                {
                                    $('#{$this->week_input_id}').val(current);
                                }
                                $('#{$this->week_input_id}').change();
                            }      
                        }
                        else
                        {
                            bootbox.alert(us_message(data.message, 'warning'));
                        }
                        window.active_ajax--;
                    },
                    error: function(request, status, error) { // if error occured
                        bootbox.alert(us_message(request.responseText, 'error'));
                        window.active_ajax--;
                    },
                    dataType: 'json'
                });
            
        }
        
        function SIANLoadPayroll(){
            var xregime = $('#{$this->regime_input_id}').val();
            var xyear = $('#{$this->year_input_id}').val();
            var xmonth = $('#{$this->month_input_id}').val();
            var xweek = $('#{$this->week_input_id}').val();
                
            $.ajax({
                    type: 'post',
                    url: '" . Yii::app()->controller->createUrl('/components/listPayroll') . "',
                    data: {
                        regime: xregime, year: xyear, month: xmonth, week: xweek
                    },
                    beforeSend: function (xhr) {
                        window.active_ajax++;
                        //Ocultamos los tooltip
                        $('div.ui-tooltip').remove();
                    },
                    success: function(data) {
                        if(data.code == " . USREST::CODE_SUCCESS . ")
                        {
                            var xdata = data.data,  l = xdata.length, i;
                            $('#{$this->payroll_input_id}').find('option').remove();
                            if (l > 0) {
                                $('#{$this->payroll_input_id}').append($(document.createElement('option')).attr('value', 0).text('Todas'));
                                var found = false;
                                for (i = 0; i < l; i++) {   
                                   $('#{$this->payroll_input_id}').append($(document.createElement('option')).attr('value', xdata[i].id).text(xdata[i].value));                                   
                                }                               
                                $('#{$this->payroll_input_id}').change();
                            }      
                        }
                        else
                        {
                            bootbox.alert(us_message(data.message, 'warning'));
                        }
                        window.active_ajax--;
                    },
                    error: function(request, status, error) { // if error occured
                        bootbox.alert(us_message(request.responseText, 'error'));
                        window.active_ajax--;
                    },
                    dataType: 'json'
                });
            
        }
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='$this->id'>";

        echo "<div class='col-lg-2 col-md-2 col-sm-3 col-xs-12'>";
        echo CHtml::label('Régimen', $this->regime_input_id);
        echo CHtml::dropDownList('regime_id', '', Regime::getRegime('id'), array('id' => $this->regime_input_id,
            'class' => 'sian-change-dates form-control'));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo CHtml::label('Año', $this->year_input_id);
        echo CHtml::dropDownList('year', '', Calendar::getListYear(), array('id' => $this->year_input_id,
            'class' => 'sian-change-week sian-change-dates form-control'));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo CHtml::label('Mes', $this->month_input_id);
        echo CHtml::dropDownList('period', '', USTime::getMonths(false), array('id' => $this->month_input_id,
            'class' => 'sian-change-week sian-change-dates form-control'));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12' >";
        echo CHtml::label('Semana', $this->week_input_id);
        echo CHtml::dropDownList('week', '', [], array('id' => $this->week_input_id,
            'class' => 'sian-change-dates form-control'));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-2 col-xs-12' >";
        echo CHtml::label('Planilla', $this->payroll_input_id);
        echo CHtml::dropDownList('payroll', '', [], array(
            'id' => $this->payroll_input_id,
            'class' => ' form-control',
            'required' => true
        ));
        echo "</div>";

        echo "</div>";
    }

}
