<?php

class SIANProductListItems extends CWidget {

    public $id;
    public $model;
    public $dataProvider;
    public $route = '/logistic/product/preview';
    public $title = 'Lista de productos';
    public $show_amounts = false;
    public $show_quantity_balance = false;
    public $show_bar_code = false;
    public $show_combinations = false;
    public $modal_id = null;
    public $delivery_date_by_item = false;
    public $show_transformation_type = false;
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //CONTROL
        if (is_null($this->model)) {
            throw new Exception("Debe especificar la instancia del modelo 'CommercialMovement'!");
        }
        if (is_null($this->dataProvider)) {
            throw new Exception("Debe especificar el dataProvider!");
        }
        //
        $this->dataProvider->pagination = false;
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->dataProvider->getTotalItemCount() > 0) {

            echo "<h4>{$this->title}:</h4>";
            //Accesos
            $grid_id = $this->id;
            $route = $this->route;
            $access1 = $this->controller->checkRoute($route);
            $access2 = $this->controller->checkRoute('/logistic/merchandise/kardex');
            $modal_id = $this->modal_id;
            $currency = $this->model->currency;
            //GRID
            $columns = [];

            array_push($columns, array(
                'name' => 'item_number',
                'headerHtmlOptions' => array('style' => 'width:2%; text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center;'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->item_number;
                },
            ));

            array_push($columns, array(
                'name' => 'product_id',
                'headerHtmlOptions' => array('style' => 'width:3%; text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->product_id;
                },
            ));

            array_push($columns, array(
                'name' => 'product_name',
                'headerHtmlOptions' => array('style' => 'text-align:left;'),
                'type' => 'raw',
                'value' => function ($row) use ($access1, $route, $modal_id) {
                    return in_array($row->product_type, [Product::TYPE_NEW, Product::TYPE_GROUP]) ? $row->product_name : Yii::app()->controller->widget('application.widgets.USLink', array(
                        'route' => $route,
                        'label' => $row->product_name,
                        'title' => 'Ver producto',
                        'class' => 'form',
                        'data' => array(
                            'id' => $row->product_id,
                            'parent_id' => $modal_id,
                        ),
                        'visible' => $access1,
                            ), true);
                },
            ));

            if ($this->show_bar_code) {

                array_push($columns, array(
                    'name' => 'Codbar.',
                    'headerHtmlOptions' => array('style' => 'text-align:left;'),
                    'htmlOptions' => array('style' => 'text-align:left'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        return isset($row->barcode) ? $row->barcode : '-';
                    },
                ));
            }

            if ($this->model->movement->route == 'logistic/prePurchaseOrder') {
                array_push($columns, array(
                    'header' => 'Descripción Original',
                    'name' => 'description',
                    'headerHtmlOptions' => array('style' => 'width:15%; text-align:left;', 'label' => 'Descripción Original'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        return $row->product_type_original == Product::TYPE_NEW ? $row->description : Strings::NONE;
                    },
                ));
            } else {
                array_push($columns, array(
                    'name' => 'description',
                    'headerHtmlOptions' => array('style' => 'width:15%; text-align:left;', 'label' => 'Descripción Original'),
                    'type' => 'raw',
                ));
            }

            if ($this->show_transformation_type) {
                array_push($columns, array(
                    'name' => 'transform_type',
                    'headerHtmlOptions' => array('style' => 'width:15%; text-align:left;', 'label' => 'Tipo de Transformación'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        return ProductListMovement::getTransformationType($row->transform_type);
                    },
                ));
            }

            if ($this->show_combinations) {
                array_push($columns, array(
                    'name' => 'combination_name',
                    'headerHtmlOptions' => array('style' => 'width:10%;text-align:center;'),
                    'htmlOptions' => array('style' => 'text-align:center'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        return $row->combination_name;
                    },
                ));
            }

            if ($this->show_quantity_balance) {
                array_push($columns, array(
                    'name' => 'pres_balance',
                    'headerHtmlOptions' => array('style' => 'width:5%;text-align:center;'),
                    'htmlOptions' => array('style' => 'text-align:center'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        $d_now = SIANTime::formatDate();
                        if (isset($row->delivery_date) && $row->pres_balance > 0 && SIANTime::substractDates($row->delivery_date, $d_now, true) <= 0) {
                            return '<p style="color:red;">' . $row->pres_balance . '</p>';
                        } else {
                            return $row->pres_balance;
                        }
                    }
                ));
            }

            if ($this->delivery_date_by_item) {
                array_push($columns, array(
                    'name' => 'delivery_date',
                    'headerHtmlOptions' => array('style' => 'width:5%;text-align:center;'),
                    'htmlOptions' => array('style' => 'text-align:center'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        $d_now = SIANTime::formatDate();
                        if (isset($row->delivery_date) && $row->pres_balance > 0 && SIANTime::substractDates($row->delivery_date, $d_now, true) <= 0) {
                            return '<p style="color:red;">' . $row->delivery_date . '</p>';
                        } else {
                            return $row->delivery_date;
                        }
                    },
                ));
            }

            array_push($columns, array(
                'name' => 'pres_quantity',
                'headerHtmlOptions' => array('style' => 'width:5%;text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->pres_quantity;
                },
            ));

            array_push($columns, array(
                'name' => 'measure_name',
                'headerHtmlOptions' => array('style' => 'width:5%;text-align:right;'),
                'htmlOptions' => array('style' => 'text-align:right'),
                'type' => 'raw',
                'value' => function ($row) {
                    return $row->measure_name;
                },
            ));

            if ($this->show_amounts) {
                array_push($columns, array(
                    'name' => "amount_{$currency}",
                    'headerHtmlOptions' => array('style' => 'width:5%;text-align:right;'),
                    'htmlOptions' => array('style' => 'text-align:right;'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        return $row->displayValue('amount', $row->currency, ProductListMovement::IFIXED);
                    },
                    'footerHtmlOptions' => array('style' => 'width:5%;text-align:right;'),
                    'footer' => '<b>TOTAL:</b>',
                ));

                array_push($columns, array(
                    'name' => "total_{$currency}",
                    'headerHtmlOptions' => array('style' => 'width:5%;text-align:right;'),
                    'htmlOptions' => array('style' => 'text-align:right'),
                    'type' => 'raw',
                    'value' => function ($row) {
                        return $row->displayValue('total', $row->currency, ProductListMovement::TFIXED);
                    },
                    'footerHtmlOptions' => array('style' => 'width:5%;text-align:right;'),
                    'footer' => $this->model['total_' . $this->model->movement->currency],
                ));
            }

            array_push($columns, array(
                'header' => 'Opc.',
                'type' => 'raw',
                'headerHtmlOptions' => array('style' => 'width:5%;text-align:center;'),
                'htmlOptions' => array('style' => 'text-align:center'),
                'value' => function ($row) use ($grid_id, $access2) {
                    //Si bloquea stock
                    if (isset($row->direction) && isset($row->warehouse_id) && $row->product_type === Product::TYPE_MERCHANDISE) {
                        $i_kardex_mode = '';
                        $s_label = '';
                        switch ($row->direction) {
                            case Kardex::DIRECTION_COMING_IN:
                                $i_kardex_mode = Kardex::MODE_PURCHASE;
                                $s_label = '(para compras)';
                                break;
                            case Kardex::DIRECTION_COMING_OUT:
                                $i_kardex_mode = Kardex::MODE_SALE;
                                $s_label = '(disponible)';
                                break;
                            default:
                                throw new Exception('Dirección de kardex no soportada');
                        }

                        return Yii::app()->controller->widget('application.widgets.USGridButtons', array(
                                    'id' => $row->product_id,
                                    'grid_id' => $grid_id,
                                    'buttons' => array(
                                        array('icon' => 'fa fa-retweet fa-lg', 'title' => 'Ver kardex ' . $s_label, 'route' => '/logistic/merchandise/kardex', 'params' => array('warehouse_id' => $row->warehouse_id, 'mode' => $i_kardex_mode), 'target' => $row->owner_id . "_" . $row->product_id, 'visible' => $access2),
                                    )), true);
                    }

                    return '-';
                },
            ));

            $gridParams = array(
                'id' => $this->id,
                'type' => 'hover condensed',
                'dataProvider' => $this->dataProvider,
                'enableSorting' => true,
                'selectableRows' => 0,
                'columns' => $columns,
                'template' => '{items}',
                'nullDisplay' => Strings::NONE,
            );

            $this->widget('application.widgets.USGridView', $gridParams);
        } else {
            echo Strings::NO_DATA;
        }
    }

}
