<?php

class SIANDictionaryConceptFormulate extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $data;
    //PRIVATE
    private $controller;
    private $const_numeric_id;
    private $const_text_id;
    private $function_sum_id;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->modal_id = isset($this->modal_id) ? $this->modal_id : $this->controller->getServerId();
        //PRIVATE      
        $this->const_numeric_id = $this->controller->getServerId();
        $this->const_text_id = $this->controller->getServerId();
        $this->function_sum_id = $this->controller->getServerId();

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-dictionary-concept-formulate.js');

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        $(document).ready(function() {
            SIANDictionaryConceptFormulateInit('{$this->id}', false);     
            //$('#{$this->id} li.list-group-item-warning').draggable();
            
           $('li.sdcf-function-used').droppable({
                accept: '.list-group-item-warning',
                drop: function (event, ui) {

                    var s_function = $(this).text();
                    var a_function = s_function.split('|');
                    var s_new_function = '';
                    if (a_function.length == 2) {
                        s_new_function = a_function[0].trim() + '|' + $(ui.draggable).text();
                        ui.draggable.remove();
                    }
                    if (s_new_function != '') {
                        $(this).text(s_new_function);
                    }
                }
            });
        });
        
        $('body').on('dblclick', '#{$this->id} li.sdcf-numeric-editable', function (e) {  
            SIANDictionaryConceptFormulateMakeEditable($(this), 'number', 'Número');
        });
        
        $('body').on('dblclick', '#{$this->id} li.sdcf-text-editable', function (e) {  
            SIANDictionaryConceptFormulateMakeEditable($(this), 'text', 'Texto');
        });
        
        $('body').on('click', '#{$this->id} ul.sdcf-factor-list li.sdcf-sortable-item', function (e) {  
            SIANDictionaryConceptFormulateAdd('{$this->id}', $(this));
        });

        $('body').on('click', '#{$this->id} ul.sdcf-factor-employee-list li.sdcf-sortable-item', function (e) {  
            SIANDictionaryConceptFormulateAdd('{$this->id}', $(this));
        });
        
        $('body').on('click', '#{$this->id} ul.sdcf-comparators li.sdcf-sortable-item', function (e) {  
            
            if($('#{$this->id}').find('input.sdcf-active-div-condition').prop('checked'))
            {
                SIANDictionaryConceptFormulateAdd('{$this->id}', $(this));
            }
        });

        $('body').on('click', '#{$this->id} ul.sdcf-operators li.sdcf-sortable-item', function (e) {  
            SIANDictionaryConceptFormulateAdd('{$this->id}', $(this));
        });
        
        $('body').on('click', '#{$this->id} ul.sdcf-concepts-list li.sdcf-sortable-item', function (e) {  
            SIANDictionaryConceptFormulateAdd('{$this->id}', $(this));
        });
        
        $('body').on('click', '#{$this->id} ul.sdcf-constant-list li.sdcf-sortable-item', function (e) {  
            SIANDictionaryConceptFormulateAdd('{$this->id}', $(this));
        });
        
        $('body').on('click', '#{$this->id} ul.sdcf-function-list li.sdcf-sortable-item', function (e) {
            SIANDictionaryConceptFormulateAdd('{$this->id}', $(this));
        });
        
        $('body').on('mouseenter', '#{$this->id} li.sdcf-remove', function (e) {

            if ($(this).children('i').get().length === 0) {
                $(this).append('<i class=\'fa fa-remove fa-1x pull-right\' style=\'color: grey;\'></i>');
            }
        });

        $('body').on('mouseleave', '#{$this->id} li.sdcf-remove', function (e) {
            $(this).children('i').remove();
        });
             
     
     
       ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array('title' => "Formulación",
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
            )
        ));
        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo "<span>&nbsp;FACTOR GENERAL</span>";
        echo "<ul class='sdcf-factor-list sdcf-connected-factors'>";
        foreach ($this->data['factors'] as $factor) {
            echo "<li class='list-group-item list-group-item-info sdcf-sortable-item' data-type='factorGeneral'>{$factor->short_name}</li>";
        }
        echo "</ul>";
        echo "<br><br><br><br>";
        echo "<span>&nbsp;FACTOR DE PERSONAL</span>";
        echo "<ul class='sdcf-factor-employee-list sdcf-connected-employee'>";
        foreach ($this->data['factorsEmployee'] as $factor) {
            echo "<li class='list-group-item list-group-item-success sdcf-sortable-item'  data-type='factorEmployee'>{$factor->short_name}</li>";
        }
        echo "</ul>";
        echo "</div> ";
        echo "<div class='col-lg-8 col-md-8 col-sm-8 col-xs-12'>";
        echo "<span><b>FÓRMULAR CONCEPTO</b></span>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12 sdcf-simple panel panel-default sdcf-removable'>";
        echo "<span><input type='radio' class='sdcf-active-div-radio sdcf-active-div-simple' name='activate_panel'>&nbsp;&nbsp;OPERACIÓN:</span>";
        echo "<ul class='sdcf-panel sdcf-simple-sentence sdcf-connected-factors sdcf-connected-employee sdcf-connected-concepts sdcf-connected-operators sdcf-background-grey'>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12 sdcf-condition panel panel-default sdcf-removable'>";
        echo "<span><input type='radio' class='sdcf-active-div-radio sdcf-active-div-condition' name='activate_panel'>&nbsp;&nbsp;CONDICIÓN:</span>";
        echo "<ul class='sdcf-panel sdcf-condition sdcf-connected-factors sdcf-connected-employee sdcf-connected-concepts sdcf-connected-operators sdcf-connected-comparators sdcf-background-grey'>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12 sdcf-condition'>";
        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-1'></div>";
        echo "<div class='col-lg-8 col-md-8 col-sm-8 col-xs-12 multiple_operators'>";
        echo "<ul class='sdcf-comparators sdcf-connected-comparators background_operators'>";
        echo "<li class='list-group-item list-group-item-secondary sdcf-operator-conditional sdcf-sortable-item' data-type='operator'>></li>";
        echo "<li class='list-group-item list-group-item-secondary sdcf-operator-conditional sdcf-sortable-item' data-type='operator'><</li>";
        echo "<li class='list-group-item list-group-item-secondary sdcf-operator-conditional sdcf-sortable-item' data-type='operator'>>=</li>";
        echo "<li class='list-group-item list-group-item-secondary sdcf-operator-conditional sdcf-sortable-item' data-type='operator'><=</li>";
        echo "<li class='list-group-item list-group-item-secondary sdcf-operator-conditional sdcf-sortable-item' data-type='operator'>=</li> ";
        echo "<li class='list-group-item list-group-item-secondary sdcf-operator-conditional sdcf-sortable-item' data-type='operator'><></li> ";
        echo "<li class='list-group-item list-group-item-secondary sdcf-operator-conditional sdcf-sortable-item' data-type='operator'>Y</li> ";
        echo "<li class='list-group-item list-group-item-secondary sdcf-operator-conditional sdcf-sortable-item' data-type='operator'>O</li> ";

        echo "</ul>";
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-1'></div>";
        echo "</div>                        ";
        echo "</div>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12 sdcf-condition panel panel-default' > ";
        echo "<span><input type='radio' class='sdcf-active-div-radio sdcf-active-div-yes' name='activate_panel'>&nbsp;&nbsp;SI ES VERDAD:</span>";
        echo "<ul class='sdcf-panel sdcf-true-sentence sdcf-connected-factors sdcf-connected-employee sdcf-connected-concepts sdcf-connected-operators sdcf-background-grey sdcf-removable'>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12 sdcf-condition panel panel-default' >   ";
        echo "<span><input type='radio' class='sdcf-active-div-radio sdcf-active-div-no' name='activate_panel'>&nbsp;&nbsp;SI ES FALSO</span>";
        echo "<ul class='sdcf-panel sdcf-false-sentence sdcf-connected-factors sdcf-connected-employee sdcf-connected-concepts sdcf-connected-operators sdcf-background-grey sdcf-removable'>";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12' >";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-1'></div>";
        echo "<div class='col-lg-9 col-md-9 col-sm-9 col-xs-12 sdcf-multiple-operators'>";
        echo "<ul class='sdcf-operators sdcf-connected-operators sdcf-background-operators'>";
        echo "<li class='list-group-item list-group-item-secondary sdcf-sortable-item' data-type='operator'>+</li>";
        echo "<li class='list-group-item list-group-item-secondary sdcf-sortable-item' data-type='operator'>-</li>";
        echo "<li class='list-group-item list-group-item-secondary sdcf-sortable-item' data-type='operator'>*</li>";
        echo "<li class='list-group-item list-group-item-secondary sdcf-sortable-item' data-type='operator'>/</li>";
        echo "<li class='list-group-item list-group-item-secondary sdcf-sortable-item' data-type='operator'>(</li> ";
        echo "<li class='list-group-item list-group-item-secondary sdcf-sortable-item' data-type='operator'>)</li> ";
        echo "<li class='list-group-item list-group-item-secondary sdcf-sortable-item' data-type='operator'>^</li> ";
        echo "</ul>";
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-1'></div>";
        echo "</div>";
        echo "</div>  ";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo "<span>CONSTANTE</span>";
        echo "<ul class='sdcf-constant-list sdcf-connected-concepts'>  ";
        echo "<li id='{$this->const_numeric_id}' class='list-group-item list-group-item-default sdcf-numeric-editable sdcf-sortable-item' data-type='constNumeric'>Número</li>";
        echo "<li id='{$this->const_text_id}' class='list-group-item list-group-item-default sdcf-text-editable sdcf-sortable-item' data-type='constText'>Texto</li>";
        echo "</ul>";
        echo "<br>";
        echo "<span>FUNCIONES</span>";
        echo "<ul class='sdcf-function-list sdcf-connected-concepts'>  ";
        echo "<li id='{$this->function_sum_id}' class='list-group-item list-group-item-default sdcf-function sdcf-function-sum sdcf-sortable-item' data-type='functionData'>Suma</li>";
        echo "</ul>";
        echo "<br>";
        echo "<span>CONCEPTOS</span>";
        echo "<ul class='sdcf-concepts-list sdcf-connected-concepts'> ";
        echo "</ul>";
        echo "</div>  ";
        echo "</div>";
        $this->endWidget();
    }

}
