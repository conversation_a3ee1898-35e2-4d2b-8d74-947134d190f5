<?php

class ComponentsController extends USController {

    private function normalizeArray($p_a_array) {
        $a_array = [];
        foreach ($p_a_array as $s_key => $s_value) {
            $a_array[] = [
                'id' => $s_key,
                'value' => $s_value,
            ];
        }

        return $a_array;
    }

    public function actionListDivision() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('di.division_id AS id,'
                            . 'di.division_name AS value')
                    ->from('division di')
                    ->where('di.status');

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListProductType() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {

            $query = Yii::app()->db->createCommand()
                    ->select('ml.multi_id AS id,' . 'ml.description as value')
                    ->from('multitable ml')
                    ->where("ml.value != 'Serv' AND ml.multi_parent_id = (SELECT multi_id FROM multitable WHERE value = '" . Product::ITEM_TYPE_VALUE . "')");

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListTypeMovement() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'CONCAT(pe.identification_number, \' - \', replace(pe.person_name,\',\',\' \')) AS value')
                    ->from('person pe')
                    ->order('pe.person_name')
                    ->where('pe.retention');

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListLine() {
//header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $idsDivision = !Util::isBlank($_POST['idsDivision']) ? $_POST['idsDivision'] : null;
            $q = isset($_POST['q']) ? $_POST['q'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('li.line_id AS id,'
                            . 'li.line_name AS value')
                    ->from('line li')
                    ->order('li.line_name')
                    ->where('li.status');

            if ($idsDivision != null)
                $query->andWhere(array('IN', 'li.division_id', $idsDivision));

            if ($q != null) {
                $query->andWhere("li.line_id LIKE '%$q%'OR li.line_name LIKE '%$q%'");
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListEntries() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $data = USArray::getComponentArray(Scenario::getTypes());

            $aResponse['data'] = $data;
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListSubline() {

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $idsLine = !Util::isBlank($_POST['idsLine']) ? $_POST['idsLine'] : null;
            $q = isset($_POST['q']) ? $_POST['q'] : null;
            $query = Yii::app()->db->createCommand()
                    ->select('su.subline_id AS id,'
                            . 'su.subline_name AS value')
                    ->from('subline su')
                    ->order('su.subline_name')
                    ->where('su.status');

            if ($idsLine != null)
                $query->andWhere(array('IN', 'su.line_id', $idsLine));

            if ($q != null) {
                $query->andWhere("su.subline_name LIKE '%$q%'");
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListItemType() {

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $q = isset($_POST['q']) ? $_POST['q'] : null;
            $query = Yii::app()->db->createCommand()
                    ->select('mh.multi_id AS id,'
                            . 'mh.description AS value')
                    ->from('multitable mh')
                    ->join('multitable mp', 'mp.multi_id = mh.multi_parent_id')
                    ->order('mh.order')
                    ->where("mp.`value` = '" . Multitable:: ITEM_TYPE . "' AND mh.value <> '" . Multitable::ITEM_TYPE_SERVICE . "'");

            if ($q != null) {
                $query->andWhere("mu.description LIKE '%$q%'");
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListMerchandise() {
//        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $idsSubline = isset($_POST['idsSubline']) ? $_POST['idsSubline'] : null;
            $idsLine = isset($_POST['idsLine']) ? $_POST['idsLine'] : null;
            $q = isset($_POST['q']) ? $_POST['q'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('me.product_id AS id,'
                            . 'CONCAT(me.product_id,\' - \',pr.product_name) AS value')
                    ->from('merchandise me')
                    ->join('product pr', 'me.product_id = pr.product_id')
                    ->join('subline su', 'me.subline_id = su.subline_id')
                    ->join('line li', 'li.line_id = su.line_id')
                    ->order('me.product_id')
                    ->where('pr.status');

            if ($idsLine != null)
                $query->andWhere(array('IN', 'li.line_id', $idsLine));

            if ($idsSubline != null)
                $query->andWhere(array('IN', 'me.subline_id', $idsSubline));


            if ($q != null) {
                $query->andWhere("me.product_id LIKE '%$q%' or pr.product_name LIKE '%$q%' ");
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    //mercaderias y servicios.
    public function actionListProduct() {
//        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $idsSubline = isset($_POST['idsSubline']) ? $_POST['idsSubline'] : null;
            $idsLine = isset($_POST['idsLine']) ? $_POST['idsLine'] : null;
            $q = isset($_POST['q']) ? $_POST['q'] : null;
            $types = isset($_POST['types']) ? $_POST['types'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pr.product_id AS id,' . 'CONCAT(pr.product_id,\' - \',pr.product_name) AS value')
                    ->from('product pr')
                    ->leftjoin('merchandise me', 'me.product_id = pr.product_id')
                    ->leftjoin('subline su', 'me.subline_id = su.subline_id')
                    ->leftjoin('line li', 'li.line_id = su.line_id')
                    ->order('pr.product_id')
                    ->where("pr.status AND pr.product_type IN ('M', 'S')");

            if ($idsLine != null)
                $query->andWhere(array('IN', 'li.line_id', $idsLine));

            if ($idsSubline != null)
                $query->andWhere(array('IN', 'me.subline_id', $idsSubline));


            if ($q != null) {
                $query->andWhere("pr.product_id LIKE '%$q%' or pr.product_name LIKE '%$q%' ");
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    //mercaderias, combos y servicios.
    public function actionListProductAll() {
//        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $idsSubline = isset($_POST['idsSubline']) ? $_POST['idsSubline'] : null;
            $idsLine = isset($_POST['idsLine']) ? $_POST['idsLine'] : null;
            $q = isset($_POST['q']) ? $_POST['q'] : null;
            $types = isset($_POST['types']) ? $_POST['types'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pr.product_id AS id,' . 'CONCAT(pr.product_id,\' - \',pr.product_name) AS value')
                    ->from('product pr')
                    ->leftjoin('merchandise me', 'me.product_id = pr.product_id')
                    ->leftjoin('subline su', 'me.subline_id = su.subline_id')
                    ->leftjoin('line li', 'li.line_id = su.line_id')
                    ->order('pr.product_id')
                    ->where("pr.status AND pr.product_type IN ('M', 'S', 'C')");

            if ($idsLine != null)
                $query->andWhere(array('IN', 'li.line_id', $idsLine));

            if ($idsSubline != null)
                $query->andWhere(array('IN', 'me.subline_id', $idsSubline));


            if ($q != null) {
                $query->andWhere("pr.product_id LIKE '%$q%' or pr.product_name LIKE '%$q%' ");
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListDocumentSerie() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $idDocumentType = !Util::isBlank($_POST['idDocumentType']) ? $_POST['idDocumentType'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('CONCAT_WS(\'-\',document_code, document_serie) AS id,'
                            . 'CONCAT(CONCAT_WS(\'-\',document_code, document_serie) ,\' \',document_serie_name) AS value,'
                            . 'status')
                    ->from('document_serie');

            if (!is_null($idDocumentType))
                $query->andWhere(array('IN', 'document_code', $idDocumentType));
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }
        echo CJSON::encode($aResponse);
    }

    public function actionListScenarioField() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $ids = count($_POST) == 0 ? [] : !Util::isBlank($_POST['a_ids']) ? $_POST['a_ids'] : [];
            $query = Yii::app()->db->createCommand()
                    ->select('CONCAT(`type`,\'-\',`field`) AS pk,`field`, `label`')
                    ->from('scenario_field')
                    ->andWhere('editable')
                    ->andWhere("`type` = '" . Scenario::TYPE_PAYROLL . "'");
            if (count($ids) > 0) {
                $concat_ids = implode('\',\'', $ids);
                $query->andWhere("`field` not in ('$concat_ids')");
            }
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }
        echo CJSON::encode($aResponse);
    }

    public function actionListOwner() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->selectDistinct('O.owner as id, O.owner_name as value')
                    ->from('owner O')
                    ->join('entry E', 'E.`owner` = O.`owner`');
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }
        echo CJSON::encode($aResponse);
    }

    public function actionListOwnerIds() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $owner = isset($_POST['owner']) ? $_POST['owner'] : NULL;
            $q = !Util::isBlank($_POST['q']) ? $_POST['q'] : null;
            $query = Yii::app()->db->createCommand()
                    ->select('OW.owner_id as id, CONCAT(OW.owner_aux, " - " , OW.owner_name) as value')
                    ->from('owner O')
                    ->join('owner_name OW', 'OW.owner = O.owner');

            if (isset($owner)) {
                $query->andWhere("OW.owner = :owner", [
                    ':owner' => $owner
                ]);
            }
            if ($q != null) {
                $query->andWhere("OW.owner_name LIKE '%$q%' OR OW.owner_id LIKE '%$q%' OR OW.owner_aux LIKE '%$q%'");
            }
            $query->limit(50);

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }
        echo CJSON::encode($aResponse);
    }

    public function actionListPlameCodes() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $ids = count($_POST) == 0 ? [] : !Util::isBlank($_POST['a_ids']) ? $_POST['a_ids'] : [];
            $query = Yii::app()->db->createCommand()
                    ->select('M1.multi_id AS pk, M1.`value` as `field`, CONCAT(M1.`value`, \' | \', M1.description) as `label`')
                    ->from('multitable M1 ')
                    ->join('multitable M2', 'M2.multi_id = M1.multi_parent_id')
                    ->join('multitable M3', 'M3.multi_id = M2.multi_parent_id')
                    ->andWhere("M3.`value` = ':value'", [
                ':value' => Multitable::PLAME
            ]);

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }
        echo CJSON::encode($aResponse);
    }

    public function actionListWarehouse() {
//        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $incoming = !Util::isBlank($_POST['incoming']) ? $_POST['incoming'] : null;
            $outgoing = !Util::isBlank($_POST['outgoing']) ? $_POST['outgoing'] : null;
            $commercial_treatment = !Util::isBlank($_POST['commercial_treatment']) ? $_POST['commercial_treatment'] : null;
            $warehouse_types = isset($_POST['warehouse_types']) ? !Util::isBlank($_POST['warehouse_types']) ? $_POST['warehouse_types'] : null : null;
            $stores_ids = isset($_POST['store_ids']) ? !Util::isBlank($_POST['store_ids']) ? $_POST['store_ids'] : null : null;
            $business_unit_ids = isset($_POST['business_unit_ids']) ? !Util::isBlank($_POST['business_unit_ids']) ? $_POST['business_unit_ids'] : null : null;

            $query = Yii::app()->db->createCommand()
                    ->select([
                        'wa.warehouse_id AS id',
                        'wa.warehouse_name AS value'
                    ])
                    ->from('warehouse wa')
                    ->join('store st', 'st.store_id = wa.store_id');

            if ($incoming != null)
                if ($incoming == 1)
                    $query->andWhere('wa.incoming');

            if ($outgoing != null)
                if ($outgoing == 1)
                    $query->andWhere('wa.outgoing');

            if ($commercial_treatment != null)
                if ($commercial_treatment == 1)
                    $query->andWhere('wa.commercial_treatment');

            if ($warehouse_types != null && count($warehouse_types) > 0) {
                $cad = implode('\',\'', $warehouse_types);
                $query->andWhere('wa.warehouse_type in (\'' . $cad . '\')');
            }

            if ($stores_ids != null && count($stores_ids) > 0) {
                $cad2 = implode(',', $stores_ids);
                $query->andWhere('wa.store_id in (' . $cad2 . ')');
            }

            if ($business_unit_ids != null && count($business_unit_ids) > 0) {
                $cad3 = implode(',', $business_unit_ids);
                $query->andWhere('st.business_unit_id in (' . $cad3 . ')');
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListWarehouse2() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $active = !Util::isBlank($_POST['active']) ? $_POST['active'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select([
                        'wa.warehouse_id AS id',
                        'wa.warehouse_name AS value'
                    ])
                    ->from('warehouse wa')
                    ->join('store st', 'st.store_id = wa.store_id');

            switch ($active) {
                case 1:
                    $query->andwhere('wa.status = 1');
                    break;
                default:
                    break;
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListFeatureBySubline() {
//        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $idSubline = !Util::isBlank($_POST['idSubline']) ? $_POST['idSubline'] : '0';

            $query = Yii::app()->db->createCommand()
                    ->select([
                        'F.feature_id AS id',
                        'F.feature_name AS value'
                    ])
                    ->from('owner_pair OP1')
                    ->join('owner_pair OP2', 'OP2.parent_id = OP1.pair_id AND OP2.owner = "' . Feature::OWNER . '" AND OP2.type = "' . OwnerPair::TYPE_SUBLINE_FEATURE . '"')
                    ->join('feature F', 'F.feature_id = OP2.owner_id')
                    ->where('OP1.owner = "' . Subline::OWNER . '" AND OP1.owner_id = ' . $idSubline . ' AND OP1.type = "' . OwnerPair::TYPE_SUBLINE_FEATURE . '"');

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListOperation() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $type = !Util::isBlank($_POST['type']) ? $_POST['type'] : null;
            $accountingFileId = !Util::isBlank($_POST['accountingFileId']) ? $_POST['accountingFileId'] : null;
            $module = !Util::isBlank($_POST['module']) ? $_POST['module'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('O.operation_code AS id,'
                            . 'O.operation_name AS value')
                    ->from('owner_pair op')
                    ->join('scenario S', 'S.scenario_id = op.owner_id')
                    ->join('owner_pair op2', 'op2.parent_id = op.pair_id')
                    ->join('operation O', "O.operation_id = op2.owner_id AND op2.`owner` = 'Operation'")
                    ->where("O.status and op.`owner` = 'Scenario' and op.type = 'ScenarioOperation'")
                    ->group('O.operation_code')
                    ->order('value');

            if ($type != null)
                $query->andWhere(array('IN', 'O.type', $type));

            if ($accountingFileId != null)
                $query->andWhere(array('IN', 'O.accounting_file_id', $accountingFileId));

            if ($module != null)
                $query->andWhere(array('IN', 'S.module', $module));

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

//multiples operaciones
    public function actionListOperationByRoute() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );
        try {
            $route = !Util::isBlank($_POST['route']) ? $_POST['route'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('O.operation_code AS id,'
                            . 'O.operation_name AS value')
                    ->from('owner_pair op')
                    ->join('scenario S', 'S.scenario_id = op.owner_id')
                    ->join('owner_pair op2', 'op2.parent_id = op.pair_id')
                    ->join('operation O', "O.operation_id = op2.owner_id AND op2.`owner` = 'Operation'")
                    ->where("O.status and op.`owner` = 'Scenario' and op.type = 'ScenarioOperation'")
                    ->group('O.operation_code')
                    ->order('value');

            if ($route != null)
                $query->andWhere(array('IN', 'S.route', $route));

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListDirection() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('direction AS id,'
                            . ' direction AS value')
                    ->from('operation op')
                    ->group('direction');

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListEmployee() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $ids = !array_key_exists('a_ids', $_POST) ? [] : !Util::isBlank($_POST['a_ids']) ? $_POST['a_ids'] : [];
            $regime_id = isset($_POST['regime_id']) ? $_POST['regime_id'] : NULL;
            $query = Yii::app()->db->createCommand()
                    ->select('e.`person_id` AS `id`,'
                            . ' CONCAT(p.`identification_number`,\' - \',LTRIM(REPLACE(`person_name`,\',\',\' \'))) AS `value`')
                    ->from('employee e')
                    ->join('person p', 'p.person_id = e.person_id')
                    ->join('regime r', 'r.regime_id = e.regime_id')
                    ->where('e.status = 1')
                    ->order('LTRIM(REPLACE(`person_name`,\',\',\' \'))');
            if (isset($regime_id)) {
                $query->andWhere('r.regime_id = ' . $regime_id);
            } else {
                //Default
                $query->andWhere('r.regime_code = "' . Regime::REGIME_EMP . '"');
            }
            if (count($ids) > 0) {
                $concat_ids = implode(',', $ids);
                $query->andWhere("p.person_id not in ($concat_ids)");
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListUser() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'REPLACE(pe.person_name, \',\', \' \') AS value')
                    ->from('user us')
                    ->join('person pe', 'pe.person_id = us.person_id')
                    ->where('us.status')
                    ->order('value');

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListEmployeeFromPayroll() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $idPayroll = !Util::isBlank($_POST['idPayroll']) ? $_POST['idPayroll'] : null;
            $q = !Util::isBlank($_POST['q']) ? $_POST['q'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . ' REPLACE(pe.person_name, \',\', \' \') AS value')
                    ->from('entry en')
                    ->join('movement mo', 'mo.movement_id = en.movement_id')
                    ->join('person pe', 'pe.person_id = en.owner')
                    ->where('mo.route = \'human/payroll\' AND en.owner = \'Person\'')
                    ->andWhere('pe.identification_type = \'1\'');

            if (!is_null($idPayroll))
                $query->andWhere(array('IN', 'en.movement_id', $idPayroll));

            $query->group('en.owner_id')
                    ->order('pe.person_name');
            ;
            if ($q != null) {
                $query->where("pe.person_id LIKE '%$q%' OR pe.person_name LIKE '%$q%'");
                $query->limit(10);
            }
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

//        echo $query->getText().'<br/>';exit();

        echo CJSON::encode($aResponse);
    }

    public function actionListDocumentType() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $type = array_key_exists('type', $_POST) && !Util::isBlank($_POST['type']) ? $_POST['type'] : null;
            $direction = array_key_exists('direction', $_POST) && !Util::isBlank($_POST['direction']) ? $_POST['direction'] : null;
            $isElectronic = isset($_POST['isElectronic']) && !Util::isBlank($_POST['isElectronic']) ? $_POST['isElectronic'] : 0;
            $isSerialized = isset($_POST['isSerialized']) && !Util::isBlank($_POST['isSerialized']) ? $_POST['isSerialized'] : null;
            $isThirdParty = isset($_POST['isThirdParty']) && !Util::isBlank($_POST['isThirdParty']) ? $_POST['isThirdParty'] : null;
            $isAccounting = isset($_POST['isAccounting']) && !Util::isBlank($_POST['isAccounting']) ? $_POST['isAccounting'] : null;
            // Contabilidad = Accounting         
            $query = Yii::app()->db->createCommand()->select('document_code as id, CONCAT(UPPER(document_code),"-", UPPER(document_name))  as value')
                    ->from('document d')
                    ->where('d.status = :status ', array(':status' => 1));
            if ($isAccounting != NULL)
                if ($isAccounting == 1)
                    $query->andWhere('d.accounting_file_id IS NOT NULL ');

            if ($isSerialized != NULL)
                $query->andWhere('d.serialized = ' . $isSerialized);

            if ($isThirdParty != NULL)
                $query->andWhere('d.third_party = ' . $isThirdParty);

            if ($type == 'Accounting') {
                $query->andWhere('d.accounting_file_id = "2" ');
            }

            if ($direction != null || $direction != '') {
                $query->andWhere('d.direction = :direction ', array(':direction' => $direction));
            }

            if (($type != null || $type != '') && $type != 'Accounting') {
                $query->andWhere('d.type = :type ', array(':type' => $type));
            }

            if ($isElectronic != null) {
                if ($isElectronic) {
                    $query->andWhere('d.is_electronic = :isElectronic ', array(':isElectronic' => $isElectronic));
                }
            }
            $aResponse ['data'] = $query->queryAll();
        } catch (Exception $exc) {

            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListDocumentTypeFromMovement() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $type = array_key_exists('type', $_POST) && !Util::isBlank($_POST['type']) ? $_POST['type'] : null;
            // Contabilidad = Accounting         
            $query = Yii::app()->db->createCommand()->select('d.document_code as id, CONCAT(UPPER(d.document_code),"-", UPPER(d.document_name))  as value')
                    ->from('movement m')
                    ->join('document d', 'd.document_code = m.document_code')
                    ->where("d.status = :status and m.status = :status", array(':status' => 1));

            if ($type == 'toPay') {
                $query->andWhere(" m.route IN ('accounting/manual', 'accounting/operatingCost', 'logistic/debitNote', 'financial/providerLetter', 'logistic/operatingCost', 'logistic/purchaseBill', 'logistic/mobility', 'treasury/prePay', 'human/payroll', 'accounting/fecline', 'accounting/leasing', 'accounting/loan')");
                $query->group('d.document_code');
            }

            if ($type == 'toCollect') {
                $query->andWhere(" m.route IN ('accounting/manual', 'commercial/debitNote', 'commercial/saleBill', 'financial/clientLetter', 'treasury/transferenceOut')");
                $query->group('d.document_code');
            }


            $aResponse ['data'] = $query->queryAll();
        } catch (Exception $exc) {

            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListCashbox() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            // Contabilidad = Accounting
            $query = Yii::app()->db->createCommand()->select('A.account_code AS id, 
  CONCAT(A.account_code," - ", A.account_name) AS `value`, 
  A.is_usable,
  A.level')
                    ->from('account A')
                    ->where("!A.is_wildcard")
                    ->group("A.account_code")
                    ->having("(account_code REGEXP '^10[1234]') AND (is_usable = 1 OR `level` = 3)")
                    ->order("A.account_code");

            $aResponse ['data'] = $query->queryAll();
        } catch (Exception $exc) {

            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListBusinessPartner() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $type = !Util::isBlank($_POST['type']) ? $_POST['type'] : null;
            $q = !Util::isBlank($_POST['q']) ? $_POST['q'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'CONCAT(pe.identification_number, \' - \', replace(pe.person_name,\',\',\' \')) AS value')
                    ->from('movement mo')
                    ->join('person pe', 'pe.person_id = mo.aux_person_id')
                    ->order('pe.person_name');

            switch ($type) {
                case "provider":
                    $query->where('mo.route  IN (\'accounting/manual\', \'accounting/operatingCost\', \'logistic/debitNote\', \'financial/providerLetter\', \'logistic/operatingCost\', \'logistic/purchaseBill\', \'treasury/prePay\') AND mo.status');
                    $query->group('mo.aux_person_id');
                    break;
                case "client":
                    $query->where('mo.route IN (\'commercial/saleBill\') AND mo.status');
                    $query->group('mo.aux_person_id');
                    break;
                case "clientConsignment":
                    $query->where('mo.route IN (\'commercial/consignmentOrder\') AND mo.status');
                    $query->group('mo.aux_person_id');
                    break;
                case "clientCredit":
                    $query->where('mo.route IN (\'commercial/saleBill\') AND mo.status AND pe.client_credit');
                    $query->group('mo.aux_person_id');
                    break;
                case "all":
                    $query->where('mo.route IN (\'accounting/manual\', \'accounting/operatingCost\', \'logistic/debitNote\', \'financial/providerLetter\', \'logistic/operatingCost\', \'logistic/purchaseBill\', \'treasury/prePay\',\'commercial/saleBill\') AND mo.status');
                    $query->group('mo.aux_person_id');
                case "associated":
                    $query = Yii::app()->db->createCommand()
                            ->select('pe.person_id AS id,'
                                    . 'CONCAT(pe.identification_number, \' - \', replace(pe.person_name,\',\',\' \')) AS value')
                            ->from('person pe')
                            ->order('pe.person_name');
                    break;
                default:
                    break;
            }

            if ($q != null) {
                $query->andWhere("pe.person_name LIKE '%$q%' OR pe.identification_number LIKE '%$q%'");
                $query->limit(10);
            }
            if ($type == 'associated') {
                $query->andWhere('pe.is_associated = 1');
            }
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListBusinessPartnerByRoute() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $route = !Util::isBlank($_POST['route']) ? $_POST['route'] : null;
            $q = !Util::isBlank($_POST['q']) ? $_POST['q'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'CONCAT(pe.identification_number, \' - \', replace(pe.person_name,\',\',\' \')) AS value')
                    ->from('movement mo')
                    ->join('person pe', 'pe.person_id = mo.aux_person_id')
                    ->order('pe.person_name');

            $query->group('mo.aux_person_id');
            if ($q != null) {
                $query->where("REPLACE(pe.person_name, ',', ' ') LIKE '%$q%' OR pe.identification_number LIKE '%$q%' and mo.route IN ('" . $route . "') AND mo.status ");
                $query->limit(10);
            }
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListBusinessPartnerTpo() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $type = !Util::isBlank($_POST['type']) ? $_POST['type'] : null;
            $q = !Util::isBlank($_POST['q']) ? $_POST['q'] : null;
            $year = !Util::isBlank($_POST['year']) ? $_POST['year'] : null;
            $condition = !Util::isBlank($_POST['condition']) ? $_POST['condition'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'CONCAT(pe.identification_number, \' - \', replace(pe.person_name,\',\',\' \')) AS value , SUM(CASE WHEN d.sunat_code IN (\'07\') THEN -cm.affected_pen ELSE cm.affected_pen END) AS monto ')
                    ->from(' accounting_movement am ')
                    ->join('movement mo', 'mo.movement_id = am.movement_id ')
                    ->join('commercial_movement cm', 'cm.movement_id = mo.movement_id ')
                    ->join('document d', 'd.document_code = mo.document_code ')
                    ->join('person pe', 'pe.person_id = mo.aux_person_id ')
                    ->where(' mo.status ');
            IF ($year != null) {
                $query->andWhere(" am.year = " . $year);
            }

            switch ($type) {
                case "provider":
                    $query->andWhere(' mo.route  IN (\'accounting/manual\', \'accounting/operatingCost\', \'logistic/debitNote\', \'financial/providerLetter\', \'logistic/operatingCost\', \'logistic/purchaseBill\', \'treasury/prePay\') AND mo.status');
                    break;
                case "client":
                    $query->andWhere(" mo.route IN ('commercial/saleBill', 'commercial/creditNote1', 'commercial/creditNote2', 'commercial/creditNote3', 'commercial/debitNote') ");
                    break;
                default:
                    break;
            }
            if ($q != null) {
                $query->andWhere(" pe.person_name LIKE '%$q%' OR pe.identification_number LIKE '%$q%'");
            }
            $query->group(' pe.person_id ');
//            IF($condition != null){
//              
//                $query->having(' monto '.$condition.' (SELECT uit_amount_pen FROM `global_var`) ');
//            }
            $query->limit(10);

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }
        echo CJSON::encode($aResponse);
    }

    public function actionListBusinessPartnerAlternate() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $type = !Util::isBlank($_POST['type']) ? $_POST['type'] : null;
            $q = !Util::isBlank($_POST['q']) ? $_POST['q'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'CONCAT(pe.identification_number, \' - \', replace(pe.person_name,\',\',\' \')) AS value')
                    ->from('movement mo')
                    ->join('person pe', 'pe.person_id = mo.mask_person_id')
                    ->order('pe.person_name');

            switch ($type) {
                case "provider":
                    $query->where('mo.route  IN (\'accounting/manual\', \'accounting/operatingCost\', \'logistic/debitNote\', \'financial/providerLetter\', \'logistic/operatingCost\', \'logistic/purchaseBill\', \'treasury/prePay\') AND mo.status');
                    break;
                case "client":
                    $query->where('mo.route IN (\'commercial/saleBill\') AND mo.status');
                    break;
                case "all":
                    $query->where('mo.route IN (\'accounting/manual\', \'accounting/operatingCost\', \'logistic/debitNote\', \'financial/providerLetter\', \'logistic/operatingCost\', \'logistic/purchaseBill\', \'treasury/prePay\',\'commercial/saleBill\') AND mo.status');
                default:
                    break;
            }
            $query->group('mo.mask_person_id');
            if ($q != null) {
                $query->where("pe.person_name LIKE '%$q%' OR pe.identification_number LIKE '%$q%'");
                $query->limit(10);
            }
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListPersonToPay() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $type = !Util::isBlank($_POST['type']) ? $_POST['type'] : array();

            $queryEmployee = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'CONCAT(pe.identification_number, \' - \', replace(pe.person_name,\',\',\' \')) AS value')
                    ->from('movement mo')
                    ->join('entry en', 'en.movement_id = mo.movement_id')
                    ->join('person pe', 'pe.person_id = en.owner_id AND pe.identification_type = \'1\'')
                    ->where('mo.status AND en.owner = \'Person\'  AND mo.route = \'human/payroll\'')
                    ->group('en.owner_id');

            $queryProvider = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'CONCAT(pe.identification_number, \' - \', replace(pe.person_name,\',\',\' \')) AS value')
                    ->from('movement mo')
                    ->join('person pe', 'pe.person_id = mo.aux_person_id')
                    ->where('mo.route  IN (\'accounting/manual\', \'accounting/operatingCost\', \'logistic/debitNote\', \'financial/providerLetter\', \'logistic/operatingCost\', \'logistic/purchaseBill\', \'treasury/prePay\') AND mo.status ')
                    ->group('mo.aux_person_id');

            switch (count($type)) {
                case 1:
                    if ($type[0] == 'employee') {
                        $query = $queryEmployee;
                    } else {
                        $query = $queryProvider;
                    }
                    break;
                default:
                    $query = $queryEmployee
                            ->union($queryProvider->getText());
                    break;
            }

            $query->order('value');
//            $query->limit(10);
//            echo $query->getText();exit();

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListRetentionAgent() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'CONCAT(pe.identification_number, \' - \', replace(pe.person_name,\',\',\' \')) AS value')
                    ->from('person pe')
                    ->order('pe.person_name')
                    ->where('pe.retention');

            $query->limit(10);
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListTypePerson() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $data = Condition::getTypePersonArrayReport();

            $aResponse['data'] = $data;
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListCurrency() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $data = USArray::getComponentArray(Currency::getListData());
            $aResponse['data'] = $data;
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListCondition() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $data = USArray::getComponentArray(Condition::getListData());

            $aResponse['data'] = $data;
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListStore() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $business_unit_ids = isset($_POST['business_unit_ids']) ? !Util::isBlank($_POST['business_unit_ids']) ? $_POST['business_unit_ids'] : null : null;
            $query = Yii::app()->db->createCommand()
                    ->select('st.store_id AS id,'
                            . 'st.store_name AS value')
                    ->from('store st')
                    ->order('st.store_name')
                    ->where('st.status');

            if ($business_unit_ids != null && count($business_unit_ids) > 0) {
                $cad3 = implode(',', $business_unit_ids);
                $query->andWhere('st.business_unit_id in (' . $cad3 . ')');
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListStatus() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $type = !Util::isBlank($_POST['type']) ? $_POST['type'] : null;

            $data = Status::getArrayStatusReport($type);

            $aResponse['data'] = $data;
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionTypeServiceOrder() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $data = ServiceOrder::getArrayStatusReport();
            $aResponse['data'] = $data;
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }
        echo CJSON::encode($aResponse);
    }

    public function actionListRetention() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {

            $data = Condition::getArrayRetentionReport();

            $aResponse['data'] = $data;
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListMovement() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $route = !Util::isBlank($_POST['route']) ? $_POST['route'] : null;
            $operation = !Util::isBlank($_POST['operation']) ? $_POST['operation'] : null;
            $idsAuxPerson = !Util::isBlank($_POST['idsAuxPerson']) ? $_POST['idsAuxPerson'] : null;
            $search = !Util::isBlank($_POST['search']) ? $_POST['search'] : null;
            $d = !Util::isBlank($_POST['d']) ? $_POST['d'] : null;
            $sd = !Util::isBlank($_POST['sd']) ? $_POST['sd'] : null;
            $ed = !Util::isBlank($_POST['ed']) ? $_POST['ed'] : null;

            $term = isset($_POST['term']) ? (!Util::isBlank($_POST['term']) ? $_POST['term'] : null) : null;

            $query = Yii::app()->db->createCommand()
                    ->select('mo.movement_id AS id,'
                            . 'CONCAT_WS(\'-\',mo.document_code,mo.document_serie,document_correlative) AS value')
                    ->from('movement mo')
                    ->where('mo.status')
                    ->order('mo.emission_date DESC');

            if (!is_null($route))
                $query->andWhere(array('IN', 'mo.route', $route));

            if (!is_null($operation))
                $query->andWhere(array('IN', 'mo.operation_code', $operation));

            if (!is_null($idsAuxPerson))
                $query->andWhere(array('IN', 'mo.aux_person_id', $idsAuxPerson));

            if ($search != null && $search == 1) {
                $query->andWhere("mo.emission_date BETWEEN '" . $sd . "' AND ADDTIME(CAST('" . $ed . "' AS DATETIME), '23:59:59')");
            } elseif ($search != null) {
                $query->andWhere("mo.emission_date <= ADDTIME(CAST('" . $d . "' AS DATETIME), '23:59:59')");
            }

            if ($term != null)
                $query->having(array('LIKE', 'value', "%$term%"));

            // echo $query->getText();exit();

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListMovementWithPayment() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $route = !Util::isBlank($_POST['route']) ? $_POST['route'] : null;
            $operation = !Util::isBlank($_POST['operation']) ? $_POST['operation'] : null;
            $sd = !Util::isBlank($_POST['sd']) ? $_POST['sd'] : null;
            $ed = !Util::isBlank($_POST['ed']) ? $_POST['ed'] : null;
            $idsAuxPerson = !Util::isBlank($_POST['idsAuxPerson']) ? $_POST['idsAuxPerson'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('mp.movement_id AS id,'
                            . 'CONCAT_WS(\'-\',mp.document_code,mp.document_serie,mp.document_correlative) AS value')
                    ->from('movement mo')
                    ->join('movement_link ml', 'ml.movement_id = mo.movement_id')
                    ->join('movement mp', 'mp.movement_id = ml.movement_parent_id');

            $query->where('mo.status');

            if (!is_null($route))
                $query->andWhere(array('IN', 'mp.route', $route));

            if (!is_null($operation)) {
//                $query->andWhere(array('IN', 'mp.operation_code', $operation));
                if ($operation == 'C') {
                    $query->andWhere('mo.route IN (\'financial/outgoing\', \'financial/providerLetter\',\'treasury/providerApplication\',\'accounting/providerWithholding\')');
                } else {
                    $query->andWhere('mo.route IN (\'accounting/clientWithholding\', \'treasury/clientApplication\', \'financial/clientLetter\', \'treasury/saleCollect\')');
                }
            }

            if (!is_null($idsAuxPerson))
                $query->andWhere(array('IN', 'mp.aux_person_id', $idsAuxPerson));

            if (!is_null($sd) && !is_null($ed))
                $query->andWhere("mo.emission_date BETWEEN '" . $sd . "' AND ADDTIME(CAST('" . $ed . "' AS DATETIME), '23:59:59')");

            $query->group('mp.movement_id')
                    ->order('mp.emission_date DESC');

//            echo $query->getText(); exit();

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListPaymentMethod() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('type AS id,'
                            . ' type AS value')
                    ->from('cashbox_movement')
                    ->group('type');

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListClient() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $retention = !Util::isBlank($_POST['retention']) ? $_POST['retention'] : null;
            $q = isset($_POST['q']) ? $_POST['q'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'CONCAT(pe.identification_number, \' - \', replace(pe.person_name,\',\',\' \')) AS value')
                    ->from('movement mo')
                    ->join('person pe', 'pe.person_id = mo.aux_person_id')
                    ->order('pe.person_name')
                    ->where('mo.route IN (\'commercial/saleBill\', \'commercial/debitNote\') AND mo.status');

            switch ($retention) {
                case 1:
                    $query->andwhere('pe.retention');
                    break;
                case 2:
                    $query->andwhere('pe.retention = 0');
                    break;
                default:
                    break;
            }

            $query->group('mo.aux_person_id');
            $query->limit(10);

            if ($q != null) {
                $query->where("pe.identification_number LIKE '%$q%' OR pe.person_name LIKE '%$q%'");
                $query->limit(10);
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListClientProject() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $q = isset($_POST['q']) ? $_POST['q'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'CONCAT(pe.identification_number, \' - \', replace(pe.person_name,\',\',\' \')) AS value')
                    ->from('project p')
                    ->join('person pe', 'pe.person_id = p.person_id')
                    ->order('pe.person_name');

            $query->limit(10);

            if ($q != null) {
                $query->where("pe.identification_number LIKE '%$q%' OR pe.person_name LIKE '%$q%'");
                $query->limit(10);
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListBuyer() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $active = !Util::isBlank($_POST['active']) ? $_POST['active'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'replace(pe.person_name,\',\',\' \') AS value,'
                            . 'us.status AS status')
                    ->from('movement mo')
                    ->join('person pe', 'pe.person_id = mo.person_id')
                    ->join('user us', 'us.person_id = pe.person_id')
                    ->order('pe.person_name')
                    ->where('mo.route IN (\'logistic/purchaseBill\') AND mo.status');

            switch ($active) {
                case 1:
                    $query->andwhere('us.status = 1');
                    break;
                default:
                    break;
            }

            $query->group('mo.person_id');

//            echo $query->getText(); exit();

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListSeller() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $active = !Util::isBlank($_POST['active']) ? $_POST['active'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'replace(pe.person_name,\',\',\' \') AS value,'
                            . 'us.status AS status')
                    ->from('movement mo')
                    ->join('person pe', 'pe.person_id = mo.person_id')
                    ->join('user us', 'us.person_id = pe.person_id')
                    ->order('pe.person_name')
                    ->where('mo.route IN (\'commercial/saleBill\', \'commercial/saleOrder\') AND mo.status');

            switch ($active) {
                case 1:
                    $query->andwhere('us.status = 1');
                    break;
                default:
                    break;
            }

            $query->group('mo.person_id');

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListSeller2() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $active = !Util::isBlank($_POST['active']) ? $_POST['active'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select("S.seller_id as id, REPLACE(P.person_name, ',', ' ') as `value`, S.status")
                    ->from('seller S')
                    ->join('person P', 'P.person_id = S.person_id');

            switch ($active) {
                case 1:
                    $query->andwhere('S.status = 1');
                    break;
                default:
                    break;
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListTechnical() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $active = !Util::isBlank($_POST['active']) ? $_POST['active'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('pe.person_id AS id,'
                            . 'replace(pe.person_name,\',\',\' \') AS value,'
                            . 'us.status AS status')
                    ->from('movement mo')
                    ->join('person pe', 'pe.person_id = mo.person_id')
                    ->join('user us', 'us.person_id = pe.person_id')
                    ->order('pe.person_name')
                    ->where('mo.route IN (\'support/serviceOrder\') AND mo.status');

            switch ($active) {
                case 1:
                    $query->andwhere('us.status = 1');
                    break;
                default:
                    break;
            }

            $query->group('mo.person_id');

//            echo $query->getText(); exit();

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListMark() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('mark_id AS id,'
                            . ' mark_name AS value')
                    ->from('mark')
                    ->where("status")
                    ->order("mark_name");

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListYear() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $minYear = !Util::isBlank($_POST['minYear']) ? $_POST['minYear'] : null;
            $years = USTime::getYears($minYear);
            $aResponse['data'] = $this->normalizeArray($years);
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListMonth() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $accounting = !Util::isBlank($_POST['accounting']) ? $_POST['accounting'] : null;
            $b_accounting = $accounting == "true";
            $months = USTime::getPeriods($b_accounting);

            $aResponse['data'] = $this->normalizeArray($months);
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListDepartment() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $idDep = !Util::isBlank($_POST['idDep']) ? $_POST['idDep'] : null;
            $q = isset($_POST['q']) ? $_POST['q'] : null;
            $query = Yii::app()->db->createCommand()
                    ->select('ge.dept_code AS id,'
                            . ' ge.dept_name AS value')
                    ->from('geoloc ge')
                    ->group("ge.dept_code")
                    ->order("ge.dept_code");

            if ($idDep != null) {
                $query->andWhere("ge.dept_code LIKE '%$idDep%'OR ge.dept_name LIKE '%$idDep%'");
            }

            if ($q != null) {
                $query->andWhere("ge.dept_code LIKE '%$q%'OR ge.dept_name LIKE '%$q%'");
            }

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListProvince() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $idsDepartment = !Util::isBlank($_POST['idsDepartment']) ? $_POST['idsDepartment'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('CONCAT(dept_code,prov_code) AS id,'
                            . 'prov_name AS value')
                    ->from('geoloc');

            if ($idsDepartment != null)
                $query->andWhere(array('IN', 'dept_code', $idsDepartment));

            $query->group("dept_code, prov_code")
                    ->order("prov_code");

//            echo $query->getText(); exit();

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListDistrict() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $idsDepartment = !Util::isBlank($_POST['idsDepartment']) ? $_POST['idsDepartment'] : null;
            $idsProvince = !Util::isBlank($_POST['idsProvince']) ? $_POST['idsProvince'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select('CONCAT(dept_code,prov_code,dist_code) AS id,'
                            . ' dist_name AS value')
                    ->from('geoloc');

            if ($idsDepartment != null)
                $query->andWhere(array('IN', 'dept_code', $idsDepartment));

            if ($idsProvince != null)
                $query->andWhere(array('IN', 'prov_code', $idsProvince));

            $query->group("dept_code, prov_code, dist_code")
                    ->order("dist_code");

//            echo $query->getText(); exit();
            $query->limit(20);

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListAccount() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('account_code AS id,'
                            . ' CONCAT(account_code, " - ", account_name) AS value')
                    ->from('account');

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListUsableAccount() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('account_code AS id,'
                            . ' CONCAT(account_code, " - ", account_name) AS value')
                    ->from('account')
                    ->where('is_usable  and  not is_wildcard');

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListAccountMajor() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('account_code AS id,'
                            . ' CONCAT(account_code, " - ", account_name) AS value')
                    ->from('account')
                    ->where(" ((account_parent IS NULL AND `level` = 2) OR "
                    . " (account_code NOT IN (SELECT account_parent FROM account WHERE account_parent IS NOT NULL GROUP BY account_parent))) "
                    . " AND CONCAT('', account_code * 1) = account_code");
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListFinancialEntities() {
        header('Content-type: application/json');
        $type = $_POST['type'];
        $idsStore = isset($_POST['idsStore']) ? !Util::isBlank($_POST['idsStore']) ? $_POST['idsStore'] : null : null;
        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            //eso es aqui en la consulta
            $query = Yii::app()->db->createCommand()
                    ->select('ac.account_code AS id,'
                            . 'ac.account_name AS value')
                    ->from('cashbox c')
                    ->leftjoin('store s', 's.store_id = c.store_id')
                    ->join('account ac', 'c.account_code = ac.account_code')
                    ->where('c.status <> 0');
            if (isset($type) and $type != '')
                $query->andWhere(" c.type = '" . $type . "'");

            if ($idsStore != null && count($idsStore) > 0) {
                $cad5 = implode(',', $idsStore);
                $query->andWhere('c.store_id in (' . $cad5 . ')');
            }
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListCostCenter() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('c.cost_level_item_id AS id, c.cost_level_item_name AS value, c.cost_level as level')
                    ->from('cost_level_item c');

            $result = $query->queryAll();
            $data = [];
            $names = GlobalVar::getLevelNames();

            foreach ($result as $item) {
                $data[$item['level']][$item['id']] = $item['value'];
            }

            $aResponse['data'] = $data;
            $aResponse['level'] = $names;
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListWeek() {
        header('Content-type: application/json');

        $year = $_POST['year'];
        $month = $_POST['month'];

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->selectDistinct('C.week as id , C.week as value')
                    ->from('calendar C')
                    ->where('YEAR(C.date)= ' . $year . ' AND MONTH(C.date) =' . $month);

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }
        echo CJSON::encode($aResponse);
    }

    public function actionListPayroll() {
        header('Content-type: application/json');

        $regime = $_POST['regime'];
        $year = $_POST['year'];
        $month = $_POST['month'];
        $week = $_POST['week'];

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('MO.movement_id as id, CONCAT(MO.document_code,"-",MO.document_serie,"-",MO.document_correlative ," ", COALESCE(MO.observation,"")) as `value`')
                    ->from('movement MO')
                    ->join('payroll_movement PM', 'PM.movement_id = MO.movement_id')
                    ->join('dictionary D', 'D.dictionary_id = PM.dictionary_id')
                    ->join('assistance_summary SU', 'SU.assistance_summary_id = PM.assistance_summary_id')
                    ->where('MO.status AND SU.year= ' . $year . ' AND SU.period =' . $month . ' AND D.regime_id = ' . $regime);
            if ($week != '' && $week != '0') {
                $query->andWhere('SU.week = ' . $week);
            }
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }
        echo CJSON::encode($aResponse);
    }

    public function actionListBusinessPartnerCRM() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $q = !Util::isBlank($_POST['q']) ? $_POST['q'] : null;

            $query = Yii::app()->db->createCommand()
                    ->select("P.person_id as id, REPLACE(P.person_name, ',', ' ') as `value`")
                    ->from('commercial_case CC')
                    ->join('person P', 'P.person_id = CC.aux_person_id')
                    ->group('P.person_id');

            if ($q != null) {
                $query->where("REPLACE(P.person_name, ',', ' ') LIKE '%$q%' OR P.identification_number LIKE '%$q%' ");
                $query->limit(10);
            }
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListContactMode() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('MH.multi_id as id, UPPER(MH.description) as value')
                    ->from('multitable MP')
                    ->join('multitable MH', 'MH.multi_parent_id = MP.multi_id')
                    ->where("MP.`value` = '" . Multitable::CONTACT_MODE_CRM . "'");

            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListCommercialCasesStates() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $aResponse['data'] = [
                ['id' => CommercialCase::STATE_NEW],
                ['id' => CommercialCase::STATE_OPEN],
                ['id' => CommercialCase::STATE_COMPLETED],
                ['id' => CommercialCase::STATE_CANCELED]];
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    public function actionListCommercialCasesDetailStates() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $aResponse['data'] = [
                ['id' => CommercialCaseDetail::STATE_NEW],
                ['id' => CommercialCaseDetail::STATE_PROGRAMMED],
                ['id' => CommercialCaseDetail::STATE_REPROGRAMMED],
                ['id' => CommercialCaseDetail::STATE_IN_PROGRESS],
                ['id' => CommercialCaseDetail::STATE_COMPLETED],
                ['id' => CommercialCaseDetail::STATE_CANCELLED]];
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    //Acción agreement- Convenios
    public function actionListAgreement() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                ->select('A.agreement_id as id, A.agreement_name as value')
                ->from('agreement as A')
                ->where("A.`status` = 1");

            $aResponse ['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

    //List projects
    public function actionListProjects() {
        header('Content-type: application/json');

        $aResponse = array(
            'code' => USREST::CODE_SUCCESS,
            'msg' => USREST::SUCCESS_MESSAGE
        );

        try {
            $query = Yii::app()->db->createCommand()
                    ->select('Pr.project_id AS id,'
                            . 'Pr.short_name AS value')
                    ->from('project Pr');
            $aResponse['data'] = $query->queryAll();
        } catch (Exception $exc) {
            $aResponse ['code'] = $exc->getCode();
            $aResponse ['data'] = $exc->getMessage();
        }

        echo CJSON::encode($aResponse);
    }

}
