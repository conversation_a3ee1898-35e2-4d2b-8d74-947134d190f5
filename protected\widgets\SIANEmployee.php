<?php

class SIANEmployee extends CWidget {

    public $form;
    public $model;
    public $data;
    //PRIVATE
    private $controller;
    //IDS
    public $div_main;
    public $regime_id;
    public $regime_code_id;
    public $children_id;
    public $category_id;
    public $expedition_date_id;
    public $license_number_id;
    public $revalidation_date_id;
    public $pension_system_id;
    public $type_commission_id;
    public $afiliate_code_id;
    public $is_private_id;
    public $modal_id = null;

    public function init() {

        $this->controller = Yii::app()->controller;
        //IDS
        $this->div_main = $this->controller->getServerId();
        $this->regime_id = isset($this->regime_id) ? $this->regime_id : $this->controller->getServerId();
        $this->regime_code_id = isset($this->regime_code_id) ? $this->regime_code_id : $this->controller->getServerId();
        $this->children_id = isset($this->children_id) ? $this->children_id : $this->controller->getServerId();
        $this->category_id = isset($this->category_id) ? $this->category_id : $this->controller->getServerId();
        $this->expedition_date_id = isset($this->expedition_date_id) ? $this->children_id : $this->controller->getServerId();
        $this->license_number_id = isset($this->license_number_id) ? $this->license_number_id : $this->controller->getServerId();
        $this->revalidation_date_id = isset($this->revalidation_date_id) ? $this->revalidation_date_id : $this->controller->getServerId();
        $this->pension_system_id = isset($this->pension_system_id) ? $this->pension_system_id : $this->controller->getServerId();
        $this->type_commission_id = isset($this->type_commission_id) ? $this->type_commission_id : $this->controller->getServerId();
        $this->afiliate_code_id = isset($this->afiliate_code_id) ? $this->afiliate_code_id : $this->controller->getServerId();
        $this->is_private_id = isset($this->is_private_id) ? $this->is_private_id : $this->controller->getServerId();
        $regimeList = CJSON::encode(Regime::getListData());
        $pension_system_items = CJSON::encode($this->data['pension_system_items']);
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "                
            var div = $('#{$this->div_main}');
            div.data('regimeList', {$regimeList});
            $('#{$this->regime_id}').change();
                
            var selectObj = $('#{$this->pension_system_id}').html('');
            var o_pensionSystem = {$pension_system_items};
            var found = false;
            var optionObj = $('<option>').val('')
                        .text('(Seleccione una opción)')
                        .data('is_private', 0);
                        
            selectObj.append(optionObj);
            $.each( o_pensionSystem, function (index, pensionSystem) {
                optionObj = $('<option>').val(pensionSystem.pension_system_id)
                        .text(pensionSystem.pension_system_name)
                        .data('is_private', pensionSystem.is_private);

                selectObj.append(optionObj);                             
            });
            selectObj.val({$this->model->pension_system_id});
           
            
            {$this->id}changePensionSystem();
                
            function {$this->id}changePensionSystem(){                
                
                var pensionSystemObj = $('#{$this->pension_system_id}');
                var optionObj = pensionSystemObj.find('option:selected');
                var is_private = optionObj.data('is_private');
                $('#{$this->is_private_id}').val(is_private);                    
                var b_is_private = (is_private == 1);
                
                if(!b_is_private){
                    $('#{$this->type_commission_id},#{$this->afiliate_code_id}').val('');
                }
                $('#{$this->type_commission_id},#{$this->afiliate_code_id}').readonly(!b_is_private);
                $('#{$this->type_commission_id}').makeRequired(b_is_private);  
            }
        ");
    }

    public function renderPrincipal() {
        echo '<br>';
        echo "<div  id='" . $this->div_main . "'  class='row'>";
        echo '<div class="col-lg-7 col-md-8 col-sm-12 col-xs-12">';
        $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model,
            'attribute' => 'person_id',
            'parent_id' => $this->modal_id,
            'readonly' => !$this->model->isNewRecord,
            'view' => array(
                'model' => 'DpBusinessPartner',
                'scenario' => $this->model->isNewRecord ? DpBusinessPartner::SCENARIO_PERSON_NOT_EMPLOYEE : DpBusinessPartner::SCENARIO_PEOPLE,
                'attributes' => array(
                    array(
                        'name' => 'person_id',
                        'width' => 10,
                        'types' => array('id', 'value', 'aux'),
                        'not_in' => '[1]',
                    ),
                    array(
                        'name' => 'identification_number',
                        'width' => 25
                    ),
                    array(
                        'name' => 'person_name',
                        'width' => 65,
                        'types' => array('text')
                    ),
                    array(
                        'name' => 'person_type',
                        'hidden' => true,
                        'in' => "['" . Person::TYPE_NATURAL . "']"
                    ),
                )
            ),
            'maintenance' => array(
                'module' => 'administration',
                'controller' => 'person'
            )
        ));
        echo "</div>";

        echo "<div class=col-lg-2 col-md-2 col-sm-6 col-xs-12>";
        $this->widget('application.widgets.USDatePicker', array(
            'form' => $this->form,
            'model' => $this->model,
            'attribute' => 'date_in',
            'options' => array(
                'maxDate' => SIANTime::formatDate(),
            )
        ));
        echo "</div>";

        echo "<div class=col-lg-3 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'regime_id', Regime::getRegime('id'), array(
            'id' => $this->regime_id,
            'empty' => Strings::SELECT_OPTION,
            'onchange' => "var regimeList = $('#" . $this->div_main . "').data('regimeList');"
            . " var regime = '" . Regime::REGIME_EMP . "';"
            . "if($(this).val() != ''){"
            . "if(regimeList[$(this).val()]['regime_code'] ==  '" . Regime::REGIME_OCC . "') {regime = regimeList[$(this).val()]['regime_code'];}"
            . "$('.sian-regime').hide(5); "
            . "$('.sian-'+regime.toLowerCase()).show(150);"
            . "$('#" . $this->regime_code_id . "').val(regime);"
            . "$('#" . $this->children_id . "').parent().find('span.help-block').text('-');"
            . "if(regime == '" . Regime::REGIME_OCC . "'){ $('#" . $this->children_id . "').parent().find('span.help-block').text('Para Asignación por Escolaridad.');}"
            . "else{ $('#" . $this->children_id . "').parent().find('span.help-block').text('Para Asignación Familiar.');}}"
            ,
            'onload' => "var regimeList = $('#" . $this->div_main . "').data('regimeList');"
            . " var regime = '" . Regime::REGIME_EMP . "';"
            . "if(regimeList[$(this).val()]['regime_code'] ==  '" . Regime::REGIME_OCC . "') {regime = regimeList[$(this).val()]['regime_code'];}"
            . "$('.sian-regime').hide(5); "
            . "$('.sian-'+regime.toLowerCase()).show(150);"
            . "$('#" . $this->children_id . "').parent().find('span.help-block').text('-');"
            . "if(regime == '" . Regime::REGIME_OCC . "'){ $('#" . $this->children_id . "').parent().find('span.help-block').text('Para Asignación por Escolaridad.');}"
            . "else{ $('#" . $this->children_id . "').parent().find('span.help-block').text('Para Asignación Familiar.');}"
                ,
        ));
        echo $this->form->hiddenField($this->model, 'regime_code', array(
            'id' => $this->regime_code_id
        ));
        echo "</div>";
        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->textFieldRow($this->model, 'work_days', array(
            'min' => 0,
            'step' => 1,
            'max' => 31,
        ));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, 'children', array(
            'id' => $this->children_id,
            'hint' => '-'
        ));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        $this->widget('application.widgets.USSwitch', array('model' => $this->model, 'attribute' => 'status'));
        echo "</div>";
        echo "</div>";
    }

    public function renderRegimeEMP() {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Datos del Régimen',
            'headerIcon' => 'certificate',
            'htmlOptions' => array(
                'class' => 'sian-emp sian-regime',
                'style' => "display: none;",
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-8 col-md-8 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model,
            'attribute' => 'station_id',
            'parent_id' => $this->modal_id,
            'view' => array(
                'model' => 'DpStation',
                'attributes' => array(
                    array(
                        'name' => 'station_id',
                        'width' => 10,
                        'types' => array('id', 'value', 'aux'),
                        'hidden' => true
                    ),
                    array(
                        'name' => 'station_name',
                        'width' => 45,
                        'types' => array('text')
                    ),
                    array(
                        'name' => 'area_name',
                        'width' => 45,
                    ),
                )
            ),
            'maintenance' => array(
                'module' => 'human',
                'controller' => 'station'
            )
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->textFieldRow($this->model, 'salary', array(
            'min' => 0,
            'step' => 0.01,
        ));
        echo "</div>";
        echo "</div>";
        $this->endWidget();
    }

    public function renderRegimeOCC() {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Datos del Régimen',
            'headerIcon' => 'certificate',
            'htmlOptions' => array(
                'class' => 'sian-occ sian-regime',
                'style' => "display: none;",
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-8 col-md-8 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model,
            'attribute' => 'human_category_id',
            'parent_id' => $this->modal_id,
            'view' => array(
                'model' => 'DpHumanCategory',
                'attributes' => array(
                    array(
                        'name' => 'category_id',
                        'width' => 15,
                        'types' => array('id', 'value', 'aux'),
                    ),
                    array(
                        'name' => 'category_name',
                        'width' => 45,
                        'types' => array('text')
                    ),
                    array(
                        'name' => 'remuneration_diary',
                        'width' => 20,
                    ),
                    array(
                        'name' => 'buc',
                        'width' => 20,
                    ),
                )
            ),
            'maintenance' => array(
                'module' => 'human',
                'controller' => 'category'
            )
        ));
        echo "</div>";
        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-8 col-md-8 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model,
            'attribute' => 'project_id',
            'parent_id' => $this->modal_id,
            'view' => array(
                'model' => 'DpProject',
                'attributes' => array(
                    array(
                        'name' => 'project_id',
                        'width' => 15,
                        'types' => array('id', 'value', 'aux'),
                    ),
                    array(
                        'name' => 'short_name',
                        'width' => 65,
                        'types' => array('text'),
                    ),
                    array(
                        'name' => 'project_status',
                        'width' => 20,
                    ),
                )
            )
        ));
        echo "</div>";
        echo "</div>";
        $this->endWidget();
    }

    public function renderPensionSystem() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Sistema de Pensión',
            'headerIcon' => 'certificate'
        ));

        echo '<div class = "row">';
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'has_pension_system',
            'switchChange' => "$('#{$this->pension_system_id}').readonly(!this.checked);
                               $('#{$this->pension_system_id}').val('');
                               $('#{$this->pension_system_id}').makeRequired(this.checked);
                               {$this->id}changePensionSystem();"
        ));
        echo "</div>";
        echo '<div class = "col-lg-6 col-md-6 col-sm-6 col-xs-12">';
        echo $this->form->dropDownListRow($this->model, 'pension_system_id', [], array(
            'id' => $this->pension_system_id,
            'empty' => Strings::SELECT_OPTION,
            'required' => $this->model->has_pension_system == 1,
            'readonly' => $this->model->has_pension_system == 0,
            'onchange' => "{$this->id}changePensionSystem();",            
            'class' => 'sian-employee-pension-system',
        ));
        echo $this->form->hiddenField($this->model, 'is_private', array(
            'id' => $this->is_private_id
        ));
        echo "</div>";
        echo '<div class = "col-lg-2 col-md-2 col-sm-6 col-xs-12">';

        echo $this->form->dropDownListRow($this->model, 'type_comission', $this->data['type_comission_items'], array(
            'id' => $this->type_commission_id,
            'empty' => Strings::SELECT_OPTION
        ));

        echo "</div>";
        echo '<div class = "col-lg-2 col-md-2 col-sm-6 col-xs-12">';

        echo $this->form->textFieldRow($this->model, 'afiliate_code', array(
            'id' => $this->afiliate_code_id,
            'maxlength' => 50,
            'placeholder' => $this->model->getAttributeLabel('afiliate_code'),
        ));

        echo "</div>";
        echo "</div>";
        $this->endWidget();
    }

    public function renderTransportist() {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Info. Transportista',
            'headerIcon' => 'certificate'
        ));
        echo "<div class = 'row'>";
        echo "<div class = 'col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        $this->widget('application.widgets.USSwitch', array(
            'model' => $this->model,
            'attribute' => 'is_shipper',
            'switchChange' => " $('#{$this->category_id}, #{$this->license_number_id},#{$this->expedition_date_id}, #{$this->revalidation_date_id}').readonly(!this.checked);
                                $('#{$this->category_id}, #{$this->license_number_id},#{$this->expedition_date_id}, #{$this->revalidation_date_id}').val('');
                                $('#{$this->license_number_id}').makeRequired(this.checked);
                                $('#{$this->category_id}').makeRequired(this.checked);
                                $('#{$this->expedition_date_id}').makeRequired(this.checked);
                                $('#{$this->revalidation_date_id}').makeRequired(this.checked);"
        ));
        echo "</div>";
        echo "<div class = 'col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->textFieldRow($this->model, "license_number", array(
            'id' => $this->license_number_id,
            'maxlength' => 20,
            'readonly' => !$this->model->is_shipper,
            'required' => $this->model->is_shipper
        ));
        echo "</div>";
        echo "<div class = 'col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, "category_id", (new DpMultitable(Multitable::LICENSE_CATEGORY_CODE))->getListData('multi_id', 'description'), array(
            'id' => $this->category_id,
            'readonly' => !$this->model->is_shipper,
            'required' => $this->model->is_shipper,
            'empty' => Strings::SELECT_OPTION,
        ));
        echo "</div>";
        echo "<div class = 'col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        $this->widget('application.widgets.USDatePicker', array(
            'id' => $this->expedition_date_id,
            'form' => $this->form,
            'model' => $this->model,
            'attribute' => 'expedition_date',
            'options' => array(
                'maxDate' => SIANTime::formatDate(),
            ),
            'htmlOptions' => array(
                'readonly' => !$this->model->is_shipper,
                'required' => $this->model->is_shipper,
            )
        ));
        echo "</div>";
        echo "<div class = 'col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        $this->widget('application.widgets.USDatePicker', array(
            'id' => $this->revalidation_date_id,
            'form' => $this->form,
            'model' => $this->model,
            'attribute' => 'revalidation_date',
            'htmlOptions' => array(
                'readonly' => !$this->model->is_shipper,
                'required' => $this->model->is_shipper,
            )
        ));
        echo "</div>";

        $this->endWidget();

        echo "</div>";
        echo "<div class = 'modal-footer'>";
        echo "</div>";
    }

    /**
     * Runs the widget.
     */
    public function run() {
        $this->renderPrincipal();
        $this->renderRegimeEMP();
        $this->renderRegimeOCC();
        $this->renderPensionSystem();
        $this->renderTransportist();
    }

}
