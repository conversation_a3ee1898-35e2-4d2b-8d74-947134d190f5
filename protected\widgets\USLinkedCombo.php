<?php

class USLinkedCombo extends CWidget {

    public $form;
    public $modal_id;
    public $combos;
    public $disabled = false;

    public function init() {
        
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $combo_ids = [];
        $create_ids = [];

        foreach ($this->combos as $combo) {
            $attribute = $combo['attribute'];
            $combo_ids[$attribute] = isset($combo['id']) ? $combo['id'] : $this->owner->getServerId();
            $create_ids[$attribute] = $this->owner->getServerId();
        }

        $span = floor(12 / count($this->combos));

        echo "<div class='row'>";

        foreach ($this->combos as $combo) {

            $onchange = '';

            $model = $combo['model'];
            $attribute = $combo['attribute'];
            $send = $combo['send'];
            $success = isset($combo['success']) ? $combo['success'] : null;
            $link = isset($combo['link']) ? $combo['link'] : null;
            $items = $combo['items'];
            $empty = isset($combo['empty']) ? $combo['empty'] : true;

            $combo_id = $combo_ids[$attribute];

            if (isset($link)) {
                $module = $link['module'];
                $controller = $link['controller'];
                $action = $link['action'];

                $_data = [];
                foreach ($send as $_attr) {
                    array_push($_data, "{$_attr}:$('#" . $combo_ids[$_attr] . "').val()");
                }

                $_success = "";
                foreach ($success as $_attr) {
                    $_success .= "$('#" . $combo_ids[$_attr] . "').html(data.$_attr);";
                    $_success .= "$('#" . $create_ids[$_attr] . "').data('$attribute',$('#$combo_id').val());";
                }

                if (!$this->disabled) {
                    $onchange = "
                            $.ajax({
                                type: 'post',
                                url: '" . Yii::app()->controller->createUrl("/$module/$controller/$action") . "',
                                async: false,//HACEMOS QUE EL AJAX SEA SYNCRONO PARA EVITAR PROBLEMAS CUANDO SE ASIGNA UN VALOR MANUAL
                                data: {
                                    " . implode(',', $_data) . "
                                },
                                beforeSend: function (xhr) {
                                    window.active_ajax++;
                                    //Ocultamos los tooltip
                                    $('div.ui-tooltip').remove();
                                },                                   
                                success: function(data) {
                                    {$_success} 
                                    window.active_ajax--;
                                },
                                error: function(request, status, error) { // if error occured
                                    bootbox.alert(us_message(request.responseText, 'error'));
                                    window.active_ajax--;
                                },
                                dataType: 'json'
                            });";
                }
            }

            $htmlOptions = array(
                'id' => $combo_id,
                
                'disabled' => $this->disabled
            );

            if ($empty) {
                $htmlOptions['empty'] = Strings::SELECT_OPTION;
            }

            echo "<div class='span{$span}'>";
            echo $this->form->dropDownListRow($model, $attribute, $items, $htmlOptions);
            echo "</div>";

            if (!$this->disabled) {
                Yii::app()->clientScript->registerScript(Yii::app()->controller->getServerId(), "
                    $('body').on('change', '#{$combo_id}', function(e) {
                        {$onchange}
                    });
            ");
            }
        }
        echo "</div>";
    }

}
