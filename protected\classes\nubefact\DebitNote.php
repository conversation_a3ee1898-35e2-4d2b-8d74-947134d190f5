<?php
// class NotaDebito extends DocumentoEmision
class DebitNote extends DocumentIssue
{
    public static $ND_INTERESES_MORA = 1; // 1 = INTERESES POR MORA
    public static $ND_AUMENTO_VALOR = 2; // 2 = AUMENTO DE VALOR
    public static $ND_PENALIDADES = 3; // 3 = PENALIDADES
    public static $ND_AJUSTES_AFECTOS_IVAP = 4; // 4= AJUSTES AFECTOS AL IVAP
    public static $ND_AJUSTES_OPERACIONES_EXPORTACION = 5; // 5 = AJUSTES DE OPERACIONES DE EXPORTACIÓN


    public function __construct()
    {
        parent::__construct();
        $this->data['tipo_de_comprobante'] = self::$NOTA_DEBITO;
    }

    /**
     * ATRIBUTO: documento_que_se_modifica_tipo
     * VALOR: 1 = FACTURAS ELECTRÓNICAS, 2 = B<PERSON>ETAS DE VENTA ELECTRÓNICAS
     * TIPO DE DATO: Integer
     * REQUISITO: Condicional
     * LONGITUD: 1 exacto
     */
    public function setDocumentWhichModifiesType($type)
    {
        $this->setRaw('documento_que_se_modifica_tipo', $type);
    }

    /**
     * ATRIBUTO: documento_que_se_modifica_serie
     * VALOR: SERIE de la FACTURA o BOLETA que se modifica (previamente comunicado)
     * TIPO DE DATO: String
     * REQUISITO: Condicional
     * LONGITUD: 4 exactos
     */
    public function setDocumentToModifySeries($serie)
    {
        $this->setRaw('documento_que_se_modifica_serie', $serie);
    }

    /**
     * ATRIBUTO: documento_que_se_modifica_numero
     * VALOR: NÚMERO de la FACTURA o BOLETA que se modifica (previamente comunicado)
     * TIPO DE DATO: Integer
     * REQUISITO: Condicional
     * LONGITUD: 1 hasta 8
     */
    public function setDocumentWhichModifiesNumber($number)
    {
        $this->setRaw('documento_que_se_modifica_numero', $number);
    }

    /**
     * ATRIBUTO: tipo_de_nota_de_debito
     * VALOR: 1 = INTERESES POR MORA, 2 = AUMENTO DE VALOR, 3 = PENALIDADES, 4= AJUSTES AFECTOS AL IVAP, 5 = AJUSTES DE OPERACIONES DE EXPORTACIÓN
     * TIPO DE DATO: Integer
     * REQUISITO: Condicional
     * LONGITUD: 1 exacto
     */
    public function setDebitNoteType($type)
    {
        $this->setRaw('tipo_de_nota_de_debito', $type);
    }
}