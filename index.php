<?php

//http://www.yiiframework.com/wiki/83/netbeans-ide-and-yii-projects/
// change the following paths if necessary
include(\dirname(__FILE__) . '/protected/config/us-config.php');

if ($environment !== YII_ENVIRONMENT_PRODUCTION) {
    defined('YII_DEBUG') or define('YII_DEBUG', true);
    defined('YII_TRACE_LEVEL') or define('YII_TRACE_LEVEL', 3);
}

$yii = \dirname(__FILE__) . '/../' . $framework . '/yii.php';
$config = \dirname(__FILE__) . '/protected/config/main.php';

require_once($yii);
Yii::createWebApplication($config)->run();
