<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANUserCache
 *
 * <AUTHOR>
 */
class SIANUserCache extends SIANCache {

    const NO_PAGINATION = 'ALL';
    const PAGE_SIZE_DEFAULT = 15;

    private static function _getUser() {
        if (Yii::app()->user->isGuest) {
            throw new Exception('Los métodos de la clase SIANUserCache sólo pueden ser usados por usuarios que han iniciado sesión.');
        }

        return Yii::app()->user->username;
    }

    private static function _normalizeKey($p_s_key) {
        return self::_getUser() . '_' . $p_s_key;
    }

    public static function add($key, $value) {
        return parent::add(self::_normalizeKey($key), $value);
    }

    public static function get($key) {
        return parent::get(self::_normalizeKey($key));
    }

    public static function getPageSize($p_i_grid_id) {
        $i_page_size = self::get($p_i_grid_id);
        return $i_page_size ? $i_page_size : self::PAGE_SIZE_DEFAULT;
    }

    public static function getPagination($p_i_grid_id) {
        $a_pagination = array(
            'pageSize' => self::PAGE_SIZE_DEFAULT
        );

        $m_page_size = self::get($p_i_grid_id);

        if ($m_page_size) {
            $a_pagination = ($m_page_size == self::NO_PAGINATION ) ? false : array(
                'pageSize' => $m_page_size
            );
        }

        return $a_pagination;
    }

}
