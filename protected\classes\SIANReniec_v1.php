<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of SIANReniec
 *
 * <AUTHOR>
 */
class SIANReniec_v1 {

    const CAPTCHA_URL = 'https://cel.reniec.gob.pe/valreg/codigo.do';
    const DNI_URL = 'https://cel.reniec.gob.pe/valreg/valreg.do';
    const CAPTCHA_LENGTH = 4;
    const MAX_CAPTCHA_ATTEMPS = 10;
    const MAX_DATA_ATTEMPS = 5;
    const BLANK_PIXEL = 16777215;
    const NON_EXISTS = "No se encuentra en el Archivo Magnético del RENIEC";
    const ERROR_INVALID_CAPTCHA = 'Ingrese el código que aparece en la imagen';
    const ERROR_NETWORK = 'Hubo un error al procesar la petición, vuelva a intentarlo después';
    const ERROR_DECEASED = 'Cancelado por fallecimiento.';

    /**
     * Obtiene un captcha de consulta de DNI de RENIEC
     * @return string Captcha reconocido
     */
    public static function getDNICaptcha() {
        //Llamamos al procesador
        return self::_getDNICaptcha(0);
    }

    /**
     * Obtiene un captcha de consulta de DNI de RENIEC
     * @param integer $p_i_attempt Número de intento
     * @return string Captcha reconocido
     * @throws Exception Error de conexión o de procesamiento
     */
    private static function _getDNICaptcha($p_i_attempt) {
        //Traemos otra imagen
        $a_captcha_data = self::_downloadCaptchaData();

        //Obtenemos el captcha
        $s_captcha = strtoupper(str_replace(' ', '', USOCR::init($a_captcha_data['filename'])->whitelist(range('A', 'Z'), range(0, 9))->run()));
        //Comprobamos si tiene los 4 caracteres
        if ((strlen($s_captcha) !== self::CAPTCHA_LENGTH)) {
            //Comprobamos si está dentro del número de intentos válidos
            if ($p_i_attempt < self::MAX_CAPTCHA_ATTEMPS) {
                return self::_getDNICaptcha($p_i_attempt + 1);
            }
        }

        //Comprobamos si contiene caracteres que pueden causar problemas
        if (preg_match('(0|2|5|O|S|M|N|Z)', $s_captcha) === 1) {
            //Comprobamos si está dentro del número de intentos válidos
            if ($p_i_attempt < self::MAX_CAPTCHA_ATTEMPS) {
                return self::_getDNICaptcha($p_i_attempt + 1);
            }
        }

        return array(
            'captcha' => $s_captcha,
            'cookies' => $a_captcha_data['cookies'],
            'attemps' => $p_i_attempt + 1,
        );
    }

    /**
     * Descarga el captcha de SUNAT
     * @return string Contenido obtenido de la descarga o FALSE si falla
     * @throws Exception Error de conexión o de procesamiento
     */
    private static function _downloadCaptchaData() {
        //Dirección de la imagen
        $s_img = Yii::app()->params['admin_dir'] . '/images/captcha/reniec.jpeg';
        //Obtenemos la imagen desde el captcha
        $o_ch = curl_init(self::CAPTCHA_URL);
        curl_setopt($o_ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($o_ch, CURLOPT_VERBOSE, 1);
        curl_setopt($o_ch, CURLOPT_HEADER, 1);
        //Ejecutamos
        $response = curl_exec($o_ch);
        //Obtenemos la longitud de la cabecera
        $header_size = curl_getinfo($o_ch, CURLINFO_HEADER_SIZE);
        //Cerramos recursos
        curl_close($o_ch);
        //La primera parte es la cabcera
        $header = substr($response, 0, $header_size);
        //La segunda parte es el cuerpo
        $body = substr($response, $header_size);
        //Guardamos la imagen a partir del cuerpo
        file_put_contents($s_img, $body);
        //Procesamos la imagen
        $mime = mime_content_type($s_img);

        if ($mime === 'text/html') {
            //Como mejora se puede mostrar el mensaje exacto
            throw new Exception(self::ERROR_NETWORK, 404);
        }
        //Obtenemos los cookies
        preg_match_all('/^Set-Cookie:\s*([^;]*)/mi', $header, $matches);        // get cookie
        $cookies = [];
        foreach ($matches[1] as $item) {
            parse_str($item, $cookie);
            $cookies = array_merge($cookies, $cookie);
        }
        return array(
            'filename' => self::_processImage($s_img),
            'cookies' => $cookies,
        );
    }

    /**
     * Procesa el captch y quita el fondo y líneas verticales
     * @param string $filename Ruta completa del archivo
     * @return string Ruta completa del archivo resultante
     */
    private static function _processImage($filename) {
        $img = imagecreatefromjpeg($filename);
        //Tamaño de la imagen
        $size = getimagesize($filename);
        $width = $size[0];
        $height = $size[1];

        $gd = imagecreatetruecolor($width, $height);

        for ($x = 0; $x < $width; $x++) {
            for ($y = 0; $y < $height; $y++) {
                $rgb = imagecolorat($img, $x, $y);

                $r = ($rgb >> 16) & 0xFF;
                $g = ($rgb >> 8) & 0xFF;
                $b = $rgb & 0xFF;
                $transparency = ($rgb >> 24) & 0x7F;

                if ($r < 40 && $g < 40 && $b > 100) {
                    imagesetpixel($gd, $x, $y, $rgb);
                } else {
                    imagesetpixel($gd, $x, $y, self::BLANK_PIXEL);
                }
            }
        }

        $s_new = Yii::app()->params['admin_dir'] . '/images/captcha/new.png';
        imagepng($gd, $s_new);
        return $s_new;
    }

    /**
     * Descarga los datos de DNI de RENIEC
     * @param string $p_s_dni DNI a consultar
     * @param string $p_a_captcha Captcha decodificado
     * @return string Contenido en formato String
     */
    private static function _downloadDNIData($p_s_dni, array $p_a_captcha) {
        //Obtenemos los cookies
        $a_cookie = [];
        foreach ($p_a_captcha['cookies'] as $s_key => $value) {
            $a_cookie[] = "{$s_key}={$value}";
        }
        //Obtenemos la data con CURL
        $o_ch = curl_init();

        curl_setopt($o_ch, CURLOPT_VERBOSE, false);
        curl_setopt($o_ch, CURLOPT_URL, self::DNI_URL);
        curl_setopt($o_ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($o_ch, CURLOPT_POSTFIELDS, http_build_query([
            'accion' => 'buscar',
            'nuDni' => $p_s_dni,
            'imagen' => $p_a_captcha['captcha']
        ]));
        curl_setopt($o_ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($o_ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($o_ch, CURLOPT_VERBOSE, 1);
        curl_setopt($o_ch, CURLOPT_HEADER, 1);
        curl_setopt($o_ch, CURLOPT_COOKIE, implode('; ', $a_cookie));
        //Ejecutamos
        $a_response = utf8_encode(curl_exec($o_ch));
        //Obtenemos la data
        $header_size = curl_getinfo($o_ch, CURLINFO_HEADER_SIZE);
        //Cerramos recursos
        curl_close($o_ch);

        return substr($a_response, $header_size);
    }

    /**
     * Obtiene los datos de una persona desde RENIEC
     * @param string $p_s_dni DNI de la persona a consultar
     * @return array Datos obtenidos o FALSE si el DNI no existe
     * @throws Exception Error de conexión o de procesamiento
     */
    public static function getRawDNIData($p_s_dni) {
        return self::_getRawDNIData($p_s_dni, 0);
    }

    /**
     * Obtiene los datos de una persona desde RENIEC
     * @param string $p_s_dni DNI de la persona a consultar
     * @return array Datos obtenidos o FALSE si el DNI no existe
     * @throws Exception Error de conexión o de procesamiento
     */
    public static function getDNIData($p_s_dni) {

        $a_data = self::getRawDNIData($p_s_dni);

        if ($a_data === false) {
            return false;
        }

        $a_parts = explode(' ', $a_data['name']);
        $last = count($a_parts);
        //
        $a_fdata = [];
        $a_fdata['identification_type'] = USIdentificationValidator::DNI;
        $a_fdata['identification_number'] = $p_s_dni;
        $a_fdata['maternal'] = $a_parts[$last - 1];
        $a_fdata['paternal'] = $a_parts[$last - 2];
        $a_fdata['firstname'] = implode(' ', array_slice($a_parts, 0, $last - 2));
        $a_fdata['person_name'] = $a_fdata['paternal'] . ',' . $a_fdata['maternal'] . ',' . $a_fdata['firstname'];

        return $a_fdata;
    }

    /**
     * Obtiene los datos de una persona desde RENIEC
     * @param string $p_s_dni DNI de la persona a consultar
     * @param integer $p_i_attempt Número de intento
     * @return array Datos obtenidos o FALSE si el DNI no existe
     * @throws Exception Error de conexión o de procesamiento
     */
    private static function _getRawDNIData($p_s_dni, $p_i_attempt) {

        $a_captcha = self::getDNICaptcha();

        //Descargamos la data
        $s_data = self::_downloadDNIData($p_s_dni, $a_captcha);
        //Convertimos el DOM a un objeto
        $o_html = USHtml::getDOM($s_data);

        if (!is_object($o_html)) {
            throw new Exception(self::ERROR_NETWORK, 404);
        }

        $o_text = $o_html->find('td.style2', 0);

        //
        if (!isset($o_text)) {
            throw new Exception(self::ERROR_NETWORK, 404);
        }

        $s_plaintext = $o_text->plaintext;
        $s_text = self::normalizeText($s_plaintext);

        if ($s_text === self::ERROR_INVALID_CAPTCHA) {
            throw new Exception(self::ERROR_NETWORK, 404);
        }

        //Fallecido
        if ($s_text == self::ERROR_DECEASED) {
            return false;
        }

        $s_parts = explode($p_s_dni, $s_text);

        //El DNI no existe en RENIEC
        if (trim($s_parts[1]) === self::NON_EXISTS) {
            return false;
        }

        return [
            'dni' => $p_s_dni,
            'name' => trim($s_parts[0]),
        ];
    }

    private static function normalizeText($p_s_text) {

        return trim(str_replace("\r\n", " ", str_replace("  ", " ", str_replace("\t", " ", str_replace("          ", " ", html_entity_decode($p_s_text))))));
    }

    /**
     * Determina si el DNI existe o no
     * @param string $p_s_dni DNI
     * @param array $p_a_data (Opcional) Array dónde se guardarán los datos del RUC
     * @return boolean TRUE si existe o FALSE si no
     */
    public static function existsDNI($p_s_dni, &$p_a_data = null) {

        $p_a_data = self::getDNIData($p_s_dni);

        //No existe
        if ($p_a_data === false) {
            return $p_a_data;
        }

        return true;
    }

}
