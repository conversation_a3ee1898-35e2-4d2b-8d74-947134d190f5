<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'staging3sipe.siansystem.com/admin';
$domain = 'https://staging3sipe.siansystem.com';
$report_domain = 'rstaging3.siansystem.com';
$org = 'sipe';
$us = 'us_staging3';
$database_server = '************';
$database_name = 'sipe_staging3';
$database_username = 'sian_test';
$database_password = '75nppt6vr57lx4';
$mongo_enabled = true;
$mongo_server = '************';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_BETA;
$e_billing_certificate = '';
$e_billing_certificate_pass = ''; //PIN
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '', //usuario
        'password' => ''
    ]
];
$smtp_username = '<EMAIL>';
$smtp_password = 'qwerty123a';
$environment_reports = YII_ENVIRONMENT_STAGING;
$environment = YII_ENVIRONMENT_STAGING;
