<?php

class SIANMovementAttachment extends CWidget {

    public $id;
    public $form;
    public $model;
    public $title = "Adjuntar archivos";
    public $min_file_count = 1;
    public $max_file_count = 5;
    public $accept = "*";
    public $readonly = false;
    public $include_parent = false;
    public $has_panel = true;
    public $count_input_id;
    public $sub_path = "";
    public $data_show_preview = true;
    private $property_count = "attachment_count";
    public $limit = 0;
    public $preview_files = true;
    public $confirmation_mode = false;
    //PRIVATE
    private $controller;
    private $input_id;
    private $max_file_size;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        if ($this->sub_path != "") {
            $this->property_count = $this->sub_path . "_count";
        }
        //
        $this->input_id = $this->controller->getServerId();
        $this->max_file_size = $this->controller->getOrganization()->globalVar->max_attachment_size * 1024;

        SIANAssets::registerScriptFile('js/sian-movement-attachment.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/js/plugins/piexif.min.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/js/plugins/sortable.min.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/js/plugins/purify.min.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/js/fileinput.min.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/themes/fas/theme.min.js');
        SIANAssets::registerScriptFile('other/bootstrap-fileinput/js/locales/es.js');
        //
        SIANAssets::registerCssFile('other/bootstrap-fileinput/css/fileinput.min.css');
        SIANAssets::registerCssFile('css/sian-movement-attachment.css');
        if (!$this->preview_files) {
            SIANAssets::registerCssFile('css/sian-movement-attachment-no-preview.css');
        }

        $a_movement_codes = [$this->model->movement_code];
        if ($this->include_parent && isset($this->model->linkParent) && isset($this->model->linkParent->movementParent)) {
            $a_movement_codes[] = $this->model->linkParent->movementParent->movement_code;
        }

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        var divObj = $('#{$this->id}');
        //Seteamos data
        divObj.data('input_id', '{$this->input_id}');
        divObj.data('load_attachments_url', '{$this->controller->createUrl("/{$this->model->route}/viewAttachments")}');
        divObj.data('upload_attachments_url', '{$this->controller->createUrl("/{$this->model->route}/uploadAttachments")}');
        divObj.data('sort_attachments_url', '{$this->controller->createUrl("/{$this->model->route}/sortAttachments")}');
        divObj.data('movement_id', '{$this->model->movement_id}');
        divObj.data('movement_code', '{$this->model->movement_code}');
        divObj.data('movement_codes', '" . CJSON::encode($a_movement_codes) . "');
        divObj.data('min_file_count', '{$this->min_file_count}');
        divObj.data('max_file_count', '{$this->max_file_count}');
        divObj.data('max_file_size', '{$this->max_file_size}');
        divObj.data('allow_modify', " . CJSON::encode($this->model->status == 1 && $this->controller->checkRoute("/{$this->model->route}/modifyAttachments")) . ");
        divObj.data('sub_path', '{$this->sub_path}');
        divObj.data('property_count', '{$this->property_count}');
        divObj.data('limit', '{$this->limit}');
                
        $(document).ready(function() {
            SIANMovementAttachmentInit('{$this->id}');
        });

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->has_panel) {
            $this->beginWidget('booster.widgets.TbPanel', array(
                'title' => $this->title,
                'headerIcon' => 'file',
                'htmlOptions' => [
                    'id' => $this->id
                ]
            ));
        } else {
            echo "<div id = '{$this->id}'>";
        }
        if ($this->confirmation_mode) {

            $i_counter = $this->model[$this->property_count];

            if ($this->include_parent && isset($this->model->linkParent) && isset($this->model->linkParent->movementParent)) {
                $i_counter += $this->model->linkParent->movementParent[$this->property_count];
            }

            if ($i_counter > 0) {
                echo "<div class='file-loading'>";
                // ocultar el panel de vista previa : data-show-preview='false'
                echo "<input id='{$this->input_id}' name='MovementAttachment[]' data-show-preview='{$this->data_show_preview}' type='file' accept='{$this->accept}' multiple " . ($this->readonly ? "readonly" : "") . ">";
                echo "</div>";
            } else {
                echo Strings::NO_ATTACHMENT;
            }
        } else {
            echo "<div class='file-loading'>";
            // ocultar el panel de vista previa : data-show-preview='false'
            echo "<input id='{$this->input_id}' name='MovementAttachment[]' data-show-preview='{$this->data_show_preview}' type='file' accept='{$this->accept}' multiple " . ($this->readonly ? "readonly" : "") . ">";
            echo "</div>";
        }

        if ($this->has_panel) {
            $this->endWidget();
        } else {
            echo "</div>";
        }
    }

}
