<?php

class SIANWarehouseCheckedGrid extends CWidget {

    public $id;
    public $form;
    public $model;
    public $items;
    public $checkedItems;
    public $readonly = false;
    public $ifixed = 6;
    public $tfixed = 2;
    public $step = 0.0001;
    public $advanced = false;
    public $combinations = false;
    public $update_ubications = false;
    public $allow_duplicate_checkbox_id;
    public $physical_id;
    public $level_quantity_id;
    public $onChangeCCDependence = '';
    public $weight_input_id;
    public $number_packages_input_id; // input N° de bultos
    public $allow_generating_dispatch_id;
    //PRIVATE
    private $dataProvider;
    private $presentationMode = SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES;
    private $presentationItems = [];
    private $preview_access;
    private $local_storage_name;
    private $global_use_batch;
    private $pks = [];

    public function init() {

        //CONTROL
        if ($this->model->movement->validate_stock == 1 && !isset($this->model->movement->stock_mode)) {
            throw new Exception('Se necesita un modo de stock');
        }
        //GRID ID
        $this->id = isset($this->id) ? $this->id : Yii::app()->controller->getServerId();
        $this->allow_generating_dispatch_id = isset($this->allow_generating_dispatch_id) ? $this->allow_generating_dispatch_id : Yii::app()->controller->getServerId();
        $this->allow_duplicate_checkbox_id = $this->controller->getServerId();
        $this->physical_id = $this->controller->getServerId();
        $this->level_quantity_id = $this->controller->getServerId();
        $this->local_storage_name = 'datos_' . $this->id;
        $this->global_use_batch = Yii::app()->controller->getOrganization()->globalVar->use_batch == GlobalVar::BATCH_USE_BATCH_NO ? 0 : 1;
        // Variables para `peso` y `numero de bultos`
        $this->weight_input_id = isset($this->weight_input_id) ? $this->weight_input_id : Yii::app()->controller->getServerId();
        $this->number_packages_input_id = isset($this->number_packages_input_id) ? $this->number_packages_input_id : Yii::app()->controller->getServerId();

        //CHECKED ITEMS
        $a_batch_detail = [];
        foreach ($this->model->tempItems as $o_item) {

            if (USString::isBlank($o_item->itemObj->parent_item_id)) {
                throw new Exception('No está seteado el campo parent_item_id para uno o más ítems que deben ir en la grilla.');
            }
            if ($this->global_use_batch == 1) {
                foreach ($o_item->tempBatchDetails as $batchDetail) {
                    $attributes2 = [];
                    $attributes2['batch_id'] = $batchDetail->batch_id;
                    $attributes2['batch_code'] = $batchDetail->batch_code;
                    $attributes2['direction'] = $batchDetail->direction;
                    $attributes2['unit_stock'] = $batchDetail->unit_stock;
                    $attributes2['pres_stock'] = $batchDetail->pres_stock;
                    $attributes2['pres_stock_label'] = $batchDetail->pres_stock_label;
                    $attributes2['pres_quantity'] = $batchDetail->pres_quantity;
                    $attributes2['expiration_date'] = $batchDetail->expiration_date;
                    $a_batch_detail[$o_item->itemObj->parent_item_id][] = $attributes2;
                }
            }
            $this->checkedItems[$o_item->itemObj->parent_item_id] = $o_item;
        }
        //ITEMS
        $product_ids = [];

        foreach ($this->items as $spItem) {
            $product_ids[] = $spItem->product_id;
            $this->pks[] = $spItem->pk;
        }
        $this->presentationItems = SpGetProductPresentations::getAssociative($this->presentationMode, $product_ids, $this->controller->getOrganization()->globalVar->display_currency);

        foreach ($this->items as $spItem) {

            $spItem['id'] = $spItem->parent_item_id;
            $spItem['original_pres_quantity'] = $spItem->pres_quantity;
            //BALANCES
            switch ($spItem->currency) {
                case Currency::PEN:
                    //MAX
                    $spItem->max_bcost_pen = $spItem->bcost;
                    $spItem->max_icost_pen = $spItem->icost;
                    $spItem->max_ecost_pen = $spItem->ecost;
                    $spItem->max_dcost_pen = $spItem->dcost;
                    $spItem->max_bcost_usd = round($spItem->bcost / $this->model->movement->exchange_rate, $this->ifixed);
                    $spItem->max_icost_usd = round($spItem->icost / $this->model->movement->exchange_rate, $this->ifixed);
                    $spItem->max_ecost_usd = round($spItem->ecost / $this->model->movement->exchange_rate, $this->ifixed);
                    $spItem->max_dcost_usd = round($spItem->dcost / $this->model->movement->exchange_rate, $this->ifixed);
                    //SALE
                    $spItem->sale_cost_pen = $spItem->sale_cost;
                    $spItem->sale_cost_usd = round($spItem->sale_cost / $this->model->movement->exchange_rate, $this->ifixed);
                    break;
                case Currency::USD:
                    //MAX
                    $spItem->max_bcost_pen = round($spItem->bcost * $this->model->movement->exchange_rate, $this->ifixed);
                    $spItem->max_icost_pen = round($spItem->icost * $this->model->movement->exchange_rate, $this->ifixed);
                    $spItem->max_ecost_pen = round($spItem->ecost * $this->model->movement->exchange_rate, $this->ifixed);
                    $spItem->max_dcost_pen = round($spItem->dcost * $this->model->movement->exchange_rate, $this->ifixed);
                    $spItem->max_bcost_usd = $spItem->bcost;
                    $spItem->max_icost_usd = $spItem->icost;
                    $spItem->max_ecost_usd = $spItem->ecost;
                    $spItem->max_dcost_usd = $spItem->dcost;
                    //SALE
                    $spItem->max_cost_pen = round($spItem->sale_cost * $this->model->movement->exchange_rate, $this->ifixed);
                    $spItem->sale_cost_usd = $spItem->sale_cost;
                    break;
                default:
                    throw new Exception('Moneda inválida!', USREST::CODE_INTERNAL_SERVER_ERROR);
            }
            
            if (isset($this->checkedItems[$spItem->parent_item_id])) {
                $spItem->checked = true;
                $spItem->equivalence = $this->checkedItems[$spItem->parent_item_id]->itemObj->equivalence;
                $spItem->item_type_id = $this->checkedItems[$spItem->parent_item_id]->itemObj->item_type_id;
                $spItem->pres_quantity = $this->checkedItems[$spItem->parent_item_id]->itemObj->pres_quantity;
                $spItem->series = $this->checkedItems[$spItem->parent_item_id]->arraySeries;
                $spItem->bcost = number_format($this->checkedItems[$spItem->parent_item_id]->{"bcost_{$this->model->movement->currency}"}, $this->ifixed, '.', '');
                $spItem->icost = number_format($this->checkedItems[$spItem->parent_item_id]->{"icost_{$this->model->movement->currency}"}, $this->ifixed, '.', '');
                $spItem->ecost = number_format($this->checkedItems[$spItem->parent_item_id]->{"ecost_{$this->model->movement->currency}"}, $this->ifixed, '.', '');
                $spItem->dcost = number_format($this->checkedItems[$spItem->parent_item_id]->{"dcost_{$this->model->movement->currency}"}, $this->ifixed, '.', '');

                if ($this->checkedItems[$spItem->parent_item_id]->itemObj->hasErrors('pres_quantity')) {
                    $spItem->addError('pres_quantity', $this->checkedItems[$spItem->parent_item_id]->itemObj->getError('pres_quantity'));
                }

                if ($this->checkedItems[$spItem->parent_item_id]->hasErrors('arraySeries')) {
                    $spItem->addError('series', $this->checkedItems[$spItem->parent_item_id]->getError('arraySeries'));
                }

                if ($this->checkedItems[$spItem->parent_item_id]->itemObj->hasErrors('has_ubication')) {
                    $spItem->addError('has_ubication', $this->checkedItems[$spItem->parent_item_id]->getError('has_ubication'));
                }

                if ($this->checkedItems[$spItem->parent_item_id]->hasErrors("bcost_{$this->model->movement->currency}")) {
                    $spItem->addError('bcost', $this->checkedItems[$spItem->parent_item_id]->getError("bcost_{$this->model->movement->currency}"));
                }

                if ($this->checkedItems[$spItem->parent_item_id]->hasErrors("icost_{$this->model->movement->currency}")) {
                    $spItem->addError('icost', $this->checkedItems[$spItem->parent_item_id]->getError("icost_{$this->model->movement->currency}"));
                }

                if ($this->checkedItems[$spItem->parent_item_id]->hasErrors("ecost_{$this->model->movement->currency}")) {
                    $spItem->addError('ecost', $this->checkedItems[$spItem->parent_item_id]->getError("ecost_{$this->model->movement->currency}"));
                }

                if ($this->checkedItems[$spItem->parent_item_id]->hasErrors("dcost_{$this->model->movement->currency}")) {
                    $spItem->addError('dcost', $this->checkedItems[$spItem->parent_item_id]->getError("dcost_{$this->model->movement->currency}"));
                }

                if ($this->checkedItems[$spItem->parent_item_id]->hasErrors("cost_{$this->model->movement->currency}")) {
                    $spItem->addError('cost', $this->checkedItems[$spItem->parent_item_id]->getError("cost_{$this->model->movement->currency}"));
                }
            } else {
                $spItem->checked = false;
                $spItem->unit_stock = 0;
                $spItem->series = [];

                //MONTO
                switch ($this->model->movement->currency) {
                    case Currency::PEN:
                        $spItem->bcost = $spItem->max_bcost_pen;
                        $spItem->icost = $spItem->max_icost_pen;
                        $spItem->ecost = $spItem->max_ecost_pen;
                        $spItem->dcost = $spItem->max_dcost_pen;
                        $spItem->sale_cost = $spItem->sale_cost_pen;
                        break;
                    case Currency::USD:
                        $spItem->bcost = $spItem->max_bcost_usd;
                        $spItem->icost = $spItem->max_icost_usd;
                        $spItem->ecost = $spItem->max_ecost_usd;
                        $spItem->dcost = $spItem->max_dcost_usd;
                        $spItem->sale_cost = $spItem->sale_cost_usd;
                        break;
                    default:
                        throw new Exception('Moneda inválida!', USREST::CODE_INTERNAL_SERVER_ERROR);
                }
            }
        }

        //DATA PROVIDER
        $this->dataProvider = new USArrayDataProvider($this->items, array(
            'keyField' => array('parent_item_id'), // PRIMARY KEY
            'sort' => false,
        ));
        $this->dataProvider->pagination = false;

        //ACCESS
        $this->preview_access = $this->controller->checkRoute('/logistic/product/preview');
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-warehouse-checked-grid.js');
        SIANAssets::registerScriptFile('js/sian-warehouse-general-grid.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript(Yii::app()->controller->getServerId(), "

        var grid = $('#{$this->id}');
        grid.data('ifixed', {$this->ifixed});
        grid.data('tfixed', {$this->tfixed});
        grid.data('currency', '{$this->model->movement->currency}');
        grid.data('exchange_rate', '{$this->model->movement->exchange_rate}');
        grid.data('direction', '{$this->model->movement->direction}');
        grid.data('route', '{$this->model->movement->route}');
        grid.data('stockUrl', '{$this->controller->createUrl("/movement/loadCostsAndStocks")}');
        grid.data('stock_mode', {$this->model->movement->stock_mode});
        grid.data('exclude_id', " . CJSON::encode($this->model->movement->kardex_unlock_exclude_id) . ");
        grid.data('count', 0);//COUNT
        grid.data('allow_duplicate', $('#{$this->allow_duplicate_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('independent_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INDEPENDENT . "');
        grid.data('inherit_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INHERIT . "');
        grid.data('get-url-batch-stock', '{$this->controller->createUrl("/logistic/batch/getBatchStock")}');
        grid.data('view-url-batch', '{$this->controller->createUrl('/logistic/batch/batchMovement')}');//URL BATCH
        grid.data('local_storage_name', '{$this->local_storage_name}');
        grid.data('auto-batch-output', '" . Yii::app()->controller->getOrganization()->globalVar->auto_batch_output . "');
        grid.data('order-batch', '" . Yii::app()->controller->getOrganization()->globalVar->order_batch_output . "');
        grid.data('batch_detail', " . CJSON::encode($a_batch_detail) . "); 
        grid.data('ready', 0); 
        grid.data('weight_input_id', '{$this->weight_input_id}');
        grid.data('number_packages_input_id', '{$this->number_packages_input_id}');
        grid.data('packing_code', {$this->model->movement->packing_code});
        SIANWarehouseCheckedGridCalculate('{$this->id}');
            
        $(document).ready(function() {   
            " . (($this->model->movement->direction == Scenario::DIRECTION_IN && $this->model->movement->route != 'warehouse/transferenceIn') ?
                        " 
                setTimeout(() => {
                    SIANWarehouseCheckedGridLoad('{$this->id}'); 
                }, 1000); " : "") . "
                window.sianWarehouseCheckedGridPks = " . json_encode($this->pks) . ";
        });

        //CURRENCY SYMBOL
        $('span.currency-symbol').text('" . Currency::getSymbol($this->model->movement->currency) . "');
        
        $('#{$this->id}').data('changeOperation', function(changeData){
            
            var divObj = $('#{$this->id}');
            //GUARDAMOS INFO DE OPERACIÓN
            divObj.data('operationChangeData', changeData);
            SIANWarehouseCheckedGridCalculate('{$this->id}');
        });
        
        $('#{$this->id}').data('changeCCDependence', function(operationChangeData){
            {$this->onChangeCCDependence};
        });
        
        //SI CAMBIA LA MONEDA
        $('#{$this->id}').data('changeCurrency', function(currency){

            var grid = $('#{$this->id}');
            var exchange_rate = grid.data('exchange_rate');
            var ifixed = grid.data('ifixed');
            var tfixed = grid.data('tfixed')

            grid.find('tr.sian-warehouse-checked-grid-item').each(function(index) {

                var item = $(this);

                var max_bcost_pen = item.find('input.sian-warehouse-checked-grid-item-max-bcost-pen').floatVal({$this->ifixed});
                var max_icost_pen = item.find('input.sian-warehouse-checked-grid-item-max-icost-pen').floatVal({$this->ifixed});
                var max_ecost_pen = item.find('input.sian-warehouse-checked-grid-item-max-ecost-pen').floatVal({$this->ifixed});
                var max_dcost_pen = item.find('input.sian-warehouse-checked-grid-item-max-dcost-pen').floatVal({$this->ifixed});
                var max_bcost_usd = item.find('input.sian-warehouse-checked-grid-item-max-bcost-usd').floatVal({$this->ifixed});
                var max_icost_usd = item.find('input.sian-warehouse-checked-grid-item-max-icost-usd').floatVal({$this->ifixed});
                var max_ecost_usd = item.find('input.sian-warehouse-checked-grid-item-max-ecost-usd').floatVal({$this->ifixed});
                var max_dcost_usd = item.find('input.sian-warehouse-checked-grid-item-max-dcost-usd').floatVal({$this->ifixed});

                //Actualizamos montos
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-warehouse-checked-grid-item-bcost').toPen(exchange_rate, ifixed, {max: max_bcost_pen});
                        item.find('input.sian-warehouse-checked-grid-item-icost').toPen(exchange_rate, ifixed, {max: max_icost_pen});
                        item.find('input.sian-warehouse-checked-grid-item-ecost').toPen(exchange_rate, ifixed, {max: max_ecost_pen});
                        item.find('input.sian-warehouse-checked-grid-item-dcost').toPen(exchange_rate, ifixed, {max: max_dcost_pen});
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-warehouse-checked-grid-item-bcost').toUsd(exchange_rate, ifixed, {max: max_bcost_usd});
                        item.find('input.sian-warehouse-checked-grid-item-icost').toUsd(exchange_rate, ifixed, {max: max_icost_usd});
                        item.find('input.sian-warehouse-checked-grid-item-ecost').toUsd(exchange_rate, ifixed, {max: max_ecost_usd});
                        item.find('input.sian-warehouse-checked-grid-item-dcost').toUsd(exchange_rate, ifixed, {max: max_dcost_usd});
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
            });

            //SET
            grid.data('currency', currency);
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            SIANWarehouseCheckedGridCalculate('{$this->id}');
        });    

        $('#{$this->id}').data('changeExchange', function(exchange_rate){

            var grid = $('#{$this->id}');
                
            //Seteamos
            grid.data('exchange_rate', exchange_rate); 
            
            //Obtenemos
            var currency = grid.data('currency');
            var ifixed = grid.data('ifixed');
            var tfixed = grid.data('tfixed');

            grid.find('tr.sian-warehouse-checked-grid-item').each(function(index) {
            
                var item = $(this);
                var item_currency = item.find('input.sian-warehouse-checked-grid-item-currency').val();
                //MAX
                var max_bcost_pen = item.find('input.sian-warehouse-checked-grid-item-max-bcost-pen').floatVal({$this->ifixed});
                var max_icost_pen = item.find('input.sian-warehouse-checked-grid-item-max-icost-pen').floatVal({$this->ifixed});
                var max_ecost_pen = item.find('input.sian-warehouse-checked-grid-item-max-ecost-pen').floatVal({$this->ifixed});
                var max_dcost_pen = item.find('input.sian-warehouse-checked-grid-item-max-dcost-pen').floatVal({$this->ifixed});
                var max_bcost_usd = item.find('input.sian-warehouse-checked-grid-item-max-bcost-usd').floatVal({$this->ifixed});
                var max_icost_usd = item.find('input.sian-warehouse-checked-grid-item-max-icost-usd').floatVal({$this->ifixed});
                var max_ecost_usd = item.find('input.sian-warehouse-checked-grid-item-max-ecost-usd').floatVal({$this->ifixed});
                var max_dcost_usd = item.find('input.sian-warehouse-checked-grid-item-max-dcost-usd').floatVal({$this->ifixed});
                //SALE
                var sale_cost_pen = item.find('input.sian-warehouse-checked-grid-item-sale-cost-pen').floatVal({$this->ifixed});
                var sale_cost_usd = item.find('input.sian-warehouse-checked-grid-item-sale-cost-usd').floatVal({$this->ifixed});
                    
                //Cambiamos los balances
                switch(item_currency)
                {
                    case '" . Currency::PEN . "':
                        //MAX
                        max_bcost_usd = max_bcost_pen / exchange_rate;
                        max_icost_usd = max_icost_pen / exchange_rate;
                        max_ecost_usd = max_ecost_pen / exchange_rate;
                        max_dcost_usd = max_dcost_pen / exchange_rate;
                        //SALE
                        sale_cost_usd = sale_cost_pen / exchange_rate;
                    break;
                    case '" . Currency::USD . "':
                        //MAX
                        max_bcost_pen = max_bcost_usd * exchange_rate;
                        max_icost_pen = max_icost_usd * exchange_rate;
                        max_ecost_pen = max_ecost_usd * exchange_rate;
                        max_dcost_pen = max_dcost_usd * exchange_rate;
                        //SALE
                        sale_cost_pen = sale_cost_usd * exchange_rate;
                     break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }

                //CAMBIAMOS EL MAX
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        item.find('input.sian-warehouse-checked-grid-item-bcost').floatAttr('max', {$this->ifixed}, max_bcost_pen);
                        item.find('input.sian-warehouse-checked-grid-item-icost').floatAttr('max', {$this->ifixed}, max_icost_pen);
                        item.find('input.sian-warehouse-checked-grid-item-ecost').floatAttr('max', {$this->ifixed}, max_ecost_pen);
                        item.find('input.sian-warehouse-checked-grid-item-dcost').floatAttr('max', {$this->ifixed}, max_dcost_pen);
                    break;
                    case '" . Currency::USD . "':
                        item.find('input.sian-warehouse-checked-grid-item-bcost').floatAttr('max', {$this->ifixed}, max_bcost_usd);
                        item.find('input.sian-warehouse-checked-grid-item-icost').floatAttr('max', {$this->ifixed}, max_icost_usd);
                        item.find('input.sian-warehouse-checked-grid-item-ecost').floatAttr('max', {$this->ifixed}, max_ecost_usd);
                        item.find('input.sian-warehouse-checked-grid-item-dcost').floatAttr('max', {$this->ifixed}, max_dcost_usd);
                     break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida!', 'error'));
                    break;
                }
                    
                //Seteamos balances
                //MAX
                item.find('input.sian-warehouse-checked-grid-item-max-bcost-pen').floatVal({$this->ifixed}, max_bcost_pen);
                item.find('input.sian-warehouse-checked-grid-item-max-icost-pen').floatVal({$this->ifixed}, max_icost_pen);
                item.find('input.sian-warehouse-checked-grid-item-max-ecost-pen').floatVal({$this->ifixed}, max_ecost_pen);
                item.find('input.sian-warehouse-checked-grid-item-max-dcost-pen').floatVal({$this->ifixed}, max_dcost_pen);
                item.find('input.sian-warehouse-checked-grid-item-max-bcost-usd').floatVal({$this->ifixed}, max_bcost_usd);
                item.find('input.sian-warehouse-checked-grid-item-max-icost-usd').floatVal({$this->ifixed}, max_icost_usd);
                item.find('input.sian-warehouse-checked-grid-item-max-ecost-usd').floatVal({$this->ifixed}, max_ecost_usd);
                item.find('input.sian-warehouse-checked-grid-item-max-dcost-usd').floatVal({$this->ifixed}, max_dcost_usd);
                //SALE
                item.find('input.sian-warehouse-checked-grid-item-sale-cost-pen').floatVal({$this->ifixed}, sale_cost_pen);
                item.find('input.sian-warehouse-checked-grid-item-sale-cost-usd').floatVal({$this->ifixed}, sale_cost_usd);
            });

            //SET
            grid.data('exchange_rate', exchange_rate);
                
            //Actualizamos montos
            SIANWarehouseCheckedGridCalculate('{$this->id}');
        });
        
        $('#{$this->id}').data('changeEmission', function(changeData){
            var grid = $('#{$this->id}');
            //Seteamos
            grid.data('emission_date', changeData.emission_date);
            grid.find('a.sian-warehouse-checked-grid-item-mixed-stock').data('emission_date', changeData.emission_date);             
            //Obtenemos
            var warehouse_id = grid.data('warehouse_id');
            if(warehouse_id !== undefined && warehouse_id.length > 0)
            {
                SIANWarehouseCheckedGridLoadStocks(warehouse_id, changeData.emission_date, '{$this->id}');                
                " . ($this->global_use_batch == 1 && ($this->model->movement->direction == Scenario::DIRECTION_OUT || ($this->model->movement->direction == Scenario::DIRECTION_IN && $this->model->movement->direction = 'warehouse/transferenceIn')) ? "  
                
                    grid.find('tr.sian-warehouse-checked-grid-item').each(function (index) {
                    
                    var rowObj = $(this);
                    var use_batch = rowObj.find('input.sian-warehouse-checked-grid-item-use-batch').val();
                    if(use_batch == 1){
                        var isReady = grid.data('ready');
                        console.log('listo en cambio de fecha: ' + isReady);
                        if(isReady == 1){
                            console.log('entre en cambio de fecha');
                            SIANWarehouseGeneralGridBatch('{$this->id}', rowObj.attr('id'), '-checked');
                        }                        
                    }
                });
                
                " : "") . "
            }
        });
        
        $('#{$this->id}').data('changeWarehouse', function(changeData){
            var grid = $('#{$this->id}');
            //Seteamos
            grid.data('warehouse_id', changeData.warehouse_id);
            grid.find('a.sian-warehouse-checked-grid-item-mixed-stock').data('warehouse_id', changeData.warehouse_id);     
            $('#{$this->physical_id}').val(changeData.physical); 
            $('#{$this->level_quantity_id}').val(changeData.level_quantity); 
            //Obtenemos
            var emission_date = grid.data('emission_date');
            //
            if(changeData.warehouse_id !== 'undefined' && changeData.warehouse_id.length > 0 && emission_date)
            {
                SIANWarehouseCheckedGridLoadStocks(changeData.warehouse_id, emission_date, '{$this->id}');
            }            
            if(changeData.physical == 1 && changeData.level_quantity > 0){
                grid.find('th.sian-warehouse-checked-grid-column-ubications').css('display', '');
                grid.find('td.sian-warehouse-checked-grid-column-ubications').css('display', '');
                " . ($this->update_ubications ? " 
                grid.find('a.sian-warehouse-checked-grid-option-update_ubications').data('warehouse_id', changeData.warehouse_id);
                grid.find('a.sian-warehouse-checked-grid-option-update_ubications').css('display', '');" : "") . "
            }else{
                grid.find('th.sian-warehouse-checked-grid-column-ubications').css('display', 'none');
                grid.find('td.sian-warehouse-checked-grid-column-ubications').css('display', 'none');
                " . ($this->update_ubications ? " 
                grid.find('a.sian-warehouse-checked-grid-option-update_ubications').data('warehouse_id', changeData.warehouse_id);
                grid.find('a.sian-warehouse-checked-grid-option-update_ubications').css('display', 'none');" : "") . "
            }
        });
        " . ($this->update_ubications ? " 
        $('#{$this->id}').data('afterWarehouseArea', function(data){
            //Lógica de buscar qué filas tienen el product_id que viene en data, y actualizar esos
            var grid = $('#{$this->id}');
            grid.find('tr.sian-warehouse-checked-grid-item').each(function(index) {
                var item = $(this);
                var product_id = item.find('input.sian-warehouse-checked-grid-item-product-id').val();
                if(product_id == data.product_id){
                    item.find('p.sian-warehouse-checked-grid-item-ubications').empty().append(data.warehouseAreaLabel).css('color', 'black');
                    item.find('a.sian-warehouse-checked-grid-option-update_ubications').find('span').removeClass('red').addClass('black');
                }
            });            
        });
        " : "") . "
            
        $('body').on('change', '#{$this->id} select.sian-warehouse-checked-grid-item-equivalence', function(e) {

            var equivalenceObj = $(this);
            var row = equivalenceObj.closest('tr');
            var table = row.closest('table');
            
            var maxUnitQuantityInput = row.find('input.sian-warehouse-checked-grid-item-max-unit-quantity');
            var presQuantityInput = row.find('input.sian-warehouse-checked-grid-item-pres-quantity');
            presQuantityInput.floatAttr('max', 2, maxUnitQuantityInput.double2() / equivalenceObj.double2());
            //VALORES
            var presBCostObj = row.find('input.sian-warehouse-checked-grid-item-bcost');
            var presICostObj = row.find('input.sian-warehouse-checked-grid-item-icost');
            var presECostObj = row.find('input.sian-warehouse-checked-grid-item-ecost');
            var presDCostObj = row.find('input.sian-warehouse-checked-grid-item-dcost');
            //MAX
            var maxPresBCostPenObj = row.find('input.sian-warehouse-checked-grid-item-max-bcost-pen');
            var maxPresICostPenObj = row.find('input.sian-warehouse-checked-grid-item-max-icost-pen');
            var maxPresECostPenObj = row.find('input.sian-warehouse-checked-grid-item-max-ecost-pen');
            var maxPresDCostPenObj = row.find('input.sian-warehouse-checked-grid-item-max-dcost-pen');
            var maxPresBCostUsdObj = row.find('input.sian-warehouse-checked-grid-item-max-bcost-usd');
            var maxPresICostUsdObj = row.find('input.sian-warehouse-checked-grid-item-max-icost-usd');
            var maxPresECostUsdObj = row.find('input.sian-warehouse-checked-grid-item-max-ecost-usd');
            var maxPresDCostUsdObj = row.find('input.sian-warehouse-checked-grid-item-max-dcost-usd');
            //SALE
            var minPresCostPenObj = row.find('input.sian-warehouse-checked-grid-item-sale-cost-pen');
            var minPresCostUsdObj = row.find('input.sian-warehouse-checked-grid-item-sale-cost-usd');

            var mult = 1 / equivalenceObj.floatData('equivalence', 2) * equivalenceObj.double2();
            //VALORES
            presBCostObj.floatVal({$this->ifixed}, presBCostObj.floatVal({$this->ifixed}) * mult);
            presICostObj.floatVal({$this->ifixed}, presICostObj.floatVal({$this->ifixed}) * mult);
            presECostObj.floatVal({$this->ifixed}, presECostObj.floatVal({$this->ifixed}) * mult);
            presDCostObj.floatVal({$this->ifixed}, presDCostObj.floatVal({$this->ifixed}) * mult);
            //MAX
            maxPresBCostPenObj.floatVal({$this->ifixed}, maxPresBCostPenObj.floatVal({$this->ifixed}) * mult);
            maxPresICostPenObj.floatVal({$this->ifixed}, maxPresICostPenObj.floatVal({$this->ifixed}) * mult);
            maxPresECostPenObj.floatVal({$this->ifixed}, maxPresECostPenObj.floatVal({$this->ifixed}) * mult);
            maxPresDCostPenObj.floatVal({$this->ifixed}, maxPresDCostPenObj.floatVal({$this->ifixed}) * mult);
            maxPresBCostUsdObj.floatVal({$this->ifixed}, maxPresBCostUsdObj.floatVal({$this->ifixed}) * mult);
            maxPresICostUsdObj.floatVal({$this->ifixed}, maxPresICostUsdObj.floatVal({$this->ifixed}) * mult);
            maxPresECostUsdObj.floatVal({$this->ifixed}, maxPresECostUsdObj.floatVal({$this->ifixed}) * mult);
            maxPresDCostUsdObj.floatVal({$this->ifixed}, maxPresDCostUsdObj.floatVal({$this->ifixed}) * mult);
            //SALE
            minPresCostPenObj.floatVal({$this->ifixed}, minPresCostPenObj.floatVal({$this->ifixed}) * mult);
            minPresCostUsdObj.floatVal({$this->ifixed}, minPresCostUsdObj.floatVal({$this->ifixed}) * mult);

            equivalenceObj.floatData('equivalence', 2, equivalenceObj.double2());
            //Load stock actualizará los montos
            SIANWarehouseCheckedGridLoadStock('{$this->id}', row);
                
            var use_batch = $(row).find('input.sian-warehouse-checked-grid-item-use-batch').val();
            
            if(use_batch == 1){                   
                var isReady = $('#{$this->id}').data('ready');
                console.log('listo en cambio de equivalence: ' + isReady);
                if(isReady == 1){
                    SIANWarehouseGeneralGridBatch('{$this->id}', row.attr('id'), '-checked');
                }
            }
       });

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $preview_access = $this->preview_access;
        $form = $this->form;
        $form_currency = $this->model->movement->currency;
        $stock_mode = $this->model->movement->stock_mode;
        $emission_date = $this->model->movement->emission_date;
        $exclude_id = $this->model->movement->kardex_unlock_exclude_id;
        $warehouse_id = $this->model->warehouse_id;

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Mercaderías',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempItems') ? 'us-error' : ''
            )
        ));

        $this->widget('application.widgets.SIANSearchGrid', array(
            'id' => $this->controller->getServerId(),
            'property_pks' => "window.sianWarehouseCheckedGridPks",
            'onCheck' => "SIANWarehouseCheckedGridCheckItem('{$this->id}', pk, pres_quantity, true);",
            'onUncheck' => "SIANWarehouseCheckedGridCheckItem('{$this->id}', pk, null, false);",
            'deactivateList' => "SIANWarehouseCheckedGridDisabledList('{$this->id}');",
            'resetList' => "SIANWarehouseCheckedGridResetList('{$this->id}');",
        ));

        $counter = 0;
        $columns = [];

        array_push($columns, array(
            'header' => CHtml::checkBox("All", $this->getGlobalCheckBoxState(), array(
                'class' => 'sian-warehouse-checked-grid-check',
                'onchange' => 'SIANWarehouseCheckedGridGridCheck($(this));',
                'readonly' => $this->readonly,
            )),
            'headerHtmlOptions' => array('style' => 'width:2%; text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) {
                return CHtml::checkBox("Checked[{$row->parent_item_id}]", $row->checked, array(
                    'class' => 'sian-warehouse-checked-grid-item-check',
                    'onchange' => 'SIANWarehouseCheckedGridRowCheck($(this));',
                    'readonly' => $this->readonly,
                ));
            },
        ));

        array_push($columns, array(
            'header' => '#',
            'headerHtmlOptions' => array('style' => 'width:2%;'),
            'type' => 'raw',
            'value' => function ($row) use (&$counter) {
                return "<span class='sian-warehouse-checked-grid-item-index'>" . (++$counter) . "</span>";
            },
        ));

        array_push($columns, array(
            'header' => 'ID',
            'headerHtmlOptions' => array('style' => 'width:6%;text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->textFieldRow($row, 'product_id', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][product_id]",
                    'class' => 'sian-warehouse-checked-grid-item-product-id',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right;',
                ));
            },
        ));

        $width = 15;

        //Si NO es avanzado aumentamos 40 porque no se mostrarán los montos
        if (!$this->advanced) {
            $width += 37;
        }

        array_push($columns, array(
            'header' => 'Producto',
            'headerHtmlOptions' => array('style' => "width:{$width}%;text-align:center;"),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->textFieldRow($row, 'product_name', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][product_name]",
                    'class' => 'sian-warehouse-checked-grid-item-product-name sian-force-tooltip',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'title' => $row->product_name
                ));
            },
        ));

        array_push($columns, array(
            'header' => 'Stock *',
            'headerHtmlOptions' => array('style' => 'width:5%;text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:right;'),
            'type' => 'raw',
            'value' => function ($row) use ($form, $stock_mode, $emission_date, $warehouse_id, $exclude_id) {
                return $this->widget('application.widgets.USLink', array(
                    'route' => '/logistic/merchandise/explainStock',
                    'label' => '0',
                    'title' => '¿Por qué veo este stock?',
                    'class' => 'form sian-warehouse-checked-grid-item-mixed-stock',
                    'data' => array(
                        'id' => $row->product_id,
                        'equivalence' => $row->equivalence,
                        'stock_mode' => $stock_mode,
                        'emission_date' => $emission_date,
                        'warehouse_id' => $warehouse_id,
                        'exclude_id' => $exclude_id,
                    ),
                    'visible' => true
                        ), true);
            },
        ));

        array_push($columns, array(
            'header' => 'Restante',
            'headerHtmlOptions' => array('style' => 'width:5%;text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:right;'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = "<span class='sian-warehouse->checked-grid-item-max-pres-quantity'>{$row->max_pres_quantity}</span>";
                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_pres_quantity]", $row->max_pres_quantity, array(
                            'class' => 'sian-warehouse->checked-grid-item-max-pres-quantity',
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));
                return $html;
            },
        ));

        array_push($columns, array(
            'header' => 'Peso',
            'headerHtmlOptions' => array('style' => "width:5%;display:none;"),
            'htmlOptions' => array('style' => "width:5%;display:none;"),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->textFieldRow($row, 'weight', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][weight]",
                    'class' => 'sian-warehouse-checked-grid-item-weight',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'title' => $row->weight
                ));
            },
        ));

        array_push($columns, array(
            'header' => 'Cantidad',
            'headerHtmlOptions' => array('style' => 'width:7%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->numberFieldRow($row, 'pres_quantity', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][pres_quantity]",
                    'class' => "sian-warehouse-checked-grid-item-pres-quantity " . ($row->allow_decimals == 1 ? "us-double2" : "us-double0"),
                    'min' => $row->allow_decimals == 1 ? 0.01 : 0,
                    'max' => round($row->max_unit_quantity / $row->equivalence, $row->allow_decimals == 1 ? 2 : 0),
                    'step' => $row->allow_decimals == 1 ? 0.01 : 0,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right;',
                    'onchange' => "SIANWarehouseGeneralGridBatch('{$this->id}', $(this).closest('tr').attr('id'), '-checked');SIANWarehouseCheckedGridCalculate('{$this->id}');",
                    'readonly' => $this->readonly,
                ));
            },
        ));

        array_push($columns, array(
            'header' => 'Pres.',
            'headerHtmlOptions' => array('style' => 'width:6%'),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $this->renderPresentationSelect($row->parent_item_id, $row->product_id, $row->equivalence, $this->presentationItems, $this->readonly, !$row->checked);
            },
        ));

        array_push($columns, array(
            'header' => 'Hidden',
            'headerHtmlOptions' => array('style' => 'display:none'),
            'htmlOptions' => array('style' => 'display:none'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row) use ($form, $form_currency) {

                $html = CHtml::hiddenField("Item[{$row->parent_item_id}][item_type_id]", $row->item_type_id, array('class' => "sian-warehouse-checked-grid-item-item-type-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][pk]", $row->pk, array('class' => "sian-warehouse-checked-grid-item-pk",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][unit_stock]", $row->unit_stock, array('class' => "sian-warehouse-checked-grid-item-unit-stock",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_unit_quantity]", $row->max_unit_quantity, array('class' => "sian-warehouse-checked-grid-item-max-unit-quantity",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                if (isset($row->original_pres_quantity)) {
                    $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][original_pres_quantity]", $row->original_pres_quantity, array('class' => "sian-warehouse-checked-grid-item-original-pres-quantity",
                                'readonly' => true,
                                'disabled' => !$row->checked,
                    ));
                }

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][allow_decimals]", $row->allow_decimals, array('class' => "sian-warehouse-checked-grid-item-allow-decimals",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][use_batch]", $row->use_batch, array('class' => "sian-warehouse-checked-grid-item-use-batch",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][skip_cost_validation]", $row->skip_cost_validation, array('class' => "sian-warehouse-checked-grid-item-skip-cost-validation",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][currency]", $row->currency, array(
                            'class' => "sian-warehouse-checked-grid-item-currency",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                //MAX
                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_bcost_pen]", $row->max_bcost_pen, array(
                            'class' => "sian-warehouse-checked-grid-item-max-bcost-pen",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_dcost_pen]", $row->max_dcost_pen, array(
                            'class' => "sian-warehouse-checked-grid-item-max-dcost-pen",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_icost_pen]", $row->max_icost_pen, array(
                            'class' => "sian-warehouse-checked-grid-item-max-icost-pen",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_ecost_pen]", $row->max_ecost_pen, array(
                            'class' => "sian-warehouse-checked-grid-item-max-ecost-pen",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_bcost_usd]", $row->max_bcost_usd, array(
                            'class' => "sian-warehouse-checked-grid-item-max-bcost-usd",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_dcost_usd]", $row->max_dcost_usd, array(
                            'class' => "sian-warehouse-checked-grid-item-max-dcost-usd",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_icost_usd]", $row->max_icost_usd, array(
                            'class' => "sian-warehouse-checked-grid-item-max-icost-usd",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_ecost_usd]", $row->max_ecost_usd, array(
                            'class' => "sian-warehouse-checked-grid-item-max-ecost-usd",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));
                //SALE
                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][sale_cost_pen]", $row->sale_cost_pen, array(
                            'class' => "sian-warehouse-checked-grid-item-sale-cost-pen",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][sale_cost_usd]", $row->sale_cost_usd, array(
                            'class' => "sian-warehouse-checked-grid-item-sale-cost-usd",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][item_type_id]", $row->item_type_id, array(
                            'class' => "sian-warehouse-checked-grid-item-item-type-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                //ITEM LINKS
                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][parent_item_id]", $row->parent_item_id, array(
                            'class' => "sian-warehouse-checked-grid-item-parent-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][quantity_item_id]", $row->quantity_item_id, array(
                            'class' => "sian-warehouse-checked-grid-item-quantity-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                //PROMO
                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][promotion_item_id]", $row->promotion_item_id, array(
                            'class' => "sian-warehouse-checked-grid-item-promotion-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][promotion_gift_option_id]", $row->promotion_gift_option_id, array(
                            'class' => "ssian-warehouse-checked-grid-item-promotion-gift-option-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                //COMBINATIONS

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][combination_id]", $row->combination_id, array(
                            'class' => "sian-warehouse-checked-grid-item-combination-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][combination_name]", $row->combination_name, array(
                            'class' => "sian-warehouse-checked-grid-item-combination-name",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $s_url = null;
                if (isset($row->image)) {
                    $s_url = SIANImage::getMediumUrl($row->image, 'merchandise');
                }
                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][image]", $s_url, array(
                            'class' => "sian-warehouse-checked-grid-item-image",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                return $html;
            },
        ));

        array_push($columns, array(
            'header' => "Costo.F <span class='currency-symbol'></span> **",
            'headerHtmlOptions' => array('style' => "width:" . ($this->combinations ? '4%' : '6%') . "; display:" . ($this->advanced ? 'table-cell' : 'none')),
            'htmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'footerHtmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'type' => 'raw',
            'value' => function ($row) use ($form, $form_currency) {
                return $form->textFieldRow($row, 'bcost', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][bcost]",
                    'class' => 'sian-warehouse-checked-grid-item-bcost',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => "display:" . ($this->advanced ? 'table-cell' : 'none'),
                    'max' => $form_currency ? $row->max_bcost_pen : $row->max_bcost_usd,
                ));
            },
        ));

        array_push($columns, array(
            'header' => "Costo.Pro.I <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => "width:" . ($this->combinations ? '4%' : '6%') . "; display:" . ($this->advanced ? 'table-cell' : 'none')),
            'htmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'footerHtmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'type' => 'raw',
            'value' => function ($row) use ($form, $form_currency) {
                return $form->textFieldRow($row, 'icost', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][icost]",
                    'class' => 'sian-warehouse-checked-grid-item-icost',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right;',
                    'max' => $form_currency ? $row->max_icost_pen : $row->max_icost_usd,
                ));
            },
        ));

        array_push($columns, array(
            'header' => "Costo.Pro.E <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => "width:" . ($this->combinations ? '4%' : '6%') . "; display:" . ($this->advanced ? 'table-cell' : 'none')),
            'htmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'footerHtmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'type' => 'raw',
            'value' => function ($row) use ($form, $form_currency) {
                return $form->textFieldRow($row, 'ecost', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][ecost]",
                    'class' => 'sian-warehouse-checked-grid-item-ecost',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right;',
                    'max' => $form_currency ? $row->max_ecost_pen : $row->max_ecost_usd,
                ));
            },
        ));

        array_push($columns, array(
            'header' => "Descuento <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => "width:" . ($this->combinations ? '4%' : '6%') . "; display:" . ($this->advanced ? 'table-cell' : 'none')),
            'htmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'footerHtmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'type' => 'raw',
            'value' => function ($row) use ($form, $form_currency) {
                return $form->textFieldRow($row, 'dcost', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][dcost]",
                    'class' => 'sian-warehouse-checked-grid-item-dcost',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right;',
                    'max' => $form_currency ? $row->max_dcost_pen : $row->max_dcost_usd,
                ));
            },
        ));

        array_push($columns, array(
            'header' => "Costo <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => "width:5%; display:" . ($this->advanced ? 'table-cell' : 'none')),
            'htmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'footerHtmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->numberFieldRow($row, 'cost', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][cost]",
                    'class' => "sian-warehouse-checked-grid-item-cost",
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right'
                ));
            },
            'footer' => "<b>Neto:</b>",
        ));

        array_push($columns, array(
            'header' => "Neto <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => "width:5%; display:" . ($this->advanced ? 'table-cell' : 'none')),
            'htmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'footerHtmlOptions' => array('style' => "display:" . ($this->advanced ? 'table-cell' : 'none')),
            'type' => 'raw',
            'value' => function () {
                return "<span class='sian-warehouse-checked-grid-item-net'></span>";
            },
            'footer' => "<span class='sian-warehouse-checked-grid-total-net'></span>",
        ));

        array_push($columns, array(
            'header' => 'Series',
            'headerHtmlOptions' => array('style' => "width:" . ($this->combinations ? '4%' : '11%') . ";text-align:center;"),
            'type' => 'raw',
            'value' => function ($row) use ($form) {

                $html = $form->hiddenField($row, 'serialized', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][serialized]",
                    'class' => 'sian-warehouse-checked-grid-item-serialized',
                    'disabled' => !$row->checked
                ));

                if ($row->serialized) {
                    $html .= $form->textAreaRow($row, 'series', array(
                        'label' => false,
                        'name' => "Item[{$row->parent_item_id}][series]",
                        'value' => implode($row->series, "\n"),
                        'class' => 'sian-warehouse-checked-grid-item-series',
                        'disabled' => !$row->checked,
                    ));
                } else {
                    $html .= Strings::NOT_REQUIRED;
                }

                return $html;
            },
        ));

        array_push($columns, array(
            'header' => 'Ubicaciones',
            'headerHtmlOptions' => array('style' => 'width:5%;text-align:center; display:none', 'class' => 'sian-warehouse-checked-grid-column-ubications'),
            'htmlOptions' => array('style' => 'text-align:right;  display:none', 'class' => 'sian-warehouse-checked-grid-column-ubications'),
            'footerHtmlOptions' => array('class' => 'sian-warehouse-checked-grid-column-ubications'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = "<p class='sian-warehouse-checked-grid-item-ubications' style='color:" . ($row->hasErrors('has_ubication') ? 'red' : 'black') . ";'>{$row->ubications}</p>";
                return $html;
            },
        ));

        if ($this->combinations) {
            array_push($columns, array(
                'header' => 'CC',
                'headerHtmlOptions' => array('style' => "width:" . ($this->combinations ? '16%' : '1%') . "; text-align:center; display:" . ($this->combinations ? 'table-cell' : 'none')),
                'type' => 'raw',
                'value' => function ($row) use ($form) {

                    return $form->textAreaRow($row, 'combination_name', array(
                        'label' => false,
                        'name' => "Item[{$row->parent_item_id}][combination_name]",
                        'class' => 'sian-warehouse-checked-grid-item-combination_name sian-force-tooltip',
                        'readonly' => true,
                        'disabled' => !$row->checked,
                        'style' => 'text-align:left;',
                        'title' => $row->combination_name
                    ));
                },
            ));
        }
        $b_warehouse_area_access = $this->update_ubications ? $this->controller->checkRoute('/logistic/merchandise/warehouseAreas') : false;
        array_push($columns, array(
            'header' => 'Opc.',
            'headerHtmlOptions' => array('style' => 'width:8%;text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) use ($preview_access, $b_warehouse_area_access) {
                return Yii::app()->controller->widget('application.widgets.USLink', array(
                    'icon' => 'fa fa-eye fa-lg',
                    'route' => '/logistic/merchandise/preview',
                    'label' => '',
                    'title' => 'Visualizar',
                    'class' => 'form',
                    'data' => array(
                        'id' => $row->product_id
                    ),
                    'visible' => $preview_access
                        ), true) . " " . ($this->update_ubications ?
                Yii::app()->controller->widget('application.widgets.USLink', array(
                    'icon' => 'fa fa-map-marker fa-lg red',
                    'route' => '/logistic/merchandise/warehouseAreas',
                    'title' => 'Ubicaciones',
                    'color' => ($row->hasErrors('has_ubication') ? 'red' : 'black'),
                    'class' => 'form sian-warehouse-checked-grid-option-update_ubications',
                    'data' => array(
                        'id' => $row->product_id,
                        'warehouse_id' => $this->model->warehouse_id,
                        'type' => 'afterWarehouseArea',
                        'element_id' => $this->id
                    ),
                    'visible' => $b_warehouse_area_access
                        ), true) : "") . " " .
                ($row->use_batch == 1 ?
                "<a title = 'Lotes' class = 'sian-warehouse-checked-grid-batch' 
                            onclick = \"var row_id = $(this).closest('tr').attr('id'); 
                                      var product_id = $(this).closest('tr').find('input.sian-warehouse-checked-grid-item-product-id').val();
                                      var allow_decimals = $(this).closest('tr').find('input.sian-warehouse-checked-grid-item-allow-decimals').val();
                                      SIANWarehouseGeneralGridLoadBatchs(this, '{$this->id}', row_id, product_id, allow_decimals, '-checked');\"><span class='fa fa-calendar fa-lg black'></span></a>" :
                "<span class='fa fa-calendar fa-lg grey'></span>");
            },
        ));

        $gridParams = array(
            'id' => $this->id,
            'type' => 'bordered condensed',
            'dataProvider' => $this->dataProvider,
            'enableSorting' => false,
            'selectableRows' => 0,
            'columns' => $columns,
            'template' => '{items}',
            'rowCssClassExpression' => '"sian-warehouse-checked-grid-item " . ($data->checked == 1 ? "success" : "danger")',
        );
        $this->widget('application.widgets.USGridView', $gridParams);

        echo "<div class='row'>";
        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->allow_duplicate_checkbox_id,
            'model' => $this->model,
            'attribute' => 'allow_duplicate',
            'htmlOptions' => array(
                'readonly' => true
            )
                ), true);
        echo $form->hiddenField($this->model, 'physical', array(
            'id' => $this->physical_id,
        ));
        echo $form->hiddenField($this->model, 'level_quantity', array(
            'id' => $this->level_quantity_id,
        ));
        echo $form->hiddenField($this->model, 'allow_generating_dispatch', array(
            'id' => $this->allow_generating_dispatch_id
        ));
        echo "</div>";
        echo "</div>";

        if ($this->advanced) {
            echo "<p><b>(*)</b> El stock usado es el stock <b>{$this->model->movement->getStockModeLabel()}</b> a la fecha de emisión elegida " . (isset($this->model->movement->kardex_unlock_exclude_id) ? ' y para este movimiento en específico' : "") . "</p>";
            echo "<p><b>(**)</b> Todos los montos están sin IGV</p>";
        }

        $this->endWidget();
    }

    private function renderPresentationSelect($row_id, $product_id, $equivalence, $items, $readonly, $disabled) {
        $html = "<select class='form-control sian-warehouse-checked-grid-item-equivalence' name='Item[{$row_id}][equivalence]' data-equivalence={$equivalence} " . ($disabled ? 'disabled' : '') . " " . ($readonly ? 'readonly' : '') . ">";
        foreach ($items[$product_id] as $item) {
            $selected = $item['equivalence'] == $equivalence ? 'selected' : '';
            $html .= "<option value = '{$item['equivalence']}' {$selected}>{$item['measure_name']}</option>";
        }
        $html .= '</select>';
        return $html;
    }

    private function getGlobalCheckBoxState() {
        return count($this->model->tempItems) > 0;
    }

}
