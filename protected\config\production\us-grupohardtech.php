<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'grupohardtech.siansystem.com/admin';
$domain = 'https://grupohardtech.siansystem.com';
$domain2 = 'http://grupohardtech.siansystem.com';
$report_domain = 'rgrupohardtech.siansystem.com';
$org = 'grupohardtech';
//SIAN 2
$domain_react_app = 'https://grupohardtech2.siansystem.com';
$api_sian = 'https://api.siansystem.com/';
//database enterprise
$database_server = '161.132.48.88';
$database_name = 'grupohardtech';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
$e_billing_ose = YII_OSE_SUNAT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = 'grupohardtech.p12';
$e_billing_certificate_pass = '2021harTE';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20560020903BILLING7',
        'password' => 'Billing7'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = '75neb011w5w5m6';
$environment_reports = YII_ENVIRONMENT_PRODUCTION;
$environment = YII_ENVIRONMENT_PRODUCTION;
