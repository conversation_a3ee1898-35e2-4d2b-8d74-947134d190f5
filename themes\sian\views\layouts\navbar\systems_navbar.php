<?php

$arrayItemsNavbar = array(
    // array('icon' => 'list', 'label' => 'Datos Generales', 'url' => '#', 'items' => array(
    array('label' => 'Datos Generales', 'url' => '#', 'items' => array(
            array('icon' => 'qrcode', 'label' => 'Impresoras RAW', 'route' => 'rawPrinter/index'),
            array('icon' => 'th-large', 'label' => 'Lockers', 'route' => 'locker/index'),
            array('icon' => 'user', 'label' => 'Usuarios RESTFul API', 'route' => 'apiUser/index'),
        )),
    // array('icon' => 'home', 'label' => 'Inicializar', 'url' => '#', 'items' => array(
    array('label' => 'Inicializar', 'url' => '#', 'items' => array(
            array('icon' => 'upload', 'label' => 'Subir Excel', 'route' => 'init/upload'),
            array('icon' => 'upload', 'label' => 'Subir Excel Cuentas', 'route' => 'init/uploadAccounts'),
            array('icon' => 'upload', 'label' => 'Subir Saldo Inicial de Productos', 'route' => 'init/uploadWarehouseInitialBalance'),
        )),
    // array('icon' => 'wrench', 'label' => 'Utilidades', 'route' => 'utility/index'),
    array('label' => 'Utilidades', 'route' => 'utility/index'),
    // array('icon' => 'eye-open', 'label' => 'Monitoreo', 'url' => '#', 'items' => array(
    array('label' => 'Monitoreo', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'pencil', 'label' => 'Historial de cambios', 'route' => 'changelog/index'),
            ),
            array(
                array('icon' => 'file', 'label' => 'Logs de errores', 'route' => 'errorlog/index'),
                array('icon' => 'check', 'label' => 'Logs de validaciones', 'route' => 'validationlog/index'),
            )
        )),
);

$this->widget('application.widgets.SIANNavbar', array(
    'brand' => Yii::app()->id,
    'class' => 'navbar-systems',
    'items' => $this->items_menu,
));
