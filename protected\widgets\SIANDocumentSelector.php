<?php

class SIANDocumentSelector extends CWidget {

    public $id;
    public $form;
    public $model;
    public $store_items;
    public $operation_input_id;
    public $operation_require_extra_movement_input_id;
    public $store_input_id;
    public $document_input_id;
    public $document_serie_input_id;
    public $document_correlative_input_id;
    public $document_internal_input_id;
    public $movement_exchanged_input_id;
    public $movement_from_exchange_input_id;
    public $emission_input_id;
    public $exchange_input_id;
    public $business_unit_input_id;
    public $business_unit_readonly = false;
    public $project_readonly = false;
    public $business_unit_items = [];
    //CONTROL 
    public $disabled = false;
    public $sunat_code = null;
    public $onLoadOperation = '';
    public $onLoadDocument = '';
    public $onChangeOperation = '';
    public $onChangeStore = '';
    public $onChangeDocument = '';
    public $onChangeEmission = '';
    public $onChangeExchange = '';
    public $emission_date_class = '';
    public $add_select_operation = false;
    public $add_select_document = false;
    //PRIVATE
    private $controller;
    private $refresh_button_id;
    private $exchange_column;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //CONTROL
        $this->disabled = !$this->model->isNewRecord && $this->model->document->serialized;
        if (is_null($this->model->scenario)) {
            throw new Exception("Debe especificar el escenario!");
        }
        $this->exchange_column = isset($this->model->scenario->accountingFile) ? $this->model->scenario->accountingFile->exchange_rate : ExchangeRate::SALE;

        //OPERATION
        $this->operation_input_id = isset($this->operation_input_id) ? $this->operation_input_id : $this->controller->getServerId();
        //
        $this->operation_require_extra_movement_input_id = isset($this->operation_require_extra_movement_input_id) ? $this->operation_require_extra_movement_input_id : $this->controller->getServerId();
        //CODE
        $this->document_input_id = isset($this->document_input_id) ? $this->document_input_id : $this->controller->getServerId();
        //DOCUMENT INTERNAL
        $this->document_internal_input_id = isset($this->document_internal_input_id) ? $this->document_internal_input_id : $this->controller->getServerId();
        //CANJEADO
        $this->movement_exchanged_input_id = isset($this->movement_exchanged_input_id) ? $this->movement_exchanged_input_id : $this->controller->getServerId();
        //DOCUMENT INTERNAL
        $this->movement_from_exchange_input_id = isset($this->movement_from_exchange_input_id) ? $this->movement_from_exchange_input_id : $this->controller->getServerId();
        //STORE
        $this->store_input_id = isset($this->store_input_id) ? $this->store_input_id : $this->controller->getServerId();
        //SERIE
        $this->document_serie_input_id = isset($this->document_serie_input_id) ? $this->document_serie_input_id : $this->controller->getServerId();
        //CORRELATIVE
        $this->document_correlative_input_id = isset($this->document_correlative_input_id) ? $this->document_correlative_input_id : $this->controller->getServerId();
        //EMISSION
        $this->emission_input_id = isset($this->emission_input_id) ? $this->emission_input_id : $this->controller->getServerId();
        //EXCHANGE
        $this->exchange_input_id = isset($this->exchange_input_id) ? $this->exchange_input_id : $this->controller->getServerId();
        //OTROS
        $this->refresh_button_id = $this->controller->getServerId();
        //BUSINESS UNIT
        $this->business_unit_input_id = isset($this->business_unit_input_id) ? $this->business_unit_input_id : $this->controller->getServerId();
        $this->business_unit_readonly = isset($this->business_unit_readonly) ? $this->business_unit_readonly : false;
        $this->project_readonly = isset($this->project_readonly) ? $this->project_readonly : false;

        //Registramos assets
        SIANAssets::registerScriptFile("other/jquery.datetimepicker/js/jquery.datetimepicker.js");
        SIANAssets::registerCssFile("other/jquery.datetimepicker/css/jquery.datetimepicker.css");
        SIANAssets::registerScriptFile('js/sian-document-selector.js');
        //
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        var divObj = $('#{$this->id}');
        divObj.data('disabled', " . CJSON::encode($this->disabled) . ");
        divObj.data('store_input_id', '{$this->store_input_id}');
        divObj.data('document_serie_input_id', '{$this->document_serie_input_id}');
        divObj.data('document_internal_input_id', '{$this->document_internal_input_id}');
        divObj.data('refresh_button_id', '{$this->refresh_button_id}');
        divObj.data('scope', '{$this->model->scope}');
        divObj.data('route', '{$this->model->route}');
        divObj.data('from_exchange', " . (isset($this->model->from_exchange) ? $this->model->from_exchange : 'null') . ");
        divObj.data('parent_is_internal', " . (isset($this->model->parent_is_internal) ? $this->model->parent_is_internal : 'null') . ");
        divObj.data('require_project', " . ($this->model->require_project == 1 ? 1 : 0) . ");
        divObj.data('person_type', '{$this->model->scenario->person_type}');
        divObj.data('operations_url', '{$this->controller->createUrl("/movement/loadOperations")}');
        divObj.data('operation_data_url', '{$this->controller->createUrl("/movement/loadOperationData")}');
        divObj.data('documents_url', '{$this->controller->createUrl("/movement/loadDocuments")}');
        divObj.data('series_url', '{$this->controller->createUrl("/movement/loadSeries")}');
        divObj.data('correlative_url', '{$this->controller->createUrl("/movement/loadCorrelative")}');
        divObj.data('date_js_format', '" . Yii::app()->params['date_js_format'] . "');
        divObj.data('combination_id', " . (isset($this->model->extraInfo) ? $this->model->extraInfo->combination_id : 'null') . ");
        divObj.data('target_movement_id', " . (isset($this->model->target_movement_id) ? $this->model->target_movement_id : 'null') . ");
        divObj.data('entry_id1', " . (isset($this->model->entry_id1) ? $this->model->entry_id1 : 'null') . ");
        divObj.data('entry_id2', " . (isset($this->model->entry_id2) ? $this->model->entry_id2 : 'null') . ");
        divObj.data('default_store_id', " . (Yii::app()->user->hasState('default_store') ? Yii::app()->user->getState('default_store') : 'null') . ");
        divObj.data('parent_store_id', " . (isset($this->model->parent_store_id) ? $this->model->parent_store_id : 'null') . ");
        divObj.data('add_select_operation', " . CJSON::encode($this->add_select_operation) . ");
        divObj.data('add_select_document', " . CJSON::encode($this->add_select_document) . ");
        divObj.data('sunat_code', " . CJSON::encode($this->sunat_code) . ");
                    
        //Eventos que se ejecutarán apenas cargue la ventana
        $(document).ready(function()
        {        
            window.operation_changes = 0;
            window.document_changes = 0;
            window.exchange_changes = 0;
            window.operations_loaded = false;
            window.documents_loaded = false;

            //Operaciones
            SIANDocumentSelectorFillOperations('{$this->id}', '{$this->model->operation_code}', '{$this->model->type}');
            //Cargamos tiendas
            SIANDocumentSelectorFillStores('{$this->id}', " . CJSON::encode($this->store_items) . ", '{$this->model->store_id}')
            //Documentos llenará series
            SIANDocumentSelectorFillDocumentDropDownList('{$this->id}', '{$this->model->document_code}');
        });

        $('body').on('change', '#{$this->operation_input_id}', function(e) {
            window.operation_changes++;
            SIANDocumentSelectorLoadOperationData('{$this->id}', '{$this->operation_input_id}');
        });
        
        $('#{$this->id}').data('changeOperation', function(changeData){
            $('#{$this->operation_require_extra_movement_input_id}').val(changeData.require_extra_movement);   
            {$this->onChangeOperation};
        });
        
        $('#{$this->id}').data('loadOperation', function(){
            window.operations_loaded = true;
            {$this->onLoadOperation};
        });

        $('body').on('change', '#{$this->document_input_id}', function(e) {

            //consoleLog('Se inicia cambio de documento');

            window.document_changes++;

            var divObj = $('#{$this->id}');
            
            var documentObj = $('#{$this->document_input_id}');
            var optionObj = documentObj.find('option:selected');
                
            var document_code = optionObj.val();
            var last = documentObj.data('last');
            var internal = optionObj.data('internal');

            var changeData = {
                document_code : document_code,
                sunat_code : optionObj.data('sunat_code'),
                realChange: (last != document_code),
                is_electronic : optionObj.data('is_electronic'),
                internal: internal
            };
          
            $('#{$this->document_internal_input_id}').val(internal);     
                
            if(window.document_changes > 1)
            {
                $('#{$this->document_serie_input_id}').val('');
                $('#{$this->document_correlative_input_id}').val('');
            }
            
            {$this->onChangeDocument};
            //consoleLog('Cambio de documento a: ' + document_code);
            documentObj.data('last', document_code);

            SIANDocumentSelectorLoadSeries('{$this->id}');
        });
        
        $('#{$this->id}').data('loadDocument', function(){
            window.documents_loaded = true;            
            var emission_date = $('#{$this->emission_input_id}').val();
            {$this->onLoadDocument};
        });
        
        $('body').on('change', '#{$this->store_input_id}', function(e) {
            
            //consoleLog('Se inicia cambio de tienda');
            
            var selectObj = $(this);
            var optionObj = selectObj.find('option:selected');

            var changeData = {
                store_id: optionObj.val(),
                business_unit_id: optionObj.data('business_unit_id')
            };

            {$this->onChangeStore};
                
            //consoleLog('Cambio de tienda a: ' + optionObj.val());
            
            //Si ya cargó el combo de series llamamos
            if($('#{$this->document_input_id}').data('loaded') === true)
            {
                $('#{$this->document_serie_input_id}').val('');
                $('#{$this->document_correlative_input_id}').val('');

                //Cargamos series
                SIANDocumentSelectorLoadSeries('{$this->id}');
            }
        });
        
        $('body').on('change', 'select#{$this->document_serie_input_id}', function(e) {
            //consoleLog('Se inicia cambio de serie');
            SIANDocumentSelectorLoadCorrelative('{$this->id}');
        });     
        
        $('body').on('change', '#{$this->emission_input_id}', function(e) {  
            
            //consoleLog('Se inicia cambio de fecha de emisión');
            
            var emissionObj = $('#{$this->emission_input_id}');
                
            var emission_date = emissionObj.val();
            var last = emissionObj.data('last');
            
            //Si el escenario es automático cambiaremos el tipo de cambio al cambiar la fecha
            " . ($this->model->same_exchange == 0 ? "
            //Desactivamos botón
            $('#{$this->form->id}').find(':submit').prop('disabled', true);
            //Ocupamos
            //
            $.ajax({
                type: 'post',
                url: '" . $this->controller->createUrl("/movement/loadExchange") . "',
                data: {
                    exchange_column: '{$this->exchange_column}',
                    emission_date: emission_date
                },
                beforeSend: function (xhr) {
                    window.active_ajax++;
                    //Ocultamos los tooltip
                    $('div.ui-tooltip').remove();
                },                    
                success: function(data) {
                
                    if(data.code === REST_CODE_SUCCESS)
                    {
                        if(data.data.exchange_rate == null)
                        {
                            bootbox.alert(us_message('<h4>Falta tipo de cambio</h4><p>No hay tipo de cambio el día ' + emission_date + '.</p><p>Por favor contacte con contabilidad.</p>', 'error'));
                            emissionObj.val(last);
                        }
                        else
                        {
                            var changeData = {
                                emission_date : emission_date,
                                realChange: (last != emission_date)
                            };

                            {$this->onChangeEmission};

                            //consoleLog('Cambio de fecha de emisión a: ' + emission_date);
                            emissionObj.data('last', emission_date);

                            $('#{$this->exchange_input_id}').val(data.data.exchange_rate).change();
                        }
                    }
                    else
                    {
                            bootbox.alert(us_message('<h4>' + data.message + '</h4><p>Por favor ingrese una fecha válida. Si considera que hay un error por favor contacte con sistemas.</p>', 'error'));
                            emissionObj.val(last);
                    }

                    //Activamos botón
                    $('#{$this->form->id}').find(':submit').prop('disabled', false);

                    window.active_ajax--;
                },
                error: function(request, status, error) { // if error occured
                    window.active_ajax--;
                    bootbox.alert(us_message(request.responseText, 'error'));
                },
                dataType: 'json'
            });
            " : "
                
            var changeData = {
                emission_date : emission_date,
                realChange: (last != emission_date)
            };
            
            {$this->onChangeEmission};
                
            //consoleLog('Cambio de fecha de emisión a: ' + emission_date);
            emissionObj.data('last', emission_date);
            
            //Si el TC no es automatico se debe llamar al evento de change_exchange
            $('#{$this->exchange_input_id}').change();

            ") . "
        });

        $('body').on('change', '#{$this->exchange_input_id}', function(e) {
            
            window.exchange_changes++;

            //consoleLog('Se inicia cambio de TC');
            var exchange_rate = $('#{$this->exchange_input_id}').floatVal(3);
            {$this->onChangeExchange};
            //consoleLog('Cambio de TC a: ' + exchange_rate);
        });

        $('body').on('click', '#{$this->refresh_button_id}', function(e) {

            if ($(this).hasAttr('disabled') === true)
            {
                return false;
            }
            else
            {
                SIANDocumentSelectorLoadCorrelative('{$this->id}');
            }                
        });   

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}' class='row'>";
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        //2do nivel
        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'store_id', [], array(
            'id' => $this->store_input_id,
            'class' => "sian-document-selector-store-id",
            'disabled' => $this->disabled
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-6 col-sm-6 col-xs-12'>";
        $this->widget('application.widgets.SIANBusinessUnit', array(
            'id' => $this->business_unit_input_id,
            'form' => $this->form,
            'model' => $this->model,
            'items' => $this->business_unit_items,
            'readonly' => $this->business_unit_readonly,
            'disabled' => false,
            'required' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-6 col-md-12 col-sm-12 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model,
            'attribute' => 'project_id',
            //'parent_id' => $this->modal_id,
            'view' => array(
                'model' => 'DpProject',
                'attributes' => array(
                    array(
                        'name' => 'project_id',
                        'width' => 0,
                        'types' => array('id', 'value', 'aux'),
                        'hidden' => true
                    ),
                    array(
                        'name' => 'short_name',
                        'width' => 50,
                        'types' => array('text'),
                    ),
                    array(
                        'name' => 'source_type',
                        'width' => 25,
                    ),
                    array(
                        'name' => 'project_status',
                        'width' => 25,
                    ),
                )
            ),
            'readonly' => $this->project_readonly
        ));
        echo "</div>";
        echo "</div>";

        //1er nivel

        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'operation_code', [], array(
            'id' => $this->operation_input_id,
            'class' => "sian-document-selector-operation-code"
        ));        
        echo $this->form->hiddenField($this->model, 'require_extra_movement', array(
            'id' => $this->operation_require_extra_movement_input_id,
            'class' => "sian-document-selector-require_extra_movement",
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'document_code', [], array(
            'id' => $this->document_input_id,
            'class' => "sian-document-selector-document-code",
            'disabled' => $this->disabled
        ));
        // renderizar el input tipo hidden-- document_internal_input_id-- declarar al inicio
        echo $this->form->hiddenField($this->model, 'internal', array(
            'id' => $this->document_internal_input_id,
            'class' => "sian-document-selector-internal",
        ));
        echo $this->form->hiddenField($this->model, 'document_sunat_code', array(
            'id' => $this->document_internal_input_id,
            'class' => "sian-document-selector-document-sunat-code",
        ));
        echo $this->form->hiddenField($this->model, 'exchanged', array(
            'id' => $this->movement_exchanged_input_id,
            'class' => "sian-document-selector-exchanged",
        ));
        echo $this->form->hiddenField($this->model, 'from_exchange', array(
            'id' => $this->movement_from_exchange_input_id,
            'class' => "sian-document-selector-from-exchange",
        ));
        echo "</div>";
        //
        //
        //
        echo "<div class='col-lg-6 col-md-12 col-sm-12 col-xs-12'>";
        echo "<div class='row'>";

        echo "<div class='col-lg-4 col-md-6 col-sm-6 col-xs-12'>";
        echo "<div class='form-group " . ($this->model->hasErrors('document_serie') ? 'has-error' : '') . "'>";
        echo CHtml::label($this->model->getAttributeLabel('document_serie') . ($this->model->isAttributeRequired('document_serie') ? " <span class='required'>*</span>" : ""), $this->document_serie_input_id, array(
            'class' => 'control-label',
        ));
        echo "<div class='sian-document-selector-document-serie-div'>";
        echo "Cargando serie...";
        echo $this->form->hiddenField($this->model, 'document_serie', array(
            'id' => $this->document_serie_input_id,
            'class' => 'sian-document-selector-document-serie',
        ));
        echo "</div>";
        if ($this->model->hasErrors('document_serie')) {
            echo "<div class='help-block error'>{$this->model->getError('document_serie')}</div>";
        }
        echo "</div>";
        echo "</div>";

        echo "<div class='col-lg-3 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->hiddenField($this->model, 'min_correlative', array(
            'class' => 'sian-document-selector-min-correlative',
            'readonly' => true,
        ));
        echo $this->form->numberFieldRow($this->model, 'document_correlative', array(
            'id' => $this->document_correlative_input_id,
            'min' => 1,
            'max' => 9999999999,
            'step' => 1,
            'class' => 'sian-document-selector-document-correlative us-double0',
            'append' => $this->widget('application.widgets.USLink', array(
                'id' => $this->refresh_button_id,
                'title' => 'Cargar correlativo actual',
                'icon' => 'fa fa-lg fa-refresh',
                'visible' => false
                    ), true)
        ));
        echo $this->form->hiddenField($this->model, 'max_correlative', array(
            'class' => 'sian-document-selector-max-correlative',
            'readonly' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->widget('application.widgets.USDatePicker', array(
            'id' => $this->emission_input_id,
            'form' => $this->form,
            'model' => $this->model,
            'attribute' => 'emission_date',
            'options' => array(
                'maxDate' => SIANTime::formatDate(),
                'onChangeDateTime' => "function(dp, input){
                    //consoleLog('Se eligió una fecha de emisión');
                }"
            ),
            'htmlOptions' => array(
                'class' => 'sian-document-selector-emission-date ' . $this->emission_date_class,
            )
                ), true);
        echo "</div>";

        echo "<div class='col-lg-2 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->textFieldRow($this->model, 'exchange_rate', array(
            'id' => $this->exchange_input_id,
            'readonly' => true,
            'class' => 'sian-document-selector-exchange-rate',
        ));
        echo $this->form->hiddenField($this->model, 'web_user_id', array(
            'readonly' => true,
        ));
        echo $this->form->hiddenField($this->model, 'scope', array(
            'readonly' => true,
        ));
        echo $this->form->hiddenField($this->model, 'same_exchange', array(
            'readonly' => true,
        ));
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";

        echo "</div>";
        echo "</div>";
    }

}
