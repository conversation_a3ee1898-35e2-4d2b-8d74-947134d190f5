<?php

class SIANAddressList extends CWidget {

    public $id;
    public $form;
    public $model;
    public $attribute;
    public $type = null;
    public $auxModel;
    public $prerequisites = [];
    public $title = 'Direcciones disponibles';
    public $visible = true;
    public $disabled = false;
    public $readonly = false;
    public $hint = false;
    //PRIVATE
    private $attribute_id;
    private $attribute_input_id = null;
    private $clientScript;
    private $controller;
    private $address_attributes = [
        'dept_code',
        'prov_code',
        'dist_code',
        'address',
        'reference',
        'lat',
        'lng',
        'type',
    ];
    private $address_key = [
        'dept_code',
        'prov_code',
        'dist_code',
        'address',
    ];
    public $prerequisites_attributes = [];

    public function init() {

        //CONTROL
        if (!isset($this->type)) {
            throw new Exception('Debe especificar un tipo');
        }

        $this->clientScript = Yii::app()->clientScript;
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->attribute_input_id = $this->controller->getServerId();
        //
        switch ($this->type) {
            case Locker::OWNER:
                $this->attribute_id = 'locker_id';
                break;
            case Person::OWNER:
                $this->attribute_id = 'address_id';
                $this->prerequisites_attributes = ['person_id'];
                break;
            default:
                throw new Exception('Tipo inválido');
        }

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-address-list.js');

        //SCRIPTS
        $this->clientScript->registerScript($this->id, "

         //COUNT y LIMIT
        var divObj = $('#{$this->id}');
        divObj.data('prerequisites_attributes', " . CJSON::encode($this->prerequisites_attributes) . ");
        divObj.data('items_url', '{$this->controller->createUrl('/widget/getAddressList')}');
        divObj.data('type', '{$this->type}');
        divObj.data('attribute_id', '{$this->attribute_id}');
        divObj.data('attribute_input_id', '{$this->attribute_input_id}');
        divObj.data('visible', " . CJSON::encode($this->visible) . ");
        divObj.data('readonly', " . CJSON::encode($this->readonly) . ");
        divObj.data('disabled', " . CJSON::encode($this->disabled) . ");
        divObj.data('address_attributes', " . CJSON::encode($this->address_attributes) . ");
        divObj.data('address_key', " . CJSON::encode($this->address_key) . ");
                
        $(document).ready(function() {                  
                
            //Seteamos prerequisitos
            SIANAddressListSetPrerequisites('{$this->id}', " . CJSON::encode($this->prerequisites) . ");
            //
            SIANAddressListDisabled('{$this->id}', " . CJSON::encode($this->disabled) . ");
        });

        $('body').on('click','#{$this->id} input.sian-address-list-item-address-id', function(e){
            var elementObj = $(this);
            //
            if(elementObj.prop('readonly'))
            {
                e.preventDefault();
                return false;
            }
        });
        
        $('body').on('change','#{$this->id} input.sian-address-list-item-address-id', function(e){
            var elementObj = $(this);
            //
            var attribute_id = elementObj.val();
            var address_data = elementObj.data('address_data');
            //
            $('#{$this->attribute_input_id}').val(attribute_id); 
            //
            var divObj = $('#{$this->id}');
            var address_attributes = divObj.data('address_attributes');
            $.each(address_attributes, function(index, attribute) {
                $('#{$this->id}_' + attribute).val(address_data[attribute]);         
            });
            //Si es tipo locker, ponemos el nombre del locker en la referencia
            " . ($this->type === Locker::OWNER ? "
                $('#{$this->id}_reference').val(address_data.locker_name);         
            " : "") . "
        });
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->title,
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => $this->model->hasErrors($this->attribute) ? 'us-error' : '',
                'style' => $this->visible ? 'display:block;' : 'display:none;',
            )
        ));
        echo "<div class='sian-address-list-items'></div>";
        echo "<div class='form-group " . ($this->model->hasErrors($this->attribute) ? 'has-error' : '') . "'>";
        echo $this->form->hiddenField($this->auxModel, $this->attribute_id, [
            'id' => $this->attribute_input_id
        ]);
        //Verificamos si hay direcciones
        if (count($this->model->{$this->attribute})) {
            $o_address = $this->model->{$this->attribute}[0];
            foreach ($this->address_attributes as $s_attribute) {
                echo CHtml::hiddenField("Address[0][{$s_attribute}]", $o_address->{$s_attribute}, [
                    'id' => $this->id . '_' . $s_attribute
                ]);
            }
        } else {
            foreach ($this->address_attributes as $s_attribute) {
                echo CHtml::hiddenField("Address[0][{$s_attribute}]", '', [
                    'id' => $this->id . '_' . $s_attribute
                ]);
            }
        }

        echo $this->form->errorBlock($this->model, $this->attribute);
        echo "</div>";
        if ($this->hint) {
            echo "<i>{$this->hint}</i>";
        }
        $this->endWidget();
    }

}
