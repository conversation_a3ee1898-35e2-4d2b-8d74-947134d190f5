<?php

class SIANPayroll extends CWidget {

    const SEPARATOR = '||';

    public $form;
    public $id;
    public $model;
    public $modal_id;
    public $child_modal_id;
    public $dictionary_input_id;
    public $detail_input_id;
    public $last_id;    
    public $factors_employee;
    public $factors_general;
    public $data_functions;
    public $dictionary_id;
    public $save_button_id;
    public $type_period;
    public $submit_id;
    //PRIVATE
    private $controller;
    private $grid_detail_id;
    private $recalc_button_id;
    private $concepts;
    private $concepts_order;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->modal_id = isset($this->modal_id) ? $this->modal_id : $this->controller->getServerId();
        $this->child_modal_id = isset($this->child_modal_id) ? $this->child_modal_id : $this->controller->getServerId();
        $this->grid_detail_id = isset($this->grid_detail_id) ? $this->grid_detail_id : $this->controller->getServerId();
        $this->dictionary_input_id = isset($this->dictionary_input_id) ? $this->dictionary_input_id : $this->controller->getServerId();
        $this->save_button_id = isset($this->save_button_id) ? $this->save_button_id : $this->controller->getServerId();
        $this->recalc_button_id = isset($this->recalc_button_id) ? $this->recalc_button_id : $this->controller->getServerId();
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-payroll.js');
        SIANAssets::registerScriptFile('other/bootstrapValidator/bootstrapValidator.js');
        SIANAssets::registerScriptFile('other/jquery.jqGrid/jquery.jqGrid.js');
        SIANAssets::registerScriptFile('other/jquery.jqGrid/i18n/grid.locale-es.js');
        SIANAssets::registerCssFile('other/jqgrid/css/ui.jqgrid.css');

        //ITEMS
        $details = [];

        foreach ($this->model->tempHeaders as $header) {
            $attributes = [];
            $attributes["person_id"] = $header->person_id;
            $attributes["name"] = $header->name;
            $attributes["typeDoc"] = $header->typeDoc;
            $attributes["assistance_summary_employee_id"] = $header->assistance_summary_employee_id;
            $attributes["combination_id"] = $header->combination_id;
            $attributes["combination_name"] = $header->combination_name;
            $attributes["NRO_MES"] = $header->NRO_MES;
            $attributes["DIAS_LAB"] = $header->DIAS_LAB;
            $attributes["FERIADOS"] = $header->FERIADOS;
            $attributes["DIAS_CALC"] = $header->work_days_calc;
            $attributes["DIAS_TRAB"] = $header->work_days_employee;
            $attributes["INASISTENCIAS"] = $header->absences;
            $attributes["HTRAB"] = $header->work_hours;
            $attributes["HE1"] = $header->extrahours1;
            $attributes["HE2"] = $header->extrahours2;
            $attributes["HNOCTURNAS"] = $header->night_hours;
            $attributes["HDDESC"] = $header->holiday_hours;
            $attributes["MTARDANZAS"] = $header->lateness;
            $attributes["TIENE_AF"] = $header->children;
            $attributes["NAC"] = $header->birthday;
            $attributes["EDAD"] = $header->age;
            $attributes["SPID"] = $header->SPID;
            $attributes["CATEGORIA"] = $header->CATEGORIA;
            $attributes["JORNAL_DIARIO"] = $header->JORNAL_DIARIO;
            $attributes["PORC_BUC"] = $header->PORC_BUC;
            $attributes["BASICO"] = $header->salary;
            $attributes["TIPOCOM"] = $header->type_commision;
            $attributes["APORTE"] = $header->APORTE;
            $attributes["PRIMA"] = $header->PRIMA;
            $attributes["COMISION"] = $header->COMISION;
            $attributes["REMASEG"] = $header->REMASEG; //20

            foreach ($header->tempDetails as $detail) {
                $attributes[$detail->concept_name] = $detail->amount;
            }
            $details[] = $attributes;
        }

        $a_columnsToSave = [];
        $a_columnsToSave[PayrollMovementHeader::COLUMN_SALARY_ALIAS] = PayrollMovementHeader::COLUMN_SALARY;
        $a_columnsToSave[PayrollMovementHeader::COLUMN_CHILDREN_ALIAS] = PayrollMovementHeader::COLUMN_CHILDREN;
        $a_columnsToSave[PayrollMovementHeader::COLUMN_TYPE_COMMISSION_ALIAS] = PayrollMovementHeader::COLUMN_TYPE_COMMISSION;
        $a_columnsToSave[PayrollMovementHeader::COLUMN_AGE_ALIAS] = PayrollMovementHeader::COLUMN_AGE;

        $a_columnsFixed = PayrollMovementHeader::getListColumnsFixed();

        $this->concepts = $this->model->rawConcepts;
        $this->concepts_order = $this->model->rawConcepts; 
        USArray::usortInt($this->concepts_order, 'calc_order'); 

        $columns = '';
        $headers = '';
        $obj_factor_general = '';
        $obj_factor_employee = '';
        $obj_concept_informed = '';
        $obj_concept_informed_read = '';
        $obj_concept_calculated = '';
        $concept_result = '';
        $concept_total = '';
        $concepts_for_add = '';

        // =================================== FACTORES =========================================
        foreach ($this->factors_general as $fg) {
            switch ($fg['type']) {
                case 'text':
                    $obj_factor_general .= "      obj." . $fg['header'] . " = '" . $fg['value'] . "';\n";
                    break;
                case 'num':
                    $obj_factor_general .= "      obj." . $fg['header'] . " = " . $fg['value'] . ";\n";
                    break;
                case '%':
                    $obj_factor_general .= "      obj." . $fg['header'] . " = " . $fg['value'] . "/100;\n";
                    break;
            }
        }

        foreach ($this->factors_employee as $fe) {
            switch ($fe['type']) {
                case 'text':
                    $obj_factor_employee .= "      obj." . $fe['header'] . " = dataRow." . $fe['header'] . ";\n";
                    break;
                default:
                    $obj_factor_employee .= "      obj." . $fe['header'] . " = parseFloat(dataRow." . $fe['header'] . ");\n";
                    break;
            }
        }
        // =================================== COLUMNAS DEL JQGRID =========================================
        foreach ($this->concepts as $concept) {
            $headers .= ",'" . $concept['header'] . "'";

            $tam = strlen($concept['header']) > 7 ? '80' : '60';
            if ($concept['is_printable'] == 0) {
                $columns .= ", {name: '" . $concept['header'] . "', index: '" . $concept['header'] . "', hidden: true}";
            } else if ($concept['type_formula'] == Concept::TYPE_INFORMED) {

                $columns .= ", {name: '" . $concept['header'] . "', index: '" . $concept['header'] . "', width: " . $tam . ", height:'25px', align: 'right', title: false, editable: true, summaryType: 'sum', editoptions: {dataInit: function (elem) {
                                $(elem).bind('keypress', function (e) {
                                    return SIANPayrollSoloNumeroDecimal(e)
                                })
                            }}}";
            } else {
                $columns .= ", {name: '" . $concept['header'] . "', index: '" . $concept['header'] . "', width: " . $tam . ", height:'25px', align: 'right', title: false, editable: false, summaryType: 'sum', editoptions: {dataInit: function (elem) {
                                $(elem).bind('keypress', function (e) {
                                    return SIANPayrollSoloNumeroDecimal(e)
                                })
                                
                                $(elem).bind('keydown', function (e) {
                                    //alert('keydown');
                                    return false;
                                })
                            }}}";
            }
            $concept_result .= "      " . $concept['header'] . ":obj." . $concept['header'] . ",\n";
            $concept_total .= " var " . $concept['header'] . "Sum = $('#{$this->grid_detail_id}').jqGrid('getCol', '" .
                    $concept['header'] . "', false, 'sum');\n $('#{$this->grid_detail_id}').jqGrid('footerData', 'set', {" . $concept['header'] . ": " . $concept['header'] . "Sum.toFixed(2)});\n";
            $concepts_for_add .= "       objectRow." . $concept['header'] . " = data[i]." . $concept['header'] . ";\n";
        }

        // =================================== CALCULO DE CONCEPTOS =========================================
        foreach ($this->concepts_order as $concept) {

            switch ($concept['type_formula']) {

                case Concept::TYPE_INFORMED:
                    $obj_concept_informed .= "      obj." . $concept['header'] . "= parseFloat(($(\"[name='" . $concept['header'] . "']\").val() == undefined || $(\"[name='" . $concept['header'] . "']\").val() == '')? 0 : $(\"[name='" . $concept['header'] . "']\").val()).toFixed(2);\n";
                    $obj_concept_informed_read .= "      obj." . $concept['header'] . " = parseFloat(dataRow." . $concept['header'] . ").toFixed(2);\n";
                    break;

                case Concept::TYPE_SIMPLE:
                    $obj_concept_calculated .= "      obj." . $concept['header'] . " = USMath.calcExpression(SIANPayrollReplaceFunctions(data_functions, '" . $this->removePrefix($concept['sentence']) . "', employee_id),obj,2);\n";
                    break;

                case Concept::TYPE_CONDITIONAL:

                    $condition = $concept['condition'];
                    $obj_concept_calculated .= "   if( USMath.calcExpression('" . $this->removePrefix($condition) . "',obj,2) ==1)\n";
                    $obj_concept_calculated .= "      obj." . $concept['header'] . " = USMath.calcExpression(SIANPayrollReplaceFunctions(data_functions, '" . $this->removePrefix($concept['true_sentence']) . "', employee_id,),obj,2);\n";
                    $obj_concept_calculated .= "   else\n";
                    $obj_concept_calculated .= "      obj." . $concept['header'] . " = USMath.calcExpression(SIANPayrollReplaceFunctions(data_functions, '" . $this->removePrefix($concept['false_sentence']) . "', employee_id),obj,2);\n";
                    break;
            }
        }
        $url = $this->model->isNewRecord ? Yii::app()->controller->createUrl('getTemplatePayroll') : Yii::app()->controller->createUrl('get');
        $data = $this->model->isNewRecord ? "assistance_summary_id:{$this->model->assistance_summary_id},dictionary_id:1" : "movement_id:{$this->model->movement_id}";
        $isNewRecord = $this->model->isNewRecord == true ? "true" : "false";
        $varMovement = $this->model->isNewRecord == false ? "data.movement_id = " . $this->model->movement_id . ";" : "";

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            $(document).ready(function() { 
            var loadInitial = false;
            var dictionary_id = '';
            $('body').on('change', '#{$this->dictionary_input_id}', function () {                
                dictionary_id = $('#{$this->dictionary_input_id}').val() == null ? '' : $('#{$this->dictionary_input_id}').val();                
            });
            var lastsel; 
            var lastFocus;
            gridEmployee =  $('#{$this->grid_detail_id}').jqGrid({                                   
                    postData: {" . $data . "},
                    datatype: 'local',
                    multiselect: false,                    
                    colNames: ['ID',
                               'N° Doc.',
                               'Colaborador',
                               'ID Centro de Costos',
                               'Centro de Costos',
                               'Resumen de Asistencia Item',
                               'N° Mes',
                               'Dias Lab.',
                               'Feriados',
                               'Dias Calc',
                               'Dias Trab.',
                               'Inasistencias',
                               'Hrs. Trabajadas',
                               'Hrs. Extra 1',
                               'Hrs. Extra2',
                               'Hrs. Nocturnas',
                               'Hrs. D. Descanso',
                               'Tardanzas',
                               'Tiene AF',
                               'F. Nacimiento',
                               'Edad',
                               'SPID',
                               'Categoría',
                               'Jornal Diario',
                               'BUC',
                               'S.Básico',
                               'Tipo Com.',
                               'Aporte',
                               'Prima',
                               'Comisión',
                               'Rem. Max. Aseg.'" . $headers . "
                    ],
                    colModel: [
                        {name: 'person_id', index: 'person_id', hidden: true,  frozen : true},
                        {name: 'typeDoc', index: 'typeDoc', width: 60,  frozen : true},
                        {name: 'name', index: 'name', width: 200,  frozen : true},
                        {name: 'combination_id', index: 'combination_id', width: 200,  frozen : true, hidden: true},
                        {name: 'combination_name', index: 'combination_name', width: 170,  frozen : true},
                        {name: 'assistance_summary_employee_id', index: 'assistance_summary_employee_id',width: 0, hidden: true},
                        {name: 'NRO_MES', index: 'NRO_MES', hidden: true},
                        {name: 'DIAS_LAB', index: 'DIAS_LAB', hidden: true},
                        {name: 'FERIADOS', index: 'FERIADOS', hidden: true},
                        {name: 'DIAS_CALC', index: 'DIAS_CALC', hidden: true},
                        {name: 'DIAS_TRAB', index: 'DIASTRAB', width: 60, align: 'center'" . ($this->type_period != Regime::TYPE_PERIOD_MONTHLY ? ", hidden: true" : "") . "},
                        {name: 'INASISTENCIAS', index: 'INASISTENCIAS', hidden: true},
                        {name: 'HTRAB', index: 'HTRAB', width: 60 " . ($this->type_period != Regime::TYPE_PERIOD_WEEKLY ? ", hidden: true" : "") . "},
                        {name: 'HE1', index: 'HE1', width: 60 " . ($this->type_period != Regime::TYPE_PERIOD_WEEKLY ? ", hidden: true" : "") . "},
                        {name: 'HE2', index: 'HE2', width: 60 " . ($this->type_period != Regime::TYPE_PERIOD_WEEKLY ? ", hidden: true" : "") . "},
                        {name: 'HNOCTURNAS', index: 'HNOCTURNAS', hidden: true},
                        {name: 'HDDESC', index: 'HDDESC', width: 60 " . ($this->type_period != Regime::TYPE_PERIOD_WEEKLY ? ", hidden: true" : "") . "},                        
                        {name: 'MTARDANZAS', index: 'MTARDANZAS', hidden: true},
                        {name: 'TIENE_AF', index: 'TIENE_AF', hidden: true},
                        {name: 'NAC', index: 'NAC', width: 70,  frozen : true},
                        {name: 'EDAD', index: 'EDAD', width: 50,  frozen : true},
                        {name: 'SPID', index: 'SPID' ,width: 60},
                        {name: 'CATEGORIA', index: 'CATEGORIA', width: 60 " . ($this->type_period != Regime::TYPE_PERIOD_WEEKLY ? ", hidden: true" : "") . "},
                        {name: 'JORNAL_DIARIO', index: 'JORNAL_DIARIO', hidden: true},
                        {name: 'PORC_BUC', index: 'PORC_BUC', hidden: true},
                        {name: 'BASICO', index: 'BASICO', width: 60, align: 'right' " . ($this->type_period != Regime::TYPE_PERIOD_MONTHLY ? ", hidden: true" : "") . "},
                        {name: 'TIPOCOM', index: 'TIPOCOM', hidden: true},
                        {name: 'APORTE', index: 'APORTE', hidden: true},
                        {name: 'PRIMA', index: 'PRIMA', hidden: true},
                        {name: 'COMISION', index: 'COMISION', hidden: true},
                        {name: 'REMASEG', index: 'REMASEG', hidden: true}" . $columns . "
                    ],
                    rowNum: 50,
                    rowList: [50, 100, 200, 300, 400, 500],
                    sortorder: 'asc',
                    width: '',
                    shrinkToFit: false,
                    height: 300,
                    cellsubmit: 'clientArray',
                    sortname: 'name',
                    gridview: true,
                    footerrow: true,
                    userDataOnFooter: true,
                    celledit: false,
                    gridComplete: function () {
                        {$this->id}updateTotalAmounts();
                    },
                    loadComplete: function () {  
                        if(!loadInitial){
                            var data = " . CJSON::encode($details) . ";   
                            for (var i = 0; i < data.length; i++) {

                                var objectRow = {};
                                objectRow.person_id = data[i].person_id;\n"
                . "objectRow.typeDoc = data[i].typeDoc;\n"
                . "objectRow.name = data[i].name;\n"
                . "objectRow.combination_id = data[i].combination_id;\n"
                . "objectRow.combination_name = data[i].combination_name;\n"
                . "objectRow.assistance_summary_employee_id = data[i].assistance_summary_employee_id;\n"
                . "objectRow.NRO_MES = data[i].NRO_MES;\n"
                . "objectRow.DIAS_LAB = data[i].DIAS_LAB;\n"
                . "objectRow.FERIADOS = data[i].FERIADOS;\n"
                . "objectRow.DIAS_CALC = data[i].DIAS_CALC;\n"
                . "objectRow.DIAS_TRAB = data[i].DIAS_TRAB;\n"
                . "objectRow.INASISTENCIAS = data[i].INASISTENCIAS;\n"
                . "objectRow.HTRAB = data[i].HTRAB;\n"
                . "objectRow.HE1 = data[i].HE1;\n"
                . "objectRow.HE2 = data[i].HE2;\n"
                . "objectRow.HNOCTURNAS = data[i].HNOCTURNAS;\n"
                . "objectRow.HDDESC = data[i].HDDESC;\n"
                . "objectRow.MTARDANZAS = data[i].MTARDANZAS;\n"
                . "objectRow.TIENE_AF = data[i].TIENE_AF;\n"
                . "objectRow.NAC = data[i].NAC;\n"
                . "objectRow.EDAD = data[i].EDAD;\n"
                . "objectRow.SPID = data[i].SPID;\n"
                . "objectRow.CATEGORIA = data[i].CATEGORIA;\n"
                . "objectRow.JORNAL_DIARIO = data[i].JORNAL_DIARIO;\n"
                . "objectRow.PORC_BUC = data[i].PORC_BUC;\n"
                . "objectRow.BASICO = data[i].BASICO;\n"
                . "objectRow.TIPOCOM = data[i].TIPOCOM;\n"
                . "objectRow.APORTE = data[i].APORTE;\n"
                . "objectRow.PRIMA = data[i].PRIMA;\n"
                . "objectRow.COMISION = data[i].COMISION;\n"
                . "objectRow.REMASEG = data[i].REMASEG;\n"
                . $concepts_for_add . "\n" .
                "jQuery('#{$this->grid_detail_id}').jqGrid('addRowData', objectRow.person_id, objectRow);
                            }
                            loadInitial = true;
                        }
                    },
                    beforeSelectRow: function(employee_id, e) {
                        
                        if(lastsel != undefined){
                            if(lastsel != employee_id){
                                bootbox.alert(us_message('Debe confirmar la edición con \'Enter\' o cancelar con \'ESC\' antes de cambiar de fila.', 'warning'));
                                $('#'+ lastFocus).focus()
                                return false;
                            }
                        }else{
                            return true;
                        }                        
                    },
                    onSelectRow: function (employee_id, status, e) {
                                               
                       var dataRow = $('#{$this->grid_detail_id}').getRowData(employee_id);
                        if (employee_id && employee_id != lastsel) {                                        
                            $('#{$this->grid_detail_id}').jqGrid('saveRow', lastsel,null,'clientArray');
                            $('#{$this->grid_detail_id}').jqGrid('editRow', employee_id, true, null, null, 'clientArray', null, function () {                                
                                {$this->id}updateTotalAmounts();                                
                            });
                            $('#{$this->recalc_button_id}').attr('disabled',1);
                            $('#{$this->submit_id}').attr('disabled',1);
                            lastsel = employee_id;
                            $('#{$this->recalc_button_id}').val(lastsel);
                        } else {
                            $('#{$this->grid_detail_id}').jqGrid('editRow', employee_id, true, null, null, 'clientArray', null, function () {                                
                                {$this->id}updateTotalAmounts();
                            });
                            $('#{$this->recalc_button_id}').attr('disabled',1);
                            $('#{$this->submit_id}').attr('disabled',1);
                        }" . "\n" .
                "$('input.editable').keydown(function (e) {\n" .
                " if (e.keyCode == 13){
                            {$this->id}calculatePayroll(employee_id,dataRow);
                            $('#{$this->last_id}').val('');
                            lastsel = undefined;
                            $('#{$this->recalc_button_id}').removeAttr('disabled');
                            $('#{$this->submit_id}').removeAttr('disabled');
                          }
                          if (e.keyCode == 27){
                            lastsel = undefined;
                             $('#{$this->recalc_button_id}').removeAttr('disabled');
                             $('#{$this->submit_id}').removeAttr('disabled');
                          }
                        });
                        $('input.editable').focus(function(e){
                            lastFocus = $(this).attr('id');
                        });
                        return false;
                    },
                    loadError: function (request, status, error) {
                        var s_message = processError(request.responseText);
                        bootbox.alert(us_message(s_message, 'error'));
                    }
                });
                
                $('#{$this->grid_detail_id}').jqGrid('navGrid', '#grid_employee_pager', {add: false, edit: false, del: false, search: false, refresh: true});
               // $('#{$this->grid_detail_id}').jqGrid('setFrozenColumns');  
                   
                $('body').on('click', '#{$this->recalc_button_id}', function(e) {            
                    var rows = $('#{$this->grid_detail_id}').jqGrid('getRowData');

                    for(var i=0;i<rows.length;i++){                            
                          var row=rows[i];
                          {$this->id}calculatePayrollRead(row['person_id'], row);
                    }
                    lastsel = undefined;
                });
            
            });
                        
            $('#{$this->id}').submit(function(e) {                
                {$this->id}ajaxUpdateCreatePayroll();
            });
            
            function {$this->id}updateTotalAmounts() {
                " . $concept_total . "
            }          
            
            function {$this->id}calculatePayroll(employee_id, dataRow){
                
                var obj= new Object();
                var data_functions = " . CJSON::encode($this->data_functions) . ";" .
                $obj_factor_general .
                $obj_factor_employee .
                $obj_concept_informed .
                "\n//============== CALCULOS DE CONCEPTOS ==================\n" .
                $obj_concept_calculated .
                "$('#{$this->grid_detail_id}').jqGrid('setRowData', employee_id, {\n" .
                $concept_result . "});\n" .
                "    {$this->id}updateTotalAmounts(); \n
            }
            function {$this->id}calculatePayrollRead(employee_id, dataRow){
                var obj= new Object();
                var data_functions = " . CJSON::encode($this->data_functions) . ";" .
                $obj_factor_general .
                $obj_factor_employee .
                $obj_concept_informed_read .
                "\n//============== CALCULOS DE CONCEPTOS ==================\n" .
                $obj_concept_calculated .
                "$('#{$this->grid_detail_id}').jqGrid('setRowData', employee_id, {\n" .
                $concept_result . "});\n" .
                "    {$this->id}updateTotalAmounts(); \n
            }
            
            function {$this->id}ajaxUpdateCreatePayroll() {
                var lastsel = $('#{$this->last_id}').val();
                if(lastsel != ''){
                    var rw = $('#{$this->grid_detail_id}').jqGrid('getRowData', lastsel);                    
                    {$this->id}calculatePayroll(lastsel, rw);  
                    $('#{$this->grid_detail_id}').jqGrid('saveRow', lastsel,null,'clientArray');                
                }                    
                var data = new Object();
                $('#{$this->save_button_id}').attr('disabled',true);
                var a_detail = [];
                var a_columnFixedToSave = " . CJSON::encode($a_columnsToSave) . ";
                var a_columnFixed = " . CJSON::encode($a_columnsFixed) . ";                
                $.each($('#{$this->grid_detail_id}').jqGrid('getDataIDs'), function () {
                    var row = $('#{$this->grid_detail_id}').jqGrid('getRowData', this);
                    
                    var item = $.map(row, function (value, i) {                        
                        if(a_columnFixedToSave[i] != undefined){
                            if(a_columnFixedToSave[i] == '" . PayrollMovementHeader::LAST_FACTOR_EMPLOYEE . "'){
                                return a_columnFixedToSave[i] +':' + [value] + ';';     
                            }else{
                                return a_columnFixedToSave[i] +':' + [value];     
                            }                                                  
                        } 
                        if(inArray(i,a_columnFixed)){ 
                            if([i] == '" . PayrollMovementHeader::LAST_FACTOR_EMPLOYEE . "'){
                                return [i]+ ':' + [value] + ';';     
                            }else{
                                return [i]+ ':' + [value];          
                            }                                                   
                        }else{                            
                            return [i]+':' + [value];  
                        }                        
                    });
                    a_detail.push(item.join('" . self::SEPARATOR . "'));                    
                    $('#{$this->detail_input_id}').val(a_detail.toString());
                });
            } 
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Detalle de planilla',
            'headerIcon' => 'list'
        ));
        echo "<table id='{$this->grid_detail_id}' name='{$this->grid_detail_id}'></table>";
        echo "<br>";
        echo "<button type='button' class='btn btn-info' id='{$this->recalc_button_id}' data-toggle='modal' data-target='#modalEntry'>Recalcular</button>";

        $this->endWidget();
    }

    private function removePrefix($formula) {
        return str_replace(array('c_', 'f_', 'n_', 'p_', 't_'), '', $formula);
    }  
}
