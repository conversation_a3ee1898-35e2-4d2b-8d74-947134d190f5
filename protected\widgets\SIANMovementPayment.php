<?php

class SIANMovementPayment extends CWidget {

    public $id;
    public $form;
    public $model;
    public $readonly = false;
    public $person_items = [];
    public $direction = Scenario::DIRECTION_IN;
    public $input_total_id = null;
    //PRIVATE
    private $clientScript;
    private $controller;
    private $payment_internal_input_id = null;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->clientScript = Yii::app()->clientScript;
        //Público
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->input_total_id = isset($this->input_total_id) ? $this->input_total_id : $this->controller->getServerId();
        //Privado
        $this->payment_internal_input_id = $this->controller->getServerId();
        //Recorremos los ítems
        $a_items = [];
        foreach ($this->model->tempMovementPayments as $o_item) {

            $a_errors = $o_item->getErrors();
            $a_items[] = [
                'payment_date' => $o_item->payment_date,
                'type' => isset($o_item->type) ? $o_item->type : null,
                'cashbox_id' => isset($o_item->cashbox_id) ? $o_item->cashbox_id : null,
                'reference_number' => isset($o_item->reference_number) ? $o_item->reference_number : '',
                'credit_card' => isset($o_item->credit_card) ? $o_item->credit_card : '',
                'amount' => isset($o_item->amount) ? $o_item->amount : 0.00,
                'errors' => $a_errors
            ];
        }
        //Registramos assets
        SIANAssets::registerScriptFile("other/jquery.datetimepicker/js/jquery.datetimepicker.js");
        SIANAssets::registerCssFile("other/jquery.datetimepicker/css/jquery.datetimepicker.css");
        SIANAssets::registerScriptFile('other/moment/moment.js');
        SIANAssets::registerScriptFile('js/sian-movement-payment.js');
        //SCRIPTS
        $this->clientScript->registerScript($this->id, "

        var array = " . CJSON::encode($a_items) . ";

        //COUNT y LIMIT
        var divObj = $('#{$this->id}');
        divObj.data('paymentTypeItems', " . CJSON::encode(CashboxMovement::getPaymentMethodItems(false, [CashboxMovement::PAYMENT_METHOD_BANK_CHECK, CashboxMovement::PAYMENT_METHOD_AGREEMENT])) . ");
        divObj.data('cashboxUrl', '{$this->controller->createUrl("/movement/getCashboxes")}');
        divObj.data('direction', '{$this->direction}');
        divObj.data('currency', '{$this->model->currency}');
        divObj.data('store_id', '{$this->model->store_id}');
        divObj.data('total_input_id', '{$this->input_total_id}');
        divObj.data('internal', '{$this->model->payment_internal}');
            
        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
               SIANMovementPaymentAddItem('{$this->id}', array[i]['payment_date'], array[i]['type'], array[i]['cashbox_id'], array[i]['reference_number'], array[i]['credit_card'], array[i]['amount'], array[i]['errors']);
            }
        }        
        SIANMovementPaymentUpdate('{$this->id}');      
            
        $('#{$this->id}').data('changeStore', function(changeData){
            $('#{$this->id}').data('store_id', changeData.store_id);
            $('#{$this->id}').children('tbody').find('tr').each(function () {
                var trObj = $(this);
                var movement_type = trObj.find('input.sian-movement-payment-item-type').val();
                var selectObj = trObj.find('select.sian-movement-payment-item-cashbox-id');
                SIANMovementPaymentUpdateCashboxes('{$this->id}', selectObj, movement_type, null);    
            });             
        });
        
        $('#{$this->id}').data('changeCurrency', function(changeData){
            $('#{$this->id}').data('currency', changeData.currency);
            $('#{$this->id}').children('tbody').find('tr').each(function () {
                var trObj = $(this);
                var movement_type = trObj.find('select.sian-movement-payment-item-type').val();
                var selectObj = trObj.find('select.sian-movement-payment-item-cashbox-id');
                SIANMovementPaymentUpdateCashboxes('{$this->id}', selectObj, movement_type, null);    
            });             
        }); 
        
        $('#{$this->id}').data('changeInternal', function(changeData){
            
            var divObj = $('#{$this->id}'); 
            var old_internal = divObj.data('internal');
            if(old_internal != changeData.internal){
                $('#{$this->payment_internal_input_id}').val(changeData.internal);
                divObj.data('internal', changeData.internal);
                var tableObj = divObj.find('table.sian-movement-payment-table');            

                tableObj.find('tbody tr.sian-movement-payment-item').each(function (index) {
                    var trObj = $(this);
                    var movement_type = trObj.find('select.sian-movement-payment-item-type').val();
                    var selectObj = trObj.find('select.sian-movement-payment-item-cashbox-id');
                    var cashbox_id = selectObj.val();
                    SIANMovementPaymentUpdateCashboxes('{$this->id}', selectObj, movement_type, cashbox_id); 
                });
            }            
        }); 

        $('body').on('change', '.sian-movement-payment-item-type', function(e) {            
            var movement_type = $(this).val();
            var trObj = $(this).parent().parent();
            var selectObj = trObj.find('select.sian-movement-payment-item-cashbox-id');
            //Seteamos
            SIANMovementPaymentEnabledOrDisabledRow(trObj);
            SIANMovementPaymentUpdateCashboxes('{$this->id}', selectObj, movement_type, null);
        });        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "Registro de " . ($this->direction == Scenario::DIRECTION_IN ? 'Cobros' : 'Pagos'),
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => $this->model->hasErrors('tempMovementPayments') ? 'us-error' : ''
            )
        ));
        echo $this->form->hiddenField($this->model, 'payment_internal', array(
            'id' => $this->payment_internal_input_id,
            'readonly' => true,
        ));
        echo $this->_renderPanel();
        $this->endWidget();
    }

    private function _renderPanel() {
        //Renderizar
        $html = '';
        $html .= "<table class='table table-condensed table-hover sian-movement-payment-table sian-movement-payment' data-count=0>";
        $html .= "<thead>";
        $html .= "<tr>";
        $html .= "<th width='5%'>#</th>";
        $html .= "<th width='10%'>{$this->model->getAttributeLabel('payments.payment_date')}</th>";
        $html .= "<th width='15%'>{$this->model->getAttributeLabel('payments.type')}</th>";
        $html .= "<th width='38%'>{$this->model->getAttributeLabel('payments.cashbox_id')}</th>";
        $html .= "<th width='10%'>{$this->model->getAttributeLabel('payments.reference_number')}</th>";
        $html .= "<th width='7%'>{$this->model->getAttributeLabel('payments.credit_card')}</th>";
        $html .= "<th width='10%' style='text-align:center'>{$this->model->getAttributeLabel('payments.amount')}</th>";
        $html .= "<th width='5%'></th>";
        $html .= "</tr>";
        $html .= "</thead>";
        $html .= "<tbody>";
        $html .= "<td class='empty' colspan='99'>";
        $html .= Strings::NO_DATA;
        $html .= "</td>";
        $html .= "</tbody>";
        $html .= "<tfoot>";
        $html .= "<tr>";
        $html .= "<td colspan='5'>";
        if (!$this->readonly) {
            $html .= CHtml::link("<span class='fa fa-plus fa-lg black'></span> Agregar nuevo", Strings::LINK_TEXT, array(
                        'onclick' => "var balance = SIANMovementPaymentGetBalance('{$this->id}');
                                      SIANMovementPaymentAddItem('{$this->id}', null, '', '', '', '', balance, []);
                                      SIANMovementPaymentUpdate('{$this->id}')",
                        'title' => 'Agregar'
            ));
        }
        $html .= "</td>";
        $html .= "<td style='text-align:right'><b>Total</b></td>";
        $a_errors = $this->model->getErrors();
        $s_error = isset($a_errors['payment_total']) && count($a_errors['payment_total']) > 0 ? $a_errors['payment_total'][0] : '';
        $html .= "<td style='text-align:right' class='form-group " . (isset($a_errors['payment_total']) ? "has-error" : "") . "'>";
        $html .= "<input type='text' style='text-align:right' class='form-control us-double sian-movement-payment-table-payment-total' name='Movement[payment_total]' placeholder='Monto' value='0.00' readonly/>";
        if (isset($a_errors['payment_total']) && count($a_errors['payment_total']) > 0) {
            $html .= '<span class="help-block error">' . $s_error . '</span>';
        }
        $html .= "</td>";
        $html .= "<td></td>";
        $html .= "</tr>";
        $html .= "</tfoot>";
        $html .= "</table>";
        return $html;
    }

}
