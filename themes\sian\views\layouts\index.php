<!DOCTYPE html>
<html lang="es-us">
    <head>
        <meta charset="utf-8" />
        <title><?php echo CHtml::encode($this->pageTitle); ?></title>

        <meta name="description" content="overview &amp; stats" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="shortcut icon"  href="<?php echo Yii::app()->params['admin_url']; ?>/images/icons/favicon.svg">
        <link rel="stylesheet" href="<?php echo Yii::app()->theme->getBaseUrl(); ?>/bootstrap/css/bootstrap.min.css"/>

        <?php
        SIANAssets::registerCoreCss();
        ?>

        <style type="text/css"></style>
    </head>

    <body id="SianBody" style="background-image: url('<?php echo Yii::app()->params['admin_url']; ?>/images/wallpaper/login-banner.svg'); background-size: cover; background-attachment: fixed;">
        <?php
        if (Yii::app()->params['environment'] !== YII_ENVIRONMENT_PRODUCTION) {
        ?>
        <div id="development">
            <div class="img-1" style="position: absolute; width: 28px; height: 100%; top: 0; left: 0;"></div>
            <div class="img-2" style="position: absolute; width: 28px; height: 100%; top: 0; right: 0;"></div>
        </div>
        <?php } ?>
            
        <div class="container-fluid" id="page">
            <?php echo $content; ?>
        </div>
        <div class="clear"></div>        
    </body>
    <?php
    if (Yii::app()->params['environment'] === YII_ENVIRONMENT_PRODUCTION) {
//        $this->widget('application.widgets.SIANFreshChat');
    }
    SIANAssets::registerCoreJs();
    ?>

</html>
