<?php

class USYoutubeEmbed extends CWidget {

    public $id;
    public $video_id;
    public $url;
    //PRIVATE
    private $clientScript;
    private $controller;

    public function init() {
        //ID
        $this->controller = Yii::app()->controller;
        $this->clientScript = Yii::app()->clientScript;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        SIANAssets::registerCssFile('us/us-youtube/css/us-youtube.css');
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $s_video_id = '';
        if (isset($this->video_id)) {
            $s_video_id = $this->video_id;
        } else {
            $s_video_id = USYoutube::getVideoId($this->url);
        }

        echo "<div id='{$this->id}' class='us-youtube'>";
        echo USYoutube::getEmbedCode($s_video_id);
        echo "</div>";
    }

}
