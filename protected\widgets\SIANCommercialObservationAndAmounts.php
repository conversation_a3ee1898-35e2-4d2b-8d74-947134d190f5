<?php

class SIANCommercialObservationAndAmounts extends CWidget {

    //CONST
    public $id;
    public $model;
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<hr>";
        echo "<div class='row'>";
        echo "<div class='col-lg-10 col-md-10 col-sm-10 col-xs-12 us-detail'>";
        echo "<table style='table-layout: fixed'>";

        echo "<tr><td><strong>{$this->model->getAttributeLabel('sales_specifications')}</strong></td></tr>";
        echo "<tr><td>" . USString::ifBlank($this->model->sales_specifications, Strings::NONE) . "</td></tr>";

        echo "<tr><td><strong>{$this->model->movement->getAttributeLabel('observation')}</strong></td></tr>";
        echo "<tr><td>" . USString::ifBlank($this->model->movement->observation, Strings::NONE) . "</td></tr>";

        echo "</table>";
        echo "</div>";
        //
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-2 us-detail'>";
        echo "<table>";
        echo "<tr><td colspan='2'><h4>Montos {$this->model->movement->getCurrencySymbol()}</h4></td></tr>";
        if ($this->model->getValue('affected') > 0) {
            echo "<tr><td><strong>GRAVADO:</strong></td><td class='pull-right'>{$this->model->displayValue('affected')}</td></tr>";
        }
        if ($this->model->getValue('inaffected') > 0) {
            echo "<tr><td><strong>INAFECTO:</strong></td><td class='pull-right'>{$this->model->displayValue('inaffected')}</td></tr>";
        }
        if ($this->model->getValue('nobill') > 0) {
            echo "<tr><td><strong>EXONERADO:</strong></td><td class='pull-right'>{$this->model->displayValue('nobill')}</td></tr>";
        }
        if ($this->model->getValue('export') > 0) {
            echo "<tr><td><strong>EXPORTACIÓN:</strong></td><td class='pull-right'>{$this->model->displayValue('export')}</td></tr>";
        }
        if ($this->model->getValue('free') > 0) {
            echo "<tr><td><strong>GRATUITO:</strong></td><td class='pull-right'>{$this->model->displayValue('free')}</td></tr>";
        }
        echo "<tr><td><strong>SUBTOTAL:</strong></td><td class='pull-right'>{$this->model->displayValue('net')}</td></tr>";
        if ($this->model->getValue('isc') > 0) {
            echo "<tr><td><strong>ISC:</strong></td><td class='pull-right'>{$this->model->displayValue('isc')}</td></tr>";
        }       
        if ($this->model->getValue('adjust_loss') > 0) {
            echo "<tr><td><strong>+ AJUSTE:</strong></td><td class='pull-right'>{$this->model->displayValue('adjust_loss')}</td></tr>";
        }        
        if ($this->model->getValue('adjust_gain') > 0) {
            echo "<tr><td><strong>- AJUSTE:</strong></td><td class='pull-right'>{$this->model->displayValue('adjust_gain')}</td></tr>";
        }
        echo "<tr><td><strong>IGV:</strong></td><td class='pull-right'>{$this->model->displayValue('igv')}</td></tr>";
        if ($this->model->getValue('icbp') > 0) {
            echo "<tr><td><strong>ICBP:</strong></td><td class='pull-right'>{$this->model->displayValue('icbp')}</td></tr>";
        }
        echo "<tr><td><strong>TOTAL:</strong></td><td class='pull-right'>{$this->model->displayValue('total')}</td></tr>";
        if ($this->model->getValue('perception') > 0) {
            echo "<tr><td><strong>PERP.:</strong></td><td class='pull-right'>{$this->model->displayValue('perception')}</td></tr>";
            echo "<tr><td><strong>TOTAL A PAGAR:</strong></td><td class='pull-right'>{$this->model->displayValue('real')}</td></tr>";
        }
        //Retención
        if ($this->model->retention_percent > 0) {
            echo "<tr><td colspan=2><hr style='margin-top:0px;margin-bottom:0px;'></td></tr>";
            echo "<tr><td><strong>RETENIDO:</strong></td><td class='pull-right'>{$this->model->displayValue('ret')}</td></tr>";
            echo "<tr><td><strong>SIN RETENCIÓN:</strong></td><td class='pull-right'>{$this->model->displayValue('no_ret')}</td></tr>";
        }
        //Detracción
        if ($this->model->detraction_percent > 0) {
            echo "<tr><td colspan=2><hr style='margin-top:0px;margin-bottom:0px;'></td></tr>";
            echo "<tr><td><strong>DETRACCIÓN:</strong></td><td class='pull-right'>{$this->model->displayValue('det')}</td></tr>";
            echo "<tr><td><strong>SIN DETRACCIÓN:</strong></td><td class='pull-right'>{$this->model->displayValue('no_ret')}</td></tr>";
        }

        $advance_percentage = number_format($this->model->advance_percentage, 2, '.', ',');
        $advance_amount = number_format($this->model->advance_amount, 2, '.', ',');
        $advance_balance = number_format($this->model->advance_balance, 2, '.', ',');

        if ($advance_amount > 0) {
            echo "<tr><td><strong>% DE ADELANTO:</strong></td><td class='pull-right'>{$advance_percentage}</td></tr>";
            echo "<tr><td><strong>TOTAL ADELANTO:</strong></td><td class='pull-right'>{$advance_amount}</td></tr>";
            echo "<tr><td><strong>SALDO ADELANTO:</strong></td><td class='pull-right'>{$advance_balance}</td></tr>";
        }
        echo "</table>";
        echo "</div>";
        echo "</div>";
    }

}
