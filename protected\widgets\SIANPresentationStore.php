<?php

class SIANPresentationStore extends CWidget {

    public $id;
    public $form;
    public $model;
    public $unit_measure;
    public $ifixed = Product::IFIXED;
    public $tfixed = Product::TFIXED;
    public $measure_id = null;
    public $measure_items = [];
    public $custom_measure_items = [];
    public $min_margin = 0;
    public $avg_margin = 0;
    public $web_margin = 0;
    public $cost = 0;
    public $presentation_items = [];
    public $store_items = [];
    public $has_panel = true;
    public $unique_price = false;
    public $readonly = false;
    public $show_only_prices = false;
    public $lock_prices = false;
    public $lock_default = false;
    public $lock_add_and_remove = false;
    //PRIVATE
    private $controller;
    private $istep;
    private $tstep;
    private $currency;
    private $thead;
    private $use_addprice;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->currency = isset($this->model->price_currency) ? $this->model->price_currency : $this->controller->getOrganization()->globalVar->display_currency;
        $this->use_addprice = $this->model->product_type == Product::TYPE_MERCHANDISE && $this->model->merchandise->subline->line->use_addprice1 == 1;
        //VAR
        $this->istep = USMath::toStep($this->ifixed);
        $this->tstep = USMath::toStep($this->tfixed);

        if ($this->controller->getOrganization()->globalVar->price_mode == GlobalVar::PRICE_MODE_BY_ORGANIZATION) {
            $this->unique_price = true;
        }
        //Cabecera de cada div
        $this->thead = '<thead><tr>';
        $i_width = '33';
        if (!$this->lock_add_and_remove) {
            $i_width = '20';
            $this->thead .= '<th width="15%" class="sian-presentation-store-basic">' . $this->model->getAttributeLabel('presentations.measure_id') . '</th>';
            $this->thead .= '<th width="15%" class="sian-presentation-store-basic">' . $this->model->getAttributeLabel('presentations.custom_measure_id') . '</th>';
            $this->thead .= '<th width="10%" class="sian-presentation-store-basic">' . $this->model->getAttributeLabel('presentations.equivalence') . '</th>';
        }
        if ($this->use_addprice) {
            $i_width = '15';
        }

        $mprice_label = 'P.Min.';
        $aprice_label = Yii::app()->controller->getOrganization()->globalVar->aprice_label;
        $wprice_label = Yii::app()->controller->getOrganization()->globalVar->wprice_label;
        if ($this->model->product_type == Product::TYPE_MERCHANDISE && $this->model->merchandise->subline->line->use_addprice1 == 1) {
            $addprice_label = Yii::app()->controller->getOrganization()->globalVar->addprice1_label;
        }

        $this->thead .= '<th width="' . $i_width . '%" class="sian-presentation-store-basic sian-presentation-store-without-igv"' . ($this->model->price_mode == 0 ? '' : 'style="display: none;"') . '>' . $mprice_label . '<span class = "currency-symbol"></span></th>';
        $this->thead .= '<th width="' . $i_width . '%" class="sian-presentation-store-basic sian-presentation-store-with-igv"' . ($this->model->price_mode == 1 ? '' : 'style="display: none;"') . '>' . $mprice_label . '<span class = "currency-symbol"></span></th>';
        $this->thead .= '<th width="' . $i_width . '%" class="sian-presentation-store-basic sian-presentation-store-without-igv"' . ($this->model->price_mode == 0 ? '' : 'style="display: none;"') . '>' . $aprice_label . '<span class = "currency-symbol"></span></th>';
        $this->thead .= '<th width="' . $i_width . '%" class="sian-presentation-store-basic sian-presentation-store-with-igv"' . ($this->model->price_mode == 1 ? '' : 'style="display: none;"') . '>' . $aprice_label . '<span class = "currency-symbol"></span></th>';
        $this->thead .= '<th width="' . $i_width . '%" class="sian-presentation-store-basic sian-presentation-store-without-igv"' . ($this->model->price_mode == 0 ? '' : 'style="display: none;"') . '>' . $wprice_label . '<span class = "currency-symbol"></span></th>';
        $this->thead .= '<th width="' . $i_width . '%" class="sian-presentation-store-basic sian-presentation-store-with-igv"' . ($this->model->price_mode == 1 ? '' : 'style="display: none;"') . '>' . $wprice_label . '<span class = "currency-symbol"></span></th>';
        if ($this->use_addprice) {
            $this->thead .= '<th width="' . $i_width . '%" class="sian-presentation-store-basic sian-presentation-store-without-igv"' . ($this->model->price_mode == 0 ? '' : 'style="display: none;"') . '>' . $addprice_label . '<span class = "currency-symbol"></span></th>';
            $this->thead .= '<th width="' . $i_width . '%" class="sian-presentation-store-basic sian-presentation-store-with-igv"' . ($this->model->price_mode == 1 ? '' : 'style="display: none;"') . '>' . $addprice_label . '<span class = "currency-symbol"></span></th>';
        }
        $this->thead .= '</tr></thead><tbody></tbody>';

        foreach ($this->model->presentations as $o_presentation) {
            $attributes_presentation = [];
            $attributes_presentation['abbreviation'] = $o_presentation->abbreviation;
            $attributes_presentation['equivalence'] = $o_presentation->equivalence;
            $attributes_presentation['measure_id'] = $o_presentation->measure_id;
            $attributes_presentation['custom_measure_id'] = $o_presentation->custom_measure_id;
            $attributes_presentation['measure_name'] = $o_presentation->measure_name;
            $this->presentation_items[] = $attributes_presentation;
        }
        $b_renderDiv = true;

        $prices = [];
        if (count($this->model->tempPrices) > 0) {
            //Precios

            foreach ($this->model->tempPrices as $o_price) {

                $attributes = [];
                $attributes['price_id'] = $o_price->price_id;
                $attributes['product_id'] = $o_price->product_id;

                if (count($o_price->tempPricePresentations) > 0) {

                    foreach ($o_price->tempPricePresentations as $o_pricePresentation) {

                        $attributes_presentation = [];
                        $attributes_presentation['price_id'] = $o_pricePresentation->price_id;
                        $attributes_presentation['product_id'] = $o_pricePresentation->product_id;
                        $attributes_presentation['equivalence'] = $o_pricePresentation->equivalence;
                        $attributes_presentation['measure_id'] = $o_pricePresentation->measure_id;
                        $attributes_presentation['custom_measure_id'] = $o_pricePresentation->custom_measure_id;
                        $attributes_presentation['mprice'] = $o_pricePresentation->{"mprice_{$this->currency}"};
                        $attributes_presentation['imprice'] = $o_pricePresentation->{"imprice_{$this->currency}"};
                        $attributes_presentation['aprice'] = $o_pricePresentation->{"aprice_{$this->currency}"};
                        $attributes_presentation['iaprice'] = $o_pricePresentation->{"iaprice_{$this->currency}"};
                        $attributes_presentation['wprice'] = $o_pricePresentation->{"wprice_{$this->currency}"};
                        $attributes_presentation['iwprice'] = $o_pricePresentation->{"iwprice_{$this->currency}"};
                        $attributes_presentation['addprice1'] = $o_pricePresentation->{"addprice1_{$this->currency}"};
                        $attributes_presentation['iaddprice1'] = $o_pricePresentation->{"iaddprice1_{$this->currency}"};
                        $attributes_presentation['errors'] = [
                            'mprice' => $o_pricePresentation->getError("mprice_{$this->currency}"),
                            'aprice' => $o_pricePresentation->getError("aprice_{$this->currency}"),
                            'wprice' => $o_pricePresentation->getError("wprice_{$this->currency}"),
                            'addprice1' => $o_pricePresentation->getError("addprice1_{$this->currency}"),
                            'imprice' => $o_pricePresentation->getError("imprice_{$this->currency}"),
                            'iaprice' => $o_pricePresentation->getError("iaprice_{$this->currency}"),
                            'iwprice' => $o_pricePresentation->getError("iwprice_{$this->currency}"),
                            'iaddprice1' => $o_pricePresentation->getError("iaddprice1_{$this->currency}")
                        ];
                        $attributes['presentations'][] = $attributes_presentation;
                    }

                    foreach ($o_price->tempStores as $o_store) {

                        $attributes['stores'][$o_store->owner_id] = $o_store->owner_name;
                    }
                } else {
                    $b_renderDiv = $b_renderDiv && false;
                }
                $errors = $o_price->getErrors('tempStores');
                $attributes['errors'] = isset($errors) ? $errors : [];
                $prices[] = $attributes;
            }
        } else {
            $b_renderDiv = false;
        }

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-presentation-store.js');

        //Registramos scripts
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        $(document).ready(function() {
        
            //COUNT
            $('#{$this->id}').data('count', 0);
            //TIPE
            $('#{$this->id}').data('product_id', '{$this->model->product_id}');
            $('#{$this->id}').data('product_type', '{$this->model->product_type}');
            //IGV
            $('#{$this->id}').data('igv', {$this->controller->getOrganization()->globalVar->igv});
            $('#{$this->id}').data('measure_items', " . CJSON::encode($this->measure_items) . ");
            $('#{$this->id}').data('custom_measure_items', " . CJSON::encode($this->custom_measure_items) . ");
            $('#{$this->id}').data('allow_decimals', {$this->model->allow_decimals});
            $('#{$this->id}').data('ifixed', {$this->ifixed});
            $('#{$this->id}').data('tfixed', {$this->tfixed});
            $('#{$this->id}').data('istep', {$this->istep});
            $('#{$this->id}').data('tstep', {$this->tstep});
            $('#{$this->id}').data('lock_prices', {$this->lock_prices});
            $('#{$this->id}').data('lock_default', {$this->lock_default});
            $('#{$this->id}').data('lock_add_and_remove', {$this->lock_add_and_remove});
            $('#{$this->id}').data('advanced', {$this->model->advanced});
            $('#{$this->id}').data('price_mode', {$this->model->price_mode});
            $('#{$this->id}').data('measure_id', '{$this->measure_id}');
            $('#{$this->id}').data('unique_price', " . ( $this->unique_price ? 1 : 0 ) . ");
            $('#{$this->id}').data('readonly', " . ( $this->readonly ? 1 : 0 ) . ");
            $('#{$this->id}').data('has_panel', " . ( $this->has_panel ? 1 : 0 ) . ");
            $('#{$this->id}').data('show_only_prices', " . ( $this->show_only_prices ? 1 : 0 ) . ");   
            //MARGENES DE UTILIDAD
            $('#{$this->id}').data('min_margin', {$this->min_margin});
            $('#{$this->id}').data('avg_margin', {$this->avg_margin});
            $('#{$this->id}').data('web_margin', {$this->web_margin});
            $('#{$this->id}').data('cost', {$this->cost});
            $('#{$this->id}').data('currency', '{$this->currency}')
            $('#{$this->id}').data('presentation_items', " . CJSON::encode($this->presentation_items) . ");
            $('#{$this->id}').data('store_items', " . CJSON::encode($this->store_items) . ");        
            $('#{$this->id}').data('use_addprice', " . CJSON::encode($this->use_addprice) . ");  
            //MOVIMIENTOS
            var renderDiv = " . CJSON::encode($b_renderDiv) . ";   
            if(renderDiv){
                var array = " . CJSON::encode($prices) . ";
                if (array.length > 0)
                {
                    for (var i = 0; i < array.length; i++)
                    {
                        SIANPresentationStoreAddDivPrice('{$this->id}', '{$this->thead}', 0, array[i]);
                    }
                }
            }           
//          SIANPresentationStoreUpdate('{$this->id}');
            unfocusable();
            $('#{$this->id}').find('thead span.currency-symbol').text(USCurrencyGetSymbol('{$this->currency}'));           
        });
        
        $('body').on('click', '#{$this->id} input.sian-presentation-store-item-default', function() {
            $('#{$this->id} input[type=radio]:checked').not(this).prop('checked', false);
        });
                   
        $('#{$this->id}').data('allowDecimals', function(allow_decimals){
            var table = $('#{$this->id}');
            table.data('allow_decimals', allow_decimals);
            //Actualizamos
            SIANPresentationStoreUpdate('{$this->id}');
        });
        
        $('#{$this->id}').data('changeCurrency', function(currency){
            
            var table = $('#{$this->id}');            
            var header = table.find('thead');
            var exchange_rate = " . Yii::app()->controller->getLastExchange() . ";
            var tfixed = {$this->tfixed};
            
            $(header).find('span.currency-symbol').text(USCurrencyGetSymbol(currency));
            
            $.each(table.find('tr.sian-presentation-store-item'), function( index, row ) {
            
                var rowObj = $(row);                
                rowObj.find('input.sian-presentation-store-item-currency').val(currency);
                
                if(currency == '" . Currency::USD . "'){  
                    
                    rowObj.find('input.sian-presentation-store-item-mprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-imprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-aprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-iaprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-wprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-iwprice').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-addprice1').toUsd(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-iaddprice1').toUsd(exchange_rate, tfixed);
                    
                }else{

                    rowObj.find('input.sian-presentation-store-item-mprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-imprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-aprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-iaprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-wprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-iwprice').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-addprice1 ').toPen(exchange_rate, tfixed);
                    rowObj.find('input.sian-presentation-store-item-iaddprice1 ').toPen(exchange_rate, tfixed);
                }
            });           
        });
        
        $('#{$this->id}').data('setCurrency', function(currency){
            
            var table = $('#{$this->id}');     
            $.each(table.find('tr.sian-presentation-store-item'), function (index, row) {
                var rowObj = $(row);
                rowObj.find('input.sian-presentation-store-item-currency').val(currency);
            });
        });                    
       
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->has_panel) {
            $this->beginWidget('booster.widgets.TbPanel', array('title' => "Lista de Precios",
                'headerIcon' => 'list',
                'htmlOptions' => array(
                    'class' => ($this->model->hasErrors('tempPresentations') ? 'us-error' : ''),
                )
            ));
        }

        if ($this->controller->getOrganization()->globalVar->price_mode == GlobalVar::PRICE_MODE_BY_STORE) {
            if (!$this->lock_add_and_remove) {
                echo CHtml::link("<span class='fa fa-plus fa-lg black'></span> Agregar Precio", null, array(
                    'onclick' => "SIANPresentationStoreAddDivPrice('{$this->id}', '{$this->thead}', 1 ,[]);",
                    'title' => 'Agregar Precio'
                ));
                echo "<br><br>";
            }
        }
        echo "<div id='{$this->id}' class='table-responsive'>";
        echo "</div>";
        if ($this->has_panel) {
            $this->endWidget();
        }
    }

}
