<?php

$a_classification = [
    array(
        array('icon' => 'star', 'label' => 'Marcas', 'route' => 'mark/index'),
    ),
    array(
        array('icon' => 'list', 'label' => 'Divisiones', 'route' => 'division/index'),
        array('icon' => 'list', 'label' => 'Líneas', 'route' => 'line/index'),
        array('icon' => 'list', 'label' => 'Sublíneas', 'route' => 'subline/index'),
        array('icon' => 'barcode', 'label' => 'Productos', 'route' => 'merchandise/index'),
    ),
    array(
        array('icon' => 'list', 'label' => 'Categorías', 'route' => 'serviceCategory/index'),
        array('icon' => 'wrench', 'label' => 'Servicios', 'route' => 'service/index'),
    ),
    array(
        array('icon' => 'qrcode', 'label' => 'Combos', 'route' => 'combo/index'),
        array('icon' => 'qrcode', 'label' => 'Combos por Tienda', 'route' => 'comboStore/index'),
    )
];

if ($this->getOrganization()->globalVar->use_batch != GlobalVar::BATCH_USE_BATCH_NO) {
    $a_classification[] = [
        array('icon' => 'list', 'label' => 'Lotes', 'route' => 'batch/index'),
        array('icon' => 'list', 'label' => 'Cruce de Lotes', 'route' => 'batchCrossing/index'),
    ];
}

$arrayItemsNavbar = array(
    //array('icon' => 'list', 'label' => 'Datos Generales', 'url' => '#', 'items' => array(
    array('label' => 'Datos Generales', 'url' => '#', 'items' => array(
            array('icon' => 'th-large', 'label' => 'Almacenes', 'route' => 'warehouse/index'),
            array('icon' => 'list', 'label' => 'Presentación Personalizada', 'route' => 'customMeasure/index'),
            array('icon' => 'user', 'label' => 'Proveedores E-Commerce', 'route' => 'eCommerceProvider/index'),
        )),
    //array('icon' => 'list', 'label' => 'Clasificación', 'url' => '#', 'items' => array(
    array('label' => 'Clasificación', 'url' => '#', 'items' => $a_classification),
    //array('icon' => 'shopping-cart', 'label' => 'Movimientos', 'url' => '#', 'items' => array(
    array('label' => 'Movimientos', 'url' => '#', 'items' => array(
            array(
                array('icon' => 'tag', 'label' => 'Requerimientos', 'route' => 'prePurchaseOrder/index', 'items' => array(
                        array('icon' => 'plus', 'label' => 'Crear', 'route' => 'prePurchaseOrder/create'),
                    )),
                array('icon' => 'tag', 'label' => 'Ordenes de compra', 'route' => 'purchaseOrder/index', 'items' => array(
                        array('icon' => 'plus', 'label' => 'Crear', 'route' => 'purchaseOrder/create'),
                    )),
                array('icon' => 'tags', 'label' => 'Comprobantes de compra', 'route' => 'purchaseBill/index'),
                array('icon' => 'tags', 'label' => 'Gastos operativos (Costo)', 'route' => 'operatingCost/index', 'items' => array(
                        array('icon' => 'plus', 'label' => 'Crear', 'route' => 'operatingCost/create'),
                    )),
            ),
            array(
                array('icon' => 'file', 'label' => 'Notas de crédito de proveedor (Dif.Precio)', 'route' => 'creditNote1/index'),
                array('icon' => 'file', 'label' => 'Notas de crédito de proveedor (Devolución)', 'route' => 'creditNote2/index'),
                array('icon' => 'file', 'label' => 'Notas de crédito de proveedor (No despacho)', 'route' => 'creditNote3/index'),
                array('icon' => 'file', 'label' => 'Notas de débito de proveedor', 'route' => 'debitNote/index'),
            ),
            array(
                array('icon' => 'plane', 'label' => 'Gastos por movilidad', 'route' => 'mobility/index', 'items' => array(
                        array('icon' => 'plus', 'label' => 'Crear', 'route' => 'mobility/create'),
                    )),
            )
        )),
    //array('icon' => 'file', 'label' => 'Reportes', 'url' => '#', 'items' => array(
    array('label' => 'Reportes', 'url' => '#', 'items' => array(
            array('icon' => 'list', 'label' => 'Mercaderías', 'route' => 'report/merchandises'),
            array('icon' => 'list', 'label' => 'Mercaderías - Stocks', 'route' => 'report/stocks'),
            array('icon' => 'list', 'label' => 'Mercaderías - Proveedores', 'route' => 'report/merchandisesProviders'),
            array('icon' => 'list', 'label' => 'Mercaderías - Últimos movimientos', 'route' => 'report/merchandisesLastMovement'),
            array('icon' => 'tag', 'label' => 'Costo de Ventas', 'route' => 'report/saleCost'),
            array('icon' => 'plus', 'label' => 'Compras', 'route' => 'report/purchases'),
            array('icon' => 'retweet', 'label' => 'Rotación de Productos', 'route' => 'report/noRotation'),
            array('icon' => 'th-list', 'label' => 'Productos Recientes', 'route' => 'report/recentProducts'),
            array('icon' => 'th-list', 'label' => 'Kardex ', 'route' => 'report/kardex'),
            array('icon' => 'th-list', 'label' => 'Mov. por Operación ', 'route' => 'report/operation'),
            array('icon' => 'th-list', 'label' => 'Gastos por Movilidad', 'route' => 'report/mobility'),
            array('icon' => 'th-list', 'label' => 'Gastos Operativos', 'route' => 'report/operatingCost'),
            array('icon' => 'retweet', 'label' => 'Reposición de Productos', 'route' => 'report/replenishment'),
            array('icon' => 'retweet', 'label' => 'Valorizado de Existencias', 'route' => 'report/valuedStock'),
            array('icon' => 'retweet', 'label' => 'Resumen por Operación', 'route' => 'report/summaryOperation'),
            array('icon' => 'list', 'label' => 'Lotes', 'route' => 'report/batch'),
        )),
);
$this->widget('application.widgets.SIANNavbar', array(
    'brand' => Yii::app()->id,
    'class' => 'navbar-logistic',
    'items' => $this->items_menu, //$arrayItemsNavbar,
));

