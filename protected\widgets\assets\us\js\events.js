window.has_beforeunload = 0;
window.active_ajax = 0;

$(window).bind('beforeunload', function () {
    if (window.has_beforeunload > 0)
    {
        return 'Hay un formulario abierto. Si sale o refresca está página podría perder información.';
    }
});

// Automatically cancel unfinished ajax requests 
// when the user navigates elsewhere.
//https://stackoverflow.com/questions/1802936/stop-all-active-ajax-requests-in-jquery
(function ($) {
    var xhrPool = [];
    $(document).ajaxSend(function (e, jqXHR, options) {
        xhrPool.push(jqXHR);
    });
    $(document).ajaxComplete(function (e, jqXHR, options) {
        xhrPool = $.grep(xhrPool, function (x) {
            return x != jqXHR
        });
    });
    var abort = function () {
        $.each(xhrPool, function (idx, jqXHR) {
            jqXHR.abort();
        });
    };

    var oldbeforeunload = window.onbeforeunload;
    window.onbeforeunload = function () {
        var r = oldbeforeunload ? oldbeforeunload() : undefined;
        if (r == undefined) {
            // only cancel requests if there is no prompt to stay on the page
            // if there is a prompt, it will likely give the requests enough time to finish
            abort();
        }
        return r;
    }
})(jQuery);

$('body').on('click', '.lbl', function () {
    $(this).parent('label').children('input').trigger('click');
});

$('body').on('keypress', '.us-cleantext', function (event) {
    var charCode = (event.which) ? event.which : event.keyCode;
    if (charCode === 39) {
        event.preventDefault();
    }
});

$('body').on('keypress', '.us-phone', function (event) {

    var string = $(this).val();
    var charCode = (event.which) ? event.which : event.keyCode;

    var error = false;

    if (string.length > 0)
    {
        error = !(charCode >= 48 && charCode <= 57);
    } else
    {
        error = !(charCode === 35 || charCode === 42 || charCode === 43 || (charCode >= 48 && charCode <= 57));
    }

    if (error) {
        event.preventDefault();
    }
});

$('body').on('keypress', '.us-integer, .us-double0', function (event) {
    var charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
        event.preventDefault();
    }
});

$('body').on('keypress', '.us-double1, .us-double, .us-double2, .us-double3, .us-double4, .us-double5, .us-double6', function (event) {
    var charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && charCode !== 46 && charCode !== 44 && (charCode < 48 || charCode > 57)) {
        event.preventDefault();
    }
});

$('body').on('keypress', '.us-nospace', function (event) {
    var charCode = (event.which) ? event.which : event.keyCode;
    if (charCode === 32) {
        event.preventDefault();
    }
});

$('body').on('change', '.us-integer, .us-double0', function () {
    var obj = $(this);
    if (obj.val().length === 0)
        obj.val(0);
    obj.val(parseInt(obj.val()));
});

$('body').on('change', '.us-double1', function () {
    var obj = $(this);
    if (obj.val().length === 0)
        obj.val(0);
    obj.val(USMath.round(obj.val(), 1).toFixed(1));
});

$('body').on('change', '.us-double', function () {
    var obj = $(this);
    if (obj.val().length === 0)
        obj.val(0);
    obj.val(USMath.round(obj.val(), 2).toFixed(2));
});

$('body').on('change', '.us-double2', function () {
    var obj = $(this);
    if (obj.val().length === 0)
        obj.val(0);
    obj.val(USMath.round(obj.val(), 2).toFixed(2));
});

$('body').on('change', '.us-double3', function () {
    var obj = $(this);
    if (obj.val().length === 0)
        obj.val(0);
    obj.val(USMath.round(obj.val(), 3).toFixed(3));
});

$('body').on('change', '.us-double4', function () {
    var obj = $(this);
    if (obj.val().length === 0)
        obj.val(0);
    obj.val(USMath.round(obj.val(), 4).toFixed(4));
});

$('body').on('change', '.us-double5', function () {
    var obj = $(this);
    if (obj.val().length === 0)
        obj.val(0);
    obj.val(USMath.round(obj.val(), 5).toFixed(5));
});

$('body').on('change', '.us-double6', function () {
    var obj = $(this);
    if (obj.val().length === 0)
        obj.val(0);
    obj.val(USMath.round(obj.val(), 6).toFixed(6));
});

$('body').on('change', '.us-cleantext', function () {
    var obj = $(this);
    obj.val(obj.val().replace("'", '"'));
});

/*
 $('body').on('change', '.us-field', function () {
 var value = $(this).val();
 $(this).attr('value', value);
 });
 
 $('body').on('change', 'textarea.us-field', function () {
 var value = $(this).val();
 $(this).text(value);
 });
 
 $('body').on('change', 'select.us-field', function () {
 var value = $(this).val();
 $(this).find("option").attr('selected', false).filter(function () {
 return $(this).val() == value
 }).attr('selected', true);
 });
 */

$('body').on('keydown', '.enterTab', function (event) {
    if (event.which === 13 && !$(this).is("textarea, :button, :submit")) {
        event.stopPropagation();
        event.preventDefault();

        $(this)
                .nextAll(":input:not(:disabled, [readonly='readonly'])")
                .first()
                .focus();
    }
});

$('body').on('keydown mousedown', 'select', function (event) {
    if ($(this).hasAttr('readonly'))
    {
        event.stopPropagation();
        event.preventDefault();
    }
});

$('body').on('click', 'input[readonly]', function (e) {
    e.preventDefault();
    return false;
});