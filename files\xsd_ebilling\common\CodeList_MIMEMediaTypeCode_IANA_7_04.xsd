<?xml version="1.0" encoding="UTF-8"?>
<!-- ====================================================================== -->
<!-- ===== Mime Media Type Codelist Schema Module  ===== -->
<!-- ====================================================================== -->
<!--
  	Module of		Mime Media Type Codelist
  	Agency: 		UN/CEFACT	
  	Version:		1.1	
  	Last change: 	14 January 2005
  	
  	Copyright (C) UN/CEFACT (2006). All Rights Reserved.

	This document and translations of it may be copied and furnished to others, 
	and derivative works that comment on or otherwise explain it or assist 
	in its implementation may be prepared, copied, published and distributed, 
	in whole or in part, without restriction of any kind, provided that the 
	above copyright notice and this paragraph are included on all such copies 
	and derivative works. However, this document itself may not be modified in 
	any way, such as by removing the copyright notice or references to 
	UN/CEFACT, except as needed for the purpose of developing UN/CEFACT 
	specifications, in which case the procedures for copyrights defined in the 
	UN/CEFACT Intellectual Property Rights document must be followed, or as 
	required to translate it into languages other than English.

	The limited permissions granted above are perpetual and will not be revoked 
	by UN/CEFACT or its successors or assigns.

	This document and the information contained herein is provided on an "AS IS"
	basis and UN/CEFACT DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING 
	BUT NOT LIMITED TO ANY WARRANTY THAT THE USE OF THE INFORMATION HEREIN WILL 
	NOT INFRINGE ANY RIGHTS OR ANY IMPLIED WARRANTIES OF MERCHANTABILITY OR 
	FITNESS FOR A PARTICULAR PURPOSE.
-->
<xsd:schema targetNamespace="urn:un:unece:uncefact:codelist:specification:IANAMIMEMediaType:2003" 
xmlns:ccts="urn:un:unece:uncefact:documentation:2"
xmlns:clmIANAMIMEMediaType="urn:un:unece:uncefact:codelist:specification:IANAMIMEMediaType:2003" 
xmlns:xsd="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xsd:simpleType name="BinaryObjectMimeCodeContentType">
		<xsd:restriction base="xsd:normalizedString">
      <xsd:enumeration value="application/CSTAdata+xml"/>
      <xsd:enumeration value="application/EDI-Consent"/>
      <xsd:enumeration value="application/EDI-X12"/>
      <xsd:enumeration value="application/EDIFACT"/>
      <xsd:enumeration value="application/activemessage"/>
      <xsd:enumeration value="application/andrew-inset"/>
      <xsd:enumeration value="application/applefile"/>
      <xsd:enumeration value="application/atomicmail"/>
      <xsd:enumeration value="application/batch-SMTP"/>
      <xsd:enumeration value="application/beep+xml"/>
      <xsd:enumeration value="application/cals-1840"/>
      <xsd:enumeration value="application/cnrp+xml"/>
      <xsd:enumeration value="application/commonground"/>
      <xsd:enumeration value="application/cpl+xml"/>
      <xsd:enumeration value="application/csta+xml"/>
      <xsd:enumeration value="application/cybercash"/>
      <xsd:enumeration value="application/dca-rft"/>
      <xsd:enumeration value="application/dec-dx"/>
      <xsd:enumeration value="application/dialog-info+xml"/>
      <xsd:enumeration value="application/dicom"/>
      <xsd:enumeration value="application/dns"/>
      <xsd:enumeration value="application/dvcs"/>
      <xsd:enumeration value="application/epp+xml"/>
      <xsd:enumeration value="application/eshop"/>
      <xsd:enumeration value="application/fits"/>
      <xsd:enumeration value="application/font-tdpfr"/>
      <xsd:enumeration value="application/http"/>
      <xsd:enumeration value="application/hyperstudio"/>
      <xsd:enumeration value="application/iges"/>
      <xsd:enumeration value="application/im-iscomposing+xml"/>
      <xsd:enumeration value="application/index"/>
      <xsd:enumeration value="application/index.cmd"/>
      <xsd:enumeration value="application/index.obj"/>
      <xsd:enumeration value="application/index.response"/>
      <xsd:enumeration value="application/index.vnd"/>
      <xsd:enumeration value="application/iotp"/>
      <xsd:enumeration value="application/ipp"/>
      <xsd:enumeration value="application/isup"/>
      <xsd:enumeration value="application/kpml-request+xml"/>
      <xsd:enumeration value="application/kpml-response+xml"/>
      <xsd:enumeration value="application/mac-binhex40"/>
      <xsd:enumeration value="application/macwriteii"/>
      <xsd:enumeration value="application/marc"/>
      <xsd:enumeration value="application/mathematica"/>
      <xsd:enumeration value="application/mbox"/>
      <xsd:enumeration value="application/mikey"/>
      <xsd:enumeration value="application/mpeg4-generic"/>
      <xsd:enumeration value="application/msword"/>
      <xsd:enumeration value="application/news-message-id"/>
      <xsd:enumeration value="application/news-transmission"/>
      <xsd:enumeration value="application/ocsp-request"/>
      <xsd:enumeration value="application/ocsp-response"/>
      <xsd:enumeration value="application/octet-stream"/>
      <xsd:enumeration value="application/oda"/>
      <xsd:enumeration value="application/ogg"/>
      <xsd:enumeration value="application/parityfec"/>
      <xsd:enumeration value="application/pdf"/>
      <xsd:enumeration value="application/pgp-encrypted"/>
      <xsd:enumeration value="application/pgp-keys"/>
      <xsd:enumeration value="application/pgp-signature"/>
      <xsd:enumeration value="application/pidf+xml"/>
      <xsd:enumeration value="application/pkcs10"/>
      <xsd:enumeration value="application/pkcs7-mime"/>
      <xsd:enumeration value="application/pkcs7-signature"/>
      <xsd:enumeration value="application/pkix-cert"/>
      <xsd:enumeration value="application/pkix-crl"/>
      <xsd:enumeration value="application/pkix-pkipath"/>
      <xsd:enumeration value="application/pkixcmp"/>
      <xsd:enumeration value="application/postscript"/>
      <xsd:enumeration value="application/prs.alvestrand.titrax-sheet"/>
      <xsd:enumeration value="application/prs.cww"/>
      <xsd:enumeration value="application/prs.nprend"/>
      <xsd:enumeration value="application/prs.plucker"/>
      <xsd:enumeration value="application/qsig"/>
      <xsd:enumeration value="application/rdf+xml"/>
      <xsd:enumeration value="application/reginfo+xml"/>
      <xsd:enumeration value="application/remote-printing"/>
      <xsd:enumeration value="application/resource-lists+xml"/>
      <xsd:enumeration value="application/riscos"/>
      <xsd:enumeration value="application/rls-services+xml"/>
      <xsd:enumeration value="application/rtf"/>
      <xsd:enumeration value="application/samlassertion+xml"/>
      <xsd:enumeration value="application/samlmetadata+xml"/>
      <xsd:enumeration value="application/sbml+xml"/>
      <xsd:enumeration value="application/sdp"/>
      <xsd:enumeration value="application/set-payment"/>
      <xsd:enumeration value="application/set-payment-initiation"/>
      <xsd:enumeration value="application/set-registration"/>
      <xsd:enumeration value="application/set-registration-initiation"/>
      <xsd:enumeration value="application/sgml"/>
      <xsd:enumeration value="application/sgml-open-catalog"/>
      <xsd:enumeration value="application/shf+xml"/>
      <xsd:enumeration value="application/sieve"/>
      <xsd:enumeration value="application/simple-filter+xml"/>
      <xsd:enumeration value="application/simple-message-summary"/>
      <xsd:enumeration value="application/slate"/>
      <xsd:enumeration value="application/soap+xml"/>
      <xsd:enumeration value="application/spirits-event+xml"/>
      <xsd:enumeration value="application/timestamp-query"/>
      <xsd:enumeration value="application/timestamp-reply"/>
      <xsd:enumeration value="application/tve-trigger"/>
      <xsd:enumeration value="application/vemmi"/>
      <xsd:enumeration value="application/vnd.3M.Post-it-Notes"/>
      <xsd:enumeration value="application/vnd.3gpp.pic-bw-large"/>
      <xsd:enumeration value="application/vnd.3gpp.pic-bw-small"/>
      <xsd:enumeration value="application/vnd.3gpp.pic-bw-var"/>
      <xsd:enumeration value="application/vnd.3gpp.sms"/>
      <xsd:enumeration value="application/vnd.FloGraphIt"/>
      <xsd:enumeration value="application/vnd.Kinar"/>
      <xsd:enumeration value="application/vnd.Mobius.DAF"/>
      <xsd:enumeration value="application/vnd.Mobius.DIS"/>
      <xsd:enumeration value="application/vnd.Mobius.MBK"/>
      <xsd:enumeration value="application/vnd.Mobius.MQY"/>
      <xsd:enumeration value="application/vnd.Mobius.MSL"/>
      <xsd:enumeration value="application/vnd.Mobius.PLC"/>
      <xsd:enumeration value="application/vnd.Mobius.TXF"/>
      <xsd:enumeration value="application/vnd.Quark.QuarkXPress"/>
      <xsd:enumeration value="application/vnd.RenLearn.rlprint"/>
      <xsd:enumeration value="application/vnd.accpac.simply.aso"/>
      <xsd:enumeration value="application/vnd.accpac.simply.imp"/>
      <xsd:enumeration value="application/vnd.acucobol"/>
      <xsd:enumeration value="application/vnd.acucorp"/>
      <xsd:enumeration value="application/vnd.adobe.xfdf"/>
      <xsd:enumeration value="application/vnd.aether.imp"/>
      <xsd:enumeration value="application/vnd.amiga.ami"/>
      <xsd:enumeration value="application/vnd.anser-web-certificate-issue-initiation"/>
      <xsd:enumeration value="application/vnd.anser-web-funds-transfer-initiation"/>
      <xsd:enumeration value="application/vnd.audiograph"/>
      <xsd:enumeration value="application/vnd.blueice.multipass"/>
      <xsd:enumeration value="application/vnd.bmi"/>
      <xsd:enumeration value="application/vnd.businessobjects"/>
      <xsd:enumeration value="application/vnd.canon-cpdl"/>
      <xsd:enumeration value="application/vnd.canon-lips"/>
      <xsd:enumeration value="application/vnd.cinderella"/>
      <xsd:enumeration value="application/vnd.claymore"/>
      <xsd:enumeration value="application/vnd.commerce-battelle"/>
      <xsd:enumeration value="application/vnd.commonspace"/>
      <xsd:enumeration value="application/vnd.contact.cmsg"/>
      <xsd:enumeration value="application/vnd.cosmocaller"/>
      <xsd:enumeration value="application/vnd.criticaltools.wbs+xml"/>
      <xsd:enumeration value="application/vnd.ctc-posml"/>
      <xsd:enumeration value="application/vnd.cups-postscript"/>
      <xsd:enumeration value="application/vnd.cups-raster"/>
      <xsd:enumeration value="application/vnd.cups-raw"/>
      <xsd:enumeration value="application/vnd.curl"/>
      <xsd:enumeration value="application/vnd.cybank"/>
      <xsd:enumeration value="application/vnd.data-vision.rdz"/>
      <xsd:enumeration value="application/vnd.dna"/>
      <xsd:enumeration value="application/vnd.dpgraph"/>
      <xsd:enumeration value="application/vnd.dreamfactory"/>
      <xsd:enumeration value="application/vnd.dxr"/>
      <xsd:enumeration value="application/vnd.ecdis-update"/>
      <xsd:enumeration value="application/vnd.ecowin.chart"/>
      <xsd:enumeration value="application/vnd.ecowin.filerequest"/>
      <xsd:enumeration value="application/vnd.ecowin.fileupdate"/>
      <xsd:enumeration value="application/vnd.ecowin.series"/>
      <xsd:enumeration value="application/vnd.ecowin.seriesrequest"/>
      <xsd:enumeration value="application/vnd.ecowin.seriesupdate"/>
      <xsd:enumeration value="application/vnd.enliven"/>
      <xsd:enumeration value="application/vnd.epson.esf"/>
      <xsd:enumeration value="application/vnd.epson.msf"/>
      <xsd:enumeration value="application/vnd.epson.quickanime"/>
      <xsd:enumeration value="application/vnd.epson.salt"/>
      <xsd:enumeration value="application/vnd.epson.ssf"/>
      <xsd:enumeration value="application/vnd.ericsson.quickcall"/>
      <xsd:enumeration value="application/vnd.eudora.data"/>
      <xsd:enumeration value="application/vnd.fdf"/>
      <xsd:enumeration value="application/vnd.ffsns"/>
      <xsd:enumeration value="application/vnd.fints"/>
      <xsd:enumeration value="application/vnd.framemaker"/>
      <xsd:enumeration value="application/vnd.fsc.weblaunch"/>
      <xsd:enumeration value="application/vnd.fujitsu.oasys"/>
      <xsd:enumeration value="application/vnd.fujitsu.oasys2"/>
      <xsd:enumeration value="application/vnd.fujitsu.oasys3"/>
      <xsd:enumeration value="application/vnd.fujitsu.oasysgp"/>
      <xsd:enumeration value="application/vnd.fujitsu.oasysprs"/>
      <xsd:enumeration value="application/vnd.fujixerox.ddd"/>
      <xsd:enumeration value="application/vnd.fujixerox.docuworks"/>
      <xsd:enumeration value="application/vnd.fujixerox.docuworks.binder"/>
      <xsd:enumeration value="application/vnd.fut-misnet"/>
      <xsd:enumeration value="application/vnd.genomatix.tuxedo"/>
      <xsd:enumeration value="application/vnd.grafeq"/>
      <xsd:enumeration value="application/vnd.groove-account"/>
      <xsd:enumeration value="application/vnd.groove-help"/>
      <xsd:enumeration value="application/vnd.groove-identity-message"/>
      <xsd:enumeration value="application/vnd.groove-injector"/>
      <xsd:enumeration value="application/vnd.groove-tool-message"/>
      <xsd:enumeration value="application/vnd.groove-tool-template"/>
      <xsd:enumeration value="application/vnd.groove-vcard"/>
      <xsd:enumeration value="application/vnd.hbci"/>
      <xsd:enumeration value="application/vnd.hcl-bireports"/>
      <xsd:enumeration value="application/vnd.hhe.lesson-player"/>
      <xsd:enumeration value="application/vnd.hp-HPGL"/>
      <xsd:enumeration value="application/vnd.hp-PCL"/>
      <xsd:enumeration value="application/vnd.hp-PCLXL"/>
      <xsd:enumeration value="application/vnd.hp-hpid"/>
      <xsd:enumeration value="application/vnd.hp-hps"/>
      <xsd:enumeration value="application/vnd.httphone"/>
      <xsd:enumeration value="application/vnd.hzn-3d-crossword"/>
      <xsd:enumeration value="application/vnd.ibm.MiniPay"/>
      <xsd:enumeration value="application/vnd.ibm.afplinedata"/>
      <xsd:enumeration value="application/vnd.ibm.electronic-media"/>
      <xsd:enumeration value="application/vnd.ibm.modcap"/>
      <xsd:enumeration value="application/vnd.ibm.rights-management"/>
      <xsd:enumeration value="application/vnd.ibm.secure-container"/>
      <xsd:enumeration value="application/vnd.informix-visionary"/>
      <xsd:enumeration value="application/vnd.intercon.formnet"/>
      <xsd:enumeration value="application/vnd.intertrust.digibox"/>
      <xsd:enumeration value="application/vnd.intertrust.nncp"/>
      <xsd:enumeration value="application/vnd.intu.qbo"/>
      <xsd:enumeration value="application/vnd.intu.qfx"/>
      <xsd:enumeration value="application/vnd.ipunplugged.rcprofile"/>
      <xsd:enumeration value="application/vnd.irepository.package+xml"/>
      <xsd:enumeration value="application/vnd.is-xpr"/>
      <xsd:enumeration value="application/vnd.japannet-directory-service"/>
      <xsd:enumeration value="application/vnd.japannet-jpnstore-wakeup"/>
      <xsd:enumeration value="application/vnd.japannet-payment-wakeup"/>
      <xsd:enumeration value="application/vnd.japannet-registration"/>
      <xsd:enumeration value="application/vnd.japannet-registration-wakeup"/>
      <xsd:enumeration value="application/vnd.japannet-setstore-wakeup"/>
      <xsd:enumeration value="application/vnd.japannet-verification"/>
      <xsd:enumeration value="application/vnd.japannet-verification-wakeup"/>
      <xsd:enumeration value="application/vnd.jisp"/>
      <xsd:enumeration value="application/vnd.kde.karbon"/>
      <xsd:enumeration value="application/vnd.kde.kchart"/>
      <xsd:enumeration value="application/vnd.kde.kformula"/>
      <xsd:enumeration value="application/vnd.kde.kivio"/>
      <xsd:enumeration value="application/vnd.kde.kontour"/>
      <xsd:enumeration value="application/vnd.kde.kpresenter"/>
      <xsd:enumeration value="application/vnd.kde.kspread"/>
      <xsd:enumeration value="application/vnd.kde.kword"/>
      <xsd:enumeration value="application/vnd.kenameaapp"/>
      <xsd:enumeration value="application/vnd.kidspiration"/>
      <xsd:enumeration value="application/vnd.koan"/>
      <xsd:enumeration value="application/vnd.liberty-request+xml"/>
      <xsd:enumeration value="application/vnd.llamagraphics.life-balance.desktop"/>
      <xsd:enumeration value="application/vnd.llamagraphics.life-balance.exchange+xml"/>
      <xsd:enumeration value="application/vnd.lotus-1-2-3"/>
      <xsd:enumeration value="application/vnd.lotus-approach"/>
      <xsd:enumeration value="application/vnd.lotus-freelance"/>
      <xsd:enumeration value="application/vnd.lotus-notes"/>
      <xsd:enumeration value="application/vnd.lotus-organizer"/>
      <xsd:enumeration value="application/vnd.lotus-screencam"/>
      <xsd:enumeration value="application/vnd.lotus-wordpro"/>
      <xsd:enumeration value="application/vnd.mcd"/>
      <xsd:enumeration value="application/vnd.mediastation.cdkey"/>
      <xsd:enumeration value="application/vnd.meridian-slingshot"/>
      <xsd:enumeration value="application/vnd.mfmp"/>
      <xsd:enumeration value="application/vnd.micrografx.flo"/>
      <xsd:enumeration value="application/vnd.micrografx.igx"/>
      <xsd:enumeration value="application/vnd.mif"/>
      <xsd:enumeration value="application/vnd.minisoft-hp3000-save"/>
      <xsd:enumeration value="application/vnd.mitsubishi.misty-guard.trustweb"/>
      <xsd:enumeration value="application/vnd.mophun.application"/>
      <xsd:enumeration value="application/vnd.mophun.certificate"/>
      <xsd:enumeration value="application/vnd.motorola.flexsuite"/>
      <xsd:enumeration value="application/vnd.motorola.flexsuite.adsi"/>
      <xsd:enumeration value="application/vnd.motorola.flexsuite.fis"/>
      <xsd:enumeration value="application/vnd.motorola.flexsuite.gotap"/>
      <xsd:enumeration value="application/vnd.motorola.flexsuite.kmr"/>
      <xsd:enumeration value="application/vnd.motorola.flexsuite.ttc"/>
      <xsd:enumeration value="application/vnd.motorola.flexsuite.wem"/>
      <xsd:enumeration value="application/vnd.mozilla.xul+xml"/>
      <xsd:enumeration value="application/vnd.ms-artgalry"/>
      <xsd:enumeration value="application/vnd.ms-asf"/>
      <xsd:enumeration value="application/vnd.ms-excel"/>
      <xsd:enumeration value="application/vnd.ms-lrm"/>
      <xsd:enumeration value="application/vnd.ms-powerpoint"/>
      <xsd:enumeration value="application/vnd.ms-project"/>
      <xsd:enumeration value="application/vnd.ms-tnef"/>
      <xsd:enumeration value="application/vnd.ms-works"/>
      <xsd:enumeration value="application/vnd.ms-wpl"/>
      <xsd:enumeration value="application/vnd.mseq"/>
      <xsd:enumeration value="application/vnd.msign"/>
      <xsd:enumeration value="application/vnd.music-niff"/>
      <xsd:enumeration value="application/vnd.musician"/>
      <xsd:enumeration value="application/vnd.nervana"/>
      <xsd:enumeration value="application/vnd.netfpx"/>
      <xsd:enumeration value="application/vnd.noblenet-directory"/>
      <xsd:enumeration value="application/vnd.noblenet-sealer"/>
      <xsd:enumeration value="application/vnd.noblenet-web"/>
      <xsd:enumeration value="application/vnd.nokia.landmark+wbxml"/>
      <xsd:enumeration value="application/vnd.nokia.landmark+xml"/>
      <xsd:enumeration value="application/vnd.nokia.landmarkcollection+xml"/>
      <xsd:enumeration value="application/vnd.nokia.radio-preset"/>
      <xsd:enumeration value="application/vnd.nokia.radio-presets"/>
      <xsd:enumeration value="application/vnd.novadigm.EDM"/>
      <xsd:enumeration value="application/vnd.novadigm.EDX"/>
      <xsd:enumeration value="application/vnd.novadigm.EXT"/>
      <xsd:enumeration value="application/vnd.obn"/>
      <xsd:enumeration value="application/vnd.omads-email+xml"/>
      <xsd:enumeration value="application/vnd.omads-file+xml"/>
      <xsd:enumeration value="application/vnd.omads-folder+xml"/>
      <xsd:enumeration value="application/vnd.osa.netdeploy"/>
      <xsd:enumeration value="application/vnd.palm"/>
      <xsd:enumeration value="application/vnd.paos.xml"/>
      <xsd:enumeration value="application/vnd.pg.format"/>
      <xsd:enumeration value="application/vnd.pg.osasli"/>
      <xsd:enumeration value="application/vnd.picsel"/>
      <xsd:enumeration value="application/vnd.powerbuilder6"/>
      <xsd:enumeration value="application/vnd.powerbuilder6-s"/>
      <xsd:enumeration value="application/vnd.powerbuilder7"/>
      <xsd:enumeration value="application/vnd.powerbuilder7-s"/>
      <xsd:enumeration value="application/vnd.powerbuilder75"/>
      <xsd:enumeration value="application/vnd.powerbuilder75-s"/>
      <xsd:enumeration value="application/vnd.previewsystems.box"/>
      <xsd:enumeration value="application/vnd.publishare-delta-tree"/>
      <xsd:enumeration value="application/vnd.pvi.ptid1"/>
      <xsd:enumeration value="application/vnd.pwg-multiplexed"/>
      <xsd:enumeration value="application/vnd.pwg-xhtml-print+xml"/>
      <xsd:enumeration value="application/vnd.rapid"/>
      <xsd:enumeration value="application/vnd.s3sms"/>
      <xsd:enumeration value="application/vnd.sealed.doc"/>
      <xsd:enumeration value="application/vnd.sealed.eml"/>
      <xsd:enumeration value="application/vnd.sealed.mht"/>
      <xsd:enumeration value="application/vnd.sealed.net"/>
      <xsd:enumeration value="application/vnd.sealed.ppt"/>
      <xsd:enumeration value="application/vnd.sealed.xls"/>
      <xsd:enumeration value="application/vnd.sealedmedia.softseal.html"/>
      <xsd:enumeration value="application/vnd.sealedmedia.softseal.pdf"/>
      <xsd:enumeration value="application/vnd.seemail"/>
      <xsd:enumeration value="application/vnd.shana.informed.formdata"/>
      <xsd:enumeration value="application/vnd.shana.informed.formtemplate"/>
      <xsd:enumeration value="application/vnd.shana.informed.interchange"/>
      <xsd:enumeration value="application/vnd.shana.informed.package"/>
      <xsd:enumeration value="application/vnd.smaf"/>
      <xsd:enumeration value="application/vnd.sss-cod"/>
      <xsd:enumeration value="application/vnd.sss-dtf"/>
      <xsd:enumeration value="application/vnd.sss-ntf"/>
      <xsd:enumeration value="application/vnd.street-stream"/>
      <xsd:enumeration value="application/vnd.sus-calendar"/>
      <xsd:enumeration value="application/vnd.svd"/>
      <xsd:enumeration value="application/vnd.swiftview-ics"/>
      <xsd:enumeration value="application/vnd.syncml.+xml"/>
      <xsd:enumeration value="application/vnd.syncml.ds.notification"/>
      <xsd:enumeration value="application/vnd.triscape.mxs"/>
      <xsd:enumeration value="application/vnd.trueapp"/>
      <xsd:enumeration value="application/vnd.truedoc"/>
      <xsd:enumeration value="application/vnd.ufdl"/>
      <xsd:enumeration value="application/vnd.uiq.theme"/>
      <xsd:enumeration value="application/vnd.uplanet.alert"/>
      <xsd:enumeration value="application/vnd.uplanet.alert-wbxml"/>
      <xsd:enumeration value="application/vnd.uplanet.bearer-choice"/>
      <xsd:enumeration value="application/vnd.uplanet.bearer-choice-wbxml"/>
      <xsd:enumeration value="application/vnd.uplanet.cacheop"/>
      <xsd:enumeration value="application/vnd.uplanet.cacheop-wbxml"/>
      <xsd:enumeration value="application/vnd.uplanet.channel"/>
      <xsd:enumeration value="application/vnd.uplanet.channel-wbxml"/>
      <xsd:enumeration value="application/vnd.uplanet.list"/>
      <xsd:enumeration value="application/vnd.uplanet.list-wbxml"/>
      <xsd:enumeration value="application/vnd.uplanet.listcmd"/>
      <xsd:enumeration value="application/vnd.uplanet.listcmd-wbxml"/>
      <xsd:enumeration value="application/vnd.uplanet.signal"/>
      <xsd:enumeration value="application/vnd.vcx"/>
      <xsd:enumeration value="application/vnd.vectorworks"/>
      <xsd:enumeration value="application/vnd.vidsoft.vidconference"/>
      <xsd:enumeration value="application/vnd.visio"/>
      <xsd:enumeration value="application/vnd.visionary"/>
      <xsd:enumeration value="application/vnd.vividence.scriptfile"/>
      <xsd:enumeration value="application/vnd.vsf"/>
      <xsd:enumeration value="application/vnd.wap.sic"/>
      <xsd:enumeration value="application/vnd.wap.slc"/>
      <xsd:enumeration value="application/vnd.wap.wbxml"/>
      <xsd:enumeration value="application/vnd.wap.wmlc"/>
      <xsd:enumeration value="application/vnd.wap.wmlscriptc"/>
      <xsd:enumeration value="application/vnd.webturbo"/>
      <xsd:enumeration value="application/vnd.wordperfect"/>
      <xsd:enumeration value="application/vnd.wqd"/>
      <xsd:enumeration value="application/vnd.wrq-hp3000-labelled"/>
      <xsd:enumeration value="application/vnd.wt.stf"/>
      <xsd:enumeration value="application/vnd.wv.csp+wbxml"/>
      <xsd:enumeration value="application/vnd.wv.csp+xml"/>
      <xsd:enumeration value="application/vnd.wv.ssp+xml"/>
      <xsd:enumeration value="application/vnd.xara"/>
      <xsd:enumeration value="application/vnd.xfdl"/>
      <xsd:enumeration value="application/vnd.yamaha.hv-dic"/>
      <xsd:enumeration value="application/vnd.yamaha.hv-script"/>
      <xsd:enumeration value="application/vnd.yamaha.hv-voice"/>
      <xsd:enumeration value="application/vnd.yamaha.smaf-audio"/>
      <xsd:enumeration value="application/vnd.yamaha.smaf-phrase"/>
      <xsd:enumeration value="application/vnd.yellowriver-custom-menu"/>
      <xsd:enumeration value="application/watcherinfo+xml"/>
      <xsd:enumeration value="application/whoispp-query"/>
      <xsd:enumeration value="application/whoispp-response"/>
      <xsd:enumeration value="application/wita"/>
      <xsd:enumeration value="application/wordperfect5.1"/>
      <xsd:enumeration value="application/x400-bp"/>
      <xsd:enumeration value="application/xhtml+xml"/>
      <xsd:enumeration value="application/xml"/>
      <xsd:enumeration value="application/xml-dtd"/>
      <xsd:enumeration value="application/xml-external-parsed-entity"/>
      <xsd:enumeration value="application/xmpp+xml"/>
      <xsd:enumeration value="application/xop+xml"/>
      <xsd:enumeration value="application/zip"/>
      <xsd:enumeration value="audio/32kadpcm"/>
      <xsd:enumeration value="audio/3gpp"/>
      <xsd:enumeration value="audio/AMR"/>
      <xsd:enumeration value="audio/AMR-WB"/>
      <xsd:enumeration value="audio/BV16"/>
      <xsd:enumeration value="audio/BV32"/>
      <xsd:enumeration value="audio/CN"/>
      <xsd:enumeration value="audio/DAT12"/>
      <xsd:enumeration value="audio/DVI4"/>
      <xsd:enumeration value="audio/EVRC"/>
      <xsd:enumeration value="audio/EVRC-QCP"/>
      <xsd:enumeration value="audio/EVRC0"/>
      <xsd:enumeration value="audio/G.722.1"/>
      <xsd:enumeration value="audio/G722"/>
      <xsd:enumeration value="audio/G723"/>
      <xsd:enumeration value="audio/G726-16"/>
      <xsd:enumeration value="audio/G726-24"/>
      <xsd:enumeration value="audio/G726-32"/>
      <xsd:enumeration value="audio/G726-40"/>
      <xsd:enumeration value="audio/G728"/>
      <xsd:enumeration value="audio/G729"/>
      <xsd:enumeration value="audio/G729D"/>
      <xsd:enumeration value="audio/G729E"/>
      <xsd:enumeration value="audio/GSM"/>
      <xsd:enumeration value="audio/GSM-EFR"/>
      <xsd:enumeration value="audio/L16"/>
      <xsd:enumeration value="audio/L20"/>
      <xsd:enumeration value="audio/L24"/>
      <xsd:enumeration value="audio/L8"/>
      <xsd:enumeration value="audio/LPC"/>
      <xsd:enumeration value="audio/MP4A-LATM"/>
      <xsd:enumeration value="audio/MPA"/>
      <xsd:enumeration value="audio/PCMA"/>
      <xsd:enumeration value="audio/PCMU"/>
      <xsd:enumeration value="audio/QCELP"/>
      <xsd:enumeration value="audio/RED"/>
      <xsd:enumeration value="audio/SMV"/>
      <xsd:enumeration value="audio/SMV-QCP"/>
      <xsd:enumeration value="audio/SMV0"/>
      <xsd:enumeration value="audio/VDVI"/>
      <xsd:enumeration value="audio/basic"/>
      <xsd:enumeration value="audio/clearmode"/>
      <xsd:enumeration value="audio/dsr-es201108"/>
      <xsd:enumeration value="audio/dsr-es202050"/>
      <xsd:enumeration value="audio/dsr-es202211"/>
      <xsd:enumeration value="audio/dsr-es202212"/>
      <xsd:enumeration value="audio/iLBC"/>
      <xsd:enumeration value="audio/mpa-robust"/>
      <xsd:enumeration value="audio/mpeg"/>
      <xsd:enumeration value="audio/mpeg4-generic"/>
      <xsd:enumeration value="audio/parityfec"/>
      <xsd:enumeration value="audio/prs.sid"/>
      <xsd:enumeration value="audio/telephone-event"/>
      <xsd:enumeration value="audio/tone"/>
      <xsd:enumeration value="audio/vnd.3gpp.iufp"/>
      <xsd:enumeration value="audio/vnd.audiokoz"/>
      <xsd:enumeration value="audio/vnd.cisco.nse"/>
      <xsd:enumeration value="audio/vnd.cns.anp1"/>
      <xsd:enumeration value="audio/vnd.cns.inf1"/>
      <xsd:enumeration value="audio/vnd.digital-winds"/>
      <xsd:enumeration value="audio/vnd.everad.plj"/>
      <xsd:enumeration value="audio/vnd.lucent.voice"/>
      <xsd:enumeration value="audio/vnd.nokia.mobile-xmf"/>
      <xsd:enumeration value="audio/vnd.nortel.vbk"/>
      <xsd:enumeration value="audio/vnd.nuera.ecelp4800"/>
      <xsd:enumeration value="audio/vnd.nuera.ecelp7470"/>
      <xsd:enumeration value="audio/vnd.nuera.ecelp9600"/>
      <xsd:enumeration value="audio/vnd.octel.sbc"/>
      <xsd:enumeration value="audio/vnd.qcelp"/>
      <xsd:enumeration value="audio/vnd.rhetorex.32kadpcm"/>
      <xsd:enumeration value="audio/vnd.sealedmedia.softseal.mpeg"/>
      <xsd:enumeration value="audio/vnd.vmx.cvsd"/>
      <xsd:enumeration value="image/cgm"/>
      <xsd:enumeration value="image/fits"/>
      <xsd:enumeration value="image/g3fax"/>
      <xsd:enumeration value="image/gif"/>
      <xsd:enumeration value="image/ief"/>
      <xsd:enumeration value="image/jp2"/>
      <xsd:enumeration value="image/jpeg"/>
      <xsd:enumeration value="image/jpm"/>
      <xsd:enumeration value="image/jpx"/>
      <xsd:enumeration value="image/naplps"/>
      <xsd:enumeration value="image/png"/>
      <xsd:enumeration value="image/prs.btif"/>
      <xsd:enumeration value="image/prs.pti"/>
      <xsd:enumeration value="image/t38"/>
      <xsd:enumeration value="image/tiff"/>
      <xsd:enumeration value="image/tiff-fx"/>
      <xsd:enumeration value="image/vnd.cns.inf2"/>
      <xsd:enumeration value="image/vnd.djvu"/>
      <xsd:enumeration value="image/vnd.dwg"/>
      <xsd:enumeration value="image/vnd.dxf"/>
      <xsd:enumeration value="image/vnd.fastbidsheet"/>
      <xsd:enumeration value="image/vnd.fpx"/>
      <xsd:enumeration value="image/vnd.fst"/>
      <xsd:enumeration value="image/vnd.fujixerox.edmics-mmr"/>
      <xsd:enumeration value="image/vnd.fujixerox.edmics-rlc"/>
      <xsd:enumeration value="image/vnd.globalgraphics.pgb"/>
      <xsd:enumeration value="image/vnd.microsoft.icon"/>
      <xsd:enumeration value="image/vnd.mix"/>
      <xsd:enumeration value="image/vnd.ms-modi"/>
      <xsd:enumeration value="image/vnd.net-fpx"/>
      <xsd:enumeration value="image/vnd.sealed.png"/>
      <xsd:enumeration value="image/vnd.sealedmedia.softseal.gif"/>
      <xsd:enumeration value="image/vnd.sealedmedia.softseal.jpg"/>
      <xsd:enumeration value="image/vnd.svf"/>
      <xsd:enumeration value="image/vnd.wap.wbmp"/>
      <xsd:enumeration value="image/vnd.xiff"/>
      <xsd:enumeration value="message/CPIM"/>
      <xsd:enumeration value="message/delivery-status"/>
      <xsd:enumeration value="message/disposition-notification"/>
      <xsd:enumeration value="message/external-body"/>
      <xsd:enumeration value="message/http"/>
      <xsd:enumeration value="message/news"/>
      <xsd:enumeration value="message/partial"/>
      <xsd:enumeration value="message/rfc822"/>
      <xsd:enumeration value="message/s-http"/>
      <xsd:enumeration value="message/sip"/>
      <xsd:enumeration value="message/sipfrag"/>
      <xsd:enumeration value="message/tracking-status"/>
      <xsd:enumeration value="model/iges"/>
      <xsd:enumeration value="model/mesh"/>
      <xsd:enumeration value="model/vnd.dwf"/>
      <xsd:enumeration value="model/vnd.flatland.3dml"/>
      <xsd:enumeration value="model/vnd.gdl"/>
      <xsd:enumeration value="model/vnd.gs-gdl"/>
      <xsd:enumeration value="model/vnd.gtw"/>
      <xsd:enumeration value="model/vnd.mts"/>
      <xsd:enumeration value="model/vnd.parasolid.transmit.binary"/>
      <xsd:enumeration value="model/vnd.parasolid.transmit.text"/>
      <xsd:enumeration value="model/vnd.vtu"/>
      <xsd:enumeration value="model/vrml"/>
      <xsd:enumeration value="multipart/alternative"/>
      <xsd:enumeration value="multipart/appledouble"/>
      <xsd:enumeration value="multipart/byteranges"/>
      <xsd:enumeration value="multipart/digest"/>
      <xsd:enumeration value="multipart/encrypted"/>
      <xsd:enumeration value="multipart/form-data"/>
      <xsd:enumeration value="multipart/header-set"/>
      <xsd:enumeration value="multipart/mixed"/>
      <xsd:enumeration value="multipart/parallel"/>
      <xsd:enumeration value="multipart/related"/>
      <xsd:enumeration value="multipart/report"/>
      <xsd:enumeration value="multipart/signed"/>
      <xsd:enumeration value="multipart/voice-message"/>
      <xsd:enumeration value="text/RED"/>
      <xsd:enumeration value="text/calendar"/>
      <xsd:enumeration value="text/css"/>
      <xsd:enumeration value="text/csv"/>
      <xsd:enumeration value="text/directory"/>
      <xsd:enumeration value="text/dns"/>
      <xsd:enumeration value="text/enriched"/>
      <xsd:enumeration value="text/html"/>
      <xsd:enumeration value="text/parityfec"/>
      <xsd:enumeration value="text/plain"/>
      <xsd:enumeration value="text/prs.fallenstein.rst"/>
      <xsd:enumeration value="text/prs.lines.tag"/>
      <xsd:enumeration value="text/rfc822-headers"/>
      <xsd:enumeration value="text/richtext"/>
      <xsd:enumeration value="text/rtf"/>
      <xsd:enumeration value="text/sgml"/>
      <xsd:enumeration value="text/t140"/>
      <xsd:enumeration value="text/tab-separated-values"/>
      <xsd:enumeration value="text/troff"/>
      <xsd:enumeration value="text/uri-list"/>
      <xsd:enumeration value="text/vnd.DMClientScript"/>
      <xsd:enumeration value="text/vnd.IPTC.NITF"/>
      <xsd:enumeration value="text/vnd.IPTC.NewsML"/>
      <xsd:enumeration value="text/vnd.abc"/>
      <xsd:enumeration value="text/vnd.curl"/>
      <xsd:enumeration value="text/vnd.esmertec.theme-descriptor"/>
      <xsd:enumeration value="text/vnd.fly"/>
      <xsd:enumeration value="text/vnd.fmi.flexstor"/>
      <xsd:enumeration value="text/vnd.in3d.3dml"/>
      <xsd:enumeration value="text/vnd.in3d.spot"/>
      <xsd:enumeration value="text/vnd.latex-z"/>
      <xsd:enumeration value="text/vnd.motorola.reflex"/>
      <xsd:enumeration value="text/vnd.ms-mediapackage"/>
      <xsd:enumeration value="text/vnd.net2phone.commcenter.command"/>
      <xsd:enumeration value="text/vnd.sun.j2me.app-descriptor"/>
      <xsd:enumeration value="text/vnd.wap.si"/>
      <xsd:enumeration value="text/vnd.wap.sl"/>
      <xsd:enumeration value="text/vnd.wap.wml"/>
      <xsd:enumeration value="text/vnd.wap.wmlscript"/>
      <xsd:enumeration value="text/xml"/>
      <xsd:enumeration value="text/xml-external-parsed-entity"/>
      <xsd:enumeration value="video/3gpp"/>
      <xsd:enumeration value="video/BMPEG"/>
      <xsd:enumeration value="video/BT656"/>
      <xsd:enumeration value="video/CelB"/>
      <xsd:enumeration value="video/DV"/>
      <xsd:enumeration value="video/H261"/>
      <xsd:enumeration value="video/H263"/>
      <xsd:enumeration value="video/H263-1998"/>
      <xsd:enumeration value="video/H263-2000"/>
      <xsd:enumeration value="video/H264"/>
      <xsd:enumeration value="video/JPEG"/>
      <xsd:enumeration value="video/MJ2"/>
      <xsd:enumeration value="video/MP1S"/>
      <xsd:enumeration value="video/MP2P"/>
      <xsd:enumeration value="video/MP2T"/>
      <xsd:enumeration value="video/MP4V-ES"/>
      <xsd:enumeration value="video/MPV"/>
      <xsd:enumeration value="video/SMPTE292M"/>
      <xsd:enumeration value="video/mpeg"/>
      <xsd:enumeration value="video/mpeg4-generic"/>
      <xsd:enumeration value="video/nv"/>
      <xsd:enumeration value="video/parityfec"/>
      <xsd:enumeration value="video/pointer"/>
      <xsd:enumeration value="video/quicktime"/>
      <xsd:enumeration value="video/raw"/>
      <xsd:enumeration value="video/vnd.fvt"/>
      <xsd:enumeration value="video/vnd.motorola.video"/>
      <xsd:enumeration value="video/vnd.motorola.videop"/>
      <xsd:enumeration value="video/vnd.mpegurl"/>
      <xsd:enumeration value="video/vnd.nokia.interleaved-multimedia"/>
      <xsd:enumeration value="video/vnd.objectvideo"/>
      <xsd:enumeration value="video/vnd.sealed.mpeg1"/>
      <xsd:enumeration value="video/vnd.sealed.mpeg4"/>
      <xsd:enumeration value="video/vnd.sealed.swf"/>
      <xsd:enumeration value="video/vnd.sealedmedia.softseal.mov"/>
      <xsd:enumeration value="video/vnd.vivo"/>
		</xsd:restriction>
	</xsd:simpleType>
</xsd:schema>
