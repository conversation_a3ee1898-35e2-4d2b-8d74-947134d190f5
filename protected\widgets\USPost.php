<?php

class USPost extends CWidget {

    public $id;
    public $route;
    public $params = [];
    public $dynamicParams = [];
    public $type;
    public $label = null;
    public $name = 'id';
    public $value = null;
    public $items;
    public $view;
    public $index;
    public $visible = true;
    public $disabled = false;
    //Private
    private $controller;
    public $aux_id;
    private $url;

    //
    const TYPE_DROPDOWN = 'dropdown';
    const TYPE_AUTOCOMPLETE = 'autocomplete';

    /**
     * Initializes the widget.
     */
    public function init() {

        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->aux_id = isset($this->aux_id) ? $this->aux_id : $this->controller->getServerId();
        $this->index = isset($this->index) ? $this->index : 0;
        //Private
        $this->disabled = $this->disabled || !(isset($this->route) ? $this->controller->checkRoute($this->route) : true);
        $this->url = Yii::app()->controller->createUrl($this->route, $this->params);

        Yii::app()->clientScript->registerScript('USPost', " 
        function USPostGo(url, id, aux_id){
            var value = $('#' + id).val();
            var dynamicParams = $('#' + id).data('dynamicParams');
            
            if(value == '')
            {
                bootbox.alert(us_message('Primero seleccione un ítem!', 'warning'));
                $('#' + aux_id).focus();
                return false;
            }
            
            url = buildUrl(url, 'id', value);
            
            $.each(dynamicParams, function(key, value) {
                url = buildUrl(url, key, value);
            });
            
            window.location.href = url; 
        }           
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div index='{$this->index}' class='us-post row' style='display:" . ($this->visible ? 'block' : 'none') . "'>";
        switch ($this->type) {
            case self::TYPE_DROPDOWN:
                echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
                echo CHtml::label($this->label, $this->id, array('class' => 'us-label'));
                echo "</div>";
                echo "<div class='col-lg-8 col-md-8 col-sm-8 col-xs-12'>";
                echo CHtml::dropDownList($this->name, $this->value, $this->items, array(
                    'id' => $this->id,
                    'disabled' => $this->disabled,
                ));
                echo "</div>";
                break;
            case self::TYPE_AUTOCOMPLETE:
                echo "<div class='col-lg-10 col-md-10 col-sm-10 col-xs-12'>";
                echo $this->widget('application.widgets.USAutocomplete', array(
                    'value_id' => $this->id,
                    'aux_id' => $this->aux_id,
                    'label' => null,
                    'name' => $this->name,
                    'view' => $this->view,
                    'readonly' => $this->disabled,
                    'placeholder' => $this->label,
                    'useOnkeydown' => Yii::app()->controller->getOrganization()->globalVar->keyboard_search == 1 ? true : false,
                    'autoChoose' => Yii::app()->controller->getOrganization()->globalVar->keyboard_search == 1 ? false : true,
                    'onselect' => $this->normalizeDynamicParams(),
                        ), true);
                echo "</div>";
                break;
            default:
                throw new Exception("'{$this->type}' no es un tipo de Widget POST válido!");
        }
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo $this->widget('booster.widgets.TbButton', array(
            'context' => 'primary',
            'buttonType' => 'button',
            'size' => 'mini',
            'encodeLabel' => false,
            'block' => true,
            'icon' => 'plus white',
            'label' => 'Crear',
            'htmlOptions' => array(
                'disabled' => $this->disabled,
                'onclick' => "USPostGo('{$this->url}', '{$this->id}', '{$this->aux_id}')",
            )
                ), true);
        echo "</div>";
        echo "</div>";
    }

    private function normalizeDynamicParams() {
        $js = "var dynamicParams = {}\n";
        foreach ($this->dynamicParams as $dynamicParam) {
            $js .= "dynamicParams['{$dynamicParam}'] = {$dynamicParam};\n";
        }
        $js .= "$('#{$this->id}').data('dynamicParams', dynamicParams);\n";
        return $js;
    }

}
