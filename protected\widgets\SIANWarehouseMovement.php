<?php

class SIANWarehouseMovement extends CWidget {

    public $id;
    public $model;
    public $form;
    public $type_items;
    public $warehouse_items;
    public $warehouse_types = [];
    public $currency_items;
    public $readonly = false;
    public $onChangeCurrency = null;
    public $onChangeWarehouse = null;
    public $lockCurrency = true;
    public $lockWarehouse = false;
    public $warehouse_input_id;
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        //
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->warehouse_input_id = isset($this->warehouse_input_id) ? $this->warehouse_input_id : $this->controller->getServerId();

        $warehouse_id = isset($this->model->warehouse_id) ? $this->model->warehouse_id : "";

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-warehouse-movement.js');

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        //Fuera del ready para que sea lo primero que carga
        var div = $('#{$this->id}');
        div.data('store_id', {$this->model->movement->store_id});
        div.data('direction', '{$this->model->movement->scenario->direction}');
        div.data('warehouseUrl', '{$this->controller->createUrl("/movement/getWarehouses")}');
        div.data('warehouse_types', " . CJSON::encode($this->warehouse_types) . ");
        div.data('warehouse_loaded', false);
        $('#{$this->warehouse_input_id}').data('last', '{$this->model->warehouse_id}');

        //EVENTOS
        $(document).ready(function(){
            var warehouse_loaded = $('#{$this->id}').data('warehouse_loaded');
            //Si la tienda ya cargó el almacén ya no tenemos por qué volver a cargar
            if(!warehouse_loaded){
                SIANWarehouseFillWarehouses('{$this->id}', '{$this->model->warehouse_id}');
            }
        });
            
        $('#{$this->id}').data('changeStore', function(changeData){
            console.log(changeData);
            //Seteamos tienda
            $('#{$this->id}').data('store_id', changeData.store_id);

            //Obtenemos almacén

            if({$warehouse_id} === ''){
                var warehouse_id = $('#{$this->warehouse_input_id}').data('last');
                SIANWarehouseFillWarehouses('{$this->id}', warehouse_id);
            }else{
                var warehouse_id = {$this->model->warehouse_id}
                SIANWarehouseFillWarehouses('{$this->id}', warehouse_id);
            }

            //Indicamos que la tienda ya cargó el almacén
            $('#{$this->id}').data('warehouse_loaded', true);

        });    
        
        $('body').on('change', '#{$this->warehouse_input_id}', function(e) {
            
            var warehouseObj = $('#{$this->warehouse_input_id}');
            var optionObj = warehouseObj.find('option:selected');
            
            var warehouse_id = optionObj.val();
            var warehouse_name = !isBlank(warehouse_id) ? optionObj.text() : '';
            var last = warehouseObj.data('last');
            var physical = optionObj.data('physical');
            var level_quantity = optionObj.data('level_quantity');
                
            var changeData = {
                warehouse_id : isset(warehouse_id) ? warehouse_id: '',
                warehouse_name: warehouse_name,
                realChange: (last != warehouse_id),
                physical: physical,
                level_quantity: level_quantity
            };
            
            {$this->onChangeWarehouse};
            warehouseObj.data('last', warehouse_id);
                
        });
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Detalle de movimiento',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'id' => $this->id,
            )
        ));

        echo "<div class='row'>";

        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->movement, 'currency', $this->currency_items, array(
            'onchange' => $this->onChangeCurrency,
            'readonly' => $this->readonly || $this->lockCurrency
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'warehouse_id', [], array(
            'id' => $this->warehouse_input_id,
            'class' => 'sian-warehouse-movement-warehouse-id',
            'empty' => Strings::SELECT_OPTION,
            'readonly' => $this->readonly || $this->lockWarehouse,
        ));
        echo "</div>";

        echo "<div class='col-lg-6 col-md-6 col-sm-6 col-xs-12'>";
        echo $this->form->textFieldRow($this->model->movement, 'observation', array('placeholder' => 'Puede escribir una observación acerca del movimiento',
            'required' => $this->model->movement->isObservationRequired()
        ));
        echo "</div>";

        echo "</div>";

        if ($this->model->movement->scenario->auto_person_id == 1) {
            echo $this->form->hiddenField($this->model->movement, 'aux_person_id');
        }

        $this->endWidget();
    }

}
