<?php

class SIANOwnerPair extends CWidget {

    public $id;
    public $model;
    public $title = 'Objetos enlazados';
    public $viewClass = null;
    public $viewScenario = null;
    public $viewParams = null;
    public $extraViewAttributes = [];
    public $owner = null;
    public $input_name = null;
    public $property = null;
    public $modal_id = null;
    public $readonly = false;
    public $pred = true;
    public $pred_name = null;
    public $selected = false;
    public $selected_name = null;
    public $exclude_ids = [];
    public $limit = false;
    public $with_maintenance = false;
    public $has_panel = true;
    public $has_preview = true;
    public $multiple = false;
    //PRIVATE
    private $controller;
    private $ownerData = [];
    private $preview_route = null;
    private $viewAttributes = [];
    //IDS
    private $owner_input_id;
    private $owner_id_input_id;
    private $owner_name_input_id;
    private $add_button_id;
    private $delete_button_id;
    private $autocomplete_id;

    public function init() {

        //CONTROL
        if (!isset($this->owner)) {
            throw new Exception('Debe especificar el propietario');
        }

        if (!isset($this->property)) {
            throw new Exception('Debe especificar la propiedad.');
        }

        if (!isset($this->input_name)) {
            $this->input_name = $this->owner;
        }

        $this->controller = Yii::app()->controller;

        //IDS
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->autocomplete_id = $this->controller->getServerId();
        $this->owner_input_id = $this->controller->getServerId();
        $this->owner_id_input_id = $this->controller->getServerId();
        $this->owner_name_input_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();
        $this->delete_button_id = $this->controller->getServerId();
        $this->pred_name = isset($this->pred_name) ? $this->pred_name : 'Pred.?';
        $this->selected = isset($this->selected_name);

        //Obtenemos datos
        $this->ownerData = Yii::app()->db->createCommand()->select([
                    "O.owner_name",
                    "O.module",
                    "O.controller",
                    "O.action"
                ])
                ->from('owner O')
                ->where("O.owner = :owner", [
                    ':owner' => $this->owner
                ])
                ->queryRow();

        if (isset($this->ownerData['module'], $this->ownerData['controller'], $this->ownerData['action'])) {
            $this->preview_route = "/{$this->ownerData['module']}/{$this->ownerData['controller']}/{$this->ownerData['action']}";
        }

        //Recorremos los ítems
        $a_items = [];
        foreach ($this->model->{$this->property} as $item) {
            $a_attributes = [];
            $a_attributes['owner'] = $item->owner;
            $a_attributes['owner_id'] = $item->owner_id;
            $a_attributes['owner_name'] = isset($item->owner_name) ? $item->owner_name : '';
            $a_attributes['default'] = $item->default;
            $a_attributes['selected'] = $item->selected;
            $a_attributes['errors'] = $item->getAllErrors();

            $a_items[] = $a_attributes;
        }

        //Atributos extra
        $i_extra_width = 0;
        foreach ($this->extraViewAttributes as $a_extra) {
            $i_extra_width += isset($a_extra['width']) ? $a_extra['width'] : 0;
        }

        //Atributos
        $this->viewAttributes = array(
            array(
                'name' => 'pk',
                'hidden' => true,
                'not_in' => "window.sianOwnerPairOwnerIds.{$this->id}"
            ),
            array(
                'name' => 'owner',
                'hidden' => true,
                'update' => "$('#{$this->owner_input_id}').val(owner);"
            ),
            array(
                'name' => 'owner_id',
                'width' => floor((100 - $i_extra_width) * 0.1),
                'update' => "$('#{$this->owner_id_input_id}').val(owner_id);",
                'types' => array('id', 'value', 'aux')
            ),
            array(
                'name' => 'owner_name',
                'width' => floor((100 - $i_extra_width) * 0.9),
                'update' => "$('#{$this->owner_name_input_id}').val(owner_name);",
                'types' => array('text')
            ),
        );
        //Agregamos atributos extra
        foreach ($this->extraViewAttributes as $a_extra) {
            $this->viewAttributes[] = $a_extra;
        }

        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-owner-pair.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id, "

        $(document).ready(function() {                  

            //COUNT y LIMIT
            var tableObj = $('#{$this->id}');
            tableObj.data('count', 0);
            tableObj.data('preview_url', '{$this->controller->createUrl($this->preview_route)}');
            tableObj.data('access_preview', " . CJSON::encode($this->controller->checkRoute($this->preview_route)) . ");
            tableObj.data('modal_id', '{$this->modal_id}');
            tableObj.data('input_name', '{$this->input_name}');
            tableObj.data('readonly', " . CJSON::encode($this->readonly) . ");
            tableObj.data('pred', " . CJSON::encode($this->pred) . ");
            tableObj.data('selected', " . CJSON::encode($this->selected) . ");
            tableObj.data('exclude_ids', " . CJSON::encode($this->exclude_ids) . ");
            tableObj.data('limit', " . CJSON::encode($this->limit) . ");
            tableObj.data('has_preview', " . CJSON::encode($this->has_preview) . ");
            tableObj.data('multiple', " . CJSON::encode($this->multiple) . ");

            var array = " . CJSON::encode($a_items) . ";

            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {
                    SIANOwnerPairAddItem('{$this->id}', array[i]['owner'], array[i]['owner_id'], array[i]['owner_name'], array[i]['default'], array[i]['selected'], array[i]['errors']);
                }
            }
            else
            {
                tableObj.find('tbody').html('<tr><td class=\'empty\' colspan=\'10\'>" . Strings::NO_DATA . "</td></tr>');
            }
            
            SIANOwnerPairGetIds('{$this->id}');
            SIANOwnerPairSortable('{$this->id}');
            $('.sian-owner-pair-select-all, .sian-owner-pair-select').on('change', handleCheckboxChange);
        });
        
        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var tableObj = $('#{$this->id}');
            var count = tableObj.data('count');

            var owner = $('#{$this->owner_input_id}').val();
            var owner_id = $('#{$this->owner_id_input_id}').val();
            var owner_name = $('#{$this->owner_name_input_id}').val();
            var xdefault = (count == 0);
            
            if(isBlank(owner_id))
            {
                bootbox.alert(us_message('Debe elegir un(a) {$this->ownerData['owner_name']}!', 'warning'));
                USAutocompleteFocus('{$this->autocomplete_id}');
                return;
            }

            SIANOwnerPairAddItem('{$this->id}', owner, owner_id, owner_name, xdefault, false, []);
            USAutocompleteReset('{$this->autocomplete_id}');
            SIANOwnerPairGetIds('{$this->id}');
        });
        
        $('body').on('click', '#{$this->id} input.sian-owner-pair-item-default', function() {
            $('#{$this->id} input[type=radio]:checked').not(this).prop('checked', false);
        });
        
        function {$this->id}SIANOwnerPairReadonly(value)
        {            
            USAutocompleteReadOnly('{$this->autocomplete_id}', value);
            if(value){
                $('#{$this->add_button_id}').attr('disabled', true);
                SIANOwnerPairRemoveAll('{$this->id}');
            }else{
                $('#{$this->add_button_id}').removeAttr('disabled');
            }
        }

        function {$this->id}SIANAddMerchandises(owner_id,owner_name){
            var tableObj = $('#{$this->id}');
            var count = tableObj.data('count');

            var owner = '{$this->owner}';
            var xdefault = (count == 0);
            
            if(isBlank(owner_id))
            {
                bootbox.alert(us_message('Debe elegir un(a) {$this->ownerData['owner_name']}!', 'warning'));
                USAutocompleteFocus('{$this->autocomplete_id}');
                return;
            }
            SIANOwnerPairAddItem('{$this->id}', owner, owner_id, owner_name, xdefault, false, []);
            SIANOwnerPairGetIds('{$this->id}');
        }
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->has_panel) {
            $this->beginWidget('booster.widgets.TbPanel', array(
                'title' => "{$this->title}" . (isset($this->aclaration) ? ' *' : ''),
                'headerIcon' => 'list',
                'htmlOptions' => array(
                    'class' => $this->model->hasErrors($this->property) ? 'us-error' : ''
                )
            ));
        }

        echo "<div class='row'>";
        echo "<div class='col-lg-9 col-md-9 col-sm-12 col-xs-12'>";
        $a_attributes_owner = array(
            'id' => $this->autocomplete_id,
            'name' => 'OwnerChilds',
            'label' => $this->ownerData['owner_name'],
            'parent_id' => $this->modal_id,
            'view' => array(
                'model' => $this->viewClass,
                'scenario' => $this->viewScenario,
                'params' => $this->viewParams,
                'attributes' => $this->viewAttributes
            ),
            'readonly' => $this->readonly,
            'onreset' => "
                $('#{$this->owner_input_id}').val('');
                $('#{$this->owner_id_input_id}').val('');
                $('#{$this->owner_name_input_id}').val('');
            "
        );

        if ($this->with_maintenance) {
            $a_attributes_owner['maintenance'] = array(
                'module' => $this->ownerData['module'],
                'controller' => $this->ownerData['controller'],
            );
        }

        $this->widget('application.widgets.USAutocomplete', $a_attributes_owner);
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->owner_input_id,
        ));
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->owner_id_input_id,
        ));
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->owner_name_input_id,
        ));
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        echo CHtml::label('Agregar', $this->add_button_id, array(
        ));
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_button_id,
            'context' => 'primary',
            'icon' => 'fa fa-lg fa-plus white',
            'size' => 'default',
            'title' => 'Añadir',
            'block' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
        echo CHtml::label('Eliminar', $this->delete_button_id, array(
        ));
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->delete_button_id,
            'context' => 'danger',
            'icon' => 'fa fa-lg fa-trash white',
            'size' => 'default',
            'title' => 'Eliminar',
            'block' => true,
            'onclick' => "SIANOwnerPairRemoveSelected('{$this->id}');",
        ));
        echo "</div>";

        echo "</div>";
        if(Merchandise::OWNER == $this->owner){
            echo "<div class='row'>";
            $this->widget('SIANLoadProducts', array(
                'id' => $this->controller->getServerId(),
                'presentationMode' => 0,
                'colums' => 3,
                'onLoadProduct' => "
                        if(!window.sianOwnerPairOwnerIds['{$this->id}'].includes('{$this->owner}-'+ itemProduct['product_id'])){
                            {$this->id}SIANAddMerchandises(itemProduct['product_id'],itemProduct['product_name']);
                            itemsCharged++;
                        }
                    ",
                ));
            echo "</div>";
        }

        echo "<hr>";

        echo CHtml::tag('table', array(
            'id' => $this->id,
            'class' => 'table table-condensed table-hover ' . (!$this->has_panel ? ($this->model->hasErrors($this->property) ? 'us-error' : '') : ''),
                ), $this->renderTable(), true);

        if ($this->has_panel) {
            $this->endWidget();
        }
    }

    private function renderTable() {

        $o_model = new $this->viewClass($this->viewScenario);

        $html = '';
        //Según el modo
        $html .= "<thead>";
        $html .= "<tr>";
        $html .= "<th width='5%' style='padding-left:15px;'><input type='checkbox' class='sian-owner-pair-select-all'></th>";
        $html .= "<th width='5%'>#</th>";
        $html .= "<th width='65%'>{$o_model->getAttributeLabel('owner_name')}</th>";
        if ($this->pred == true) {
            $html .= "<th width='10%' style='text-align:center;'>{$this->pred_name}</th>";
        }
        if ($this->selected == true) {
            $html .= "<th width='10%' style='text-align:center;'>{$this->selected_name}</th>";
        }
        $html .= "<th width='10%' style='text-align:center;'>Opciones</th>";
        $html .= "</tr>";
        $html .= "</thead>";
        $html .= "<tbody></tbody>";
        $html .= "<tfoot>";
        $html .= "</tfoot>";

        return $html;
    }

}
