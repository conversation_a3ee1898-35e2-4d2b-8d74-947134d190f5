<?php

class SIANProductListCheckedGrid extends CWidget {

    public $id;
    public $form;
    public $model;
    public $items;
    public $checkedItems;
    public $readonly = false;
    public $ifixed = 4;
    public $tfixed = 2;
    public $step = 0.0001;
    public $show_amounts = false;
    public $show_observations = false;
    public $combinations = false;
    public $allow_duplicate_checkbox_id;
    public $observation_input_id;
    public $has_parent = false;
    //PRIVATE
    private $dataProvider;
    private $presentationMode = SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES;
    private $presentationItems = [];
    private $preview_access;
    private $local_storage_name;

    public function init() {

        //CONTROL
        if ($this->model->movement->validate_stock == 1 && !isset($this->model->movement->stock_mode)) {
            throw new Exception('Se necesita un modo de stock');
        }
        //GRID ID
        $this->id = isset($this->id) ? $this->id : Yii::app()->controller->getServerId();
        $this->allow_duplicate_checkbox_id = $this->controller->getServerId();
        $this->observation_input_id = isset($this->observation_input_id) ? $this->observation_input_id : Yii::app()->controller->getServerId();
        $this->local_storage_name = 'datos_' . $this->id;
        //CHECKED ITEMS
        $a_new_products = [];
        foreach ($this->model->tempItems as $o_item) {

            if (USString::isBlank($o_item->itemObj->parent_item_id)) {
                throw new Exception('No está seteado el campo parent_item_id para uno o más ítems que deben ir en la grilla.');
            }
            //Si vienen cargados los items (update, clone o recargar del create) y hay tipos de productos original NEW 
            if (in_array($o_item->itemObj->parent_product_type_original, [Product::TYPE_NEW])) {
                $a_attributes = [];
                $a_attributes['item_id'] = $o_item->itemObj->parent_item_id;
                $a_attributes['product_id'] = $o_item->itemObj->product_id;
                $a_attributes['product_name'] = $o_item->itemObj->product_name;
                $a_attributes['product_type'] = $o_item->itemObj->product_type;
                $a_attributes['equivalence'] = $o_item->itemObj->equivalence;
                $a_attributes['item_type_id'] = $o_item->itemObj->item_type_id;
                $a_attributes['allow_decimals'] = $o_item->itemObj->allow_decimals;
                $a_attributes['max_pres_quantity'] = $o_item->itemObj->max_pres_quantity;
                $a_attributes['max_unit_quantity'] = $o_item->itemObj->max_unit_quantity;
                $a_attributes['combination_id'] = $o_item->itemObj->combination_id;
                $a_attributes['combination_name'] = $o_item->itemObj->combination_name;
                $a_new_products[$o_item->itemObj->parent_item_id] = $a_attributes;
            }
            $this->checkedItems[$o_item->itemObj->parent_item_id] = $o_item;
        }

        //ITEMS
        $a_product_ids = [];
        foreach ($this->items as $spItem) {
            if (isset($spItem->product_id)) {
                $a_product_ids[] = $spItem->product_id;
            }
            if ($spItem->product_type == Product::TYPE_NEW) {
                if (isset($a_new_products[$spItem->parent_item_id]) && isset($a_new_products[$spItem->parent_item_id]['product_id'])) {
                    $a_product_ids[] = $a_new_products[$spItem->parent_item_id]['product_id'];
                }
            }
        }
        $this->presentationItems = SpGetProductPresentations::getAssociative($this->presentationMode, $a_product_ids, $this->controller->getOrganization()->globalVar->display_currency);

        foreach ($this->items as $spItem) {

            $spItem['id'] = $spItem->parent_item_id;

            //Seteamos subitems
            switch ($spItem->product_type) {

                case Product::TYPE_NEW:
                    $a_presentationOption = [];
                    if (isset($a_new_products[$spItem->parent_item_id])) {

                        $a_new = $a_new_products[$spItem->parent_item_id];
                        $spItem->product_id = isset($a_new['product_id']) ? $a_new['product_id'] : $spItem->product_id;
                        $spItem->product_name = isset($a_new['product_name']) ? $a_new['product_name'] : $spItem->product_name;
                        $spItem->product_type = isset($a_new['product_type']) ? $a_new['product_type'] : $spItem->product_type;
                        $spItem->item_type_id = isset($a_new['item_type_id']) ? $a_new['item_type_id'] : $spItem->item_type_id;
                        $spItem->allow_decimals = isset($a_new['allow_decimals']) ? $a_new['allow_decimals'] : $spItem->allow_decimals;
                        $spItem->max_pres_quantity = isset($a_new['max_pres_quantity']) ? $a_new['max_pres_quantity'] : $spItem->max_pres_quantity;
                        $spItem->max_unit_quantity = isset($a_new['max_unit_quantity']) ? $a_new['max_unit_quantity'] : $spItem->max_unit_quantity;
                    }
                    break;
            }

            //BALANCES
            switch ($spItem->currency) {
                case Currency::PEN:
                    $spItem->amount_pen = $spItem->amount;
                    $spItem->amount_usd = round($spItem->amount / $this->model->movement->exchange_rate, $this->ifixed);
                    break;
                case Currency::USD:
                    $spItem->amount_pen = round($spItem->amount * $this->model->movement->exchange_rate, $this->ifixed);
                    $spItem->amount_usd = $spItem->amount;
                    break;
                default:
                    throw new Exception('Moneda inválida!', USREST::CODE_INTERNAL_SERVER_ERROR);
            }

            if (isset($this->checkedItems[$spItem->parent_item_id])) {
                $spItem->checked = true;
                $spItem->equivalence = $this->checkedItems[$spItem->parent_item_id]->itemObj->equivalence;
                $spItem->item_type_id = $this->checkedItems[$spItem->parent_item_id]->itemObj->item_type_id;
                $spItem->pres_quantity = $this->checkedItems[$spItem->parent_item_id]->itemObj->pres_quantity;
                $spItem->combination_id = $this->checkedItems[$spItem->parent_item_id]->itemObj->combination_id;
                $spItem->combination_name = $this->checkedItems[$spItem->parent_item_id]->itemObj->combination_name;

                if ($this->checkedItems[$spItem->parent_item_id]->hasErrors('product_id')) {
                    $spItem->addError('product_id', $this->checkedItems[$spItem->parent_item_id]->getError('product_id'));
                }

                if ($this->checkedItems[$spItem->parent_item_id]->itemObj->hasErrors('pres_quantity')) {
                    $spItem->addError('pres_quantity', $this->checkedItems[$spItem->parent_item_id]->itemObj->getError('pres_quantity'));
                }

                if ($this->checkedItems[$spItem->parent_item_id]->hasErrors("cost_{$this->model->movement->currency}")) {
                    $spItem->addError('amount', $this->checkedItems[$spItem->parent_item_id]->getError("amount_{$this->model->movement->currency}"));
                }
            } else {
                $spItem->checked = false;

                //MONTO
                switch ($this->model->movement->currency) {
                    case Currency::PEN:
                        $spItem->amount = $spItem->amount_pen;
                        break;
                    case Currency::USD:
                        $spItem->amount = $spItem->amount_usd;
                        break;
                    default:
                        throw new Exception('Moneda inválida!', USREST::CODE_INTERNAL_SERVER_ERROR);
                }
            }
        }

        //DATA PROVIDER
        $this->dataProvider = new USArrayDataProvider($this->items, array(
            'keyField' => array('parent_item_id'), // PRIMARY KEY
            'sort' => false,
        ));
        $this->dataProvider->pagination = false;

        //ACCESS
        $this->preview_access = $this->controller->checkRoute('/logistic/product/preview');
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-product-list-checked-grid.js');
        //Registramos assets
        if ($this->combinations) {
            SIANAssets::registerScriptFile('other/select2/js/select2.min.js');
            SIANAssets::registerScriptFile('other/select2/js/i18n/es.js');
            SIANAssets::registerCssFile('other/select2/css/select2.css');
            SIANAssets::registerScriptFile('js/us-select2.js');
            SIANAssets::registerScriptFile('js/us-cost-level.js');
        }
        //SCRIPTS
        Yii::app()->clientScript->registerScript(Yii::app()->controller->getServerId(), "

        var grid = $('#{$this->id}');
        grid.data('ifixed', {$this->ifixed});
        grid.data('tfixed', {$this->tfixed});
        grid.data('currency', '{$this->model->movement->currency}');
        grid.data('exchange_rate', '{$this->model->movement->exchange_rate}');
        grid.data('direction', '{$this->model->movement->direction}');        
        grid.data('exclude_id', " . CJSON::encode($this->model->movement->kardex_unlock_exclude_id) . ");
        grid.data('count', 0);//COUNT
        grid.data('allow_duplicate', $('#{$this->allow_duplicate_checkbox_id}').prop('checked') ? 1 : 0);
        grid.data('independent_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INDEPENDENT . "');
        grid.data('inherit_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INHERIT . "');
        grid.data('local_storage_name', '{$this->local_storage_name}');
        grid.data('new_products', " . CJSON::encode($a_new_products) . ");
        grid.data('combinations', " . CJSON::encode($this->combinations) . ");
            
        //SIANProductListCheckedGridCalculate('{$this->id}');
            
        $(document).ready(function() {    

            //CURRENCY SYMBOL
            $('span.currency-symbol').text('" . Currency::getSymbol($this->model->movement->currency) . "');   
            //Generamos cambios  
            SIANProductListCheckedGridCalculate('{$this->id}'); 
            SIANProductListCheckedGridLoad('{$this->id}');
           
            " . ($this->combinations ? "
                
            $('#{$this->id}').find('table td div.sian-product-list-checked-grid-item-destiny').each(function (index) {
                var combination_span_id = getLocalId();
                var trObj = $(this).parent().parent();
                var s_combination_id = $(this).find('input.sian-product-list-checked-grid-item-combination-id-aux').val();
                var s_combination_name = $(this).find('input.sian-product-list-checked-grid-item-combination-name-aux').val();
                var s_combination_readonly = $(this).find('input.sian-product-list-checked-grid-item-combination-readonly').val();
                
                var html = USSpanInit(combination_span_id, 'sian-product-list-checked-grid-item-combination-id-span', s_combination_id, s_combination_name, 'Item[' + trObj.prop('id') + '][combination_id]', 'Item[' + trObj.prop('id') + '][combination_name]', false, 
                {
                    owner: OWNER_COMBINATION, 
                    target_ids: [], 
                    overwrite_ids: []
                }, [], false, s_combination_readonly == 1);
                $(this).append(html);
                var check = $(trObj).find('input.sian-product-list-checked-grid-item-check');
                SIANProductListCheckedGridEnableOrDisabled($(check));
            });
             " : "") . "
        }); 
        
        $('#{$this->id}').data('changeEmission', function(changeData){
            var grid = $('#{$this->id}');
            //Seteamos
            grid.data('emission_date', changeData.emission_date);
            SIANProductListCheckedGridCalculate('{$this->id}'); 
        });
        
    " . ($this->combinations ? "
            $('body').on('click', 'span.sian-product-list-checked-grid-item-combination-id-span', function(e) {
                var spanObj = $(this);
                var span_id = spanObj.attr('id');
                USCostLevelInit(span_id, 1, 2, 400);
            });
            " : "") . "
        
        $('body').on('change', '#{$this->id} select.sian-product-list-checked-grid-item-equivalence', function(e) {

            var equivalenceObj = $(this);
            var row = equivalenceObj.closest('tr');
            var table = row.closest('table');
            
            var maxUnitQuantityInput = row.find('input.sian-product-list-checked-grid-item-max-unit-quantity');
            var presQuantityInput = row.find('input.sian-product-list-checked-grid-item-pres-quantity');
            presQuantityInput.floatAttr('max', 2, maxUnitQuantityInput.double2() / equivalenceObj.double2());
            
            var minPresCostPenObj = row.find('input.sian-product-list-checked-grid-item-sale-cost-pen');
            var minPresCostUsdObj = row.find('input.sian-product-list-checked-grid-item-sale-cost-usd');

            var mult = 1 / equivalenceObj.floatData('equivalence', 2) * equivalenceObj.double2();
            
            minPresCostPenObj.floatVal({$this->ifixed}, minPresCostPenObj.floatVal({$this->ifixed}) * mult);
            minPresCostUsdObj.floatVal({$this->ifixed}, minPresCostUsdObj.floatVal({$this->ifixed}) * mult);

            equivalenceObj.floatData('equivalence', 2, equivalenceObj.double2());
            //Actualizar montos
            SIANProductListCheckedGridCalculate('{$this->id}');
       });

        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $b_preview_access = $this->preview_access;
        $form = $this->form;
        $form_currency = $this->model->movement->currency;
        $stock_mode = $this->model->movement->stock_mode;
        $emission_date = $this->model->movement->emission_date;
        $exclude_id = $this->model->movement->kardex_unlock_exclude_id;
        $warehouse_id = $this->model->warehouse_id;

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => 'Mercaderías',
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempItems') ? 'us-error' : ''
            )
        ));

        $counter = 0;
        $columns = [];

        array_push($columns, array(
            'header' => CHtml::checkBox("All", $this->getGlobalCheckBoxState(), array(
                'class' => 'sian-product-list-checked-grid-check',
                'onchange' => 'SIANProductListCheckedGridGridCheck($(this));',
                'readonly' => $this->readonly,
            )),
            'headerHtmlOptions' => array('style' => 'width:2%; text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) {
                return CHtml::checkBox("Checked[{$row->parent_item_id}]", $row->checked, array(
                    'class' => 'sian-product-list-checked-grid-item-check',
                    'onchange' => 'SIANProductListCheckedGridRowCheck($(this));',
                    'readonly' => $this->readonly,
                ));
            },
        ));

        array_push($columns, array(
            'header' => '#',
            'headerHtmlOptions' => array('style' => 'width:2%;'),
            'type' => 'raw',
            'value' => function ($row) use (&$counter) {
                return "<span class='sian-product-list-checked-grid-item-index'>" . ( ++$counter) . "</span>";
            },
        ));

        array_push($columns, array(
            'header' => 'ID',
            'headerHtmlOptions' => array('style' => 'width:6%;text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->textFieldRow($row, 'product_id', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][product_id]",
                    'class' => 'sian-product-list-checked-grid-item-product-id',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:center;',
                ));
            },
        ));

        $width = 15;

//Si NO es avanzado aumentamos 40 porque no se mostrarán los montos
        if (!$this->show_amounts) {
            $width += 10;
        }

        array_push($columns, array(
            'header' => 'Producto',
            'headerHtmlOptions' => array('style' => "width:{$width}%;text-align:center;"),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->textFieldRow($row, 'product_name', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][product_name]",
                    'class' => 'sian-product-list-checked-grid-item-product-name sian-force-tooltip',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'title' => $row->product_name
                ));
            },
        ));

        array_push($columns, array(
            'header' => 'Restante',
            'headerHtmlOptions' => array('style' => 'width:5%;text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:right;'),
            'type' => 'raw',
            'value' => function ($row) {
                $html = "<span class='sian-warehouse->checked-grid-item-max-pres-quantity'>{$row->max_pres_quantity}</span>";
                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_pres_quantity]", $row->max_pres_quantity, array(
                            'class' => 'sian-warehouse->checked-grid-item-max-pres-quantity',
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));
                return $html;
            },
        ));

        array_push($columns, array(
            'header' => 'Cantidad',
            'headerHtmlOptions' => array('style' => 'width:7%; text-align:center;'),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->numberFieldRow($row, 'pres_quantity', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][pres_quantity]",
                    'class' => "sian-product-list-checked-grid-item-pres-quantity " . ($row->allow_decimals == 1 ? "us-double2" : "us-double0"),
                    'min' => $row->allow_decimals == 1 ? 0.01 : 0,
                    'max' => round($row->max_unit_quantity / $row->equivalence, $row->allow_decimals == 1 ? 2 : 0),
                    'step' => $row->allow_decimals == 1 ? 0.01 : 0,
                    'disabled' => !$row->checked,
                    'style' => 'text-align:right;',
                    'onchange' => "SIANProductListCheckedGridCalculate('{$this->id}')",
                    'readonly' => $this->readonly,
                ));
            },
        ));

        array_push($columns, array(
            'header' => 'Pres.',
            'headerHtmlOptions' => array('style' => 'width:6%'),
            'type' => 'raw',
            'value' => function ($row) {
                return $this->renderPresentationSelect($row, $this->presentationItems, $this->readonly, !$row->checked);
            },
        ));

        if ($this->combinations) {
            array_push($columns, array(
                'header' => 'Destino',
                'headerHtmlOptions' => array('style' => 'width:6%'),
                'type' => 'raw',
                'value' => function ($row) {
                    $s_input = "";
                    $s_input .= CHtml::hiddenField("Item[{$row->parent_item_id}][combination_id_aux]", $row->combination_id, array(
                                'class' => "sian-product-list-checked-grid-item-combination-id-aux",
                    ));
                    $s_input .= CHtml::hiddenField("Item[{$row->parent_item_id}][combination_name_aux]", $row->combination_name, array(
                                'class' => "sian-product-list-checked-grid-item-combination-name-aux",
                    ));
                    $s_input .= CHtml::hiddenField("Item[{$row->parent_item_id}][combination_readonly]", !$row->checked ? 1 : 0, array(
                                'class' => "sian-product-list-checked-grid-item-combination-readonly",
                    ));
                    return "<div class = 'sian-product-list-checked-grid-item-destiny'>" . $s_input . "</div>";
                },
            ));
        }

        array_push($columns, array(
            'header' => 'Hidden',
            'headerHtmlOptions' => array('style' => 'display:none'),
            'htmlOptions' => array('style' => 'display:none'),
            'footerHtmlOptions' => array('style' => 'display:none'),
            'type' => 'raw',
            'value' => function ($row)use ($form, $form_currency) {

                $html = CHtml::hiddenField("Item[{$row->parent_item_id}][item_type_id]", $row->item_type_id, array('class' => "sian-product-list-checked-grid-item-item-type-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][product_type]", $row->product_type, array('class' => "sian-product-list-checked-grid-item-product-type",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][max_unit_quantity]", $row->max_unit_quantity, array('class' => "sian-product-list-checked-grid-item-max-unit-quantity",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][allow_decimals]", $row->allow_decimals, array('class' => "sian-product-list-checked-grid-item-allow-decimals",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][currency]", $row->currency, array(
                            'class' => "sian-product-list-checked-grid-item-currency",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                if (isset($row['parent_product_type'])) {
                    $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][parent_product_type]", $row->parent_product_type, array('class' => "sian-product-list-checked-grid-item-parent-product-type",
                                'readonly' => true,
                                'disabled' => !$row->checked,
                    ));
                }

                if (isset($row['parent_product_type_original'])) {
                    $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][parent_product_type_original]", $row->parent_product_type_original, array('class' => "sian-product-list-checked-grid-item-parent-product-type-original",
                                'readonly' => true,
                                'disabled' => !$row->checked,
                    ));
                }

                //ITEM LINKS
                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][parent_item_id]", $row->parent_item_id, array(
                            'class' => "sian-product-list-checked-grid-item-parent-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][quantity_item_id]", $row->quantity_item_id, array(
                            'class' => "sian-product-list-checked-grid-item-quantity-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));

                $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][total_item_id]", $row->total_item_id, array(
                            'class' => "sian-product-list-checked-grid-item-total-item-id",
                            'readonly' => true,
                            'disabled' => !$row->checked,
                ));
                if (!$this->combinations) {
                    $html .= CHtml::hiddenField("Item[{$row->parent_item_id}][combination_id]", $row->combination_id, array(
                                'class' => "sian-product-list-checked-grid-item-combination-id",
                                'readonly' => true,
                                'disabled' => !$row->checked,
                    ));
                }
                return $html;
            },
        ));

        array_push($columns, array(
            'header' => "Monto <span class='currency-symbol'></span> ",
            'headerHtmlOptions' => array('style' => "width:6%; display:" . ($this->show_amounts ? 'table-cell' : 'none')),
            'htmlOptions' => array('style' => "display:" . ($this->show_amounts ? 'table-cell' : 'none')),
            'footerHtmlOptions' => array('style' => "display:" . ($this->show_amounts ? 'table-cell' : 'none')),
            'type' => 'raw',
            'value' => function ($row) use ($form) {
                return $form->textFieldRow($row, 'amount', array(
                    'label' => false,
                    'name' => "Item[{$row->parent_item_id}][amount]",
                    'class' => 'sian-product-list-checked-grid-item-amount',
                    'readonly' => true,
                    'disabled' => !$row->checked,
                    'style' => "display:" . ($this->show_amounts ? 'table-cell' : 'none'),
                ));
            },
        ));

        array_push($columns, array(
            'header' => "Total <span class='currency-symbol'></span>",
            'headerHtmlOptions' => array('style' => "width:6%; display:" . ($this->show_amounts ? 'table-cell' : 'none')),
            'htmlOptions' => array('style' => "display:" . ($this->show_amounts ? 'table-cell' : 'none')),
            'footerHtmlOptions' => array('style' => "display:" . ($this->show_amounts ? 'table-cell' : 'none')),
            'type' => 'raw',
            'value' => function () {
                return "<span class='sian-product-list-checked-grid-item-total'></span>";
            },
            'footer' => "<span class='sian-product-list-checked-grid-total-total'></span>",
        ));

        array_push($columns, array(
            'header' => 'Opc.',
            'headerHtmlOptions' => array('style' => 'width:3%;text-align:center;'),
            'htmlOptions' => array('style' => 'text-align:center'),
            'type' => 'raw',
            'value' => function ($row) use ($b_preview_access) {
                return $this->_renderRowButtons($row, $b_preview_access);
            },
        ));

        $gridParams = array(
            'id' => $this->id,
            'type' => 'bordered condensed',
            'dataProvider' => $this->dataProvider,
            'enableSorting' => false,
            'selectableRows' => 0,
            'columns' => $columns,
            'template' => '{items}',
            'rowCssClassExpression' => '"sian-product-list-checked-grid-item " . ($data->checked == 1 ? "success" : "danger")',
            'htmlOptions' => [
                'class' => 'sian-product-list-checked-grid-table sian-product-list-checked-grid-item-table',
            ]
        );
        $this->widget('application.widgets.USGridView', $gridParams);
        echo "<br>";
        echo "<div class='row'>";
        if ($this->show_observations) {
            echo "<div class='col-lg-10 col-md-10 col-sm-10 col-xs-12'>";
            $this->_renderObservations();
            echo "</div>";
        }
        echo "<div class='col-lg-1 col-md-1 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->allow_duplicate_checkbox_id,
            'model' => $this->model,
            'attribute' => 'allow_duplicate',
            'htmlOptions' => array(
                'readonly' => true
            )
                ), true);
        echo "</div>";
        echo "</div>";

        $this->endWidget();
    }

    private function _renderRowButtons($row, $preview_access) {
        $s_html = Yii::app()->controller->widget('application.widgets.USLink', array(
            'icon' => 'fa fa-eye fa-lg',
            'route' => '/logistic/merchandise/preview',
            'label' => '',
            'title' => 'Visualizar',
            'class' => 'form',
            'data' => array(
                'id' => $row->product_id
            ),
            'visible' => $preview_access
                ), true);

        if (isset($row['parent_product_type']) && $row['parent_product_type'] == Product::TYPE_NEW) {
            $s_html .= " ";
            $s_html .= Yii::app()->controller->widget('application.widgets.USLink', array(
                'icon' => 'fa fa-plus fa-lg',
                'label' => '',
                'route' => '/logistic/product/assign',
                'title' => 'Asignar Producto',
                'class' => 'form',
                'data' => [
                    'id' => $this->id,
                    'local_storage_name' => $this->local_storage_name,
                    'row_id' => $row->id,
                    'types' => json_encode([Product::TYPE_MERCHANDISE]),
                    'widget' => 'SIANProductListCheckedGrid',
                ],
                'visible' => $this->preview_access,
                    ), true);
        }

        return $s_html;
    }

    private function renderPresentationSelect($p_o_row, $items, $readonly, $disabled) {

        $s_html = "<select class='form-control sian-product-list-checked-grid-item-equivalence' name='Item[{$p_o_row['parent_item_id']}][equivalence]' data-equivalence={$p_o_row['equivalence']} " . ($disabled ? 'disabled' : '') . " " . ($readonly ? 'readonly' : '') . ">";
        if ($p_o_row['product_type'] == Product::TYPE_NEW) {
            $s_html .= "<option value='1' data-mprice=0 data-imprice=0 data-aprice=0 data-iaprice=0 selected>UNI.</option>";
        } else {
            foreach ($items[$p_o_row['product_id']] as $item) {
                $selected = $item['equivalence'] == $p_o_row['equivalence'] ? 'selected' : '';
                $s_html .= "<option value = '{$item['equivalence']}' {$selected}>{$item['measure_name']}</option>";
            }
        }
        $s_html .= '</select>';
        if ($p_o_row->hasErrors('equivalence')) {
            $s_html .= "<span class='help-block error'>{$p_o_row->getFirstError()['error']}</span>";
        }
        return $s_html;
    }

    private function getGlobalCheckBoxState() {
        return count($this->model->tempItems) > 0;
    }

    private function _renderObservations() {

        echo "<div class='row'>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->textAreaRow($this->model->movement, 'observation', array(
            'id' => $this->observation_input_id,
            'rows' => 1,
            'maxlength' => 500,
            'required' => $this->model->movement->isObservationRequired()
        ));
        echo "</div>";

        echo "</div>";
    }

}
