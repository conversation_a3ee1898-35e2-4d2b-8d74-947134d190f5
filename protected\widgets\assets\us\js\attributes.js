(function ($) {

    $.fn.integer = function (value) {
        if (typeof value == "undefined")
        {
            return USMath.round(this.val(), 0);
        } else
        {
            return this.val(USMath.round(value, 0).toFixed(0));
        }
    };

    $.fn.double0 = function (value) {
        if (typeof value == "undefined")
        {
            return USMath.round(this.val(), 0);
        } else
        {
            return this.val(USMath.round(value, 0).toFixed(0));
        }
    };

    $.fn.double1 = function (value) {
        if (typeof value == "undefined")
        {
            return USMath.round(this.val(), 1);
        } else
        {
            return this.val(USMath.round(value, 1).toFixed(1));
        }
    };

    $.fn.double = function (value) {
        if (typeof value == "undefined")
        {
            return USMath.round(this.val(), 2);
        } else
        {
            return this.val(USMath.round(value, 2).toFixed(2));
        }
    };

    $.fn.double2 = function (value) {
        if (typeof value == "undefined")
        {
            return USMath.round(this.val(), 2);
        } else
        {
            return this.val(USMath.round(value, 2).toFixed(2));
        }
    };

    $.fn.double3 = function (value) {
        if (typeof value == "undefined")
        {
            return USMath.round(this.val(), 3);
        } else
        {
            return this.val(USMath.round(value, 3).toFixed(3));
        }
    };

    $.fn.double4 = function (value) {
        if (typeof value == "undefined")
        {
            return USMath.round(this.val(), 4);
        } else
        {
            return this.val(USMath.round(value, 4).toFixed(4));
        }
    };

    $.fn.double5 = function (value) {
        if (typeof value == "undefined")
        {
            return USMath.round(this.val(), 5);
        } else
        {
            return this.val(USMath.round(value, 5).toFixed(5));
        }
    };

    $.fn.readonly = function (value) {
        if (typeof value == "undefined")
        {
            return this.hasAttr('readonly');
        } else
        {
            var element = this;
            element.attr('readonly', value);
            if (value)
            {
                element.attr('tabIndex', '-1');

            } else
            {
                element.removeAttr('tabindex');
            }
            return element;
        }
    };

    $.fn.hasAttr = function (name) {
        return this.attr(name) !== undefined;
    };

    $.fn.fix = function (precision) {
        this.val(us_formatNumber(parseFloat(this.val()), precision));
        return this;
    };


    /**
     * Obtiene o establece el valor float de un input
     * @param {Number} fixed Dígitos a redondear
     * @param {Number} value Valor. Si es null es una lectura
     * @returns {Number}|{Object} Retorna el valor del objeto si es lectura, o el mismo objeto si es escritura
     */
    $.fn.floatVal = function (fixed, value) {
        if (typeof value == "undefined")
        {
            return USMath.round(this.val(), fixed);
        } else
        {
            return this.val(USMath.round(value, fixed).toFixed(fixed));
        }
    };

    /**
     * Obtiene o establece el valor float del texto de un input
     * @param {Number} fixed Dígitos a redondear
     * @param {Number} value Valor del text. Si es null es una lectura
     * @returns {Number}|{Object} Retorna el valor float del text del objeto si es lectura, o el mismo objeto si es escritura
     */
    $.fn.floatText = function (fixed, value) {
        if (typeof value == "undefined")
        {
            return USMath.round(this.text(), fixed);
        } else
        {
            return this.text(USMath.round(value, fixed).toFixed(fixed));
        }
    };

    /**
     * Obtiene o establece el valor float de un atributo HTML
     * @param {String} attr Atributo HTML
     * @param {Number} fixed Dígitos a redondear
     * @param {Number} value Valor del atributo. Si es null es una lectura
     * @returns {Number}|{Object} Retorna el valor del atributo si es lectura, o el mismo objeto si es escritura
     */
    $.fn.floatAttr = function (attr, fixed, value) {

        //Obtenemos el balance actual!
        var balance = USMath.round(this.attr(attr), fixed);

        if (typeof value == "undefined")
        {
            return balance;
        } else
        {
            //Obtenemos el valor actual
            var currentValue = this.floatVal(fixed);

            //Si el balance y el valor son iguales ajustamos este último
            if (currentValue === balance) {
                this.floatVal(fixed, value);
            }

            return this.attr(attr, USMath.round(value, fixed).toFixed(fixed));

        }
    };

    /**
     * Obtiene o establece el valor int de un atributo HTML
     * @param {String} attr Atributo HTML
     * @param {Number} value Valor del atributo. Si es null es una lectura
     * @returns {Number}|{Object} Retorna el valor del atributo si es lectura, o el mismo objeto si es escritura
     */
    $.fn.intAttr = function (attr, value) {
        if (typeof value == "undefined")
        {
            return parseInt(this.attr(attr));
        } else
        {
            return this.attr(attr, parseInt(value));
        }
    };

    /**
     * Obtiene o establece el valor int de un campo data
     * @param {String} data Data HTML
     * @param {Number} fixed Dígitos a redondear
     * @param {Number} value Valor del atributo. Si es null es una lectura
     * @returns {Number}|{Object} Retorna el valor del data si es lectura, o el mismo objeto si es escritura
     */
    $.fn.intData = function (data, value) {
        if (typeof value == "undefined")
        {
            return parseInt(this.data(data));
        } else
        {
            return this.data(data, parseInt(value));
        }
    };

    /**
     * Obtiene o establece el valor float de un campo data
     * @param {String} data Data HTML
     * @param {Number} fixed Dígitos a redondear
     * @param {Number} value Valor del atributo. Si es null es una lectura
     * @returns {Number}|{Object} Retorna el valor del data si es lectura, o el mismo objeto si es escritura
     */
    $.fn.floatData = function (data, fixed, value) {
        if (typeof value == "undefined")
        {
            return USMath.round(this.data(data), fixed);
        } else
        {
            return this.data(data, USMath.round(value, fixed).toFixed(fixed));
        }
    };

    $.fn.toPen = function (exchange_rate, digits, options) {

        if (typeof options === 'undefined')
        {
            options = {};
        }

        if (typeof digits === 'undefined')
        {
            digits = 2;
        }

        var tolerance = 1 / Math.pow(10, digits);

        //VALUE
        var value = parseFloat(this.val()) * exchange_rate;

        if (typeof options.min !== 'undefined')
        {
            value = (USMath.round(Math.abs(value - options.min), digits) <= tolerance || value < options.min) ? options.min : value;
        }

        if (typeof options.max !== 'undefined')
        {
            value = (USMath.round(Math.abs(value - options.max), digits) <= tolerance || value > options.max) ? options.max : value;
        }

        this.val(USMath.round(value, digits).toFixed(digits));
        //MIN
        if (this.hasAttr('min'))
        {
            if (typeof options.min !== 'undefined')
            {
                this.attr('min', USMath.round(options.min, digits).toFixed(digits));
            } else
            {
                var min = parseFloat(this.attr('min')) * exchange_rate;
                this.attr('min', USMath.round(min, digits).toFixed(digits));
            }

        }
        //MAX
        if (this.hasAttr('max'))
        {
            if (typeof options.max !== 'undefined')
            {
                this.attr('max', USMath.round(options.max, digits).toFixed(digits));
            } else
            {
                var attrMax = parseFloat(this.attr('max')) * exchange_rate;
                this.attr('max', USMath.round(attrMax, digits).toFixed(digits));
            }
        }

        return this;
    };

    $.fn.toUsd = function (exchange_rate, digits, options) {

        if (typeof options === 'undefined')
        {
            options = {};
        }

        if (typeof digits === 'undefined')
        {
            digits = 2;
        }

        var tolerance = 1 / Math.pow(10, digits);

        //VALUE
        var value = parseFloat(this.val()) / exchange_rate;

        if (typeof options.min !== 'undefined')
        {
            value = (USMath.round(Math.abs(value - options.min), digits) <= tolerance || value < options.min) ? options.min : value;
        }

        if (typeof options.max !== 'undefined')
        {
            value = (USMath.round(Math.abs(value - options.max), digits) <= tolerance || value > options.max) ? options.max : value;
        }

        this.val(USMath.round(value, digits).toFixed(digits));

        //MIN
        if (this.hasAttr('min'))
        {
            if (typeof options.min !== 'undefined')
            {
                this.attr('min', USMath.round(options.min, digits).toFixed(digits));
            } else
            {
                var min = parseFloat(this.attr('min')) / exchange_rate;
                this.attr('min', USMath.round(min, digits).toFixed(digits));
            }

        }
        //MAX
        if (this.hasAttr('max'))
        {
            if (typeof options.max !== 'undefined')
            {
                this.attr('max', USMath.round(options.max, digits).toFixed(digits));
            } else
            {
                var attrMax = parseFloat(this.attr('max')) / exchange_rate;
                this.attr('max', USMath.round(attrMax, digits).toFixed(digits));
            }

        }
        return this;

    };

    $.fn.dataToVal = function (dataname) {
        var value = this.data(dataname);
        this.val(value);
        return this;
    };

    $.fn.lpad = function (char, max) {
        var str = this.val().toString();
        this.val(str.length < max ? lpad(char + str, char, max) : str);
        return this;
    };

    $.fn.getDataParams = function () {
        var data = this.data();
        delete data['uiTooltipId'];
        delete data['uiTooltipTitle'];
        delete data['uiTooltipOpen'];
        delete data['_'];
        return data;
    };

//    $.fn.serializeObject = function () {
//        var data = {};
//        $.each(this.serializeArray(), function (key, obj) {
//            var a = obj.name.match(/(.*?)\[(.*?)\]/);
//            if (a !== null) {
//                var subName = new String(a[1]);
//                var subKey = new String(a[2]);
//
//                if (!data[subName])
//                    data[subName] = {};
////			if (data[subName][subKey]) {
////				if ($.isArray(data[subName][subKey])) {
////					data[subName][subKey].push(obj.value);
////				} else {
////					data[subName][subKey] = {};
////					data[subName][subKey].push(obj.value);
////				}
////				;
////			} else {
//                data[subName][subKey] = obj.value;
////			}
//            } else {
//                var keyName = new String(obj.name);
//                if (data[keyName]) {
//                    if ($.isArray(data[keyName])) {
//                        data[keyName].push(obj.value);
//                    } else {
//                        data[keyName] = {};
//                        data[keyName].push(obj.value);
//                    }
//                } else {
//                    data[keyName] = obj.value;
//                }
//            }
//        });
//        return data;
//    };

    $.fn.disabled = function (value) {
        if (this.data('hidden_id') !== 'undefined')
        {
            var hidden_id = this.data('hidden_id');
            $('#' + hidden_id).prop('disabled', value);
        }

        this.prop('disabled', value);
        return this;
    };

    $.fn.sortOptions = function ()
    {
        var selectList = this.find('option');
        selectList.sort(function (a, b) {
            return $(a).text() > $(b).text() ? 1 : $(a).text() < $(b).text() ? -1 : 0;
        });
        return this.html(selectList);
    };

    $.fn.addError = function (error)
    {
        if (error)
        {
            var group = this.closest('.form-group');
            if (group.hasClass('has-error'))
            {
                group.find('span.error').text(error);
            } else
            {
                group.addClass('has-error');
                group.append('<span class="help-block error">' + error + '</span>');
            }

        }
        return this;
    };

    $.fn.cleanError = function ()
    {
        var group = this.closest('.form-group');
        if (group.removeClass('has-error'))
        {
            group.find('span.error').remove();
        }

        return this;
    };

    $.fn.makeRequired = function (value) {

        var span = '<span class="required">*</span>';

        if (typeof value == "undefined")
        {
            return this.parents('div.form-group').find('label.control-label').hasClass('required');
        } else
        {
            if (value)
            {
                var label = this.parents('div.form-group').find('label.control-label');
                //Agregamos sólo si no tiene la clase required
                if (!label.hasClass('required'))
                {
                    label.addClass('required');
                    label.append(' ' + span);
                }

            } else
            {
                var label = this.parents('div.form-group').find('label.control-label');
                label.removeClass('required');

                var newHtml = label.html().replace(' ' + span, '');
                label.html(newHtml);

            }
            return this;
        }
    };

    $.fn.makeHeaderRequired = function (value) {

        var span = '<span class="required">*</span>';

        if (typeof value == "undefined")
        {
            return this.hasClass('required');
        } else
        {
            if (value)
            {
                //Agregamos sólo si no tiene la clase required
                if (!this.hasClass('required'))
                {
                    this.addClass('required');
                    this.append(' ' + span);
                }

            } else
            {
                this.removeClass('required');

                var newHtml = this.html().replace(' ' + span, '');
                this.html(newHtml);

            }

            return this;
        }
    };

    $.fn.allowDecimals = function (decimals) {

        var step = 1 / Math.pow(10, decimals);

        this.removeClass('us-integer')
                .removeClass('us-double0')
                .removeClass('us-double1')
                .removeClass('us-double2')
                .removeClass('us-double3')
                .removeClass('us-double4')
                .removeClass('us-double5')
                .removeClass('us-double2')
                .removeClass('us-double2')
                .removeClass('us-double2')
                .addClass('us-double' + decimals)
                .attr('min', step)
                .attr('step', step);

        return this;
    };

    $.fn.resizeFooterJQuery = function () {
        var pageHeight = document.documentElement.scrollHeight;
        var windowHeight = window.innerHeight;

        var coordsfooter =  $("#SianFooter").get(0).getBoundingClientRect();
        var coordsContent =  $("#SianContent").get(0).getBoundingClientRect();

        // distancia del borde inferior del footer al inicio del documento (cuando se ha hecho scroll)
        var heightToValidate = Math.ceil(coordsfooter.bottom+scrollY);

        if ( pageHeight <= windowHeight ) {
            if ( heightToValidate <= pageHeight ) {
                $("#SianFooter").addClass( "footer-abs-bottom" );
                if ( coordsfooter.top < coordsContent.bottom ) {
                    console.log("removeClass");
                    $("#SianFooter").removeClass("footer-abs-bottom");
                }
            } else {
                $("#SianFooter").removeClass("footer-abs-bottom");
            }

        } else {
            $("#SianFooter").removeClass("footer-abs-bottom");
        }

        flatResizeFooter = true;
        return false;
    }

    $.fn.fitToContent = function () {
        if (this[0] === undefined) { 
            $.fn.resizeFooterJQuery();
        } else {
            $(this[0]).css('height', this[0].contentWindow.document.body.clientHeight);
            $(this[0]).load(function(){ 
                $.fn.resizeFooterJQuery(); 
            });
        }
        
        return this;
    };

    $.fn.visible = function (visible) {

        if (typeof visible == "undefined") {
            return this.is(":visible");
        } else
        {
            if (visible) {
                this.show();
            } else {
                this.hide();
            }
        }
    };

})(jQuery);
