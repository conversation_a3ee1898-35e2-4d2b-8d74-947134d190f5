<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of USService
 *
 * <AUTHOR>
 */
class HBSHelper {

    const HBS_TOKEN_KEY = 'HBS_TOKEN';
    const CODE_SUCCESS = 200;
    const CODE_ERROR = 500;
    const CODE_UNAUTHORIZED = 403;
    const EXCEPTION_INVALID_MESSAGE = "Error sending webservice response";
    const MAX_ATTEMPS = 1;

    public static function getToken() {
        $s_token = Yii::app()->getGlobalState(self::HBS_TOKEN_KEY);

        if (empty($s_token)) {
            $s_token = self::login();
            Yii::app()->setGlobalState(self::HBS_TOKEN_KEY, $s_token);
        }

        return $s_token;
    }

    public static function login() {
        $a_response = self::loginService("/auth/signin", [
                    'username' => Yii::app()->params['hbs_username'],
                    'password' => Yii::app()->params['hbs_password'],
        ]);
        return self::getValidatedMessage($a_response);
    }

    public static function readExcel($p_s_filepath) {
        $a_response = self::uploadService("/util/excel/read", $p_s_filepath);
        return self::getValidatedMessage($a_response);
    }

    public static function deleteExcel($p_s_basename) {
        $a_response = self::deleteService("/util/excel", $p_s_basename);
        return self::getValidatedMessage($a_response);
    }

    public static function download($p_a_parameters) {
        $a_response = self::postService("/util/download/start", $p_a_parameters);
        return self::getValidatedMessage($a_response);
    }

    protected static function uploadService($p_s_ws, $p_s_filepath) {
        $s_url = Yii::app()->params['hbs_url'];
        return self::_uploadService($p_s_ws, $s_url, $p_s_filepath, TRUE, 0);
    }

    protected static function loginService($p_s_ws, $p_a_parameters = []) {
        $s_url = Yii::app()->params['hbs_url'];
        return self::_loginService($p_s_ws, $s_url, $p_a_parameters, FALSE);
    }

    protected static function postService($p_s_ws, $p_a_parameters = []) {
        $s_url = Yii::app()->params['hbs_url'];
        return self::_postService($p_s_ws, $s_url, $p_a_parameters, FALSE, 0);
    }

    protected static function deleteService($p_s_ws, $p_a_parameters = []) {
        $s_url = Yii::app()->params['hbs_url'];
        return self::_deleteService($p_s_ws, $s_url, $p_a_parameters, FALSE, 0);
    }

    protected static function _loginService($p_s_ws, $p_s_url, $p_a_parameters, $p_b_array = FALSE) {
        $o_curl = curl_init();
        curl_setopt_array($o_curl, array(
            CURLOPT_URL => $p_s_url . $p_s_ws,
            CURLOPT_POST => TRUE,
            CURLOPT_POSTFIELDS => json_encode($p_a_parameters),
            CURLOPT_SSL_VERIFYPEER => FALSE,
            CURLOPT_SSL_VERIFYHOST => FALSE,
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_USERAGENT => "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:35.0) Gecko/20100101 Firefox/35.0",
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json'
            ]
        ));
        $s_response_str = curl_exec($o_curl);

        $i_response_code = curl_getinfo($o_curl, CURLINFO_HTTP_CODE);

        if ($i_response_code !== self::CODE_SUCCESS) {
            curl_close($o_curl);
            throw new \Exception("HBS request failed: {$p_s_ws} (Code {$i_response_code})");
        }
        $o_temp_response = json_decode($s_response_str);
        if (!isset($o_temp_response)) {
            return (object) [
                        'code' => self::CODE_ERROR,
                        'msg' => $s_response_str
            ];
        } elseif (is_object($o_temp_response)) {
            if (isset($o_temp_response->code) AND $o_temp_response->code != self::CODE_SUCCESS) {
                return (object) [
                            'code' => $o_temp_response->code,
                            'msg' => $o_temp_response->msg
                ];
            }
        } else {
            
        } curl_close($o_curl);

        return json_decode($s_response_str, $p_b_array);
    }

    protected static function _postService($p_s_ws, $p_s_url, $p_a_parameters, $p_b_array = FALSE, $p_i_attemp = 0) {
        $o_curl = curl_init();
        curl_setopt_array($o_curl, array(
            CURLOPT_URL => $p_s_url . $p_s_ws,
            CURLOPT_POST => TRUE,
            CURLOPT_POSTFIELDS => json_encode($p_a_parameters),
            CURLOPT_SSL_VERIFYPEER => FALSE,
            CURLOPT_SSL_VERIFYHOST => FALSE,
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_USERAGENT => "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:35.0) Gecko/20100101 Firefox/35.0",
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer " . self::getToken(),
                'Content-Type: application/json'
            ]
        ));
        $s_response_str = curl_exec($o_curl);

        $i_response_code = curl_getinfo($o_curl, CURLINFO_HTTP_CODE);

        if ($i_response_code !== self::CODE_SUCCESS) {
            curl_close($o_curl);

            if ($i_response_code == self::CODE_UNAUTHORIZED && $p_i_attemp < self::MAX_ATTEMPS) {
                //Se intentará renovar el token sólo una vez
                Yii::app()->setGlobalState(self::HBS_TOKEN_KEY, null);
                return self::_postService($p_s_ws, $p_s_url, $p_a_parameters, $p_b_array, $p_i_attemp + 1);
            }

            throw new \Exception("HBS request failed: {$p_s_ws} (Code {$i_response_code})");
        }
        $o_temp_response = json_decode($s_response_str);
        if (!isset($o_temp_response)) {
            return (object) [
                        'code' => self::CODE_ERROR,
                        'msg' => $s_response_str
            ];
        } elseif (is_object($o_temp_response)) {
            if (isset($o_temp_response->code) AND $o_temp_response->code != self::CODE_SUCCESS) {
                return (object) [
                            'code' => $o_temp_response->code,
                            'msg' => $o_temp_response->msg
                ];
            }
        } else {
            
        } curl_close($o_curl);

        return json_decode($s_response_str, $p_b_array);
    }

    protected static function _deleteService($p_s_ws, $p_s_url, $p_a_parameters, $p_b_array = FALSE, $p_i_attemp = 0) {
        $o_curl = curl_init();
        curl_setopt_array($o_curl, array(
            CURLOPT_URL => $p_s_url . $p_s_ws,
            CURLOPT_CUSTOMREQUEST => "DELETE",
            CURLOPT_POSTFIELDS => json_encode($p_a_parameters),
            CURLOPT_SSL_VERIFYPEER => FALSE,
            CURLOPT_SSL_VERIFYHOST => FALSE,
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_USERAGENT => "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:35.0) Gecko/20100101 Firefox/35.0",
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer " . self::getToken(),
                'Content-Type: application/json'
            ]
        ));

        $s_response_str = curl_exec($o_curl);

        $i_response_code = curl_getinfo($o_curl, CURLINFO_HTTP_CODE);

        if ($i_response_code !== self::CODE_SUCCESS) {
            curl_close($o_curl);

            if ($i_response_code == self::CODE_UNAUTHORIZED && $p_i_attemp < self::MAX_ATTEMPS) {
                //Se intentará renovar el token sólo una vez
                Yii::app()->setGlobalState(self::HBS_TOKEN_KEY, null);
                return self::_deleteService($p_s_ws, $p_s_url, $p_a_parameters, $p_b_array, $p_i_attemp + 1);
            }

            throw new \Exception("HBS request failed: {$p_s_ws} (Code {$i_response_code})");
        }
        $o_temp_response = json_decode($s_response_str);
        if (!isset($o_temp_response)) {
            return (object) [
                        'code' => self::CODE_ERROR,
                        'msg' => $s_response_str
            ];
        } elseif (is_object($o_temp_response)) {
            if (isset($o_temp_response->code) AND $o_temp_response->code != self::CODE_SUCCESS) {
                return (object) [
                            'code' => $o_temp_response->code,
                            'msg' => $o_temp_response->msg
                ];
            }
        } else {
            
        } curl_close($o_curl);

        return json_decode($s_response_str, $p_b_array);
    }

    protected static function _uploadService($p_s_ws, $p_s_url, $p_s_filepath, $p_b_array = FALSE, $p_i_attemp = 0) {

        $o_curl = curl_init();
        curl_setopt_array($o_curl, array(
            CURLOPT_URL => $p_s_url . $p_s_ws,
            CURLOPT_POST => TRUE,
            CURLOPT_POSTFIELDS => [
                'file' => curl_file_create($p_s_filepath)
            ],
            CURLOPT_USERAGENT => "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:35.0) Gecko/20100101 Firefox/35.0",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                "Authorization: Bearer " . self::getToken()
            ]
        ));
        $s_response_str = curl_exec($o_curl);

        $i_response_code = curl_getinfo($o_curl, CURLINFO_HTTP_CODE);

        if ($i_response_code !== self::CODE_SUCCESS) {
            curl_close($o_curl);

            if ($i_response_code == self::CODE_UNAUTHORIZED && $p_i_attemp < self::MAX_ATTEMPS) {
                //Se intentará renovar el token sólo una vez
                Yii::app()->setGlobalState(self::HBS_TOKEN_KEY, null);
                return self::_uploadService($p_s_ws, $p_s_url, $p_s_filepath, $p_b_array, $p_i_attemp + 1);
            }

            throw new \Exception("WS FAILED: {$p_s_ws} (Code {$i_response_code})");
        }

        $o_temp_response = json_decode($s_response_str);
        if (!isset($o_temp_response)) {
            return (object) [
                        'code' => self::CODE_ERROR,
                        'msg' => $s_response_str
            ];
        } elseif (is_object($o_temp_response)) {
            if (isset($o_temp_response->code) AND $o_temp_response->code != self::CODE_SUCCESS) {
                return (object) [
                            'code' => $o_temp_response->code,
                            'msg' => $o_temp_response->msg
                ];
            }
        } else {
            
        }

        curl_close($o_curl);

        return json_decode($s_response_str, $p_b_array);
    }

    /**
     * Valida el mensaje obtenido desde HBS
     * @param object $p_o_response Respuesta
     * @return object Data
     * @throws \Exception
     */
    protected static function getValidatedMessage($p_o_response) {

        if (is_array($p_o_response)) {
            if (isset($p_o_response)) {
                if ($p_o_response['code'] == self::CODE_SUCCESS) {
                    if (!empty($p_o_response['data'])) {
                        return $p_o_response['data'];
                    }
                    return $p_o_response;
                } else {
                    throw new \Exception(isset($p_o_response['msg']) ? $p_o_response['msg'] : self::EXCEPTION_INVALID_MESSAGE, $p_o_response['code']);
                }
            } else {
                //Translate CEXC_UNKNOWN_ERROR: Error desconocido
                throw new \Exception('CEXC_UNKNOWN_ERROR', self::CODE_ERROR);
            }
        } else {
            if (isset($p_o_response)) {
                if ($p_o_response->code == self::CODE_SUCCESS) {
                    if (!empty($p_o_response->data)) {
                        return $p_o_response->data;
                    }
                    return $p_o_response;
                } else {
                    throw new \Exception(isset($p_o_response->msg) ? $p_o_response->msg : self::EXCEPTION_INVALID_MESSAGE, $p_o_response->code);
                }
            } else {
                //Translate CEXC_UNKNOWN_ERROR: Error desconocido
                throw new \Exception('CEXC_UNKNOWN_ERROR', self::CODE_ERROR);
            }
        }
    }

}
