<?php

class SIANLockerBox extends CWidget {

    public $id;
    public $model;
    public $attribute;
    public $title = 'Dimensiones Casillas';
    public $name_preffix = 'LockerBox';
    public $limit = 20;
    public $hint = false;
    public $class = '';
    public $class_panel = '';
    public $disabled = false;
    public $readonly = false;
    //PRIVATE
    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;
        //
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //Recorremos los ítems
        $a_lockerBox = [];
        foreach ($this->model->{$this->attribute} as $o_lockerbox) {
            $a_attributes = [];
            $a_attributes['box_number'] = isset($o_lockerbox->box_number) ? $o_lockerbox->box_number : '';
            $a_attributes['height'] = isset($o_lockerbox->height) ? $o_lockerbox->height : '';
            $a_attributes['width'] = isset($o_lockerbox->width) ? $o_lockerbox->width : '';
            $a_attributes['depth'] = isset($o_lockerbox->depth) ? $o_lockerbox->depth : '';
            $a_attributes['errors'] = $o_lockerbox->getAllErrors();

            $a_lockerBox[] = $a_attributes;
        }

        //Registramos script
        SIANAssets::registerScriptFile('js/sian-locker-box.js');

        Yii::app()->clientScript->registerScript($this->id, "

        //COUNT
        var divObj = $('#{$this->id}');
        divObj.data('count', 0);
        divObj.data('name_preffix', '{$this->name_preffix}');
        divObj.data('disabled', " . CJSON::encode($this->disabled) . ");
        divObj.data('readonly', " . CJSON::encode($this->readonly) . ");
        divObj.data('limit', " . CJSON::encode($this->limit) . ");
              
        var array = " . CJSON::encode($a_lockerBox) . ";
            
        if (array.length > 0)
        {
            for (var i = 0; i < array.length; i++)
            {
                SIANLockerBoxAddItem('{$this->id}', array[i]['box_number'], array[i]['height'], array[i]['width'], array[i]['depth'], array[i]['errors']);                
            }
        }
        else
        {
            $('#{$this->id}').html('<p>" . Strings::NO_DATA . "</p>');
        }
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->title . ($this->hint ? '*' : ''),
            'headerIcon' => 'envelope',
            'htmlOptions' => array(
                'class' => $this->class_panel . ' ' . ( isset($this->model, $this->attribute) && $this->model->hasErrors($this->attribute) ? 'us-error' : '')
            )
        ));

        echo "<div id='{$this->id}' class='{$this->class}'></div>";
        echo CHtml::link("<span class='fa fa-plus fa-lg black'></span> Agregar nuevo", null, array(
            'onclick' => "SIANLockerBoxAddItem('{$this->id}', '', '', '', '', [])",
            'title' => 'Agregar'
        ));

        if ($this->hint) {
            echo "<hr>";
            echo "<p>(*) {$this->hint}</p>";
        }
        $this->endWidget();
    }

}
