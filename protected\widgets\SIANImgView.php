<?php

class SIANImgView extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $box = true;
    public $cms = 'default';
    //PRIVATE

    private $controller;

    public function init() {

        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        SIANAssets::registerCssFile('css/sian-img-view.css');

        Yii::app()->clientScript->registerScript($this->id, "

        $(document).ready(function() {
        });
        ");
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if ($this->box) {
            $this->beginWidget('booster.widgets.TbPanel', array(
                'title' => $this->model->getAttributeLabel('tempImgs'),
                'headerIcon' => 'picture',
                'htmlOptions' => []
            ));
        }

        if (count($this->model->img) > 0) {

            $s_image_url = SIANImage::getMediumUrl($this->model->img->url, $this->cms);
            $s_image_title = isset($this->model->img->title) ? $this->model->img->title : Strings::NO_TITLE;
            $s_image_description = isset($this->model->img->description) ? $this->model->img->description : Strings::NO_DESCRIPTION;

            echo '<div class = "sian-img-view-flip-card">';
            echo '<div class = "sian-img-view-flip-card-inner">';
            echo '<div class = "sian-img-view-flip-card-front">';
            echo "<img class = 'sian-img-view-img-modal' src='{$s_image_url}'>";
            echo '</div>';
            echo '<div class = "sian-img-view-flip-card-back">';
            echo "<h1>{$s_image_title}</h1>";
            echo "<p class = 'sian-img-view-description'>{$s_image_description}</p>";
            echo '</div>';
            echo '</div>';
            echo '</div>';
        } else {
            echo Strings::NO_IMAGE;
        }
        if ($this->box) {
            $this->endWidget();
        }
    }

}
