function SIANBankAccountAddItem(div_id, entity_multi_id, type, currency, account_number, icc, errors)
{
    //DIV
    var divObj = $('#' + div_id);
    var count = parseInt(divObj.data('count'));
    var bank_items = divObj.data('bank_items');
    var type_items = divObj.data('type_items');
    var currency_items = divObj.data('currency_items');
    var name_preffix = divObj.data('name_preffix');
    var disabled = divObj.data('disabled');
    var readonly = divObj.data('readonly');
    var limit = divObj.data('limit');

    if (limit === false || count < parseInt(limit))
    {
        //ID
        var id = getLocalId();

        var html = '<div id=\'' + id + '\' class=\'row sian-bank-account-item\'>';

        html += '<div class=\'col-lg-2 col-md-4 col-sm-4 col-xs-12\'>';
        html += '<div class=\'form-group ' + (errors['entity_multi_id'] ? 'has-error' : '') + '\'>';
        html += '<select class=\'form-control sian-bank-account-item-entity-multi-id\' name=\'' + name_preffix + '[' + id + '][entity_multi_id]\' ' + (disabled ? 'disabled' : '') + '>' + getOptionsHtml(bank_items, entity_multi_id, true) + '</select>';
        if (errors['entity_multi_id'])
            html += '<span class=\'help-block error\'>' + errors['entity_multi_id'] + '</span>';
        html += '</div>';
        html += '</div>';

        html += '<div class=\'col-lg-2 col-md-4 col-sm-4 col-xs-12\'>';
        html += '<div class=\'form-group ' + (errors['type'] ? 'has-error' : '') + '\'>';
        html += '<select class=\'form-control sian-bank-account-item-type\' name=\'' + name_preffix + '[' + id + '][type]\' ' + (disabled ? 'disabled' : '') + '>' + getOptionsHtml(type_items, type, true) + '</select>';
        if (errors['type'])
            html += '<span class=\'help-block error\'>' + errors['type'] + '</span>';
        html += '</div>';
        html += '</div>';

        html += '<div class=\'col-lg-2 col-md-4 col-sm-4 col-xs-12\'>';
        html += '<div class=\'form-group ' + (errors['currency'] ? 'has-error' : '') + '\'>';
        html += '<select class=\'form-control sian-bank-account-item-currency\' name=\'' + name_preffix + '[' + id + '][currency]\' ' + (disabled ? 'disabled' : '') + '>' + getOptionsHtml(currency_items, currency, true) + '</select>';
        if (errors['currency'])
            html += '<span class=\'help-block error\'>' + errors['currency'] + '</span>';
        html += '</div>';
        html += '</div>';

        html += '<div class=\'col-lg-3 col-md-6 col-sm-6 col-xs-12\'>';
        html += '<div class=\'form-group ' + (errors['account_number'] ? 'has-error' : '') + '\'>';
        html += '<input class=\'form-control us-cleantext\' maxlength=\'25\' name=\'' + name_preffix + '[' + id + '][account_number]\' type=\'text\' placeholder=\'Número de Cuenta\' value=\'' + (account_number === null ? '' : account_number) + '\' ' + (readonly ? 'readonly' : '') + '>';
        if (errors['account_number'])
            html += '<span class=\'help-block error\'>' + errors['account_number'] + '</span>';
        html += '</div>';
        html += '</div>';

        html += '<div class=\'col-lg-3 col-md-6 col-sm-6 col-xs-12\'>';
        html += '<div class=\'form-group ' + (errors['icc'] ? 'has-error' : '') + '\'>';
        html += '<div class=\'input-group\'>';
        html += '<input class=\'form-control us-cleantext\' maxlength=\'25\' name=\'' + name_preffix + '[' + id + '][icc]\' type=\'text\' placeholder=\'CCI\' value=\'' + (icc === null ? '' : icc) + '\' ' + (readonly ? 'readonly' : '') + '>';
        html += '<span class=\'input-group-addon\'>';
        html += '<a onclick=\'SIANBankAccountRemoveItem(\"' + div_id + '\",\"' + id + '\", false)\' title=\'Eliminar Cuenta\'><span class=\'fa fa-times fa-lg black\'></span></a>';
        html += '</span>';
        html += '</div>';
        if (errors['icc'])
            html += '<span class=\'help-block error\'>' + errors['icc'] + '</span>';
        html += '</div>';
        html += '</div>';

        html += '</div>';
        //COUNT DE ITEMS
        if (count === 0)
        {
            divObj.html(html);
        } else
        {
            divObj.append(html);
        }
        divObj.data('count', count + 1);
    } else
    {
        bootbox.alert(us_message('El máximo permitido es ' + limit + ' cuenta(s) bancaria(s)!', 'warning'));
    }
}

function SIANBankAccountRemoveItem(div_id, id, confirmation)
{
    //DIV
    var divObj = $('#' + div_id);
    var count = parseInt(divObj.data('count'));
    var disabled = divObj.data('disabled');

    if (disabled)
    {
        bootbox.alert(us_message('No se puede "Eliminar" en este momento porque el componente está deshabilitado.', 'warning'));
        return;
    }

    if (confirmation ? confirm('¿Está seguro de eliminar este ítem?') : true)
    {
        $('#' + id).remove();

        if (count === 1)
        {
            divObj.html(STRINGS_NO_DATA);
        }

        //COUNT DE ITEMS
        divObj.data('count', count - 1);
    }
}