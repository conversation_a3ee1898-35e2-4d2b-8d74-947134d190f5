<?php

class SIANSublineFeatures extends CWidget {

    public $id;
    public $form;
    public $model;
    public $features;
    public $modal_id = null;
    public $maintenance;
    //PRIVATE
    private $controller;
    private $autocomplete_aux_id;
    private $autocomplete_value_id;
    private $autocomplete_text_id;
    private $alias_id;
    private $reset_id;
    private $add_id;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        //PRIVATE
        $this->autocomplete_aux_id = $this->controller->getServerId();
        $this->autocomplete_value_id = $this->controller->getServerId();
        $this->autocomplete_text_id = $this->controller->getServerId();
        $this->alias_id = $this->controller->getServerId();
        $this->reset_id = $this->controller->getServerId();
        $this->add_id = $this->controller->getServerId();

        //FEATURES
        $a_items = [];

        foreach ($this->features as $feature) {
            $a_item = [];
            $a_item['id'] = $feature['feature_id'];
            $a_item['label'] = $feature['feature_name'];
            $a_item['alias'] = $feature['alias'];
            $a_items[] = $a_item;
        }

        SIANAssets::registerScriptFile('js/sian-subline-features.js');
        SIANAssets::registerCssFile('css/sian-subline-features.css');

        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        $(document).ready(function() { 
            
            //LENAMOS LA TABLA
            var array = " . CJSON::encode($a_items) . ";
            var divObj = $('#{$this->id}');
            divObj.data('count', 0);
            divObj.data('update_url', '{$this->controller->createUrl('feature/update')}');
            divObj.data('modal_id', '{$this->modal_id}');
            
            SIANSublineFeaturesInit('{$this->id}', array);
        });
        

        $('body').on('click', '#{$this->add_id}', function(e){

            var id = $('#{$this->autocomplete_value_id}').val();
            var label = $('#{$this->autocomplete_text_id}').val();
            var alias = $('#{$this->alias_id}').val();
            
            if(id.length === 0)
            {
                bootbox.alert(us_message('Debe seleccionar un atributo', 'warning'));
                $('#{$this->autocomplete_aux_id}').focus();
                return false;
            }

            var data = {
                id: id,
                label: label,
                alias: alias,
            };

            SIANSublineFeaturesAddItem('{$this->id}', data);
            $('#{$this->reset_id}').click();
            $('#{$this->alias_id}').val(null);
            SIANSublineFeaturesIterate('{$this->id}');
            $('#{$this->autocomplete_aux_id}').focus();
        });
       
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div class='row'>";
        echo "<div class='col-lg-10 col-md-10 col-sm-10 col-xs-12'>";
        $this->widget('application.widgets.USAutocomplete', array(
            'name' => 'newItem',
            'placeholder' => 'Atributo',
            'parent_id' => $this->modal_id,
            'aux_id' => $this->autocomplete_aux_id,
            'value_id' => $this->autocomplete_value_id,
            'text_id' => $this->autocomplete_text_id,
            'reset_id' => $this->reset_id,
            'view' => array(
                'model' => 'Feature',
                'attributes' => array(
                    array('name' => 'feature_id', 'width' => 10, 'types' => array('id', 'value', 'aux'), 'not_in' => 'window.featureIds'),
                    array('name' => 'feature_name', 'width' => 90, 'types' => array('text')),
                    array('name' => 'alias', 'hidden' => true, 'update' => "$('#{$this->alias_id} ').val(alias);"),
                ),
            ),
            'maintenance' => $this->maintenance,));
        echo CHtml::hiddenField(null, null, array('id' => $this->alias_id));

        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_id,
            'context' => 'primary',
            'block' => true,
            'icon' => 'fa fa-lg fa-plus white',
            'size' => 'default',
            'title' => 'Añadir',
        ));
        echo "</div>";
        echo "</div>";
        echo "<hr>";
        echo "<div id='{$this->id}' class='row'>";
        echo "</div>";
        //
        echo "<div class='row'>";
        echo "<div class='col-lg-9 col-md-9 col-sm-6 col-xs-12'>";
        echo "<p><em>Para editar un atributo haga click en su nombre.</em></p>";
        echo "<p><em><b>Puede cambiar el orden de los atributos arrastrándolos.</b></em></p>";
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo SIANForm::checkBoxNonActive('Propagar a las mercaderías', 'Spread', true, [
        ]);
        echo "</div>";
        echo "</div>";
    }

}
