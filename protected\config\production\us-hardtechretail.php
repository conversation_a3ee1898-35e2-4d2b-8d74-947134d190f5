<?php

include(dirname(__FILE__) . DIRECTORY_SEPARATOR  . '..' . DIRECTORY_SEPARATOR . 'us-default.php');

$rdomain = 'hardtechretail.siansystem.com/admin';
$domain = 'https://hardtechretail.siansystem.com';
$domain2 = 'http://hardtechretail.siansystem.com';
$report_domain = 'rhardtechretail.siansystem.com';
$org = 'hardtechretail';
//SIAN 2
$domain_react_app = 'https://hardtechretail2.siansystem.com';
$api_sian = 'https://api.siansystem.com/';
//database enterprise
$database_server = '161.132.48.88';
$database_name = 'hardtechretail';
$database_username = 'sian';
$database_password = '75nppt6pf1gpje';
$mongo_enabled = false;
$mongo_server = '69.10.37.246';
$mongo_port = '27017';
$mongo_db = 'sian';
$mongo_username = 'sian';
$mongo_password = 'Sist3m@s';
//
$e_billing_ose = YII_OSE_EFACT;
$e_billing_env = YII_OSE_PROD;
$e_billing_certificate = 'hardtechretail.p12';
$e_billing_certificate_pass = 'aWxHiS3BsxU4LqEu';
$e_billing_ri = '';
$e_billing_credentials = [
    YII_OSE_SUNAT => [
        'username' => '20481066094BILLING7',
        'password' => 'EXITO123'
    ],
    YII_OSE_EFACT => [
        'username' => '20481066094',
        'password' => 'oP3yC3COQd'
    ]
];
//
$smtp_username = '<EMAIL>';
$smtp_password = '75wjexij43wz4g';
$environment_reports = YII_ENVIRONMENT_PRODUCTION;
$environment = YII_ENVIRONMENT_PRODUCTION;
