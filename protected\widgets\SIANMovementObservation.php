<?php

class SIANMovementObservation extends CWidget {

    public $id;
    public $form;
    public $model;
    public $title = 'Observación';
    public $rows = 3;
    //PRIVATE
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->title,
            'headerIcon' => 'asterisk'
        ));

        echo $this->form->textAreaRow($this->model, 'observation', array(
            'label' => $this->title,
            'id' => $this->id,
            'rows' => $this->rows,
            'maxlength' => 300,
            'required' => $this->model->isObservationRequired()
        ));
        $this->endWidget();
    }

}
