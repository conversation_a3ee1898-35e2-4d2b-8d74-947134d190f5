<?php

class USActionMenu extends CWidget {

    public $id;
    public $grid_id;
    public $buttons = [];
    public $nullDisplay = Strings::NONE;
    private $controller;

    public function init() {
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->normalize($this->id) : null;
        SIANAssets::registerScriptFile('js/us-action-menu.js');
        SIANAssets::registerCssFile('css/us-action-menu.css');
    }

    public function run() {
        if (empty($this->buttons)) {
            echo $this->nullDisplay;
            return;
        }

        echo "<button id='menu-toggle-" . $this->id . "' class='action-menu-button' type='button' title='Más Opciones'>
            <svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='currentColor' class='bi bi-three-dots-vertical' viewBox='0 0 16 16'>
                <path d='M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0'/>
            </svg>
        </button>";
        echo '<div class="action-menu-container">';
        echo '<ul id="menu-' . $this->id . '" class="action-menu">';

        foreach ($this->buttons as $button) {
            $button = $this->normalizeButton($button);
            $when = isset($button['when']) ? $button['when'] : 1;

            $params = null;
            if(!isset($button['params']['id'])){
                $params =array_merge($button['params'], ['id' => $this->id]);
            }else{
                $params = $button['params'];
            }

            if ($when) {
                echo '<li class="menu-item">';
                echo $this->widget('application.widgets.USLink', array(
                    'route' => isset($button['route']) ? $button['route'] : null,
                    'url' => isset($button['url']) ? $button['url'] : null,
                    'icon' => isset($button['icon']) ? $button['icon'] : null,
                    'subicons' => isset($button['subicons']) ? $button['subicons'] : [],
                    'color' => isset($button['color']) ? $button['color'] : 'black',
                    'label' => isset($button['title']) ? $button['title'] : null,
                    'title' => $button['title'],
                    'class' => isset($button['class']) ? $button['class'] : null,
                    'params' => $params,
                    'data' => $button['data'],
                    'jsonData' => $button['jsonData'],
                    'target' => isset($button['target']) ? $button['target'] : null,
                    'confirm' => isset($button['confirm']) ? $button['confirm'] : null,
                    'visible' => $button['visible'],
                    'onclick' => isset($button['onclick']) ? $button['onclick'] : null,
                ), true);
                echo '</li>';
            }
        }
        echo '</ul>';
        echo '</div>';
    }

    private function normalizeButton(array $button) {
        if (!isset($button['visible'])) {
            $button['visible'] = isset($button['route']) ? $this->controller->checkRoute($button['route']) : true;
        }
        $button['params'] = isset($button['params']) ? $button['params'] : [];
        $button['data'] = isset($button['data']) ? $button['data'] : [];
        $button['jsonData'] = isset($button['jsonData']) ? $button['jsonData'] : [];
        $button['data']['type'] = 'grid';
        $button['data']['element_id'] = $this->grid_id;
        return $button;
    }

    private function normalize($id) {
        if (is_array($id)) {
            return (count($id) > 0) ? implode('-', $id) : null;
        } else {
            return USString::unsetBlank($id);
        }
    }
}
?>