<?php

class SIANServicePrices extends CWidget {

    public $id;
    public $grid_id;
    public $form;
    public $model;
    public $modal_id = null;
    public $currency_items = [];
    public $readonly = false;
    public $measure_id = null;
    public $measure_items = [];
    public $custom_measure_items = [];
    public $item_types = [];
    public $store_items = [];
    public $category_items = [];
    //PRIVATE
    private $controller;
    private $price_mode_id;
    private $allow_decimals_id;
    private $cost_input_id;

    //IDS

    public function init() {

        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->grid_id = isset($this->grid_id) ? $this->grid_id : $this->controller->getServerId();
        //
        $this->price_mode_id = $this->controller->getServerId();
        $this->allow_decimals_id = $this->controller->getServerId();
        $this->cost_input_id = $this->controller->getServerId();
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-service.js');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->id, " 
        
        //Llamamos
        SIANPresentationStoreChangePriceMode('{$this->grid_id}', {$this->model->product->price_mode});                
        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        echo "<div id='{$this->id}'>";
        $this->renderGeneral();
        //Renderizamos presentación
        $this->renderPresentation();
        echo "</div>";
    }

    private function renderGeneral() {
        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => "Datos principales",
            'headerIcon' => 'asterisk',
            'htmlOptions' => array(
            )
        ));

        echo "<div class='row'>";
        echo "<div class='col-lg-9 col-md-9 col-sm-10 col-xs-12'>";
        echo $this->form->textFieldRow($this->model->product, 'product_name', []);
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-2 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model, 'service_category_id', $this->category_items, array());
        echo "</div>";
        echo "</div>";

        echo "<div class='row'>";
        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo $this->form->dropDownListRow($this->model->product, 'price_currency', $this->currency_items, [
            'onchange' => " var currency = $(this).val();
                            $('#{$this->grid_id}').data('changeCurrency')(currency);"]);
        echo '</div>';
        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo SIANForm::textFieldNonActive('TC', 'exchangeRate', Yii::app()->controller->getLastExchange(), ['readonly' => 1]);
        echo '</div>';
        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model->product, 'uref_cost_' . $this->controller->getOrganization()->globalVar->display_currency, array(
            'id' => $this->cost_input_id,
            'min' => 0,
            'step' => 0.01,
            'class' => 'us-double3',
            'hint' => '* Unitario.',
        ));
        echo "</div>";
        echo "<div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>";
        $this->widget('application.widgets.USSwitch', array(
            'id' => $this->price_mode_id,
            'model' => $this->model->product,
            'attribute' => 'price_mode',
            'switchChange' => "SIANPresentationStoreChangePriceMode('{$this->grid_id}', (this.checked ? 1 : 0));"
        ));
        echo "</div>";
        echo "</div>";
        //
        $this->endWidget();
    }

    private function renderPresentation() {

        $this->widget('application.widgets.SIANPresentationStore', array(
            'id' => $this->grid_id,
            'form' => $this->form,
            'model' => $this->model->product,
            'measure_id' => $this->measure_id,
            'measure_items' => $this->measure_items,
            'custom_measure_items' => $this->custom_measure_items,
            'cost' => 0,
            'store_items' => $this->store_items,
        ));
    }

}
