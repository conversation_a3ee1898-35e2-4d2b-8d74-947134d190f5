<?php

class USController extends USBaseController {

    public $title;
    public $breadcrumbs = [];

    public function init() {
        parent::init();

//        Yii::app()->attachEventHandler('onError', array($this, 'handleError'));
//        Yii::app()->attachEventHandler('onException', array($this, 'handleError'));
    }

    public function actions() {
        return array(
            // captcha action renders the CAPTCHA image displayed on the contact page
            'captcha' => array(
                'class' => 'CCaptchaAction',
                'backColor' => 0xFFFFFF,
            ),
        );
    }

    public function filters() {
        return array(
            'accessControl',
        );
    }

    protected function beforeAction($action) {


        $bootstrapScripts = [
            'bootstrap.min.css',
            'bootstrap-responsive.min.css',
            'bootstrap-yii.css',
            'jquery-ui-bootstrap.css',
            'bootstrap-notify.css',
            'bootstrap.notify.css',
            //JS
            "jquery.min.js",
            "jquery.js",
            "bootstrap.bootbox.min.js",
            "bootstrap.notify.js",
            "prettify.js",
            "bootstrap.min.js",
        ];

        $cs = Yii::app()->clientScript;
        $cs->scriptMap["jquery.yiigridview.js"] = false;
        if (Yii::app()->request->isAjaxRequest) {
            $this->layout = '//layouts/ajax';
            foreach ($bootstrapScripts as $script) {
                $cs->scriptMap[$script] = false;
            }
        }

        return parent::beforeAction($action);
    }

//    public function handleError(CEvent $event) {
//        if ($event instanceof CExceptionEvent) {
//            
//        } elseif ($event instanceof CErrorEvent) {
//            
//        }
//
//        //$event->handled = TRUE;
//    }

    function afterSuccess($element_id, $type, $model = null) {

        $html = "";
        if (isset($element_id)) {
            switch ($type) {
                case 'html':
                    $html = $this->afterSuccessHtml($element_id, $type, $model);
                    break;
                case 'string':
                    $html = $this->afterSuccessString($element_id, $type, $model);
                    break;
                case 'select':
                    $html = $this->afterSuccessSelect($element_id, $type, $model);
                    break;
                case 'val':
                    $html = $this->afterSuccessVal($element_id, $type, $model);
                    break;
                default:
                    break;
            }
        }
        return $html;
    }

    public function afterSuccessHtml($element_id, $type, $model = null) {
        return "";
    }

    public function afterSuccessString($element_id, $type, $model = null) {
        return $model->toString();
    }

    public function afterSuccessSelect($element_id, $type, $model = null) {
        return "";
    }

    public function afterSuccessVal($element_id, $type, $model) {
        return $model->primaryKey;
    }

    public function defaultEncode($success, $modal_id, $modal_html, $content_html, $type, $element_id, $model) {
        return CJSON::encode(array(
                    'success' => $success,
                    'modal_id' => $modal_id,
                    'modal_html' => $modal_html,
                    'content_html' => $content_html,
                    'type' => $type,
                    'element_id' => $element_id,
                    'primaryKey' => $model->primaryKey,
                    'toString' => $model->toString(),
        ));
    }

}
