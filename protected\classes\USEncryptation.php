<?php

class USEncryptation {

    const ENCRYPT_METHOD = "AES-256-CBC";
    const SECRET_KEY = 'This is my secret key';
    const SECRET_IV = 'This is my secret iv';

    public static function encrypt($string) {
        $key = hash('sha256', self::SECRET_KEY);
        $iv = substr(hash('sha256', self::SECRET_IV), 0, 16);

        return base64_encode(openssl_encrypt($string, self::ENCRYPT_METHOD, $key, 0, $iv));
    }

    public static function decrypt($string) {
        $key = hash('sha256', self::SECRET_KEY);
        $iv = substr(hash('sha256', self::SECRET_IV), 0, 16);

        return openssl_decrypt(base64_decode($string), self::ENCRYPT_METHOD, $key, 0, $iv);
    }

}
?>

