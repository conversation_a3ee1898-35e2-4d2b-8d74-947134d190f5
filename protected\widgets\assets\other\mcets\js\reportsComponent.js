/*  Variables: */
var s_all = "Todos";
var msg_warning = "Alerta";
var msgSelectAllOption = "Todos";

var domain = $("#rdomain").val();
var environment_reports = $("#environment_reports").val();

// Si es 30, significa que esta en entorno de TESTING : Local
var enviroment = environment_reports == '30' ? '/admin' : '/admin';

var a_monthNames = new Array('Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
        'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre');
var getYears = function () {
    var years = [];
    var minYear = 2012;
    var currentYear = new Date().getFullYear();
    while (currentYear >= minYear) {
        years.push(currentYear.toString());
        currentYear--;
    }
    return years;
}
var a_years = getYears();
var s_day = 'Día';
var s_range = 'Rango de Fechas';
var s_dateIni = 'F. Inicial';
var s_dateEnd = 'F. Final';
var s_month = 'Mes';
var s_year = 'Año';

var dateSystem = convertDate(new Date());

var a_movement = [];

var s_businessUnit = "Unidad de Negocio";
var s_division = "División";
var s_product_type = "Tipo de Producto";
var s_document_type = "Tipos de Documento";
var s_cashbox = "Caja o Banco";
var s_line = "Línea";
var s_subline = "Sub-Línea";
var s_merchandise = "Mercadería";
var s_product = "Producto";
var s_mark = "Marca";
var s_warehouse = "Almacén";
var s_owner = "Tipo Asociado";
var s_ownerId = "Asociado";
var s_documentSerie = "Serie de Documento";
var s_operation = "Operación";
var s_contact_mode = "Modo de contacto";
var s_state_commercial_case = "Estado del caso";
var s_state_commercial_case_detail = "Estado del detalle";
var s_modality_commercial_case_detail = "Modalidad";
var s_status = "Estado";
var s_active = "Activos";
var s_movement = "Documento";
var s_direction = "Dirección de Operación";
var s_employee = "Empleado";
var s_user = "Usuario";
var s_provider = "Proveedor";
var s_providerAlternate = "Proveedor Alterno";
var s_client = "Cliente";
var s_associated = "Asociado";
var s_clientAlternate = "Cliente Alterno";
var s_businesspartner = "Socio de Negocio";
var s_currency = "Moneda";
var s_paymentMethod = "Tipo de Pago";
var s_condition = "Condición";
var s_typePerson = "Tipo de Socio de Negocio";
var s_retentionAgent = "Agente de Retención";
var s_financialEntities = "Entidades Financieras";
var s_centerCost = "Centro de Costos";
var s_entries = "Asientos Contables";
var s_store = "Tienda/Oficina";
var s_seller = "Vendedor";
var s_buyer = "Usuario";
var s_technical = "Técnico";
var s_retention = "Retenciones de Ley";
var s_year = "Año";
var s_month = "Mes";
var s_department = "Departamento";
var s_project = "Proyecto";
var s_province = "Provincia";
var s_district = "Distrito";
var s_department = "Departamento";
var s_account = "Cuenta Contable";
var s_feature = "Característica";
var s_selectLine = "Seleccione una línea";
var s_selectFeature = "Seleccione una característica";
var s_agreement = "Convenio";
var s_SelectAgreement = "Seleccione un convenio";
var m_selectEntries = "Asiento Contable";
var m_selectBusinessUnit = "Seleccione una(s) unidad de negocio";
var m_selectDivision = "Seleccione una(s) división";
var m_selectProductType = "Seleccione uno(s) tipo de producto";
var m_selectDocumentType = "Seleccione un(os) tipos de documentos";
var m_selectLine = "Seleccione una(s) línea";
var m_selectSubline = "Seleccione una(s) sub-línea(s)";
var m_selectSublineSimple = "Seleccione un sub-línea";
var m_selectMerchandise = "Seleccione un(os) producto(s)";
var m_selectProduct = "Seleccione un(os) producto(s)";
var m_selectMark = "Seleccione una(s) marca(s)";
var m_selectPerson = "Seleccione una(s) persona(s)";
var m_selectEmployee = "Seleccione un(os) empleado(s)";
var m_selectUser = "Seleccione un(os) usuario(s)";
var m_selectProvider = "Seleccione un(os) proveedor(es)";
var m_selectClient = "Seleccione un(os) cliente(s)";
var m_selectAssociated = "Seleccione un(os) asociado(s)";
var m_selectBusinessPartner = "Seleccione un(os) socio(s) de negocio";
var m_selectCurrency = "Seleccione una(s) moneda(s)";
var m_selectPaymentMethod = "Seleccione un(os) tipo(s) de pago(s)";
var m_selectCondition = "Seleccione una(s) condición(es)";
var m_selectTypePerson = "Seleccione un(os) tipo(s)";
var m_selectSeller = "Seleccione un(os) vendedor(es)";
var m_selectTechnical = "Seleccione un(os) técnico(s)";
var m_selectDocumentSerie = "Seleccione una(s) Serie(s) de documento(s)";
var m_selectDocumentSerieSingular = "Seleccione una Serie de documento";
var m_selectRetentionAgent = "Seleccione un(os) agente(s) de retención";
var m_selectFinancialEntity = "Seleccione una(s) entidad(es) financiera(s)";
var m_selectCostCenter = "Seleccione el nivel ";
var m_selectStore = "Seleccione una(s) tienda(s)";
var m_selectWarehouse = "Seleccione un(os) almacén(es)";
var s_selectWarehouse = "Seleccione un almacén";
var s_selectOwner = "Seleccione un tipo de asociado";
var m_selectOwner = "Seleccione un asociado";
var m_selectOperation = "Seleccione una(s) operación(es)";
var m_selectContactMode = "Seleccione modo de contacto";
var m_selectCrmState = "Seleccione el estado";
var m_selectCrmModality = "Seleccione una modalidad";
var m_selectStatus = "Seleccione un(os) estado(s)";
var m_selectMovement = "Seleccione un(os) documento(s)";
var s_selectMovement = "Seleccione un documento";
var m_selectDirection = "Seleccione una(s) dirección(es)";
var s_selectYear = "Seleccione un año";
var s_selectMonth = "Seleccione un mes";
var m_selectMonth = "Seleccione un(os) mes(es)";
var m_selectYear = "Seleccione un(os) año(s)";
var m_selectDepartment = "Seleccione un(os) departamento(s)";
var m_selectProject = "Seleccione un(os) proyecto(s)";
var m_selectProvince = "Seleccione una(s) provincia(s)";
var m_selectDistrict = "Seleccione un(os) distrito(s)";
var s_writeSerie = "Escriba una serie";
var s_writeInventoryID = "Escriba el id del inventario";
var m_selectRetention = "Seleccione una(s) retención(es)";
var s_selectAccount = "Seleccione una cuenta contable";
var s_document_type = "Tipo de documento";
var m_selectDocumentType = "Ingrese tipo de documento";
var m_selectCashbox = "Ingrese caja o banco";
var m_selectRange = "Seleccione como máximo 31 días";
var m_selectAgreement = "Seleccione convenio(s)"

/* Fin de Variables */

/* Funciones Generales */

function getElement(tag) {
    return $(document.createElement(tag));
}

var loading_queue = [];
// funcion para mostrar el loader en los componentes
function showLoader() {
    var d = new Date();
    loading_queue.push(d.getTime());
    $(".loader-content-report").css("display", "block");
}
// funcion para ocultar el loader en los componentes
function hideLoader() {
    var e = loading_queue.pop();
    if (loading_queue.length === 0) {
        $(".loader-content-report").css("display", "none");
    }
}

function textSelect2(container) {
    var string = [].map.call($(container + " option:selected"), function (obj) {
        return obj.innerHTML;
    }).join(', ');
    return string;
}

// Funcion que establece al obj los valores del filtro de busqueda
function setValues_filterSearch_obj(obj, id_date_component, isActive = 0) {

    // El parametro isActive es para usar multiples componentes de calendario en una vista,
    // por eso se valida y se envía 1 pora el rango
    var type_search = (isActive == 0) ? $("input[name='type_search']:checked").val() :
            "1";

    // Variables locales para id de componentes de fecha (sólo se usan cuando existe mas de un componente de fecha en una vista)
    var val_c_date = (id_date_component == null) ? $("#c_date").val() : $("#c_date_" + id_date_component).val();
    var val_date_i = (id_date_component == null) ? $("#c_daterango_i").val() : $("#c_daterango_i_" + id_date_component).val();
    var val_date_e = (id_date_component == null) ? $("#c_daterango_e").val() : $("#c_daterango_e_" + id_date_component).val();

    if (type_search != null) {
        obj.search = type_search;
        if (type_search == "0") { //day
            obj.d = val_c_date;
        } else if (type_search == "1") {//range
            obj.sd = val_date_i;
            obj.ed = val_date_e;
        } else if (type_search == "2") {//month
            obj.y = $("#c_year").val();
            obj.m = $("#c_month").val();
            obj.month = $("#select2-c_month-container").text();
        }
    } else {
        var option = $('.c_filter_search').data('option');// solo 1 opcion
        obj.search = option;
        if (option == "0") {//day
            obj.d = val_c_date;
        } else if (option == "1") {//range
            obj.sd = val_date_i;
            obj.ed = val_date_e;
        } else if (option == "2") {//month
            obj.y = $("#c_year").val();
            obj.m = $("#c_month").val();
            obj.month = $("#select2-c_month-container").text();
        }
}

}

function build_radioButtons(ArrayObjet) {
    var $_div = getElement("div").attr("class", "radio");
    var $_input, $_label;
    for (var i = 0, max = ArrayObjet.length; i < max; i++) {
        if (ArrayObjet[i].default == "1") {
            $_input = getElement("input").attr({type: "radio", id: ArrayObjet[i].id, name: ArrayObjet[i].name, value: ArrayObjet[i].value, checked: "checked"});
        } else {
            $_input = getElement("input").attr({type: "radio", id: ArrayObjet[i].id, name: ArrayObjet[i].name, value: ArrayObjet[i].value});
        }
        $_label = getElement("label").attr("for", ArrayObjet[i].id).text(ArrayObjet[i].text);
        $_div.append($_input).append($_label);
    }
    return $_div;
}

function build_tagLabel(container, name) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(name);
    var $_element = getElement("select").attr({id: "c_Tag", multiple: "", name: "c_Tag", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $(container).html($_content);
    $("#c_Tag").select2({
        language: "en",
        placeholder: 'Ingrese un(as) ' + name + '(s)',
        tags: [],
        tokenSeparators: [',', ' ']
    });
}

function build_radioButtonsStandar(ArrayObjet, l) {
    var $_div = getElement("div").attr("class", "radio");
    var $_input, $_label, $_aux;
    for (var i = 0, max = l; i < max; i++) {
        $_aux = ArrayObjet[i].value;
        if (ArrayObjet[$_aux].hidden == null || ArrayObjet[$_aux].hidden == false) {
            if (ArrayObjet[$_aux].default == "1") {
                $_input = getElement("input").attr({type: "radio", id: ArrayObjet[$_aux].id, name: ArrayObjet[$_aux].name, value: ArrayObjet[$_aux].value, checked: "checked"});
            } else {
                $_input = getElement("input").attr({type: "radio", id: ArrayObjet[$_aux].id, name: ArrayObjet[$_aux].name, value: ArrayObjet[$_aux].value});
            }
            $_label = getElement("label").attr("for", ArrayObjet[$_aux].id).text(ArrayObjet[$_aux].text);
            $_div.append($_input).append($_label);
        }
    }
    return $_div;
}

function convertDate(inputFormat) {
    function pad(s) {
        return (s < 10) ? '0' + s : s;
    }
    var d = new Date(inputFormat);
    return [d.getFullYear(), pad(d.getMonth() + 1), pad(d.getDate())].join('-');
}

// LISTA DE TYPOS DE DOCUMENTOS - COMBOBOX MULTIPLE
function build_typeMovement_selectMultiple(container, type) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_typemovement);
    var $_element = getElement("select").attr({id: "c_typemovement", multiple: "", name: "c_typemovement", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listTypeMovement',
        data: {type: type},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    });

    $(container).html($_content);
    $("#c_typemovement").select2({
        language: "en",
        placeholder: m_selectypeMovement
    });
}

/* Fin de Funciones Generales */

/* Componentes */

function build_componentGeneral_radioButton(container, list, s_label) {
    var $_radio = build_radioButtons(list);
    var $_component = getElement("div").addClass("component").
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component lbl-c-main  ").text(s_label + " :")).
                    append(getElement("div").addClass(" ").append($_radio)));
    $(container).append($_component);
    $(".radio").buttonset();
}

function build_componentGeneral_selectSimple(container, component, list, s_label) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main ").text(s_label + " :");
    var $_element = getElement("select").attr({id: component, name: component, style: "width: 100%;"});

    for (i = 0; i < list.length; i++) {
        $_element.append(getElement("option").attr("value", list[i].value).text(list[i].text));
    }

    $_content.append($_label).append($_element);
    $(container).html($_content);
    $("#" + component).select2({language: "en"});
}

// LISTA DE DIVISIONES - COMBOBOX SIMPLE
function build_division_selectSimple(container, isAll) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_division + " :");
    var $_element = getElement("select").attr({id: "c_division", name: "c_division", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listDivision',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            if (l > 0) {
                if (isAll == 1) {
                    $_element.append(getElement("option").attr("value", 0).text(msgSelectAllOption));
                }
                for (i = 0; i < l; i++) {
                    $_element.append(getElement("option").attr("value", data[i].id).text(data[i].value));
                }
            }
        }
    });

    $(container).html($_content);
    $("#c_division").select2({
        language: "en",
        placeholder: m_selectDivision
    });
}

// LISTA DE TIPOS DE DOCUMENTO - COMBOBOX SIMPLE
function build_documentType_selectSimple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_document_type + " :");
    var $_element = getElement("select").attr({id: "c_document_type", name: "c_document_type", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listDocumentType',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                $("#c_document_type").append(getElement("option"))
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    })

    $(container).html($_content);

    $("#c_document_type").select2({
        language: "en",
        placeholder: m_selectDocumentType
    });
}

// LISTA DE CAJAS Y BANCOS - COMBOBOX MULTIPLE
function build_cashbox_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_cashbox + " :");
    var $_element = getElement("select").attr({id: "c_cashbox", name: "c_cashbox", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listCashbox',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                $_element.append(getElement("option").attr("value", 0).text("-- Todas las Cuentas --"));
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    })
    $(container).html($_content);
    $("#c_cashbox").select2({
        language: "en",
        placeholder: m_selectCashbox
    });
}

// LISTA DE TIPOS DE DOCUMENTO - COMBOBOX SIMPLE
function build_documentType_selectSimple(container, type, direction, isElectronic, isAccounting, isSerialized) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_document_type + " :");
    var $_element = getElement("select").attr({id: "c_document_type", name: "c_document_type", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listDocumentType',
        data: {type: type, direction: direction, isElectronic: isElectronic, isAccounting: isAccounting, isSerialized: isSerialized},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    })

    $(container).html($_content);

    $("#c_document_type").select2({
        language: "en",
        placeholder: m_selectDocumentType
    });
}

// LISTA DE TIPOS DE DOCUMENTO - COMBOBOX MULTIPLE
function build_documentType_selectMultiple(container, type, direction, isElectronic, isAccounting, isSerialized, origin) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_document_type + " :");
    var $_element = getElement("select").attr({id: "c_document_type", multiple: "", name: "c_document_type", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    var s_url = enviroment + '/components/listDocumentType';
    if (origin == 'Movement') {
        s_url = enviroment + '/components/listDocumentTypeFromMovement';
    }

    $.ajax({
        url: s_url,
        data: {type: type, direction: direction, isElectronic: isElectronic, isAccounting: isAccounting, isSerialized: isSerialized},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    })

    $(container).html($_content);

    $("#c_document_type").select2({
        language: "en",
        placeholder: m_selectDocumentType
    });
}


// LISTA DE ASIENTOS - COMBOBOX MULTIPLE
function build_entries_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_entries);
    var $_element = getElement("select").attr({id: "c_entries", multiple: "", name: "c_entries", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listEntries',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    });

    $(container).html($_content);
    $("#c_entries").select2({
        language: "en",
        placeholder: m_selectEntries
    });
}

// LISTA DE DIVISIONES - COMBOBOX MULTIPLE
function build_division_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_division);
    var $_element = getElement("select").attr({id: "c_division", multiple: "", name: "c_division", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listDivision',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    });

    $(container).html($_content);
    $("#c_division").select2({
        language: "en",
        placeholder: m_selectDivision
    });
}

// LISTA DE LINEAS - COMBOBOX MULTIPLE
function build_checkbox(container, name) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(name + ": ");
    var $_element = build_checkBoxComponent("chk_" + name.replaceAll(' ', ''));
    $_content.append($_label).append($_element);
    $(container).html($_content);
}

// LISTA DE LINEAS - COMBOBOX MULTIPLE
function build_line_selectMultiple(container, idsDivision) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_line);
    var $_element = getElement("select").attr({id: "c_line", multiple: "", name: "c_line", style: "width: 100%;"});
    $_content.append($_label).append($_element);
    var queryURL = enviroment + '/components/listLine';

    $(container).html($_content);

    $("#c_line").select2({
        language: "en",
        placeholder: m_selectLine,
        //minimumInputLength: 3,
        ajax: {
            url: queryURL,
            type: 'post',
            delay: 250,
            dataType: 'json',
            data: function (params) {
                return{
                    q: params.term,
                    idsDivision: idsDivision
                };
            },
            processResults: function (data) {
                return{
                    results: $.map(data.data, function (obj) {
                        return{id: obj.id, text: obj.value};
                    })
                };
            }, cache: true,
        }
    });
}

// LISTA DE SUBLINEAS - COMBOBOX MULTIPLE
function build_subline_selectMultiple(container, idsLine) {

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_subline);
    var $_element = getElement("select").attr({id: "c_subline", multiple: "", name: "c_subline", style: "width: 100%;"});
    $_content.append($_label).append($_element);
    var queryURL = enviroment + '/components/listSubline';

    $(container).html($_content);

    $("#c_subline").select2({
        language: "en",
        placeholder: m_selectSubline,
        //minimumInputLength: 3,
        ajax: {
            url: queryURL,
            type: 'post',
            delay: 250,
            dataType: 'json',
            data: function (params) {
                return{
                    q: params.term,
                    idsLine: idsLine
                };
            },
            processResults: function (data) {
                return{
                    results: $.map(data.data, function (obj) {
                        return{id: obj.id, text: obj.value};
                    })
                };
            }, cache: true,
        }
    });
}

// LISTA DE SUBLINEAS - COMBOBOX MULTIPLE
function build_itemType_selectMultiple(container) {

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_product_type);
    var $_element = getElement("select").attr({id: "c_itemType", multiple: "", name: "c_itemType", style: "width: 100%;"});
    $_content.append($_label).append($_element);
    var queryURL = enviroment + '/components/listItemType';

    $(container).html($_content);

    $("#c_itemType").select2({
        language: "en",
        placeholder: m_selectProductType,
        //minimumInputLength: 3,
        ajax: {
            url: queryURL,
            type: 'post',
            delay: 250,
            dataType: 'json',
            data: function (params) {
                return{
                    q: params.term
                };
            },
            processResults: function (data) {
                return{
                    results: $.map(data.data, function (obj) {
                        return{id: obj.id, text: obj.value};
                    })
                };
            }, cache: true,
        }
    });
}

// LISTA DE SUBLINEAS - COMBOBOX MULTIPLE
function build_subline_selectSimple(container, idsLine) {

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_subline);
    var $_element = getElement("select").attr({id: "c_subline", name: "c_subline", style: "width: 100%;"});
    $_content.append($_label).append($_element);
    var queryURL = enviroment + '/components/listSubline';

    $(container).html($_content);

    $("#c_subline").select2({
        language: "en",
        placeholder: m_selectSublineSimple,
        minimumInputLength: 2,
        ajax: {
            url: queryURL,
            type: 'post',
            delay: 250,
            dataType: 'json',
            data: function (params) {
                return{
                    q: params.term,
                    idsLine: idsLine
                };
            },
            processResults: function (data) {
                return{
                    results: $.map(data.data, function (obj) {
                        return{id: obj.id, text: obj.value};
                    })
                };
            }, cache: true,
        }
    });
}

function build_product_type_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_product_type);
    var $_element = getElement("select").attr({id: "c_product_type", multiple: "", name: "c_product_type", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listProductType',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    });

    $(container).html($_content);
    $("#c_product_type").select2({
        language: "en",
        placeholder: m_selectProductType
    });
}

// LISTA DE ALMACENES - COMBOBOX SIMPLE
function build_feature_by_subline_selectSimple(container, idSubline) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_feature);
    var $_element = getElement("select").attr({id: "c_feature", name: "c_feature", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listFeatureBySubline',
        data: {idSubline: idSubline},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            $_element.append(getElement("option").attr("value", 0).text("- Seleccione una opción -"));
            if (l > 0) {
                for (i = 0; i < l; i++) {
                    $_element.append(getElement("option").attr("value", data[i].id).text(data[i].value));
                }
            }

            $(container).html($_content);

            $("#c_feature").select2({
                language: "en",
                placeholder: s_selectFeature
            });
        }
    });
}

// LISTA DE MERCADERIAS - COMBOBOX MULTIPLE
function build_merchandise_selectMultiple(container, idsSubline, idsLine) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_product);
    var $_element = getElement("select").attr({id: "c_merchandise", multiple: "", name: "c_merchandise", style: "width: 100%;"});
    $_content.append($_label).append($_element);
    var queryURL = enviroment + '/components/listMerchandise';

    $(container).html($_content);

    $("#c_merchandise").select2({
        language: "en",
        placeholder: m_selectProduct,
        //minimumInputLength: 3,
        ajax: {
            url: queryURL,
            type: 'post',
            delay: 250,
            dataType: 'json',
            data: function (params) {
                return{
                    q: params.term,
                    idsSubline: idsSubline,
                    idsLine: idsLine
                };
            },
            processResults: function (data) {
                return{
                    results: $.map(data.data, function (obj) {
                        return{id: obj.id, text: obj.value};
                    })
                };
            }, cache: true,
        }
    });
}

// LISTA DE PRODUCTOS - COMBOBOX MULTIPLE
function build_product_selectMultiple(container, include_combos) {

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_product);
    var $_element = getElement("select").attr({id: "c_product", multiple: "", name: "c_product", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    var queryURL = enviroment + '/components/listProduct';
    if (include_combos == 1) {
        queryURL = enviroment + '/components/listProductAll';
    }

    $(container).html($_content);

    $("#c_product").select2({
        language: "en",
        placeholder: m_selectProduct,
        //minimumInputLength: 3,
        ajax: {
            url: queryURL,
            type: 'post',
            delay: 250,
            dataType: 'json',
            data: function (params) {
                return{
                    q: params.term,
                };
            },
            processResults: function (data) {
                return{
                    results: $.map(data.data, function (obj) {
                        return{id: obj.id, text: obj.value};
                    })
                };
            }, cache: true,
        }
    });
}


// LISTA DE ALMACEN - COMBOBOX MULTIPLE
function build_warehouse_selectMultiple(container, incoming, outgoing, commercial_treatment, warehouse_types, name, title, hint, store_ids, business_unit_ids) {

    if (name == null) {
        name = 'c_warehouse';
    }
    if (title == null) {
        title = s_warehouse
    }
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(title);
    var $_element = getElement("select").attr({id: name, multiple: "", name: name, style: "width: 100%;"});
    var $_hint = "";
    if (hint != null && hint != "") {
        $_hint = getElement("span").addClass("lbl-component-simple").text(hint);
    }
    $_content.append($_label).append($_element).append($_hint);
    $.ajax({
        url: enviroment + '/components/listWarehouse',
        data: {
            incoming: incoming,
            outgoing: outgoing,
            commercial_treatment: commercial_treatment,
            warehouse_types: warehouse_types,
            store_ids: store_ids,
            business_unit_ids: business_unit_ids
        },
        async: false,
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    });

    $(container).html($_content);
    $("#" + name).select2({
        language: "en",
        placeholder: m_selectWarehouse
    });
}

// LISTA DE ALMACEN - COMBOBOX MULTIPLE
function build_warehouse2_selectMultiple(container, active) {
    console.log("container", container);
    console.log("active", active);

    var $_inputActive = getElement('input').attr('type', 'checkbox').attr('id', 'c_warehouse_active').prop('checked', true);
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(s_warehouse);
    var $_labelActive = getElement("label").addClass("lbl-component lbl-s-active").text(s_active).append($_inputActive);
    var $_element = getElement("select").attr({id: "c_warehouse", multiple: "", name: "c_warehouse", style: "width: 100%;"});

    if (typeof active != 'undefined') {
        $_content.append($_label).append($_element);
    } else {
        $_content.append($_label).append($_labelActive).append($_element);
    }

    var ajaxWarehouse = function (active) {
//        console.log("ruta", enviroment + '/components/listWarehouse2');
        $.ajax({
            url: enviroment + '/components/listWarehouse2',
            type: 'post',
            data: {active: active},
            dataType: 'json',
            beforeSend: function () {
                showLoader();
                if ($("#c_warehouse").data('select2')) {
                    $("#c_warehouse").select2('destroy');
                    $("#c_warehouse").empty();
                }
                window.active_ajax++;
            },
            complete: function () {
                hideLoader();
                $("#c_warehouse").select2({
                    language: "en",
                    placeholder: m_selectWarehouse,
                    minimumInputLength: 3
                });
            },
            success: function (response) {
                var data = response.data;
                var l = data.length;
                if (l > 0) {
                    for (var i = 0, max = l; i < max; i++) {
                        var inactiveText = data[i].status === '0' ? data[i].value + ' (I)' : data[i].value;
                        $("#c_warehouse").append(getElement("option").attr({value: data[i].id}).text(inactiveText));
                    }
                }
                window.active_ajax--;
            },
            error: function (request, status, error) {
                window.active_ajax--;
            }
        });
    }
    //if(typeof active != 'undefined'){
    $_inputActive.on('click', function () {
        var active = $(this).prop('checked') === true ? 1 : 0;
        $(this).val(active);
        ajaxWarehouse(active);
    });

    $_inputActive.val($($_inputActive).prop('checked') == true ? 1 : 0)
    typeof active != 'undefined' ? ajaxWarehouse(0) : ajaxWarehouse(1);

    $(container).html($_content);
    $("#c_warehouse").select2({
        language: "en",
        placeholder: m_selectWarehouse,
        minimumInputLength: 3,
    });
}

// LISTA DE ALMACENES - COMBOBOX SIMPLE
function build_warehouse_selectSimple(container, incoming, outgoing, commercial_treatment) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_warehouse);
    var $_element = getElement("select").attr({id: "c_warehouse", name: "c_warehouse", style: "width: 100%;"});
    $_content.append($_label).append($_element);


    $.ajax({
        url: enviroment + '/components/listWarehouse',
        data: {incoming: incoming, outgoing: outgoing, commercial_treatment: commercial_treatment},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            if (l > 0) {
                for (i = 0; i < l; i++) {
                    $_element.append(getElement("option").attr("value", data[i].id).text(data[i].value));
                }
            }

            $(container).html($_content);

            $("#c_warehouse").select2({
                language: "en",
                placeholder: s_selectWarehouse
            });
        }
    });
}

// LISTA DE OPERACIONES - COMBOBOX MULTIPLE
function build_operation_selectMultiple(container, type, isAll, accountingFileId, module) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_operation);
    var $_element = getElement("select").attr({id: "c_operation", multiple: "", name: "c_operation", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listOperation',
        data: {type: type, accountingFileId: accountingFileId, module: module},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                if (isAll == 1) {
                    $_element.append(getElement("option").attr("value", 0).text(msgSelectAllOption));
                }
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    });
    $(container).html($_content);
    $("#c_operation").select2({
        language: "en",
        placeholder: m_selectOperation
    });
}

// LISTA DE OPERACIONES - COMBOBOX MULTIPLE
function build_operation_byRoute_selectMultiple(container, route) {

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_operation);
    var $_element = getElement("select").attr({id: "c_operation2", multiple: "", name: "c_operation2", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listOperationByRoute',
        data: {route: route},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
//                if (isAll == 1) {
//                    $_element.append(getElement("option").attr("value", 0).text(msgSelectAllOption));
//                }
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    });

    $(container).html($_content);
    $("#c_operation2").select2({
        language: "en",
        placeholder: m_selectOperation
    });
}

// LISTA DE DIRECCIÓN DE MOVIMIENTOS - COMBOBOX MULTIPLE
function build_direction_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_direction);
    var $_element = getElement("select").attr({id: "c_direction", multiple: "", name: "c_direction", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listDirection',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    });

    $(container).html($_content);
    $("#c_direction").select2({
        language: "en",
        placeholder: m_selectDirection
    });
}

// LISTA DE EMPLEADOS - COMBOBOX MULTIPLE
function build_employee_selectMultiple(container, label) {
    label = (label == null ? s_employee : label);
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(label);
    var $_element = getElement("select").attr({id: "c_employee", multiple: "", name: "c_employee", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listEmployee',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_employee").select2({
        language: "en",
        placeholder: m_selectEmployee,
        minimumInputLength: 3
    });
}

// LISTA DE USUARIOS - COMBOBOX MULTIPLE
function build_user_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_user);
    var $_element = getElement("select").attr({id: "c_user", multiple: "", name: "c_user", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listUser',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_user").select2({
        language: "en",
        placeholder: m_selectUser,
    });
}

// LISTA DE SERIE DE DOCUMENTOS - COMBOBOX SIMPLE
function build_documentSerie_selectSimple(container, idDocumentType) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-xs-8").text(s_documentSerie + ' :');
    var $_element = getElement("select").attr({id: "c_documentSerie", name: "c_documentSerie", style: "width: 100%;"});


    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listDocumentSerie',
        type: 'post',
        data: {idDocumentType: idDocumentType},
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();

        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            if (l > 0) {
                $("#c_documentSerie").append(getElement("option"))
                for (i = 0; i < l; i++) {
                    var inactiveText = data[i].status === '0' ? data[i].value + ' (I)' : data[i].value;
                    $("#c_documentSerie").append(getElement("option").attr({value: data[i].id}).text(inactiveText));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);

    $("#c_documentSerie").select2({
        language: "en",
        placeholder: m_selectDocumentSerieSingular
    });
}
// LISTA DE SERIE DE DOCUMENTOS - COMBOBOX MULTIPLE
function build_documentSerie_selectMultiple(container, idDocumentType) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-xs-8").text(s_documentSerie + ' :');
    var $_element = getElement("select").attr({id: "c_documentSerie", multiple: "", name: "c_documentSerie", style: "width: 100%;"});


    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listDocumentSerie',
        type: 'post',
        data: {idDocumentType: idDocumentType},
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            if (l > 0) {
                $("#c_documentSerie").append(getElement("option"))
                for (i = 0; i < l; i++) {
                    var inactiveText = data[i].status === '0' ? data[i].value + ' (I)' : data[i].value;
                    $("#c_documentSerie").append(getElement("option").attr({value: data[i].id}).text(inactiveText));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);

    $("#c_documentSerie").select2({
        language: "en",
        placeholder: m_selectDocumentSerieSingular
    });
}

// LISTA DE VENDEDORES - COMBOBOX MULTIPLE
function build_seller_selectMultiple(container, data_user = null, active) {
    var $_inputActive = getElement('input').attr('type', 'checkbox').attr('id', 'c_seller_active').prop('checked', true);
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(s_seller);
    var $_labelActive = getElement("label").addClass("lbl-component lbl-s-active").text(s_active).append($_inputActive);
    var $_element = getElement("select").attr({id: "c_seller", multiple: "", name: "c_seller", style: "width: 100%;"});
    if (typeof active != 'undefined') {
        $_content.append($_label).append($_element);
    } else {
        $_content.append($_label).append($_labelActive).append($_element);
    }
    var ajaxSeller = function (active) {
        $.ajax({
            url: enviroment + '/components/listSeller',
            type: 'post',
            data: {active: active},
            dataType: 'json',
            beforeSend: function () {
                showLoader();
                if ($("#c_seller").data('select2')) {
                    $("#c_seller").select2('destroy');
                    $("#c_seller").empty();
                }
                window.active_ajax++;
            },
            complete: function () {
                hideLoader();
                $("#c_seller").select2({
                    language: "en",
                    placeholder: m_selectSeller,
                    minimumInputLength: 3
                });
            },
            success: function (response) {
                var data = response.data;
                var l = data.length;
                if (l > 0) {
                    for (var i = 0, max = l; i < max; i++) {
                        var inactiveText = data[i].status === '0' ? data[i].value + ' (I)' : data[i].value;
                        $("#c_seller").append(getElement("option").attr({value: data[i].id}).text(inactiveText));
                    }
                }
                window.active_ajax--;
            },
            error: function (request, status, error) {
                window.active_ajax--;
            }
        });
    }
    //if(typeof active != 'undefined'){
    $_inputActive.on('click', function () {
        var active = $(this).prop('checked') === true ? 1 : 0;
        $(this).val(active);
        ajaxSeller(active);
    });
    $_inputActive.val($($_inputActive).prop('checked') == true ? 1 : 0)

    if (data_user === null) {
        typeof active != 'undefined' ? ajaxSeller(0) : ajaxSeller(1);
    } else {
        if (data_user.user_level != 1) {
            typeof active != 'undefined' ? ajaxSeller(0) : ajaxSeller(1);
        } else {
            $_content.html('');
            var $_inputseller = getElement('input').attr({
                type: "text",
                id: "txt_name_seller",
                class: "form-control",
                value: data_user.user_name,
                readonly: "readonly"
            });
            $_inputseller.css("height", "32px");

            $_content.append($_label).append($_inputseller);

        }
    }
    //}

    $(container).html($_content);

    $("#c_seller").select2({
        language: "en",
        placeholder: m_selectSeller,
        minimumInputLength: 3,
    });
}

// LISTA DE VENDEDORES - COMBOBOX MULTIPLE
function build_seller2_selectMultiple(container, active) {
    var $_inputActive = getElement('input').attr('type', 'checkbox').attr('id', 'c_seller_active').prop('checked', true);
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(s_seller);
    var $_labelActive = getElement("label").addClass("lbl-component lbl-s-active").text(s_active).append($_inputActive);
    var $_element = getElement("select").attr({id: "c_seller", multiple: "", name: "c_seller", style: "width: 100%;"});
    if (typeof active != 'undefined') {
        $_content.append($_label).append($_element);
    } else {
        $_content.append($_label).append($_labelActive).append($_element);
    }
    var ajaxSeller = function (active) {
        $.ajax({
            url: enviroment + '/components/listSeller2',
            type: 'post',
            data: {active: active},
            dataType: 'json',
            beforeSend: function () {
                showLoader();
                if ($("#c_seller").data('select2')) {
                    $("#c_seller").select2('destroy');
                    $("#c_seller").empty();
                }
                window.active_ajax++;
            },
            complete: function () {
                hideLoader();
                $("#c_seller").select2({
                    language: "en",
                    placeholder: m_selectSeller,
                    minimumInputLength: 3
                });
            },
            success: function (response) {
                var data = response.data;
                var l = data.length;
                if (l > 0) {
                    for (var i = 0, max = l; i < max; i++) {
                        var inactiveText = data[i].status === '0' ? data[i].value + ' (I)' : data[i].value;
                        $("#c_seller").append(getElement("option").attr({value: data[i].id}).text(inactiveText));
                    }
                }
                window.active_ajax--;
            },
            error: function (request, status, error) {
                window.active_ajax--;
            }
        });
    }
    //if(typeof active != 'undefined'){
    $_inputActive.on('click', function () {
        var active = $(this).prop('checked') === true ? 1 : 0;
        $(this).val(active);
        ajaxSeller(active);
    });
    $_inputActive.val($($_inputActive).prop('checked') == true ? 1 : 0)

//    if (data_user === null) {
//        typeof active != 'undefined' ? ajaxSeller(0) : ajaxSeller(1);
//    } else {
//        if (data_user.user_level != 1) {
//            typeof active != 'undefined' ? ajaxSeller(0) : ajaxSeller(1);
//        } else {
//            $_content.html('');
//            var $_inputseller = getElement('input').attr({
//                type: "text",
//                id: "txt_name_seller",
//                class: "form-control",
//                value: data_user.user_name,
//                readonly: "readonly"
//            });
//            $_inputseller.css("height", "32px");
//
//            $_content.append($_label).append($_inputseller);
//
//        }
//    }
    //}

    $(container).html($_content);

    $("#c_seller").select2({
        language: "en",
        placeholder: m_selectSeller,
        minimumInputLength: 3,
    });
}


// LISTA DE COMPRADORES - COMBOBOX MULTIPLE
function build_buyer_selectMultiple(container, active) {
    var $_inputActive = getElement('input').attr('type', 'checkbox').attr('id', 'c_buyer_active').prop('checked', true).css({'margin': '0px'});
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(s_buyer);
    var $_labelActive = getElement("label").addClass("lbl-component lbl-s-active").text(s_active).append($_inputActive);
    var $_element = getElement("select").attr({id: "c_buyer", multiple: "", name: "c_buyer", style: "width: 100%;"});
    if (typeof active != 'undefined') {
        $_content.append($_label).append($_element);
    } else {
        $_content.append($_label).append($_labelActive).append($_element);
    }

    var ajaxBuyer = function (active) {
        $.ajax({
            url: enviroment + '/components/listBuyer',
            type: 'post',
            data: {active: active},
            dataType: 'json',
            beforeSend: function () {
                showLoader();
                if ($("#c_buyer").data('select2')) {
                    $("#c_buyer").select2('destroy');
                    $("#c_buyer").empty();
                }
                window.active_ajax++;
            },
            complete: function () {
                hideLoader();
                $("#c_buyer").select2({
                    language: "en",
                    placeholder: m_selectUser,
                    minimumInputLength: 2
                });
            },
            success: function (response) {
                var data = response.data;
                var l = data.length;
                if (l > 0) {
                    for (var i = 0, max = l; i < max; i++) {
                        var inactiveText = data[i].status === '0' ? data[i].value + ' (I)' : data[i].value;
                        $("#c_buyer").append(getElement("option").attr({value: data[i].id}).text(inactiveText));
                    }
                }
                window.active_ajax--;
            },
            error: function (request, status, error) {
                window.active_ajax--;
            }
        });
    }

    //if(typeof active != 'undefined'){
    $_inputActive.on('click', function () {
        var active = $(this).prop('checked') === true ? 1 : 0;
        $(this).val(active);
        ajaxBuyer(active);
    });
    $_inputActive.val($($_inputActive).prop('checked') === true ? 1 : 0);
    typeof active != 'undefined' ? ajaxBuyer(0) : ajaxBuyer(1);

    $(container).html($_content);

    $("#c_buyer").select2({
        language: "en",
        placeholder: m_selectUser,
        minimumInputLength: 2,
    });
}


// LISTA DE TECNICOS - COMBOBOX MULTIPLE
function build_technical_selectMultiple(container, data_user = null, active) {
    var $_inputActive = getElement('input').attr('type', 'checkbox').attr('id', 'c_technical_active').prop('checked', true);
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(s_technical);
    var $_labelActive = getElement("label").addClass("lbl-component lbl-s-active col-xs-6").text(s_active).css("margin-top", "0px").append($_inputActive);
    var $_element = getElement("select").attr({id: "c_technical", multiple: "", name: "c_technical", style: "width: 100%;"});
    if (typeof active != 'undefined') {
        $_content.append($_label).append($_element);
    } else {
        $_content.append($_label).append($_labelActive).append($_element);
    }
    var ajaxSeller = function (active) {
        $.ajax({
            url: enviroment + '/components/listTechnical',
            type: 'post',
            data: {active: active},
            dataType: 'json',
            beforeSend: function () {
                showLoader();
                if ($("#c_technical").data('select2')) {
                    $("#c_technical").select2('destroy');
                    $("#c_technical").empty();
                }
                window.active_ajax++;
            },
            complete: function () {
                hideLoader();
                $("#c_technical").select2({
                    language: "en",
                    placeholder: m_selectTechnical,
                });
            },
            success: function (response) {
                var data = response.data;
                var l = data.length;
                if (l > 0) {
                    for (var i = 0, max = l; i < max; i++) {
                        var inactiveText = data[i].status === '0' ? data[i].value + ' (I)' : data[i].value;
                        $("#c_technical").append(getElement("option").attr({value: data[i].id}).text(inactiveText));
                    }
                }
                window.active_ajax--;
            },
            error: function (request, status, error) {
                window.active_ajax--;
            }
        });
    }
    //if(typeof active != 'undefined'){
    $_inputActive.on('click', function () {
        var active = $(this).prop('checked') === true ? 1 : 0;
        $(this).val(active);
        ajaxSeller(active);
    });
    $_inputActive.val($($_inputActive).prop('checked') === true ? 1 : 0)

    if (data_user === null) {
        typeof active != 'undefined' ? ajaxSeller(0) : ajaxSeller(1);
    } else {
        if (data_user.user_level != 1) {
            typeof active != 'undefined' ? ajaxSeller(0) : ajaxSeller(1);
        } else {
            $_content.html('');
            var $_inputseller = getElement('input').attr({
                type: "text",
                id: "txt_name_technical",
                class: "form-control",
                value: data_user.user_name,
                readonly: "readonly",
            });
            $_inputseller.css("height", "32px");

            $_content.append($_label).append($_inputseller);

        }
    }
    //}

    $(container).html($_content);

    $("#c_technical").select2({
        language: "en",
        placeholder: m_selectTechnical,
//        minimumInputLength: 1,
    });
}

// LISTA DE VENDEDORES - COMBOBOX MULTIPLE
function build_seller2_selectMultiple(container, active) {
    var $_inputActive = getElement('input').attr('type', 'checkbox').attr('id', 'c_seller_active').prop('checked', true);
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(s_seller);
    var $_labelActive = getElement("label").addClass("lbl-component lbl-s-active col-xs-6").text(s_active).append($_inputActive);
    var $_element = getElement("select").attr({id: "c_seller", multiple: "", name: "c_seller", style: "width: 100%;"});
    if (typeof active != 'undefined') {
        $_content.append($_label).append($_element);
    } else {
        $_content.append($_label).append($_labelActive).append($_element);
    }
    var ajaxSeller = function (active) {

        $.ajax({
            url: enviroment + '/components/listSeller2',
            type: 'post',
            data: {active: active},
            dataType: 'json',
            beforeSend: function () {
                showLoader();
                if ($("#c_seller").data('select2')) {
                    $("#c_seller").select2('destroy');
                    $("#c_seller").empty();
                }
                window.active_ajax++;
            },
            complete: function () {
                hideLoader();
                $("#c_seller").select2({
                    language: "en",
                    placeholder: m_selectSeller,
                    minimumInputLength: 2
                });
            },
            success: function (response) {
                var data = response.data;
                var l = data.length;
                if (l > 0) {
                    for (var i = 0, max = l; i < max; i++) {
                        var inactiveText = data[i].status === '0' ? data[i].value + ' (I)' : data[i].value;
                        $("#c_seller").append(getElement("option").attr({value: data[i].id}).text(inactiveText));
                    }
                }
                window.active_ajax--;
            },
            error: function (request, status, error) {
                window.active_ajax--;
            }
        });
    }
    //if(typeof active != 'undefined'){
    $_inputActive.on('click', function () {
        var active = $(this).prop('checked') === true ? 1 : 0;
        $(this).val(active);
        ajaxSeller(active);
    });
    $_inputActive.val($($_inputActive).prop('checked') === true ? 1 : 0)

    typeof active != 'undefined' ? ajaxSeller(0) : ajaxSeller(1);


    $(container).html($_content);
    $("#c_seller").select2({
        language: "en",
        placeholder: m_selectSeller,
        minimumInputLength: 2,
    });
}

// LISTA DE EMPLEADOS POR BOLETA DE PAGO - COMBOBOX MULTIPLE
function build_employeeFromPayroll_selectMultiple(container, idPayroll) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_employee);
    var $_element = getElement("select").attr({id: "c_employee", multiple: "", name: "c_employee", style: "width: 100%;"});
    $_content.append($_label).append($_element);
    var queryURL = enviroment + '/components/listEmployeeFromPayroll';
    $(container).html($_content);

    $("#c_employee").select2({
        language: "en",
        placeholder: m_selectEmployee,
        minimumInputLength: 3,
        ajax: {
            url: queryURL,
            type: 'post',
            delay: 250,
            dataType: 'json',
            data: function (params) {
                return{
                    q: params.term,
                    idPayroll: idPayroll
                };
            },
            processResults: function (data) {
                return{
                    results: $.map(data.data, function (obj) {
                        return{id: obj.id, text: obj.value};
                    })
                };
            }, cache: true,
        }
    });
}

// LISTA DE SOCIOS DE NEGOCIO - COMBOBOX MULTIPLE
function build_businessPartnerByRoute_selectMultiple(container, route, title, id) {

    var s_title = s_businesspartner;
    if (title !== null) {
        s_title = title;
    }
    var s_id = "c_businesspartner2";
    if (id !== null) {
        s_id = id;
    }
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main");
    $_label.text(s_title);
    var $_element = getElement("select").attr({id: s_id, multiple: "", name: s_id, style: "width: 100%;"});


    $_content.append($_label).append($_element);
    $(container).html($_content);
    var queryURL = enviroment + '/components/listBusinessPartnerByRoute';

    $("#" + s_id).select2({
        language: "en",
        placeholder: m_selectBusinessPartner,
        minimumInputLength: 2,
        ajax: {
            url: queryURL,
            type: 'post',
            data: function (params) {
                return {q: params.term, route: route}
            },
            dataType: 'json',
            delay: 250,
            processResults: function (data) {
                return {
                    results: $.map(data.data, function (obj) {
                        return {id: obj.id, text: obj.value};
                    })
                };
            },
            cache: true,
        }
    });

}

// LISTA DE SOCIOS DE NEGOCIO CRM - COMBOBOX MULTIPLE
function build_businessPartnerCRM_selectMultiple(container, title, id) {

    var s_title = s_provider;
    if (title !== null) {
        s_title = title;
    }
    var s_id = "c_businesspartner2";
    if (id !== null) {
        s_id = id;
    }
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main");
    $_label.text(s_title);
    var $_element = getElement("select").attr({id: s_id, multiple: "", name: s_id, style: "width: 100%;"});


    $_content.append($_label).append($_element);
    $(container).html($_content);
    var queryURL = enviroment + '/components/listBusinessPartnerCRM';

    $("#" + s_id).select2({
        language: "en",
        placeholder: m_selectBusinessPartner,
        minimumInputLength: 2,
        ajax: {
            url: queryURL,
            type: 'post',
            data: function (params) {
                return {q: params.term}
            },
            dataType: 'json',
            delay: 250,
            processResults: function (data) {
                return {
                    results: $.map(data.data, function (obj) {
                        return {id: obj.id, text: obj.value};
                    })
                };
            },
            cache: true,
        }
    });

}


// LISTA DE SOCIOS DE NEGOCIO - COMBOBOX MULTIPLE
function build_businessPartner_selectMultiple(container, type) {
    var $_content = getElement("div");

    var $_label = getElement("label").addClass("lbl-component lbl-c-main");

    switch (type) {
        case "provider":
            $_label.text(s_provider);
            var $_element = getElement("select").attr({id: "c_provider", multiple: "", name: "c_provider", style: "width: 100%;"});
            break;
        case "client":
            $_label.text(s_client);
            var $_element = getElement("select").attr({id: "c_client", multiple: "", name: "c_client", style: "width: 100%;"});
            break;
        case "clientConsignment":
            $_label.text(s_client);
            var $_element = getElement("select").attr({id: "c_client", multiple: "", name: "c_client", style: "width: 100%;"});
            break;
        case "clientCredit":
            $_label.text(s_client);
            var $_element = getElement("select").attr({id: "c_client", multiple: "", name: "c_client", style: "width: 100%;"});
            break;
        case "associated":
            $_label.text(s_associated);
            var $_element = getElement("select").attr({id: "c_associated", multiple: "", name: "c_associated", style: "width: 100%;"});
            break;
        case "all":
            $_label.text(s_businesspartner);
            var $_element = getElement("select").attr({id: "c_businesspartner", multiple: "", name: "c_businesspartner", style: "width: 100%;"});
            break;

    }

    $_content.append($_label).append($_element);
    $(container).html($_content);
    var queryURL = enviroment + '/components/listBusinessPartner';

    switch (type) {
        case "provider":
            $("#c_provider").select2({
                language: "en",
                placeholder: m_selectProvider,
                minimumInputLength: 3,
                ajax: {
                    url: queryURL,
                    type: 'post',
                    data: function (params) {
                        return {q: params.term, type: type}
                    },
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return {
                            results: $.map(data.data, function (obj) {
                                return {id: obj.id, text: obj.value};
                            })
                        };
                    },
                    cache: true,
                }
            });
            break;
        case "client":
        case "clientConsignment":
        case "clientCredit":
            $("#c_client").select2({
                language: "en",
                placeholder: m_selectClient,
                minimumInputLength: 3,
                ajax: {
                    url: queryURL,
                    type: 'post',
                    data: function (params) {
                        return {q: params.term, type: type}
                    },
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return {
                            results: $.map(data.data, function (obj) {
                                return {id: obj.id, text: obj.value};
                            })
                        };
                    },
                    cache: true,
                }
            });
            break;
        case "associated":
            $("#c_associated").select2({
                language: "en",
                placeholder: m_selectAssociated,
                minimumInputLength: 3,
                ajax: {
                    url: queryURL,
                    type: 'post',
                    data: function (params) {
                        return {q: params.term, type: type}
                    },
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return {
                            results: $.map(data.data, function (obj) {
                                return {id: obj.id, text: obj.value};
                            })
                        };
                    },
                    cache: true,
                }
            });
            break;
        case "all":
            $("#c_businesspartner").select2({
                language: "en",
                placeholder: m_selectBusinessPartner,
                minimumInputLength: 3,
                ajax: {
                    url: queryURL,
                    type: 'post',
                    data: function (params) {
                        return {q: params.term, type: type}
                    },
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return {
                            results: $.map(data.data, function (obj) {
                                return {id: obj.id, text: obj.value};
                            })
                        };
                    },
                    cache: true,
                }
            });
            break;
    }
}

// LISTA DE SOCIOS DE NEGOCIO - COMBOBOX MULTIPLE
function build_businessPartnerTpo_selectMultiple(container, type, condition, year) {
    var $_content = getElement("div");

    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12");

    switch (type) {
        case "provider":
            $_label.text(s_provider);
            var $_element = getElement("select").attr({id: "c_provider_tpo", multiple: "", name: "c_provider_tpo", style: "width: 100%;"});
            break;
        case "client":
            $_label.text(s_client);
            var $_element = getElement("select").attr({id: "c_client_tpo", multiple: "", name: "c_client_tpo", style: "width: 100%;"});
            break;
    }

    $_content.append($_label).append($_element);
    $(container).html($_content);
    var queryURL = enviroment + '/components/listBusinessPartnerTpo';

    switch (type) {
        case "provider":
            $("#c_provider_tpo").select2({
                language: "en",
                placeholder: m_selectProvider,
                minimumInputLength: 3,
                ajax: {
                    url: queryURL,
                    type: 'post',
                    data: function (params) {
                        return {q: params.term, type: type, condition: condition, year: year}
                    },
                    dataType: 'json',
                    processResults: function (data) {
                        return {
                            results: $.map(data.data, function (obj) {
                                return {id: obj.id, text: obj.value};
                            })
                        };
                    },
                    cache: true,
                }
            });
            break;
        case "client":
            $("#c_client_tpo").select2({
                language: "en",
                placeholder: m_selectClient,
                minimumInputLength: 3,
                ajax: {
                    url: queryURL,
                    type: 'post',
                    data: function (params) {
                        return {q: params.term, type: type, condition: condition, year: year}
                    },
                    dataType: 'json',
                    processResults: function (data) {
                        return {
                            results: $.map(data.data, function (obj) {
                                return {id: obj.id, text: obj.value};
                            })
                        };
                    },
                    cache: true,
                }
            });
            break;
    }
}


// LISTA DE SOCIOS DE NEGOCIO - COMBOBOX MULTIPLE
function build_businessPartnerAlternate_selectMultiple(container, type) {
    var $_content = getElement("div");

    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12");

    switch (type) {
        case "provider":
            $_label.text(s_providerAlternate);
            var $_element = getElement("select").attr({id: "c_provider_alternate", multiple: "", name: "c_provider_alternate", style: "width: 100%;"});
            break;
        case "client":
            $_label.text(s_clientAlternate);
            var $_element = getElement("select").attr({id: "c_client_alternate", multiple: "", name: "c_client_alternate", style: "width: 100%;"});
            break;
        case "all":
            $_label.text(s_businesspartner);
            var $_element = getElement("select").attr({id: "c_businesspartner", multiple: "", name: "c_businesspartner", style: "width: 100%;"});
            break;

    }

    $_content.append($_label).append($_element);
    $(container).html($_content);
    var queryURL = enviroment + '/components/listBusinessPartnerAlternate';

    switch (type) {
        case "provider":
            $("#c_provider_alternate").select2({
                language: "en",
                placeholder: m_selectProvider,
                minimumInputLength: 3,
                ajax: {
                    url: queryURL,
                    type: 'post',
                    data: function (params) {
                        return {q: params.term, type: type}
                    },
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return {
                            results: $.map(data.data, function (obj) {
                                return {id: obj.id, text: obj.value};
                            })
                        };
                    },
                    cache: true,
                }
            });
            break;
        case "client":
            $("#c_client_alternate").select2({
                language: "en",
                placeholder: m_selectClient,
                minimumInputLength: 3,
                ajax: {
                    url: queryURL,
                    type: 'post',
                    data: function (params) {
                        return {q: params.term, type: type}
                    },
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return {
                            results: $.map(data.data, function (obj) {
                                return {id: obj.id, text: obj.value};
                            })
                        };
                    },
                    cache: true,
                }
            });
            break;
        case "all":
            $("#c_businesspartner").select2({
                language: "en",
                placeholder: m_selectBusinessPartner,
                minimumInputLength: 3,
                ajax: {
                    url: queryURL,
                    type: 'post',
                    data: function (params) {
                        return {q: params.term, type: type}
                    },
                    dataType: 'json',
                    delay: 250,
                    processResults: function (data) {
                        return {
                            results: $.map(data.data, function (obj) {
                                return {id: obj.id, text: obj.value};
                            })
                        };
                    },
                    cache: true,
                }
            });
            break;
    }
}

// LISTA DE CLIENTES - COMBOBOX MULTIPLE
function build_clients_selectMultiple(container, retention) {
    var $_content = getElement("div");

    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_client);
    var $_element = getElement("select").attr({id: "c_client", multiple: "", name: "c_client", style: "width: 100%;"});


    $_content.append($_label).append($_element);
    var queryURL = enviroment + '/components/listClient';

    $(container).html($_content);

    $("#c_client").select2({
        language: "en",
        placeholder: m_selectClient,
        minimumInputLength: 3,
        ajax: {
            url: queryURL,
            type: 'post',
            delay: 250,
            dataType: 'json',
            data: function (params) {
                return{
                    q: params.term,
                    retention: retention
                };
            },
            processResults: function (data) {
                return{
                    results: $.map(data.data, function (obj) {
                        return{id: obj.id, text: obj.value};
                    })
                };
            }, cache: true,
        }
    });
}

// LISTA DE CLIENTES - COMBOBOX MULTIPLE
function build_clientsProject_selectMultiple(container) {
    var $_content = getElement("div");

    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_client);
    var $_element = getElement("select").attr({id: "c_client", multiple: "", name: "c_client", style: "width: 100%;"});


    $_content.append($_label).append($_element);
    var queryURL = enviroment + '/components/listClientProject';

    $(container).html($_content);

    $("#c_client").select2({
        language: "en",
        placeholder: m_selectClient,
        minimumInputLength: 1,
        ajax: {
            url: queryURL,
            type: 'post',
            delay: 250,
            dataType: 'json',
            data: function (params) {
                return{
                    q: params.term
                };
            },
            processResults: function (data) {
                return{
                    results: $.map(data.data, function (obj) {
                        return{id: obj.id, text: obj.value};
                    })
                };
            }, cache: true,
        }
    });
}

// LISTA DE MONEDAS - COMBOBOX MULTIPLE
function build_currency_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_currency);
    var $_element = getElement("select").attr({id: "c_currency", multiple: "", name: "c_currency", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listCurrency',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_currency").select2({
        language: "en",
        placeholder: m_selectCurrency
    });
}

// LISTA DE CONDICIONES - COMBOBOX MULTIPLE
function build_condition_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(s_condition);
    var $_element = getElement("select").attr({id: "c_condition", multiple: "", name: "c_condition", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listCondition',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_condition").select2({
        language: "en",
        placeholder: m_selectCondition
    });
}

// LISTA DE RETENCIONES - COMBOBOX MULTIPLE
function build_retention_selectMultiple(container, type) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_retention);
    var $_element = getElement("select").attr({id: "c_retention", multiple: "", name: "c_retention", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listRetention',
        data: {type: type},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_retention").select2({
        language: "en",
        placeholder: m_selectRetention
    });
}

// LISTA DE TYPOS DE PERSONAS - COMBOBOX MULTIPLE
function build_typePerson_selectMultiple(container) {

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(s_typePerson);
    var $_element = getElement("select").attr({id: "c_typePerson", multiple: "", name: "c_typePerson", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listTypePerson',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_typePerson").select2({
        language: "en",
        placeholder: m_selectTypePerson
    });
}

function build_toPayPersonByType_selectMultiple(container, type) {
    var c_persontype = "Socio de Negocio";
    var m_persontype = m_selectPerson;

    if (type == "provider") {
        c_persontype = "Proveedores";
        m_persontype = m_selectProvider;
    }
    if (type == "employee") {
        c_persontype = "Empleados";
        m_persontype = m_selectEmployee;
    }

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(c_persontype);
    var $_element = getElement("select").attr({id: "c_person", multiple: "", name: "c_person", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listPersonToPay',
        data: {type: type},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });
    $(container).html($_content);
    $("#c_person").select2({
        language: "en",
        placeholder: m_persontype
    });
}

function build_toPayPersonAlternativeByType_selectMultiple(container, type, showlabel) {
    var c_persontype = "Socio Alterno";
    var m_persontype = m_selectPerson;

    if (type == "provider") {
        c_persontype = "Proveedores Alternos";
        m_persontype = m_selectProvider;
    }
    if (type == "employee") {
        c_persontype = "Empleados Alternos";
        m_persontype = m_selectEmployee;
    }

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(c_persontype);
    var $_element = getElement("select").attr({id: "c_person_alterno", multiple: "", name: "c_person_alterno", style: "width: 100%;"});
    if (showlabel == true) {
        $_content.append($_label).append($_element);
    } else {
        $_content.append($_element);
    }

    $.ajax({
        url: enviroment + '/components/listPersonForeignToPay',
        data: {type: type},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });
    $(container).html($_content);
    $("#c_person_alterno").select2({
        language: "en",
        placeholder: m_persontype
    });
}

function build_showMore(container, name) {

    var $_content = getElement("div");
    var $_labelCondition = getElement("label").addClass("lbl-component lbl-s-active col-xs-6").text(name);
    var $_inputCondition = getElement('input').attr('type', 'checkbox').attr('id', 'c_showMore').attr('name', 'c_showMore').prop('checked', false);

    var $_content = getElement("div");
    $_content.append($_inputCondition).append($_labelCondition);

    $(container).html($_content);
}

// LISTA DE PERSONAS PARA PAGAR POR TYPO - COMBOBOX MULTIPLE
function build_toPayPersonByType_selectMultipleOld(container, type) {
    var c_persontype = "Socio";
    var m_persontype = m_selectPerson;

    if (type == "provider") {
        c_persontype = "Proveedores";
        m_persontype = m_selectProvider;
    }
    if (type == "employee") {
        c_persontype = "Empleados";
        m_persontype = m_selectEmployee;
    }

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12");
    $_label.text(c_persontype);
    var $_element = getElement("select").attr({id: "c_person", multiple: "", name: "c_person", style: "width: 100%;"});

    $_content.append($_label).append($_element);
    var queryURL = enviroment + '/components/listPersonToPay';
    $(container).html($_content);


    $("#c_person").select2({
        language: "en",
        placeholder: "Select a state",
        // minimumInputLength: 3,
        ajax: {
            url: queryURL,
            type: 'post',
            delay: 250,
            dataType: 'json',
            data: function (params) {
                return{
                    q: params.term,
                    type: type
                };
            },
            processResults: function (data) {
                return{
                    results: $.map(data.data, function (obj) {
                        return{id: obj.id, text: obj.value};
                    })
                };
            }, cache: true,
        }
    });
    $("#c_person").on("change", function (e) {
        var selected = $(e.target).val();
        if (selected !== null) {
            if (selected.length > 1) {
                $('span[aria-owns="select2-c_person-results"] ul').css('cssText', "overflow-y: scroll !important");
            }
        }
    })

    $("#c_person").select2({
        language: "en",
        placeholder: m_persontype
    });
}

// LISTA DE AGENTES DE RETENCIÓN - COMBOBOX MULTIPLE
function build_financialEntity_selectMultiple(container, type) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_financialEntities);
    var $_element = getElement("select").attr({id: "c_financialEntities", multiple: "", name: "c_financialEntities", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listFinancialEntities',
        data: {type: type},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_financialEntities").select2({
        language: "en",
        placeholder: m_selectFinancialEntity
    });
}
//LISTA DE ENTIDADES FINANCIERAS- Todos/Limpiar

function build_financialEntity2_selectMultiple(container, type, optionAll, idsStore) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_financialEntities);
    var $_element = getElement("select").attr({id: "c_financialEntities", multiple: "", name: "c_financialEntities", style: "width: 100%;"});
    var $_button = getElement("button").addClass("btn btn-default").text("Todos").attr({id: "c_all_financialEntities"});
    var $_button2 = getElement("button").addClass("btn btn-default").text("Limpiar").attr({id: "c_clear_financialEntities"});
    $_content.append($_label).append($_element);
    var queryURL = enviroment + '/components/listFinancialEntities';

    $(container).html($_content);
    $("#c_financialEntities").select2({
        language: "en",
        placeholder: m_selectFinancialEntity
    });

    $.ajax({
        url: queryURL,

        data: {
            type: type,
            idsStore: idsStore
        },
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });



    if (optionAll) {
        $_content.append("<br>").append($_button).append($_button2);
    }
    if (optionAll) {
        $("#c_all_financialEntities").on("click", function (e) {

            var a_all = [];
            $("#c_financialEntities option").each(function () {
                a_all.push($(this).attr('value'));
            });
            $("#c_financialEntities").val(a_all).trigger("change");
            e.preventDefault();
            return false;
        });
        $("#c_clear_financialEntities").on("click", function (e) {

            $("#c_financialEntities").val(null).trigger("change");
            e.preventDefault();
            return false;
        });
    }
}
// LISTA DE COMBINACIONES DE CENTRO DE COSTOS.
function build_centerCost_selectMultiple(container) {
    var $_content = getElement("div");
//    var $_label = getElement("label").addClass("lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_centerCost);
//    $_content.append($_label);
    var $a_levels = [];
    var l = 0;

    $.ajax({
        url: enviroment + '/components/listCostCenter',
        type: 'post',
        dataType: 'json',
        async: false,
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var level = response.level;
            l = Object.keys(data).length;
            var width = 100;
            if (l > 0) {
                Object.keys(data).forEach(function (key) {
                    var $_label_level = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(level[key] + ": ");
                    var $_element = getElement("select").attr({id: "c_costCenter_" + key, class: "cost_level", multiple: "", level: key, name: "c_costCenter_" + key, style: "width: " + width + "%;"});
                    $a_levels.push(key);
                    Object.keys(data[key]).forEach(function (subkey) {

                        $_content.append($_label_level).append($_element);
                        $_element.append(getElement("option").attr({value: subkey}).text(data[key][subkey]));
                    });
                });
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    for (var i = 0; i < l; i++) {
        $("#c_costCenter_" + $a_levels[i]).select2({
            language: "en",
            placeholder: m_selectCostCenter + $a_levels[i]
        });
    }

}
// LISTA DE AGENTES DE RETENCIÓN - COMBOBOX MULTIPLE
function build_retentionAgent_selectMultiple(container) {

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_retentionAgent);
    var $_element = getElement("select").attr({id: "c_retentionAgent", multiple: "", name: "c_retentionAgent", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listRetentionAgent',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_retentionAgent").select2({
        language: "en",
        placeholder: m_selectRetentionAgent
    });
}

// LISTA DE TIENDAS - COMBOBOX MULTIPLE
function build_store_selectMultiple(container, business_unit_ids) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_store);
    var $_element = getElement("select").attr({id: "c_store", multiple: "", name: "c_store", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listStore',
        type: 'post',
        dataType: 'json',
        async: false,
        data: {
            business_unit_ids: business_unit_ids
        },
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_store").select2({
        language: "en",
        placeholder: m_selectStore
    });
}

// LISTA DE ESTADOS - COMBOBOX MULTIPLE
function build_status_selectMultiple(container, type, title, name) {
    if (title == '' || title == undefined)
        title = s_status;
    if (name == '' || name == undefined)
        name = "c_status";
//    
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(title);
    var $_element = getElement("select").attr({id: name, multiple: "", name: name, style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listStatus',
        data: {type: type},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#" + name).select2({
        language: "en",
        placeholder: m_selectStatus
    });
}

// LISTA DE ESTADOS - COMBOBOX MULTIPLE
function build_type_serviceOrder_selectMultiple(container) {
    var title = "Tipo de Servicio";
    var name = "c_type_service_order";

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(title);
    var $_element = getElement("select").attr({id: name, multiple: "", name: name, style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/typeServiceOrder',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#" + name).select2({
        language: "en",
        placeholder: "Seleccione un(os) tipo(s) de Servicio"
    });
}

// LISTA DE MOVIMIENTOS - COMBOBOX MULTIPLE
function build_movement_selectMultiple(container, route, operation, search, d, sd, ed, idsAuxPerson) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_movement);
    var $_element = getElement("select").attr({id: "c_movement", multiple: "", name: "c_movement", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $(container).html($_content);

    $("#c_movement").select2({
        language: "en",
        placeholder: m_selectMovement,
        minimumInputLength: 3,
        ajax: {
            url: enviroment + '/components/listMovement',
            dataType: 'json',
            type: "POST",
            data: function (params) {
                return {
                    term: params.term
                    , route: route
                    , operation: operation
                    , search: search
                    , d: d
                    , sd: sd
                    , ed: ed
                    , idsAuxPerson: idsAuxPerson
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                console.log(data);
                a_movement = $.map(data.data, function (item) {

                    return {id: item.id, text: item.value}
                });
                return {
                    results: a_movement
                    ,
                    pagination: {
                        more: (params.page * 30) < data.total_count
                    }
                };
            }
        }
    }).on('select2:selected', function (evt) {
        console.log(evt);
    });
}

// LISTA DE MOVIMIENTOS CON PAGOS - COMBOBOX MULTIPLE
function build_movementWithPayment_selectMultiple(container, route, operation, sd, ed, idsAuxPerson) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_movement);
    var $_element = getElement("select").attr({id: "c_movement", multiple: "", name: "c_movement", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listMovementWithPayment',
        data: {route: route, operation: operation, sd: sd, ed: ed, idsAuxPerson: idsAuxPerson},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_movement").select2({
        language: "en",
        placeholder: m_selectMovement
    });
}

// LISTA DE MOVIMIENTOS - COMBOBOX SIMPLE
function build_movement_selectSimple(container, route, operation, search, d, sd, ed, idsAuxPerson) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_movement);
    var $_element = getElement("select").attr({id: "c_movement", name: "c_movement", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listMovement',
        data: {route: route, operation: operation, search: search, d: d, sd: sd, ed: ed, idsAuxPerson: idsAuxPerson},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            if (l > 0) {
                for (i = 0; i < l; i++) {
                    $_element.append(getElement("option").attr("value", data[i].id).text(data[i].value));
                }
            }

            $(container).html($_content);

            $("#c_movement").select2({
                language: "en",
                placeholder: s_selectMovement
            });
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });
}

// LISTA DE TIPOS DE PAGO - COMBOBOX MULTIPLE
function build_paymentMethod_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_paymentMethod);
    var $_element = getElement("select").attr({id: "c_paymentMethod", multiple: "", name: "c_paymentMethod", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listPaymentMethod',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_paymentMethod").select2({
        language: "en",
        placeholder: m_selectPaymentMethod
    });
}

// LISTA DE MARCAS - COMBOBOX MULTIPLE
function build_mark_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_mark);
    var $_element = getElement("select").attr({id: "c_mark", multiple: "", name: "c_mark", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listMark',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_mark").select2({
        language: "en",
        placeholder: m_selectMark
    });
}

// LISTA DE AÑOS - COMBOBOX SIMPLE
function build_year_selectSimple(container, minYear) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_year + " :");
    var $_element = getElement("select").attr({id: "c_year", name: "c_year", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listYear',
        data: {minYear: minYear},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            if (l > 0) {
                for (i = 0; i < l; i++) {
                    $_element.append(getElement("option").attr("value", data[i].id).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_year").select2({
        language: "en",
        placeholder: s_selectYear
    });
}

// LISTA DE AÑOS - COMBOBOX MULTIPLE
function build_year_selectMultiple(container, minYear) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_year);
    var $_element = getElement("select").attr({id: "c_year", multiple: "", name: "c_year", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listYear',
        data: {minYear: minYear},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_year").select2({
        language: "en",
        placeholder: m_selectYear
    });
}

// LISTA DE MESES - COMBOBOX SIMPLE
function build_month_selectSimple(container, accounting, isAll) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_month + " :");
    var $_element = getElement("select").attr({id: "c_month", name: "c_month", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listMonth',
        data: {accounting: accounting},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            if (l > 0) {
                if (isAll == 1) {
                    $_element.append(getElement("option").attr("value", 0).text(msgSelectAllOption));
                }
                for (i = 0; i < l; i++) {
                    $_element.append(getElement("option").attr("value", data[i].id).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_month").select2({
        language: "en",
        placeholder: s_selectMonth
    });
}

// LISTA DE MESES - COMBOBOX MULTIPLE
function build_month_selectMultiple(container, accounting, option_all) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_month + " :");
    var $_element = getElement("select").attr({id: "c_month", multiple: "", name: "c_month", style: "width: 100%;"});
    var $_button = getElement("button").addClass("btn btn-default").text("Todos").attr({id: "c_all"});
    var $_button2 = getElement("button").addClass("btn btn-default").text("Limpiar").attr({id: "c_clear"});

    $_content.append($_label).append($_element);

    if (option_all) {
        $_content.append("<br>").append($_button).append($_button2);
    }

    $.ajax({
        url: enviroment + '/components/listMonth',
        data: {accounting: accounting},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
            window.active_ajax--;
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_month").select2({
        language: "en",
        placeholder: m_selectMonth
    });

    if (option_all) {
        $("#c_all").on("click", function (e) {

            var a_all = [];
            $("#c_month option").each(function () {
                a_all.push($(this).attr('value'));
            });
            $("#c_month").val(a_all).trigger("change");
            e.preventDefault();
            return false;
        });
        $("#c_clear").on("click", function (e) {

            $("#c_month").val(null).trigger("change");
            e.preventDefault();
            return false;
        });
    }
}

// LISTA DE DEPARTAMENTOS - COMBOBOX MULTIPLE
function build_department_selectMultiple(container, idDep) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_department);
    var $_element = getElement("select").attr({id: "c_department", multiple: "", name: "c_department", style: "width: 100%;"});
    $_content.append($_label).append($_element);
    var queryURL = enviroment + '/components/listDepartment';

    $(container).html($_content);

    $("#c_department").select2({
        language: "en",
        placeholder: m_selectDepartment,
        minimumInputLength: 3,
        ajax: {
            url: queryURL,
            type: 'post',
            delay: 250,
            dataType: 'json',
            data: function (params) {
                return{
                    q: params.term,
                    idDep: idDep
                };
            },
            processResults: function (data) {
                return{
                    results: $.map(data.data, function (obj) {
                        return{id: obj.id, text: obj.value};
                    })
                };
            }, cache: true,
        }
    });
}

// LISTA DE PROVINCIA - COMBOBOX MULTIPLE
function build_province_selectMultiple(container, idsDepartment) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_province);
    var $_element = getElement("select").attr({id: "c_province", multiple: "", name: "c_province", style: "width: 100%;"});
    $_content.append($_label).append($_element);
    //var queryURL = enviroment + '/components/listProvince';

    $.ajax({
        url: enviroment + '/components/listProvince',
        data: {idsDepartment: idsDepartment},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_province").select2({
        language: "en",
        placeholder: m_selectProvince
    });
}

// LISTA DE DISTRITO - COMBOBOX MULTIPLE
function build_district_selectMultiple(container, idsDepartment, idsProvince) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_district);
    var $_element = getElement("select").attr({id: "c_district", multiple: "", name: "c_district", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listDistrict',
        data: {idsDepartment: idsDepartment, idsProvince: idsProvince},
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_district").select2({
        language: "en",
        placeholder: m_selectDistrict
    });
}

// LISTA DE CUENTAS CONTABLES - COMBOBOX SIMPLE
function build_accountMajor_selectSimple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_account + " :");
    var $_element = getElement("select").attr({id: "c_account", name: "c_account", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listAccountMajor',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            if (l > 0) {
                $_element.append(getElement("option").attr("value", 0).text("-- Todas las Cuentas --"));
                for (i = 0; i < l; i++) {
                    $_element.append(getElement("option").attr("value", data[i].id).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_account").select2({
        language: "en",
        placeholder: s_selectAccount
    });
}

// LISTA DE CUENTAS CONTABLES - COMBOBOX SIMPLE
function build_account_selectSimple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_account + " :");
    var $_element = getElement("select").attr({id: "c_account", name: "c_account", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listUsableAccount',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            if (l > 0) {
                for (i = 0; i < l; i++) {
                    $_element.append(getElement("option").attr("value", data[i].id).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_account").select2({
        language: "en",
        placeholder: s_selectAccount
    });
}


// LISTA DE CUENTAS CONTABLES - COMBOBOX SIMPLE
function build_fromAccount_selectSimple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text("DESDE " + s_account + " :");
    var $_element = getElement("select").attr({id: "c_fromAccount", name: "c_fromAccount", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listAccount',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            if (l > 0) {
                for (i = 0; i < l; i++) {
                    $_element.append(getElement("option").attr("value", data[i].id).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_fromAccount").select2({
        language: "en",
        placeholder: s_selectAccount
    });
}

function build_toAccount_selectSimple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text("HASTA " + s_account + " :");
    var $_element = getElement("select").attr({id: "c_toAccount", name: "c_toAccount", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listAccount',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            if (l > 0) {
                for (i = 0; i < l; i++) {
                    $_element.append(getElement("option").attr("value", data[i].id).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_toAccount").select2({
        language: "en",
        placeholder: s_selectAccount
    });
}

// LISTA DE ALMACENES - COMBOBOX SIMPLE
function build_owner_selectSimple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_owner);
    var $_element = getElement("select").attr({id: "c_owner", name: "c_owner", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listOwner',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data, l = data.length, i;
            $_element.append(getElement("option").attr("value", 0).text("- Cualquiera -"));
            if (l > 0) {
                for (i = 0; i < l; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }

            $(container).html($_content);
            $("#c_owner").select2({
                language: "en",
                placeholder: s_selectOwner
            });
        }
    });
}

function build_ownerIds_selectMultiple(container, owner) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main col-lg-12 col-md-12 col-sm-12 col-xs-12").text(s_ownerId + " :");
    var $_element = getElement("select").attr({id: "c_ownerIds", multiple: "", name: "c_ownerIds", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $(container).html($_content);
    $("#c_ownerIds").select2({
        language: "en",
        placeholder: m_selectOwner,
        minimumInputLength: 2,
        ajax: {
            url: enviroment + '/components/listOwnerIds',
            type: 'post',
            data: function (params) {
                return {q: params.term, owner: owner}
            },
            dataType: 'json',
            delay: 250,
            processResults: function (data) {
                return {
                    results: $.map(data.data, function (obj) {
                        return {id: obj.id, text: obj.value};
                    })
                };
            },
            cache: true,
        }
    });
}

/* Fin de Componentes */


function build_checkBoxComponent(id_component) {
    return getElement("input").attr({type: "checkbox", name: id_component, id: id_component, value: ""});
}

/* Componente de Fecha */

function build_filterSearch(container, sizeDate, s_label, new_component, id_date_component) {
    var i, $_aux;
    var l = sizeDate.length;

    /* Variables locales para multiples identificadores */

    var id_date_i = (id_date_component) == null ? ("c_daterango_i") : ("c_daterango_i_" + id_date_component);
    var id_date_e = (id_date_component) == null ? ("c_daterango_e") : ("c_daterango_e_" + id_date_component);
    var id_c_date = (id_date_component) == null ? ("c_date") : ("c_date_" + id_date_component);


    var $_input_day = getElement("input").attr({type: "text", maxlength: "25", name: id_c_date, id: id_c_date, value: dateSystem});

    var $_input_date_i = getElement("input").attr({type: "text", maxlength: "20", name: id_date_i, id: id_date_i});
    var $_input_date_e = getElement("input").attr({type: "text", maxlength: "20", name: id_date_e, id: id_date_e});

    var $_select_month = getElement("select").attr({id: "c_month", name: "c_month", style: "width: 100%;"});
    var $_select_year = getElement("select").attr({id: "c_year", name: "c_year", style: "width: 100%;"});

    for (i = 0; i < a_monthNames.length; i++) {
        $_select_month.append(getElement("option").attr("value", (i + 1)).text(a_monthNames[i]));
    }
    for (i = 0; i < a_years.length; i++) {
        $_select_year.append(getElement("option").attr("value", (a_years[i])).text(a_years[i]));
    }

    if (l == 1) {
        var $_day = getElement("div").addClass("sub-component sub-ui filter_day").
                append(getElement("div").append($_input_day));
    } else {
        var $_day = getElement("div").addClass("sub-component sub-ui filter_day").
                append(getElement("label").addClass("lbl-component ").text(s_day)).
                append(getElement("div").append($_input_day));
    }
    var $_range = getElement("div").addClass("sub-component-group sub-ui hide-comp filter_range").
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_dateIni)).
                    append(getElement("div").append($_input_date_i))).
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_dateEnd)).
                    append(getElement("div").append($_input_date_e)));

    var $_month = getElement("div").addClass("sub-component-group sub-ui hide-comp filter_month").
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_month)).
                    append(getElement("div").append($_select_month))).
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_year)).
                    append(getElement("div").append($_select_year)));

    if (l > 1) {
        var $_radio_listSearchType;
        if (sizeDate[0] === 1) {
            $_radio_listSearchType = build_radioButtonsStandar([
                {id: "tbd", value: "0", text: s_day, name: "type_search", default: "0", hidden: true},
                {id: "tbr", value: "1", text: s_range, name: "type_search", default: "1"},
                {id: "tbm", value: "2", text: s_month, name: "type_search", default: "0"}
            ], 3);
        } else {
            $_radio_listSearchType = build_radioButtonsStandar([
                {id: "tbd", value: "0", text: s_day, name: "type_search", default: "1"},
                {id: "tbr", value: "1", text: s_range, name: "type_search", default: "0"},
                {id: "tbm", value: "2", text: s_month, name: "type_search", default: "0"}
            ], l);
        }
        var $_component = getElement("div").addClass("component c_filter_search").
                append(getElement("div").addClass("sub-component show-comp").
                        append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :")).
                        append(getElement("div").append($_radio_listSearchType)));

        for (var i = 0, max = l; i < max; i++) {
            $_aux = sizeDate[i];
            if ($_aux == 0) {
                $_component.append($_day);
            }
            if ($_aux == 1) {
                $_component.append($_range);
            }
            if ($_aux == 2) {
                $_component.append($_month);
            }
        }
    } else {

        var filter_title = getElement("div").addClass("sub-component show-comp").append
                (getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"));

        if (sizeDate[0] == 0) {
            var $_component = getElement("div").addClass("component c_filter_search").
                    append(filter_title).
                    append($_day);
            $_component.data("option", 0);
        }
        if (sizeDate[0] == 1) {
            if (new_component != null) {
                var $_component = getElement("div").addClass("component c_filter_search").
                        append(getElement("div").addClass("sub-component show-comp").
                                append(new_component).
                                append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"))).
                        append($_range.removeClass("hide-comp"));
                $_component.data("option", 1);
            } else {

                if (s_label == '') {
                    var $_component = getElement("div").addClass("component c_filter_search").
                            append($_range.removeClass("hide-comp"));
                    $_component.data("option", 1);
                } else {
                    var $_component = getElement("div").addClass("component c_filter_search").
                            append(getElement("div").addClass("sub-component show-comp").
                                    append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"))).
                            append($_range.removeClass("hide-comp"));
                    $_component.data("option", 1);
                }
            }
        }
        if (sizeDate[0] == 2) {
            var $_component = getElement("div").addClass("component c_filter_search").
                    append(getElement("div").addClass("sub-component show-comp").
                            append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"))).
                    append($_month.removeClass("hide-comp"));
            $_component.data("option", 2);
        }
    }

    $(container).html($_component);
    $("#c_listSearchType").select2({language: "en"});
    $("#" + id_c_date).datepicker({
        dateFormat: 'yy-mm-dd'
    }).val(dateSystem);

    $("#c_month").select2({language: "en"});
    var objectData = new Date();
    var actuallyMonth = objectData.getMonth() + 1;

    if ($("#c_month").length != 0) {
        $("#c_month").select2({val: actuallyMonth, language: "en"});
    }

    $("#c_year").select2({language: "en"});

    validateDateRange(id_date_i, id_date_e, 'yy-mm-dd');
    $(".radio").buttonset();

    $("input[name='type_search']").on("change", function () {
        var val = $(this).val();
        if (val == "0") {
            $(".c_filter_search .sub-ui").addClass("hide-comp");
            $(".c_filter_search .filter_day").removeClass("hide-comp");
        } else if (val == "1") {
            $(".c_filter_search .sub-ui").addClass("hide-comp");
            $(".c_filter_search .filter_range").removeClass("hide-comp");
        } else {
            $(".c_filter_search .sub-ui").addClass("hide-comp");
            $(".c_filter_search .filter_month").removeClass("hide-comp");
        }
    });

    hideLoader();
    // Arreglame y salvas la vida de un panda
    $('#tbr').trigger('click');
    $('#tbm').trigger('click');
    $('#tbr').trigger('click');
//    return 0;
}

function build_filterSearch2(container, sizeDate, s_label, new_component, id_date_component) {
    var i, $_aux;
    var l = sizeDate.length;

    /* Variables locales para multiples identificadores */

    var id_date_i = (id_date_component) == null ? ("c_daterango_i") : ("c_daterango_i_" + id_date_component);
    var id_date_e = (id_date_component) == null ? ("c_daterango_e") : ("c_daterango_e_" + id_date_component);
    var id_c_date = (id_date_component) == null ? ("c_date") : ("c_date_" + id_date_component);


    var $_input_day = getElement("input").attr({type: "text", maxlength: "25", name: id_c_date, id: id_c_date, value: dateSystem});

    var $_input_date_i = getElement("input").attr({type: "text", maxlength: "20", name: id_date_i, id: id_date_i});
    var $_input_date_e = getElement("input").attr({type: "text", maxlength: "20", name: id_date_e, id: id_date_e});

    var $_select_month = getElement("select").attr({id: "c_month", name: "c_month", style: "width: 100%;"});
    var $_select_year = getElement("select").attr({id: "c_year", name: "c_year", style: "width: 100%;"});

    for (i = 0; i < a_monthNames.length; i++) {
        $_select_month.append(getElement("option").attr("value", (i + 1)).text(a_monthNames[i]));
    }
    for (i = 0; i < a_years.length; i++) {
        $_select_year.append(getElement("option").attr("value", (a_years[i])).text(a_years[i]));
    }

    var $_day = getElement("div").addClass("sub-component sub-ui filter_day").
            append(getElement("label").addClass("lbl-component ").text(s_day)).
            append(getElement("div").append($_input_day));

    var $_range = getElement("div").addClass("sub-component-group sub-ui hide-comp filter_range").
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_dateIni)).
                    append(getElement("div").append($_input_date_i))).
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_dateEnd)).
                    append(getElement("div").append($_input_date_e)));

    var $_month = getElement("div").addClass("sub-component-group sub-ui hide-comp filter_month").
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_month)).
                    append(getElement("div").append($_select_month))).
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_year)).
                    append(getElement("div").append($_select_year)));

    if (l > 1) {
        var $_radio_listSearchType = build_radioButtonsStandar([

            {id: "tbr", value: "0", text: s_range, name: "type_search", default: "1"},
            {id: "tbm", value: "1", text: s_month, name: "type_search", default: "0"}
        ], l);

        var $_component = getElement("div").addClass("component c_filter_search").
                append(getElement("div").addClass("sub-component show-comp").
                        append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :")).
                        append(getElement("div").append($_radio_listSearchType)));

        for (var i = 0, max = l; i < max; i++) {
            $_aux = sizeDate[i];
//            if ($_aux == 0) {
//                $_component.append($_day);
//            }
            if ($_aux == 0) {
                $_component.append($_range);
            }
            if ($_aux == 1) {
                $_component.append($_month);
            }
        }
    } else {

        var filter_title = getElement("div").addClass("sub-component show-comp").append
                (getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"));

//        if (sizeDate[0] == 0) {
//            var $_component = getElement("div").addClass("component c_filter_search").
//                    append(filter_title).
//                    append($_day);
//            $_component.data("option", 0);
//        }
        if (sizeDate[0] == 0) {
            if (new_component != null) {
                var $_component = getElement("div").addClass("component c_filter_search").
                        append(getElement("div").addClass("sub-component show-comp").
                                append(new_component).
                                append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"))).
                        append($_range.removeClass("hide-comp"));
                $_component.data("option", 0);
            } else {
                var $_component = getElement("div").addClass("component c_filter_search").
                        append(getElement("div").addClass("sub-component show-comp").
                                append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"))).
                        append($_range.removeClass("hide-comp"));
                $_component.data("option", 0);
            }
        }
        if (sizeDate[0] == 1) {
            var $_component = getElement("div").addClass("component c_filter_search").
                    append(getElement("div").addClass("sub-component show-comp").
                            append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"))).
                    append($_month.removeClass("hide-comp"));
            $_component.data("option", 1);
        }
    }

    $(container).html($_component);
    $("#c_listSearchType").select2({language: "en"});
    $("#" + id_c_date).datepicker({
        dateFormat: 'yy-mm-dd'
    }).val(dateSystem);

    $("#c_month").select2({language: "en"});
    var objectData = new Date();
    var actuallyMonth = objectData.getMonth() + 1;

    if ($("#c_month").length != 0) {
        $("#c_month").select2({val: actuallyMonth, language: "en"});
    }

    $("#c_year").select2({language: "en"});

    validateDateRange(id_date_i, id_date_e, 'yy-mm-dd');
    $(".radio").buttonset();

    $("input[name='type_search']").on("change", function () {
        var val = $(this).val();
        if (val == "0") {
            $(".c_filter_search .sub-ui").addClass("hide-comp");
            $(".c_filter_search .filter_range").removeClass("hide-comp");
        } else {
            $(".c_filter_search .sub-ui").addClass("hide-comp");
            $(".c_filter_search .filter_month").removeClass("hide-comp");
        }
    });

    hideLoader();
//    return 0;
}

function build_filterSearch_dynamic(container, sizeDate, s_label, new_component, id_date_component, dynamic) {
    var i, $_aux;
    var l = sizeDate.length;

    /* Variables locales para multiples identificadores */

    var id_date_i = (id_date_component) == null ? ("c_daterango_i") : ("c_daterango_i_" + id_date_component);
    var id_date_e = (id_date_component) == null ? ("c_daterango_e") : ("c_daterango_e_" + id_date_component);
    var id_c_date = (id_date_component) == null ? ("c_date") : ("c_date_" + id_date_component);


    var $_input_day = getElement("input").attr({type: "text", maxlength: "25", name: id_c_date, id: id_c_date, value: dateSystem});

    var $_input_date_i = getElement("input").attr({type: "text", maxlength: "20", name: id_date_i, id: id_date_i});
    var $_input_date_e = getElement("input").attr({type: "text", maxlength: "20", name: id_date_e, id: id_date_e});

    var $_select_month = getElement("select").attr({id: "c_month", name: "c_month", style: "width: 100%;"});
    var $_select_year = getElement("select").attr({id: "c_year", name: "c_year", style: "width: 100%;"});

    for (i = 0; i < a_monthNames.length; i++) {
        $_select_month.append(getElement("option").attr("value", (i + 1)).text(a_monthNames[i]));
    }
    for (i = 0; i < a_years.length; i++) {
        $_select_year.append(getElement("option").attr("value", (a_years[i])).text(a_years[i]));
    }

    var $_day = getElement("div").addClass("sub-component sub-ui filter_day").
            append(getElement("label").addClass("lbl-component ").text(s_day)).
            append(getElement("div").append($_input_day));

    var $_range = getElement("div").addClass("sub-component-group sub-ui hide-comp filter_range").
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_dateIni)).
                    append(getElement("div").append($_input_date_i))).
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_dateEnd)).
                    append(getElement("div").append($_input_date_e)));

    var $_month = getElement("div").addClass("sub-component-group sub-ui hide-comp filter_month").
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_month)).
                    append(getElement("div").append($_select_month))).
            append(getElement("div").addClass("sub-component").
                    append(getElement("label").addClass("lbl-component").text(s_year)).
                    append(getElement("div").append($_select_year)));

    if (l > 1) {
        var $_radio_listSearchType = build_radioButtonsStandar([
            {id: "tbd", value: "0", text: s_day, name: "type_search", default: "1"},
            {id: "tbr", value: "1", text: s_range, name: "type_search", default: "0"},
            {id: "tbm", value: "2", text: s_month, name: "type_search", default: "0"}
        ], l);

        var $_component = getElement("div").addClass("component");

        if (dynamic == 1)
            $_component.addClass("c_filter_search");

        $_component.append(getElement("div").addClass("sub-component show-comp").
                append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :")).
                append(getElement("div").append($_radio_listSearchType)));

        for (var i = 0, max = l; i < max; i++) {
            $_aux = sizeDate[i];
            if ($_aux == 0) {
                $_component.append($_day);
            }
            if ($_aux == 1) {
                $_component.append($_range);
            }
            if ($_aux == 2) {
                $_component.append($_month);
            }
        }
    } else {

        var filter_title = getElement("div").addClass("sub-component show-comp").append
                (getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"));

        if (sizeDate[0] == 0) {
            if (new_component != null) {
                var $_component = getElement("div").addClass("component");

                if (dynamic == 1)
                    $_component.addClass("c_filter_search");

                $_component.append(getElement("div").addClass("sub-component show-comp").
                        append(new_component).
                        append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :")).
                        append($_day));
                $_component.data("option", 0);
            } else {
                var $_component = getElement("div").addClass("component");

                if (dynamic == 1)
                    $_component.addClass("c_filter_search");

                $_component.append(filter_title).
                        append($_day);
                $_component.data("option", 0);
            }
        }
        if (sizeDate[0] == 1) {
            if (new_component != null) {
                var $_component = getElement("div").addClass("component");

                if (dynamic == 1)
                    $_component.addClass("c_filter_search");

                $_component.append(getElement("div").addClass("sub-component show-comp").
                        append(new_component).
                        append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"))).
                        append($_range.removeClass("hide-comp"));
                $_component.data("option", 1);
            } else {
                var $_component = getElement("div").addClass("component");

                if (dynamic == 1)
                    $_component.addClass("c_filter_search");

                $_component.append(getElement("div").addClass("sub-component show-comp").
                        append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"))).
                        append($_range.removeClass("hide-comp"));
                $_component.data("option", 1);
            }
        }
        if (sizeDate[0] == 2) {
            var $_component = getElement("div").addClass("component");

            if (dynamic == 1)
                $_component.addClass("c_filter_search");

            $_component.append(getElement("div").addClass("sub-component show-comp").
                    append(getElement("label").addClass("lbl-component lbl-c-main").text(s_label + " :"))).
                    append($_month.removeClass("hide-comp"));
            $_component.data("option", 2);
        }
    }

    $(container).html($_component);
    $("#c_listSearchType").select2({language: "en"});
    $("#" + id_c_date).datepicker({
        dateFormat: 'yy-mm-dd'
    }).val(dateSystem);

    $("#c_month").select2({language: "en"});
    var objectData = new Date();
    var actuallyMonth = objectData.getMonth() + 1;

    if ($("#c_month").length != 0) {
        $("#c_month").select2({val: actuallyMonth, language: "en"});
    }

    $("#c_year").select2({language: "en"});

    validateDateRange(id_date_i, id_date_e, 'yy-mm-dd');
    $(".radio").buttonset();

    $("input[name='type_search']").on("change", function () {
        var val = $(this).val();
        if (val == "0") {
            $(".c_filter_search .sub-ui").addClass("hide-comp");
            $(".c_filter_search .filter_day").removeClass("hide-comp");
        } else if (val == "1") {
            $(".c_filter_search .sub-ui").addClass("hide-comp");
            $(".c_filter_search .filter_range").removeClass("hide-comp");
        } else {
            $(".c_filter_search .sub-ui").addClass("hide-comp");
            $(".c_filter_search .filter_month").removeClass("hide-comp");
        }
    });

    hideLoader();
//    return 0;
}

function validateDateRange(cNotFechaInicial, cNotFechaFinal, dateFormat) {

    var dates = $("#" + cNotFechaInicial + ", #" + cNotFechaFinal + "").datepicker({
        changeYear: false,
        changeMonth: false,
        closeText: 'Cerrar',
        prevText: '&#x3c;Ant',
        nextText: 'Sig&#x3e;',
        currentText: 'Hoy',
        monthNames: ['Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'],
        monthNamesShort: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
        dayNames: ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'],
        dayNamesShort: ['Dom', 'Lun', 'Mar', 'Mié', 'Juv', 'Vie', 'Sáb'],
        dayNamesMin: ['Do', 'Lu', 'Ma', 'Mi', 'Ju', 'Vi', 'Sá'],
        weekHeader: 'Sm',
        dateFormat: dateFormat,
        firstDay: 0,
        isRTL: false,
        showMonthAfterYear: false,
        yearSuffix: '',
        yearRange: '1970:2050',
        onSelect: function (selectedDate) {
            var option = this.id === "" + cNotFechaInicial + "" ? "minDate" : "maxDate",
                    instance = $(this).data("datepicker"),
                    date = $.datepicker.parseDate(
                            instance.settings.dateFormat ||
                            $.datepicker._defaults.dateFormat,
                            selectedDate, instance.settings);
            date.setDate(date.getDate() + 1);
            dates.not(this).datepicker("option", option, date);
        },
        onClose: function () {
            $(this).trigger("change")
        }
    });
    var f = new Date();
    var fnext = new Date(Date.now() + 1 * 24 * 60 * 60 * 1000);
    $("#" + cNotFechaInicial).datepicker("setDate", f);
    $("#" + cNotFechaFinal).datepicker("setDate", fnext);
}

// LISTA DE UNIDADES DE NEGOCIO - COMBOBOX MULTIPLE
function build_business_unit_selectMultiple(container, option_all, data_business_unit) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(s_businessUnit);
    var $_element = getElement("select").attr({id: "c_businessUnit", multiple: "", name: "c_businessUnit", style: "width: 100%;"});
    var $_button = getElement("button").addClass("btn btn-default").text("Todos").attr({id: "c_all_businessUnit"});
    var $_button2 = getElement("button").addClass("btn btn-default").text("Limpiar").attr({id: "c_clear_businessUnit"});

    var l = Object.keys(data_business_unit).length;
    if (l > 0) {
        for (var key in data_business_unit) {
            $_element.append(getElement("option").attr({value: key}).text(data_business_unit[key]));
        }
    }
    $_content.append($_label).append($_element);

    if (option_all) {
        $_content.append("<br>").append($_button).append($_button2);
    }

    $(container).html($_content);
    $("#c_businessUnit").select2({
        language: "en",
        placeholder: m_selectBusinessUnit
    });

    if (option_all) {
        $("#c_all_businessUnit").on("click", function (e) {

            var a_all = [];
            $("#c_businessUnit option").each(function () {
                a_all.push($(this).attr('value'));
            });
            $("#c_businessUnit").val(a_all).trigger("change");
            e.preventDefault();
            return false;
        });
        $("#c_clear_businessUnit").on("click", function (e) {

            $("#c_businessUnit").val(null).trigger("change");
            e.preventDefault();
            return false;
        });
    }
}

// LISTA DE OPERACIONES - COMBOBOX MULTIPLE
function build_contact_mode_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_contact_mode);
    var $_element = getElement("select").attr({id: "c_contact_mode", multiple: "", name: "c_contact_mode", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listContactMode',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {

                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
        }
    });
    $(container).html($_content);
    $("#c_contact_mode").select2({
        language: "en",
        placeholder: m_selectContactMode
    });
}



// LISTA DE ESTADOS - COMBOBOX MULTIPLE
function build_crm_states_selectMultiple(container, id, detail, hint) {
    var s_url = '';
    var s_label = '';
    if (detail) {
        s_url = enviroment + '/components/listCommercialCasesDetailStates';
        s_label = s_state_commercial_case_detail;
    } else {
        s_url = enviroment + '/components/listCommercialCasesStates';
        s_label = s_state_commercial_case;
    }
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_label);
    var $_element = getElement("select").attr({id: id, multiple: "", name: id, style: "width: 100%;"});
    var $_hint = "";
    if (hint != '' && hint != null) {
        $_hint = getElement("span").addClass("lbl-component-simple").text(hint);
        $_content.append($_label).append($_element).append($_hint);
    } else {
        $_content.append($_label).append($_element);
    }


    $.ajax({
        url: s_url,
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].id));
                }
            }
        }
    });
    $(container).html($_content);
    $("#" + id).select2({
        language: "en",
        placeholder: m_selectCrmState
    });
}

function build_agreement_selectMultiple(container) {
    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main  ").text(s_agreement);
    var $_element = getElement("select").attr({id: "c_agreement", multiple: "", name: "c_agreement", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listAgreement',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_agreement").select2({
        language: "en",
        placeholder: m_selectAgreement
    });
}

//LISTA DE PROYECTOS
function build_projects_selectMultiple(container) {

    var $_content = getElement("div");
    var $_label = getElement("label").addClass("lbl-component lbl-c-main").text(s_project);
    var $_element = getElement("select").attr({id: "c_projects", multiple: "", name: "c_projects", style: "width: 100%;"});
    $_content.append($_label).append($_element);

    $.ajax({
        url: enviroment + '/components/listProjects',
        type: 'post',
        dataType: 'json',
        beforeSend: function () {
            showLoader();
            window.active_ajax++;
        },
        complete: function () {
            hideLoader();
        },
        success: function (response) {
            var data = response.data;
            var l = data.length;
            if (l > 0) {
                for (var i = 0, max = l; i < max; i++) {
                    $_element.append(getElement("option").attr({value: data[i].id}).text(data[i].value));
                }
            }
            window.active_ajax--;
        },
        error: function (request, status, error) {
            window.active_ajax--;
        }
    });

    $(container).html($_content);
    $("#c_projects").select2({
        language: "en",
        placeholder: m_selectProject
    });
}

$(document).on("ready", function () {
//    $(".report").append(getElement("div").addClass("loader-content-report").append(getElement("div").addClass("loader-report")));
});
/* Fin de Componente de Fecha */

