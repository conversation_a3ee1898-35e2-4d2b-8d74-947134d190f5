<?php

class DocumentRepository implements IDocumentRepository {

    public function getFirstDocument(
            $scenarioId,
            $internal = null,
            $serialized = null,
            $sunatCode = null,
            $documentCode = null,
            $person_type = Document::PERSON_TYPE_ANY,
            $default = null
    ) {
        $command = $this->buildQuery($scenarioId, $internal, $serialized, $sunatCode, $documentCode, $person_type, $default);
        return $command->queryRow();
    }

    public function getAllDocuments(
            $scenarioId,
            $internal = null,
            $serialized = null,
            $sunatCode = null,
            $documentCode = null,
            $person_type = Document::PERSON_TYPE_ANY,
            $default = null
    ) {
        $command = $this->buildQuery($scenarioId, $internal, $serialized, $sunatCode, $documentCode, $person_type, $default);
        return $command->queryAll();
    }

    private function buildQuery($scenarioId, $internal = null, $serialized = null, $sunatCode = null, $documentCode = null, $personType = null, $default = null) {

        $conditions = 'OP2.type = :type AND OP2.owner = :owner AND OP2.owner_id = D.document_id';

        if ($internal !== null) {
            $conditions .= ' AND D.internal = ' . (int) $internal;
        }

        if ($default !== null) {
            $conditions .= ' AND OP2.`default` = ' . (int) $default;
        }

        if ($serialized !== null) {
            $conditions .= ' AND D.serialized = ' . (int) $serialized;
        }

        if ($sunatCode !== null) {
            $conditions .= " AND D.sunat_code = '" . addslashes($sunatCode) . "'";
        }

        if ($documentCode !== null) {
            $conditions .= " AND D.document_code = '" . addslashes($documentCode) . "'";
        }

        if ($personType !== null && $personType !== Document::PERSON_TYPE_ANY) {
            $conditions .= " AND D.person_type = '" . addslashes($personType) . "'";
        }

        $command = Yii::app()->db->createCommand()
                ->select([
                    'D.document_id',
                    'D.document_code',
                    'D.serialized',
                    'OP2.default'
                ])
                ->from('document D')
                ->join('owner_pair OP2', $conditions, [
                    ':type' => OwnerPair::TYPE_SCENARIO_DOCUMENT,
                    ':owner' => Document::OWNER
                ])
                ->join('owner_pair OP1', 'OP1.pair_id = OP2.parent_id AND OP1.owner_id = :owner_id', [
            ':owner_id' => $scenarioId
        ]);

        return $command;
    }

}
