<?php

class SIANCategoryRemuneration extends CWidget {

    public $id;
    public $form;
    public $model;
    public $modal_id;
    public $fixed = 2;
    public $step = 0.01;
    //PRIVATE
    private $controller;
    private $date_ini_input_id;
    private $date_end_input_id;
    private $remuneration_diary_input_id;
    private $buc_id;
    private $add_button_id;

    public function init() {

        //CONTROL
        $this->controller = Yii::app()->controller;
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->modal_id = isset($this->modal_id) ? $this->modal_id : $this->controller->getServerId();
        //PRIVATE
        $this->date_ini_input_id = $this->controller->getServerId();
        $this->date_end_input_id = $this->controller->getServerId();
        $this->buc_id = $this->controller->getServerId();
        $this->remuneration_diary_input_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();

        //ITEMS
        $items = [];

        foreach ($this->model->tempItems as $item) {
            $attributes = [];
            array_push($attributes, "date_ini:'{$item->date_ini}'");
            array_push($attributes, "date_end:'{$item->date_end}'");
            array_push($attributes, "remuneration_diary:{$item->remuneration_diary}");
            array_push($attributes, "buc:{$item->buc}");
            array_push($attributes, "errors:" . json_encode($item->getErrors()));
            array_push($items, '{' . implode(',', $attributes) . "}");
        }

        $items = '[' . implode(',', $items) . ']';

        //Assets
        SIANAssets::registerScriptFile('other/moment/moment.js');
        SIANAssets::registerScriptFile("other/jquery.datetimepicker/js/jquery.datetimepicker.js");
        SIANAssets::registerCssFile("other/jquery.datetimepicker/css/jquery.datetimepicker.css");

        //ITEMS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "
            
        $(document).ready(function() {
            //FORMAT
            var date_format = '" . Yii::app()->params['date_php_format'] . "';
            $('#{$this->id}').data('date_format', date_format);
            //COUNT
            $('#{$this->id}').data('count', 0);
            //MODAL
            $('#{$this->id}').data('modal_id', '{$this->modal_id}');
            //MOVIMIENTOS
            var array = {$items};

            if (array.length > 0)
            {
                for (var i = 0; i < array.length; i++)
                {
                    SIANCategoryRemunerationAddItem('{$this->id}', moment(array[i]['date_ini'], 'DD/MM/YYYY'), moment(array[i]['date_end'], 'DD/MM/YYYY'), array[i]['remuneration_diary'], array[i]['buc'], array[i]['errors']);
                }
                $('#{$this->remuneration_diary_input_id}').val(array[array.length-1]['remuneration_diary']);
                $('#{$this->buc_id}').val(array[array.length-1]['buc']);
            }
            else
            {
                $('#{$this->id}').find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
            }
           
            SIANCategoryRemunerationUpdate('{$this->id}');
            unfocusable();
            
            //Para hacer reordenable las filas
            SIANCategoryRemunerationSortable('{$this->id}');
                
            $('#{$this->date_ini_input_id}').datetimepicker({
                lang: 'es',
                format: date_format,
                formatDate: date_format,
                timepicker: false,
                closeOnDateSelect: true,
                scrollMonth: false,
                scrollInput: false
            });
            
            $('#{$this->date_end_input_id}').datetimepicker({
                lang: 'es',
                format: date_format,
                formatDate: date_format,
                timepicker: false,
                closeOnDateSelect: true,
                scrollMonth: false,
                scrollInput: false
            });
     
            $('#{$this->buc_id}').focus();
        });
       
        $('body').on('click', '#{$this->add_button_id}', function(e) {

            var date_ini = $('#{$this->date_ini_input_id}').val();
            var date_end = $('#{$this->date_end_input_id}').val();
            var remuneration_diary = $('#{$this->remuneration_diary_input_id}').floatVal({$this->fixed});
            var buc = $('#{$this->buc_id}').floatVal({$this->fixed});

            if(date_ini && date_end)
            {                
                SIANCategoryRemunerationAddItem('{$this->id}', moment(date_ini, 'DD/MM/YYYY'), moment(date_end, 'DD/MM/YYYY'), remuneration_diary, buc, []); 
                
                SIANCategoryRemunerationUpdate('{$this->id}');
                unfocusable();
            }
            
            $('#{$this->remuneration_diary_input_id}').floatVal({$this->fixed}, 0);
            $('#{$this->buc_id}').floatVal({$this->fixed}, 0);
        });  
        
        ", CClientScript::POS_END);


        Yii::app()->clientScript->registerScript(get_class($this), "
        
        function SIANCategoryRemunerationSortable(table_id)
        {
            //Para hacer reordenable las filas
            $(function() {
                $('#' + table_id).find('tbody').sortable({
                    stop: function( event, ui ) {
                        SIANCategoryRemunerationUpdate(table_id);
                    }
                });
            });
        }
        
        function SIANCategoryRemunerationUpdate(table_id){
        
            var table = $('#' + table_id);
            
            var q = (table.find('tbody tr.sian-category-remuneration-item')).length;
            table.find('tbody tr.sian-category-remuneration-item').each(function(index) {
                var row = $(this);   
                row.find('span.sian-category-remuneration-item-order').text(q-index);
            });
        }
        
        function SIANCategoryRemunerationAddItem(table_id, date_ini, date_end, remuneration_diary, buc, errors)
        {
            //TABLE
            var table = $('#' + table_id);
            var count = parseInt(table.data('count'));
            var modal_id = table.data('modal_id');
            var date_format = table.data('date_format');

            //ID
            var id = getLocalId();
            var date_ini_id = getLocalId();
            var date_end_id = getLocalId();
            var fixed = table.data('fixed');
            var step = table.data('step');

            //HTML
            var row = '';
            row += '<tr id=\'' + id + '\' class=\'sian-category-remuneration-item us-sortable-item\'>';
            
            row += '<td class=\'form-group\'>';
            row += '<span class=\'sian-category-remuneration-item-order\'></span>';
            row += '</td>';
            
            row += '<td class=\'form-group ' + (errors['date_ini'] ? 'has-error' : '') + '\'>';
            row += '<input id=\'' + date_ini_id + '\' type=\'text\' class=\'form-control us-datepicker sian-category-remuneration-date-ini\' name=\'Item[' + id + '][date_ini]\' value=\'' + date_ini.format('DD/MM/YYYY') + '\'>';
            if (errors['date_ini'])
            {
                row += '<span class=\'help-block error\'>' + errors['date_ini'] + '</span>';
            }
            row += '</td>';
            
            row += '<td class=\'form-group ' + (errors['date_end'] ? 'has-error' : '') + '\'>';
            row += '<input id=\'' + date_end_id + '\' type=\'text\' class=\'form-control us-datepicker sian-category-remuneration-date-ini\' name=\'Item[' + id + '][date_end]\' value=\'' + date_end.format('DD/MM/YYYY') + '\'>';
            if (errors['date_end'])
            {
                row += '<span class=\'help-block error\'>' + errors['date_end'] + '</span>';
            }
            row += '</td>';
            
            row += '<td class=\'form-group ' + (errors['remuneration_diary'] ? 'has-error' : '') + '\'>';
            row += '<input type=\'number\' class=\'form-control sian-category-remuneration-remuneration_diary us-double' + fixed + '\' style=\'text-align:right;\' name=\'Item[' + id + '][remuneration_diary]\' value=\'' + remuneration_diary.toFixed(fixed) + '\' min=\'0\' step=\'' + step + '\'>';
            if (errors['remuneration_diary'])
            {
                row += '<span class=\'help-block error\'>' + errors['remuneration_diary'] + '</span>';
            }
            row += '</td>';

            row += '<td class=\'form-group ' + (errors['buc'] ? 'has-error' : '') + '\'>';
            row += '<input type=\'number\' class=\'form-control sian-category-remuneration-insurance-tax us-double' + fixed + '\' style=\'text-align:right;\' name=\'Item[' + id + '][buc]\' value=\'' + buc.toFixed(fixed) + '\' min=\'0\' step=\'' + step + '\'>';
            if (errors['buc'])
            {
                row += '<span class=\'help-block error\'>' + errors['buc'] + '</span>';
            }
            row += '</td>';
            
            row += '<td>';      
            row += '<a title=\'Borrar ítem\' onclick = \'SIANCategoryRemunerationRemoveItem(\"' + table_id + '\", \"' + id + '\", false)\'><span class=\'fa fa-lg fa-times black\'></span></a>';
            row += '</td>';
            
            row += '</tr>';
            
            //COUNT DE ITEMS
            if(count === 0)
            {
                table.find('tbody').html(row);
            }
            else
            {
                table.find('tbody').prepend(row);
            }
            
            table.data('count', count + 1);
            $('#' + date_ini_id).datetimepicker({
                lang: 'es',
                format: date_format,
                formatDate: date_format,
                timepicker: false,
                closeOnDateSelect: true,
                scrollMonth: false,
                scrollInput: false
            });
            $('#' + date_end_id).datetimepicker({
                lang: 'es',
                format: date_format,
                formatDate: date_format,
                timepicker: false,
                closeOnDateSelect: true,
                scrollMonth: false,
                scrollInput: false
            });
        }
        
        function SIANCategoryRemunerationRemoveItem(table_id, id, confirmation)
        {
            if(confirmation ? confirm('¿Está seguro de eliminar este ítem?') : true)
            {
                var table = $('#' + table_id);
                var count = parseInt(table.data('count'));

                $('#' + id).remove();


                if(count === 1)
                {
                    table.find('tbody').html('<tr><td class=\'empty\' colspan=\'99\'>" . Strings::NO_DATA . "</td></tr>');
                }

                //COUNT DE ITEMS
                table.data('count', count - 1);
                SIANCategoryRemunerationUpdate(table_id);
            }
        }
       
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array('title' => $this->model->getAttributeLabel('tempItems'),
            'headerIcon' => 'list',
            'htmlOptions' => array(
                'class' => $this->model->hasErrors('tempItems') ? 'us-error' : ''
            )
        ));
        echo "<div class='row'>";
        echo "<div class='col-lg-11 col-md-11 col-sm-12 col-xs-12'>";
        //SUB ROW
        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo SIANForm::textFieldNonActive($this->model->getAttributeLabel('items.date_ini'), null, SIANTime::formatDate(), array(
            'id' => $this->date_ini_input_id,
            'placeholder' => $this->model->getAttributeLabel('items.date_ini'),
            'class' => 'us-datepicker'
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo SIANForm::textFieldNonActive($this->model->getAttributeLabel('items.date_end'), null, SIANTime::formatDate(), array(
            'id' => $this->date_end_input_id,
            'placeholder' => $this->model->getAttributeLabel('items.date_end'),
            'class' => 'us-datepicker'
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo SIANForm::numberFieldNonActive($this->model->getAttributeLabel('items.remuneration_diary'), null, 0.00, array(
            'id' => $this->remuneration_diary_input_id,
            'min' => 0,
            'step' => $this->step,
            'class' => "us-double{$this->fixed}",
        ));
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-6 col-xs-12'>";
        echo SIANForm::numberFieldNonActive($this->model->getAttributeLabel('items.buc'), null, 0.00, array(
            'id' => $this->buc_id,
            'min' => 0,
            'step' => $this->step,
            'class' => "us-double{$this->fixed}",
        ));
        echo "</div>";
        echo "</div>";
        //SUB ROW
        echo "</div>";
        echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-12'>";
        echo CHtml::label('Agregar', $this->add_button_id, []);
        echo "<br/>";
        $this->widget('application.widgets.USButton', array(
            'id' => $this->add_button_id,
            'block' => true,
            'context' => 'primary',
            'icon' => 'fa fa-lg fa-plus white',
            'size' => 'default',
            'title' => 'Añadir'
        ));
        echo "</div>";
        echo "</div>";
        echo "<hr>";
        echo "<table id='{$this->id}' class='table table-condensed table-hover' data-fixed={$this->fixed} data-step={$this->step}>";
        echo "<thead>";
        echo "<tr>";
        echo "<th width='5%'>#</th>";
        echo "<th width='15%'>{$this->model->getAttributeLabel('items.date_ini')}</th>";
        echo "<th width='15%'>{$this->model->getAttributeLabel('items.date_end')}</th>";
        echo "<th width='15%'>{$this->model->getAttributeLabel('items.remuneration_diary')}</th>";
        echo "<th width='15%'>{$this->model->getAttributeLabel('items.buc')}</th>";
        echo "<th width='5%'>Operaciones</th>";
        echo "</tr>";
        echo "</thead>";
        echo "<tbody></tbody>";
        echo "<tfoot>";
        echo "</tfoot>";
        echo "</table>";
        $this->endWidget();
    }

}
