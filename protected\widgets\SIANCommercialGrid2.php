<?php

class SIANCommercialGrid2 extends CWidget {

    public $id;
    public $product_list_id;
    public $form;
    public $model;
    public $readonly = false;
    public $advanced = false;
    public $as_bill_checkbox_id;
    public $force_igv_checkbox_id;
    public $allow_duplicate_checkbox_id;
    public $retention_checkbox_id;
    public $lock_discount = true;
    public $ifixed = CommercialMovement::IFIXED;
    public $tfixed = CommercialMovement::TFIXED;
    public $istep = CommercialMovement::ISTEP;
    public $tstep = CommercialMovement::TSTEP;
    public $astep = CommercialMovement::ASTEP;
    public $tolerance = CommercialMovement::TOLERANCE;
    public $allow_groups = false;
    public $is_template = false;
    public $only_allowed_divisions = false;
    public $main_title = 'Mercaderías, servicios y combos (Listado principal)';
    public $context = 'success';
    public $onChangeCCDependence = ''; //Para hacer un puente
    public $viewCosts = false;
    public $price_type_items = [];
    //PRIVATE
    private $controller;
    private $presentationMode;
    private $presentationUrl;
    private $presentationItems = [];
    private $comboItems = [];
    private $promotionGiftOptions = [];
    private $include_igv_checkbox_id;
    private $round_mode_select_id;
    private $autocomplete_id;
    private $item_aux_id;
    private $item_value_id;
    private $item_text_id;
    private $item_item_type_id;
    private $item_product_type_id;
    private $item_expiration_id;
    private $item_pres_quantity_id;
    private $item_equivalence_id;
    private $item_warehouse_id;
    private $item_allow_decimals_id;
    private $item_unit_stock_id;
    private $item_mixed_stock_label_id;
    private $item_skip_cost_validation_id;
    private $item_min_price_pen_id;
    private $item_min_price_usd_id;
    private $item_avg_price_pen_id;
    private $item_avg_price_usd_id;
    private $item_web_price_pen_id;
    private $item_web_price_usd_id;
    private $item_igv_affection_id;
    private $item_price_id;
    private $item_discount_id;
    private $item_promotion_item_id;
    private $item_perception_id;
    private $add_button_id;
    private $batch_add_button_id;
    private $summary_id;
    private $sales_specifications_input_id;
    private $observation_input_id;
    private $retention_percent_field_id;
    private $ret_field_id;
    private $no_ret_field_id;
    private $warehouse_input_id;
    private $cost_link_id;
    private $preview_access;
    private $stockUrl;
    private $commercialStockUrl;
    private $explainCommercialStockUrl;
    private $comboItemsUrl;
    private $promotionGiftsUrl;
    private $promotionGiftOptionsUrl;
    private $routesValidate = ["commercial/saleOrder", "commercial/saleQuote", "support/serviceOrder"];
    private $is_commercial_route;
    private $dispatch_multiple_origin;
    private $use_products_related;
    private $route;
    //Totales
    private $summary_crude_input_id;
    private $summary_affected1_input_id;
    private $summary_affected_input_id;
    private $summary_inaffected_input_id;
    private $summary_nobill_input_id;
    private $summary_export_input_id;
    private $summary_free_input_id;
    private $summary_nnet_input_id;
    private $summary_fnet_input_id;
    private $summary_net_input_id;
    private $summary_nigv_input_id;
    private $summary_figv_input_id;
    private $summary_igv_input_id;
    private $summary_ntotal_input_id;
    private $summary_ftotal_input_id;
    private $summary_total_input_id;
    private $summary_perception_input_id;
    private $summary_nreal_input_id;
    private $summary_freal_input_id;
    public $summary_real_input_id;
    private $summary_is_editable;
    private $summary_advance_percentage_input_id;
    private $summary_advance_amount_input_id;
    private $summary_advance_balance_input_id;
    private $batch_autocomplete_id;
    private $batch_item_aux_id;
    private $batch_item_value_id;
    private $batch_item_text_id;
    private $batch_loader_id;
    private $on_group_id;
    private $header_id;

    public function init() {

        $this->controller = Yii::app()->controller;
        //
        $this->presentationMode = SpGetProductPresentations::MODE_COMBOBOX_WITH_PRICES;
        $this->presentationUrl = $this->controller->createUrl("/movement/getPresentations");
        $this->is_commercial_route = $this->model->movement->scenario->isCommercialRoute();
        //GRID ID
        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();
        $this->product_list_id = isset($this->product_list_id) ? $this->product_list_id : $this->controller->getServerId();
        $this->as_bill_checkbox_id = Yii::app()->controller->getServerId();
        $this->force_igv_checkbox_id = Yii::app()->controller->getServerId();
        $this->allow_duplicate_checkbox_id = Yii::app()->controller->getServerId();
        //PRIVATE
        $this->include_igv_checkbox_id = $this->controller->getServerId();
        $this->retention_checkbox_id = Yii::app()->controller->getServerId();
        $this->round_mode_select_id = $this->controller->getServerId();
        $this->autocomplete_id = $this->controller->getServerId();
        $this->item_aux_id = $this->controller->getServerId();
        $this->item_value_id = $this->controller->getServerId();
        $this->item_text_id = $this->controller->getServerId();
        $this->item_item_type_id = $this->controller->getServerId();
        $this->item_product_type_id = $this->controller->getServerId();
        $this->item_pres_quantity_id = $this->controller->getServerId();
        $this->item_equivalence_id = $this->controller->getServerId();
        $this->item_warehouse_id = $this->controller->getServerId();
        $this->item_unit_stock_id = $this->controller->getServerId();
        $this->item_mixed_stock_label_id = $this->controller->getServerId();
        $this->item_allow_decimals_id = $this->controller->getServerId();
        $this->item_skip_cost_validation_id = $this->controller->getServerId();
        $this->item_min_price_pen_id = $this->controller->getServerId();
        $this->item_min_price_usd_id = $this->controller->getServerId();
        $this->item_avg_price_pen_id = $this->controller->getServerId();
        $this->item_avg_price_usd_id = $this->controller->getServerId();
        $this->item_web_price_pen_id = $this->controller->getServerId();
        $this->item_web_price_usd_id = $this->controller->getServerId();
        $this->item_igv_affection_id = $this->controller->getServerId();
        $this->item_price_id = $this->controller->getServerId();
        $this->item_discount_id = $this->controller->getServerId();
        $this->item_promotion_item_id = $this->controller->getServerId();
        $this->item_perception_id = $this->controller->getServerId();
        $this->item_expiration_id = $this->controller->getServerId();
        $this->add_button_id = $this->controller->getServerId();
        $this->batch_add_button_id = $this->controller->getServerId();
        $this->observation_input_id = $this->controller->getServerId();
        $this->retention_percent_field_id = Yii::app()->controller->getServerId();
        $this->ret_field_id = Yii::app()->controller->getServerId();
        $this->no_ret_field_id = Yii::app()->controller->getServerId();
        $this->warehouse_input_id = $this->controller->getServerId();
        $this->cost_link_id = $this->controller->getServerId();
        //DIVS
        $this->summary_id = $this->controller->getServerId();
        //
        $this->summary_crude_input_id = $this->controller->getServerId();
        $this->summary_affected1_input_id = $this->controller->getServerId();
        $this->summary_affected_input_id = $this->controller->getServerId();
        $this->summary_inaffected_input_id = $this->controller->getServerId();
        $this->summary_nobill_input_id = $this->controller->getServerId();
        $this->summary_export_input_id = $this->controller->getServerId();
        $this->summary_free_input_id = $this->controller->getServerId();
        $this->summary_nnet_input_id = $this->controller->getServerId();
        $this->summary_fnet_input_id = $this->controller->getServerId();
        $this->summary_net_input_id = $this->controller->getServerId();
        $this->summary_nigv_input_id = $this->controller->getServerId();
        $this->summary_figv_input_id = $this->controller->getServerId();
        $this->summary_igv_input_id = $this->controller->getServerId();
        $this->summary_ntotal_input_id = $this->controller->getServerId();
        $this->summary_ftotal_input_id = $this->controller->getServerId();
        $this->summary_total_input_id = $this->controller->getServerId();
        $this->summary_perception_input_id = $this->controller->getServerId();
        $this->summary_nreal_input_id = $this->controller->getServerId();
        $this->summary_freal_input_id = $this->controller->getServerId();
        $this->summary_advance_percentage_input_id = $this->controller->getServerId();
        $this->summary_advance_amount_input_id = $this->controller->getServerId();
        $this->summary_advance_balance_input_id = $this->controller->getServerId();
        $this->summary_real_input_id = isset($this->summary_real_input_id) ? $this->summary_real_input_id : $this->controller->getServerId();        
        $this->summary_is_editable = in_array($this->model->movement->route, ['logistic/purchaseOrder']);
        $this->model->totals_editable = $this->summary_is_editable ? 1 : 0;
        $this->dispatch_multiple_origin = $this->controller->getOrganization()->globalVar->allow_dispatch_multi_origin && $this->model->movement->route == 'commercial/saleOrder' ? 1 : 0;
        $this->use_products_related = Yii::app()->controller->getOrganization()->globalVar->use_products_related;

        $this->batch_autocomplete_id = $this->controller->getServerId();
        $this->batch_item_aux_id = $this->controller->getServerId();
        $this->batch_item_value_id = $this->controller->getServerId();
        $this->batch_item_text_id = $this->controller->getServerId();
        $this->batch_loader_id = $this->controller->getServerId();
        $this->header_id = $this->controller->getServerId();

        $this->route = Yii::app()->getController()->getRoute();

        //
        $a_product_ids = [];
        $a_combo_ids = [];
        $a_promotion_gift_option_ids = [];
        $a_instance_ids = [];

        $total_affected1 = 0;
        $total_inaffected = 0;
        $total_nobill = 0;
        $total_igv = 0;
        $total_perception = 0;

        foreach ($this->model->tempItems as $o_item) {
            //Si no esá en blanco se agrega ID de producto, es decir tipos M, S y C
            if (!USString::isBlank($o_item->itemObj->product_id)) {
                $a_product_ids[] = $o_item->itemObj->product_id;
            }
            //Recorremos subitems
            if (in_array($o_item->itemObj->product_type, [Product::TYPE_COMBO, Product::TYPE_GROUP])) {
                foreach ($o_item->tempItems as $o_subitem) {
                    if (!USString::isBlank($o_subitem->itemObj->product_id)) {
                        $a_product_ids[] = $o_subitem->itemObj->product_id;
                    }
                    //Instancia
                    $s_sub_instance_id = $this->controller->getServerId();
                    if (USString::isBlank($o_subitem->instance_id)) {
                        $o_subitem->instance_id = $s_sub_instance_id;
                    }

                    $a_instance_ids[$o_subitem->instance_id] = $s_sub_instance_id;

                    $total_affected1 += $o_subitem["affected1_{$this->model->movement->currency}"];
                    $total_inaffected += $o_subitem["inaffected_{$this->model->movement->currency}"];
                    $total_nobill += $o_subitem["nobill_{$this->model->movement->currency}"];
                    $total_igv += $o_subitem["igv_{$this->model->movement->currency}"];
                    $total_perception += $o_subitem["perception_{$this->model->movement->currency}"];
                }
            } else {
                $total_affected1 += $o_item["affected1_{$this->model->movement->currency}"];
                $total_inaffected += $o_item["inaffected_{$this->model->movement->currency}"];
                $total_nobill += $o_item["nobill_{$this->model->movement->currency}"];
                $total_igv += $o_item["igv_{$this->model->movement->currency}"];
                $total_perception += $o_item["perception_{$this->model->movement->currency}"];
            }
            //
            if ($o_item->itemObj->product_type === Product::TYPE_COMBO) {
                $a_combo_ids[] = $o_item->itemObj->product_id;
            }
            //Revisamos
            if (!USString::isBlank($o_item->itemObj->promotion_gift_option_id)) {
                $a_promotion_gift_option_ids[] = $o_item->itemObj->promotion_gift_option_id;
            }
            //Instancia
            $s_instance_id = $this->controller->getServerId();
            if (USString::isBlank($o_item->instance_id)) {
                $o_item->instance_id = $s_instance_id;
            }
            $a_instance_ids[$o_item->instance_id] = $s_instance_id;
        }

        if (in_array($this->model->movement->route, ['logistic/purchaseOrder'])) {

            if (!isset($this->model->max_affected1) || $this->model->max_affected1 == 0) {
                $this->model->max_affected1 = $total_affected1 + $this->tolerance;
            }
            if (!isset($this->model->min_affected1) || $this->model->min_affected1 == 0) {
                $this->model->min_affected1 = $total_affected1 - $this->tolerance;
                $this->model->min_affected1 = ($this->model->min_affected1 < 0 ? 0 : $this->model->min_affected1);
            }
            if (!isset($this->model->max_inaffected) || $this->model->max_inaffected == 0) {
                $this->model->max_inaffected = $total_inaffected + $this->tolerance;
            }
            if (!isset($this->model->min_inaffected) || $this->model->min_inaffected == 0) {
                $this->model->min_inaffected = $total_inaffected - $this->tolerance;
                $this->model->min_inaffected = ($this->model->min_inaffected < 0 ? 0 : $this->model->min_inaffected);
            }
            if (!isset($this->model->max_nobill) || $this->model->max_nobill == 0) {
                $this->model->max_nobill = $total_nobill + $this->tolerance;
            }
            if (!isset($this->model->min_nobill) || $this->model->min_nobill == 0) {
                $this->model->min_nobill = $total_nobill - $this->tolerance;
                $this->model->min_nobill = ($this->model->min_nobill < 0 ? 0 : $this->model->min_nobill);
            }
            if (!isset($this->model->max_igv) || $this->model->max_igv == 0) {
                $this->model->max_igv = $total_igv + $this->tolerance;
            }
            if (!isset($this->model->min_igv) || $this->model->min_igv == 0) {
                $this->model->min_igv = $total_igv - $this->tolerance;
                $this->model->min_igv = ($this->model->min_igv < 0 ? 0 : $this->model->min_igv);
            }
            if (!isset($this->model->max_perception) || $this->model->max_perception == 0) {
                $this->model->max_perception = $total_perception + $this->tolerance;
            }
            if (!isset($this->model->min_perception) || $this->model->min_perception == 0) {
                $this->model->min_perception = $total_perception - $this->tolerance;
                $this->model->min_perception = ($this->model->min_perception < 0 ? 0 : $this->model->min_perception);
            }
        }

        //Datos comunes
        $i_store_id = $this->is_commercial_route == 1 && $this->is_template == 0 ? $this->model->movement->store_id : null;
        $s_emission_date = $this->is_commercial_route == 1 && $this->is_template == 0 ? $this->model->movement->emission_date : null;
        $i_exclude_id = $this->is_commercial_route == 1 && $this->is_template == 0 ? $this->model->movement->kardex_unlock_exclude_id : null;
        //Si hay productos...
        if (count($a_product_ids) > 0) {
            $this->presentationItems = SpGetProductPresentations::getAssociative($this->presentationMode, array_unique($a_product_ids), $this->controller->getOrganization()->globalVar->display_currency, 0, $i_store_id, $s_emission_date, $i_exclude_id);
        }
        if (count($a_combo_ids) > 0) {
            $this->comboItems = DpComboItems::getAssociative(array_unique($a_combo_ids));
        }
        if (count($a_promotion_gift_option_ids) > 0) {
            $this->promotionGiftOptions = SpGetPromotionGiftOptions::getAssociative(SpGetPromotionGiftOptions::MODE_PROMOTION_GIFT_OPTION_WIDGET, array_unique($a_promotion_gift_option_ids), $this->controller->getOrganization()->globalVar->display_currency, $i_store_id, $s_emission_date, $i_exclude_id);
        }
        //PRODUCT ITEMS
        $a_items = [];
        foreach ($this->model->tempItems as $o_item) {
            //
            $a_item = $this->_processItem($o_item, $a_instance_ids, null, null);

            if (in_array($o_item->itemObj->product_type, [Product::TYPE_COMBO, Product::TYPE_GROUP])) {
                //Si es un grupo recorremos sus hijos
                $a_subitems = [];
                foreach ($o_item->tempItems as $o_subitem) {
                    $a_subitems[] = $this->_processItem($o_subitem, $a_instance_ids, $o_item->itemObj->product_type, $o_item->itemObj->product_id);
                }

                $a_item['subitems'] = $a_subitems;
            }

            $a_items[] = $a_item;
        }

        $this->model->totals_editable = in_array($this->model->movement->route, ['logistic/purchaseOrder']) ? 1 : 0;

        //URL
        $this->stockUrl = $this->controller->createUrl("/movement/loadCostsAndStocks");
        $this->commercialStockUrl = $this->controller->createUrl("/movement/loadCommercialStocks");
        $this->explainCommercialStockUrl = $this->controller->createUrl("/logistic/merchandise/explainCommercialStock");
        $this->comboItemsUrl = $this->controller->createUrl('/widget/getComboItems');
        $this->promotionGiftsUrl = $this->controller->createUrl('/widget/getPromotionGifts');
        $this->promotionGiftOptionsUrl = $this->controller->createUrl('/widget/getPromotionGiftOptions');
        //ACCESS
        $this->preview_access = $this->controller->checkRoute('/logistic/product/preview');
        //Registramos assets
        SIANAssets::registerScriptFile('js/sian-commercial-grid-2.js');
        SIANAssets::registerCssFile('css/sian-commercial-grid-2.css');

        //SCRIPTS
        Yii::app()->clientScript->registerScript($this->controller->getServerId(), "

        var divObj = $('#{$this->id}');
        divObj.data('view-access', " . json_encode($this->preview_access) . ");//ACCESS
        divObj.data('view-url', '{$this->controller->createUrl('/logistic/product/preview')}');//URL
        divObj.data('include_igv', $('#{$this->include_igv_checkbox_id}').prop('checked') ? 1 : 0);
        divObj.data('as_bill', $('#{$this->as_bill_checkbox_id}').prop('checked') ? 1 : 0);
        divObj.data('force_igv', $('#{$this->force_igv_checkbox_id}').prop('checked') ? 1 : 0);
        divObj.data('allow_duplicate', $('#{$this->allow_duplicate_checkbox_id}').prop('checked') ? 1 : 0);
        divObj.data('is_commercial_route', " . CJSON::encode($this->is_commercial_route) . ");
        divObj.data('is_template', " . CJSON::encode($this->is_template) . ");
        divObj.data('scope', '{$this->model->movement->scope}');
        divObj.data('from_exchange', '{$this->model->movement->from_exchange}');
        divObj.data('is_buy', " . CJSON::encode($this->model->isBuy()) . ");
        divObj.data('summary_is_editable', " . CJSON::encode($this->summary_is_editable) . ");
        divObj.data('route', '{$this->model->movement->route}');
        divObj.data('igv_affection_map', " . CJSON::encode(CommercialMovementProduct::getIGVAffectionMap()) . ");
        divObj.data('igv_affection_order', " . CJSON::encode(CommercialMovementProduct::getIGVAffectionOrder()) . ");
        divObj.data('currency', '{$this->model->movement->currency}');
        divObj.data('currency_symbol', '" . Currency::getSymbol($this->model->movement->currency) . "');
        divObj.data('exchange_rate', {$this->model->movement->exchange_rate});
        divObj.data('round_mode', {$this->model->round_mode});
        divObj.data('ifixed', {$this->ifixed});
        divObj.data('tfixed', {$this->tfixed});
        divObj.data('istep', {$this->istep});
        divObj.data('astep', {$this->astep});
        divObj.data('tolerance', {$this->tolerance});
        divObj.data('no_detraction_max_amount_pen', {$this->controller->getOrganization()->globalVar->no_detraction_max_amount_pen});
        divObj.data('no_detraction_max_amount_usd', {$this->controller->getOrganization()->globalVar->no_detraction_max_amount_usd});
        divObj.data('readonly', " . CJSON::encode($this->readonly) . ");
        divObj.data('lock_discount', " . CJSON::encode($this->lock_discount) . ");
        divObj.data('use_inaffected', " . CJSON::encode($this->model->use_inaffected) . ");
        divObj.data('use_free', " . CJSON::encode($this->model->use_free) . ");
        divObj.data('advanced', " . CJSON::encode($this->advanced) . ");
        divObj.data('allow_groups', " . CJSON::encode($this->allow_groups) . ");
        divObj.data('igv_global', {$this->controller->getOrganization()->globalVar->igv});
        divObj.data('perception_global', {$this->controller->getOrganization()->globalVar->perception});
        divObj.data('display_currency_global', '{$this->controller->getOrganization()->globalVar->display_currency}');
        divObj.data('price_mode', '{$this->controller->getOrganization()->globalVar->price_mode}');               
        divObj.data('retention4', {$this->controller->getOrganization()->globalVar->retention4});
        divObj.data('main_title', '{$this->main_title}');
        divObj.data('context', '{$this->context}');
        divObj.data('igv_affection_id', '{$this->item_igv_affection_id}');
        divObj.data('summary_id', '{$this->summary_id}');
        divObj.data('product_list_id', '{$this->product_list_id}');
        divObj.data('autocomplete_id', '{$this->autocomplete_id}');
        divObj.data('allow_duplicate_checkbox_id', '{$this->allow_duplicate_checkbox_id}');
        divObj.data('submit_id', '{$this->form->submit_id}');
        divObj.data('default_merchandise_measure', '{$this->controller->getDefaultMerchandiseMeasure()->abbreviation}');
        divObj.data('validate_stock', " . CJSON::encode($this->model->movement->validate_stock == 1) . ");
        divObj.data('presentationMode', '{$this->presentationMode}');
        divObj.data('presentationUrl', '{$this->presentationUrl}');
        divObj.data('stockUrl', '{$this->stockUrl}');
        divObj.data('commercialStockUrl', '{$this->commercialStockUrl}');
        divObj.data('explainCommercialStockUrl', '{$this->explainCommercialStockUrl}');
        divObj.data('comboItemsUrl', '{$this->comboItemsUrl}');
        divObj.data('promotionGiftsUrl', '{$this->promotionGiftsUrl}');
        divObj.data('promotionGiftOptionsUrl', '{$this->promotionGiftOptionsUrl}');
        divObj.data('stock_mode', " . CJSON::encode($this->model->movement->scenario->stock_mode) . ");
        divObj.data('exclude_id', " . CJSON::encode($this->model->movement->kardex_unlock_exclude_id) . ");
        divObj.data('a_product_type_to_validate_prices'," . CJSON::encode($this->controller->getOrganization()->globalVar->a_product_type_to_validate_prices) . ");
        divObj.data('routesValidate'," . CJSON::encode($this->routesValidate) . ");                 
        divObj.data('acceptance_message', '" . Combo::ACCEPTANCE_MESSAGE . "');
        divObj.data('independent_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INDEPENDENT . "');
        divObj.data('inherit_mode', '" . SpGetNetWildcardData::MODE_WILDCARD_INHERIT . "');
        divObj.data('parent_type', null);
        divObj.data('count', 0);//COUNT
        divObj.data('direction', '{$this->model->movement->direction}');
        divObj.data('dispatch_multiple_origin', {$this->dispatch_multiple_origin});
        divObj.data('modifiers', " . CJSON::encode(Document::getModifiers()) . ");
        divObj.data('price_type_items', " . CJSON::encode($this->price_type_items) . ");
        divObj.data('use_products_related', " . CJSON::encode($this->controller->getOrganization()->globalVar->use_products_related) . ");
        " . ( in_array($this->model->movement->route, ['logistic/purchaseOrder']) ? "divObj.data('set_summary_total', 0);" : "divObj.data('set_summary_total', 1);" ) . " 
        
        $(document).ready(function() {
        
            var divObj = $('#{$this->id}');
            divObj.data('loaded', 1);
            " . ( in_array($this->model->movement->route, ['commercial/saleOrder', 'commercial/saleQuote', 'support/serviceOrder', 'commercial/quoteTemplate']) ? "divObj.data('allowCostProducts', 1);" : "divObj.data('allowCostProducts', 0);" ) . "            

            //Generamos tabla principal
            var main_table_id = SIANCommercialGridAddTable('{$this->id}', '', " . CJSON::encode($this->model->getErrors('tempItems')) . ");
            divObj.data('main_table_id', main_table_id);
            divObj.data('active_table_id', main_table_id);
            
            //PRODUCTOS
            var items = " . CJSON::encode($a_items) . ";
                          
            if (items.length > 0)
            {
                $.each(items, function(i, item) 
                {
                    switch(item.product_type)
                    {
                        case '" . Product::TYPE_COMBO . "':
                            console.log(`*409* => case:`+item.product_type);
                            var subtable_id = SIANCommercialGridAddCombo('{$this->id}', item.product_id, item.item_type_id, item.product_name, item.pres_quantity, item.presentationItems, item.equivalence, item.igv_affection,item.price, item.discount, item.is_locked, item.errors);

                            $.each(item.subitems, function(j, subitem) 
                            {
                                SIANCommercialGridAddItem('{$this->id}', subtable_id, subitem.instance_id, subitem.gift_of_instance, subitem.product_id, subitem.item_type_id, subitem.product_type, subitem.product_name, subitem.unit_stock, subitem.mixed_stock, subitem.pres_quantity, subitem.presentationItems, subitem.equivalence, '', subitem.allow_decimals, subitem.skip_cost_validation, subitem.min_price_pen, subitem.min_price_usd, subitem.avg_price_pen, subitem.avg_price_usd, subitem.web_price_pen, subitem.web_price_usd, subitem.price, subitem.discount, subitem.promotion_item_id, subitem.promotion_gift_option_id, subitem.igv_affection, '', subitem.perception_affected, subitem.expiration, subitem.is_locked, '" . Product::TYPE_COMBO . "', subitem.parent_route, subitem.quantity_item_id, subitem.total_item_id, subitem.errors);
                            }); 
                        break;
                        case '" . Product::TYPE_GROUP . "':
                            console.log(`*418* => case:`+item.product_type + ' ' + item.price_type);
                            var subtable_id = SIANCommercialGridAddGroup('{$this->id}', item.product_name, item.pres_quantity, item.equivalence, item.igv_affection, item.min_price_pen, item.min_price_usd, item.avg_price_pen, item.avg_price_usd, item.web_price_pen, item.web_price_usd, item.price, item.discount, item.is_locked, item.price_type, item.errors);

                            $.each(item.subitems, function(j, subitem) 
                            {
                                SIANCommercialGridAddItem('{$this->id}', subtable_id, subitem.instance_id, subitem.gift_of_instance, subitem.product_id, subitem.item_type_id, subitem.product_type, subitem.product_name, subitem.unit_stock, subitem.mixed_stock, subitem.pres_quantity, subitem.presentationItems, subitem.equivalence, '', subitem.allow_decimals, subitem.skip_cost_validation, subitem.min_price_pen, subitem.min_price_usd, subitem.avg_price_pen, subitem.avg_price_usd, subitem.web_price_pen, subitem.web_price_usd, subitem.price, subitem.discount, subitem.promotion_item_id, subitem.promotion_gift_option_id, subitem.igv_affection, subitem.price_type, subitem.perception_affected, subitem.expiration, subitem.is_locked, '" . Product::TYPE_GROUP . "', subitem.parent_route, subitem.quantity_item_id, subitem.total_item_id, subitem.errors);
                            });    
                            break;
                        default:
                            SIANCommercialGridAddItem('{$this->id}', main_table_id, item.instance_id, item.gift_of_instance, item.product_id, item.item_type_id, item.product_type, item.product_name, 
                                                      item.unit_stock, item.mixed_stock, item.pres_quantity, item.presentationItems, item.equivalence, '', item.allow_decimals, item.skip_cost_validation, 
                                                      item.min_price_pen, item.min_price_usd, item.avg_price_pen, item.avg_price_usd, item.web_price_pen, item.web_price_usd, item.price, item.discount, 
                                                      item.promotion_item_id, item.promotion_gift_option_id, item.igv_affection, item.price_type, item.perception_affected, item.expiration, item.is_locked, null, 
                                                      item.parent_route, item.quantity_item_id, item.total_item_id, item.errors);
                        break;
                    }
                });
            }
            
            //UPDATE
            SIANCommercialGridUpdateIn('{$this->id}', true);
            SIANCommercialGridGetIds('{$this->id}');
            SIANCommercialGridUpdateAmounts('{$this->id}');          
            unfocusable();        
            
            //CURRENCY SYMBOL
            $('span.currency-symbol').text('" . Currency::getSymbol($this->model->movement->currency) . "');
            divObj.data('set_summary_total', 1);
        });

        
        $('#{$this->summary_advance_amount_input_id}').change(function() {
            var value = parseFloat($(this).val());
            var total_value = parseFloat($('#{$this->summary_real_input_id}').val());

            if(value > total_value){
                $(this).val(total_value)
            }

            var new_value = parseFloat($(this).val())

            var percentage = (new_value / total_value) * 100;

            $('#{$this->summary_advance_percentage_input_id}').val(isNaN(percentage) ? 0 : percentage.toFixed(2));
        });

        $('#{$this->summary_advance_percentage_input_id}').change(function() {

            var percentage = parseFloat($(this).val());
            var total_value = parseFloat($('#{$this->summary_real_input_id}').val());

            var new_value = parseFloat($(this).val())

            var result = (total_value * percentage) / 100;

            $('#{$this->summary_advance_amount_input_id}').val(isNaN(result) ? 0 : result.toFixed(2));
        });

        //SI CAMBIA OPERACION
        $('#{$this->id}').data('changeOperation', function(changeData){
            var gridObj =  $('#{$this->id}');
            //GUARDAMOS INFO DE OPERACIÓN
            gridObj.data('operationChangeData', changeData);

            SIANCommercialGridUpdateAmounts('{$this->id}');
        });

        //SI CAMBIA LA MONEDA
        $('#{$this->id}').data('changeCurrency', function(currency){
            console.log(`CAMBIO LA MONEDA`);
            //Cambiamos la moneda
            SIANCommercialGridChangeCurrency('{$this->id}', currency);
            //Limpiamos
            USAutocompleteReset('{$this->autocomplete_id}');
            //Seteamos símbolo
            $('span.currency-symbol').text(USCurrencyGetSymbol(currency));
        });    

        $('#{$this->id}').data('changeDocument', function(changeData){
            
            var divObj = $('#{$this->id}');
            var scope = divObj.data('scope');
            var is_commercial_route = divObj.data('is_commercial_route');
            var modifiers = divObj.data('modifiers');
              
            " . ($this->model->isBuy() ? "
                
            //Desactivamos y seteamos data de sunat
            $('#{$this->retention_checkbox_id}').data('sunat_code', changeData.sunat_code);
            //Comparamos
            if(scope == '" . Scenario::SCOPE_NOT_DOMICILED . "' || changeData.sunat_code == '" . Document::HONORARIOS . "')
            {
                //Quitamos readonly
                $('#{$this->retention_checkbox_id}')
                    .attr('readonly', false)
                    .change(); 
                   
                $.notify('Se activó la casilla \'Retención\'.', {
                    className: 'info',
                    showDuration: 30,
                    hideDuration: 50,
                    autoHideDelay: 1000,
                }); 
            } 
            else 
            {
                $('#{$this->retention_checkbox_id}')
                    .prop('readonly', true)
                    .prop('checked', false)
                    .change();
            }

            " : "") . "
            
            var as_bill_readonly = is_commercial_route || jQuery.inArray(changeData.sunat_code, ['" . Document::TICKET . "']) == -1;
            
            $('#{$this->as_bill_checkbox_id}').prop('readonly', as_bill_readonly);

            if(as_bill_readonly){

                if(!jQuery.inArray(changeData.sunat_code, modifiers))
                {
                    $('#{$this->as_bill_checkbox_id}').prop('checked', is_commercial_route || jQuery.inArray(changeData.sunat_code, ['" . Document::BOLETA . "', '" . Document::BOLETO . "', '" . Document::HONORARIOS . "']) == -1).change();            
                }

            } else {

                $.notify('Se activó la casilla \'Crédito fiscal\'.', {
                    className: 'info',
                    showDuration: 30,
                    hideDuration: 50,
                    autoHideDelay: 1000,
                }); 
            }
        }); 

        $('#{$this->id}').data('changeStore', function(changeData){
            //
            var divObj = $('#{$this->id}');
            //Seteamos
            divObj.data('store_id', changeData.store_id);
            //Limpiamos autocomplete
            USAutocompleteReset('{$this->autocomplete_id}');
//            //Actualizamos precios (AL CAMBIAR TIENDA TAMBIEN CAMBIA FECHA DE EMISION ENTONCES NO ES NECESARIO)
//            SIANCommercialGridChangeStoreOrDate('{$this->id}');
        });
        
        $('#{$this->id}').data('changeEmission', function(changeData){
            var divObj = $('#{$this->id}');
            " . ( in_array($this->model->movement->route, ['logistic/purchaseOrder']) ? "divObj.data('set_summary_total', 0);" : "" ) . " 
            //Seteamos
            divObj.data('emission_date', changeData.emission_date);
            //Cambiamos a todos los links
            divObj.find('a.sian-commercial-grid-item-mixed-stock').data('emission_date', changeData.emission_date); 
            $('#{$this->item_mixed_stock_label_id}').data('emission_date', changeData.emission_date);
            //Actualizamos precios
            SIANCommercialGridChangeStoreOrDate('{$this->id}');
            //Si usa stock se cargará al cambiar la fecha de emisión
            " . ($this->model->movement->validate_stock == 1 ? "SIANCommercialGridLoadCommercialStocks('{$this->id}');" : "") . "
            USAutocompleteReset('{$this->autocomplete_id}');
            " . ( in_array($this->model->movement->route, ['logistic/purchaseOrder']) ? "divObj.data('set_summary_total', 1);" : "" ) . " 
        });
        
        $('#{$this->id}').data('changeExchange', function(exchange_rate){
            var divObj = $('#{$this->id}');
            " . ( in_array($this->model->movement->route, ['logistic/purchaseOrder']) ? "divObj.data('set_summary_total', 0);" : "" ) . "
            SIANCommercialGridChangeExchange('{$this->id}', exchange_rate);

            //Limpiamos
            USAutocompleteReset('{$this->autocomplete_id}');
            " . ( in_array($this->model->movement->route, ['logistic/purchaseOrder']) ? "divObj.data('set_summary_total', 1);" : "" ) . "
        });
        
        $('#{$this->id}').data('changeWarehouse', function(changeData){
            var divObj = $('#{$this->id}');
            " . ( in_array($this->model->movement->route, ['logistic/purchaseOrder']) ? "divObj.data('set_summary_total', 0);" : "" ) . "
            //Seteamos
            $('#{$this->warehouse_input_id}').val(changeData.warehouse_id);
            " . ( in_array($this->model->movement->route, ['commercial/saleOrder', 'commercial/saleQuote', 'support/serviceOrder', 'commercial/quoteTemplate']) ? "$('#{$this->cost_link_id}').data('warehouse_id', changeData.warehouse_id);" : "" ) . "
            //Obtenemos
            var emission_date = divObj.data('emission_date');
            //
            if(changeData.warehouse_id !== 'undefined' && changeData.warehouse_id.length > 0 && emission_date)
            {
                SIANCommercialGridUpdateAmounts('{$this->id}');
            }
            " . ( in_array($this->model->movement->route, ['logistic/purchaseOrder']) ? "divObj.data('set_summary_total', 1);" : "" ) . "
        });
       
        $('#{$this->id}').data('changeCCDependence', function(operationChangeData){
            {$this->onChangeCCDependence};
        });
        
        $('body').on('change', '#{$this->item_equivalence_id}', function(e) {
            //
            if(!SIANCommercialGridSetPrices('{$this->id}', '', $('#{$this->item_pres_quantity_id}'), $('#{$this->item_equivalence_id}'), $('#{$this->item_allow_decimals_id}'), $('#{$this->item_igv_affection_id}'), $('#{$this->item_price_id}'), $('#{$this->item_discount_id}'), $('#{$this->item_min_price_pen_id}'), $('#{$this->item_min_price_usd_id}'), $('#{$this->item_avg_price_pen_id}'), $('#{$this->item_avg_price_usd_id}'), $('#{$this->item_web_price_pen_id}'), $('#{$this->item_web_price_usd_id}'), $('#{$this->item_promotion_item_id}'), $('#{$this->item_warehouse_id}'), 0, true)){
                e.preventDefault();
                return false;
            }
            var product_id = USAutocompleteField('{$this->autocomplete_id}', 'value');
            //Si se cambia
            " . ($this->model->movement->validate_stock == 1 ? "
            SIANCommercialGridLoadCommercialStock('{$this->id}', product_id, $('#{$this->item_equivalence_id}'), $('#{$this->item_unit_stock_id}'), null, $('#{$this->item_mixed_stock_label_id}'));
            " : "") . "
        });

        $('body').on('change', '#{$this->item_igv_affection_id}', function(e) {
            SIANCommercialGridSetPrices('{$this->id}', '', $('#{$this->item_pres_quantity_id}'), $('#{$this->item_equivalence_id}'), $('#{$this->item_allow_decimals_id}'), $('#{$this->item_promotion_item_id}'), $('#{$this->item_igv_affection_id}'), $('#{$this->item_price_id}'), $('#{$this->item_discount_id}'), $('#{$this->item_min_price_pen_id}'), $('#{$this->item_min_price_usd_id}'), $('#{$this->item_avg_price_pen_id}'), $('#{$this->item_avg_price_usd_id}'), $('#{$this->item_web_price_pen_id}'), $('#{$this->item_web_price_usd_id}'), 1, 0);
        });

        $('body').on('change', '#{$this->id} select.sian-commercial-grid-item-equivalence', function(e) {
            var rowObj = $(this).closest('tr');
            //
            if(SIANCommercialGridGetAndChangePresentation('{$this->id}', rowObj.attr('id'), true)) {
                //Actualizar montos
                SIANCommercialGridUpdateAmounts('{$this->id}'); 
            } else {
                e.preventDefault();
                return false;
            }
            " . ($this->model->movement->validate_stock == 1 ? "
            SIANCommercialGridGetAndLoadCommercialStock('{$this->id}', rowObj.attr('id'));
            " : "") . "
            
        });
        
        $('body').on('change', '#{$this->id} select.sian-commercial-grid-item-igv-affection', function(e) {
            var rowObj = $(this).closest('tr');
            var instance_id = rowObj.attr('id');
            //Propagamos IGV (solo tiene efecto si es grupo o combo)
            SIANCommercialGridPropagateIGVAffection('{$this->id}', instance_id);
            //
            " . ($this->is_commercial_route ? "
            SIANCommercialGridGetAndSetPrices('{$this->id}', instance_id, 1);
            " : "") . "
            SIANCommercialGridUpdateAmounts('{$this->id}');    
            
        });
       
        $('body').on('change', '#{$this->retention_checkbox_id}', function(e) {
            
            var divObj = $('#{$this->id}');
            var retention4 = divObj.data('retention4');
            
            var elementObj = $(this);            
            var sunat_code = elementObj.data('sunat_code');
            var retention = elementObj.prop('checked') ? 1 : 0;

            if(retention != elementObj.data('retention'))
            {                    
                $('#{$this->retention_percent_field_id}').attr('readonly', retention == 0 || sunat_code === '" . Document::HONORARIOS . "');
                //
                if(retention == 0)
                {
                    $('#{$this->retention_percent_field_id}').val(0);
                    $('#{$this->retention_percent_field_id}').closest('div.row').addClass('hide');
                }
                else
                {
                    $('#{$this->retention_percent_field_id}').closest('div.row').removeClass('hide');
                    //Si es recibo por honorarios se pone 8%
                    if(sunat_code == '" . Document::HONORARIOS . "') {
                        $('#{$this->retention_percent_field_id}').val(retention4 * 100);
                    }
                }
                //Calculamos retención
                SIANCommercialGridCalculateRetention('{$this->id}');
                
                $(this).data('last', retention);
            }
        });
        
        $('body').on('change', '#{$this->summary_affected1_input_id}, #{$this->summary_inaffected_input_id}, #{$this->summary_nobill_input_id}, #{$this->summary_igv_input_id}, #{$this->summary_perception_input_id}', function(e) {
            SIANCommercialGridCalculateTotals();            
        });
        
        function SIANCommercialGridCalculateTotals(){
            var divObj = $('#{$this->id}');
            var include_igv = divObj.data('include_igv');
                
            var summary_affected1 = $('#{$this->summary_affected1_input_id}').double2();
            var summary_inaffected = $('#{$this->summary_inaffected_input_id}').double2();
            var summary_nobill = $('#{$this->summary_nobill_input_id}').double2();
            var summary_free = $('#{$this->summary_free_input_id}').double2();
            var summary_igv = $('#{$this->summary_igv_input_id}').double2();
            var summary_perception = $('#{$this->summary_perception_input_id}').double2();
            var summary_advance_amount = parseFloat($('#{$this->summary_advance_amount_input_id}').val());
            
            var summary_nnet  = summary_affected1 + summary_inaffected + summary_nobill + summary_free;
            var summary_fnet  = summary_free;
            var summary_net   = summary_nnet - summary_fnet;
            var summary_affected = summary_affected1;
            
            var summary_nigv = summary_igv;
            var summary_figv = 0;
            
            var summary_ntotal = summary_nnet + summary_nigv;
            var summary_ftotal = summary_fnet + summary_figv;
            var summary_total  = summary_net  + summary_igv;
            
            var summary_nreal  = summary_total + summary_perception;
            var summary_freal  = summary_ftotal;
            var summary_real   = summary_nreal;
            
            var summary_crude = 0;
            
            if(include_igv == 1){
                summary_crude = summary_total;
            }else{
                summary_crude = summary_net;
            }
            
            var new_sumary_advance_percentage = (summary_advance_amount / summary_real) * 100
            new_sumary_advance_percentage = isNaN(new_sumary_advance_percentage) ? 0 : new_sumary_advance_percentage;
    
            $('#{$this->summary_crude_input_id}').floatVal({$this->tfixed}, summary_crude);
            $('#{$this->summary_affected_input_id}').floatVal({$this->tfixed}, summary_affected);            
            $('#{$this->summary_nnet_input_id}').floatVal({$this->tfixed}, summary_nnet);
            $('#{$this->summary_fnet_input_id}').floatVal({$this->tfixed}, summary_fnet);
            $('#{$this->summary_net_input_id}').floatVal({$this->tfixed}, summary_net);
            $('#{$this->summary_nigv_input_id}').floatVal({$this->tfixed}, summary_nigv);
            $('#{$this->summary_figv_input_id}').floatVal({$this->tfixed}, summary_figv);
            $('#{$this->summary_ntotal_input_id}').floatVal({$this->tfixed}, summary_ntotal);
            $('#{$this->summary_ftotal_input_id}').floatVal({$this->tfixed}, summary_ftotal);
            $('#{$this->summary_total_input_id}').floatVal({$this->tfixed}, summary_total);
            $('#{$this->summary_nreal_input_id}').floatVal({$this->tfixed}, summary_nreal);
            $('#{$this->summary_freal_input_id}').floatVal({$this->tfixed}, summary_freal);
            $('#{$this->summary_real_input_id}').floatVal({$this->tfixed}, summary_real);
            $('#{$this->summary_advance_percentage_input_id}').floatVal({$this->tfixed},new_sumary_advance_percentage);
        }
        
        // EVENTO ENTER CAMPO CANTIDAD
        $('body').on('keydown', '#{$this->item_pres_quantity_id}', function(e) {
            if(e.which === 13) {
                $('#{$this->add_button_id}').click();
            }
        })
        
        // EVENTO ENTER CAMPO PRECIO
        $('body').on('keydown', '#{$this->item_price_id}', function(e) {
            if(e.which === 13) {
                $('#{$this->add_button_id}').click();
            }
        })
        
        // EVENTO ENTER CAMPO DESCUENTO
        $('body').on('keydown', '#{$this->item_discount_id}', function(e) {
            if(e.which === 13) {
                $('#{$this->add_button_id}').click();
            }
        })

        $('body').on('click', '#{$this->add_button_id}', function(e) {
            console.log(`*713* => click en {$this->add_button_id}`);
            //
            var divObj = $('#{$this->id}');
            var currency = divObj.data('currency');
            var active_table_id = divObj.data('active_table_id');
            var parent_type = divObj.data('parent_type');
            var optionObj = $('#{$this->item_equivalence_id}').find(':selected');
            var warehouseObj = $('#{$this->item_warehouse_id}').find(':selected');
            //
            var tableObj = $('#' + active_table_id);
            //
            var aux_id = $('#{$this->item_aux_id}').val();
            var product_id = $('#{$this->item_value_id}').val();
            var product_name = $('#{$this->item_text_id}').val();
            var item_type_id = $('#{$this->item_item_type_id}').val();
            var product_type = $('#{$this->item_product_type_id}').val();
            var pres_quantity = $('#{$this->item_pres_quantity_id}').floatVal(2);
            var presentationItems = $('#{$this->item_equivalence_id}').data('presentationItems');
            var equivalence = optionObj.floatVal(2);
            var warehouse_id = warehouseObj.val();
            var allow_decimals = $('#{$this->item_allow_decimals_id}').integer();
            " . ($this->model->movement->validate_stock == 1 ? "
                if (product_type === PRODUCT_TYPE_MERCHANDISE)
                {
                    var unit_stock = $('#{$this->item_unit_stock_id}').val();
                    var mixed_stock = $('#{$this->item_mixed_stock_label_id}').text();                        
                }
                else
                {
                    var unit_stock = '0'
                    var mixed_stock = '-';   
                }
            " : "
                var unit_stock = 0;
                var mixed_stock = '';                
            ") . "
            var skip_cost_validation = $('#{$this->item_skip_cost_validation_id}').integer();
            var min_price_pen = $('#{$this->item_min_price_pen_id}').floatVal({$this->ifixed});
            var min_price_usd = $('#{$this->item_min_price_usd_id}').floatVal({$this->ifixed});
            var avg_price_pen = $('#{$this->item_avg_price_pen_id}').floatVal({$this->ifixed});
            var avg_price_usd = $('#{$this->item_avg_price_usd_id}').floatVal({$this->ifixed});
            var web_price_pen = $('#{$this->item_web_price_pen_id}').floatVal({$this->ifixed});
            var web_price_usd = $('#{$this->item_web_price_usd_id}').floatVal({$this->ifixed});
            var price = $('#{$this->item_price_id}').floatVal({$this->ifixed});
            var discount = $('#{$this->item_discount_id}').floatVal({$this->ifixed});
            var promotion_item_id = $('#{$this->item_promotion_item_id}').val();
            var igv_affection = $('#{$this->item_igv_affection_id}').val();
            var perception_affected = $('#{$this->item_perception_id}').val();
            var expiration = $('#{$this->item_expiration_id}').val();
            var carry = optionObj.data('carry');
            var has_gifts = optionObj.data('has_gifts');
            //
            if(parent_type === '" . Product::TYPE_COMBO . "')
            {
                bootbox.alert(us_message('No se puede agregar un ítem a un combo, conviértalo en grupo primero', 'error'));
                return;
            }
            //
            if (product_id.length === 0)
            {
                $('#{$this->item_aux_id}').focus();
                return;
            }
            
            if(isNaN(equivalence)){
                $('#{$this->item_equivalence_id}').notify('Debe elegir una presentación!', 
                    {   className: 'warn',
                        showDuration: 50,
                        hideDuration: 50,
                        autoHideDelay: 5000
                    }
                );
                $('#{$this->item_equivalence_id}').focus().select();
                return;
            }
            
            if(isNaN(price)){
                $('#{$this->item_price_id}').notify('Debe ingresar un precio!', 
                    {   className: 'warn',
                        showDuration: 50,
                        hideDuration: 50,
                        autoHideDelay: 5000
                    }
                );
                $('#{$this->item_price_id}').focus().select();
                return;
            }

            //PRECIO INFERIOR AL MINIMO, SOLO CUANDO NO HAY PROMOCION
            var min_error_message = false;
            if(isBlank(promotion_item_id))
            {
                switch(currency)
                {
                    case '" . Currency::PEN . "':
                        if (price < min_price_pen)
                        {
                            min_error_message = 'El precio no puede ser menor que ' + min_price_pen;
                        }
                    break;
                    case '" . Currency::USD . "':
                        if (price < min_price_usd)
                        {
                            min_error_message = 'El precio no puede ser menor que ' + min_price_usd;

                        }
                    break;
                    default:
                        bootbox.alert(us_message('Moneda Inválida', 'error'));
                    break;
                }
            }
            
            if (min_error_message)
            {
                bootbox.alert(us_message(min_error_message, 'warning'));
                $('#{$this->item_price_id}').focus();
                return;
            }

            if (discount > price)
            {
                bootbox.alert(us_message('El descuento no puede ser mayor que el precio', 'warning'));
                $('#{$this->item_discount_id}').focus();
                return;
            }
            
            console.log(`*836* => product_type = `+product_type);
            if (product_type === PRODUCT_TYPE_COMBO) //aqui
            {
                var subtable_id = SIANCommercialGridAddCombo('{$this->id}', product_id, item_type_id, product_name, pres_quantity, presentationItems, equivalence, igv_affection, price, discount, 0, []);
                SIANCommercialGridFillCombo('{$this->id}', product_id, igv_affection, subtable_id, function() {
                    SIANCommercialGridGetIds('{$this->id}');
                    SIANCommercialGridUpdateAmounts('{$this->id}');
                    unfocusable();
                });

                USAutocompleteReset('{$this->autocomplete_id}');

                //FOCUS
                $('#{$this->item_aux_id}').focus();
            }
            else
            {
                //Si no hay promoción agregamos
                if(!isBlank(promotion_item_id) && USMath.mod(pres_quantity, carry, 0) == 0 && has_gifts == 1) {
                    console.log(`No hay promoción, agregamos`);
                    SIANCommercialGridShowGiftPopup('{$this->id}', promotion_item_id, igv_affection, function(option_ids) {
                        var instance_id = SIANCommercialGridAddItem('{$this->id}', active_table_id, '', '', product_id, item_type_id, product_type, product_name, unit_stock, mixed_stock, pres_quantity, presentationItems, equivalence, warehouse_id, allow_decimals, skip_cost_validation, min_price_pen, min_price_usd, avg_price_pen, avg_price_usd, web_price_pen, web_price_usd, price, discount, promotion_item_id, '', igv_affection, '', perception_affected, expiration, 0, parent_type, '', '', '', []);
                        //
                        SIANCommercialGridGetGiftOptionsData('{$this->id}', instance_id, option_ids, function(b_result) {
                            SIANCommercialGridGetIds('{$this->id}');
                            SIANCommercialGridUpdateAmounts('{$this->id}');
                            unfocusable();
                            USAutocompleteReset('{$this->autocomplete_id}');
                            //FOCUS
                            $('#{$this->item_aux_id}').focus();                            
                        });
                    });
         
                } else {
                    console.log(`*869* => ingresamos a SIANCommercialGridAddItem`);
                    var price_type = $('#' + active_table_id).data('price_type');
                    price_type = (price_type != undefined? price_type: '');                    
                    SIANCommercialGridAddItem('{$this->id}', active_table_id, '', '', product_id, item_type_id, product_type, product_name, unit_stock, mixed_stock, pres_quantity, presentationItems, equivalence, warehouse_id, allow_decimals, skip_cost_validation, min_price_pen, min_price_usd, avg_price_pen, avg_price_usd, web_price_pen, web_price_usd, price, discount, promotion_item_id, '', igv_affection, price_type, perception_affected, expiration, 0, parent_type, '', '', '', []);
                    SIANCommercialGridGetIds('{$this->id}');
                    SIANCommercialGridUpdateAmounts('{$this->id}');
                    unfocusable();

                    var defaultTitle = '{$this->main_title}';
                    var currentTitle = divObj.find('h3.panel-title').text();
                    
                    USAutocompleteReset('{$this->autocomplete_id}');
                    
                    if(defaultTitle !== currentTitle){
                        var titleParts = currentTitle.split(':');    
                        if (titleParts.length > 1) {
                            var keyword = titleParts[1];
                            USAutocompleteSetValue('{$this->autocomplete_id}', keyword, false);     
                        } 
                    }
                    //FOCUS
                    $('#{$this->item_aux_id}').focus();                    
                }               
            }
        });


        $('body').on('click', '#{$this->batch_add_button_id}', function(e) {
            var batch_code = $('#{$this->batch_item_aux_id}').val();
            
            var divObj = $('#{$this->id}');
            var active_table_id = divObj.data('active_table_id');
            var igv_affection = $('#{$this->item_igv_affection_id}').val();
            var perception_affected = $('#{$this->item_perception_id}').val();



            $('#{$this->batch_loader_id}').css({
                'width': '4rem',
                'display': 'block'
            });

            $.ajax({
                type: 'post',
                url: '{$this->controller->createUrl('/widget/getDpModel')}',
                data: {
                    viewClass: 'DpAllProductPresentations',
                    scenario: 'stock',
                    attribute: 'batch_code',
                    attribute_in: [batch_code],
                    presentationMode: {$this->presentationMode},
                },
                beforeSend: function(xhr) {
                    window.active_ajax++;
                    $('div.ui-tooltip').remove();
                },
                success: function(odata) {
                    window.active_ajax--;

                    $('#{$this->batch_loader_id}').css({
                        'width': '4rem',
                        'display': 'none'
                    });

                    if (odata.length > 0) {
                        odata.forEach((product) => {
                            SIANCommercialGridAddItem(
                                '{$this->id}', 
                                active_table_id, '', '', 
                                product.product_id, 
                                product.item_type_id, 
                                product.product_type, 
                                product.product_name, 
                                product.unit_stock, 
                                product.mixed_stock, 
                                1, 
                                product.presentationItems, 
                                product.equivalence, 
                                undefined, 
                                product.allow_decimals, 
                                product.skip_cost_validation, 
                                0, 
                                0, 
                                0, 
                                0, 
                                0, 
                                0,
                                0, 
                                0,
                                '', '', 
                                '10', 
                                '',
                                product.perception_affected, 
                                product.expiration, 
                                0, 
                                null, '', '', '', 
                                []);

                                //    
                                SIANCommercialGridGetIds('{$this->id}');
                                SIANCommercialGridUpdateAmounts('{$this->id}');
                                unfocusable();
                                
                        });
                        USAutocompleteReset('{$this->batch_autocomplete_id}');
                        //FOCUS
                        $('#{$this->batch_item_aux_id}').focus(); 

                    }else {
                        alert('No se encontró ningún producto.');
                    }
                },
                error: function(request, status, error) {
                    bootbox.alert(us_message(request.responseText, 'error'));
                    window.active_ajax--;
                },
                dataType: 'json'
            });
            
        });

        function {$this->id}GetPresentations(product_id, equivalence, store_id, emission_date)
        {
            if(product_id != undefined)
            {
                $.ajax({
                    type: 'post',
                    url: '{$this->presentationUrl}',
                    data: normalizeAjaxParams({
                        mode: {$this->presentationMode},
                        product_ids: [product_id],
                        currency: '" . $this->controller->getOrganization()->globalVar->display_currency . "',
                        store_id: store_id,
                        date: emission_date,
                        exclude_id: " . CJSON::encode($this->is_commercial_route == 1 && $this->is_template == 0 ? $this->model->movement->kardex_unlock_exclude_id : null) . "
                    }),
                    beforeSend: function (xhr) {
                        window.active_ajax++;
                        //Ocultamos los tooltip
                        $('div.ui-tooltip').remove();
                    },                     
                    success: function(data) {
                    
                        $.each(data, function(index, item) {
                            //Llenamos combo
                            var selectedObj = SIANCommercialGridFillPresentations('{$this->item_equivalence_id}', item, equivalence, '{$this->item_warehouse_id}', '');
                            if(isset(selectedObj) && isset(selectedObj.promotion_item_id)){
                                $('#{$this->item_equivalence_id}').notify(SIANCommercialGridGetPromotionLabel(selectedObj.carry, selectedObj.pay, selectedObj.has_gifts, selectedObj.measure_name), {
                                    className: 'info',
                                    showDuration: 50,
                                    hideDuration: 50,
                                    autoHideDelay: 4000
                                });
                            }
                        });       
                        
                        SIANCommercialGridSetPrices('{$this->id}', '', $('#{$this->item_pres_quantity_id}'), $('#{$this->item_equivalence_id}'), $('#{$this->item_allow_decimals_id}'), $('#{$this->item_promotion_item_id}'), $('#{$this->item_igv_affection_id}'), $('#{$this->item_price_id}'), $('#{$this->item_discount_id}'), $('#{$this->item_min_price_pen_id}'), $('#{$this->item_min_price_usd_id}'), $('#{$this->item_avg_price_pen_id}'), $('#{$this->item_avg_price_usd_id}'), $('#{$this->item_web_price_pen_id}'), $('#{$this->item_web_price_usd_id}'), 1, 0);
                        window.active_ajax--;
                    },
                    error: function(request, status, error) { // if error occured
                        window.active_ajax--;
                        bootbox.alert(us_message(request.responseText, 'error'));
                    },
                    dataType: 'json'
                });
            }
        }        
        ", CClientScript::POS_END);
    }

    /**
     * Runs the widget.
     */
    public function run() {

        $this->beginWidget('booster.widgets.TbPanel', array(
            'title' => $this->main_title,
            'headerIcon' => 'list',
            'context' => $this->context,
            'header_id' => $this->header_id,
            'htmlOptions' => array(
                'id' => $this->id,
                'class' => ($this->model->hasErrors('tempItems') ? 'us-error' : '')
            )
        ));

        if ($this->model->movement->from_exchange == 0) {
            //Main div
            if (!$this->readonly) {
                $this->_renderBar();
            }
        }
        // Tabla "Mercaderías, servicios y combos (Listado principal)"
        echo "<div id='SaleOrderListProductForMobile' class='list-products'><ul class='accordion-products'><ul/></div>";
        echo "<div id='SaleOrderListProductsFooterForMobile'><div id='TotalsListProductsFooterForMobile'></div></div>";
        echo "<div id='SaleOrderListProduct' class='sian-commercial-grid-tables'></div>";
        //End of main div
        echo "<hr>";

        echo "<div class='row'>"; //ROW1

        echo "<div class='col-lg-7 col-md-7 col-sm-7 col-xs-12'>";
        $this->_renderCheckboxes();
        echo "</div>";


        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-12'>";
        if ($this->model->isBuy()) {
            $this->_renderRetention();
        }
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-12'>";
        $this->_renderSummary();
        echo "</div>";

        echo "</div>"; //ROW1

        $this->endWidget();
    }

    private function _renderBar() {

        echo "<div class='sian-commercial-grid-shared-toolbar row'>";
        echo "<div class='col-lg-4 col-md-4 col-sm-12 col-xs-12'>";
        $this->_renderCol0();
        echo "</div>";
        echo $this->dispatch_multiple_origin == 1 ? "<div class='col-lg-6 col-md-6 col-sm-12 col-xs-12'>" : "<div class='col-lg-5 col-md-5 col-sm-12 col-xs-12'>";
        $this->_renderCol1();
        echo "</div>";
        echo $this->dispatch_multiple_origin == 1 ? "<div class='col-lg-2 col-md-2 col-sm-12 col-xs-12'>" : "<div class='col-lg-3 col-md-3 col-sm-12 col-xs-12'>";
        $this->_renderCol2();
        echo "</div>";
        echo "</div>";
        //
        echo "<div class='sian-commercial-grid-combo-toolbar' style='display:none;'></div>";
        echo "<hr>";
    }


    private function _renderColPacking() {

        $a_attributes = [];
        $a_attributes[] = array('name' => 'packing_code', 'width' => 20, 'types' => array('id', 'value', 'aux'));
        $a_attributes[] = array('name' => 'label_packing_code', 'width' => 20,'types' => array('text'));

        echo "<div class='col-lg-9 col-md-7 col-sm-12 col-xs-12'>";   
        echo $this->widget('application.widgets.USAutocomplete', array(
            'model' => $this->model->movement,
            'id' => $this->batch_autocomplete_id,
            'label' => 'Packing',
            'attribute' => 'packing_code',
            'aux_id' => $this->batch_item_aux_id,
            'value_id' => $this->batch_item_value_id,
            'text_id' => $this->batch_item_text_id,
            'view' => array('model' => 'DpGetPacking','attributes' => $a_attributes)), true); 
        echo "</div>";
    }

    private function _renderCol0() {

        $a_attributes = [];
        $a_attributes[] = array('name' => 'product_id', 'width' => 7, 'types' => array('id', 'value', 'aux'), 'not_in' => "window.sianCommercialGridDynamicIds");
        $a_attributes[] = array('name' => 'item_type_id', 'hidden' => true);
        $a_attributes[] = array('name' => 'product_type', 'width' => 5, 'in' => "window.sianCommercialGridProductTypes");
        $a_attributes[] = array('name' => 'barcode', 'width' => 20);
        $a_attributes[] = array('name' => 'product_name', 'width' => 33, 'types' => array('text'));
        $a_attributes[] = array('name' => 'model', 'hidden' => true);
        $a_attributes[] = array('name' => 'part_number', 'hidden' => true);
        $a_attributes[] = array('name' => 'perception_affected', 'hidden' => true, 'search' => false, 'in' => 'window.sianCommercialGridPerceptionAffected');
        $a_attributes[] = array('name' => 'expiration', 'hidden' => true, 'search' => false);
        $a_attributes[] = array('name' => 'currency', 'hidden' => true, 'sortable' => false, 'search' => false);
        $a_attributes[] = array('name' => 'currency_name', 'width' => 5, 'sortable' => false, 'search' => false);
        $a_attributes[] = array('name' => 'equivalence', 'hidden' => true, 'search' => false);
        $a_attributes[] = array('name' => 'allow_decimals', 'hidden' => true, 'search' => false);
        $a_attributes[] = array('name' => 'skip_cost_validation', 'hidden' => true, 'search' => false);

        $a_attributes[] = array('name' => 'unit_stock', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->item_unit_stock_id}').val(unit_stock);");
        $a_attributes[] = array('name' => 'stock', 'width' => 7, 'search' => false);
        $a_attributes[] = array('name' => 'mixed_stock', 'hidden' => true, 'search' => false, 'update' => "$('#{$this->item_mixed_stock_label_id}').text(product_type === '" . Product::TYPE_COMBO . "' ? '-': mixed_stock); USLinkStatus('{$this->item_mixed_stock_label_id}', product_type !== '" . Product::TYPE_COMBO . "', {id: product_id, equivalence: equivalence})");

        $a_attributes[] = array('name' => 'measure_name', 'width' => 10, 'search' => false);
        $a_attributes[] = array('name' => 'iaprice', 'width' => 13);
        $a_attributes[] = array('name' => 'imprice', 'hidden' => true, 'search' => false);
        $a_attributes[] = array('name' => 'iwprice', 'hidden' => true, 'search' => false);

        echo $this->widget('application.widgets.USAutocomplete', array(
            'id' => $this->autocomplete_id,
            'label' => 'Producto',
            'name' => null,
            'useOnkeydown' => Yii::app()->controller->getOrganization()->globalVar->keyboard_search == 1 ? true : false,
            'autoChoose' => Yii::app()->controller->getOrganization()->globalVar->keyboard_search == 1 ? false : true,
            'aux_id' => $this->item_aux_id,
            'value_id' => $this->item_value_id,
            'text_id' => $this->item_text_id,
            'hint' => "El stock que se muestra es el stock sumado <b>{$this->model->movement->getStockModeLabel()}</b> de los almacenes comerciales.",
            'view' => array(
                'model' => 'DpAllProductPresentations',
                'scenario' => DpAllProductPresentations::SCENARIO_WITH_STOCK,
                'attributes' => $a_attributes,
                //Stock a la fecha
                'params' => "{
                        only_allowed_divisions: " . ($this->only_allowed_divisions == 1 ? 1 : 0) . ",
                        only_with_stock: " . ($this->model->movement->validate_stock == 1 ? 1 : 0) . ",
                        store_id: function () { 
                            return SIANCommercialGridGetStoreIdForPromotion('{$this->id}');
                        },
                        date: function () { 
                            return SIANCommercialGridGetEmissionDateForPromotionOrStock('{$this->id}');
                        },
                        exclude_id: " . CJSON::encode($this->model->movement->kardex_unlock_exclude_id) . ",
                        stock_mode: " . CJSON::encode($this->model->movement->validate_stock == 1 ? $this->model->movement->stock_mode : null) . "
                }",
            ),
            'maintenance' => array(
                'module' => 'logistic',
                'controller' => 'product',
                'buttons' => array(
                    'preview' => array(
                        'access' => $this->preview_access,
                    ),
                    'create' => [],
                    'update' => [],
                ),
            ),
            "onselect" => "
                
                    var divObj = $('#{$this->id}');
                    var active_table_id = divObj.data('active_table_id');
                    var validate_stock = divObj.data('validate_stock');
                    var is_commercial_route = divObj.data('is_commercial_route');
                    var is_template = divObj.data('is_template');
                    var scope = divObj.data('scope');
                    var igv_affection_map = divObj.data('igv_affection_map');
                    var igv_affection_order = divObj.data('igv_affection_order');
                    var parent_igv_affection = divObj.data('parent_igv_affection');
                    var use_inaffected = divObj.data('use_inaffected');
                    var use_free = divObj.data('use_free');
    
                    var tableObj = $('#' + active_table_id);
                    var step = allow_decimals == 0 ? 1 : 0.01;
                    
                    
                    var as_bill = $('#{$this->as_bill_checkbox_id}').prop('checked') ? 1 : 0;
                    var affection_items = getIGVAffectionItems(is_commercial_route, as_bill, scope, igv_affection_map, use_inaffected, use_free, false);

                    $('#{$this->item_item_type_id}').val(item_type_id);
                    $('#{$this->item_product_type_id}').val(product_type);
                    $('#{$this->item_igv_affection_id}').html(getIGVAffectionOptions(affection_items, parent_igv_affection, igv_affection_order));
                    $('#{$this->item_perception_id}').val(perception_affected);
                    $('#{$this->item_expiration_id}').val(expiration);
                    $('#{$this->item_allow_decimals_id}').val(allow_decimals);
                    $('#{$this->item_skip_cost_validation_id}').val(skip_cost_validation);
                    $('#{$this->item_pres_quantity_id}').val(1).allowDecimals(allow_decimals * 2);
                    $('#{$this->item_pres_quantity_id}').attr('step', step).attr('min', step).data('last', 1);

                    if(product_type === '" . Product::TYPE_MERCHANDISE . "' && validate_stock)
                    {
                        SIANCommercialGridAlertStock(unit_stock, '{$this->autocomplete_id}', '{$this->id}');                
                    }
                    SIANCommercialGridAlertPrice(iaprice, imprice, '{$this->item_price_id}', '{$this->id}'); 
                                        
                    var store_id = SIANCommercialGridGetStoreIdForPromotion('{$this->id}');
                    var emission_date = SIANCommercialGridGetEmissionDateForPromotionOrStock('{$this->id}');
                    {$this->id}GetPresentations(product_id, equivalence, store_id, emission_date);
                    ",
            'onreset' => "
                    $('#{$this->item_pres_quantity_id}').allowDecimals(0);    
                    $('#{$this->item_pres_quantity_id}').val(1);    
                    $('#{$this->item_pres_quantity_id}').attr('step', 1).attr('min', 1);    
                    $('#{$this->item_pres_quantity_id}').removeAttr('disabled');
                    $('#{$this->item_equivalence_id}').html('<option value>" . Strings::SELECT_OPTION . "</option>')
                    $('#{$this->item_warehouse_id}').html('<option value>" . Strings::SELECT_OPTION . "</option>')
                    $('#{$this->item_allow_decimals_id}').val(0);
                    $('#{$this->item_unit_stock_id}').val(null);
                    $('#{$this->item_mixed_stock_label_id}').text(0);  
                    USLinkStatus('{$this->item_mixed_stock_label_id}', false, {id: null, equivalence: null});                        
                    $('#{$this->item_skip_cost_validation_id}').val(0);
                    $('#{$this->item_min_price_pen_id}').val(0);
                    $('#{$this->item_min_price_usd_id}').val(0);
                    $('#{$this->item_avg_price_pen_id}').val(0);
                    $('#{$this->item_avg_price_usd_id}').val(0);
                    $('#{$this->item_web_price_pen_id}').val(0);
                    $('#{$this->item_web_price_usd_id}').val(0);
                    $('#{$this->item_price_id}').val(0).attr('min', 0).prop('readonly', false);
                    $('#{$this->item_discount_id}').val(0).prop('readonly', false);
                    $('#{$this->item_promotion_item_id}').val('');
                    $('#{$this->item_igv_affection_id}').val('');
                    $('#{$this->item_perception_id}').val(0);                    
                    $('#{$this->item_aux_id}').focus().select();
                    $('#{$this->item_expiration_id}').val(0);
                ",
            'afterreset' => "
                    var defaultTitle = '{$this->main_title}';
                    var currentTitle = $('h3#{$this->header_id}.panel-title').text();
                    if(defaultTitle !== currentTitle){
                        var titleParts = currentTitle.split(':');
                        
                        if (titleParts.length > 1) {
                            var keyword = titleParts[1];
                            USAutocompleteSetValue('{$this->autocomplete_id}', keyword, false);     
                        } 
                    }
                "
                ), true);
    }

    private function _renderCol1() {
        $col = $this->model->movement->validate_stock == 1 ? ($this->dispatch_multiple_origin == 1 ? 2 : 3) : 3;
        echo "<div class='row'>";
        echo "<div class='col-lg-{$col} col-md-{$col} col-sm-{$col} col-xs-12'>";
        echo CHtml::hiddenField(null, '', array(
            'id' => $this->item_item_type_id,
        ));
        echo SIANForm::dropDownListNonActive('Tipo', null, null, Product::getTypeItems(), array(
            'id' => $this->item_product_type_id,
            'disabled' => true
        ));
        echo "</div>";
        //Si se usa stock se renderiza el campo
        if ($this->model->movement->validate_stock == 1) {
            echo "<div class='col-lg-1 col-md-1 col-sm-1 col-xs-6'>";
            echo CHtml::hiddenField(null, 0, array(
                'id' => $this->item_unit_stock_id,
                'disabled' => true,
            ));
            echo CHtml::label('Stock', $this->item_mixed_stock_label_id);
            echo "<p>" . $this->widget('application.widgets.USLink', array(
                'id' => $this->item_mixed_stock_label_id,
                'route' => '/logistic/merchandise/explainCommercialStock',
                'label' => '0',
                'title' => '¿Por qué veo este stock?',
                'class' => 'form',
                'data' => array(
                    'stock_mode' => $this->model->movement->stock_mode,
                    'emission_date' => $this->model->movement->emission_date,
                    'exclude_id' => $this->model->movement->kardex_unlock_exclude_id,
                    'store_id' => $this->model->movement->store_id,
                ),
                'visible' => false
                    ), true) . "</p>";
            echo "</div>";
        }
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo SIANForm::numberFieldNonActive('Cantidad', null, 1, array(
            'id' => $this->item_pres_quantity_id,
            'data-last' => 1,
            'class' => 'enterTab',
            'style' => 'text-align:right',
            'onchange' => "SIANCommercialGridChangePresQuantity('{$this->id}', '', $(this), $('#{$this->item_equivalence_id}'), $('#{$this->item_allow_decimals_id}'), $('#{$this->item_igv_affection_id}'), $('#{$this->item_price_id}'), $('#{$this->item_discount_id}'));"
        ));
        echo "</div>";

        echo $this->dispatch_multiple_origin == 1 ? "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>" : "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-6'>";
        echo SIANForm::dropDownListNonActive('Pres.', null, null, [], array(
            'id' => $this->item_equivalence_id,
            'empty' => Strings::SELECT_OPTION,
            'data-last' => 1,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_allow_decimals_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_skip_cost_validation_id,
        ));
        echo "</div>";
        if ($this->dispatch_multiple_origin == 1) {
            echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-6'>";
            echo SIANForm::dropDownListNonActive('Alm. Disp.', null, null, [], array(
                'id' => $this->item_warehouse_id,
                'empty' => Strings::SELECT_OPTION,
                'data-last' => 1,
            ));
            echo "</div>";
        }
        echo $this->dispatch_multiple_origin == 1 ? "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>" : "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-6'>";
        echo SIANForm::dropDownListNonActive('Afec. IGV', null, '', [], [
            'id' => $this->item_igv_affection_id,
            'empty' => Strings::SELECT_OPTION
        ]);
        echo "</div>";
        echo "</div>";
    }

    private function _renderCol2() {
        echo "<div class='row'>";
        echo "<div class='col-lg-5 col-md-5 col-sm-5 col-xs-6'>";
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_min_price_pen_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_min_price_usd_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_avg_price_pen_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_avg_price_usd_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_web_price_pen_id,
        ));
        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_web_price_usd_id,
        ));
        echo SIANForm::numberFieldNonActive("Precio <span class='currency-symbol'></span>", null, 0, array(
            'id' => $this->item_price_id,
            'class' => "us-double{$this->ifixed} enterTab",
            'min' => 0,
            'step' => $this->istep,
        ));
        echo "</div>";

        echo "<div class='col-lg-4 col-md-4 col-sm-4 col-xs-6'>";
        echo SIANForm::numberFieldNonActive("Desc. <span class='currency-symbol'></span>", null, 0, array(
            'id' => $this->item_discount_id,
            'class' => "us-double{$this->ifixed} enterTab",
            'min' => 0,
            'step' => $this->istep,
            'readonly' => $this->lock_discount,
        ));

        echo CHtml::hiddenField(null, '', array(
            'id' => $this->item_promotion_item_id,
        ));

        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_perception_id,
        ));

        echo CHtml::hiddenField(null, 0, array(
            'id' => $this->item_expiration_id,
        ));
        echo "</div>";

        echo "<div class='col-lg-3 col-md-3 col-sm-3 col-xs-6 text-center'>";
        echo CHtml::label('Agregar', $this->add_button_id, []);
        echo "<br/>";
        $this->widget('application.widgets.USButtons', array(
            'buttons' => array(
                array(
                    'id' => $this->add_button_id,
                    'context' => 'primary',
                    'icon' => 'fa fa-lg fa-plus white',
                    'size' => 'default',
                    'title' => 'Añadir'
                )
            )
        ));
        echo "</div>";
        echo "</div>";
    }

    private function _renderCheckboxes() {

        if ($this->model->movement->route == 'logistic/purchaseOrder') {
            echo "<div class='row'>";
            $this->widget('SIANLoadProducts', array(
                'id' => $this->controller->getServerId(),
                'presentationMode' => $this->presentationMode,
                'colums' => 3,
                'onLoadProduct' => "
                var divObj = $('#{$this->id}');
                var main_table_id = divObj.data('main_table_id');
                var mainTableObj = $('#' + main_table_id);
                var allow_duplicate = divObj.data('allow_duplicate');
                
                if(allow_duplicate == 0){
                    if (window.sianCommercialGridDynamicIds.findIndex(id => id == itemProduct.product_id) === -1) {
                        
                        var amount = parseFloat(itemFile['amount'] ?? 0 );
                        var pres_quantity = itemFile['pres_quantity'] == null || itemFile['pres_quantity']  == undefined || itemFile['pres_quantity'] == 0? 1 : itemFile['pres_quantity'];
                        pres_quantity = itemProduct['allow_decimals'] === 1 ? parseFloat(pres_quantity) : Math.round(pres_quantity).toFixed();

                        SIANCommercialGridAddItem('{$this->id}', main_table_id, '', '', itemProduct.product_id, itemProduct.item_type_id, itemProduct.product_type, itemProduct.product_name, itemProduct.unit_stock, itemProduct.mixed_stock, pres_quantity, itemProduct.presentationItems, itemProduct.equivalence, '', itemProduct.allow_decimals, itemProduct.skip_cost_validation, 0, 0, 0, 0, 0, 0, amount, 0, '', '', 10, '', 0, '', 0, '', '', '', '', []);                    
                        window.sianCommercialGridDynamicIds.push(itemProduct.product_id);
                        itemsCharged++;
                    } 
                }else{
                    var amount = parseFloat(itemFile['amount'] ?? 1 );
                    var pres_quantity = itemFile['pres_quantity'] == null || itemFile['pres_quantity']  == undefined || itemFile['pres_quantity'] == 0? 1 : itemFile['pres_quantity'];
                    pres_quantity = itemProduct['allow_decimals'] === 1 ? parseFloat(pres_quantity) : Math.round(pres_quantity).toFixed();

                    SIANCommercialGridAddItem('{$this->id}', main_table_id, '', '', itemProduct.product_id, itemProduct.item_type_id, itemProduct.product_type, itemProduct.product_name, itemProduct.unit_stock, itemProduct.mixed_stock, pres_quantity, itemProduct.presentationItems, itemProduct.equivalence, '', itemProduct.allow_decimals, itemProduct.skip_cost_validation, 0, 0, 0, 0, 0, 0, amount, 0, '', '', 10, '', 0, '', 0, '', '', '', '', []);                    
                    itemsCharged++;
                }                
            ",
                'onLoadedProducts' => " SIANCommercialGridUpdateAmounts('{$this->id}');",
            ));
            
            if($this->use_products_related){
                $this->_renderColPacking();            
            }
            echo "</div>";
        }
        echo "<div class='row'>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->include_igv_checkbox_id,
            'model' => $this->model,
            'attribute' => 'include_igv',
            'hint' => 'Si los precios incluyen IGV',
            'htmlOptions' => array(
                'readonly' => ($this->readonly == 1 || $this->is_commercial_route == 1),
                'onchange' => ($this->readonly == 1 || $this->is_commercial_route == 1) ? "" : "
                    var include_igv = $(this).prop('checked') ? 1 : 0;$('#{$this->id}').data('include_igv', include_igv);
                    " . (in_array($this->model->movement->route, ['commercial/saleOrder', 'commercial/saleQuote', 'support/serviceOrder', 'commercial/quoteTemplate']) ? "$('#{$this->cost_link_id}').data('include_igv', include_igv);" : "") . "                    
                    USAutocompleteReset('{$this->autocomplete_id}');
                    $('#{$this->force_igv_checkbox_id}').readonly(include_igv == 1);
                    SIANCommercialGridGetAndSetAllPrices('{$this->id}', 1);
                    SIANCommercialGridUpdateAmounts('{$this->id}');
                ",
            )
                ), true);
        echo "</div>";
        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->as_bill_checkbox_id,
            'model' => $this->model,
            'attribute' => 'as_bill',
            'hint' => 'Afecta el crédito fiscal (editable solo en compras)',
            'htmlOptions' => array(
                'onchange' => "                                    
                    if(window.documents_loaded)
                    {
                        consoleLog('Se entró al evento onchange de as_bill pues ya terminaron de cargar los documentos');

                        var as_bill = $(this).prop('checked') ? 1 : 0;
                        $('#{$this->id}').data('as_bill', as_bill);
                        //
                        SIANCommercialGridUpdateIGVAffection('{$this->id}');
                        SIANCommercialGridUpdateAmounts('{$this->id}');
                    }
                    else
                    {
                        consoleLog('Se intentó cambiar as_bill pero aún no terminaban de cargar los documentos');
                    }
                ",
            )
                ), true);
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->force_igv_checkbox_id,
            'model' => $this->model,
            'attribute' => 'force_igv',
            'hint' => 'Para indicar si se aumenta el IGV al monto inafecto/exonerado',
            'htmlOptions' => array(
                'readonly' => $this->model->include_igv == 1,
                'onchange' => "                                    
                    consoleLog('Se entró al evento onchange de force_igv pues ya terminaron de cargar los documentos');

                    var force_igv = $(this).prop('checked') ? 1 : 0;
                    $('#{$this->id}').data('force_igv', force_igv);
                        
                    USAutocompleteReset('{$this->autocomplete_id}');
                    SIANCommercialGridUpdateAmounts('{$this->id}');
                ",
            )
                ), true);
        echo "</div>";

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->widget('application.widgets.USCheckBox', array(
            'id' => $this->allow_duplicate_checkbox_id,
            'model' => $this->model,
            'attribute' => 'allow_duplicate',
            'hint' => 'Para que un producto esté más de una vez en la lista',
            'htmlOptions' => array(
                'onchange' => "      
                    var allow_duplicate = $(this).prop('checked') ? 1 : 0;
                    $('#{$this->id}').data('allow_duplicate', allow_duplicate);
                    SIANCommercialGridGetIds('{$this->id}');
                ",
            )
                ), true);
        echo "</div>";

        if ($this->model->isBuy()) {
            echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
            echo $this->widget('application.widgets.USCheckBox', array(
                'id' => $this->retention_checkbox_id,
                'model' => $this->model,
                'attribute' => 'retention',
                'htmlOptions' => array(
                    'readonly' => false,
                ),
                    ), true);
            echo "</div>";
        }

        echo "<div class='col-lg-2 col-md-2 col-sm-2 col-xs-6'>";
        echo $this->form->dropDownListRow($this->model, 'round_mode', [
            PHP_ROUND_HALF_UP => 'Hacia arriba',
            PHP_ROUND_HALF_DOWN => 'Hacia abajo',
                ], [
            'id' => $this->round_mode_select_id,
            'readonly' => $this->readonly,
            'onchange' => $this->readonly ? "" : "
                    $('#{$this->id}').data('round_mode', this.value);
                    SIANCommercialGridUpdateAmounts('{$this->id}');
                "
        ]);
        echo "</div>";
        echo "</div>"; //COL2

        if (in_array($this->model->movement->route, ['commercial/saleOrder', 'commercial/saleQuote', 'support/serviceOrder', 'commercial/quoteTemplate'])) {

            echo $this->form->widget('application.widgets.USLink', array(
                'id' => $this->cost_link_id,
                'route' => '/widget/costProducts',
                'label' => "<span class='fa fa-lg fa-eye black'></span> Ver costos  ",
                'title' => 'Ver',
                'class' => 'form sian-commercial-grid-cost-link',
                'data' => array(
                    'product_ids' => [],
                    'warehouse_id' => null,
                    'include_igv' => $this->model->include_igv,
                ),
                'visible' => $this->viewCosts,
                    ), true);
            echo "<br>";
            echo "<br>";
        }

        echo "<div class='row'>"; //ROW3
        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->textAreaRow($this->model->movement, 'observation', array(
            'id' => $this->observation_input_id,
            'rows' => 3,
            'maxlength' => 500,
            'required' => $this->model->movement->isObservationRequired()
        ));
        if ($this->model->movement->direction == Scenario::DIRECTION_OUT) {
            echo $this->form->textAreaRow($this->model, 'sales_specifications', array(
                'id' => $this->sales_specifications_input_id,
                'rows' => 3,
                'maxlength' => 500,
            ));
        }
        echo $this->form->hiddenField($this->model, 'warehouse_id', [
            'id' => $this->warehouse_input_id,
            'class' => 'sian-commercial-grid-warehouse-id'
        ]);

        echo "</div>";
        echo "</div>";
    }

    private function _renderRetention() {

        echo "<div class='row " . ($this->model->retention == 0 ? 'hide' : '') . "'>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "retention_percent", array(
            'id' => $this->retention_percent_field_id,
            'label' => "% retención",
            'class' => "us-integer sian-commercial-grid-retention-percent",
            'step' => 0,
            'readonly' => $this->model->retention == 0,
            'onchange' => "SIANCommercialGridCalculateRetention('{$this->id}');"
        ));
        echo "</div>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "ret_{$this->model->movement->currency}", array(
            'id' => $this->ret_field_id,
            'label' => "Retenido <span class='currency-symbol'></span>",
            'class' => "us-double{$this->tfixed} sian-commercial-grid-total-ret",
            'step' => $this->tstep,
            'readonly' => true,
        ));
        echo "</div>";

        echo "<div class='col-lg-12 col-md-12 col-sm-12 col-xs-12'>";
        echo $this->form->numberFieldRow($this->model, "no_ret_{$this->model->movement->currency}", array(
            'id' => $this->no_ret_field_id,
            'label' => "No Ret. <span class='currency-symbol'></span>",
            'class' => "us-double{$this->tfixed} sian-commercial-grid-total-no-ret",
            'step' => $this->tstep,
            'readonly' => true,
        ));
        echo "</div>";

        echo "</div>";
    }

    private function _renderSummary() {
        echo "<div id='{$this->summary_id}'>";
        echo "<div class='well pull-right extended-summary " . ($this->model->hasErrors("total_{$this->model->movement->currency}") ? 'us-error' : '') . "' style='padding: 0px !important;'>";
        echo "<table class='table-condensed'>";
        $a_attributes = [];
        $a_attributes[] = array('name' => 'product_id', 'width' => 7, 'types' => array('id', 'value', 'aux'), 'not_in' => "window.sianCommercialGridDynamicIds");
        $a_attributes[] = array('name' => 'item_type_id', 'hidden' => true);

        $editable = false;
        $currentRoute = $this->model->movement->route;
        $isVisibleAdvanceRoute =  $currentRoute === 'logistic/purchaseOrder';

        echo CHtml::hiddenField('CommercialMovement[totals_editable]', $this->model->totals_editable, []);
        $input_affected1 = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "affected1_{$this->model->movement->currency}",
            'value_id' => $this->summary_affected1_input_id,
            'name' => 'Totals[affected1]',
            'class' => 'sian-commercial-grid-total-affected1',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => $editable,
            'has_up_option' => $editable,
            'max_attribute' => "max_affected1",
            'min_attribute' => "min_affected1",
                ), true);
        $input_crude = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "crude_{$this->model->movement->currency}",
            'value_id' => $this->summary_crude_input_id,
            'name' => 'Totals[crude]',
            'class' => 'sian-commercial-grid-total-crude',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_affected = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "affected_{$this->model->movement->currency}",
            'value_id' => $this->summary_affected_input_id,
            'name' => 'Totals[affected]',
            'class' => 'sian-commercial-grid-total-affected',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_inaffected = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "inaffected_{$this->model->movement->currency}",
            'value_id' => $this->summary_inaffected_input_id,
            'name' => 'Totals[inaffected]',
            'class' => 'sian-commercial-grid-total-inaffected',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => $editable,
            'has_up_option' => $editable,
            'max_attribute' => "max_inaffected",
            'min_attribute' => "min_inaffected",
                ), true);
        $input_nobill = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "nobill_{$this->model->movement->currency}",
            'value_id' => $this->summary_nobill_input_id,
            'name' => 'Totals[nobill]',
            'class' => 'sian-commercial-grid-total-nobill',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => $editable,
            'has_up_option' => $editable,
            'max_attribute' => "max_nobill",
            'min_attribute' => "min_nobill",
                ), true);
        $input_export = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "export_{$this->model->movement->currency}",
            'value_id' => $this->summary_export_input_id,
            'name' => 'Totals[export]',
            'class' => 'sian-commercial-grid-total-export',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $input_free = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "free_{$this->model->movement->currency}",
            'value_id' => $this->summary_free_input_id,
            'name' => 'Totals[free]',
            'class' => 'sian-commercial-grid-total-free',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $input_nnet = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "nnet_{$this->model->movement->currency}",
            'value_id' => $this->summary_nnet_input_id,
            'name' => 'Totals[nnet]',
            'class' => 'sian-commercial-grid-total-nnet',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_fnet = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "fnet_{$this->model->movement->currency}",
            'value_id' => $this->summary_fnet_input_id,
            'name' => 'Totals[fnet]',
            'class' => 'sian-commercial-grid-total-fnet',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_net = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "net_{$this->model->movement->currency}",
            'value_id' => $this->summary_net_input_id,
            'name' => 'Totals[net]',
            'class' => 'sian-commercial-grid-total-net',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $input_nigv = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "nigv_{$this->model->movement->currency}",
            'value_id' => $this->summary_nigv_input_id,
            'name' => 'Totals[nigv]',
            'class' => 'sian-commercial-grid-total-nigv',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_figv = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "figv_{$this->model->movement->currency}",
            'value_id' => $this->summary_figv_input_id,
            'name' => 'Totals[figv]',
            'class' => 'sian-commercial-grid-total-figv',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_igv = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "igv_{$this->model->movement->currency}",
            'value_id' => $this->summary_igv_input_id,
            'name' => 'Totals[igv]',
            'class' => 'sian-commercial-grid-total-igv',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => $editable,
            'has_up_option' => $editable,
            'max_attribute' => "max_igv",
            'min_attribute' => "min_igv",
                ), true);
        $input_ntotal = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "ntotal_{$this->model->movement->currency}",
            'value_id' => $this->summary_ntotal_input_id,
            'name' => 'Totals[ntotal]',
            'class' => 'sian-commercial-grid-total-ntotal',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_ftotal = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "ftotal_{$this->model->movement->currency}",
            'value_id' => $this->summary_ftotal_input_id,
            'name' => 'Totals[ftotal]',
            'class' => 'sian-commercial-grid-total-ftotal',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_total = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "total_{$this->model->movement->currency}",
            'value_id' => $this->summary_total_input_id,
            'name' => 'Totals[total]',
            'class' => 'sian-commercial-grid-total-total',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);
        $input_perception = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "perception_{$this->model->movement->currency}",
            'value_id' => $this->summary_perception_input_id,
            'name' => 'Totals[perception]',
            'class' => 'sian-commercial-grid-total-perception',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => $editable,
            'has_up_option' => $editable,
            'max_attribute' => "max_perception",
            'min_attribute' => "min_perception",
                ), true);
        $input_nreal = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "nreal_{$this->model->movement->currency}",
            'value_id' => $this->summary_nreal_input_id,
            'name' => 'Totals[nreal]',
            'class' => 'sian-commercial-grid-total-nreal',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_freal = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "freal_{$this->model->movement->currency}",
            'value_id' => $this->summary_freal_input_id,
            'name' => 'Totals[freal]',
            'class' => 'sian-commercial-grid-total-freal',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'visible' => false,
            'has_down_option' => $editable,
            'has_up_option' => $editable
                ), true);
        $input_real = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "real_{$this->model->movement->currency}",
            'value_id' => $this->summary_real_input_id,
            'name' => 'Totals[real]',
            'class' => 'sian-commercial-grid-total-real',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false
                ), true);

        $advance_percentage = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_percentage",
            'value_id' => $this->summary_advance_percentage_input_id,
            'name' => 'Totals[advance_percentage]',
            'class' => 'sian-commercial-grid-total-advance_percentage',
            'prepend' => "<span>%</span>",
            'min_value' => 0,
            'max_value' => 100,
            'readonly_text' => false,
            'has_down_option' => false,
            'has_up_option' => false,
            'visible' => $isVisibleAdvanceRoute
        ), true);

        $advance_amount = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_amount",
            'value_id' => $this->summary_advance_amount_input_id,
            'name' => 'Totals[advance_amount]',
            'class' => 'sian-commercial-grid-total-advance_amount',
            'prepend' => "<span class='currency-symbol'></span>",
            'min_value' => 0,
            'readonly_text' => false,
            'has_down_option' => false,
            'has_up_option' => false,
            'visible' => $isVisibleAdvanceRoute
        ), true);

        $advance_balance = $this->widget('application.widgets.USNumberField', array(
            'model' => $this->model,
            'attribute' => "advance_balance",
            'value_id' => $this->summary_advance_balance_input_id,
            'name' => 'Totals[advance_balance]',
            'class' => 'sian-commercial-grid-total-advance_balance',
            'prepend' => "<span class='currency-symbol'></span>",
            'readonly_text' => true,
            'has_down_option' => false,
            'has_up_option' => false,
            'visible' => $isVisibleAdvanceRoute
        ), true);

        echo "<tr><td><b>Gravado:</b></td><td style='text-align:right'>{$input_crude}{$input_affected1}{$input_affected}</td></tr>"; //<span class='sian-commercial-grid-total-affected1'></span>
        echo "<tr><td><b>Inafecto:</b></td><td style='text-align:right'>{$input_inaffected}</td></tr>"; //<span class='sian-commercial-grid-total-inaffected'></span>
        echo "<tr><td><b>Exonerado:</b></td><td style='text-align:right'>{$input_nobill}</td></tr>"; //<span class='sian-commercial-grid-total-nobill'></span>
        echo "<tr style='display:none'><td><b>Exportación:</b></td><td style='text-align:right'>{$input_export}</td></tr>"; //<span class='sian-commercial-grid-total-export'></span>
        echo "<tr style='display:none'><td><b>Gratuito:</b></td><td style='text-align:right'>{$input_free}</td></tr>"; //<span class='sian-commercial-grid-total-free'></span>
        echo "<tr><td><b>Subtotal:</b></td><td style='text-align:right'>{$input_nnet}{$input_fnet}{$input_net}</td></tr>"; //<span class='sian-commercial-grid-total-net'></span>
        echo "<tr><td><b>IGV:</b></td><td style='text-align:right'>{$input_nigv}{$input_figv}{$input_igv}</td></tr>"; //<span class='sian-commercial-grid-total-igv'></span>
        echo "<tr><td><b>Total venta:</b></td><td style='text-align:right'>{$input_ntotal}{$input_ftotal}{$input_total}</span></td></tr>"; // <span class='sian-commercial-grid-total-total'></span>
        echo "<tr><td><b>Percepción:</b></td><td style='text-align:right'>{$input_perception}</td></tr>"; //<span class='sian-commercial-grid-total-perception'></span>
        echo "<tr><td><b>Total a pagar:</b></td><td style='text-align:right'>{$input_nreal}{$input_freal}{$input_real}</td></tr>"; //<span class='sian-commercial-grid-total-real'></span>
        if($isVisibleAdvanceRoute){
            echo "<tr><td><b>% Adelanto:</b></td><td style='text-align:right'>{$advance_percentage}</td></tr>"; 
            echo "<tr><td><b>Total Adelanto:</b></td><td style='text-align:right'>{$advance_amount}</td></tr>"; 
            echo "<tr><td><b>Saldo Adelanto:</b></td><td style='text-align:right'>{$advance_balance}</td></tr>";
        }
        echo "</table>";
        echo "</div>";
        echo "</div>";
    }

    private function _processItem($p_o_item, $p_a_instance_ids, $p_s_parent_type = null, $p_i_parent_id = null) {

        $a_attributes = [];
        $a_attributes['instance_id'] = $p_a_instance_ids[$p_o_item->instance_id];
        $a_attributes['gift_of_instance'] = !USString::isBlank($p_o_item->gift_of_instance) ? $p_a_instance_ids[$p_o_item->gift_of_instance] : '';
        $a_attributes['product_id'] = $p_o_item->itemObj->product_id;
        $a_attributes['item_type_id'] = $p_o_item->itemObj->item_type_id;
        $a_attributes['product_type'] = $p_o_item->itemObj->product_type;
        $a_attributes['product_name'] = isset($p_o_item->itemObj->product_name) ? $p_o_item->itemObj->product_name : '';
        $a_attributes['unit_stock'] = isset($p_o_item->itemObj->unit_stock) ? $p_o_item->itemObj->unit_stock : 0;
        $a_attributes['mixed_stock'] = isset($p_o_item->itemObj->mixed_stock) ? $p_o_item->itemObj->mixed_stock : '';
        $a_attributes['pres_quantity'] = $p_o_item->itemObj->pres_quantity;
        //
        $a_attributes['equivalence'] = $p_o_item->itemObj->equivalence;
        $a_attributes['allow_decimals'] = $p_o_item->itemObj->allow_decimals;
        $a_attributes['skip_cost_validation'] = $p_o_item->itemObj->skip_cost_validation;
        //
        $a_attributes['igv_affection'] = $p_o_item->igv_affection;
        $a_attributes['price_type'] = $p_o_item->itemObj->price_type;
        $a_attributes['perception_affected'] = $p_o_item->perception_affected;
        $a_attributes['expiration'] = $p_o_item->expiration;
        $a_attributes['is_locked'] = $p_o_item->is_locked;
        //Restauramos montos (en el caso de combo y grupo no)
        if ($p_o_item->itemObj->product_type === Product::TYPE_GROUP) {
            if ($this->is_commercial_route) {
                //Actualizamos los mínimos según el tipo de cambio del formulario
                if ($this->model->movement->currency == Currency::PEN) {
                    $p_o_item->min_price_usd = $p_o_item->min_price_pen / $this->model->movement->exchange_rate;
                } else {
                    $p_o_item->min_price_pen = $p_o_item->min_price_usd * $this->model->movement->exchange_rate;
                }

                $a_attributes['min_price_pen'] = round($p_o_item->min_price_pen, $this->ifixed);
                $a_attributes['min_price_usd'] = round($p_o_item->min_price_usd, $this->ifixed);

                $a_attributes['avg_price_pen'] = round($p_o_item->avg_price_pen, $this->ifixed);
                $a_attributes['avg_price_usd'] = round($p_o_item->avg_price_usd, $this->ifixed);
            }

            $a_attributes['presentationItems'] = [];
            $a_attributes['price'] = round($p_o_item->{"price_{$this->model->movement->currency}"}, $this->ifixed);
            $a_attributes['discount'] = round($p_o_item->{"discount_{$this->model->movement->currency}"}, $this->ifixed);
            $a_attributes['promotion_item_id'] = '';
            $a_attributes['promotion_gift_option_id'] = '';
        } else {
            //Se obtiene las presentaciones y precios por defecto
            $a_attributes['presentationItems'] = $this->presentationItems[$p_o_item->itemObj->product_id];
            $a_attributes['price'] = round($p_o_item->{"price_{$this->model->movement->currency}"}, $this->ifixed);
            $a_attributes['discount'] = round($p_o_item->{"discount_{$this->model->movement->currency}"}, $this->ifixed);
            $a_attributes['promotion_item_id'] = '';
            $a_attributes['promotion_gift_option_id'] = '';
            //Minimos
            if ($this->is_commercial_route) {
                //Obtenemos equivalencia
                $s_equivalence = number_format($p_o_item->itemObj->equivalence, 2);
                if ($p_s_parent_type === Product::TYPE_COMBO) {
                    $a_presentationOption = $this->comboItems[$p_i_parent_id][$p_o_item->itemObj->product_id][$s_equivalence];
                } else {
                    $a_presentationOption = $a_attributes['presentationItems'][$s_equivalence];
                }
                //
                $f_xmprice = $a_presentationOption[$p_o_item->include_igv == 1 ? 'imprice' : 'mprice'];
                $f_xaprice = $a_presentationOption[$p_o_item->include_igv == 1 ? 'iaprice' : 'aprice'];
                //Reseteamos precios
                if ($a_presentationOption['currency'] == Currency::PEN) {
                    $a_attributes['min_price_pen'] = (double) $f_xmprice;
                    $a_attributes['min_price_usd'] = (double) bcdiv($f_xmprice, $this->model->movement->exchange_rate, $this->ifixed);

                    $a_attributes['avg_price_pen'] = (double) $f_xaprice;
                    $a_attributes['avg_price_usd'] = (double) bcdiv($f_xaprice, $this->model->movement->exchange_rate, $this->ifixed);
                } else {
                    $a_attributes['min_price_pen'] = (double) bcmul($f_xmprice, $this->model->movement->exchange_rate, $this->ifixed);
                    $a_attributes['min_price_usd'] = (double) $f_xmprice;

                    $a_attributes['avg_price_pen'] = (double) bcmul($f_xaprice, $this->model->movement->exchange_rate, $this->ifixed);
                    $a_attributes['avg_price_usd'] = (double) $f_xaprice;
                }
                //Si hay promocion reseteamos el precio
                if ($p_s_parent_type == null && $this->is_template == 0 && $p_o_item->is_locked == 0) {
                    //Si hay promoción (no importa si antes no lo era)
                    if (isset($a_presentationOption['promotion_item_id']) && !USString::isBlank($a_presentationOption['promotion_item_id'])) {
                        $f_pprice = $this->_processAmount($a_presentationOption['currency'], $a_presentationOption[$p_o_item->include_igv == 1 ? 'ipprice' : 'pprice']);
                        $a_attributes['price'] = round($f_pprice, $this->ifixed);
                        if (bcmod($p_o_item->itemObj->pres_quantity, $a_presentationOption['carry']) == 0 && $a_presentationOption['carry'] != $a_presentationOption['pay']) {
                            $a_attributes['discount'] = round($f_pprice - ($f_pprice * $a_presentationOption['pay'] / $a_presentationOption['carry']), $this->ifixed);
                        } else {
                            $a_attributes['discount'] = 0;
                        }
                        $a_attributes['promotion_item_id'] = $a_presentationOption['promotion_item_id'];
                        $a_attributes['promotion_gift_option_id'] = '';
                    }
                    //Si está marcado como regalo...
                    if (isset($p_o_item->itemObj->promotion_gift_option_id) && !USString::isBlank($p_o_item->itemObj->promotion_gift_option_id)) {
                        //... y sigue siendo regalo
                        if (isset($this->promotionGiftOptions[$p_o_item->itemObj->promotion_gift_option_id]) && isset($this->promotionGiftOptions[$p_o_item->itemObj->promotion_gift_option_id][$s_equivalence])) {
                            $a_attributes['presentationItems'] = $this->promotionGiftOptions[$p_o_item->itemObj->promotion_gift_option_id];
                            $a_promotionOption = $a_attributes['presentationItems'][$s_equivalence];
                            $f_pprice = $this->_processAmount($a_promotionOption['currency'], $a_promotionOption[$p_o_item->include_igv == 1 ? 'ipprice' : 'pprice']);
                            $a_attributes['price'] = round($f_pprice, $this->ifixed);
                            $a_attributes['discount'] = 0;
                            $a_attributes['promotion_item_id'] = '';
                            $a_attributes['promotion_gift_option_id'] = $a_promotionOption['promotion_gift_option_id'];
                        } else {
                            //Si ya no es regalo se marca como afecto
                            $a_attributes['igv_affection'] = CommercialMovementProduct::IGV_AFFECTION_AFFECTED;
                            $a_attributes['gift_of_instance'] = '';
                        }
                    }
                }
            }
        }
        //Para enlace entre productos
        $a_attributes['parent_route'] = isset($p_o_item->itemObj->parent_route) ? $p_o_item->itemObj->parent_route : '';
        $a_attributes['quantity_item_id'] = isset($p_o_item->itemObj->quantity_item_id) ? $p_o_item->itemObj->quantity_item_id : '';
        $a_attributes['total_item_id'] = isset($p_o_item->itemObj->total_item_id) ? $p_o_item->itemObj->total_item_id : '';
        //Para errores
        $a_attributes['errors'] = $p_o_item->getAllErrors();

        return $a_attributes;
    }

    private function _processAmount($p_s_item_currency, $p_f_amount) {
        if ($p_s_item_currency === Currency::PEN) {
            if ($this->model->movement->currency === Currency::USD) {
                return round($p_f_amount / $this->model->movement->exchange_rate, $this->ifixed);
            } else {
                return $p_f_amount;
            }
        } else {
            if ($this->model->movement->currency === Currency::PEN) {
                return round($p_f_amount * $this->model->movement->exchange_rate, $this->ifixed);
            } else {
                return $p_f_amount;
            }
        }
    }

}
