window.sianBatchMovementIds = [];

function SIANBatchMovementAddItem(div_id, batch_id, batch_code, expiration_date, unit_stock, pres_stock, pres_stock_label, pres_quantity)
{
    //DIV
    var divObj = $('#' + div_id);
    var readonly = divObj.data('readonly');
    var parent_pres_quantity = divObj.data('parent_pres_quantity');
    var parent_row_id = divObj.data('parent_row_id');
    var allow_decimals = divObj.data('allow_decimals');
    var count = parseInt(divObj.data('count'));
    var function_local_storage = parseInt(divObj.data('function_local_storage'));
    var row_id = getLocalId();
    var quantity_id = getLocalId();

    //
    var row = '<tr id=\'' + row_id + '\' class=\'sian-batch-movement-item\'>';
    //
    row += '<td class=\'form-group\'>';
    row += '<input type=\'text\'   class=\'form-control sian-batch-movement-item-batch-id\' name=\'Batch[' + row_id + '][batch_id]\' value=\'' + batch_id + '\' readonly=\'readonly\' placeholder=\'ID\'>';
    row += '<input type=\'hidden\' class=\'sian-batch-movement-item-row-id\' value=\'' + parent_row_id + '\' name=\'Batch[' + row_id + '][row_id]\'>';
    row += '<input type=\'hidden\' class=\'sian-batch-movement-item-row-id-batch-id\' value=\'' + parent_row_id + batch_id + '\' name=\'Batch[' + row_id + '][row_id_batch_id]\'>';
    row += "</td>";
    //
    row += '<td class=\'form-group\'>';
    row += '<input type=\'text\' class=\'form-control sian-batch-movement-item-batch-code\' name=\'Batch[' + row_id + '][batch_code]\' value=\'' + batch_code + '\' readonly=\'readonly\'  placeholder=\'Code\'>';
    row += "</td>";
    //
    if(function_local_storage == 0){
        readonly = true;
    }
    row += '<td class=\'form-group\' ' + (readonly ? '' : 'style=\'display: none;\'') + '>';
    row += '<input type=\'hidden\' class=\'form-control sian-batch-movement-item-unit-stock\' name=\'Batch[' + row_id + '][stock]\' value=\'' + unit_stock + '\' readonly=\'readonly\' placeholder=\'Unit Stock\'>';
    row += '<input type=\'hidden\' class=\'form-control sian-batch-movement-item-pres-stock\' name=\'Batch[' + row_id + '][pres_stock]\' value=\'' + pres_stock + '\' readonly=\'readonly\' placeholder=\'Pres. Stock\'>';
    row += '<input type=\'text\' class=\'form-control sian-batch-movement-item-pres-stock-label\' name=\'Batch[' + row_id + '][pres_stock_label]\' value=\'' + pres_stock_label + '\' readonly=\'readonly\' placeholder=\'Stock\'>';
    row += "</td>";
    //
    row += '<td class=\'form-group\'>';
    row += '<input type=\'text\' style=\'text-align:center\' class=\'form-control sian-batch-movement-item-expiration-date\' name=\'Batch[' + row_id + '][expiration_date]\' value=\'' + expiration_date + '\' readonly=\'readonly\' placeholder=\'Fecha Expiración\'>';
    row += "</td>";
    //
    row += '<td class=\'form-group\'>';
    row += '<input type=\'hidden\' style=\'text-align:right\' class=\'form-control sian-batch-movement-item-parent-pres-quantity ' + (allow_decimals === 0 ? 'us-double0' : 'us-double2') + '\' name=\'Batch[' + row_id + '][parent_pres_quantity]\' value=\'' + parent_pres_quantity + '\'>';
    row += '<input type=\'number\' id=\'' + quantity_id + '\' style=\'text-align:right\' class=\'form-control sian-batch-movement-item-pres-quantity ' + (allow_decimals === 0 ? 'us-double0' : 'us-double2') + '\' name=\'Batch[' + row_id + '][pres_quantity]\' value=\'' + pres_quantity + '\'' + (allow_decimals === 0 ? ' min=\'0\' step=\'1\'' : '\' min=\'0.00\' step=\'0.01\'') + '>';
    row += "</td>";
    //
    if (!readonly) {
        row += '<td class=\'form-group\'>';
        row += '<a onclick=\'SIANBatchMovementRemoveItem(\"' + div_id + '\",\"' + row_id + '\", false)\' title=\'Eliminar Batch\'><span class=\'fa fa-times fa-lg black\'></span></a>';
        row += "</td>";
    }
    //    
    row += "</tr>";

    //COUNT DE ITEMS
    if (count === 0)
    {
        divObj.find('table.products_table tbody').html(row);
    } else
    {
        divObj.find('table.products_table tbody').append(row);
    }
    $('#' + quantity_id).focus().select();
    //
    divObj.data('count', count + 1);
}

function SIANBatchMovementRemoveItem(div_id, id, confirmation)
{
    //DIV
    var divObj = $('#' + div_id);
    var parent_row_id = divObj.data('parent_row_id');
    var count = parseInt(divObj.data('count'));
    var disabled = divObj.data('disabled');
    var table = $('#' + div_id + '_table');

    if (disabled)
    {
        bootbox.alert(us_message('No se puede "Eliminar" en este momento porque el componente está deshabilitado.', 'warning'));
        return;
    }

    if (confirmation ? confirm('¿Está seguro de eliminar este ítem?') : true)
    {
        $('#' + id).remove();
        if (count === 1)
        {
            table.find('tbody').html('<tr><td class="empty" colspan="99">' + STRINGS_NO_DATA + '</td></tr>');
        }

        //COUNT DE ITEMS
        divObj.data('count', count - 1);
        //Actualizamos
        SIANBatchMovementGetIds(div_id, parent_row_id);
    }
}

function SIANBatchMovementGetIds(div_id, row_id) {

    var table_id = div_id + '_table';
    var tableObj = $('#' + table_id);

    if (window.sianBatchMovementIds[row_id] == undefined) {
        window.sianBatchMovementIds[row_id] = [];
    } else {
        window.sianBatchMovementIds[row_id].splice(0, window.sianBatchMovementIds[row_id].length);
    }

    //Recorremos rows
    tableObj.find('tr.sian-batch-movement-item').each(function (index) {
        var rowObj = $(this);
        //Get
        var batch_id = rowObj.find('input.sian-batch-movement-item-batch-id').val();
        //Set       
        window.sianBatchMovementIds[row_id].push(batch_id);
    });
}