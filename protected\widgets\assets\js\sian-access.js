function SIANAccessInit(div_id)
{
    var divObj = $('#' + div_id);
    //Obtenemos
    var preffix = divObj.data('preffix');

    var readerObj = new FileReader();

    //Evento para importar
    readerObj.onloadend = function (evt) {

        if (evt.target.readyState == FileReader.DONE) { // DONE == 2

            try
            {
                var a_array = jQuery.parseJSON(evt.target.result);

                divObj.find('input.' + preffix + '-access').prop('checked', false);

                for (var i = 0; i < a_array.length; i++) {
                    var obj = a_array[i];
                    var className = preffix + '-' + obj.module + '-' + obj.controller + '-' + obj.action;
                    //Al importar marcamos
                    divObj.find('input.' + className).prop('checked', true);
                }
            } catch (ex)
            {
                bootbox.alert(us_message('Ha ocurrido un error al leer el archivo o no es compatible', 'error'));
            }
        }
    };

    //Evento al dar click en importar
    $('body').on('change', '#' + div_id + '_file', function () {

        var files = document.getElementById(div_id + '_file').files;

        if (!files.length) {
            bootbox.alert(us_message('Seleccione un archivo!', 'warning'));
            return;
        }

        var file = files[0];
        var start = 0;
        var stop = file.size - 1;

        var blob = file.slice(start, stop + 1);
        readerObj.readAsBinaryString(blob);
    });
}

function SIANAccessCheck(div_id, elementObj)
{
    var divObj = $('#' + div_id);
    //Obtenemos
    var preffix = divObj.data('preffix');

    var selector = 'access';
    selector += '-' + (typeof elementObj.data('module') === 'undefined' ? '.*' : elementObj.data('module'));
    selector += '-' + (typeof elementObj.data('controller') === 'undefined' ? '.*' : elementObj.data('controller'));
    selector += '-' + (typeof elementObj.data('action') === 'undefined' ? '.*' : elementObj.data('action'));

    var regex = new RegExp('^' + selector + '$');

    divObj.find('input.' + preffix + '-access').each(function (index) {

        var currentObj = $(this);
        var string = 'access-' + currentObj.data('module') + '-' + currentObj.data('controller') + '-' + currentObj.data('action');

        if (regex.test(string)) {
            currentObj.prop('checked', elementObj.prop('checked'));
        }
    });
}

function SIANAccessExport(div_id)
{
    var divObj = $('#' + div_id);
    //Obtenemos
    var export_url = divObj.data('export_url');

    var o_frame = document.getElementById(div_id + '_frame');

    $.ajax({
        type: 'post',
        url: export_url,
        data: divObj.closest('form').serialize(),
        beforeSend: function (xhr) {
            window.active_ajax++;
            //Ocultamos los tooltip
            $('div.ui-tooltip').remove();
        },
        success: function (data) {
            o_frame.src = export_url;
            window.active_ajax--;
        },
        error: function (request, status, error) { // if error occured
            window.active_ajax--;
            bootbox.alert(us_message(request.responseText, 'error'));
        },
        dataType: 'json'
    });
}