<?php

class USButton extends CWidget {

    public $id;
    public $params = [];
    public $data = [];
    public $title = null;
    public $icon = null;
    public $label = null;
    public $size = 'extra_small';
    public $buttonType = 'button';
    public $context = null;
    public $block = false;
    public $disabled = null;
    public $route = null;
    public $url = null;
    public $class = null;
    public $target = null;
    public $htmlOptions = [];
    public $onclick = null;
    public $checked = false;
    public $visible = true;
    //Private
    private $controller;

    /**
     * Initializes the widget.
     */
    public function init() {
        $this->controller = Yii::app()->controller;

        $this->id = isset($this->id) ? $this->id : $this->controller->getServerId();

        //DISABLED
        if (!isset($this->disabled)) {
            $this->disabled = !(isset($this->route) ? $this->_checkRoute() : true);
        }

        //URL
        if ($this->disabled) {
            $this->url = null;
        } else {
            if (isset($this->route)) {
                $this->url = $this->_getUrl();
            } else {
                $this->url = isset($this->url) ? $this->url : null;
            }
        }
    }

    /**
     * Runs the widget.
     */
    public function run() {

        if (isset($this->class)) {

            //Add necessary information if not exist
            $this->data['modal_id'] = isset($this->data['modal_id']) ? $this->data['modal_id'] : $this->controller->getServerId();
            //Mezclamos con los HTML Options
            $this->htmlOptions['id'] = $this->id;
            $this->htmlOptions['class'] = isset($this->htmlOptions['class']) ? ($this->htmlOptions['class'] . ' ' . $this->class) : $this->class;
            $this->htmlOptions['title'] = $this->title;
            $this->htmlOptions['url'] = $this->url;
            $this->htmlOptions['disabled'] = $this->disabled;
            $this->htmlOptions['onclick'] = $this->onclick;

            foreach ($this->data as $key => $value) {
                $this->htmlOptions["data-{$key}"] = $value;
            }


            $this->widget('booster.widgets.TbButton', array(
                'context' => $this->context,
                'size' => $this->size,
                'block' => $this->block,
                'encodeLabel' => false,
                'label' => (isset($this->icon) ? "<span class='{$this->icon}'></span> " : "") . (isset($this->label) ? $this->label : ""),
                'visible' => $this->visible,
                'htmlOptions' => $this->htmlOptions
            ));
        } else {
            //Mezclamos con los HTML Options
            $this->htmlOptions['id'] = $this->id;
            $this->htmlOptions['title'] = $this->title;
            $this->htmlOptions['disabled'] = $this->disabled;
            $this->htmlOptions['target'] = $this->target;
            $this->htmlOptions['onclick'] = $this->onclick;

            $this->widget('booster.widgets.TbButton', array(
                'context' => $this->context,
                'size' => $this->size,
                'block' => $this->block,
                'encodeLabel' => false,
                'label' => (isset($this->icon) ? "<span class='{$this->icon}'></span> " : "") . (isset($this->label) ? $this->label : ""),
                'buttonType' => isset($this->url) ? 'link' : $this->buttonType,
                'url' => $this->url,
                'visible' => $this->visible,
                'htmlOptions' => $this->htmlOptions,
            ));
        }
    }

    /**
     * Retorna la URL
     * @return string URL
     */
    private function _getUrl() {
        return $this->controller->createUrl($this->route, $this->params);
    }

    private function _checkRoute() {
        return $this->checked ? $this->checked : $this->controller->checkRoute($this->route);
    }

}
