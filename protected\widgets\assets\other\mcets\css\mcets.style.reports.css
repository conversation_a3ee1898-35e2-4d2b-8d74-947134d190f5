/*#cbx_tabs {
    position: relative;
    padding-left: 6.5em;
}
#cbx_tabs .ui-tabs-nav {
    position: absolute;
    left: 0.25em;
    top: 0.25em;
    bottom: 0.25em;
    width: 10em;
    padding: 0.2em 0 0.2em 0.2em;
    background: none;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    -moz-border-top-right-radius: 0px;
    -moz-border-bottom-right-radius: 0px;
}
#cbx_tabs .ui-tabs-nav li {
    right: 1px;
    width: 100%;
    border-right: none;
    overflow: hidden;
    border-bottom: 1px solid !important;
}
#cbx_tabs .ui-tabs-nav li.ui-tabs-selected {
    border-right: 1px solid transparent;
}
#cbx_tabs .ui-tabs-nav li a {
    float: right;
    text-align: right;
}
#cbx_tabs > div {
    margin-left: 5em;
}
.cMargin{margin: 5px;}

div#ui-datepicker-div {
    z-index: 9999 !important;
}
.radio .ui-state-default{
    background: none repeat scroll 0% 0% #F8F6F6 !important;
    border: 1px solid rgba(48, 104, 129, 0.39)!important;
    color: #747171!important;
    padding: 2px 6px!important;
}
.radio .ui-state-active{
    background: rgb(0, 91, 132) !important;
    color: #fff!important;
}
.title-mc{
    font-size: 18px;
    color: #015E79;
    padding: 10px 6px;
    font-weight: bold;
}
.title-mc div{
    display: inline-block;
}
#help{
    cursor: pointer;
    border:1px solid #E6E7E8;
    border-radius: 2px;
}
#help:hover{
    box-shadow: 0 0 3px gray;
}
.margin-input{
    margin-bottom:10px;
}
.margin-input p{
    margin-bottom: 5px;
    color:gray;
}
.align-rigth{
    text-align: right;
}
.input-mc{
    border: 1px solid #E6E7E8;
    border-radius: 2px;
    width: 200px;
}
.input-text-mc{
    border-radius: 0px;
    font-size: 13px;
    width: 15.6em;
    padding: 6px;
    height: 1.5em;
    color: #6A6A6A;
}
#cbx_id-combobox{        
    color: white;
    background: #015E79;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
    font-size: 14px;
    width: 174px;
}
#cbx_id-button-combobox{
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    border: none;
    background: #015E79;
}
.btnReport{
    margin: 21px 0 0 60px;
    border-radius: 2px;
    border: none;
}
#filterSearchDiv label{
    height: 26px;
    padding: 0!important;
    border: none;
    width: 104px;
    text-transform: uppercase;
    font-size: 14px;
}

 COMPONENTS

*/



/*
.ui-button-text-only, .ui-button-text {
    padding: 3px 7px !important;
    font-size: 12px !important;
    border: none !important;
}
.component input{
    color: rgb(106, 106, 106) !important;
    border: 1px solid rgb(107, 107, 107);
    font-size: 12px;
    padding: 4px 8px;
    height: 19px;
    font-size: 13px;
}
.lbl-component.lbl-c-main{
    display: inline-block;
    border-bottom: 1px solid rgb(223, 223, 223);
    padding-bottom: 4px;
    width: 230px;
    padding-top: 5px;
    marging-right: 5px;
}
.select2-container--default .select2-results__option--highlighted[aria-selected]{
    background-color: #363636 !important;
    color: white;
}
.select2-container--default .select2-selection--single{
    border-radius: 0 !important
}
.select2-container--default .select2-selection--single .select2-selection__rendered{
    font-size: 13px;
    height: 27px;
}
.select2-search--dropdown .select2-search__field { 
    width: 95% !important;
}
#lateral ul{
    padding: 0px 9px 0 !important;
}

#lateral li {
    line-height: 15px !important;
}

.select2-selection--multiple {
    border-radius: 0px !important;
    min-height: 30px !important;
}

.select2-selection__choice{
    font-size: 10px !important;
    border-radius: 0px !important;
    background-color: #F3F3F3;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice{
    background-color: #FAFAFA !important;
}
.select2-dropdown.select2-dropdown--above{
    z-index: 101 !important;
}
.filter_search{
    width: 217px;
    display: inline-block;
    padding-left: 6px;
}
.btn-filter{
    width: 103px;
    background-color: rgb(0,120,174);
    color: white;
    border: none;
    outline: none;
    padding: 5px;
    cursor: pointer;
    margin-top: 17px;
    font-size: 13px
}
.btn-filter:hover{
    background: #707070 !important;
}
.btn-filter .ui-button-icon-primary{
    display: inline-block;
    vertical-align: top

}
div.form-error{
    font-size: 12px;
    background-color: rgb(223, 36, 11);
    display: inline-block;
    color: white;
    padding: 3px;
    margin-top: 5px;
}
.footer-filter-s{
    text-align: right
}

.form-control>.select2{width: 100% !important;}
.select2-container>.select2-dropdown{
    width: 18.1em !important;
}
.form-control>.radio{
    width: 260px!important;
}
#com_optionSalestrip>div>div>.form-control{
    width: 22em !important;
}
#com_reportType>.component>.sub-component>.form-control{
    width: 16em !important;
}
label[for='r_reportT_1'],label[for="r_reportT_2"]{
    padding: 4px 0px !important;
}

#content-toogler .ui-jqgrid{
    margin:0
}
#content-toogler >div{
    padding-left: 25px;
    margin-bottom: 37px;
}
#loader-ctn{
    background: none repeat scroll 0% 0% rgba(252, 252, 252, 0.3);
    position: absolute;
    top: 0px;
    height: 98%;
    width: 90%;
    font-size: 49px;
    text-align: center;
    padding-top: 50%;
    color: rgb(36, 69, 120);
}

.btn-list{
    width: 19em;
}



#com_alliance .select2{
    width: 217px !important;
}

#seatPassenger_jqGrid_passanger\.completeName{
    width: 191px !important;
}
#seatPassenger_jqGrid_buscel\.numberSeat{
    width: 45px !important;
    padding-right: 7px !important;
    padding-left: 6px !important;
}
#seatPassenger_jqGrid{
    width: 251px !important;
}
#gview_seatPassenger_jqGrid>.ui-jqgrid-bdiv{
    width: 251px !important;
}
#c_date,#c_daterango_i,#c_daterango_e{
    width: 90% !important;
}
.filter_month span.select2-container{
    width: 98% !important;
}

.c_filter_search .sub-component .form-control label[for="tbr"]{
    margin-left: -3px !important;
}

#com_filterCashbox div span.select2-container{
    width: 214px !important;
}

#com_cashbox .select2-container--default .select2-selection--single .select2-selection__rendered{
      font-size: 12px;
}*/

/*COMPONENT FOR SIAN*/
ul#select2-c_document_type-results{
    text-transform: uppercase;
}
.none{
    display: none;
}
.margin-input{
    margin-top: 2px;
}

.hide-comp{
    display: none !important
}
.show-comp{
    display: block !important
}    

.component{
    padding: 0 0px;
    margin-bottom: 10px;
}
.filter_search .component:first-child{
    margin-top: 13px;
}
.lbl-component{
    margin-top: 6px;
    margin-right: 5px;
    display: inline-block;
    font-size: 12px;
    color: rgb(103, 103, 103);
    font-weight: 500;
    text-transform: uppercase;
}

.lbl-component-simple{
    margin-top: 6px;
    display: inline-block;
    font-size: 12px;
    color: rgb(103, 103, 103);
    font-weight: 500;
}

.lbl-s-active {
    text-align: right;
    padding-left: 0 !important;
}

#c_seller_active, #c_documentSerie_active{
    margin: 0 4px !important;
    vertical-align: text-bottom;    
}
span[aria-owns="select2-c_person-results"] ul{
    max-height: 86px;
}

.select2-selection--multiple{
    border: 1px solid #B1B1B1 !important;
    border-radius: 3px !important;
}
.select2-selection__choice{
    background: #656565 !important;
    border: 0 !important;
    color: white !important;
    padding: 3px 6px !important;
}
.select2-container--default .select2-results__option--highlighted[aria-selected]{
    background-color: #515151 !important;  
}
.select2-results__option
{
    font-size: 11px;
    color: #202020;
}
.ui-buttonset .ui-state-active{
    border: none;
    background: #3287D4;
}
.ui-state-default{
    border: none;
    background: #656565;
    text-shadow: none;
    color: #FFFFFF;
}
.select2-container--default .select2-selection--single{
    border-radius: 3px;
    border: 1px solid #B1B1B1;
}
.ui-button .ui-button-text{
    font-size: 11px;
    display: inline-block;
    margin: 5px 6px;
}
#com_detail label.ui-button{
    width: 50%;
}
.ui-buttonset{
    margin-top: 0 !important;
}
.sub-ui input{
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid #b7b7b7;
    color: #505050;
    font-size: 11px;
    width: 100%;
    min-height: 31px;
}
#btnSearch{
    background: #656565;
    border: none;
    padding: 10px 26px;
    border-radius: 53px;
    text-transform: uppercase;
    margin-top: 2%;
    margin-bottom: 2%;
    outline: none !important;

}
.iReportPlus-horizontal-bar{
    background-color: rgb(72, 72, 72) !important;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}
.iReportFlat li.has-sub > a{
    background-color: rgb(58, 58, 58);
    border-top-left-radius: 3px !important;
}
.iReportFlat li.has-sub a:hover{
    background-color: rgb(40, 40, 40);
}

.ui-datepicker-header{
    background: none;
    background-color: #656565 !important;
    color: white !important;
    text-shadow: none;
    text-transform: uppercase;
    font-weight: 300 !important;
    padding: 5px !important;
}
.ui-widget-content{
    padding: 0;
    border: none;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4) !important;
}
.ui-datepicker-calendar{
    background: white !important
}
.ui-datepicker td .ui-state-default{
    box-shadow: none !important;
    padding: 5px;
    font-weight: 300;
}
.ui-datepicker td .ui-state-active{
    border-radius: 13px;
    background-color: #ECECEC;
    color: gray;
}
.ui-datepicker td .ui-state-hover{
    border-radius: 19px !important;
    background: none;
    background-color: #3287D4 !important;
    color: white !important;
    border: 0;
}

.iReportFlat li.has-sub a{
    background-color: rgb(58, 58, 58);
}
.iReportPlus-container{
    box-shadow: 2px 3px 4px rgba(0, 0, 0, 0.3);
}
.ui-buttonset .ui-button{
    padding-left: 0 !important;
}
.sub-component-group .sub-component{
    display: inline-block;
    margin-right: 2%;
    width: 48%;
}
.filter_month .select2-container{
    min-width: 97%;
}
.report{
    position: relative;
}
.loader-content-report{
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.71);
    z-index: 1;
    top: 0;

}
.loader-content-report:after{
    content: "Espere un momento por favor ";
    width: 100%;
    text-align: center;
    position: absolute;
    top: 90px;
}
.loader-report {
    margin: 30px auto;
    font-size: 10px;
    position: relative;
    text-indent: -9999em;
    border-top: 0.7em solid rgb(101, 101, 101);
    border-right: 0.7em solid rgb(101, 101, 101);
    border-bottom: 0.7em solid rgb(101, 101, 101);
    border-left: 0.7em solid #0064cd;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation: load8 1.1s infinite linear;
    animation: load8 1.1s infinite linear;
}
.loader-report,
.loader-report:after {
    border-radius: 50%;
    width: 5em;
    height: 5em;
}
.select2-container--default .select2-selection--single .select2-selection__rendered{
    height: 28px;
}
@-webkit-keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes load8 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}